# TDesign web vue 税务业务脚手架模板

## 项目依赖安装

### 安装项目模块

```
yarn install
```

### 开发阶段：编译、热重载

```
yarn dev
```

### 生成阶段：编译入口文件+静态资源

```
yarn build:site
```

### 语法检测

```
yarn lint
yarn lint:style
```

### 提交校验
```
yarn cz

```

## 设置全局 Store 数据

### 菜单数据结构

```
{
  key: '', // 菜单项唯一标识，用于设置该导航高亮的key，若不设置，则默认使用href/path、name，建议有path可不设置，其余都设置
  name: '', // 菜单项标题
  href: '', // 跳转外部链接，与path二选一
  path: '', // 路由跳转路径，与href二选一，若同时存在，以href优先
  iconClassName: '', // 菜单项图标，侧栏有效，默认使用tdesgin图标中的图标
  iconFontUrl: 'https://govcloud-cdn-1258344699.cos.ap-guangzhou.myqcloud.com/tdg/docs/iconcool.js', // 默认不需要此字段，除非需自定义icon，可以传入 url 加入新的 svg 图标。引入新的图标 Url 之后，图标名称iconClassName必须写全称。创建图片库链接：http://icon.open.oa.com/resource/project/11/detail
  imgUrl: '', // 默认不需要此字段，除非需自定义图片，可以传入 图片url。不推荐使用，因为hover时图片无法自动改变色值样式。iconFontUrl与imgUrl二选一，若同时存在，以imgUrl优先
  isExpand: false, // 默认不需要此字段, 当设置为true时， 无视store.state.global.sidebarExpandLevel值，在layout的左侧展示子菜单
  target: '', // 链接或路由跳转方式，href默认为_blank，path默认为_self
  children: [{ // 有二级栏目的目录结构
    iconClassName: '', // 菜单项图标，顶栏有效
    // 字段同上
  }]
}

```

### 设置顶栏/侧栏数据

```
// 顶栏例子
const topbarConfig = [
  {
    name: '首页',
    path: '/home',
  },
  {
    name: '外部链接',
    key: 'qq',
    href: 'https://www.qq.com/',
  },
  {
    // 有二级栏目的目录结构
    name: '子菜单',
    key: 'list',
    children: [
      {
        key: 'mail',
        name: '外部链接',
        iconClassName: 'link',
        href: 'https://mail.qq.com/'
      },
      {
        name: '配置变更',
        iconClassName: 'heart',
        path: '/config',
        target: '_blank',
      },
      {
        name: '扩容',
        iconClassName: 'add-circle',
        path: '/expansion',
      }
    ],
  },
];

// 侧栏例子
const sideBarConfig = [
  {
    name: '首页',
    imgUrl: 'https://govcloud-cdn-1258344699.cos.ap-guangzhou.myqcloud.com/tdg/docs/menu-icon.svg',
    path: '/home',
  },
  {
    name: '外部一级链接',
    iconClassName: 'link',
    href: 'https://www.qq.com/',
    key: 'qq'
  },
  {
    name: '自定义icon',
    iconClassName: 'g--logo--s',
    iconFontUrl: 'https://govcloud-cdn-1258344699.cos.ap-guangzhou.myqcloud.com/tdg/docs/iconcool.js',
    href: 'https://mail.qq.com/',
  },
  {
    // 有二级栏目的目录结构
    name: '子菜单栏',
    iconClassName: 'cloud',
    children: [
      {
        key: 'mail',
        name: '外部二级链接',
        href: 'https://mail.qq.com/',
        target: '_blank',
      },
      {
        name: '测试',
        path: '/test',
      },
    ],
  }
];

/*
0: 默认值，表示不限制层级
1: 左侧sidebar只显示一层
2: 左侧sidebar最多显示两层
3: 左侧sidebar最多显示3层 （目前TDGV的layout最多允许3层）
*/
const sidebarExpandLevel = 3; 

this.$store.commit(`global/${storeGlobalTypes.SET_TOPBAR_CONFIG}`, topbarConfig); // 设置顶栏
this.$store.commit(`global/${storeGlobalTypes.SET_SIDEBAR_CONFIG}`, sideBarConfig); // 设置侧栏
this.$store.commit(`global/${storeGlobalTypes.SET_SIDEBAR_EXPAND_LEVEL}`, 3); // 设置侧栏最多显示层级
```

### 设置导航高亮

```
// 设置顶栏， 以topbarConfig的key为目标索引， key优先级：key > href > path > name
this.$store.commit(`global/${this.$storeGlobalTypes.SET_TOPBAR_ACTIVE_KEY}`, '/home');

// 设置侧栏， 以sideBarConfig的key为目标索引， key优先级：key > href > path > name
this.$store.commit(`global/${this.$storeGlobalTypes.SET_SIDEBAR_ACTIVE_KEY}`, '/test');

```

### 设置左侧菜单栏需要展开的子菜单集合

```
// 以sideBarConfig的key为目标索引， key优先级：key > name
this.$store.commit(`global/${this.$storeGlobalTypes.SET_SIDEBAR_EXPANDED}`, ['详情页']);
```

### 设置左侧菜单栏是否显示

```
this.$store.commit(`global/${this.$storeGlobalTypes.SET_SIDEBAR_STATUS}`, true); // 显示左侧菜单栏
this.$store.commit(`global/${this.$storeGlobalTypes.SET_SIDEBAR_STATUS}`, false); // 隐藏左侧菜单栏
```

### 设置用户信息

```
const userInfo = {
  isLogin: false, // 是否已登陆
  info: {
    name: '' // 用户名，显示在右上角
  },
  menu: [], // 用户中心可操作列表(待开发)
}

this.$store.commit(`global/${storeGlobalTypes.SET_USER_INFO}`, userInfo);
```

### 导航点击事件绑定

```
<template>
<g-layout @sidebar-selected="onSidebarSelected" @topbar-selected="onTopbarSelected">
 ...
</g-layout>
</template>
```

```
/**
  * 点击导航元素事件
  * @param menu Object 菜单信息
  * @param next Function 完成自己的逻辑后必须执行，不然不会执行默认的router.push操作
  */
onTopbarSelected(menu, next) {
  // 执行业务逻辑...
  next();
},
/**
  * 点击侧栏子元素事件
  * @param menu Object
  * @param next Function 完成自己的逻辑后必须执行，不然不会执行默认的router.push操作
  */
onSidebarSelected(menu, next) {
  // 执行业务逻辑...
  next();
},
```

### 注册全局事件

```
this.$store.commit(`global/${storeGlobalTypes.REGISTER_EVENT}`, {
  handleLogin() {
    // 执行登录逻辑
    return false;
  },
  handleLogout() {
    // 执行退出登录逻辑
    return false;
  },
});
```

### 自动路由规则

> 自动路由匹配配置 - AutoRouterPlugin

| 属性          | 类型    | 默认                              | 说明                                         |
| ------------- | ------- | --------------------------------- | -------------------------------------------- |
| pages         | String  | <code>./src/pages/\*/views</code> | 需要匹配的目录                               |
| importPrefix  | String  | <code>../views</code>             | router 文件所在目录 import page 的相对根路径 |
| routerPath    | String  | <code>../router/routes.js</code>  | routers.js 文件存放目录                      |
| dynamicImport | Boolean | <code>true</code>                 | 是否动态引入路由                             |

> 页面路由规则

| 命名方式                                                                                                    | 路由生成                                                                 | 说明                               |
| ----------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------ | ---------------------------------- |
| <code>name.vue</code>                                                                                       | <code>{ name: 'name-index', path: '/name', component: nameIndex }</code> | 普通路由                           |
| <code>\_id.vue</code>                                                                                       | <code>{ name: 'name-index', path: '/:id?', component: nameIndex }</code> | 动态路由                           |
| <code>< route-meta >{"sidebar": 0,"userInfo": {"name": "123","avatar": "http://..."}}</ route-meta ></code> | -                                                                        | 路由 meta 设置，必须符合 JSON 格式 |

```js
// vue.config.js
const AutoRouterPlugin = require('./auto-router');
// ...
configureWebpack: {
  plugins: [new AutoRouterPlugin({ pages: './src/pages/*/views' })];
}
// ...
```
