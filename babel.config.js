let config = {
  presets: [
    [
      '@vue/cli-plugin-babel/preset',
      {
        useBuiltIns: 'entry',
      },
    ],
  ],
};

if (process.env.VUE_APP_IE === 'true') {
  config = {
    presets: [
      '@vue/app',
      [
        '@babel/preset-env',
        {
          modules: false,
          corejs: 3,
          useBuiltIns: 'usage',
        },
      ],
    ],
    plugins: ['@babel/plugin-transform-runtime'],
  };
}

module.exports = config;
