import Vue from 'vue';
import TDesign from 'tdesign-vue';
import router from '@/pages/index/router';
import { registerIEPolyfill, isMSIE } from '@wecity/tdesign-vue-ie';
import { getPageContentHeight, getFirstLevelTableHeight } from '@gtff/tdesign-gt-vue';
import '@gtff/tdesign-gt-vue/dist/theme/normal/index.css';
import Gt4Common from '@gt4/common-front';
import '@gt4/common-front/dist/global.css';

// IE兼容样式
if (isMSIE()) {
  // 异步加载方式，避免lerna模式下优先级异常
  require(['@wecity/tdesign-vue-ie/dist/theme/ie.css']);
}

Vue.config.productionTip = false;
Vue.prototype.$getPageContentHeight = getPageContentHeight;
Vue.prototype.$getFirstLevelTableHeight = getFirstLevelTableHeight;
Vue.prototype.$EventBus = new Vue();

Vue.use(TDesign);
Vue.use(Gt4Common);

registerIEPolyfill({ router });
