import qs from 'querystring';
import axios from 'axios';

export const getUA = window.navigator.userAgent.toLowerCase();

export const isIE = !!window.ActiveXObject || 'ActiveXObject' in window;

export const downloadFile = (url, params, t = 'a') => {
  let type = t;
  const baseURL = window.STATIC_ENV_CONFIG.API_PREFIX;
  const paramsUrl = qs.stringify(params);
  if (isIE) {
    type = 'iframe';
  }
  if (type === 'a') {
    const elink = document.createElement('a');
    elink.style.display = 'none';
    let href = baseURL + url;
    if (paramsUrl) href = `${href}?${paramsUrl}`;
    elink.href = href;
    elink.download = '下载';
    document.body.appendChild(elink);
    elink.click();
    document.body.removeChild(elink);
  } else if (type === 'iframe') {
    const iframe = document.createElement('iframe');
    const id = 'saveFileFrame';
    iframe.id = id;
    iframe.style.display = 'none';
    iframe.src = '';
    document.body.appendChild(iframe);
    setTimeout(function loadUrl() {
      let href = baseURL + url;
      if (paramsUrl) href = `${href}?${paramsUrl}`;
      iframe.contentWindow.location.href = href;
      document.body.removeChild(iframe);
    }, 50);
  } else if (type === 'nginx') {
    const elink = document.createElement('a');
    elink.style.display = 'none';
    elink.target = '_block';
    elink.href = `/staticfiles/${url}`;
    document.body.appendChild(elink);
    elink.click();
    document.body.removeChild(elink);
  }
};

export const downloadBlobFile = ({ baseURL, method = 'post', data = {}, params = {}, fileName }) => {
  const PREFIX = window.STATIC_ENV_CONFIG.API_PREFIX;
  return new Promise((resolve, reject) => {
    axios({
      baseURL: PREFIX + baseURL,
      method,
      data,
      params,
      responseType: 'blob',
    })
      .then((res) => {
        const dispositionFileName = decodeURI(res.headers['content-disposition'])
          .split('filename=')[1]
          .replace(/"/g, '');
        const blob = new Blob([res.data]); // 处理文档流
        console.log(res, dispositionFileName);
        const elink = document.createElement('a');
        // dispositionFileName有后缀
        elink.download = fileName || dispositionFileName || '未命名';
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink);
        resolve(res);
      })
      .catch((c) => {
        console.log(c);
        reject(c);
      });
  });
};
// const fileType = {
//   '.xls': 'application/vnd.ms-excel',
//   '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//   '.doc': 'application/msword',
//   '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//   '.pdf': 'application/pdf',
//   '.ppt': 'application/vnd.ms-powerpoint',
//   '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
// };
