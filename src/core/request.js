import { request } from "@gtff/tdesign-gt-vue";
import { MessagePlugin } from "tdesign-vue";
import { logout } from "@/pages/index/api/login";
import { customAlphabet } from "nanoid";

// 创建自定义的 nanoid 生成器，只包含数字和小写字母
const generateTraceId = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 16)

/**
 * 基于request的实例进行拦截器的request自定义示例
 * 传参为onFulfilled、onRejected两个处理函数
 * 你可以在这里对预设配置对象进行进一步的加工，
 *  比如请求要带的header参数配置、请求Timeout的重设等
 */
request.interceptors.request.use(
  /**
   * onFulfilled钩子函数处理
   * @param conf {Object} 实例中request的配置函数
   * @returns {Object}  request配置函数
   */
  (conf) => {
    const newConf = conf;
    // 设置随机数， 定位后端日志和解决浏览器缓存
    const { url } = newConf;
    if (url.indexOf('?') !== -1) {
      newConf.url = `${url}&t=${new Date().getTime()}`; // 请求添加时间戳
    } else {
      newConf.url = `${url}?t=${new Date().getTime()}`;
    }

    // 添加 X-B3-TraceId 头部
    newConf.headers['X-B3-TraceId'] = generateTraceId()

    // get请求映射params参数
    if (newConf.method === 'get' && newConf.params) {
      let url = `${newConf.url}?`;
      for (const propName of Object.keys(newConf.params)) {
        const value = newConf.params[propName];
        const part = `${encodeURIComponent(propName)}=`;
        if (value !== null && typeof value !== 'undefined') {
          if (typeof value === 'object') {
            for (const key of Object.keys(value)) {
              const params = `${propName}[${key}]`;
              const subPart = `${encodeURIComponent(params)}=`;
              url += `${subPart + encodeURIComponent(value[key])}&`;
            }
          } else {
            url += `${part + encodeURIComponent(value)}&`;
          }
        }
      }
      url = url.slice(0, -1);
      newConf.params = {};
      newConf.url = url;
    }
    return newConf;
  },
  /**
   * onRejected钩子函数处理
   * @param err {ErrorEvent}  前一个拦截器返回的request错误事件
   * @returns {Promise<never>}  必须reject对应的错误事件
   */
  (err) => {
    console.log('====>> req err', err);
    return Promise.reject(err);
  },
);

/**
 * 基于request的实例进行拦截器的response自定义示例，
 * 传参为onFulfilled、onRejected两个处理函数
 * 你可以在这里对预设返回的对象进行进一步的加工，比如key值的大小驼峰转换
 */
request.interceptors.response.use(
  /**
   * onFulfilled钩子函数处理
   * @param res {Object} 为前一个拦截器返回的结果
   * @returns {Object} 必须要return`结果对象`
   */
  async (res) => {
    // 未设置状态码则默认成功状态
    const { code } = res.data;
    // 获取错误信息
    const msg = res.data.msg || '系统未知错误，请反馈给管理员';
    if (code === 401) {
      // 未认证
      const newUrlValue = getNewUrlParam();
      if (newUrlValue) {
        sessionStorage.setItem('newUrlValue', newUrlValue);
      }
      return handleAuthorized();
    }
    if (code !== 1) {
      MessagePlugin.error({
        content: msg,
        duration: 2000,
      });
      return Promise.reject(code);
    }
    return res.data;
  },
  /**
   * onRejected钩子函数处理
   * @param err {ErrorEvent} 前一个拦截器返回的错误结果
   * @returns {Promise<never>}
   */
  (err) => {
    let { message } = err;
    if (message === 'Network Error') {
      message = '后端接口连接异常';
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时';
    } else if (message.includes('Request failed with status code')) {
      message = `系统接口${message.substr(message.length - 3)}异常`;
    }
    MessagePlugin.error({
      content: message,
      duration: 5 * 1000,
    });
    return Promise.reject(err);
  },
);

function handleAuthorized() {
  MessagePlugin.error({
    content: '无效的会话，或者会话已过期，请重新登录。',
    duration: 2000,
  }).then(() => {
    logout().then(() => {
      // const { href } = window.location;
      window.top.location.href = '/znsb/view/sso/login';
    });
  });
  return Promise.reject('无效的会话，或者会话已过期，请重新登录。');
}
function getNewUrlParam() {
  // 获取当前 URL
  const url = window.location.href;

  // 方法1: 使用 URLSearchParams (现代浏览器支持)
  try {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    return params.get('newUrl');
  } catch (e) {
    // 如果不支持 URL 对象或没有查询参数，使用传统方法
  }

  // 方法2: 传统字符串处理方法
  const paramName = 'newUrl=';
  const startIndex = url.indexOf(paramName);
  if (startIndex === -1) return null;

  const paramValueStart = startIndex + paramName.length;
  let endIndex = url.indexOf('&', paramValueStart);
  if (endIndex === -1) endIndex = url.length;

  return decodeURIComponent(url.substring(paramValueStart, endIndex));
}

/**
 * 新版Api统一请求入口
 * @param options
 * @param custom
 */
const fetch = (options, custom) => {
  return request(
    {
      baseURL: window.STATIC_ENV_CONFIG.API_PREFIX,
      ...options,
    },
    custom,
  );
};

const fetchSso = (options, custom) => {
  return request(
    {
      ...options,
    },
    custom,
  );
};

export { fetch, fetchSso };
