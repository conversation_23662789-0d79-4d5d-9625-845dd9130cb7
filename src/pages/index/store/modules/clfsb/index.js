// /* eslint-disable no-param-reassign */
// const clfsb = {
//   namespaced: true,
//   state: () => ({
//     cxList: [],
//     fwlxList: [],
//     fwtcList: [],
//     gjhdqList: [],
//     gyfsList: [],
//     hygxList: [],
//     jtcygxList: [],
//     jzjglxList: [],
//     qsqszydxList: [],
//     qsqszylbList: [],
//     qsqszyytList: [],
//     scqdfwfsList: [],
//     sfzjlxList: [],
//     xzqhList: [],
//     zrfxx: [],
//     csfxx: [],
//     fwjyxx: {},
//     fwjbxx: {},
//   }),
//   getters: {},
//   mutations: {
//     initCxList(state, data) {
//       state.cxList = data;
//     },
//     initFwlxList(state, data) {
//       state.fwlxList = data;
//     },
//     initFwtcList(state, data) {
//       state.fwtcList = data;
//     },
//     initGjhdqList(state, data) {
//       state.gjhdqList = data;
//     },
//     initGyfsList(state, data) {
//       state.gyfsList = data;
//     },
//     initHygxList(state, data) {
//       state.hygxList = data;
//     },
//     initJtcygxList(state, data) {
//       state.jtcygxList = data;
//     },
//     initJzjglxList(state, data) {
//       state.jzjglxList = data;
//     },
//     initQsqszydxList(state, data) {
//       state.qsqszydxList = data;
//     },
//     initQsqszylbList(state, data) {
//       state.qsqszylbList = data;
//     },
//     initQsqszyytList(state, data) {
//       state.qsqszyytList = data;
//     },
//     initScqdfwfsList(state, data) {
//       state.scqdfwfsList = data;
//     },
//     initSfzjlxList(state, data) {
//       state.sfzjlxList = data;
//     },
//     initxzqhList(state, data) {
//       state.xzqhList = data;
//     },
//     saveZrfxx(state, data) {
//       state.zrfxx = data;
//     },
//     saveCsfxx(state, data) {
//       state.csfxx = data;
//     },
//     saveFwjyxx(state, data) {
//       state.fwjyxx = data;
//     },
//     saveFwjbxx(state, data) {
//       state.fwjbxx = data;
//     },
//   },
//   actions: {},
// };

// export default clfsb;
