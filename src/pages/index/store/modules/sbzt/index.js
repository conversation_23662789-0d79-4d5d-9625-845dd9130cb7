/* eslint-disable no-param-reassign */
const sbzt = {
  namespaced: true,
  state: () => ({
    sbgzEnterFlag: false, // 用于记录页面是否通过申报更正功能跳转进入，子页面需要该字段决定新增内容
    sbgzSsyfInfo: {}, // 配合sbgzEnterFlag用于记录申报更正页面传递过来的所属月份信息
    sbztCacheInfo: {},
  }),
  getters: {},
  mutations: {
    setSbztCacheInfo(state, data) {
      Object.keys(data).forEach((key) => {
        state.sbztCacheInfo[key] = data[key];
      });
    },
    setSbgzEnterFlag(state, data) {
      state.sbgzEnterFlag = data;
    },
    setSbgzSsyfInfo(state, data) {
      state.sbgzSsyfInfo = data;
    },
  },
  actions: {},
};

export default sbzt;
