/* eslint-disable no-param-reassign */
const zzstz = {
  namespaced: true,
  state: () => ({
    userInfo: {},
  }),
  getters: {},
  mutations: {
    setUserInfoData(state, data) {
      if (data && Array.isArray(data)) {
        data = data.map((item) => {
          if (item.jgmc && item.jgmc.includes('|')) {
            // 使用下划线表示忽略第一个分割结果
            const [, /* ignored */ jgmcValue] = item.jgmc.split('|');
            item.jgmc = jgmcValue;
          }
          return item;
        });
      }
      state.userInfo = data;
    },
  },
  actions: {},
};

export default zzstz;
