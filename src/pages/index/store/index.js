import Vue from 'vue';
import Vuex from 'vuex';
import { store } from '@gtff/tdesign-gt-vue';
import yfjjkctzgl from '@/pages/index/store/modules/yfjjkctzgl';
import clfsb from './modules/clfsb';
import hjbhsfh from './modules/hjbhsfh';
import zdmczh from './modules/zdmczh/index.js';
import zzstz from './modules/zzstz';
import fcstz from './modules/fcstz';
import sbzt from './modules/sbzt';

const initStore = store.init(Vue, Vuex);

initStore.registerModule('index', {
  state: {
    user: 'index name',
  },
});
initStore.registerModule('clfsb', clfsb);
initStore.registerModule('hjbhsfh', hjbhsfh);
initStore.registerModule('yfjjkctzgl', yfjjkctzgl);
initStore.registerModule('zdmczh', zdmczh);
initStore.registerModule('zzstz', zzstz);
initStore.registerModule('fcstz', fcstz);
initStore.registerModule('sbzt', sbzt);
initStore.registerModule('isProduct', {
  namespaced: true,
  state: () => ({
    envValue: false,
  }),
  getters: {},
  mutations: {
    setEnvValue(state, data) {
      state.envValue = data;
    },
  },
  actions: {},
});
initStore.registerModule('jyss', {
  namespaced: true,
  state: () => ({
    readyStatus: true,
    readyYhsStatus: true,
    readyFcsStatus: true,
    readyQysdsyjStatus: true,
  }),
  getters: {},
  mutations: {
    setReadyStatus(state, data) {
      state.readyStatus = data;
    },
    setYhsReadyStatus(state, data) {
      state.readyYhsStatus = data;
    },
    setFcsReadyStatus(state, data) {
      state.readyFcsStatus = data;
    },
    setQysdsyjReadyStatus(state, data) {
      state.readyQysdsyjStatus = data;
    },
  },
  actions: {},
});
initStore.registerModule('isYs', {
  namespaced: true,
  state: () => ({
    envValue: false,
  }),
  getters: {},
  mutations: {
    setValue(state, data) {
      state.envValue = data;
    },
  },
  actions: {},
});
export default initStore;
