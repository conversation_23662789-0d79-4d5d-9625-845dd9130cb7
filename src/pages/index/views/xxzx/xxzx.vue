<!--
 * @Descripttion: 消息中心
 * @Version: 1.0
 * @Author: cby
 * @Date: 2024-04-17 13:41:26
 * @LastEditors: cby
 * @LastEditTime: 2024-04-07 13:52:44
-->
<template>
  <div>
    <div class="xxzxHeader" style="position: relative">
      <div class="title">消息中心</div>

      <div
        style="
          position: absolute;
          right: 4%;
          bottom: 37%;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          font-weight: 400;
          color: #4285f4;
          cursor: pointer;
        "
      >
        查看全部
      </div>
    </div>
    <div style="display: flex">
      <div>
        <template>
          <div :style="{ background: 'var(--bg-color-page)', borderRadius: '3px' }">
            <t-menu defaultValue="dbsx" :collapsed="collapsed" :defaultExpanded="expanded" @change="changeHandler">
              <!-- 循环实现 -->
              <t-submenu :value="item.value" :title="item.title" v-for="item in menuList" :key="item.value">
                <template #icon>
                  <t-icon :name="item.icon" />
                </template>
                <t-menu-item :value="submenu.value" v-for="submenu in item.children" :key="submenu.value">
                  <span>{{ submenu.title }}</span>
                </t-menu-item>
              </t-submenu>
            </t-menu>
          </div>
        </template>
      </div>
      <div class="dbrwXxk">
        <component :is="activeCompo" />
      </div>
    </div>
  </div>
</template>
<script>
import dbsx from './components/dbsx.vue';
import hgsx from './components/hgsx.vue';
import fxsx from './components/fxsx.vue';
import wsqs from './components/wsqs.vue';
import aqtx from './components/aqtx.vue';
import sjgg from './components/sjgg.vue';
import tzgg from './components/tzgg.vue';
import yhzctj from './components/yhzctj.vue';
import zxzczx from './components/zxzczx.vue';
import { menuList } from './config';

export default {
  components: { dbsx, hgsx, fxsx, wsqs, aqtx, sjgg, tzgg, yhzctj, zxzczx },
  data() {
    return {
      menuList,
      activeCompo: 'dbsx', // 默认
      collapsed: false,
      expanded: ['1', '2'],
      dbrwList: [
        {
          id: '1',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
        {
          id: '2',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
        {
          id: '3',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
      ],
    };
  },

  computed: {},
  methods: {
    changeHandler(active) {
      console.log('change', active);
      this.activeCompo = active;
    },
    click(value) {
      this[value] = true;
      console.log(value);
    },
  },
};
</script>
<style scoped>
.dbrwXxk {
  width: 100%;
}
.xxzxHeader {
  display: flex;
  height: 56px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #27282e14;
  border-radius: 0 0 2px 2px;
  align-items: center;
  justify-content: space-between;
}
.title {
  margin-left: 8px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  font-weight: bolder;
  line-height: 24px;
  letter-spacing: 0;
  color: #333;
}
</style>
