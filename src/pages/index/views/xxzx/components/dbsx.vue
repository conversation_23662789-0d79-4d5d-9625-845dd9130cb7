<template>
  <div>
    <t-space direction="vertical" style="width: 100%; height: 100%">
      <t-tabs v-model="value">
        <t-tab-panel value="dbrw" label="待办任务" :destroyOnHide="false">
          <t-space class="box" style="display: block">
            <div class="kjcd_list" v-for="dbrw in dbrwList" :key="dbrw.id" :title="dbrw.bt">
              <div class="wrap">
                <div>
                  <img src="../../../assets/xxzx.png" alt="" />
                </div>
                <div>
                  <div class="nrTop">
                    <div class="bt">{{ dbrw.bt }}</div>
                    <div class="sj">{{ dbrw.sj }}</div>
                  </div>
                  <div class="nr">{{ dbrw.nr }}</div>
                </div>
              </div>
            </div>
          </t-space>
        </t-tab-panel>
        <t-tab-panel value="ybrw" label="已办任务" :destroyOnHide="false">
          <t-space class="box" style="display: block">
            <div class="kjcd_list" v-for="dbrw in dbrwList" :key="dbrw.id" :title="dbrw.bt">
              <div class="wrap">
                <div>
                  <img src="../../../assets/xxzx.png" alt="" />
                </div>
                <div>
                  <div class="nrTop">
                    <div class="bt">{{ dbrw.bt }}</div>
                    <div class="sj">{{ dbrw.sj }}</div>
                  </div>
                  <div class="nr">{{ dbrw.nr }}</div>
                </div>
              </div>
            </div>
          </t-space>
        </t-tab-panel>
        <t-tab-panel value="bjrw" label="办结任务" :destroyOnHide="false">
          <t-space class="box" style="display: block">
            <div class="kjcd_list" v-for="dbrw in dbrwList" :key="dbrw.id" :title="dbrw.bt">
              <div class="wrap">
                <div>
                  <img src="../../../assets/xxzx.png" alt="" />
                </div>
                <div>
                  <div class="nrTop">
                    <div class="bt">{{ dbrw.bt }}</div>
                    <div class="sj">{{ dbrw.sj }}</div>
                  </div>
                  <div class="nr">{{ dbrw.nr }}</div>
                </div>
              </div>
            </div>
          </t-space>
        </t-tab-panel>
      </t-tabs>
    </t-space>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dbrwList: [
        {
          id: '1',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
        {
          id: '2',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
        {
          id: '3',
          bt: '您有一条待审批任务',
          sj: '2024-04-17',
          nr: '测试公司A提交了消费税申报表，请尽快审批。',
        },
      ],
      value: 'dbrw',
    };
  },
};
</script>
<style>
.wrap {
  display: flex;
  height: 50px;
  margin: 8px 10px;
  justify-content: space-between;
  align-items: center;
}
.nrTop {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.kjcd_list {
  display: block;
  float: left;
  width: 100%;
  text-align: center;
}

.bt {
  height: 24px;
  font-family: PingFangSC-Medium;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0;
  color: #333;
}
.sj {
  height: 22px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0;
  color: #999;
}
.nr {
  height: 22px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0;
  color: #999;
}
</style>
