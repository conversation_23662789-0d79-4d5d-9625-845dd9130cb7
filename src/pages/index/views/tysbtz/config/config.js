/*
 * @Author: liumu
 * @Date: 2025-03-28 11:09:03
 * @LastEditTime: 2025-05-16 09:11:43
 * @LastEditors: liumu
 * @Description: 描述
 */
export const ghjftzQuerySearchConfig = [
  {
    label: '费款所属期起',
    type: 'range-datepicker',
    key: 'skssqq',
    placeholder: '请输入费款所属期起',
    clearable: true,
  },
  {
    label: '费款所属期止',
    type: 'range-datepicker',
    key: 'skssqz',
    placeholder: '请输入费款所属期止',
    clearable: true,
  },
  // {
  //   label: '缴纳义务人纳税人识别号（社会信用代码）',
  //   key: 'nsrsbh;',
  //   placeholder: '请输入缴纳义务人纳税人识别号',
  //   clearable: true,
  // },
];
export const ghjftzColumns = [
  {
    colKey: 'index',
    title: '序号',
    align: 'center',
    fixed: 'left',
    width: '50',
  },
  {
    colKey: 'nsrsbh',
    title: '缴费人识别号（统一社会信用代码）',
    align: 'center',
    width: '200',
  },
  {
    colKey: 'nsrmc',
    title: '缴纳义务人名称',
    align: 'center',
    width: '200',
  },
  {
    colKey: 'skssqq',
    title: '费款所属期起',
    align: 'center',
    width: '100',
  },
  {
    colKey: 'skssqz',
    title: '费款所属期止',
    align: 'center',
    width: '100',
  },
  {
    colKey: 'zsxmmc',
    title: '征收项目',
    align: 'center',
    width: '150',
  },
  {
    colKey: 'zspmmc',
    title: '征收品目',
    align: 'center',
    width: '150',
  },
  {
    colKey: 'zszmmc',
    title: '征收子目',
    align: 'center',
    width: '150',
  },
  {
    colKey: 'jsyj',
    title: '应税项',
    align: 'center',
    width: '100',
  },
  // {
  //   colKey: '',
  //   title: '税（费）率或单位税额',
  //   align: 'center',
  //   width: '200',
  // },
  // {
  //   colKey: '',
  //   title: '本期应补（退）费额',
  //   align: 'center',
  //   width: '200',
  // },
  {
    colKey: 'cz',
    title: '操作',
    align: 'center',
    fixed: 'right',
    width: '200',
  },
];
