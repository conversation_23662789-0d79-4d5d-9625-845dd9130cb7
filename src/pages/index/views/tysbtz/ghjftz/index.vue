<!--
 * @Author: liumu
 * @Date: 2025-03-26 14:25:43
 * @LastEditTime: 2025-07-25 15:24:30
 * @LastEditors: liumu
 * @Description: 描述
-->
<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="content">
      <search-control-panel
        class="searchBar"
        ref="searchform"
        :config="ghjftzQuerySearchConfig"
        @search="searchList()"
        :colNum="4"
        @formChange="(v) => (searchFormData = v)"
      />
      <div class="btnBar">
        <gt-space size="10px">
          <t-button theme="primary" @click="xz()"><AddIcon slot="icon" />新建</t-button>
          <!-- <t-upload
            :action="drurl"
            v-model="files"
            theme="custom"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, .xlsx, .xls"
            @fail="handlerFail"
            @success="handlerSuccess"
          >
            <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入</t-button>
          </t-upload> -->
          <t-dropdown
            :options="[
              { content: '下载模版', value: 1, onClick: () => xzmb() },
              { content: '导入数据', value: 2, onClick: () => importExcel() },
            ]"
          >
            <t-button variant="outline" theme="primary"> <UploadIcon slot="icon" /><span>导入</span> </t-button>
          </t-dropdown>
          <!-- <t-button theme="primary" @click="xzmb()"><DownloadIcon slot="icon" />下载模板</t-button>
          <t-button theme="primary" @click="qsb()"><LinkIcon slot="icon" />去申报</t-button> -->
        </gt-space>
      </div>
      <div class="tableBox">
        <t-table
          verticalAlign="middle"
          :data="tableData"
          :columns="ghjftzColumns"
          row-key="uuid"
          :pagination="pagination"
          @change="handlePageChange"
          height="63vh"
          :loading="tableDataLoading"
        >
          <template #cz="slotProps">
            <!-- <a href="javascript:void(0);" class="link" style="margin-left: 8px" @click="ck(slotProps)">查看</a> -->
            <a href="javascript:void(0);" class="link" style="margin-left: 8px" @click="bj(slotProps)">编辑</a>
            <t-popconfirm content="确认删除吗" @confirm="sc(slotProps)">
              <a href="javascript:void(0);" class="link" style="margin-left: 8px">删除</a>
            </t-popconfirm>
          </template>
          <template #empty>
            <div class="img">
              <div class="text">暂无数据</div>
            </div>
          </template>
        </t-table>
      </div>
      <t-dialog
        class="dialog"
        :header="dialog1HeadName"
        width="1000px"
        style="height: 100%"
        :visible.sync="dialog1.visible"
        @close="closedialog"
        confirmBtn="保存"
        destroyOnClose
        :closeOnOverlayClick="false"
      >
        <template #body>
          <div class="dialogBody">
            <div class="nr">
              <t-form ref="form" :data="dialog1.formData" class="form-1" :rules="dialog1.rules" :label-width="200">
                <t-form-item
                  label="缴费人识别号（统一社会信用代码）"
                  label-width="120px"
                  label-align="top"
                  name="nsrsbh"
                  :requiredMark="dialog1.czlx !== 'ck'"
                  class="form-item-1"
                >
                  <t-input class="dol-input" :disabled="true" v-model="dialog1.formData.nsrsbh"></t-input>
                </t-form-item>
                <t-form-item
                  label="缴纳义务人名称"
                  name="nsrmc"
                  label-width="120px"
                  label-align="top"
                  :requiredMark="dialog1.czlx !== 'ck'"
                  class="form-item-1"
                >
                  <t-input class="dol-input" :disabled="true" v-model="dialog1.formData.nsrmc"></t-input>
                </t-form-item>
                <t-form-item
                  label="纳税期限"
                  label-width="120px"
                  label-align="top"
                  name="nsqxDm"
                  :requiredMark="dialog1.czlx !== 'ck'"
                  class="form-item-1"
                >
                  <t-select
                    class="dol-input"
                    :disabled="dialog1.czlx === 'ck'"
                    v-model="dialog1.formData.nsqxDm"
                    :options="sbqxOptions"
                    @change="selectNsqxChange"
                  ></t-select>
                </t-form-item>
                <t-form-item
                  label="费款所属期起"
                  label-width="120px"
                  label-align="top"
                  name="skssqq"
                  class="form-item-1"
                  :requiredMark="dialog1.czlx !== 'ck'"
                >
                  <t-date-picker
                    class="dol-input"
                    v-model="dialog1.formData.skssqq"
                    :disabled="dialog1.czlx === 'ck'"
                    @change="jsNsqx"
                  ></t-date-picker>
                </t-form-item>
                <t-form-item
                  label="费款所属期止"
                  label-width="120px"
                  label-align="top"
                  name="skssqz"
                  class="form-item-1"
                  :requiredMark="dialog1.czlx !== 'ck'"
                >
                  <t-date-picker
                    class="dol-input"
                    v-model="dialog1.formData.skssqz"
                    :disabled="dialog1.czlx === 'ck'"
                    @change="jsNsqx"
                  ></t-date-picker>
                </t-form-item>
                <t-form-item
                  label="征收项目"
                  label-width="120px"
                  label-align="top"
                  name="zsxmDm"
                  :requiredMark="dialog1.czlx !== 'ck'"
                  class="form-item-1"
                >
                  <t-select
                    class="dol-input"
                    :disabled="dialog1.czlx === 'ck'"
                    v-model="dialog1.formData.zsxmDm"
                    :options="zsxmOptions"
                    @change="selectZsxmChange"
                  ></t-select>
                </t-form-item>
                <t-form-item
                  label="征收品目"
                  label-width="120px"
                  label-align="top"
                  name="zspmDm"
                  :requiredMark="dialog1.czlx !== 'ck'"
                  class="form-item-1"
                >
                  <t-select
                    class="dol-input"
                    :loading="zspmLoading"
                    :disabled="dialog1.czlx === 'ck'"
                    v-model="dialog1.formData.zspmDm"
                    :options="zspmOptions"
                    @change="selectZspmChange"
                  ></t-select>
                </t-form-item>
                <t-form-item label="征收子目" label-width="120px" label-align="top" name="zszmDm" class="form-item-1">
                  <t-select
                    class="dol-input"
                    :disabled="dialog1.czlx === 'ck'"
                    v-model="dialog1.formData.zszmDm"
                    :options="zszmOptions"
                    @change="selectZszmChange"
                  ></t-select>
                </t-form-item>
                <t-form-item
                  label="应税额"
                  label-width="120px"
                  label-align="top"
                  name="jsyj"
                  requiredMark
                  class="form-item-1"
                >
                  <t-input
                    class="dol-input"
                    :disabled="dialog1.czlx === 'ck'"
                    v-model.number="dialog1.formData.jsyj"
                  ></t-input>
                </t-form-item>
              </t-form>
            </div>
          </div>
        </template>
        <template #footer>
          <div style="position: relative; height: 5vh">
            <div
              class="dialogFooter"
              style="position: absolute; right: 0; bottom: 0; z-index: 999; padding: 11px"
              v-if="dialog1.czlx !== 'ck'"
            >
              <!-- <t-button theme="primary" @click="save">保存</t-button> -->
              <t-button theme="default" variant="base" @click="closedialog">取消</t-button>
              <t-button theme="primary" @click="commit">提交</t-button>
            </div>
          </div>
        </template>
      </t-dialog>
    </div>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import api from '@/pages/index/api/tysbtz/ghjftz/index.js';
import { AddIcon, UploadIcon } from 'tdesign-icons-vue';
import axios from 'axios';
import { ghjftzQuerySearchConfig, ghjftzColumns } from '../config/config.js';

export default {
  name: 'ghjftz',
  components: { Mybreadcrumb, SearchControlPanel, UploadIcon, AddIcon },
  data() {
    return {
      ghjftzQuerySearchConfig,
      ghjftzColumns,
      drurl: `${window.STATIC_ENV_CONFIG.API_PREFIX}/fssrTzzx/tysb/importData`,
      searchFormData: {},
      tableData: [],
      files: [],
      // tableSelectOptions: {
      //   type: 'multiple',
      //   selectedRowKeys: [],
      //   selectedRows: [],
      // },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      tableDataLoading: false,
      jgxx: {},
      dialog1: {
        visible: false,
        czlx: 'xz',
        formData: {},
        rules: {
          nsrsbh: [{ required: true }],
          nsrmc: [{ required: true }],
          nsqxDm: [{ required: true }],
          skssqq: [{ required: true }],
          skssqz: [{ required: true }],
          zsxmDm: [{ required: true }],
          zspmDm: [{ required: true }],
          jsyj: [
            { required: true, message: '应税额不能为空', trigger: 'blur' },
            { number: true, message: '请输入数字', trigger: 'blur' },
          ],
        },
      },
      zsxmOptions: [
        {
          value: '30433',
          label: '建设行政事业性收费收入',
        },
        {
          value: '39900',
          label: '其他收入',
        },
      ],
      zspmOptions: [],
      zszmOptions: [],
      sbqxOptions: [
        {
          value: '11',
          label: '次',
        },
        {
          value: '06',
          label: '月',
        },
        {
          value: '08',
          label: '季',
        },
        {
          value: '10',
          label: '年',
        },
      ],
      zspmLoading: false,
    };
  },
  computed: {
    dialog1HeadName() {
      if (this.dialog1.czlx === 'xz') {
        return '新增';
      }
      if (this.dialog1.czlx === 'bj') {
        return '编辑';
      }
      if (this.dialog1.czlx === 'ck') {
        return '查看';
      }
      return '';
    },
  },
  mounted() {
    const parmObj = {
      mybreadList: ['首页', '通用申报应税台账'],
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    this.$refs.myBre.initMyBre(parmObj);
    this.initData();
    this.searchList();
  },
  methods: {
    async initData() {},
    // handleSelectChange(selectedRowKeys, selectedRows) {
    //   this.tableSelectOptions.selectedRows = selectedRows.selectedRowData;
    // },
    async searchList() {
      const self = this;
      this.jgxx = JSON.parse(sessionStorage.getItem('jgxxList'));
      this.dialog1.formData.nsrsbh = this.jgxx.nsrsbh;
      let nsrmc = this.jgxx.jgmc;
      if (this.jgxx.jgmc.indexOf('|') !== -1) {
        // eslint-disable-next-line prefer-destructuring
        nsrmc = this.jgxx.jgmc.split('|')[1];
      }
      this.dialog1.formData.nsrmc = nsrmc;
      const params = {
        ...this.searchFormData,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
        nsrsbh: this.jgxx?.nsrsbh,
        djxh: this.jgxx?.djxh,
      };
      const res = await api.initData(params);
      if (res.code === 1) {
        this.tableData = res.data.list;
        if (res.data.list.size <= this.pagination.pageSize) {
          this.pagination.current = 1;
        }
        this.tableData.map((item, i) => {
          item.index = (self.pagination.current - 1) * self.pagination.pageSize + i + 1;
        });
        this.pagination.total = res.data.total;
      } else {
        this.$message.error(res.msg);
      }
    },
    // 分页变化时的回调
    async handlePageChange({ pagination }) {
      this.pagination = { ...this.pagination, current: pagination.current, pageSize: pagination.pageSize };
      this.searchList(); // 重新加载数据
    },
    xz() {
      this.dialog1.formData = { nsrmc: this.dialog1.formData.nsrmc, nsrsbh: this.dialog1.formData.nsrsbh };
      this.dialog1.zspmOptions = [];
      this.dialog1.zszmOptions = [];
      this.dialog1.czlx = 'xz';
      this.dialog1.visible = true;
    },
    xzmb() {
      const url = `${window.STATIC_ENV_CONFIG.API_PREFIX}/fssrTzzx/tysb/downloadTemplate`;
      window.open(url, '_blank');
    },
    qsb() {
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/nssb/sbrwmx`;
        window.parent.goSelfChange(menuParams);
      }
    },
    ck({ row }) {
      this.dialog1.formData = row;
      this.dialog1.czlx = 'ck';
      this.loadZspmOptions();
      this.dialog1.visible = true;
    },
    bj({ row }) {
      this.dialog1.formData = row;
      this.dialog1.czlx = 'bj';
      this.loadZspmOptions();
      this.dialog1.visible = true;
    },
    sc({ row }) {
      api
        .delete({
          ...row,
        })
        .then((res) => {
          if (res.code === 1) {
            this.$message.success('删除成功');
            if (this.pagination.total > 0) {
              this.pagination.total -= 1;
            }
            this.searchList();
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    getJguuid(data) {
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      this.searchList();
    },
    // dialog1
    closedialog() {
      this.dialog1.visible = false;
      this.searchList();
    },
    async commit() {
      const formValidate = await this.$refs.form.validate();
      console.log(formValidate);
      if (formValidate !== true) {
        return;
      }
      const jgxx = JSON.parse(sessionStorage.getItem('jgxxList'));
      const res = await api.saveOrUpdate({
        ...this.dialog1.formData,
        djxh: jgxx.djxh,
        xzqhszDm: jgxx?.xzqhszDm,
        zgswskfjDm: jgxx?.zgswskfjDm,
      });
      if (res.code === 1) {
        this.$message.success(`保存成功`);
        this.closedialog();
      } else {
        this.$message.error(res.msg);
      }
    },
    handlerFail(res) {
      this.$message.error('上传失败');
    },
    handlerSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response.data?.code === '1') {
        this.$message.success(`导入成功`);
      } else {
        this.$message.error({
          content: this.$createElement('div', {
            domProps: {
              innerHTML: res.response.data?.msg,
            },
          }),
          dangerouslyUseHTMLString: true,
        });
      }
      this.files = [];
      this.searchList();
    },
    async loadZspmOptions() {
      if (!this.dialog1.formData.zsxmDm) {
        return;
      }
      this.zspmLoading = true;
      try {
        const zspmRes = await api.hqzspmzm({
          zsxmDm: this.dialog1.formData.zsxmDm,
          skssqq: this.dialog1.formData.skssqq,
          skssqz: this.dialog1.formData.skssqz,
          djxh: this.jgxx.djxh,
          nsrsbh: this.jgxx.nsrsbh,
        });
        this.zspmOptions = [];
        if (zspmRes.code === 1) {
          zspmRes.data.forEach((item) => {
            if (this.dialog1.formData.zspmDm === item.zspmDm) {
              this.loadZszmOptions(item.zszmList);
            }
            this.zspmOptions.push({
              value: item.zspmDm,
              label: item.zspmmc,
              zszmList: item.zszmList,
            });
          });
        } else {
          this.$message.error(zspmRes.msg);
        }
      } finally {
        this.zspmLoading = false;
      }
    },
    async selectZsxmChange(value, { selectedOptions }) {
      if (!this.dialog1.formData.skssqq || !this.dialog1.formData.skssqz) {
        this.$message.error('请选择起止日期');
        return;
      }
      this.dialog1.formData.zsxmmc = selectedOptions[0].label;
      this.$set(this.dialog1.formData, 'zspmDm', '');
      this.$set(this.dialog1.formData, 'zszmDm', '');
      this.$set(this.dialog1.formData, 'zspmmc', '');
      this.$set(this.dialog1.formData, 'zszmmc', '');
      this.zspmOptions = [];
      this.zszmOptions = [];
      this.loadZspmOptions();
    },
    loadZszmOptions(zszmList) {
      this.zszmOptions = [];
      const zszmOptions = zszmList;
      zszmOptions.forEach((item) => {
        this.zszmOptions.push({
          value: item.zszmDm,
          label: item.zszmmc,
        });
      });
    },
    selectZspmChange(value, { selectedOptions }) {
      console.log(selectedOptions[0].zszmList);
      this.dialog1.formData.zspmmc = selectedOptions[0].label;
      this.$set(this.dialog1.formData, 'zszmDm', '');
      this.$set(this.dialog1.formData, 'zszmmc', '');
      this.loadZszmOptions(selectedOptions[0].zszmList);
    },
    selectZszmChange(value, { selectedOptions }) {
      this.dialog1.formData.zszmmc = selectedOptions[0].label;
      this.dialog1.formData.zszmDm = value;
    },
    jsNsqx(date) {
      if (!this.dialog1.formData.nsqxDm) {
        this.$message.error('请选择纳税期限');
        return;
      }
      const skssq = this.getSbqx(date, this.dialog1.formData.nsqxDm);
      this.$set(this.dialog1.formData, 'skssqq', skssq[0]);
      this.$set(this.dialog1.formData, 'skssqz', skssq[1]);
    },
    selectNsqxChange() {
      const date = new Date();
      if (this.dialog1.formData.nsqxDm === '06') {
        // 纳税期限选择月时，取上1个月
        date.setMonth(date.getMonth() - 1);
      } else if (this.dialog1.formData.nsqxDm === '08') {
        // 纳税期限选择季时，取上3个月
        date.setMonth(date.getMonth() - 3);
      } else if (this.dialog1.formData.nsqxDm === '10') {
        // 纳税期限选择季时，取上12个月
        date.setMonth(date.getMonth() - 12);
      }
      console.log('jsnsqx date:', date);
      this.jsNsqx(date);
      this.$set(this.dialog1.formData, 'zspmDm', '');
      this.$set(this.dialog1.formData, 'zszmDm', '');
      this.$set(this.dialog1.formData, 'zspmmc', '');
      this.$set(this.dialog1.formData, 'zszmmc', '');
      this.zspmOptions = [];
      this.zszmOptions = [];
      this.loadZspmOptions();
    },
    getSbqx(startDate, type) {
      const date = new Date(startDate);
      if (Number.isNaN(date.getTime())) {
        throw new Error('无效日期');
      }

      // 辅助函数：将 Date 对象转为 YYYY-MM-DD 字符串
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      switch (type) {
        case '11': {
          const dayStr = formatDate(date);
          return [dayStr, dayStr];
        }
        case '06': {
          const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
          const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);
          return [formatDate(monthStart), formatDate(monthEnd)];
        }
        case '08': {
          const quarter = Math.floor(date.getMonth() / 3);
          const quarterStart = new Date(date.getFullYear(), quarter * 3, 1);
          const quarterEnd = new Date(date.getFullYear(), (quarter + 1) * 3, 0);
          return [formatDate(quarterStart), formatDate(quarterEnd)];
        }
        case '10': {
          const yearStart = new Date(date.getFullYear(), 0, 1);
          const yearEnd = new Date(date.getFullYear() + 1, 0, 0);
          return [formatDate(yearStart), formatDate(yearEnd)];
        }
        default:
          throw new Error('无效的纳税期限类型');
      }
    },
    // 导入
    importExcel() {
      // 创建隐藏的文件输入元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.xls,.xlsx';
      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        const formData = new FormData();
        formData.append('file', file);
        try {
          this.dcLoading = true;
          const { data } = await axios.post(this.drurl, formData);
          const data1 = data?.data;
          if (data1?.code === '1') {
            this.$message.success('导入成功');
          } else {
            this.$message.error({
              content: this.$createElement('div', {
                domProps: {
                  innerHTML: data1?.msg,
                },
              }),
              dangerouslyUseHTMLString: true,
            });
          }
          this.searchList(); // 刷新表格数据
        } catch (error) {
          this.$message.error('导入失败');
        } finally {
          this.dcLoading = false;
          input.remove(); // 清理DOM元素
        }
      };
      input.click(); // 触发文件选择
    },
  },
};
</script>

<style scoped lang="less">
.mainbody {
  height: 100vh;
  background-color: #eee;
  .content {
    margin: 10px;
    background-color: white;
    .searchBar {
      /deep/.t-row--align-top {
        display: flex;
        height: 10vh;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
      }
      /deep/.t-form__item {
        margin: 0 0 0 10% !important;
      }
    }
    .searchBar::after {
      position: absolute;
      top: 127px;
      right: 20px;
      left: 20px;
      z-index: 999;
      height: 0.3px;
      margin: 10px 0;
      background-color: #eee;
      content: '';
    }
    .btnBar {
      display: flex;
      height: 7vh;
      .gov-space {
        display: flex;
        padding-left: 3vh;
        align-content: space-between;
        justify-content: flex-start;
        align-items: center;
      }
    }
    .tableBox {
      padding: 10px;
      /deep/ .link {
        color: #4285f4;
        text-decoration: none;
      }
    }
  }
  .dialog {
    /deep/ .t-dialog--default {
      padding: 0;
    }
    /deep/ .t-dialog__header {
      padding: 15px;
      background: rgba(66, 133, 244, 0.06274509803921569);
    }
    /deep/ .t-dialog__header::after {
      position: absolute;
      top: 44px;
      right: 0;
      left: 0;
      z-index: 1008;
      height: 0.3px;
      margin: 10px 0;
      background-color: #eee;
      content: '';
    }
    .dialogBody {
      .nr {
        height: 100%;
        padding: 0 10px;
        .form-1 {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(32%, 1fr));
          .dol-input {
            width: 100%;
            padding: 10px;
          }
        }
      }
    }
    .dialogBody::after {
      position: absolute;
      top: 396px;
      right: 0;
      left: 0;
      z-index: 1008;
      height: 0.3px;
      margin: 10px 0;
      background-color: #eee;
      content: '';
    }
    .dialogFooter {
    }
  }
}
</style>
