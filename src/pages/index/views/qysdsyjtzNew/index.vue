<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="minbox" style="display: flex">
      <TzMenu />
      <div class="tzzt">
        <gt-collapse-menu
          class="ggMenu"
          theme="primary"
          title="台账列表"
          :list="list"
          expandMutex
          :defaultExpanded="expanded"
          :default-value="defaultValue"
          :toolbar="false"
          :value="active"
          @change="collapseHandle"
        >
          <template #panel>
            <qysdsyjtz ref="qysdsyjtz" v-show="active === 'qysdsyjtz'" />
            <fzjgfztz ref="fzjgfztz" v-show="active === 'fzjgfztz'" />
          </template>
          <!-- <template #menu-item>menu-item</template> -->
        </gt-collapse-menu>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import TzMenu from '@/pages/index/components/NewTzMenu';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import qysdsyjtz from '@/pages/index/views/qysdsyjtzNew/components/qysdsyjtz/index.vue';
import fzjgfztz from '@/pages/index/views/qysdsyjtzNew/components/fzjgfztz/index.vue';
import { CollapseMenu } from '@gt4/common-front';
import {
  initComputeSszq,
  isProductEnv,
  isYsEnv,
  stopPollingQysdsyj,
  // getCompanyDifferentiationConfig,
} from '@/pages/index/views/util/tzzxTools.js';

export default {
  components: { GtCollapseMenu: CollapseMenu, TzMenu, qysdsyjtz, fzjgfztz, Mybreadcrumb },
  data() {
    return {
      expanded: ['1'],
      defaultValue: 'qysdsyjtz',
      active: 'qysdsyjtz',
      sbrwmxbz: this.$route.query.sbrwmxBz || false,
      sbrwmxAbbbz: this.$route.query.sbrwmxAbbBz || false,
      list: [
        {
          id: '1',
          title: '企业所得税预缴台账',
          children: [
            {
              id: 'qysdsyjtz',
              title: '企业所得税预缴台账',
              required: false,
            },
            {
              id: 'fzjgfztz',
              title: '分支机构辅助台账',
              required: false,
            },
          ],
        },
      ],
    };
  },
  created() {
    initComputeSszq();
    isProductEnv();
    isYsEnv();
    // 消息跳转增值税台账时的暂时处理
    if (Object.keys(this.$route.query).length === 1 && 'jguuid' in this.$route.query) {
      // 有且仅有一个jguuid参数时的处理逻辑
      this.sbrwmxbz = true; // 默认为false
    }
    // 用于记录页面是否通过申报更正功能跳转进入
    if (Object.keys(this.$route.query).length > 1 && 'gzsbbz' in this.$route.query) {
      this.$store.commit('sbzt/setSbgzEnterFlag', true);
      const sbgzSsyfInfo = {
        ssyfq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
        ssyfz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
      };
      this.$store.commit('sbzt/setSbgzSsyfInfo', sbgzSsyfInfo);
    } else {
      this.$store.commit('sbzt/setSbgzEnterFlag', false);
      this.$store.commit('sbzt/setSbgzSsyfInfo', {});
    }
    if (this.$route.query.jguuid) {
      // sessionStorage.setItem(
      //   'companyList',
      //   '[{"jguuid":"60135693bf4b4d8abe3e296a790980b7","jgmc":"演示集团北京分公司2","xzqhmc":"锡林郭勒盟","nsrsbh":"9144030070846113CS","djxh":"10111525000001030002","xzqhszDm":"152500","qydmz":"","nsrlx":"1"},{"jguuid":"1ab6136148ff41d192a5031fbf40b210","jgmc":"演示集团北京分公司3","xzqhmc":"东城区","nsrsbh":"9111000071093573CS","djxh":"10111525000001030003","xzqhszDm":"110101","qydmz":"","nsrlx":null},{"jguuid":"deca7b120f024f54bf05892f4a859b3f","jgmc":"演示集团北京分公司4","xzqhmc":null,"nsrsbh":"9111000010001770CS","djxh":"10111525000001030004","xzqhszDm":null,"qydmz":"","nsrlx":null},{"jguuid":"feb2726dd8354ab1a629706fea443d0e","jgmc":"10001|四川省新晨忆然文化发展有限公司","xzqhmc":"四川省","nsrsbh":"91510321MA6205QK2R","djxh":"10115103010000010576","xzqhszDm":"510000","qydmz":"10001","nsrlx":"2"},{"jguuid":"5f5dd426d8ce411db5ccfb99a2491231","jgmc":"11|陕西亿道新能源科技有限公司","xzqhmc":"未央区","nsrsbh":"91610133MA6TXHHD53","djxh":"10116101000051606503","xzqhszDm":"610112","qydmz":"11","nsrlx":"4"},{"jguuid":"f7cbe421265240648183ed507868fe9d","jgmc":"1100|上海森马服饰有限公司","xzqhmc":"上海市","nsrsbh":"91310112773731630K","djxh":"10013101001120017369","xzqhszDm":"310000","qydmz":"1100","nsrlx":"1"},{"jguuid":"5f5dd426d8ce411db5ccfb99a2490e0c","jgmc":"11000|演示集团北京分公司1","xzqhmc":"锡林郭勒盟","nsrsbh":"91310000MA1FL70BCS","djxh":"10111525000001030001","xzqhszDm":"152500","qydmz":"11000","nsrlx":"1"},{"jguuid":"znsb_mhqx_jgxxb_uuid_01","jgmc":"1111|千户集团阿里云测试账户（勿删）","xzqhmc":"安徽省","nsrsbh":"91340300711791371R","djxh":"10113403000106935062","xzqhszDm":"340000","qydmz":"1111","nsrlx":"1"},{"jguuid":"5c37020b3af04dc99a6b77645d8d7aba","jgmc":"1111|增值税一般纳税人成品油测试企业","xzqhmc":"锡林郭勒盟","nsrsbh":"91310000MA2SB002CS","djxh":"10111525000003330002","xzqhszDm":"152500","qydmz":"1111","nsrlx":"1"},{"jguuid":"0488f98ef802439fb28efb41423e3392","jgmc":"1111|安徽测试2","xzqhmc":"安徽省","nsrsbh":"91341024151783006H","djxh":"10213410000000135383","xzqhszDm":"340000","qydmz":"1111","nsrlx":"2"},{"jguuid":"5f5dd426d8ce411db5ccfb99a2490e01","jgmc":"1111|演示集团北京分公司qhjt","xzqhmc":"锡林郭勒盟","nsrsbh":"91310000MA1FL70BCS","djxh":"10210000000788572","xzqhszDm":"152500","qydmz":"1111","nsrlx":null},{"jguuid":"cd5be1206d6f41d1aba1bf04459cfcc3","jgmc":"1111|演示集团天津分公司1","xzqhmc":null,"nsrsbh":"9144000010000589CS","djxh":"10111525000001030005","xzqhszDm":null,"qydmz":"1111","nsrlx":null},{"jguuid":"d5f6825bfbc0494b86e5a026e95b6be3","jgmc":"1111|演示集团天津分公司2","xzqhmc":null,"nsrsbh":"91310000MA1FL701CS","djxh":"10111525000001030006","xzqhszDm":null,"qydmz":"1111","nsrlx":null},{"jguuid":"fb387d5b03b04da6b863aa98b2f2d271","jgmc":"1111|爱立信（中国）通信有限公司西安分公司","xzqhmc":"广东省","nsrsbh":"91610000773814479K","djxh":"10116101000051858660","xzqhszDm":"440000","qydmz":"1111","nsrlx":null},{"jguuid":"8uffk40569214ebeb143e210f12hdf87","jgmc":"1111|纳税人591632942","xzqhmc":"广东省","nsrsbh":"535AA730185CCDC","djxh":"10214406000000001412","xzqhszDm":"440000","qydmz":"1111","nsrlx":"1"},{"jguuid":"f7cbe421265240648183ed507868fe9e","jgmc":"1199|上海森马服饰有限公司(跨区税源户)","xzqhmc":"上海市","nsrsbh":"91310112773731630K","djxh":"10013101001120017370","xzqhszDm":"310000","qydmz":"1199","nsrlx":"1"},{"jguuid":"1111020569214ebeb143e210fbb00000","jgmc":"12312|朱行个体测试账号001","xzqhmc":"广东省","nsrsbh":"245234230100000","djxh":"10214406000000018082","xzqhszDm":"440000","qydmz":"12312","nsrlx":null},{"jguuid":"sichuanlqcs000000000000000000003","jgmc":"1234321|南充南百大珠宝有限公司","xzqhmc":"四川省","nsrsbh":"91511302791806982U","djxh":"10115113000069417805","xzqhszDm":"510000","qydmz":"1234321","nsrlx":"1"},{"jguuid":"1230f5dbd4fb40c98240c9816e69d123","jgmc":"2300|浙江华人实业发展有限公司","xzqhmc":"浙江省","nsrsbh":"9133000071256068XH","djxh":"10113301000046821733","xzqhszDm":"330000","qydmz":"2300","nsrlx":null}]',
      // );
      const companyList = JSON.parse(window.sessionStorage.getItem('companyList'));
      const userInfo = companyList.find((item) => item.jguuid === this.$route.query.jguuid);
      if (userInfo) {
        this.$store.commit('zzstz/setUserInfoData', userInfo);
      } else {
        this.$store.commit('zzstz/setUserInfoData', {
          jguuid: this.$route.query.jguuid,
          jgmc: this.$route.query.jgmc,
          xzqhmc: this.$route.query.xzqhmc,
          nsrsbh: this.$route.query.nsrsbh,
          djxh: this.$route.query.djxh,
          xzqhszDm: this.$route.query.xzqhszDm,
          qydmz: this.$route.query.qydmz,
          nsrlx: this.$route.query.nsrlx,
        });
      }
    } else {
      const userInfo = window.sessionStorage.getItem('jgxxList');
      this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    }
    // if (window.location.search) {
    //   // 暂时默认从申报明细跳转
    //   this.sbrwmxbz = true;
    // }
    // getCompanyDifferentiationConfig({
    //   djxh: this.$store.state.zzstz.userInfo.djxh,
    //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
    // });
    // const userInfo = window.sessionStorage.getItem('jgxxList');
    // this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  methods: {
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true };
      this.$refs[val].query(params);
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      stopPollingQysdsyj();
      // // 使用 await 等待 getCompanyDifferentiationConfig 完成
      // await getCompanyDifferentiationConfig({
      //   djxh: this.$store.state.zzstz.userInfo.djxh,
      //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      // });
      // // 确保状态更新完成
      // await this.$nextTick();
      const params = { flag: true };
      this.$refs[this.active].init();
      this.$refs[this.active].query(params);
      this.$refs[this.active]?.getLrzx();
    },
    // data不传的话默认用子页面的查询参数。notQuery传true时子页面不触发查询。from为父页面activ，用于子页面返回对应父页面，按需将from储存在子页面。
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) this.$refs[type].query({ flag, p: data, from });
      this.active = type;
    },
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '企业所得税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务进
    const parmObjSbrw = {
      mybreadList: ['首页', '申报概览', '税费申报', '企业所得税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmx'],
      goBackPath: '/znsb/view/nssb/sbrwmx', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务(报表)进
    const parmObjSbrwAbb = {
      mybreadList: ['首页', '申报概览', '税费申报', '企业所得税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmxAbb'],
      goBackPath: '/znsb/view/nssb/sbrwmxAbb', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    if (this.sbrwmxbz) {
      this.$refs.myBre.initMyBre(parmObjSbrw);
    } else if (this.sbrwmxAbbbz) {
      this.$refs.myBre.initMyBre(parmObjSbrwAbb);
    } else {
      this.$refs.myBre.initMyBre(parmObjDhcd);
    }
    // this.$refs.myBre.initMyBre(parmObjDhcd);
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
  /deep/.gt-collapse-menu-content-unfold {
    margin-left: 0 !important;
  }
  /deep/.gt-collapse-menu-sidebar-unfold {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content .t-default-menu {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar {
    display: block;
  }
}
.tzzt {
  width: calc(100% - 50px);
}
.ggMenu {
  border-left: 1px solid #eee;
  /deep/.gt-collapse-menu-sidebar-header__title {
    font-size: 14px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header {
    line-height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header-unfold {
    height: 55px !important;
  }
  /deep/.t-menu__item {
    height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content {
    height: calc(100% - 55px) !important;
  }
}
</style>
