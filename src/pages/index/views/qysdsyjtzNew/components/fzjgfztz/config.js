import dayjs from 'dayjs';

// 计算默认所属月份起和止
export function Ssyf() {
  const year = dayjs().format('YYYY-MM').substring(0, 4);
  const month = dayjs().format('YYYY-MM').substring(5, 7);
  let ssyfq;
  let ssyfz;
  switch (month) {
    case '01':
      ssyfq = `${(parseInt(year, 10) - 1).toString()}-10`;
      ssyfz = `${(parseInt(year, 10) - 1).toString()}-12`;
      break;
    case '02':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '03':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '04':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '05':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '06':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '07':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '08':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '09':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '10':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '11':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    case '12':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    default:
      break;
  }
  return { ssyfq, ssyfz };
}

export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: Ssyf().ssyfq,
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: Ssyf().ssyfz,
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqq',
    timeRange: 'end',
  },
  {
    label: '',
    key: 't1',
  },
];
