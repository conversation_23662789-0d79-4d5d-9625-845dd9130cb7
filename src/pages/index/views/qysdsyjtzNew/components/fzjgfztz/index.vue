<!--
 * @Descripttion: 台账-分支机构辅助台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-10-11 11:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
        <template #t1><span></span></template>
      </search-control-panel>
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" :ywlx="'fzjgfztz'" @query="query" />
          <t-button theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
            ><DownloadIcon slot="icon" />导出</t-button
          >
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="key"
          :data="tableData"
          :columns="tableColumns"
          :editable-row-keys="editableRowKeys"
          height="100%"
          hover
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          @row-edit="onRowEdit"
          @row-validate="onRowValidate"
          @validate="onValidate"
          :loading="tableLoading"
          :pagination="pagination"
          @page-change="pageChange"
          :foot-data="footData"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #ssyf="{ row }">
            <span>{{ `${row.sszqq} 至 ${row.sszqz}` }}</span>
          </template>
          <template #yysr="{ row }">
            <span>{{ numberToPrice(row.yysr) }}</span>
          </template>
          <template #zgxc="{ row }">
            <span>{{ numberToPrice(row.zgxc) }}</span>
          </template>
          <template #zcze="{ row }">
            <span>{{ numberToPrice(row.zcze) }}</span>
          </template>
          <template #fzjgxsqyxyhqk="{ row }">
            <span>{{ numberToPrice(row.fzjgxsqyxyhqk) }}</span>
          </template>
          <template #xsdfjmfd="{ row }">
            <span>{{ numberToPrice(row.xsdfjmfd) }}</span>
          </template>
        </t-table>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { ChartIcon, DownloadIcon } from 'tdesign-icons-vue';
import { queryFzjgfztz, queryFzjgfztzHj, updateFzjgfztz } from '@/pages/index/api/tzzx/qysdsyjtz/yjtz.js';
import { getFzjgxsqyxyhqkDm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { downloadBlobFile } from '@/core/download';
import { MessagePlugin, InputNumber, Select } from 'tdesign-vue';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import { getSbztBySsqqz } from '@/pages/index/views/util/tzzxTools.js';
import { Ssyf, querySearchConfig } from './config.js';

export default {
  components: {
    SkeletonFrame,
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
    ChartIcon,
  },
  data() {
    return {
      // 本地测试关闭骨架屏
      loading: false,
      isProduct: this.$store.state.isProduct.envValue,
      userInfo: {},
      editableRowKeys: ['1'],
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      selectedRowKeys: [],
      tableData: [],
      formData: {},
      footData: [],
      FzjgxsqyxyhqkDmList: [],
      querySearchConfig,
      id: dayjs().unix(),
      tableLoading: false,
      dcLoading: false,
      pagination: { current: 1, pageSize: 10, total: 0 },
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
    };
  },
  created() {
    this.formData.sszqq = Ssyf().ssyfq;
    this.formData.sszqz = Ssyf().ssyfz;
  },
  mounted() {
    this.init();
    this.checkAllowEdit();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.sszqq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyQysdsyjStatus', this.$store.state.jyss.readyQysdsyjStatus);
      return this.$store.state.jyss.readyQysdsyjStatus;
    },
    tableColumns() {
      return [
        {
          width: 50,
          align: 'center',
          colKey: 'xh',
          title: '序号',
          foot: '合计',
        },
        {
          colKey: 'sszq',
          title: '所属月份',
          ellipsis: true,
          width: 180,
        },
        {
          colKey: 'nsrsbh',
          title: '公司代码',
          ellipsis: true,
          width: 180,
        },
        {
          colKey: 'fzjgnsrsbh',
          title: '分支机构纳税人识别号（统一社会信用代码）',
          render(h, context) {
            const { type, row } = context;
            if (type === 'title')
              return (
                <div style="text-align:left,font-size:14px;color:#333;font-weight:700">
                  分支机构纳税人识别号 <br /> （统一社会信用代码）
                </div>
              );
            return <div style="text-align:left">{row.fzjgnsrsbh}</div>;
          },
          ellipsis: true,
          width: 180,
        },
        {
          colKey: 'fzjgnsrmc',
          title: '分支机构名称',
          ellipsis: true,
          width: 220,
        },
        {
          align: 'center',
          colKey: 'sxys',
          title: '三项因素',
          children: [
            {
              align: 'right',
              colKey: 'yysr',
              title: '营业收入',
              ellipsis: true,
              width: 120,
              edit: {
                component: InputNumber,
                props: {
                  autofocus: true,
                  autoWidth: true,
                  theme: 'normal',
                },
                rules: [{ required: true, message: '不能为空' }],
                showEditIcon: false,
              },
            },
            {
              align: 'right',
              colKey: 'zgxc',
              title: '员工薪酬',
              ellipsis: true,
              width: 120,
              edit: {
                component: InputNumber,
                props: {
                  autofocus: true,
                  autoWidth: true,
                  theme: 'normal',
                },
                rules: [{ required: true, message: '不能为空' }],
                showEditIcon: false,
              },
            },
            {
              align: 'right',
              colKey: 'zcze',
              title: '资产总额',
              ellipsis: true,
              width: 120,
              edit: {
                component: InputNumber,
                props: {
                  autofocus: true,
                  autoWidth: true,
                  theme: 'normal',
                },
                rules: [{ required: true, message: '不能为空' }],
                showEditIcon: false,
              },
            },
          ],
        },
        {
          align: 'right',
          colKey: 'fzjgxsqyxyhqk',
          title: '分支机构享受区域性优惠情况',
          ellipsis: true,
          width: 140,
          render(h, context) {
            const { type, row } = context;
            if (type === 'title')
              return (
                <div style="text-align:right,font-size:14px;color:#333;font-weight:700">
                  分支机构享受 <br /> 区域性优惠情况
                </div>
              );
            return <div style="text-align:right">{row.fzjgxsqyxyhqk}</div>;
          },
          cell: (h, { row }) => this.FzjgxsqyxyhqkDmList.find((t) => t.value === row.fzjgxsqyxyhqk)?.label,
          edit: {
            component: Select,
            props: {
              clearable: true,
              options: this.FzjgxsqyxyhqkDmList,
            },
            rules: [],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'xsdfjmfd',
          title: '享受民族地方减免幅度',
          ellipsis: true,
          width: 140,
          render(h, context) {
            const { type, row } = context;
            if (type === 'title')
              return (
                <div style="text-align:right,font-size:14px;color:#333;font-weight:700">
                  享受民族地方 <br /> 减免幅度
                </div>
              );
            return <div style="text-align:right">{row.xsdfjmfd}</div>;
          },
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 80,
          foot: '-',
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.key);
            return (
              <div>
                {!editable && (
                  <t-link
                    theme="primary"
                    hover="color"
                    disabled={!this.allowEdit}
                    data-id={row.key}
                    onClick={this.onEdit}
                  >
                    编辑
                  </t-link>
                )}
                {editable && (
                  <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszqq || !formData.sszqz) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit =
            dayjs(formData.sszqq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq &&
            dayjs(formData.sszqz).format('YYYY-MM') === sbgzSsyfInfo.ssyfz;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsqqz(formData.sszqq, formData.sszqz, 'BDA0611159');
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    async init() {
      const { data } = await getFzjgxsqyxyhqkDm();
      this.FzjgxsqyxyhqkDmList = data;
    },
    onEdit(e) {
      console.log(this.tableData, 'this.tableData');
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
        console.log('id1');
      } else {
        id = e.currentTarget.dataset.id;
      }

      // if (!this.editableRowKeys.includes(id)) {
      this.editableRowKeys.push(id);
      // }
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            this.tableData.splice(current.rowIndex, 1, current.editedRow);
            // MessagePlugin.success('保存成功');
            this.update(current.editedRow);
          }
          // 关闭编辑/保存折叠按钮
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        console.log('Promise Table Data Validate:', params);
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          MessagePlugin.warning(firstError[0].message);
        }
      });
    },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async query(pm = { flag: false }) {
      const { flag } = pm;
      if (flag) this.pagination.current = 1;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      this.tableLoading = true;
      params = {
        sszqq: dayjs(this.formData.sszqq).startOf('month').format('YYYY-MM-DD'),
        sszqz: dayjs(this.formData.sszqz).endOf('month').format('YYYY-MM-DD'),
        ...params,
      };
      try {
        const { data } = await queryFzjgfztz(params);
        console.log('data', data);
        console.log(data.records, 'data.records');
        this.tableData = data.records || [];
        this.pagination.total = data.pageTotal;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        if (this.tableData.length > 0) {
          const { data } = await queryFzjgfztzHj(params);
          this.footData =
            [
              {
                zcze: this.numberToPrice(data?.zcze),
                zgxc: this.numberToPrice(data?.zgxc),
                yysr: this.numberToPrice(data?.yysr),
              },
            ] || [];
        } else {
          this.footData = [];
        }
        // this.$nextTick(() => {
        //   if (this.$refs.tableRef) {
        //     this.$refs.tableRef.refreshTable();
        //   }
        // });
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    async update(params) {
      try {
        console.log('updateData', params);
        const { msg, data } = await updateFzjgfztz(params);
        console.log('data', data);
        console.log('msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'fzjgfztz',
        fileName: '分支机构辅助台账表',
        cxParam: {
          sszqq: dayjs(this.formData.sszqq).startOf('month').format('YYYY-MM-DD'),
          sszqz: dayjs(this.formData.sszqz).endOf('month').format('YYYY-MM-DD'),
          ...djParam,
        },
      };
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
</style>
