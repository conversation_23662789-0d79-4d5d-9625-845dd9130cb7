<!--
 * @Descripttion: 台账-企业所得税预缴台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-10-11 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
        <template #t1><span></span></template>
      </search-control-panel>
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" :ywlx="'qysdsyj'" @query="query" />
          <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
            ><DownloadIcon slot="icon" />导出</t-button
          >
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="key"
          :data="tableData"
          :columns="tableColumns"
          :editable-row-keys="editableRowKeys"
          height="100%"
          hover
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          @row-edit="onRowEdit"
          @row-validate="onRowValidate"
          @validate="onValidate"
          :loading="tableLoading"
          :pagination="pagination"
          @page-change="pageChange"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <!-- <template #ssyf="{ row }">
            <span>{{ `${row.sszqq} 至 ${row.sszqz}` }}</span>
          </template> -->
          <template #zcze="{ row }">
            <span>{{ numberToPrice(row.zcze, 6) }}</span>
          </template>
          <template #yysr="{ row }">
            <span>{{ numberToPrice(row.yysr) }}</span>
          </template>
          <template #yycb="{ row }">
            <span>{{ numberToPrice(row.yycb) }}</span>
          </template>
          <template #lrze="{ row }">
            <span>{{ numberToPrice(row.lrze) }}</span>
          </template>
        </t-table>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { ChartIcon, DownloadIcon } from 'tdesign-icons-vue';
import { queryYjtz, updateYjtz, queryNsrzInfo } from '@/pages/index/api/tzzx/qysdsyjtz/yjtz.js';
import { downloadBlobFile } from '@/core/download';
import { MessagePlugin, InputNumber } from 'tdesign-vue';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { numberToPrice } from '@/utils/numberToCurrency';
import { getSbztBySsqqz } from '@/pages/index/views/util/tzzxTools.js';
import { Ssyf, querySearchConfig } from './config.js';

export default {
  components: {
    ExtractDataButton,
    SkeletonFrame,
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
    ChartIcon,
  },
  data() {
    return {
      // 本地测试关闭骨架屏
      loading: false,
      isProduct: this.$store.state.isProduct.envValue,
      userInfo: {},
      editableRowKeys: ['1'],
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      selectedRowKeys: [],
      tableData: [],
      formData: {},
      querySearchConfig,
      id: dayjs().unix(),
      tableLoading: false,
      dcLoading: false,
      pagination: { current: 1, pageSize: 10, total: 0 },
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
      nsrzInfo: null, // 税费种认定信息
      isMonthlyReport: false, // 是否为月报
    };
  },
  created() {
    this.$set(this.formData, 'sszqq', Ssyf().ssyfq);
    this.$set(this.formData, 'sszqz', Ssyf().ssyfz);
  },
  mounted() {
    this.init();
  },
  computed: {
    tableColumns() {
      return [
        {
          width: 50,
          align: 'center',
          colKey: 'xh',
          title: '序号',
        },
        {
          colKey: 'sszq',
          title: '所属月份',
          ellipsis: true,
          width: 180,
        },
        {
          colKey: 'cyrs',
          title: '季末从业人数',
          ellipsis: true,
          width: 140,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'zcze',
          title: this.isMonthlyReport ? '资产总额-月末（万元）' : '资产总额-季末（万元）',
          ellipsis: true,
          width: 140,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
              max: 99999999999999.999999,
              min: -99999999999999.999999,
              decimalPlaces: 6,
            },
            rules: [
              { required: true, message: '不能为空' },
              { validator: (val) => val <= 99999999999999.999999, message: '资产总额数值过大，请重新输入' },
              { validator: (val) => val >= -99999999999999.999999, message: '资产总额数值过小，请重新输入' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'yysr',
          title: '营业收入（元）',
          ellipsis: true,
          width: 140,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
              max: 999999999999999999.99,
              min: -999999999999999999.99,
              decimalPlaces: 2,
            },
            rules: [
              { required: true, message: '不能为空' },
              { validator: (val) => val <= 999999999999999999.99, message: '营业收入数值过大，请重新输入' },
              { validator: (val) => val >= -999999999999999999.99, message: '营业收入数值过小，请重新输入' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'yycb',
          title: '营业成本（元）',
          ellipsis: true,
          width: 140,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
              max: 999999999999999999.99,
              min: -999999999999999999.99,
              decimalPlaces: 2,
            },
            rules: [
              { required: true, message: '不能为空' },
              { validator: (val) => val <= 999999999999999999.99, message: '营业成本数值过大，请重新输入' },
              { validator: (val) => val >= -999999999999999999.99, message: '营业成本数值过小，请重新输入' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'lrze',
          title: '利润总额（元）',
          ellipsis: true,
          width: 140,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
              max: 999999999999999999.99,
              min: -999999999999999999.99,
              decimalPlaces: 2,
            },
            rules: [
              { required: true, message: '不能为空' },
              { validator: (val) => val <= 999999999999999999.99, message: '利润总额数值过大，请重新输入' },
              { validator: (val) => val >= -999999999999999999.99, message: '利润总额数值过小，请重新输入' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 80,
          foot: '-',
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.key);
            return (
              <div>
                {!editable && (
                  <t-link
                    theme="primary"
                    hover="color"
                    disabled={!this.allowEdit}
                    data-id={row.key}
                    onClick={this.onEdit}
                  >
                    编辑
                  </t-link>
                )}
                {editable && (
                  <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
    sszqToExtract() {
      return dayjs(this.formData.sszqq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyQysdsyjStatus', this.$store.state.jyss.readyQysdsyjStatus);
      return this.$store.state.jyss.readyQysdsyjStatus;
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    init() {
      this.getNsrzInfo();
      this.checkAllowEdit();
    },
    // 获取税费种认定信息
    getNsrzInfo() {
      queryNsrzInfo({
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      })
        .then((res) => {
          if (res.code === 1 && res.data) {
            this.nsrzInfo = res.data;
            // 判断是月报还是季报 (06是月报，08是季报)
            this.isMonthlyReport = res.data.nsqxDm === '06';
            // 根据报表类型设置默认查询期间
            this.setDefaultQueryPeriod();
            // 执行查询
            this.query({ flag: true });
          } else {
            MessagePlugin.error('获取税费种认定信息失败');
            // 默认按季报处理
            this.isMonthlyReport = false;
            this.setDefaultQueryPeriod();
            this.query({ flag: true });
          }
        })
        .catch((err) => {
          console.error('获取税费种认定信息异常:', err);
          MessagePlugin.error('获取税费种认定信息异常');
          // 默认按季报处理
          this.isMonthlyReport = false;
          this.setDefaultQueryPeriod();
          this.query({ flag: true });
        });
    },
    // 设置默认查询期间
    setDefaultQueryPeriod() {
      if (this.isMonthlyReport) {
        // 月报: 设置为上个月
        const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM');
        this.$set(this.formData, 'sszqq', lastMonth);
        this.$set(this.formData, 'sszqz', lastMonth);
      } else {
        // 季报: 维持原有逻辑
        this.$set(this.formData, 'sszqq', Ssyf().ssyfq);
        this.$set(this.formData, 'sszqz', Ssyf().ssyfz);
      }
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszqq || !formData.sszqz) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit =
            dayjs(formData.sszqq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq &&
            dayjs(formData.sszqz).format('YYYY-MM') === sbgzSsyfInfo.ssyfz;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsqqz(formData.sszqq, formData.sszqz, 'BDA0611159');
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    numberToPrice,
    onEdit(e) {
      console.log(this.tableData, 'this.tableData');
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
        console.log('id1');
      } else {
        id = e.currentTarget.dataset.id;
      }

      // if (!this.editableRowKeys.includes(id)) {
      this.editableRowKeys.push(id);
      // }
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            // 检查月报模式下是否在允许的月份修改了从业人数
            if (this.isMonthlyReport) {
              const month = current.row.sszq?.slice(-2);
              const isAllowEditMonth = ['03', '06', '09', '12'].includes(month);

              // 检查是否修改了cyrs字段
              const originalCyrs = current.row.cyrs;
              const editedCyrs = current.editedRow.cyrs;
              const hasModifiedCyrs = originalCyrs !== editedCyrs;

              // 如果不是允许编辑的月份但修改了从业人数，则提示并中断保存
              if (!isAllowEditMonth && hasModifiedCyrs) {
                MessagePlugin.warning('月报情况下，从业人数仅在3、6、9、12月允许编辑');
                return;
              }
            }

            // 单位转换：万元->元（数据库中存储为元，前端显示为万元，此处进行转换）
            const editedData = {
              ...current.editedRow,
              zcze: current.editedRow.zcze ? current.editedRow.zcze * 10000 : 0,
            };
            this.tableData.splice(current.rowIndex, 1, editedData);
            // MessagePlugin.success('保存成功');
            this.update(editedData);
          }
          // 关闭编辑/保存折叠按钮
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        console.log('Promise Table Data Validate:', params);
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          MessagePlugin.warning(firstError[0].message);
        }
      });
    },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async query(pm = { flag: false }) {
      const { flag } = pm;
      if (flag) this.pagination.current = 1;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      this.tableLoading = true;
      params = {
        sszqq: dayjs(this.formData.sszqq).startOf('month').format('YYYY-MM-DD'),
        sszqz: dayjs(this.formData.sszqz).endOf('month').format('YYYY-MM-DD'),
        ...params,
      };
      try {
        const { data } = await queryYjtz(params);
        console.log('data', data);
        console.log(data.records, 'data.records');
        this.tableData = data.records || [];
        this.pagination.total = data.pageTotal;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    // 检查是否允许编辑从业人数
    isCyrsEditable(row) {
      if (!this.isMonthlyReport) return true;
      const month = row.sszq?.slice(-2);
      return ['03', '06', '09', '12'].includes(month);
    },
    async update(params) {
      try {
        console.log('updateData', params);
        // 月报情况下，检查是否允许编辑当前月份的从业人数
        if (this.isMonthlyReport) {
          const month = params.sszq?.slice(-2);
          // 如果当前月份不允许编辑从业人数，但params中包含从业人数，则删除该字段
          if (!['03', '06', '09', '12'].includes(month) && params.cyrs !== undefined) {
            // 避免直接修改函数参数，创建新对象
            const newParams = { ...params };
            delete newParams.cyrs;
            console.log('非允许编辑月份，已移除从业人数字段');
          }
        }
        const { msg } = await updateYjtz({ ...params, djxh: this.$store.state.zzstz.userInfo?.djxh || '' });
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'yjtz',
        fileName: '企业所得税预缴台账表',
        cxParam: {
          sszqq: dayjs(this.formData.sszqq).startOf('month').format('YYYY-MM-DD'),
          sszqz: dayjs(this.formData.sszqz).endOf('month').format('YYYY-MM-DD'),
          ...djParam,
        },
      };
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
</style>
