<template>
  <div style="padding: 10px; margin-bottom: 10px; background-color: white">
    <t-collapse :defaultValue="defaultValue" borderless>
      <t-collapse-panel value="0">
        <template #header><span class="collheader">基本信息</span></template>
        <div>
          <t-row class="row-style">
            <t-col :span="4">
              <span class="label required">纳税人识别号：</span>
              <span>{{ sbxxVO.nsrsbh || '' }}</span>
            </t-col>
            <t-col :span="4">
              <span class="label required">纳税人名称：</span>
              <span class="label">{{ sbxxVO.nsrmc || '' }}</span>
            </t-col>
            <t-col :span="4">
              <span class="label required">申请人：</span>
              <span class="label">{{ sbxxVO.bsrxm || '' }}</span>
            </t-col>
          </t-row>
          <t-row class="row-style">
            <t-col :span="4">
              <span class="label required">填报日期：</span>
              <span class="label">{{ sbxxVO.sqrq || '' }}</span>
            </t-col>
            <t-col :span="4">
              <span class="label required">办税人员身份证件类型：</span>
              <span class="label">{{ sbxxVO.bsrsfzjzlDm || '' }}</span>
            </t-col>
            <t-col :span="4">
              <span class="label required">办税人员身份证件号码：</span>
              <span class="label">{{ sbxxVO.bsrsfzjhm || '' }}</span>
            </t-col>
          </t-row>
        </div>
      </t-collapse-panel>
      <t-collapse-panel value="1">
        <template #header><span class="collheader">申请延期申报信息</span></template>
        <t-form
          class="form-table"
          ref="tableForm"
          labelAlign="right"
          :data="formData"
          :requiredMark="false"
          :colon="false"
        >
          <t-table
            row-key="key"
            size="small"
            hover
            :columns="demoColumns"
            :data="formData.listData"
            :pagination="pagination"
          >
            <template #sfzlMc="{ row }">
              {{ row.sfzlMc }}
            </template>

            <template #zspmMc="{ row }">
              {{ row.zspmMc }}
            </template>

            <template #skssqq="{ row }">
              {{ row.skssqq }}
            </template>

            <template #skssqz="{ row }">
              {{ row.skssqz }}
            </template>

            <template #gdsbqx="{ row }">
              {{ row.gdsbqx }}
            </template>

            <template #sqyqsbqx="{ row }">
              {{ row.sqyqsbqx }}
            </template>
          </t-table>
        </t-form>
      </t-collapse-panel>
      <t-collapse-panel value="2">
        <template #header><span class="collheader">申请理由</span></template>
        <t-textarea v-model="sbxxVO.sqyqsbdly" placeholder="请输入内容" />
      </t-collapse-panel>

      <t-collapse-panel value="3">
        <template #header><span class="collheader">受理信息</span></template>
        <div>
          <t-row class="row-style">
            <t-col :span="4">
              <span class="label">受理税务机关</span>
            </t-col>
            <t-col :span="4">
              <span class="label">受理人</span>
            </t-col>
            <t-col :span="4">
              <span class="label">受理时间</span>
            </t-col>
          </t-row>
          <t-row class="row-style">
            <t-col :span="4">
              <div style="margin-right: 40px">
                <t-input disabled v-model="sbxxVO.slswjgmc" />
              </div>
            </t-col>
            <t-col :span="4">
              <div style="margin-right: 40px">
                <t-input disabled v-model="sbxxVO.slrmc" />
              </div>
            </t-col>
            <t-col :span="4">
              <t-date-picker v-model="sbxxVO.slrq" disabled />
            </t-col>
          </t-row>
        </div>
      </t-collapse-panel>

      <t-radio-group v-model="qtyy" :disabled="ybbz === 'Y'" @change="onChange">
        <div style="margin-bottom: 10px; margin-left: 40px">
          <span style="margin-right: 20px">受理意见</span>
          <t-radio value="Y">通过</t-radio>
          <t-radio value="N">不予受理</t-radio> <br />
        </div>
      </t-radio-group>
      <div style="margin-right: 16px; margin-left: 40px">
        <t-textarea v-model="syyj" :disabled="ybbz === 'Y'" placeholder="请输入内容" />
      </div>
      <div class="form-submit">
        <t-space>
          <t-button theme="primary" variant="base" :disabled="btnDisabled || disable || allDisable" @click="saveSbData"
            >保 存</t-button
          >
        </t-space>
      </div>
    </t-collapse>
  </div>
</template>

<script>
import api from '@/pages/index/api/demo/demoRequest.js';
import { demoColumns } from './config/demo';

export default {
  name: 'sbfdemo',
  data() {
    return {
      defaultValue: ['0', '1', '2', '3'],
      demoColumns,
      btnDisabled: false, // 保存业务接口成功之后会都禁用
      lcswsxDm: 'LCSXA061010001',
      formData: {
        listData: [],
        slsj: '2023-01-15',
        slr: '武明松',
        slswjg: '番禺区税务局',
      },
      sbxxVO: {
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        bsrxm: '',
        sqrq: '',
        bsrsfzjzlDm: '',
        bsrsfzjhm: '',
        sqyqsbdly: '',
        slswjgDm: '',
        slswjgmc: '',
        slrDm: '',
        slrq: '',
        slrmc: '',
      },
      sqyqsbdly: '理由1.。。',
      syyj: '',
      qtyy: '',
      czlx: '', // 显示弹窗的种类
      sxbt: '对纳税人延期申报核准',
      djxh: '',
      jcglgxuuid: '',
      ybbz: '', // 已办标志， 判断是否页面可操作
      czbz: '',
      bzlx: '',
      disable: false, // 不予受理
      allDisable: false, // 都禁用点击
      tsgwList: [],
      tsryList: [],
      tsdwxx: [],
      xgsjList: [
        {
          key: 'sfjsbj',
          value: 'N',
        },
      ],
    };
  },
  computed: {
    pagination() {
      return {
        defaultCurrent: 1,
        defaultPageSize: 5,
        total: this.formData.listData.length,
        showJumper: true,
      };
    },
  },

  created() {
    this.init();
    this.ybbz = this.$route.query?.ybbz || '';
  },
  methods: {
    async init() {
      this.queryNsrxx();
      // this.querySbxx();
    },
    async queryNsrxx() {
      const djxh = this.$route.query.djxh || '';
      const params = {
        djxh,
      };
      const { error, data } = await api.queryNsrxx(params);
      if (!error) {
        this.sbxxVO = data || {};
        this.sbxxVO.sqrq = '2023-01-23';
        this.sbxxVO.slswjgmc = '番禺区税务局';
        this.sbxxVO.slrmc = '武明松';
        this.sbxxVO.slrq = '2023-01-23';
      }
    },
    async querySbxx() {
      const djxh = this.$route.query.djxh || '';
      const params = {
        djxh,
      };
      const { error, data } = await api.querySbxx(params);
      if (!error) {
        this.formData.listData = data.list || [];
      }
    },
    async saveSbData() {
      const mYqsbSaveVo = this.sbxxVO;
      if (!mYqsbSaveVo) {
        return;
      }
      if (!mYqsbSaveVo.djxh) {
        return;
      }
      if (!this.qtyy) {
        this.$message.error('请选择受理意见');
        return;
      }
      const yqsbuuid = this.$route.query.Sxuuid || '';
      this.djxh = mYqsbSaveVo.djxh || '';
      this.jcglgxuuid = yqsbuuid || '';
      if (this.qtyy === 'Y') {
        // 先调用准予受理文书，文书保存成功后禁用其他按键，此按键只能调用推送弹窗
        if (this.czbz === 'ts') {
          this.czlx = 'ts';
          return;
        }
        // 通过 准予受理
        this.czlx = 'zysl';
      } else {
        // 不通过 不予受理
        this.czlx = 'bysl';
      }
      this.saveSwzxxx();
    },
    // 公共组件相关方法
    getCzbz(val1, val2) {
      this.czbz = val1;
      this.bzlx = val2;
      console.log('操作标志，标志类型', val1, val2);
      if (this.czbz === 'Y' && this.bzlx === 'BYSL') {
        this.disable = true;
      }
      if (this.czbz === 'Y' && this.bzlx === 'ZYSL') {
        this.disable = true;
      }
    },
    async saveSwzxxx() {
      console.log('list', this.formData.listData);
      const mYqsbSaveVo = this.sbxxVO;
      const yqsbuuid = this.$route.query.Sxuuid || '';
      const params = {
        saveSbxxVO: {
          djxh: mYqsbSaveVo.djxh || '',
          yqsbuuid: yqsbuuid || '',
          sqrq: mYqsbSaveVo.sqrq || '',
          sqyqsbdly: mYqsbSaveVo.sqyqsbdly || '',
          username: mYqsbSaveVo.username || '',
          sfzjlx: mYqsbSaveVo.sfzjlx || '',
          sfzjhm: mYqsbSaveVo.sfzjhm || '',
          lcslid: yqsbuuid || '',
          sqrmc1: mYqsbSaveVo.sqrmc || '',
          slswjgDm: mYqsbSaveVo.slswjgDm || '',
          slrq: mYqsbSaveVo.slrq || '',
          lrrDm: mYqsbSaveVo.lrrDm || '',
          shjg: this.qtyy || '', // 审核结果
          lrrq: mYqsbSaveVo.lrrq || '',
          sjgsdq: mYqsbSaveVo.sjgsdq || '',
          slrDm: mYqsbSaveVo.slrDm || '',
          xgrq: mYqsbSaveVo.xgrq || '',
          ywqdDm: mYqsbSaveVo.ywqdDm || '',
          // sjtbSj: mYqsbSaveVo.djxh,
          nsrsbh: mYqsbSaveVo.nsrsbh || '',
          nsrmc: mYqsbSaveVo.nsrmc || '',
          sjcsdq: mYqsbSaveVo.sjcsdq || '',
          zgswskfjDm: mYqsbSaveVo.zgswskfjDm || '',
        },
        yqsbgwdmVOlist: this.formData.listData || [],
      };
      const { error, data } = await api.saveSbData(params);
      if (!error) {
        if (data === 'success') {
          this.$message.info(`推送成功！`);
          this.btnDisabled = true;
        } else if (data === 'fail') {
          this.$message.info(`推送失败！`);
        }
      }
    },
    // 组件回调方法文书
    async fatherMethodWs(val) {
      console.log(val);
      return true;
    },
    onChange(data) {
      if (data === 'Y') {
        this.syyj = '同意';
      } else if (data === 'N') {
        this.syyj = '不予受理';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.required {
  font-weight: 600;
}
.row-style {
  margin-right: 20px;
  margin-bottom: 10px;
}
.form-submit {
  width: 100%;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background-color: white;
}
</style>
