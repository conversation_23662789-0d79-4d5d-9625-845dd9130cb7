import dayjs from 'dayjs';

export const yjlxList = [
  { value: '101017100', label: '建筑服务' },
  { value: '101017700', label: '销售不动产' },
  { value: '101016600', label: '出租不动产' },
];

export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源地址',
    key: 'fwzldz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '预缴类型',
    key: 'yjlx',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '不动产权证号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '行政区划',
    key: 'xzqhList',
    type: 'tree-select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    multiple: true,
    filterable: true,
    minCollapsedNum: 1,
    value: [],
  },
];

export const querySearchConfigOneRules = {};

export const dataColumns = [
  //   {
  //     colKey: 'row-select',
  //     type: 'multiple',
  //     className: 'demo-multiple-select-cell',
  //     width: 50,
  //     align: 'center',
  //     fixed: 'left',
  //     foot: '合计',
  //   },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    fixed: 'left',
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'ssyf',
    align: 'center',
    title: '所属月份',
  },
  {
    colKey: 'fwzldz',
    title: '房源地址',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xzqhMc',
    title: '行政区划',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zgswjgmc',
    title: '主管税务机关',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'yjlx',
    title: '预缴类型',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'skssqq',
    title: '税款所属期起',
    width: 120,
    cell: (h, { row }) => <div>{row.skssqq ? dayjs(row.skssqq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'skssqz',
    title: '税款所属期止',
    width: 120,
    cell: (h, { row }) => <div>{row.skssqz ? dayjs(row.skssqz).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 160,
    colKey: 'hsje',
    title: '销售额（含税）',
    fixed: 'right',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 240,
    foot: '-',
    fixed: 'right',
  },
];
