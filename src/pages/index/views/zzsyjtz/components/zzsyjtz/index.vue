<!--
* @Descripttion: 台账-增值税预缴-增值税预缴台账
* @Version: 1.0
* @Author: ljf
* @Date: 2024-11-04 15:00:00
* @LastEditors: 
* @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" type="submit" @click="query({ flag: true })"
          ><CloudDownloadIcon slot="icon" />提取数据</t-button
        >
        <!-- <t-button variant="outline" theme="primary" @click="check"><ChartIcon slot="icon" />查看底稿</t-button> -->
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #yjlx="{ row }">
          {{ { '101017100': '建筑服务', '101017700': '销售不动产', '101016600': '出租不动产' }[row.yjlx] || '' }}
        </template>
        <template #hsje="{ row }">
          <span>{{ numberToPrice(row.hsje) }}</span>
        </template>
        <template #operation="{ row }">
          <t-space size="10px">
            <t-link
              theme="primary"
              hover="color"
              @click="$emit('openPage', { from: 'zzsyjtz', type: 'zzsyjqkcx', data: row })"
            >
              预缴情况
            </t-link>
            <t-link
              theme="primary"
              hover="color"
              @click="$emit('openPage', { from: 'zzsyjtz', type: 'czbdcyjmx', data: row })"
            >
              预缴明细
            </t-link>
            <t-link
              theme="primary"
              hover="color"
              @click="$emit('openPage', { from: 'zzsyjtz', type: 'spzlhtmx', data: row })"
            >
              合同详情
            </t-link>
          </t-space>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberToCurrency';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { downloadBlobFile } from '@/core/download';
import { queryZzsyjmxList, queryZzsyjmxSum, getXzqhTreeData } from '@/pages/index/api/tzzx/zzsyjtz/zzsyjmx.js';
import { CloudDownloadIcon, ChartIcon, DownloadIcon } from 'tdesign-icons-vue';
import { yjlxList, querySearchConfig, dataColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
    CloudDownloadIcon,
    ChartIcon,
  },
  data() {
    this.yjlxList = yjlxList;
    this.dataColumns = dataColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    return {
      userInfo: {},
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      tableData: [],
      footData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    ssyfqC() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    ssyfzC() {
      return dayjs(this.formData.ssyfz).format('YYYYMM');
    },
  },
  created() {},
  mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
    this.query({ flag: true });
  },
  methods: {
    getQueryParamsList() {
      this.querySearchConfig[4].selectList = this.yjlxList;
    },
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        console.log('url-params', params);
        if (params.get('skssqq') && params.get('skssqz')) {
          this.querySearchConfig[0].value = dayjs(params.get('skssqq')).format('YYYY-MM');
          this.querySearchConfig[1].value = dayjs(params.get('skssqz')).format('YYYY-MM');
          this.formData.ssyfq = dayjs(params.get('skssqq')).format('YYYY-MM');
          this.formData.ssyfz = dayjs(params.get('skssqz')).format('YYYY-MM');
          return;
        }
      }
      this.querySearchConfig[0].value = dayjs().subtract(1, 'month').format('YYYY-MM');
      this.querySearchConfig[1].value = dayjs().subtract(1, 'month').format('YYYY-MM');
      this.formData.ssyfq = dayjs().subtract(1, 'month').format('YYYY-MM');
      this.formData.ssyfz = dayjs().subtract(1, 'month').format('YYYY-MM');
      getXzqhTreeData({ djxh: this.$store.state.zzstz.userInfo?.djxh }).then((res) => {
        this.querySearchConfig[6].selectList = res.data || [];
      });
    },
    resetQueryParams() {
      this.$refs.queryControl.onReset();
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm; // 默认无需回到第一页，无父传参
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      // 不变的基础入参
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '100',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params }; // 基础入参+父传参
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6))); // 修改父传参YYYYMM为YYYY-MM给上面的时间框
        }
        this.$refs.queryControl.setParams(p); // 覆盖上方的查询条件为父传参
      } else {
        params = { ...this.formData, sszqq: this.ssyfqC, sszqz: this.ssyfzC, ...params }; // 没有父传参的话,传查询条件+基础入参.查询框sszq绑定为YYYY-MM，需要使用sszqC覆盖
      }
      try {
        const { data } = await queryZzsyjmxList(params); // 不建议api.  建议使用接口真实尾缀，方便定位代码。
        this.tableData = data.list || [];
        if (this.tableData.length > 0) {
          const { data } = await queryZzsyjmxSum(params);
          this.footData =
            [
              {
                hsje: this.numberToPrice(data),
              },
            ] || []; // 表格长度大于1时展示表尾总计行。
        } else {
          this.footData = [];
        }
        this.pagination.total = data.total;
      } catch (e) {
        console.log(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610865',
        tzlx: 'zzsyjmx',
        fileName: '增值税预缴台账',
        cxParam: {
          ...this.formData,
          sszqq: this.ssyfqC,
          sszqz: this.ssyfzC,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
</style>
