export const querySearchConfig1 = [
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源地址',
    key: 'fwzldz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同开始日期',
    key: 'zlrqq',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqz',
    timeRange: 'start',
  },
  {
    label: '合同结束日期',
    key: 'zlrqz',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqq',
    timeRange: 'end',
  },
  {
    label: '收款方式',
    key: 'skfs',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '数据来源',
    key: 'ly',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '行政区划',
    key: 'xzqhList',
    type: 'tree-select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    multiple: true,
    filterable: true,
    minCollapsedNum: 1,
    value: [],
  },
];
export const querySearchConfig2 = [
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '数据来源',
    key: 'ly',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '租赁开始日期',
    key: 'zlrqq',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqz',
    timeRange: 'start',
  },
  {
    label: '租赁结束日期',
    key: 'zlrqz',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqq',
    timeRange: 'end',
  },
];
export const dataColumns1 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ly !== '1' }),
    width: 50,
    fixed: 'left',
  },
  {
    colKey: 'xh',
    title: '序号',
    width: 60,
  },
  {
    colKey: 'htbh',
    title: '合同编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'htmc',
    title: '合同名称',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证书号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'htydsxrq',
    title: '合同开始日期',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'htydzzrq',
    title: '合同结束日期',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'zzrq',
    title: '提前终止日期',
    width: 120,
  },
  {
    colKey: 'fwzldz',
    title: '房源地址',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xzqhMc',
    title: '行政区划',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zgswjgMc',
    title: '主管税务机关',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'skfs',
    title: '收款方式',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'ly',
    title: '数据来源',
    width: 120,
    align: 'left',
    cell: (h, { row }) => {
      const { ly } = row;
      let sjly = '';
      if (ly === '0') {
        sjly = '合同系统';
      }
      if (ly === '1') {
        sjly = '手工录入';
      }
      return <span>{sjly}</span>;
    },
  },
  {
    align: 'center',
    width: 120,
    colKey: 'cz',
    title: '操作',
    fixed: 'right',
  },
];
export const dataColumns2 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ly !== '1' }),
    width: 50,
    fixed: 'left',
  },
  {
    colKey: 'xh',
    title: '序号',
    width: 60,
    foot: '合计',
  },
  {
    colKey: 'htbh',
    title: '合同编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'htmc',
    title: '合同名称',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证书号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zlrqq',
    title: '租赁开始日期',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'zlrqz',
    title: '租赁结束日期',
    width: 120,
  },
  {
    colKey: 'sjczmj',
    title: '出租面积',
    width: 120,
    align: 'right',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'sjyzjsr',
    title: '月租金收入(不含税)',
    width: 170,
    align: 'right',
  },
  {
    colKey: 'sjsl1',
    title: '预缴税率',
    width: 100,
    align: 'right',
  },
  {
    colKey: 'sjhsyzjsr',
    title: '月租金收入(含税)',
    width: 170,
    align: 'right',
  },
  {
    colKey: 'ly',
    title: '数据来源',
    width: 120,
    align: 'left',
    cell: (h, { row }) => {
      const { ly } = row;
      let sjly = '';
      if (ly === '0') {
        sjly = '合同系统';
      }
      if (ly === '1') {
        sjly = '手工录入';
      }
      if (ly === '2') {
        sjly = '合同系统手工变更';
      }
      return <span>{sjly}</span>;
    },
  },
  {
    colKey: 'xgrq',
    title: '操作时间',
    width: 120,
    align: 'center',
  },
  {
    colKey: 'xgrsfid',
    title: '操作人员',
    width: 120,
    align: 'left',
  },
  {
    align: 'left',
    width: 240,
    colKey: 'tzyy',
    title: '调整原因',
  },
  {
    align: 'center',
    width: 120,
    colKey: 'cz',
    title: '操作',
    fixed: 'right',
  },
];
