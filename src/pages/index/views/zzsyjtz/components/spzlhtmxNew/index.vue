<!--
* @Descripttion: 台账-增值税预缴商铺租赁合同
* @Version: 1.0
* @Author: <PERSON><PERSON><PERSON><PERSON>
* @Date: 2025-03-21 12:00:00
* @LastEditors: 
* @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue" @change="changeTabs">
      <search-control-panel
        ref="queryControl1"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        :config="querySearchConfig1"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
        <template #t1><span></span></template>
      </search-control-panel>
      <search-control-panel
        ref="queryControl2"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        :config="querySearchConfig2"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />

      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
          <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
          <ExtractDataButton :readyStatus="readyStatus" :ywtsMsg="'租赁合同数据'" @query="query" />
          <!-- <t-button variant="outline" theme="primary" @click="ckdg()" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          > -->
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
        </gt-space>
      </div>
      <t-tab-panel value="0" label="合同基本信息" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            hover
            :data="tableData1"
            :columns="dataColumns1"
            height="100%"
            lazyLoad
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination1"
            @page-change="(e) => pageChange(e, 0)"
            :loading="tableLoading"
          >
            <template #xh="{ rowIndex }">{{
              (pagination1.current - 1) * pagination1.pageSize + rowIndex + 1
            }}</template>
            <!-- <template #htbh="{ row }">
              <span class="specText" @click="openHtzlmx(row)" style="color: #0052d9; text-decoration: underline">{{
                row.htbh
              }}</span>
            </template> -->
            <template #htbh="{ row }">
              <div v-if="row.ycbz === 'Y'" style="display: flex; justify-content: space-between; align-items: center">
                <t-tooltip :showArrow="false" :destroyOnClose="false">
                  <span class="specText" @click="openHtzlmx(row)" style="color: #0052d9; text-decoration: underline">{{
                    row.htbh
                  }}</span>
                  <template #content>
                    <span>{{ row.ycxx }}</span>
                  </template>
                  <error-circle-filled-icon
                    :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }"
                  />
                </t-tooltip>
              </div>
              <span
                v-else
                class="specText"
                @click="openHtzlmx(row)"
                style="color: #0052d9; text-decoration: underline"
                >{{ row.htbh }}</span
              >
            </template>
            <template #skfs="{ row }">
              {{ { 0: '月付', 1: '季付', 2: '半年付', 3: '年付' }[row.skfs] || '' }}
            </template>
            <template #cz="{ row }">
              <t-link
                style="margin-right: 10px"
                v-if="row.ly === '1'"
                theme="primary"
                hover="color"
                @click="newOrEditRow(row, 0)"
              >
                编辑
              </t-link>
              <t-link style="margin-right: 10px" v-else theme="primary" hover="color" @click="newOrEditRow(row, 1)">
                查看
              </t-link>
              <t-link theme="primary" hover="color" @click="newOrEditYjjh(row)"> 预缴计划 </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="合同租赁信息" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            hover
            :data="tableData2"
            :columns="dataColumns2"
            height="100%"
            lazyLoad
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination2"
            @page-change="(e) => pageChange(e, 1)"
            :loading="tableLoading"
            :foot-data="footData"
          >
            <template #xh="{ rowIndex }">{{
              (pagination2.current - 1) * pagination2.pageSize + rowIndex + 1
            }}</template>
            <template #htbh="{ row }">
              <div v-if="row.ycbz === 'Y'" style="display: flex; justify-content: space-between; align-items: center">
                <t-tooltip :showArrow="false" :destroyOnClose="false">
                  <span>{{ row.htbh }}</span>
                  <template #content>
                    <span>{{ row.ycxx }}</span>
                  </template>
                  <error-circle-filled-icon
                    :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }"
                  />
                </t-tooltip>
              </div>
              <span v-else>{{ row.htbh }}</span>
            </template>
            <template #sjsl1="{ row }">
              {{ row.sjsl1 === null ? '-' : row.sjsl1 * 100 + '%' }}
            </template>
            <template #sjczmj="{ row }">
              <span>{{ numberToPrice(row.sjczmj) }}</span>
            </template>
            <template #sjyzjsr="{ row }">
              <span>{{ numberToPrice(row.sjyzjsr, 6) }}</span>
            </template>
            <template #sjhsyzjsr="{ row }">
              <span>{{ numberToPrice(row.sjhsyzjsr, 6) }}</span>
            </template>
            <template #cz="{ row }">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row, 0)"> 编辑 </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <ShowDialogJbxx :visible.sync="showDialogVisible" v-if="showDialogVisible && tabValue === '0'" />
      <EditDialogYjjh :visible.sync="showYjjh" v-if="showYjjh" />
      <EditDialogJbxx
        :visible.sync="editDialogVisible"
        v-if="editDialogVisible && tabValue === '0'"
        @aftersaveHtjbxx="query()"
      />
      <EditDialogZlxx
        :visible.sync="editDialogVisible"
        v-if="editDialogVisible && tabValue === '1'"
        @aftersaveHtmxxx="query()"
      />
      <div v-show="boxvisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="请确认是否删除所选明细"
          :onConfirm="confirmDelRow"
          :onClose="closeBox"
        >
        </t-dialog>
      </div>
    </t-tabs>
  </div>
</template>
<script>
import {
  queryZlht,
  deleteZlht,
  queryFcsHtxxMx,
  // queryFcsHtxxMxByZbuuid,
  deleteHtmxxx,
  queryFcsHtxxMxHj,
  // queryFcsHtxxMxByZbuuidHj,
} from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';
import { downloadBlobFile } from '@/core/download';
import { AddIcon, DeleteIcon, DownloadIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import { getXzqhTreeData } from '@/pages/index/api/tzzx/zzsyjtz/zzsyjmx.js';
import { querySearchConfig1, querySearchConfig2, dataColumns1, dataColumns2 } from './config.js';
import ShowDialogJbxx from './components/show-dialog-jbxx.vue';
import EditDialogJbxx from './components/edit-dialog-jbxx.vue';
import EditDialogZlxx from './components/edit-dialog-zlxx.vue';
import EditDialogYjjh from './components/edit-dialog-yjjh.vue';

export default {
  components: {
    ExtractDataButton,
    ShowDialogJbxx,
    EditDialogJbxx,
    EditDialogZlxx,
    SearchControlPanel,
    EditDialogYjjh,
    DownloadIcon,
    AddIcon,
    ErrorCircleFilledIcon,
    DeleteIcon,
  },
  data() {
    return {
      tabValue: '0',
      loading: true,
      isProduct: this.$store.state.isProduct.envValue,
      footerFlag: false,
      userInfo: {},
      querySearchConfig1,
      querySearchConfig2,
      dataColumns1,
      dataColumns2,
      tableLoading: false,
      dcLoading: false,
      boxvisible: false,
      editDialogVisible: false,
      showDialogVisible: false,
      showYjjh: false,
      formData1: [],
      formData2: [],
      selectedRowKeys: [],
      tableData1: [],
      tableData2: [],
      checkBox: [],
      delformData: [],
      footData: [],
      uuidList: [],
      skfsList: [],
      pagination1: { current: 1, pageSize: 10, total: 0 },
      pagination2: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    // 初始化收款方式
    this.querySearchConfig1[6].selectList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
    // 初始化数据来源
    this.querySearchConfig1[7].selectList = [
      { value: '0', label: '合同系统' },
      { value: '1', label: '手工录入' },
    ];
    this.querySearchConfig2[3].selectList = [
      { value: '0', label: '合同系统' },
      { value: '1', label: '手工录入' },
      { value: '2', label: '合同系统手工变更' },
    ];
  },
  mounted() {
    this.initQueryConditions();
  },
  computed: {
    readyStatus() {
      console.log('readyFcsStatus', this.$store.state.jyss.readyFcsStatus);
      return this.$store.state.jyss.readyFcsStatus;
    },
  },
  methods: {
    initTabValue() {
      this.tabValue = '0';
      console.log('initTabValue', this.tabValue);
    },
    async initQueryConditions() {
      console.log('init');
      getXzqhTreeData({ djxh: this.$store.state.zzstz.userInfo?.djxh }).then((res) => {
        this.querySearchConfig1[8].selectList = res.data || [];
      });
      this.query(); // 初始化查询
    },
    resetQueryParams() {
      this.$refs.queryControl1.onReset();
      this.$refs.queryControl2.onReset();
    },
    changeTabs() {
      this.formData1 = {};
      this.formData2 = {};
      this.$refs.queryControl1.onReset();
      this.$refs.queryControl2.onReset();
      this.query({ flag: true });
    },
    async openHtzlmx(row) {
      this.tabValue = '1';
      this.$refs.queryControl2.setParams({
        htbh: row.htbh,
        fwcqzsh: row.fwcqzsh,
        fybh: row.fybh,
      });
      this.formData2 = {
        zbuuid: row.uuid,
        htbh: row.htbh,
        fwcqzsh: row.fwcqzsh,
        fybh: row.fybh,
      };
      this.$store.commit('fcstz/setZlhtxxInfoData', {
        htbh: row.htbh,
        fwcqzsh: row.fwcqzsh,
        fybh: row.fybh,
      });
      this.query({ flag: true });
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      let params = {};
      if (flag) {
        this.tabValue * 1 ? (this.pagination2.current = 1) : (this.pagination1.current = 1);
      }
      this.tableLoading = true;
      const dqFormData = this.tabValue * 1 ? this.formData2 : this.formData1;
      if (p) {
        this.tabValue = '0';
        this.$refs.queryControl1.setParams(p);
        params = {
          cxbz: '2', // 房产税租赁合同查询对应值为‘1’，增值税预缴租赁合同查询对应值为‘2’
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          pageNo: this.tabValue * 1 ? this.pagination2.current : this.pagination1.current,
          pageSize: this.tabValue * 1 ? this.pagination2.pageSize : this.pagination1.pageSize,
          pzrq: p.sbsjq,
        };
        params = { ...p, ...params };
      } else {
        params = {
          cxbz: '2', // 房产税租赁合同查询对应值为‘1’，增值税预缴租赁合同查询对应值为‘2’
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          pageNo: this.tabValue * 1 ? this.pagination2.current : this.pagination1.current,
          pageSize: this.tabValue * 1 ? this.pagination2.pageSize : this.pagination1.pageSize,
        };
        params = { ...dqFormData, ...params };
      }
      try {
        let result;
        if (this.tabValue === '1') {
          console.log('dqFormData', dqFormData);
          // if ('zbuuid' in dqFormData) {
          //   result = await queryFcsHtxxMxByZbuuid({ zbuuid: dqFormData.zbuuid });
          // } else {
          // }
          result = await queryFcsHtxxMx(params);
          console.log(this.$store.state.fcstz.zlhtxxInfo, 'this.$store.state.fcstz.zlhtxxInfo');
          if (this.$store.state.fcstz.zlhtxxInfo) {
            const zlhtxzxx = this.$store.state.fcstz.zlhtxxInfo;
            console.log(params, 'params');
            if (zlhtxzxx.htbh !== params.htbh || zlhtxzxx.fwcqzsh !== params.fwcqzsh || zlhtxzxx.fybh !== params.fybh) {
              console.log(this.$store.state.fcstz.zlhtxxInfo, 'this.$store.state.fcstz.zlhtxxInfo');
              this.$store.commit('fcstz/setZlhtxxInfoData', {});
            }
          }
        } else {
          result = await queryZlht(params);
        }
        const { code, msg, data } = result;
        if (code === 1) {
          if (this.tabValue * 1) {
            this.tableData2 = data.list || [];
            this.tableData2.forEach((item, index) => {
              this.$set(item, 'index', index);
            });
            this.pagination2.total = data.total;
            if (this.tableData2.length > 0) {
              // let footDataResult;
              // if ('zbuuid' in dqFormData) {
              //   footDataResult = await queryFcsHtxxMxByZbuuidHj({ zbuuid: dqFormData.zbuuid });
              // } else {
              // }
              const footDataResult = await queryFcsHtxxMxHj(params);
              const { data } = footDataResult;
              this.footData = [
                {
                  sjczmj: this.numberToPrice(data.sjczmjHj),
                  sjyzjsr: this.numberToPrice(data.sjyzjsrHj),
                  sjhsyzjsr: this.numberToPrice(data.sjhsyzjsrHj),
                },
              ];
            } else {
              this.footData = [];
            }
          } else {
            this.tableData1 = data.list || [];
            this.tableData1.forEach((item, index) => {
              this.$set(item, 'index', index);
            });
            this.pagination1.total = data.total;
          }
        } else if (msg) {
          console.log(msg);
          this.$message.warning(msg);
        } else {
          this.$message.warning(data);
        }
      } catch (e) {
        console.error(e);
        this.tableData1 = [];
        this.tableData2 = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    // 增行
    newOrEditRow(row, type) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        pageNo: 1,
        pageSize: 10,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        gsmc: this.$store.state.zzstz.userInfo?.jgmc,
      };
      const rowdata = JSON.stringify(row);
      // type 用来区分查看还是编辑，0 为编辑，1 为查看
      if (type) {
        this.showDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType, type };
      } else {
        this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType, type };
      }
    },
    newOrEditYjjh(row) {
      const otherObj = {
        pageNo: 1,
        pageSize: 10,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        gsmc: this.$store.state.zzstz.userInfo?.jgmc,
      };
      const rowdata = JSON.stringify(row);
      // this.showDialogVisible = { row: JSON.parse(rowdata), otherObj};
      this.showYjjh = { row: JSON.parse(rowdata), otherObj };
    },
    // 删除-调用后台接口
    async delete(params) {
      try {
        let res = {};
        if (this.tabValue === '1') {
          res = await deleteHtmxxx(params);
        } else {
          res = await deleteZlht(params);
        }
        const { code, msg, data } = res;
        if (code === 1) {
          this.$message.success(msg);
        } else if (msg) {
          this.$message.warning(msg);
        } else {
          this.$message.warning(data);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    // 删行
    async delRow() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    closeBox() {
      this.boxvisible = false;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index =
          this.tabValue * 1
            ? this.tableData2.findIndex((i) => i.uuid === item.uuid)
            : this.tableData1.findIndex((i) => i.uuid === item.uuid);
        this.tabValue * 1 ? this.tableData2.splice(index, 1) : this.tableData1.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    async exportExcl(isAll) {
      if (this.tabValue * 1 ? !this.tableData2.length : !this.tableData1.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNo: this.tabValue * 1 ? this.pagination2.current : this.pagination1.current,
        pageSize: this.tabValue * 1 ? this.pagination2.pageSize : this.pagination1.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610791',
        tzlx: this.tabValue * 1 ? 'fcshtmx' : 'fcszyf',
        fileName: this.tabValue * 1 ? '合同租赁信息' : '合同基本信息',
        cxParam: {
          ...(this.tabValue * 1 ? this.formData2 : this.formData1),
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    pageChange({ current, pageSize }, type) {
      [this.pagination1, this.pagination2][type].current =
        pageSize !== [this.pagination1, this.pagination2][type].pageSize ? 1 : current;
      [this.pagination1, this.pagination2][type].pageSize = pageSize;
      this.query({ fy: true });
    },
    ckdg() {
      this.$router.push('/lsgl');
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
@import '../../../../styles/dialog.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  // padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
