<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    header="合同基本信息"
    :visible.sync="isVisible"
    :cancelBtn="null"
    :confirmBtn="{
      content: '关闭',
    }"
    :onConfirm="onClose"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <t-row :gutter="16">
            <t-col :span="4">
              <t-form-item label="合同编号" name="htbh">
                <t-input v-model="formData.htbh" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同名称" name="htmc">
                <t-input v-model="formData.htmc" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="不动产权证号" name="fwcqzsh">
                <t-input v-model="formData.fwcqzsh" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="房源编号" name="fybh">
                <t-input v-model="formData.fybh" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="房源地址" name="fwzldz">
                <t-input v-model="formData.fwzldz" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="主管税务机关" name="zgswjgMc">
                <t-input v-model="formData.zgswjgMc" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同开始日期" name="htydsxrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydsxrq"
                  style="width: 276px; height: 32px"
                  readonly
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同结束日期" name="htydzzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydzzrq"
                  style="width: 276px; height: 32px"
                  readonly
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="提前终止日期" name="zzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.zzrq"
                  style="width: 276px; height: 32px"
                  readonly
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="收款方式" name="skfs">
                <t-select v-model="formData.skfs" readonly>
                  <t-option
                    v-for="item in skfsList"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  ></t-option>
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';

export default {
  components: { CssDialog },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      dayjs,
      skfsList: [
        { value: '0', label: '月付' },
        { value: '1', label: '季付' },
        { value: '2', label: '半年付' },
        { value: '3', label: '年付' },
      ], // 收款方式
      isVisible: true,
      rules: {
        htbh: [{ required: true, message: '必填', type: 'error' }],
        htydsxrq: [{ required: true, message: '必填', type: 'error' }],
        htydzzrq: [{ required: true, message: '必填', type: 'error' }],
        fwcqzsh: [{ required: true, message: '必填', type: 'error' }],
      },
      selectedRowKeys: [],
      checkBox: [],
      formData: {
        pageNo: 1,
        pageSize: 10,
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        gsmc: '',
        gsh2: '',
        fybh: null,
        uuid: '',
        htbh: '',
        htmc: '',
        fwcqzsh: '',
        fwzldz: '',
        zgswjgMc: '',
        lrzx: '',
        htydsxrq: '',
        htydzzrq: '',
        yzjsr: '',
        hsyzjsr: '',
        sl1: 0.05,
        skfs: '',
        zzrq: '',
        czmj: '',
        zlrqq: '',
        zlrqz: '',
        sjczmj: '',
        sjhsyzjsr: '',
        sjsl1: 0.05,
        sjyzjsr: '',
        tzyy: '',
        ly: '1',
      },
    };
  },
  created() {
    // 收款方式
    this.skfsList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
  },
  mounted() {
    this.formData = this.visible.row;
  },
  methods: {
    onClose() {
      this.isVisible = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
