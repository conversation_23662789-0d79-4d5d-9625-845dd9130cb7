<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增合同基本信息', '编辑合同基本信息'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <t-row :gutter="16">
            <t-col :span="4">
              <t-form-item label="合同编号" name="htbh">
                <t-input
                  v-model.lazy="formData.htbh"
                  placeholder="请填写合同编号"
                  clearable
                  :disabled="formData.ly !== '1' || visible.pageType === 1"
                ></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同名称" name="htmc">
                <t-input
                  v-model="formData.htmc"
                  placeholder="请填写合同名称"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="不动产权证号" name="fwcqzsh">
                <t-input
                  v-model.lazy="formData.fwcqzsh"
                  placeholder="请填写不动产权证号"
                  clearable
                  :disabled="formData.ly !== '1' || visible.pageType === 1"
                ></t-input>
                <!-- @blur="handleInputBlur" -->
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="房源编号" name="fybh">
                <t-input v-model="formData.fybh" placeholder="根据不动产权证书号自动带出" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="房源地址" name="fwzldz">
                <t-input v-model="formData.fwzldz" placeholder="根据不动产权证书号自动带出" readonly></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="主管税务机关" name="zgswjgDm">
                <t-select v-model="formData.zgswjgDm" placeholder="根据不动产权证书号自动带出" readonly>
                  <t-option
                    v-for="item in zgswjgList"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  ></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同开始日期" name="htydsxrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydsxrq"
                  placeholder="请选择合同开始日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disableDate="(date) => date > new Date(this.formData.htydzzrq)"
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同结束日期" name="htydzzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydzzrq"
                  placeholder="请选择合同结束日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disableDate="(date) => date < new Date(this.formData.htydsxrq)"
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="提前终止日期" name="zzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.zzrq"
                  placeholder="请选择提前终止日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disableDate="{
                    before: dayjs(this.formData.htydsxrq).format('YYYY-MM-DD'),
                    after: dayjs(this.formData.htydzzrq).format('YYYY-MM-DD'),
                  }"
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="收款方式" name="skfs">
                <t-select
                  v-model="formData.skfs"
                  placeholder="请选择收款方式"
                  clearable
                  :disabled="formData.ly !== '1'"
                >
                  <t-option
                    v-for="item in skfsList"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  ></t-option>
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
// import { Input, InputNumber, DatePicker } from 'tdesign-vue';
import { updateZlht, saveZlht, initbQueryXq, queryBdcqzshByHtbh } from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';
import { getXzqhJdxzSwjg } from '@/pages/index/api/tzzx/gyApi/gyapi.js';

export default {
  components: { CssDialog },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      htbh: [
        { required: true, message: '必填', type: 'error' },
        {
          validator: (val) => {
            // 编辑状态直接通过验证
            return this.visible.pageType === 1 ? true : this.htbhfwcqzsonlyValidator(val);
          },
          trigger: 'blur',
        },
      ],
      htydsxrq: [{ required: true, message: '必填', type: 'error' }],
      htydzzrq: [{ required: true, message: '必填', type: 'error' }],
      fwcqzsh: [
        { required: true, message: '必填', type: 'error' },
        {
          validator: (val) => {
            // 编辑状态直接通过验证
            return this.visible.pageType === 1 ? true : this.fwcqzsValidator(val);
          },
          trigger: 'blur',
        },
      ],
    };
    return {
      dayjs,
      skfsList: [], // 收款方式
      zgswjgList: [], // 主管税务机关
      isVisible: true,
      confirmLoading: false,
      rules: {},
      selectedRowKeys: [],
      checkBox: [],
      formData: {
        pageNo: 1,
        pageSize: 10,
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        gsmc: '',
        gsh2: '',
        fybh: null,
        uuid: '',
        htbh: '',
        htmc: '',
        fwcqzsh: '',
        fwzldz: '',
        zgswjgDm: '',
        lrzx: '',
        htydsxrq: '',
        htydzzrq: '',
        yzjsr: '',
        hsyzjsr: '',
        sl1: 0.05,
        skfs: '',
        zzrq: '',
        czmj: '',
        zlrqq: '',
        zlrqz: '',
        sjczmj: '',
        sjhsyzjsr: '',
        sjsl1: 0.05,
        sjyzjsr: '',
        tzyy: '',
        ly: '1',
      },
    };
  },
  created() {
    // 收款方式
    this.skfsList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
  },
  mounted() {
    this.init();
    this.initFcsXzqhandJdxz();
    if (this.visible.row?.uuid) {
      this.formData = this.visible.row;
    }
  },
  methods: {
    async init() {
      // 基本条件
      this.rules = this.baseRules;
    },
    onClose() {
      this.isVisible = false;
    },
    async htbhfwcqzsonlyValidator() {
      // 编辑状态下直接返回验证通过
      if (this.visible.pageType === 1) {
        return { result: true, message: '', type: 'success' };
      }
      if (this.formData.htbh && this.formData.fwcqzsh) {
        const params = {
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          htbh: this.formData.htbh,
        };
        try {
          const { data } = await queryBdcqzshByHtbh(params);
          if (data && data.some((item) => item === this.formData.fwcqzsh)) {
            return {
              result: false,
              message: '已存在此合同基本信息，请查看。',
              type: 'warning',
            };
          }
        } catch (e) {
          console.error(e);
        }
      }
      return { result: true, message: '', type: 'success' };
    },
    async fwcqzsValidator() {
      // 编辑状态下直接返回验证通过
      if (this.visible.pageType === 1) {
        return { result: true, message: '', type: 'success' };
      }
      // 当同时存在合同编号和不动产权证号时，进行重复性校验
      if (this.formData.htbh && this.formData.fwcqzsh) {
        try {
          // 检查合同编号是否已存在关联证件
          const { data } = await queryBdcqzshByHtbh({
            nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
            htbh: this.formData.htbh,
          });

          if (data?.some((item) => item === this.formData.fwcqzsh)) {
            return { result: false, message: '已存在此合同基本信息，请查看。', type: 'warning' };
          }
        } catch (e) {
          console.error('合同编号查询异常:', e);
          return { result: true, message: '', type: 'success' }; // 异常时保持验证通过
        }
      }

      // 当存在不动产权证号时，查询房源信息
      if (this.formData.fwcqzsh) {
        try {
          const { data } = await initbQueryXq({
            nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
            fwcqzsh: this.formData.fwcqzsh,
          });

          if (data?.fybh) {
            // 更新房源信息
            this.formData.fwzldz = data.fwzldz;
            this.formData.fybh = data.fybh;
            this.formData.zgswjgDm = data.zgswjgDm;
          } else {
            // 清空房源信息并返回警告
            this.formData.fwzldz = null;
            this.formData.fybh = null;
            return {
              result: false,
              message: '未查询到不动产权证号对应的房源编号信息。',
              type: 'warning',
            };
          }
        } catch (e) {
          console.error('房源信息查询异常:', e);
          return { result: true, message: '', type: 'success' }; // 异常时保持验证通过
        }
      }

      // 默认返回验证通过
      return { result: true, message: '', type: 'success' };
    },
    // confirm() {
    //   this.handleInputBlur()
    //     .then(this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator }))
    //     .then(this.save());
    // },
    // async save() {
    async confirm() {
      // this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator });
      if (!this.formData.fybh) {
        this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
      } else {
        const val = await this.$refs.forms.validate();
        if (val === true) {
          this.confirmLoading = true;
          const p = {};
          [
            'fybh',
            'uuid',
            'htbh',
            'htmc',
            'fwcqzsh',
            'fwzldz',
            'zgswjgDm',
            'lrzx',
            'htydsxrq',
            'htydzzrq',
            'yzjsr',
            'hsyzjsr',
            'sl1',
            'skfs',
            'zzrq',
            'czmj',
            'zlrqq',
            'zlrqz',
            'sjczmj',
            'sjhsyzjsr',
            'sjsl1',
            'sjyzjsr',
            'tzyy',
            'ly',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = { ...p, fybh: this.formData.fybh, ...this.visible.otherObj };
          try {
            let res = {};
            if (this.visible.pageType) {
              res = await updateZlht(params);
            } else {
              res = await saveZlht(params);
            }
            const { code } = res;
            if (code === 1) {
              this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
              this.$emit('aftersaveHtjbxx');
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(2) : '0.00';
    },
    async initFcsXzqhandJdxz() {
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
      const { data } = await getXzqhJdxzSwjg(params);
      this.zgswjgList = data.DM_GY_SWJG;
      this.zgswjg = data.zgswskfjDm;
      if (this.zgswjgList.length === 1) {
        this.formData.zgswjgDm = this.zgswjgList[0].value;
      }
    },
    async handleInputBlur() {
      // 在这里编写编辑完成后的逻辑
      // this.baseRules.fwcqzsh = [{ required: true, message: '必填', type: 'error' }];
      const params = {
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.formData.fwzldz = data.fwzldz;
          this.formData.fybh = data.fybh;
        } else {
          this.formData.fwzldz = null;
          this.formData.fybh = null;
          this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
        }
      } catch (e) {
        console.error(e);
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
