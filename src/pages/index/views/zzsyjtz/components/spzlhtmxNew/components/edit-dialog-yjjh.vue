<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    header="合同基本信息"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <div>
            <p v-show="visible.pageType" class="xzsy-header">租赁合同信息</p>
            <t-row :gutter="16">
              <t-col :span="4">
                <t-form-item label="合同编号" name="htbh">
                    <t-input v-model="formData.htbh" readonly></t-input>
                  </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="合同名称" name="htmc">
                  <t-input v-model="formData.htmc" readonly></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="收款方式" name="skfs">
                  <t-select v-model="formData.skfs" readonly>
                    <t-option
                        v-for="item in skfsList"
                        :value="item.value"
                        :label="item.label"
                        :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="合同开始日期" name="zlrqq">
                  <t-date-picker
                      class="form-list-itme"
                      v-model="formData.htydsxrq"
                      style="width: 276px; height: 32px"
                      clearable
                      :disabled="true"></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="合同结束日期" name="zlrqz">
                  <t-date-picker
                      class="form-list-itme"
                      v-model="formData.htydzzrq"
                      style="width: 276px; height: 32px"
                      clearable
                      :disabled="true"></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="提前终止日期" name="zzrq">
                  <t-date-picker
                      class="form-list-itme"
                      v-model="formData.zzrq"
                      style="width: 276px; height: 32px"
                      clearable
                      :disabled="true"></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="房源编号" name="fybh">
                  <t-input v-model="formData.fybh" readonly></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="不动产权证号" name="fwcqzsh">
                  <t-input v-model="formData.fwcqzsh" readonly></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="出租面积" name="czmj">
                  <t-input v-model="formData.sjczmj" readonly></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="房源地址" name="fwzldz">
                  <t-input v-model="formData.fwzldz" readonly></t-input>
                </t-form-item>
              </t-col>
            </t-row>
          </div>
          <div v-show="true">
            <p class="xzsy-header" style="font-size: 20px;">预缴计划</p>
            <t-table
                ref="tableRef"
                row-key="xh"
                :editable-row-keys="editableRowKeys"
                hover
                :data="tableData"
                :columns="yjjhDataColumns"
                height="100%"
                @row-edit="onRowEdit"
                @row-validate="onRowValidate"
                @validate="onValidate"
                lazyLoad>
              <template #xh="{ rowIndex }">{{
                  rowIndex + 1
                }}</template>
            </t-table>
          </div>
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import {
  initZzsyjjh, saveZzsyjjhList
} from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import {
  Input,
} from 'tdesign-vue';
import dayjs from 'dayjs';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
    };
    return {
      skfsList: [], // 收款方式
      isVisible: true,
      confirmLoading: false,
      editFlag: false,
      rules: {},
      fwcqzshList: [],
      jzmj: 0,
      tableData: [
        { xh: '1', sbny: 202504, zlrqq: '2025-04', zlrqz: '2026-04', sbys: 3,sbje:60000},
        { xh: '2', sbny: 202505, zlrqq: '2025-04', zlrqz: '2026-04', sbys: 3,sbje:60000}
      ],
      htxxData: [],
      editableRowKeys: [],
      // 保存变化过的行信息
      editMap: {},
      yjjhDataColumns:[
        {
          width: 50,
          align: 'center',
          colKey: 'xh',
          title: '序号',
        },
        {
          align: 'right',
          width: 100,
          colKey: 'sbny',
          title: '申报年月',
        },
        {
          align: 'right',
          width: 100,
          colKey: 'zlrqq',
          title: '租赁日期起',
        },
        {
          align: 'right',
          width: 100,
          colKey: 'zlrqz',
          title: '租赁日期止',
        },
        {
          align: 'right',
          width: 100,
          colKey: 'sbys',
          title: '申报月数',
          edit: {
            // 1. 支持任意组件。需保证组件包含 `value` 和 `onChange` 两个属性，且 onChange 的第一个参数值为 new value。
            // 2. 如果希望支持校验，组件还需包含 `status` 和 `tips` 属性。具体 API 含义参考 Input 组件
            component: Input,
            // props, 透传全部属性到 Input 组件
            props: {
              clearable: true,
              autofocus: true,
              autoWidth: true,
              inputmode: "numeric",
              pattern: "[0-9]*"
            },
            // onEdited: (context) => {
            //   console.log(context.newRowData);
            //   this.tableData.splice(context.rowIndex, 1, context.newRowData);
            // },
            // 校验规则，此处同 Form 表单
            rules: [
              { required: true, message: '不能为空' }
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          width: 150,
          colKey: 'sbje',
          title: '申报金额（含税）',
        },
        {
          title: '操作栏',
          colKey: 'operate',
          width: 150,
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.xh);
            return (
                <div class="table-operations">
                  {!editable && (
                      <t-link theme="primary" style="margin-left:10px" hover="color" data-id={row.xh} onClick={this.onEdit}>
                        编辑
                      </t-link>
                  )}
                  {editable && (
                      <t-link theme="primary" style="margin-left:10px" hover="color" data-id={row.xh} onClick={this.onSave}>
                        保存
                      </t-link>
                  )}
                  {editable && (
                      <t-link theme="primary" style="margin-left:10px" hover="color" data-id={row.xh} onClick={this.onCancel}>
                        取消
                      </t-link>
                  )}
                </div>
            );
          },
        },
      ],
      formData: {
        pageNo: 1,
        pageSize: 10,
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        gsmc: '',
        gsh2: '',
        fybh: null,
        uuid: '',
        htbh: '',
        htmc: '',
        fwcqzsh: '',
        fwzldz: '',
        zgswjgDm: '',
        lrzx: '',
        htydsxrq: '',
        htydzzrq: '',
        yzjsr: '',
        hsyzjsr: '',
        sl1: 0.05,
        skfs: '',
        zzrq: '',
        czmj: '',
        zlrqq: '',
        zlrqz: '',
        sjczmj: '',
        sjhsyzjsr: '',
        sjsl1: 0.05,
        sjyzjsr: '',
        tzyy: '',
        ly: '1',
      },
    };
  },
  created() {
    // 收款方式
    this.skfsList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
  },
  mounted() {
    this.formData = this.visible.row;
    this.init();
  },
  computed: {
  },
  watch: {
  },
  methods: {
    async init() {
      // this.tableLoading = true;
      try{
        let params = {};
        params = {
          cxbz: '1', // 房产税租赁合同查询对应值为‘1’，增值税预缴租赁合同查询对应值为‘2’
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          fybh:this.formData.fybh,
          htbh:this.formData.htbh
        };
        let result = await initZzsyjjh(params);
        const { code, msg, data } = result;
        if (code === 1) {
          console.log(data);
          this.tableData = data.yjjh || [];
          this.htxxData = data.htxx || [];
          console.log(this.tableData);
          console.log(this.htxxData);
        } else if (msg) {
          console.log(msg);
          this.$message.warning(msg);
        } else {
          this.$message.warning(data);
        }
      }catch (e) {
        console.error(e);
        this.tableData1 = [];
        this.tableData2 = [];
        this.footData = [];
      } finally {
        // this.tableLoading = false;
      }
    },
    onClose() {
      this.isVisible = false;
    },
    onEdit(e) {
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      if(this.editableRowKeys.length > 0){
        this.$message.error('请先保存上一条修改！');
        return;
      }
      this.editableRowKeys = [];
      this.editableRowKeys.push(id);
    },
    updateEditState(id) {
      console.log('updateEditState start');
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
      console.log('updateEditState end');
    },
    onCancel(e) {
      console.log('onCancel start');
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
      console.log('onCancel end');
    },

    onSave(e) {
      console.log('onSave start');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      // 触发内部校验，而后在 onRowValidate 中接收异步校验结果
      // 重点：受框架层面限制，如果是 EnhancedTable 请更为使用 this.$refs.tableRef.primaryTableRef.validateRowData(id)
      // this.$refs.tableRef.primaryTableRef.validateRowData(id).then((params) => {
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          // MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          if (current) {
            this.tableData.splice(current.rowIndex, 1, current.editedRow);
            // MessagePlugin.success('保存成功');
            // 获取编辑行的数据
            const editedRow = this.tableData.find(row => row.xh === id);
            if (editedRow) {
              console.log('编辑行的数据:', editedRow);
              const xh = editedRow.xh;
              //修改行之前的预缴计划不变
              var ysyjjh = [];
              if(xh != 1){
                ysyjjh = this.tableData.slice(0,xh-1);
              }
              //根据skfs获取合同应该的申报年月
              const skfs = this.formData.skfs;
              var htsbys = 0;
              if(skfs == '0'){
                htsbys = 1;
              }else if(skfs == '1'){
                htsbys = 3;
              }else if(skfs == '2'){
                htsbys = 6;
              }else if(skfs == '3'){
                htsbys = 12;
              }
              //修改后的申报年月
              const xgsbys = editedRow.sbys;
              console.log('xgsbys:', xgsbys);
              //预缴计划信息
              const yjjhList = this.tableData;
              //合同明细信息
              const htxxList = this.htxxData;
              //合同最后一个月信息
              const lastHtxx = htxxList[htxxList.length-1];
              //需要计算的申报日期起止
              const sbrqq = new Date(editedRow.zlrqq);
              const sbrqz = new Date(lastHtxx.zlrqq);
              console.log('editedRow.zlrqq:', editedRow.zlrqq);
              console.log('lastHtxx.zlrqq:', lastHtxx.zlrqq);
              //获取合同的申报日期
              const sbzys = this.getMonthDifference(sbrqq,sbrqz);
              const firstSbys = xgsbys;
              console.log('firstSbys:', firstSbys);
              const otherSbys = sbzys - firstSbys;
              const count = Math.ceil(otherSbys / htsbys);
              const firstSbjh = this.generateDateRanges(sbrqq,firstSbys,1);
              const otherSbrqq = dayjs(sbrqq).add(firstSbys, 'month');
              const otherSbJh = this.generateDateRanges(otherSbrqq,htsbys,count);
              // this.tableData = firstSbjh;
              const jhxxList = [...ysyjjh, ...firstSbjh, ...otherSbJh];
              this.getSbzj(jhxxList,htxxList);
              this.tableData = jhxxList;
              console.log('sbzys:', sbzys);
            }
          }
          this.updateEditState(this.currentSaveId);
        }
      });
      console.log('onSave end');
    },
    getMonthDifference(startDate, endDate) {
      const startYear = startDate.getFullYear();
      const startMonth = startDate.getMonth();
      const endYear = endDate.getFullYear();
      const endMonth = endDate.getMonth();
      return (endYear - startYear) * 12 + (endMonth - startMonth + 1);
    },
    generateDateRanges(startDate, intervalMonths, count) {
        const result = [];
        let currentDate = new Date(startDate);
        // console.log('startDate',this.formatDateDay(startDate));
        console.log('currentDate',this.formatDateDay(currentDate));
        for (let i = 0; i < count; i++) {
          const start = new Date(currentDate);
          console.log('=============================================');
          console.log('start',this.formatDateDay(start));
          console.log('intervalMonths',intervalMonths);
          const end = new Date(dayjs(start).add(intervalMonths-1, 'month'));
          end.setMonth(end.getMonth() + 1, 0);
          console.log('end',this.formatDateDay(end));
          // end.setMonth(end.getMonth() + intervalMonths - 1); // 减去1因为包含起始月
          // end.setMonth(end.getMonth() + 1, 0);
          const startStr = this.formatDateDay(start);
          const endStr = this.formatDateDay(end);
          const sbnyStr = start.getFullYear() + String(start.getMonth() + 1).padStart(2, '0');
          const sbnyInt = Number(sbnyStr);
          result.push({
            sbys:intervalMonths,
            zlrqq: startStr,
            zlrqz: endStr,
            sbny: sbnyInt
          });
          // 移动到下一个时间段的开始
          currentDate = new Date(dayjs(currentDate).add(intervalMonths, 'month'))
        }
        return result;
      },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    formatDateDay(formDate) {
      const year = formDate.getFullYear();
      const month = String(formDate.getMonth() + 1).padStart(2, '0');
      const day = String(formDate.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    getSbzj(yjjhList, htxxList) {
      console.log('yjjhList', yjjhList);
      console.log('htxxList', htxxList);
      let xh = 0;
      for (const zzsyjjh of yjjhList) {
        xh++;
        zzsyjjh.xh = xh + '';
        const zlrqq = new Date(zzsyjjh.zlrqq);
        const zlrqz = new Date(zzsyjjh.zlrqz);
        let sbje = 0.0;
        let yzjsr = 0.0;
        for(const htxx of htxxList){
          const htzlrqq = new Date(htxx.zlrqq);
            if(zlrqq<=htzlrqq && zlrqz>= htzlrqq){
              const sjhsyzjsr = Number(htxx.sjhsyzjsr);
              const sjyzjsr = Number(htxx.sjyzjsr);
              sbje+= sjhsyzjsr;
              yzjsr+=sjyzjsr;
            }
          }
        zzsyjjh.sbje = this.convertToFloat(sbje);
        zzsyjjh.sjyzjsr = this.convertToFloat(yzjsr);
        zzsyjjh.htbh = this.formData.htbh;
        zzsyjjh.fybh = this.formData.fybh
      }
      return yjjhList;
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        console.log('Promise Table Data Validate:', params);
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          // MessagePlugin.warning(firstError[0].message);
        }
      });
    },

    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },

    onRowEdit(params) {
      const {
        row,
        col,
        value,
      } = params;
      const oldRowData = this.editMap[row.xh]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.xh] = {
        ...params,
        editedRow,
      };
    },

    async confirm() {
      if(this.editableRowKeys.length > 0){
        this.$message.error('请先保存修改后再保存预缴计划！');
        return;
      }
        const val = await this.$refs.forms.validate();
        if (val === true) {
          this.confirmLoading = true;
          const params = this.tableData;
          try {
            let res = {};
            res = await saveZzsyjjhList(params);
            console.log('res:', res);
            const { code } = res;
            const { data } = res;
            console.log('data:', data);
            if (code === 1) {
              if(data == '1'){
                this.$message.success('保存成功');
              }else {
                this.$message.success(data);
                return;
              }
            }else {
              this.$message.error('保存失败');
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
    },
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(2) : '0.00';
    },
  }
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
