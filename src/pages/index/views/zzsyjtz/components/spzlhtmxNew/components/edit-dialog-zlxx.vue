<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增合同租赁信息', '编辑合同租赁信息'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <div>
            <p v-show="visible.pageType" class="xzsy-header">租赁合同信息</p>
            <t-row :gutter="16">
              <t-col :span="4">
                <t-form-item label="合同编号" name="htbh">
                  <t-input
                    v-model.lazy="formData.htbh"
                    placeholder="请填写合同编号"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1"
                  ></t-input>
                </t-form-item>
              </t-col>
              <!-- <t-col :span="4">
                <t-form-item label="不动产权证号" name="fwcqzsh">
                  <t-input v-model="formData.fwcqzsh" placeholder="请填写不动产权证号" clearable disabled></t-input>
                </t-form-item>
              </t-col> -->
              <t-col :span="4">
                <t-form-item label="不动产权证号" name="fwcqzsh">
                  <t-select
                    v-model.lazy="formData.fwcqzsh"
                    placeholder="请选择不动产权证号"
                    clearable
                    :disabled="this.visible.pageType === 1"
                    @change="htbhFybhHandleInputBlur"
                  >
                    <t-option
                      v-for="item in fwcqzshList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="合同名称" name="htmc">
                  <t-input v-model="formData.htmc" placeholder="根据合同编号自动带出" readonly></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="房源编号" name="fybh">
                  <t-input v-model="formData.fybh" placeholder="根据不动产权证书号自动带出" readonly></t-input>
                  <!-- :disabled="formData.ly !== '1' || this.visible.pageType === 1" -->
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="收款方式" name="skfs">
                  <t-select v-model="formData.skfs" placeholder="根据不动产权证书号自动带出" readonly>
                    <t-option
                      v-for="item in skfsList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="出租面积" name="czmj">
                  <gt-input-money
                    v-model="formData.czmj"
                    theme="normal"
                    align="left"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1 || this.editFlag"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="租赁开始日期" name="zlrqq">
                  <t-date-picker
                    class="form-list-itme"
                    v-model="formData.zlrqq"
                    placeholder="请选择租赁开始日期"
                    style="width: 276px; height: 32px"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1 || this.editFlag"
                    :disableDate="this.validatorZlksrq"
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="租赁结束日期" name="zlrqz">
                  <t-date-picker
                    class="form-list-itme"
                    v-model="formData.zlrqz"
                    placeholder="请选择租赁结束日期"
                    style="width: 276px; height: 32px"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1 || this.editFlag"
                    :disableDate="this.validatorZljsrq"
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="月租金收入(不含税)" name="yzjsr">
                  <gt-input-money
                    v-model.number="formData.yzjsr"
                    @blur="handleYzjsrChange"
                    theme="normal"
                    align="left"
                    :digit="6"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1 || this.editFlag"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="预缴税率" name="sl1">
                  <gt-input-money
                    v-model.number="formData.sl1"
                    @blur="handleSl1Change"
                    theme="normal"
                    align="left"
                    clearable
                    :disabled="formData.ly !== '1' || this.visible.pageType === 1 || this.editFlag"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="月租金收入(含税)" name="hsyzjsr">
                  <gt-input-money v-model.number="formData.hsyzjsr" theme="normal" align="left" :digit="6" disabled />
                </t-form-item>
              </t-col>
            </t-row>
          </div>
          <div v-show="visible.pageType">
            <p class="xzsy-header">租赁实际信息</p>
            <t-row :gutter="16">
              <t-col :span="4">
                <t-form-item label="实际出租面积" name="sjczmj">
                  <gt-input-money v-model.number="formData.sjczmj" theme="normal" align="left" clearable />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="实际月租金收入(不含税)" name="sjyzjsr">
                  <gt-input-money
                    v-model.number="formData.sjyzjsr"
                    @blur="handleSjyzjsrChange"
                    theme="normal"
                    align="left"
                    :digit="6"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="实际预缴税率" name="sjsl1">
                  <gt-input-money
                    v-model.number="formData.sjsl1"
                    @blur="handleSjsl1Change"
                    theme="normal"
                    align="left"
                    clearable
                  />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="实际月租金收入(含税)" name="sjhsyzjsr">
                  <gt-input-money v-model.number="formData.sjhsyzjsr" theme="normal" align="left" :digit="6" disabled />
                </t-form-item>
              </t-col>
              <t-col :span="4">
                <t-form-item label="调整原因" name="tzyy">
                  <t-input v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
                </t-form-item>
              </t-col>
            </t-row>
          </div>
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import {
  queryHtxxByHtbhFybh,
  updateHtmxxx,
  saveHtmxxx,
  initbQueryXq,
  queryBdcqzshByHtbh,
} from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      htbh: [
        { required: true, message: '必填', type: 'error' },
        // 添加条件判断，仅在新增时校验（pageType=0）
        {
          validator: (val) => {
            // 编辑状态直接通过验证
            return this.visible.pageType === 1 ? true : this.htbhValidator(val);
          },
          trigger: 'blur',
        },
      ],
      zlrqq: [{ required: true, message: '必填', type: 'error' }],
      zlrqz: [{ required: true, message: '必填', type: 'error' }],
      zjsr1: [{ required: true, message: '必填', type: 'error' }],
      yzjsr: [{ required: true, message: '必填', type: 'error' }],
      czmj: [
        { required: true, message: '必填', type: 'error' },
        { validator: this.czmjValidator, trigger: 'blur' },
      ],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      hsyzjsr: [{ required: true, message: '必填', type: 'error' }],
      fwcqzsh: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      skfsList: [], // 收款方式
      isVisible: true,
      confirmLoading: false,
      editFlag: false,
      rules: {},
      fwcqzshList: [],
      jzmj: 0,
      formData: {
        pageNo: 1,
        pageSize: 10,
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        gsmc: '',
        gsh2: '',
        fybh: null,
        uuid: '',
        htbh: '',
        htmc: '',
        fwcqzsh: '',
        fwzldz: '',
        zgswjgDm: '',
        lrzx: '',
        htydsxrq: '',
        htydzzrq: '',
        yzjsr: '',
        hsyzjsr: '',
        sl1: 0.05,
        skfs: '',
        zzrq: '',
        czmj: '',
        zlrqq: '',
        zlrqz: '',
        sjczmj: '',
        sjhsyzjsr: '',
        sjsl1: 0.05,
        sjyzjsr: '',
        tzyy: '',
        ly: '1',
      },
    };
  },
  created() {
    // 收款方式
    this.skfsList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
  },
  mounted() {
    this.init();
    if (this.visible.row?.uuid) {
      this.formData = this.visible.row;
      this.rules = {
        ...this.baseRules,
        sjczmj: [{ required: true, message: '必填', type: 'error' }],
        sjyzjsr: [{ required: true, message: '必填', type: 'error' }],
        sjsl1: [{ required: true, message: '必填', type: 'error' }],
        sjhsyzjsr: [{ required: true, message: '必填', type: 'error' }],
        tzyy: [{ required: true, message: '必填', type: 'error' }],
      };
      // 编辑时，需要获取jzmj字段值
      this.getJzmj();
    }
  },
  computed: {
    validatorZlksrq() {
      if (this.formData.zzrq) {
        return {
          before: dayjs(this.formData.htydsxrq).subtract(1, 'day').format('YYYY-MM-DD'),
          after: this.formData.zzrq,
        };
      }
      if (this.formData.zlrqz) {
        return {
          before: dayjs(this.formData.htydsxrq).subtract(1, 'day').format('YYYY-MM-DD'),
          after: this.formData.zlrqz,
        };
      }
      return {
        before: dayjs(this.formData.htydsxrq).subtract(1, 'day').format('YYYY-MM-DD'),
        after: this.formData.htydzzrq,
      };
    },
    validatorZljsrq() {
      if (this.formData.zzrq) {
        return {
          before: dayjs(this.formData.htydsxrq).subtract(1, 'day').format('YYYY-MM-DD'),
          after: this.formData.zzrq,
        };
      }
      if (this.formData.zlrqq) {
        return {
          before: dayjs(this.formData.zlrqq).subtract(1, 'day').format('YYYY-MM-DD'),
          after: this.formData.htydzzrq,
        };
      }
      return {
        before: dayjs(this.formData.htydsxrq).subtract(1, 'day').format('YYYY-MM-DD'),
        after: this.formData.htydzzrq,
      };
    },
  },
  watch: {
    'formData.czmj': function () {
      if (!this.visible.pageType) {
        this.formData.sjczmj = this.formData.czmj;
      }
    },
  },
  methods: {
    async getJzmj() {
      try {
        const { data } = await initbQueryXq({ nsrsbh: this.visible.otherObj.nsrsbh, fwcqzsh: this.formData.fwcqzsh });
        if (data && data.fybh) {
          this.jzmj = data.jzmj ? Number(data.jzmj) : 0;
        }
      } catch (e) {
        console.error(e);
      }
    },
    async init() {
      // 基本条件
      this.rules = this.baseRules;
      if (this.visible.pageType === 0) {
        this.editFlag = true;
        console.log(this.$store.state.fcstz.zlhtxxInfo, 'this.$store.state.fcstz.zlhtxxInfo');
        if (this.$store.state.fcstz.zlhtxxInfo.htbh) {
          this.formData.htbh = this.$store.state.fcstz.zlhtxxInfo.htbh;
          this.formData.fwcqzsh = this.$store.state.fcstz.zlhtxxInfo.fwcqzsh;
          this.formData.fybh = this.$store.state.fcstz.zlhtxxInfo.fybh;
          this.htbhFybhHandleInputBlur();
        }
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async czmjValidator() {
      const params = {
        nsrsbh: this.visible.otherObj.nsrsbh,
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.jzmj = data.jzmj ? Number(data.jzmj) : 0;
        }
      } catch (e) {
        console.error(e);
      }
      if (this.formData.czmj > this.jzmj) {
        return {
          result: false,
          message: `出租面积不能大于该房源的建筑面积（${this.jzmj}㎡）。`,
          type: 'warning',
        };
      }
      return { result: true, message: '', type: 'success' };
    },
    // confirm() {
    //   this.handleInputBlur()
    //     .then(this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator }))
    //     .then(this.save());
    // },
    // async save() {
    async confirm() {
      // this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator });
      if (!this.formData.uuid) {
        this.$message.warning(
          '未查询到相匹配的租赁合同基础信息，请确认合同编号与房源编号输入准确后再录入租赁合同信息。',
        );
      } else if (!this.formData.fybh) {
        this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
      } else if (this.formData.czmj > this.jzmj) {
        this.$message.warning(`出租面积不能大于不动产权证号对应的房源的建筑面积（${this.jzmj}㎡）。`);
      } else {
        const val = await this.$refs.forms.validate();
        if (val === true) {
          this.confirmLoading = true;
          const p = {};
          [
            'fybh',
            'uuid',
            'htbh',
            'htmc',
            'fwcqzsh',
            'fwzldz',
            'zgswjgDm',
            'lrzx',
            'htydsxrq',
            'htydzzrq',
            'yzjsr',
            'hsyzjsr',
            'sl1',
            'skfs',
            'zzrq',
            'czmj',
            'zlrqq',
            'zlrqz',
            'sjczmj',
            'sjhsyzjsr',
            'sjsl1',
            'sjyzjsr',
            'tzyy',
            'ly',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = { ...p, fybh: this.formData.fybh, ...this.visible.otherObj };
          try {
            let res = {};
            if (this.visible.pageType) {
              res = await updateHtmxxx(params);
            } else {
              res = await saveHtmxxx(params);
            }
            const { code } = res;
            if (code === 1) {
              this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
              this.$emit('aftersaveHtmxxx');
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(6) : '0.000000';
    },
    async htbhValidator() {
      // 编辑状态下直接返回验证通过
      if (this.visible.pageType === 1) {
        return { result: true, message: '', type: 'success' };
      }
      if (this.formData.htbh) {
        const params = {
          nsrsbh: this.visible.otherObj.nsrsbh,
          htbh: this.formData.htbh,
        };
        try {
          const { data } = await queryBdcqzshByHtbh(params);
          if (data.length) {
            this.fwcqzshList = data.map((item) => ({
              value: item,
              label: item,
            }));
            // 如果合同编号对应一个不动产权证号需要自动带出
            if (data.length === 1) {
              // 使用数组解构赋值自动带出第一个不动产权证号
              [this.formData.fwcqzsh] = data;
              // 同时调用不动产权证书号的带出逻辑，进一步带出数据
              this.htbhFybhHandleInputBlur();
            }
          } else {
            return {
              result: false,
              message: '未查询到该合同信息，请返回合同基本信息页维护。',
              type: 'warning',
            };
          }
        } catch (e) {
          console.error(e);
        }
      } else {
        this.fwcqzshList = [];
        this.formData.fwcqzsh = '';
      }
      return { result: true, message: '', type: 'success' };
    },
    async htbhFybhHandleInputBlur() {
      console.log('检查是否触发htbhFybhHandleInputBlur');
      if (this.formData.htbh && this.formData.fwcqzsh) {
        try {
          const { data } = await queryHtxxByHtbhFybh({
            djxh: this.visible.otherObj.djxh,
            nsrsbh: this.visible.otherObj.nsrsbh,
            htbh: this.formData.htbh,
            fwcqzsh: this.formData.fwcqzsh,
          });
          if (data) {
            console.log('data', data);
            this.formData.uuid = data.uuid;
            this.formData.htmc = data.htmc;
            this.formData.fybh = data.fybh;
            this.formData.skfs = data.skfs;
            this.formData.jzmj = data.jzmj;
            this.formData.zlrqq = data?.zlrqq || data.htydsxrq;
            this.formData.zlrqz = data?.zlrqz || data.htydzzrq;
            this.formData.htydsxrq = data.htydsxrq;
            this.formData.htydzzrq = data.htydzzrq;
            this.formData.czmj = this.convertToFloat(data.czmj);
            this.formData.yzjsr = this.convertToFloat(data.yzjsr);
            this.formData.sl1 = this.convertToFloat(data.sl1);
            this.editFlag = false;
          }
        } catch (e) {
          console.error(e);
          this.editFlag = true;
        }
      }
    },
    // 新增计算方法
    handleYzjsrChange(value) {
      if (this.formData.sl1) {
        this.formData.hsyzjsr = (value * (1 + Number(this.formData.sl1))).toFixed(6);
        if (!this.formData.hsyzjsr) this.formData.hsyzjsr = 0;
      }
      if (!this.visible.pageType) this.formData.sjyzjsr = value;
    },
    handleSl1Change(value) {
      if (this.formData.yzjsr) {
        this.formData.hsyzjsr = (this.formData.yzjsr * (1 + value)).toFixed(6);
        if (!this.formData.hsyzjsr) this.formData.hsyzjsr = 0;
      }
      if (!this.visible.pageType) this.formData.sjsl1 = value;
    },
    // handleCzmjChange(value) {
    //   if (!this.visible.pageType) this.formData.sjczmj = value;
    // },
    handleSjsl1Change(value) {
      if (this.formData.sjyzjsr) {
        this.formData.sjhsyzjsr = (this.formData.sjyzjsr * (1 + value)).toFixed(6);
        if (!this.formData.sjhsyzjsr) this.formData.sjhsyzjsr = 0;
      }
    },
    handleSjyzjsrChange(value) {
      if (this.formData.sjsl1) {
        this.formData.sjhsyzjsr = (value * (1 + Number(this.formData.sjsl1))).toFixed(6);
        if (!this.formData.sjhsyzjsr) this.formData.sjhsyzjsr = 0;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
