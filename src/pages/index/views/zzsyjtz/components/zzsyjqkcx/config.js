import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberToCurrency';

export const querySearchConfig = [
  {
    label: '年度',
    key: 'nd',
    type: 'datepicker',
    mode: 'year',
    value: dayjs().format('YYYY'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同名称',
    key: 'htmc',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '行政区划',
    key: 'xzqhList',
    type: 'tree-select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    multiple: true,
    filterable: true,
    minCollapsedNum: 1,
    value: [],
  },
  {
    label: '房源地址',
    key: 'fydz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];

export const querySearchConfigOneRules = {};

export const dataColumns = [
  //   {
  //     colKey: 'row-select',
  //     type: 'multiple',
  //     className: 'demo-multiple-select-cell',
  //     width: 50,
  //     align: 'center',
  //     fixed: 'left',
  //     foot: '合计',
  //   },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
    fixed: 'left',
  },
  {
    colKey: 'htbh',
    title: '合同编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'htmc',
    title: '合同名称',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fydz',
    title: '房源地址',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xzqhMc',
    title: '行政区划',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'swjgmc',
    title: '主管税务机关',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'nd',
    title: '年度',
    width: 80,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'January',
    title: '一月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr1',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr1)}</div>,
      },
      {
        colKey: 'yfsbsr1',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr1)}</div>,
      },
    ],
  },
  {
    colKey: 'February',
    title: '二月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr2',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr2)}</div>,
      },
      {
        colKey: 'yfsbsr2',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr2)}</div>,
      },
    ],
  },
  {
    colKey: 'March',
    title: '三月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr3',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr3)}</div>,
      },
      {
        colKey: 'yfsbsr3',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr3)}</div>,
      },
    ],
  },
  {
    colKey: 'April',
    title: '四月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr4',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr4)}</div>,
      },
      {
        colKey: 'yfsbsr4',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr4)}</div>,
      },
    ],
  },
  {
    colKey: 'May',
    title: '五月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr5',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr5)}</div>,
      },
      {
        colKey: 'yfsbsr5',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr5)}</div>,
      },
    ],
  },
  {
    colKey: 'June',
    title: '六月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr6',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr6)}</div>,
      },
      {
        colKey: 'yfsbsr6',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr6)}</div>,
      },
    ],
  },
  {
    colKey: 'July',
    title: '七月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr7',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr7)}</div>,
      },
      {
        colKey: 'yfsbsr7',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr7)}</div>,
      },
    ],
  },
  {
    colKey: 'August',
    title: '八月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr8',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr8)}</div>,
      },
      {
        colKey: 'yfsbsr8',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr8)}</div>,
      },
    ],
  },
  {
    colKey: 'September',
    title: '九月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr9',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr9)}</div>,
      },
      {
        colKey: 'yfsbsr9',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr9)}</div>,
      },
    ],
  },
  {
    colKey: 'October',
    title: '十月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr10',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr10)}</div>,
      },
      {
        colKey: 'yfsbsr10',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr10)}</div>,
      },
    ],
  },
  {
    colKey: 'November',
    title: '十一月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr11',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr11)}</div>,
      },
      {
        colKey: 'yfsbsr11',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr11)}</div>,
      },
    ],
  },
  {
    colKey: 'December',
    title: '十二月',
    align: 'center',
    children: [
      {
        colKey: 'yfyssr12',
        title: '应申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssr12)}</div>,
      },
      {
        colKey: 'yfsbsr12',
        title: '实际申报收入',
        align: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsr12)}</div>,
      },
    ],
  },
  {
    colKey: 'hj',
    title: '合计',
    align: 'center',
    fixed: 'right',
    children: [
      {
        colKey: 'yfyssrhj',
        title: '应申报收入',
        align: 'right',
        fixed: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfyssrhj)}</div>,
      },
      {
        colKey: 'yfsbsrhj',
        title: '实际申报收入',
        align: 'right',
        fixed: 'right',
        width: 140,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
        cell: (h, { row }) => <div>{numberToPrice(row.yfsbsrhj)}</div>,
      },
    ],
  },
];
