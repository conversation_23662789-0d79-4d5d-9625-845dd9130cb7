<!--
* @Descripttion: 台账-增值税预缴-增值税 预缴情况查询
* @Version: 1.0
* @Author: ljf
* @Date: 2024-11-04 09:00:00
* @LastEditors: 
* @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :pagination="pagination"
        :loading="tableLoading"
        :foot-data="footData"
        @page-change="pageChange"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { downloadBlobFile } from '@/core/download';
import { numberToPrice } from '@/utils/numberToCurrency';
import { queryZzsyjqkList, getXzqhTreeData } from '@/pages/index/api/tzzx/zzsyjtz/zzsyjmx.js';
import { DownloadIcon } from 'tdesign-icons-vue';
import { querySearchConfig, dataColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
  },
  data() {
    this.dataColumns = dataColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    return {
      userInfo: {},
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      tableData: [],
      footData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYY');
    },
  },
  created() {},
  mounted() {
    this.initQueryConditions();
  },
  methods: {
    initQueryConditions() {
      getXzqhTreeData({ djxh: this.$store.state.zzstz.userInfo?.djxh }).then((res) => {
        this.querySearchConfig[5].selectList = res.data || [];
      });
    },
    resetQueryParams() {
      this.$refs.queryControl.onReset();
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm; // 默认无需回到第一页，无父传参
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      // 不变的基础入参
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '100',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        if (p.ssyf) {
          p.nd = String(p.ssyf.substring(0, 4)); // 修改父传参YYYYMM为YYYY-MM给上面的时间框
        }
        this.$refs.queryControl.setParams(p); // 覆盖上方的查询条件为父传参
        params = { ...p, ...params }; // 基础入参+父传参
      } else {
        params = { ...this.formData, sszq: this.sszqC, ...params }; // 没有父传参的话,传查询条件+基础入参.查询框sszq绑定为YYYY-MM，需要使用sszqC覆盖
      }
      try {
        const { data } = await queryZzsyjqkList(params); // 不建议api.  建议使用接口真实尾缀，方便定位代码。
        this.tableData = data || [];
        // 表格长度大于1时展示表尾总计行。
        if (this.tableData.length > 0) {
          // const hj = {};
          const hj = this.tableData.reduce(function (prev, cur) {
            console.log('prev', prev);
            console.log('cur', cur);
            return {
              yfyssrhj: Number(cur.yfyssrhj) + Number(prev?.yfyssrhj || 0),
              yfsbsrhj: Number(cur.yfsbsrhj) + Number(prev?.yfsbsrhj || 0),
              yfyssr1: Number(cur.yfyssr1) + Number(prev?.yfyssr1 || 0),
              yfsbsr1: Number(cur.yfsbsr1) + Number(prev?.yfsbsr1 || 0),
              yfyssr2: Number(cur.yfyssr2) + Number(prev?.yfyssr2 || 0),
              yfsbsr2: Number(cur.yfsbsr2) + Number(prev?.yfsbsr2 || 0),
              yfyssr3: Number(cur.yfyssr3) + Number(prev?.yfyssr3 || 0),
              yfsbsr3: Number(cur.yfsbsr3) + Number(prev?.yfsbsr3 || 0),
              yfyssr4: Number(cur.yfyssr4) + Number(prev?.yfyssr4 || 0),
              yfsbsr4: Number(cur.yfsbsr4) + Number(prev?.yfsbsr4 || 0),
              yfyssr5: Number(cur.yfyssr5) + Number(prev?.yfyssr5 || 0),
              yfsbsr5: Number(cur.yfsbsr5) + Number(prev?.yfsbsr5 || 0),
              yfyssr6: Number(cur.yfyssr6) + Number(prev?.yfyssr6 || 0),
              yfsbsr6: Number(cur.yfsbsr6) + Number(prev?.yfsbsr6 || 0),
              yfyssr7: Number(cur.yfyssr7) + Number(prev?.yfyssr7 || 0),
              yfsbsr7: Number(cur.yfsbsr7) + Number(prev?.yfsbsr7 || 0),
              yfyssr8: Number(cur.yfyssr8) + Number(prev?.yfyssr8 || 0),
              yfsbsr8: Number(cur.yfsbsr8) + Number(prev?.yfsbsr8 || 0),
              yfyssr9: Number(cur.yfyssr9) + Number(prev?.yfyssr9 || 0),
              yfsbsr9: Number(cur.yfsbsr9) + Number(prev?.yfsbsr9 || 0),
              yfyssr10: Number(cur.yfyssr10) + Number(prev?.yfyssr10 || 0),
              yfsbsr10: Number(cur.yfsbsr10) + Number(prev?.yfsbsr10 || 0),
              yfyssr11: Number(cur.yfyssr11) + Number(prev?.yfyssr11 || 0),
              yfsbsr11: Number(cur.yfsbsr11) + Number(prev?.yfsbsr11 || 0),
              yfyssr12: Number(cur.yfyssr12) + Number(prev?.yfyssr12 || 0),
              yfsbsr12: Number(cur.yfsbsr12) + Number(prev?.yfsbsr12 || 0),
            };
          }, 0);
          console.log('hj', hj);
          Object.keys(hj).forEach((key) => {
            hj[key] = numberToPrice(hj[key]);
          });
          this.footData = [hj];
        } else {
          this.footData = [];
        }
        this.pagination.total = this.tableData.length;
      } catch (e) {
        console.log(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610865',
        tzlx: 'zzsyjqkmx',
        fileName: '增值税预缴情况查询',
        cxParam: {
          ...this.formData,
          sszq: this.sszqC,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.t-table__th-hj {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsrhj {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-January {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr1 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-February {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr2 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-March {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr3 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-April {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr4 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-May {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr5 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-June {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr6 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-July {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr7 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-August {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr8 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-September {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr9 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-October {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr10 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-November {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr11 {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-December {
  border-right: 2px solid #e7e7e7 !important;
}
/deep/.t-table__th-sbsr12 {
  border-right: 2px solid #e7e7e7 !important;
}
</style>
