<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增租赁合同信息', '编辑租赁合同信息'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <t-row :gutter="16">
            <t-col :span="4">
              <t-form-item label="合同编号" name="htbh">
                <t-input
                  v-model="formData.htbh"
                  placeholder="请填写合同编号"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同名称" name="htmc">
                <t-input
                  v-model="formData.htmc"
                  placeholder="请填写合同名称"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="不动产权证号" name="fwcqzsh">
                <t-input
                  v-model="formData.fwcqzsh"
                  placeholder="请填写不动产权证号"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-input>
                <!-- @blur="handleInputBlur" -->
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="房源地址" name="fwzldz">
                <t-input v-model="formData.fwzldz" placeholder="请填写房源地址" disabled></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="主管税务机关" name="zgswjgDm">
                <t-select
                  v-model="formData.zgswjgDm"
                  placeholder="请选择主管税务机关"
                  clearable
                  :disabled="formData.ly !== '1'"
                >
                  <t-option
                    v-for="item in zgswjgList"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  ></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同开始日期" name="htydsxrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydsxrq"
                  placeholder="请选择合同开始日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="合同结束日期" name="htydzzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.htydzzrq"
                  placeholder="请选择合同结束日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="提前终止日期" name="zzrq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.zzrq"
                  placeholder="请选择提前终止日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="收款方式" name="skfs">
                <t-select
                  v-model="formData.skfs"
                  placeholder="请选择收款方式"
                  clearable
                  :disabled="formData.ly !== '1'"
                >
                  <t-option
                    v-for="item in skfsList"
                    :value="item.value"
                    :label="item.label"
                    :key="item.value"
                  ></t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="租赁开始日期" name="zlrqq">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.zlrqq"
                  placeholder="请选择租赁开始日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="租赁结束日期" name="zlrqz">
                <t-date-picker
                  class="form-list-itme"
                  v-model="formData.zlrqz"
                  placeholder="请选择租赁结束日期"
                  style="width: 276px; height: 32px"
                  clearable
                  :disabled="formData.ly !== '1'"
                ></t-date-picker>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="出租面积" name="czmj">
                <gt-input-money
                  v-model="formData.czmj"
                  theme="normal"
                  align="left"
                  clearable
                  :disabled="formData.ly !== '1'"
                />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="月租金收入(不含税)" name="yzjsr">
                <gt-input-money
                  v-model="formData.yzjsr"
                  theme="normal"
                  align="left"
                  clearable
                  :disabled="formData.ly !== '1'"
                />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="预缴税率" name="sl1">
                <gt-input-money
                  v-model="formData.sl1"
                  theme="normal"
                  align="left"
                  clearable
                  :disabled="formData.ly !== '1'"
                />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="月租金收入(含税)" name="hsyzjsr">
                <gt-input-money v-model="formData.hsyzjsr" theme="normal" align="left" disabled />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="实际出租面积" name="sjczmj">
                <gt-input-money v-model="formData.sjczmj" theme="normal" align="left" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="实际月租金收入(不含税)" name="sjyzjsr">
                <gt-input-money v-model="formData.sjyzjsr" theme="normal" align="left" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="实际预缴税率" name="sjsl1">
                <gt-input-money v-model="formData.sjsl1" theme="normal" align="left" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="实际月租金收入(含税)" name="sjhsyzjsr">
                <gt-input-money v-model="formData.sjhsyzjsr" theme="normal" align="left" disabled />
              </t-form-item>
            </t-col>
          </t-row>
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
// import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
// import { Input, InputNumber, DatePicker } from 'tdesign-vue';
import { getXzqhJdxzSwjg } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { updateZlht, saveZlht, initbQueryXq } from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      htbh: [{ required: true, message: '必填', type: 'error' }],
      htydsxrq: [{ required: true, message: '必填', type: 'error' }],
      htydzzrq: [{ required: true, message: '必填', type: 'error' }],
      zlrqq: [{ required: true, message: '必填', type: 'error' }],
      zlrqz: [{ required: true, message: '必填', type: 'error' }],
      zjsr1: [{ required: true, message: '必填', type: 'error' }],
      yzjsr: [{ required: true, message: '必填', type: 'error' }],
      czmj: [{ required: true, message: '必填', type: 'error' }, { validator: this.czmjValidator }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      hsyzjsr: [{ required: true, message: '必填', type: 'error' }],
      fwcqzsh: [{ required: true, message: '必填', type: 'error' }, { validator: this.fwcqzsValidator }],
      sjczmj: [{ validator: this.sjCzmjValidator }],
    };
    return {
      skfsList: [], // 收款方式
      zgswjgList: [], // 主管税务机关
      isVisible: true,
      confirmLoading: false,
      rules: {},
      selectedRowKeys: [],
      checkBox: [],
      jzmj: 0,
      formData: {
        pageNo: 1,
        pageSize: 10,
        djxh: '',
        nsrsbh: '',
        nsrmc: '',
        gsmc: '',
        gsh2: '',
        fybh: null,
        uuid: '',
        htbh: '',
        htmc: '',
        fwcqzsh: '',
        fwzldz: '',
        zgswjgDm: '',
        lrzx: '',
        htydsxrq: '',
        htydzzrq: '',
        yzjsr: '',
        hsyzjsr: '',
        sl1: 0.05,
        skfs: '',
        zzrq: '',
        czmj: '',
        zlrqq: '',
        zlrqz: '',
        sjczmj: '',
        sjhsyzjsr: '',
        sjsl1: 0.05,
        sjyzjsr: '',
        ly: '1',
      },
    };
  },
  created() {
    // 收款方式
    this.skfsList = [
      { value: '0', label: '月付' },
      { value: '1', label: '季付' },
      { value: '2', label: '半年付' },
      { value: '3', label: '年付' },
    ];
  },
  mounted() {
    this.init();
    this.initFcsXzqhandJdxz();
    if (this.visible.row?.uuid) {
      this.formData = this.visible.row;
    }
  },
  watch: {
    'formData.yzjsr': function () {
      if (this.formData.sl1) {
        this.formData.hsyzjsr = (this.formData.yzjsr * (1 + this.formData.sl1)).toFixed(2);
        if (!this.formData.hsyzjsr) {
          this.formData.hsyzjsr = 0;
        }
      }
      if (!this.visible.pageType) {
        this.formData.sjyzjsr = this.formData.yzjsr;
      }
    },
    'formData.sl1': function () {
      if (this.formData.yzjsr) {
        this.formData.hsyzjsr = (this.formData.yzjsr * (1 + this.formData.sl1)).toFixed(2);
        if (!this.formData.hsyzjsr) {
          this.formData.hsyzjsr = 0;
        }
      }
      if (!this.visible.pageType) {
        this.formData.sjsl1 = this.formData.sl1;
      }
    },
    'formData.czmj': function () {
      if (!this.visible.pageType) {
        this.formData.sjczmj = this.formData.czmj;
      }
    },
    'formData.sjsl1': function () {
      if (this.formData.sjyzjsr) {
        this.formData.sjhsyzjsr = (this.formData.sjyzjsr * (1 + this.formData.sjsl1)).toFixed(2);
        if (!this.formData.sjhsyzjsr) {
          this.formData.sjhsyzjsr = 0;
        }
      }
    },
    'formData.sjyzjsr': function () {
      if (this.formData.sjsl1) {
        this.formData.sjhsyzjsr = (this.formData.sjyzjsr * (1 + this.formData.sjsl1)).toFixed(2);
        if (!this.formData.sjhsyzjsr) {
          this.formData.sjhsyzjsr = 0;
        }
      }
    },
  },
  methods: {
    async init() {
      // 基本条件
      this.rules = this.baseRules;
    },
    onClose() {
      this.isVisible = false;
    },
    async fwcqzsValidator() {
      const params = {
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.formData.fwzldz = data.fwzldz;
          this.formData.fybh = data.fybh;
          this.jzmj = data.jzmj ? Number(data.jzmj) : 0;
        } else {
          this.formData.fwzldz = null;
          this.formData.fybh = null;
          // this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
        }
      } catch (e) {
        console.error(e);
      }
      if (!this.formData.fybh) {
        return {
          result: false,
          message: '未查询到不动产权证号对应的房源编号信息。',
          type: 'warning',
        };
      }
      return { result: true, message: '', type: 'success' };
    },
    async czmjValidator() {
      const params = {
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.jzmj = data.jzmj ? Number(data.jzmj) : 0;
        }
      } catch (e) {
        console.error(e);
      }
      if (this.formData.czmj > this.jzmj) {
        return {
          result: false,
          message: `出租面积不能大于该房源的建筑面积（${this.jzmj}㎡）。`,
          type: 'warning',
        };
      }
      return { result: true, message: '', type: 'success' };
    },
    async sjCzmjValidator() {
      const params = {
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.jzmj = data.jzmj ? Number(data.jzmj) : 0;
        }
      } catch (e) {
        console.error(e);
      }
      if (this.formData.sjczmj > this.jzmj) {
        return {
          result: false,
          message: `实际出租面积不能大于该房源的建筑面积（${this.jzmj}㎡）。`,
          type: 'warning',
        };
      }
      return { result: true, message: '', type: 'success' };
    },
    // confirm() {
    //   this.handleInputBlur()
    //     .then(this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator }))
    //     .then(this.save());
    // },
    // async save() {
    async confirm() {
      // this.baseRules.fwcqzsh.push({ validator: this.fwcqzsValidator });
      if (!this.formData.fybh) {
        this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
      } else if (this.formData.czmj > this.jzmj) {
        this.$message.warning(`出租面积不能大于不动产权证号对应的房源的建筑面积（${this.jzmj}㎡）。`);
      } else {
        const val = await this.$refs.forms.validate();
        if (val === true) {
          this.confirmLoading = true;
          const p = {};
          [
            'fybh',
            'uuid',
            'htbh',
            'htmc',
            'fwcqzsh',
            'fwzldz',
            'zgswjgDm',
            'lrzx',
            'htydsxrq',
            'htydzzrq',
            'yzjsr',
            'hsyzjsr',
            'sl1',
            'skfs',
            'zzrq',
            'czmj',
            'zlrqq',
            'zlrqz',
            'sjczmj',
            'sjhsyzjsr',
            'sjsl1',
            'sjyzjsr',
            'ly',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = { ...p, fybh: this.formData.fybh, ...this.visible.otherObj };
          try {
            let res = {};
            if (this.visible.pageType) {
              res = await updateZlht(params);
            } else {
              res = await saveZlht(params);
            }
            const { code } = res;
            if (code === 1) {
              this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
              this.$emit('updatePage');
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(2) : '0.00';
    },
    async initFcsXzqhandJdxz() {
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
      const { data } = await getXzqhJdxzSwjg(params);
      this.zgswjgList = data.DM_GY_SWJG;
      this.zgswjg = data.zgswskfjDm;
      if (this.zgswjgList.length === 1) {
        this.formData.zgswjg = this.zgswjgList[0].value;
      }
    },
    async handleInputBlur() {
      // 在这里编写编辑完成后的逻辑
      // this.baseRules.fwcqzsh = [{ required: true, message: '必填', type: 'error' }];
      const params = {
        fwcqzsh: this.formData.fwcqzsh || 'FWCQZS1234',
      };
      try {
        const { data } = await initbQueryXq(params);
        if (data && data.fybh) {
          this.formData.fwzldz = data.fwzldz;
          this.formData.fybh = data.fybh;
        } else {
          this.formData.fwzldz = null;
          this.formData.fybh = null;
          this.$message.warning('未查询到不动产权证号对应的房源编号信息，请先维护生成对应的房源信息后再录入。');
        }
      } catch (e) {
        console.error(e);
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
