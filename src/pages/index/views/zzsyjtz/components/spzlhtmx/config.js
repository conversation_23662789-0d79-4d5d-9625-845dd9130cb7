import dayjs from 'dayjs';

export const skfsList = [
  { value: '0', label: '月付' },
  { value: '1', label: '季付' },
  { value: '2', label: '半年付' },
  { value: '3', label: '年付' },
];

export const querySearchConfig = [
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源地址',
    key: 'fwzldz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '租赁开始日期',
    key: 'zlrqq',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqz',
    timeRange: 'start',
  },
  {
    label: '租赁结束日期',
    key: 'zlrqz',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'zlrqq',
    timeRange: 'end',
  },
  {
    label: '收款方式',
    key: 'skfs',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];

export const querySearchConfigOneRules = {};

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ly !== '1' }),
    width: 50,
    align: 'center',
    fixed: 'left',
    foot: '合计',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
  },
  {
    colKey: 'htbh',
    title: '合同编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'htmc',
    title: '合同名称',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwzldz',
    title: '房源地址',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zgswjgMc',
    title: '主管税务机关',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'htydsxrq',
    title: '合同开始日期',
    width: 120,
    cell: (h, { row }) => <div>{row.htydsxrq ? dayjs(row.htydsxrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'htydzzrq',
    title: '合同结束日期',
    width: 120,
    cell: (h, { row }) => <div>{row.htydzzrq ? dayjs(row.htydzzrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zzrq',
    title: '提前终止日期',
    width: 120,
    cell: (h, { row }) => <div>{row.zzrq ? dayjs(row.zzrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'skfs',
    title: '收款方式',
    width: 80,
  },
  {
    align: 'center',
    colKey: 'zlrqq',
    title: '租赁开始日期',
    width: 120,
    cell: (h, { row }) => <div>{row.zlrqq ? dayjs(row.zlrqq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zlrqz',
    title: '租赁结束日期',
    width: 120,
    cell: (h, { row }) => <div>{row.zlrqz ? dayjs(row.zlrqz).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 160,
    colKey: 'yzjsr',
    title: '月租金收入（不含税）',
  },
  {
    align: 'right',
    width: 100,
    cell: (h, { row }) => <div>{(row.sl1 && `${row.sl1 * 100}%`) || '-'}</div>,
    colKey: 'sl1',
    title: '预缴税率',
  },
  {
    align: 'right',
    width: 160,
    colKey: 'hsyzjsr',
    title: '月租金收入（含税）',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];
