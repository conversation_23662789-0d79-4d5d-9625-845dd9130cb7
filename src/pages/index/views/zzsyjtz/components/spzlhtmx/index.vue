<!--
* @Descripttion: 台账-增值税预缴-商铺租赁合同明细
* @Version: 1.0
* @Author: ljf
* @Date: 2024-10-30 15:00:00
* @LastEditors: 
* @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    />

    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
        <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
        <t-button variant="outline" theme="primary" @click="extract"
          ><CloudDownloadIcon slot="icon" />提取数据</t-button
        >
        <t-button variant="outline" theme="primary" @click="check"><ChartIcon slot="icon" />查看底稿</t-button>
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #skfs="{ row }">
          {{ { 0: '月付', 1: '季付', 2: '半年付', 3: '年付' }[row.skfs] || '' }}
        </template>
        <template #czmj="{ row }">
          <span>{{ numberToPrice(row.czmj) }}</span>
        </template>
        <template #yzjsr="{ row }">
          <span>{{ numberToPrice(row.yzjsr) }}</span>
        </template>
        <template #hsyzjsr="{ row }">
          <span>{{ numberToPrice(row.hsyzjsr) }}</span>
        </template>
        <template #operation="{ row }">
          <t-link theme="primary" hover="color" @click="newOrEditRow(row)" v-show="row.ly !== '0'"> 编辑 </t-link>
        </template>
      </t-table>
    </div>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updatePage="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import { queryZlht, deleteZlht, initbQueryXq } from '@/pages/index/api/tzzx/zzsyjtz/spzlhtmx.js';
import { downloadBlobFile } from '@/core/download';
import { numberToPrice } from '@/utils/numberToCurrency';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, CloudDownloadIcon, ChartIcon, DeleteIcon, DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { skfsList, querySearchConfig, querySearchConfigOneRules, dataColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    QsbButton,
    SearchControlPanel,
    EditDialog,
    AddIcon,
    DeleteIcon,
    DownloadIcon,
    CloudDownloadIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      editDialogVisible: false,
      boxvisible: false,
      skfsList,
      querySearchConfig,
      querySearchConfigOneRules,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      delformData: [],
      dataColumns,
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    // 初始化收款方式
    this.querySearchConfig[5].selectList = this.skfsList;
  },
  mounted() {},
  methods: {
    async extract() {
      this.query({ flag: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        pageNo: 1,
        pageSize: 10,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        gsmc: this.$store.state.zzstz.userInfo?.jgmc,
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    // 查询详情
    async queryXq(rowuuid) {
      this.tableLoading = true;
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        uuid: rowuuid,
      };
      try {
        const { data } = await initbQueryXq(params);
        this.fcyz = data.fcyz;
        this.jsbl = data.jsbl;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async delRow() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      let params = {};
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      if (p) {
        this.$refs.queryControl.setParams(p);
        params = {
          cxbz: '2', // 房产税租赁合同查询对应值为‘1’，增值税预缴租赁合同查询对应值为‘2’
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          pzrq: p.czsjq,
        };
        params = { ...p, ...params };
      } else {
        params = {
          cxbz: '2', // 房产税租赁合同查询对应值为‘1’，增值税预缴租赁合同查询对应值为‘2’
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
        };
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await queryZlht(params);
        this.tableData = data.list || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.total;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    // 删除-调用后台接口
    async delete(params) {
      try {
        const { code, msg, data } = await deleteZlht(params);
        if (code === 1) {
          this.$message.success(msg);
        } else if (msg) {
          this.$message.warning(msg);
        } else {
          this.$message.warning(data);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610791',
        tzlx: 'fcszyf',
        fileName: '租赁合同明细',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(val) {
      return numberToPrice(val);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
