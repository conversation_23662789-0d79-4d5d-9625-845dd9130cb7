<!--
* @Descripttion: 台账-增值税预缴-出租不动产预缴明细
* @Version: 1.0
* @Author: ljf
* @Date: 2024-11-01 09:00:00
* @LastEditors: 
* @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" type="submit" @click="query({ flag: true })"
          ><CloudDownloadIcon slot="icon" />提取数据</t-button
        >
        <!-- <t-button variant="outline" theme="primary" @click="check"><ChartIcon slot="icon" />查看底稿</t-button> -->
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
      >
        <!-- :foot-data="footData" -->
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #skfs="{ row }">
          {{ { 0: '月付', 1: '季付', 2: '半年付', 3: '年付' }[row.skfs] || '' }}
        </template>
        <template #hsyzjsr="{ row }">
          <span>{{ numberToPrice(row.hsyzjsr) }}</span>
        </template>
        <template #sbje="{ row }">
          <span>{{ numberToPrice(row.sbje) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberToCurrency';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { downloadBlobFile } from '@/core/download';
import { getXzqhTreeData, queryCzbdcyjmxList } from '@/pages/index/api/tzzx/zzsyjtz/zzsyjmx.js';
import { CloudDownloadIcon, ChartIcon, DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { skfsList, querySearchConfig, dataColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
    CloudDownloadIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.skfsList = skfsList;
    this.dataColumns = dataColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    return {
      userInfo: {},
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      tableData: [],
      // footData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
  },
  created() {},
  mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
    this.query({ flag: true });
  },
  methods: {
    getQueryParamsList() {
      this.querySearchConfig[5].selectList = this.skfsList;
    },
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        console.log('url-params', params);
        if (params.get('skssqq') && params.get('skssqz')) {
          this.querySearchConfig[0].value = dayjs(params.get('skssqq')).format('YYYY-MM');
          this.formData.sszq = dayjs(params.get('skssqq')).format('YYYYMM');
          return;
        }
      }
      this.querySearchConfig[0].value = dayjs().subtract(1, 'month').format('YYYY-MM');
      this.formData.sszq = dayjs().subtract(1, 'month').format('YYYY-MM');
      getXzqhTreeData({ djxh: this.$store.state.zzstz.userInfo?.djxh }).then((res) => {
        this.querySearchConfig[7].selectList = res.data || [];
      });
    },
    resetQueryParams() {
      this.$refs.queryControl.onReset();
    },
    numberToPrice(val) {
      return numberToPrice(val);
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm; // 默认无需回到第一页，无父传参
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      // 不变的基础入参
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh,
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        if (p.ssyf) {
          p.sszq = String(p.ssyf.replace('-', '')); // 修改父传参YYYYMM为YYYY-MM给上面的时间框
        }
        this.$refs.queryControl.setParams(p); // 覆盖上方的查询条件为父传参
        params = { ...p, ...params }; // 基础入参+父传参
      } else {
        params = { ...this.formData, sszq: this.sszqC, ...params }; // 没有父传参的话,传查询条件+基础入参.查询框sszq绑定为YYYY-MM，需要使用sszqC覆盖
      }
      try {
        const { data } = await queryCzbdcyjmxList(params); // 不建议api.  建议使用接口真实尾缀，方便定位代码。
        this.tableData = data.list || [];
        // if (this.tableData.length > 1) this.footData = [data.list?.hj] || []; // 表格长度大于1时展示表尾总计行。
        this.pagination.total = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610865',
        tzlx: 'czbdcyjmx',
        fileName: '出租不动产预缴明细',
        cxParam: {
          ...this.formData,
          sszq: this.sszqC,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
</style>
