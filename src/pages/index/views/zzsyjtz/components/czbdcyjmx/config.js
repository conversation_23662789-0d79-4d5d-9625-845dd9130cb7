import dayjs from 'dayjs';

export const skfsList = [
  { value: '0', label: '月付' },
  { value: '1', label: '季付' },
  { value: '2', label: '半年付' },
  { value: '3', label: '年付' },
];

export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'fwcqzsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同名称',
    key: 'htmc',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房源地址',
    key: 'fwzldz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '收款方式',
    key: 'skfs',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '行政区划',
    key: 'xzqhList',
    type: 'tree-select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    multiple: true,
    filterable: true,
    minCollapsedNum: 1,
    value: [],
  },
];

export const querySearchConfigOneRules = {};

export const dataColumns = [
  //   {
  //     colKey: 'row-select',
  //     type: 'multiple',
  //     className: 'demo-multiple-select-cell',
  //     width: 50,
  //     align: 'center',
  //     fixed: 'left',
  //     foot: '合计',
  //   },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'htbh',
    title: '合同编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'htmc',
    title: '合同名称',
    width: 280,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwzldz',
    title: '房源地址',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fwcqzsh',
    title: '不动产权证号',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xzqhMc',
    title: '行政区划',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zgswjgmc',
    title: '主管税务机关',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'htydsxrq',
    title: '合同开始日期',
    width: 120,
    cell: (h, { row }) => <div>{row.htydsxrq ? dayjs(row.htydsxrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'htydzzrq',
    title: '合同结束日期',
    width: 120,
    cell: (h, { row }) => <div>{row.htydzzrq ? dayjs(row.htydzzrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zzrq',
    title: '提前终止日期',
    width: 120,
    cell: (h, { row }) => <div>{row.zzrq ? dayjs(row.zzrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zlrqq',
    title: '申报租赁期起',
    width: 120,
    cell: (h, { row }) => <div>{row.zlrqq ? dayjs(row.zlrqq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'zlrqz',
    title: '申报租赁期止',
    width: 120,
    cell: (h, { row }) => <div>{row.zlrqz ? dayjs(row.zlrqz).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'skfs',
    title: '收款方式',
    width: 80,
  },
  {
    align: 'right',
    width: 160,
    colKey: 'hsyzjsr',
    title: '月租金收入（含税）',
  },
  {
    align: 'right',
    width: 160,
    colKey: 'sbje',
    title: '申报租金',
  },
];
