<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="minbox" style="display: flex">
      <TzMenu />
      <div class="tzzt">
        <gt-collapse-menu
          class="ggMenu"
          theme="primary"
          title="台账列表"
          :list="list"
          expandMutex
          :defaultExpanded="expanded"
          :default-value="defaultValue"
          :toolbar="false"
          :value="active"
          @change="collapseHandle"
        >
          <template #panel>
            <spzlhtmx ref="spzlhtmx" v-show="active === 'spzlhtmx'" @openPage="openPage" />
            <czbdcyjmx ref="czbdcyjmx" v-show="active === 'czbdcyjmx'" @openPage="openPage" />
            <zzsyjtz ref="zzsyjtz" v-show="active === 'zzsyjtz'" @openPage="openPage" />
            <zzsyjqkcx ref="zzsyjqkcx" v-show="active === 'zzsyjqkcx'" @openPage="openPage" />
          </template>
          <!-- <template #menu-item>menu-item</template> -->
        </gt-collapse-menu>
      </div>
    </div>
  </div>
</template>

<script>
import TzMenu from '@/pages/index/components/NewTzMenu';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import czbdcyjmx from '@/pages/index/views/zzsyjtz/components/czbdcyjmx/index.vue';
// import spzlhtmx from '@/pages/index/views/zzsyjtz/components/spzlhtmx/index.vue';
import spzlhtmx from '@/pages/index/views/zzsyjtz/components/spzlhtmxNew/index.vue';
import zzsyjqkcx from '@/pages/index/views/zzsyjtz/components/zzsyjqkcx/index.vue';
import zzsyjtz from '@/pages/index/views/zzsyjtz/components/zzsyjtz/index.vue';
import { CollapseMenu } from '@gt4/common-front';
import {
  isProductEnv,
  isYsEnv,
  initComputeSszq,
  stopPollingFcs,
  // getCompanyDifferentiationConfig,
} from '@/pages/index/views/util/tzzxTools.js';

export default {
  components: { GtCollapseMenu: CollapseMenu, TzMenu, czbdcyjmx, spzlhtmx, zzsyjqkcx, zzsyjtz, Mybreadcrumb },
  data() {
    return {
      expanded: ['1'],
      defaultValue: 'zzsyjtz',
      active: 'zzsyjtz',
      sbrwmxbz: this.$route.query.sbrwmxBz || false,
      sbrwmxAbbbz: this.$route.query.sbrwmxAbbBz || false,
      list: [
        {
          id: '1',
          title: '增值税预缴台账',
          children: [
            {
              id: 'zzsyjtz',
              title: '增值税预缴台账',
              required: false,
            },
            {
              id: 'czbdcyjmx',
              title: '出租不动产预缴明细',
              required: false,
            },
            {
              id: 'spzlhtmx',
              title: '商铺租赁合同明细',
              required: false,
            },
            {
              id: 'zzsyjqkcx',
              title: '增值税预缴情况查询',
              required: false,
            },
          ],
        },
      ],
    };
  },
  created() {
    // if (window.location.search) {
    //   // 暂时默认从申报明细跳转
    //   this.sbrwmxbz = true;
    // }
    initComputeSszq();
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  methods: {
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true };
      this.$refs[val].query(params);
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      stopPollingFcs();
      // // 使用 await 等待 getCompanyDifferentiationConfig 完成
      // await getCompanyDifferentiationConfig({
      //   djxh: this.$store.state.zzstz.userInfo.djxh,
      //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      // });
      // // 确保状态更新完成
      // await this.$nextTick();
      this.$refs[this.active].resetQueryParams();
      this.$refs[this.active].initQueryConditions();
      if (this.active === 'fcszlht') {
        this.$refs[this.active].initTabValue();
      }
      const params = { flag: true };
      this.$refs[this.active].query(params);
    },
    // data不传的话默认用子页面的查询参数。notQuery传true时子页面不触发查询。from为父页面activ，用于子页面返回对应父页面，按需将from储存在子页面。
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) this.$refs[type].query({ flag, p: data, from });
      this.active = type;
    },
  },
  mounted() {
    isProductEnv();
    isYsEnv();
    // getCompanyDifferentiationConfig({
    //   djxh: this.$store.state.zzstz.userInfo.djxh,
    //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
    // });
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '增值税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务进
    const parmObjSbrw = {
      mybreadList: ['首页', '申报概览', '税费申报', '增值税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmx'],
      goBackPath: '/znsb/view/nssb/sbrwmx', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务(报表)进
    const parmObjSbrwAbb = {
      mybreadList: ['首页', '申报概览', '税费申报', '增值税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmxAbb'],
      goBackPath: '/znsb/view/nssb/sbrwmxAbb', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    if (this.sbrwmxbz) {
      this.$refs.myBre.initMyBre(parmObjSbrw);
    } else if (this.sbrwmxAbbbz) {
      this.$refs.myBre.initMyBre(parmObjSbrwAbb);
    } else {
      this.$refs.myBre.initMyBre(parmObjDhcd);
    }
    // this.$refs.myBre.initMyBre(parmObjDhcd);
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
  /deep/.gt-collapse-menu-content-unfold {
    margin-left: 0 !important;
  }
  /deep/.gt-collapse-menu-sidebar-unfold {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content .t-default-menu {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar {
    display: block;
  }
}
.tzzt {
  width: calc(100% - 50px);
}
.ggMenu {
  border-left: 1px solid #eee;
  /deep/.gt-collapse-menu-sidebar-header__title {
    font-size: 14px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header {
    line-height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header-unfold {
    height: 55px !important;
  }
  /deep/.t-menu__item {
    height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content {
    height: calc(100% - 55px) !important;
  }
}
</style>
