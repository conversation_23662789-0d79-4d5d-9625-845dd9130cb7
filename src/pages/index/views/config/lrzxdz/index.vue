<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="ggMenu">
      <div :style="{ height: '100%' }">
        <div class="znsbBackGroupDiv adaption-wrap">
          <!-- 合同明细页签 -->
          <search-control-panel
            class="znsbHeadqueryDiv"
            ref="queryControl"
            :config="querySearchConfig"
            :formRules="querySearchConfigOneRules"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
          />
          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
              <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
              <t-button variant="outline" theme="primary" @click="downloadTemplate"
                ><FileIcon slot="icon" />下载模版</t-button
              >
              <t-upload
                action="/nssb/lrzxdzController/uploadExcel"
                :tips="tips"
                v-model="files"
                theme="custom"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                @fail="handleFail"
                @success="handleSuccess"
                :beforeUpload="handleBeforeUpload"
              >
                <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入数据</t-button>
              </t-upload>
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
              <a ref="downloadTemplate" style="display: none" href="./利润中心对照导入模板.xlsx"></a>
              <QsbButton />
            </gt-space>
            <t-button
              variant="outline"
              theme="primary"
              v-if="fromName"
              @click="$emit('openPage', { type: fromName, notQuery: true })"
              ><RollbackIcon slot="icon" />返回</t-button
            >
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="uuid"
              height="100%"
              hover
              :data="tableData"
              :columns="htColumns"
              :selected-row-keys="selectedRowKeys"
              @select-change="rehandleSelectChange"
              :pagination="pagination"
              @page-change="pageChange($event)"
              :loading="tableLoading"
              :foot-data="footData"
            >
              <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
              <template #sfzzshzsb="{ row }">
                {{ { Y: '是', N: '否' }[row.sfzzshzsb] || '' }}
              </template>
              <template #operation="{ row }">
                <t-link theme="primary" hover="color" @click="newOrEditRow(row)">
                  编辑
                </t-link>
              </template>
            </t-table>
          </div>
          <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateLrzxdz="query()" />
          <div v-show="boxvisible">
            <t-dialog
              theme="warning"
              style="display: block;max-height: 90vh; overflow-y: auto; border-radius: 10px"
              :width="400"
              header="警示"
              body="请确认是否删除所选明细"
              :onConfirm="confirmDelRow"
              :onClose="closeBox"
            >
            </t-dialog>
          </div>
          <div v-show="deleteRowvisible">
            <t-dialog
              theme="warning"
              style="display: block;max-height: 90vh; overflow-y: auto; border-radius: 10px"
              :width="400"
              header="警示"
              body="请确认是否删除所选明细"
              :onConfirm="confirmDeleleRow"
              :onClose="closeDelRowBox"
            >
            </t-dialog>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import QsbButton from '@/pages/index/components/QsbButton';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  queryByCondition,
  deleteLrzxdz,
} from '@/pages/index/api/lrzx/lrzxdz.js';
import { downloadBlobFile } from '@/core/download';
import { AddIcon, DeleteIcon, FileIcon, UploadIcon, DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';  
import EditDialog from './components/edit-dialog.vue';
import { querySearchConfig, querySearchConfigOneRules, htColumns } from './config.js';
import { companyDifferentiationConfigApi } from '@/pages/index/api/tzzx/gyApi/gyapi.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    EditDialog,
    SearchControlPanel,
    AddIcon,
    FileIcon,
    UploadIcon,
    DeleteIcon,
    DownloadIcon,
    RollbackIcon,
    Mybreadcrumb,
  },
  data() {
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.htColumns = htColumns;
    return {
      formData: {}, 
      dcLoading: false,
      querySearchConfig,
      boxvisible: false,
      deleteRowvisible: false,
      tableLoading: false,
      editDialogVisible: false,
      deleteAllvisible: false,
      fromName: false,
      queryParams: {},
      delformData: [],
      checkBox: [],
      selectedRowKeys: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      tableData: [],
      footData: [],
      tips: '',
      files: [],
      deleteRow: null,
      gsh: '',
      quanshanFlag: false,
    };
  },
  async created() {
    // if (process.env.VUE_APP_MODEL === 'local') {
    //   this.$store.commit('zzstz/setUserInfoData', {
    //     jguuid: 'sichuanlqcs000000000000*********',
    //     jgmc: '1234321|南充南百大珠宝有限公司',
    //     xzqhmc: '四川省',
    //     nsrsbh: '91310112773731630K',
    //     djxh: '10013101001120017369',
    //     xzqhszDm: '510000',
    //     qydmz: '1100',
    //     nsrlx: '1',
    //   });
    // }
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    // console.log(1111111111111111111111111);
    // try {
    //   const res = await companyDifferentiationConfigApi({
    //     nsrsbh: JSON.parse(userInfo)?.nsrsbh,
    //     djxh: JSON.parse(userInfo)?.djxh,
    //   });
    //   this.gsh = res.data?.jtbm || '';
    // } catch (e) {
    //   console.error('企业类型查询失败:', e);
    // }
    // 新增企业类型判断
    try {
      const res = await companyDifferentiationConfigApi({
        nsrsbh: JSON.parse(userInfo)?.nsrsbh,
        djxh: JSON.parse(userInfo)?.djxh,
      });
      this.jtbm = res.data?.jtbm || '';
      this.quanshanFlag = this.jtbm === '000004';
    } catch (e) {
      console.error('企业类型查询失败:', e);
    }
    this.query();
  },
  mounted() {
    const paramObj = {
      mybreadList: ['首页', '利润中心对照'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
      isxlxShow: false,
    };
    this.$refs.myBre.initMyBre(paramObj);
  },
  watch: {
    formData: {
      handler(newVal) {
        console.log('formData changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
  },
  computed: {
  },
  methods: {
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      // stopPollingYhs();
      // // 使用 await 等待 getCompanyDifferentiationConfig 完成
      // await getCompanyDifferentiationConfig({
      //   djxh: this.$store.state.zzstz.userInfo.djxh,
      //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      // });
      // // 确保状态更新完成
      // await this.$nextTick();
      // const params = { flag: true };
      // this.$refs[this.active].query(params);
      // if (this.active === 'pzmx') {
      //   this.$refs[this.active].getLrzx();
      // }
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$loading(false);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$message.error(`导入失败`);
      }
      this.$loading(false);
      this.clearFiles();
      this.query({ flag: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    closeDelRowBox() {
      this.deleteRowvisible = false;
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      
      const djParam = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'lrzx',
        tzlx: 'lrzxdz',
        fileName: '利润中心对照关系',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const rowdata = JSON.stringify(row);
      const gsh = this.gsh;
      const quanshanFlag = this.quanshanFlag;
      this.editDialogVisible = { row: JSON.parse(rowdata), pageType, gsh, quanshanFlag };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    deleteClickRow(row){
       this.deleteRow = row;
       this.deleteRowvisible = true;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    confirmDeleleRow() {
      // 关闭弹窗
      this.closeDelRowBox();
      // 执行删行
      if (!this.deleteRow) {
        this.$message.warning('没有可删除的数据');
        return;
      }
      this.delformData.push(this.deleteRow.uuid);
      const params = this.delformData;
      this.delete(params); 
      // 重置对应行
      this.deleteRow = null;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      // 下钻进入时，需要返回按钮能够返回至上次操作页面
      this.fromName = from ?? this.fromName;
      // flag标志为ture时，需要重置查询，分页重置为首页
      if (flag) {
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        // 下钻进入时，默认进入明细页
        params = {
          ...p,
          ...params,
        };
      } else {
        params = {
          ...params,
          ...this.formData,
        };
      }
      try {
        this.queryParams = params;
        const { data } = await queryByCondition(params);
        this.pagination.total = data?.total || 0;
        this.tableData = data.list;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        // 查询成功后更新对应页签的上次查询记录
        const currentParams = {
          ...params,
        };
        this.lastQueryParamsTab = currentParams;
      } catch (e) {
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params) {
      try {
        const { msg } = await deleteLrzxdz(params);
        console.log('deleteSelected-msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    handleBeforeUpload() {
      this.$loading(true);
      return true;
    },
    // 分页处理
    pageChange({ current, pageSize }) {
      const pagination = this.pagination;
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;
      this.query();
    },
    // 下载模板
    async downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../styles/dialog.less';
@import '../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
