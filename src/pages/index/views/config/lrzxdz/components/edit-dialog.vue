<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增利润中心对照', '编辑利润中心对照'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="企业税号" name="nsrsbh">
              <t-input :maxlength="30" :onBlur="getCompany" v-model="formData.nsrsbh" placeholder="请填写企业税号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="企业名称" name="nsrmc">
              <t-input :maxlength="30" v-model="formData.nsrmc" placeholder="请填写企业名称" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="公司代码" name="gsh">
              <t-input :maxlength="30" v-model="formData.gsh" placeholder="请填写公司代码" @blur="getZjg" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="利润中心代码" name="lrzxDm">
              <t-input :maxlength="30" v-model="formData.lrzxDm" placeholder="请填写利润中心代码" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="利润中心名称" name="lrzxMc">
              <t-input :maxlength="30" v-model="formData.lrzxMc" placeholder="请填写利润中心名称" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="是否增值税汇总申报" name="sfzzshzsb">
              <t-select v-model="formData.sfzzshzsb" placeholder="请选择">
                <t-option
                  v-for="item in sfList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="增值税汇总申报总机构税号" name="zzshzsbzjgsh">
              <t-input :maxlength="30" v-model="formData.zzshzsbzjgsh" :onBlur="getZzs" placeholder="请填写增值税汇总申报总机构税号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="上级企业税号" name="sjqynsrsbh">
              <t-input :maxlength="30" v-model="formData.sjqynsrsbh" :onBlur="getSjqy" placeholder="请填写上级企业税号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="上级企业名称" name="sjqynsrmc">
              <t-input :maxlength="30" v-model="formData.sjqynsrmc" placeholder="请填写上级企业名称" clearable></t-input>
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getCompanyInfo } from '@/pages/index/api/lrzx/lrzxdz.js';
import { insertLrzxdz, updateLrzxdz, getQsZjgInfoByGsh } from '@/pages/index/api/lrzx/lrzxdz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { AddIcon, DeleteIcon } from 'tdesign-icons-vue';

export default {
  components: { CssDialog, GtInputMoney, AddIcon, DeleteIcon },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      nsrsbh: [{ required: true, message: '必填', type: 'error' }],
      nsrmc: [{ required: true, message: '必填', type: 'error' }],
      lrzxDm: [{ required: true, message: '必填', type: 'error' }],
      lrzxMc: [{ required: true, message: '必填', type: 'error' }],
      sfzzshzsb: [{ required: true, message: '必填', type: 'error' }],
      zzshzsbzjgsh: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      sjjsList: [],
      dfslrList: [],
      selectedRowKeys: [],
      checkBox: [],
      value: 1,
      sfList: [],
      lrzxList: [],
      formData: {
        nsrsbh: '',
        nsrmc: '',
        gsh: '',
        lrzxDm: '',
        lrzxMc: '',
        sfzzshzsb: '',
        zzshzsbzjgsh: '',
        sjqynsrsbh: '',
        sjqynsrmc: '',
      },
    };
  },
  created() {
    this.init();
  },
  computed: {
  },
  watch: {
    'formData.sfzzshzsb': {
      handler(newVal) {
        console.log(newVal)
        if (newVal === 'N' && this.formData.nsrsbh) {
          this.formData.zzshzsbzjgsh = this.formData.nsrsbh;
        }
      },
      immediate: true,
      deep: false,
    },
    'formData.nsrsbh': {
      handler(newVal) {
        if (this.formData.sfzzshzsb === 'N' && newVal) {
          this.formData.zzshzsbzjgsh = newVal;
        }
      },
      deep: false,
    },
  },
  methods: {
    // 用于提交前校验数据（示例代码有效，勿删）
    validateTableData() {
      // 仅校验处于编辑态的单元格
      this.$refs.tableRef.validateTableData().then((result) => {
        console.log('validate result: ', result);
      });
    },
    async init() {
      // 是否List
      this.sfList = [
        { value: 'Y', label: '是' },
        { value: 'N', label: '否' },
      ];
      this.rules = this.baseRules;
      console.log(this.visible);
      if (this.visible.row?.uuid) {
        this.formData = this.visible.row;
      }
    },
    getCompany(){
      if(this.formData.nsrsbh) {
        try {
          getCompanyInfo(this.formData.nsrsbh).then(res => {
            if(!res.data){
              this.$message.warning("未查询到企业信息")
              return;
            }
            console.log("getCompany" + JSON.stringify(res));
            this.formData.nsrmc = res.data.nsrmc;
            this.formData.gsh = res.data.gsh;
          });
        } catch (e) {
          console.error('查询企业信息失败:', e);
        }
      }
    },
    getZzs() {
      if(this.formData.zzshzsbzjgsh) {
        try {
          getCompanyInfo(this.formData.zzshzsbzjgsh).then(res => {
            console.log("getZzs" + JSON.stringify(res));
            if(!res.data){
              this.$message.warning("增值税汇总申报总机构税号未在申报系统注册，请确认税号填写是否正确");
              return;
            }
          });
        } catch (e) {
          console.error('查询总机构信息失败:', e);
        }
      }
    },
    getSjqy() {
      if(this.formData.sjqynsrsbh) {
        try {
          getCompanyInfo(this.formData.sjqynsrsbh).then(res => {
            console.log("getSjqy" + JSON.stringify(res));
            if(!res.data){
              this.$message.warning("未查询到上级企业信息");
              return;
            }
            this.formData.sjqynsrmc = res.data.nsrmc;
          });
        } catch (e) {
          console.error('查询总机构信息失败:', e);
        }
      }
    },
    onClose() {
      this.isVisible = false;
    },
    getZjg() {
      console.log(this.visible)
      console.log(this.visible.quanshanFlag)
      if(this.visible.quanshanFlag) {
        try {
          getQsZjgInfoByGsh(this.formData.gsh).then(res => {
            console.log("getZjg" + JSON.stringify(res));
            if(!res.data){
              this.$message.warning("未查询到总机构信息");
              return;
            }
            this.formData.sjqynsrsbh = res.data.sjqynsrsbh;
            this.formData.sjqynsrmc = res.data.sjqynsrmc;
          });
        } catch (e) {
          console.error('查询总机构信息失败:', e)}
        }
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'uuid',
          'nsrsbh',
          'nsrmc',
          'gsh',
          'lrzxDm',
          'lrzxMc',
          'sfzzshzsb',
          'zzshzsbzjgsh',
          'sjqynsrsbh',
          'sjqynsrmc',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        try {
          console.log(this.visible.pageType)
          if (this.visible.pageType) {
            await updateLrzxdz(p);
          } else {
            await insertLrzxdz(p);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateLrzxdz');
          } else {
            this.$emit('updateLrzxdz', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
