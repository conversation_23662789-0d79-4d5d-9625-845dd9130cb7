import dayjs from 'dayjs';

export const querySearchConfig = [
  {
    label: '企业税号',
    key: 'nsrsbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '企业名称',
    key: 'nsrmc',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '公司代码',
    key: 'gsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '是否增值税汇总申报',
    key: 'sfzzshzsb',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '增值税汇总申报总机构税号',
    key: 'zzshzsbzjgsh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];
export const htColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 40,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 180,
    colKey: 'nsrsbh',
    align: 'center',
    title: '企业税号',
  },
  {
    width: 200,
    colKey: 'nsrmc',
    align: 'center',
    title: '企业名称',
  },
  {
    width: 120,
    colKey: 'gsh',
    align: 'center',
    title: '公司代码',
  },
  {
    width: 100,
    colKey: 'lrzxDm',
    align: 'center',
    title: '利润中心代码',
  },
  {
    width: 200,
    colKey: 'lrzxMc',
    align: 'center',
    title: '利润中心名称',
  },
  {
    width: 100,
    colKey: 'sfzzshzsb',
    align: 'center',
    title: '是否增值税汇总申报',
  },
  {
    width: 150,
    colKey: 'zzshzsbzjgsh',
    align: 'center',
    title: '增值税汇总申报总机构税号',
  },
  {
    width: 150,
    colKey: 'sjqynsrsbh',
    align: 'center',
    title: '上级企业税号',
  },
  {
    width: 200,
    colKey: 'sjqynsrmc',
    align: 'center',
    title: '上级企业名称',
  },
  {
    width: 150,
    colKey: 'xgrsfid',
    align: 'center',
    title: '调整人',
  },
  {
    width: 120,
    colKey: 'sjtbSj',
    align: 'center',
    title: '调整日期',
    cell: (h, { row }) => <div>{row.sjtbSj ? dayjs(row.sjtbSj).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    fixed: 'right',
  },
];
