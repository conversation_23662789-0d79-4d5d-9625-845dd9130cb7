<!--
 * @Descripttion: 用户设置类功能-客户编码与行业类别对照
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-08-26 11:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="mainbody">
      <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
      <div class="ggMenu">
        <div class="znsbBackGroupDiv adaption-wrap">
          <search-control-panel
            ref="queryControl"
            class="znsbHeadqueryDiv"
            :config="querySearchConfig"
            :formRules="querySearchConfigOneRules"
            @search="query({ flag: true })"
            :colNum="3"
            @formChange="(v) => (formData = v)"
            :LABEL_THRESHOLD="9"
            :labelWidth="'calc(8em + 32px)'"
          >
          </search-control-panel>

          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-upload
                action="/nssb/khbhhylbdz/v1/input"
                :tips="tips"
                v-model="files"
                theme="custom"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                @fail="handleFail"
                @success="handleSuccess"
              >
                <t-button theme="primary"><UploadIcon slot="icon" />导入</t-button>
              </t-upload>
              <t-button variant="outline" theme="primary" @click="patchSave(true)"
                ><EditIcon slot="icon" />设置为批发行业</t-button
              >
              <t-button variant="outline" theme="primary" @click="patchSave(false)"
                ><EditIcon slot="icon" />设置为零售行业</t-button
              >
              <t-button variant="outline" theme="primary" @click="downloadTemplate"
                ><FileIcon slot="icon" />下载模板</t-button
              >
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
              <a ref="downloadTemplate" style="display: none" href="./khbmyhylbdz.xlsx"></a>
            </gt-space>
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="uuid"
              :data="tableData"
              :columns="mainColumns"
              height="100%"
              hover
              lazyLoad
              :selected-row-keys="selectedRowKeys"
              @select-change="rehandleSelectChange"
              :pagination="pagination"
              @page-change="pageChange"
              :loading="tableLoading"
            >
              <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
              <template #hylb="{ row }">
                {{
                  (hylbList.find((d) => d.value === row.hylb) && hylbList.find((d) => d.value === row.hylb).label) || ''
                }}
              </template>
            </t-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import { queryDzb, gxDzbHylb, getKhbmList } from '@/pages/index/api/tzzx/szlgn/khbmyhylbdz.js';
import { EditIcon, UploadIcon, FileIcon, DownloadIcon } from 'tdesign-icons-vue';
import { downloadBlobFile } from '@/core/download';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { querySearchConfig, mainColumns, khbmList, hylbztList, hylbList } from './config.js';

export default {
  components: {
    SkeletonFrame,
    Mybreadcrumb,
    SearchControlPanel,
    EditIcon,
    UploadIcon,
    DownloadIcon,
    FileIcon,
  },
  data() {
    return {
      loading: true,
      userInfo: {},
      tableLoading: false,
      dcLoading: false,
      formData: {},
      querySearchConfig,
      mainColumns,
      khbmList,
      hylbList, // 开始时需求未给出码表和码值对照，暂时前端编写假数据处理
      hylbztList,
      querySearchConfigOneRules: {},
      editableRowKeys: ['1'],
      selectedRowKeys: [],
      checkBox: [],
      tableData: [],
      tips: '',
      files: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    // this.getKhbmList();
    this.querySearchConfig[1].selectList = this.hylbztList;
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '客户编码与行业类别对照'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/khbmyhylbdzsz'],
      goBackPath: '/znsb/view/khbmyhylbdzsz', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    this.$refs.myBre.initMyBre(parmObjDhcd);
    this.query();
    this.changeLoading();
  },
  methods: {
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$message.error(`导入失败`);
      }
      this.clearFiles();
      this.query();
    },
    downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
    async getKhbmList() {
      const params = {
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
      };
      try {
        const { data } = await getKhbmList(params);
        this.khbmList = data || [];
      } catch (e) {
        this.khbmList = [];
        console.log(e);
      }
      this.querySearchConfig[0].selectList = this.khbmList;
      console.log('this.querySearchConfig[0].selectList', this.querySearchConfig[0].selectList);
    },
    async patchSave(type) {
      if (this.selectedRowKeys.length) {
        const params = [];
        console.log('this.selectedRowKeys', this.selectedRowKeys);
        if (type) {
          this.selectedRowKeys.forEach((i) => {
            const item = { ...this.tableData.find((t) => t.uuid === i), hylb: '01' };
            params.push(item);
          });
        } else {
          this.selectedRowKeys.forEach((i) => {
            const item = { ...this.tableData.find((t) => t.uuid === i), hylb: '02' };
            params.push(item);
          });
        }
        const { data } = await gxDzbHylb(params);
        this.$message.success(data);
        this.selectedRowKeys = [];
        this.query();
      } else {
        this.$message.warning('请选中要执行批量设置的数据。');
      }
    },
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async query() {
      this.tableLoading = true;
      let params = {
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (this.formData.khbh) {
        params = {
          khbh: [this.formData.khbh],
          ...params,
        };
      }
      if (this.formData.hylb) {
        params = {
          hylb: this.formData.hylb,
          ...params,
        };
      }
      try {
        const { data } = await queryDzb(params);
        this.tableData = data?.list || [];
        console.log(this.tableData, 'this.tableData');
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data?.total || 0;
        console.log('this.tableData', this.tableData);
      } catch (e) {
        console.log(e);
      } finally {
        setTimeout(() => {
          this.tableLoading = false;
          if (this.loading) {
            this.loading = false;
          }
        }, 200);
      }
    },
    // 导出
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'gy',
        tzlx: 'khbhhylbdz',
        fileName: '客户编码与行业类别对照',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true };
      this.getKhbmList();
      this.query(params);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../styles/dialog.less';
@import '../../../styles/sbPageGy.less';
/deep/.filter-btns {
  float: right;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
