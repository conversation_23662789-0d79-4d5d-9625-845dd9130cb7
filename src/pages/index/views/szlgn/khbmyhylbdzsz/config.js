import dayjs from 'dayjs';

const khbmList = [
  { value: 'test001', label: '测试客户编码001' },
  { value: 'test002', label: '测试客户编码002' },
  { value: 'test003', label: '测试客户编码003' },
];
const hylbztList = [
  { value: '01', label: '批发行业' },
  { value: '02', label: '零售行业' },
  { value: '00', label: '暂未匹配行业类别' },
];
const hylbList = [
  { value: '01', label: '批发行业' },
  { value: '02', label: '零售行业' },
];
export { khbmList, hylbztList, hylbList };
export const querySearchConfig = [
  {
    label: '客户编码',
    key: 'khbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '行业类别状态',
    key: 'hylb',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  // {
  //   label: '',
  //   key: 't2',
  // },
];
export const mainColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: '7%',
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'xh',
    align: 'center',
    title: '序号',
    width: '8%',
  },
  {
    width: '15%',
    colKey: 'khbh',
    title: '客户编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: '20%',
    colKey: 'khmc',
    title: '客户名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: '20%',
    colKey: 'hylb',
    title: '行业类别',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: '15%',
    colKey: 'whrmc',
    title: '维护人',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xgrq',
    title: '维护日期',
    width: '15%',
    cell: (h, { row }) => <div>{row.xgrq ? dayjs(row.xgrq).format('YYYY-MM-DD') : ''}</div>,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
