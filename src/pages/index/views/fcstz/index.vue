<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <gt-collapse-menu
      class="ggMenu"
      theme="primary"
      title="台账列表"
      :list="list"
      :default-value="defaultValue"
      :toolbar="false"
      :value="active"
      @change="collapseHandle"
    >
      <template #panel>
        <Fcjcxxtz ref="fcjcxxtz" v-show="active === 'fcjcxxtz'" @openPage="openPage" />
        <Zyfcsymxz ref="zyfcsymxz" v-show="active === 'zyfcsymxz'" @openPage="openPage" />
        <Czfcsymxz ref="czfcsymxz" v-show="active === 'czfcsymxz'" @openPage="openPage" />
      </template>
      <!-- <template #menu-item>menu-item</template> -->
    </gt-collapse-menu>
  </div>
</template>

<script>
import Fcjcxxtz from '@/pages/index/views/fcstz/components/fcjcxxtz/index.vue'; // 房产基础信息台账 fcjcxxtz
import Zyfcsymxz from '@/pages/index/views/fcstz/components/zyfcsymxz/index.vue'; // 自用房产税源明细帐 zyfcsymxz
import Czfcsymxz from '@/pages/index/views/fcstz/components/czfcsymxz/index.vue'; // 出租房产税源明细帐 czfcsymxz

import { CollapseMenu } from '@gt4/common-front';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';

export default {
  components: {
    Mybreadcrumb,
    GtCollapseMenu: CollapseMenu,
    Fcjcxxtz,
    Zyfcsymxz,
    Czfcsymxz,
  },
  data() {
    return {
      defaultValue: 'fcjcxxtz',
      lsgltzbz: this.$route.query.lsgltzbz || false,
      active: 'fcjcxxtz',
      list: [
        {
          id: '1',
          title: '房产税台账',
          children: [
            {
              id: 'fcjcxxtz',
              title: '房产税总账',
              required: false,
              description: '房产基础信息台账',
            },
            {
              id: 'zyfcsymxz',
              title: '自用明细账',
              required: false,
              description: '自用房产税源明细帐',
            },
            {
              id: 'czfcsymxz',
              title: '出租明细账',
              required: false,
              description: '出租房产税源明细帐',
            },
          ],
        },
      ],
    };
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  methods: {
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true };
      this.$refs[val].query(params);
    },
    getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true };
      this.$refs[this.active].query(params);
    },
    // data不传的话默认用子页面的查询参数。notQuery传true时子页面不触发查询。from为父页面activ，用于子页面返回对应父页面，按需将from储存在子页面。
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) this.$refs[type].query({ flag, p: data, from });
      this.active = type;
    },
  },
  mounted() {
    console.log('this.$route.query', this.$route.query);
    if (this.$route.query.active === 'srzz') {
      this.active = this.$route.query.active;
      console.log('当前页面：', this.active);
      const params = { flag: true, p: { tabValue: this.$route.query.tabValue } };
      this.$refs[this.active].query(params);
    }
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '房产税台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: false, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从理税概览进
    const parmObjLsgl = {
      mybreadList: ['首页', '理税概览', '房产税台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/tzzx/lsgl'],
      goBackPath: '/znsb/view/tzzx/lsgl', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    if (this.lsgltzbz) {
      this.$refs.myBre.initMyBre(parmObjLsgl);
    } else {
      this.$refs.myBre.initMyBre(parmObjDhcd);
    }
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
