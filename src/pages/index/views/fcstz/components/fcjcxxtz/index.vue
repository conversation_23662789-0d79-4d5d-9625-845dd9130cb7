<!--
 * @Descripttion: 台账-房产基础信息台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-07-29 15:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />

      <div class="queryBtns">
        <gt-space size="10px">
          <!-- <t-button variant="outline" theme="primary" :loading="dcLoading" @click="dr()">导入</t-button> -->
          <t-button theme="primary" @click="query({ flag: true })"><CloudDownloadIcon slot="icon" />提取数据</t-button>
          <t-button theme="primary" @click="scsy()"><FileAddIcon slot="icon" />生成税源</t-button>
          <t-button variant="outline" theme="primary" @click="newOrEditRow(null, true)"
            ><add-icon slot="icon" />新增</t-button
          >
          <t-button variant="outline" theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
          <t-button variant="outline" theme="primary" @click="ckdg()"><ChartIcon slot="icon" />查看底稿</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
        >
          <!-- <template #zyfcyz="{ row }">
          <span>{{ numberToPrice(row.zyfcyz) }}</span>
        </template>
        <template #czfcyz="{ row }">
          <span>{{ numberToPrice(row.czfcyz) }}</span>
        </template> -->
          <template #cz="{ row }">
            <gt-space size="10px">
              <t-link theme="primary" hover="color" :key="row.uuid" @click="newOrEditRow(row, false)">
                查询详情
              </t-link>
              <!-- <t-link theme="primary" hover="color" @click="qureyZcmx(row, false)"> 查询资产明细 </t-link> -->
              <t-link theme="primary" hover="color" @click="newOrEditRow(row, true)" v-show="row.sfyscsy === '否'">
                编辑
              </t-link>
            </gt-space>
          </template>
        </t-table>
      </div>
      <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @saveFcs="query()" />
      <ZcmxDialog :visible.sync="zcmxDialogVisible" v-if="zcmxDialogVisible" />
      <div v-show="boxvisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="请确认是否删除所选明细"
          :onConfirm="confirmDelRow"
          :onClose="closeBox"
        >
        </t-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import { initFcjcxxtzbQuery, deleteFcs, scsyFcs, initbQueryXq } from '@/pages/index/api/tzzx/fcstz/fcjcxxtz.js';
import { downloadBlobFile } from '@/core/download';
import { AddIcon, FileAddIcon, CloudDownloadIcon, ChartIcon, DeleteIcon, DownloadIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { querySearchConfig, dataColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';
import ZcmxDialog from './components/zcmx-dialog.vue';

export default {
  components: {
    SkeletonFrame,
    SearchControlPanel,
    AddIcon,
    DeleteIcon,
    DownloadIcon,
    FileAddIcon,
    CloudDownloadIcon,
    ChartIcon,
    EditDialog,
    ZcmxDialog,
  },
  data() {
    return {
      loading: true,
      footerFlag: false,
      userInfo: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      boxvisible: false,
      editDialogVisible: false,
      zcmxDialogVisible: false,
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      dataColumns,
      checkBox: [],
      delformData: [],
      footData: [],
      uuidList: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      qzczfcyz: '',
      qzczfcmj: '',
      jsbl: '',
      czmj: '',
      czfmc: '',
      czfnsrsbh: '',
      sbzjsr: '',
      sbqq: '',
      sbqz: '',
      fcyz: '',
    };
  },
  created() {
    // 房产用途
    // 征收对象
    // 不动产权证类型
  },
  mounted() {
    this.init();
  },
  methods: {
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    async init() {
      this.query(); // 初始化查询
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        this.$refs.queryControl.setParams(p);
      } else {
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await initFcjcxxtzbQuery(params);
        this.tableData = data.list || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.total;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    // 生成税源
    async scsy() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      if (!this.checkBox.length) {
        this.$message.warning('请选择要生成税源的数据');
        return;
      }
      this.checkBox.forEach((item) => {
        this.uuidList.push(item.uuid);
      });
      try {
        const { msg } = await scsyFcs(this.uuidList);
        this.$message.success(msg);
        this.checkBox = [];
        this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      } finally {
        this.query();
      }
    },
    // 查看底稿
    ckdg() {
      this.$router.push('/lsgl');
    },
    // 增行
    newOrEditRow(row, Flag) {
      const pageType = row?.uuid ? 1 : 0;
      if (pageType) {
        this.queryXq(row.uuid);
      }
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        footerFlag: Flag,
        fcyz: this.fcyz,
        qzczfcyz: this.qzczfcyz,
        qzczfcmj: this.qzczfcmj,
        jsbl: this.jsbl,
        czmj: this.czmj,
        czfmc: this.czfmc,
        czfnsrsbh: this.czfnsrsbh,
        sbzjsr: this.sbzjsr,
        sbqq: this.sbqq,
        sbqz: this.sbqz,
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    // 查询资产明细
    qureyZcmx(row, Flag) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        footerFlag: Flag,
      };
      const rowdata = JSON.stringify(row);
      this.zcmxDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    // 查询详情
    async queryXq(rowuuid) {
      this.tableLoading = true;
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        uuid: rowuuid,
      };
      try {
        const { data } = await initbQueryXq(params);
        this.fcyz = data.zyfresvo.fcyz;
        this.qzczfcyz = data.czfresvo.czyz;
        this.qzczfcmj = data.czfresvo.czmj;
        this.jsbl = data.zyfresvo.jsbl;
        this.czmj = data.czfresvo.czmj;
        this.czfmc = data.czfresvo.czf1;
        this.czfnsrsbh = data.czfresvo.spmc1;
        this.sbzjsr = data.czfresvo.zjsr1;
        this.sbqq = data.czfresvo.czsjq;
        this.sbqz = data.czfresvo.czsjz;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    // 删除-调用后台接口
    async delete(params) {
      try {
        const { msg } = await deleteFcs(params);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    // 删行
    async delRow() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    closeBox() {
      this.boxvisible = false;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610791',
        tzlx: 'fcszb',
        fileName: '房产基础信息台账',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/.filter-btns {
  float: right;
}
</style>
