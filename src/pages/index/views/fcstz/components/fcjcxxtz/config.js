export const querySearchConfig = [
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房产名称',
    key: 'fcmc',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房屋坐落地址',
    key: 'fwzldz',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.sfyscsy === '否') }),
    width: 50,
    fixed: 'left',
  },
  {
    colKey: 'nsrsbh',
    title: '企业税号',
    width: 180,
  },
  {
    colKey: 'nsrmc',
    title: '企业名称',
    width: 180,
  },
  {
    colKey: 'sfyscsy',
    title: '是否已生成税源',
    width: 120,
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 160,
  },
  {
    colKey: 'fcmc',
    title: '房产名称',
    width: 160,
  },
  {
    colKey: 'fwzldz',
    title: '房屋坐落地址',
    width: 200,
  },
  {
    align: 'right',
    width: 120,
    colKey: 'zyfcyz',
    title: '自用房产原值',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'czyz',
    title: '出租房产原值',
  },
  {
    align: 'center',
    width: 260,
    colKey: 'cz',
    title: '操作',
  },
];
