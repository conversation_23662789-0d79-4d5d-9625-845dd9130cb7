<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增房产基础信息', '编辑房产基础信息'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
    :footer="footerFlag"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <template>
          <!-- <t-tabs v-model="value">
          <t-tab-panel :value="1" label="基础信息"> -->
          <!-- <label style="font-size: 16px; color: blue">基础信息</label> -->
          <div class="zxsy-bg-new">
            <p class="xzsy-header">基础信息</p>
            <!-- <t-divider style="margin: 2px 0"></t-divider> -->
            <!-- <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules"> -->
            <t-row :gutter="16">
              <t-col :span="3">
                <t-form-item label="房源编号" name="fybh">
                  <t-input
                    :maxlength="30"
                    v-model="formData.fybh"
                    placeholder="请填写房源编号"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房产名称" name="fcmc">
                  <t-input
                    :maxlength="30"
                    v-model="formData.fcmc"
                    placeholder="请填写房产名称"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房产用途" name="fcytDm">
                  <t-select v-model="formData.fcytDm" placeholder="请选择房产用途" clearable :disabled="disabled">
                    <t-option
                      v-for="item in fcytList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房产取得时间" name="qdsj">
                  <t-date-picker
                    class="form-list-itme"
                    v-model="formData.qdsj"
                    placeholder="请选择房产取得时间"
                    style="width: 276px; height: 32px"
                    clearable
                    :disabled="disabled"
                    :disable-date="disabledDate"
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="建筑面积" name="jzmj">
                  <gt-input-money v-model="formData.jzmj" theme="normal" align="left" clearable :disabled="disabled" />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="纳税人类型" name="fcsnsrlx">
                  <t-select
                    v-model="formData.fcsnsrlx"
                    placeholder="请选择纳税人类型"
                    clearable
                    :disabled="disabled"
                    @change="nsrlxChange"
                  >
                    <t-option
                      v-for="item in nsrlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="所有权人纳税人识别号" name="qsrnsrsbh">
                  <t-input
                    v-model="formData.qsrnsrsbh"
                    placeholder="请填写所有权人纳税人识别号"
                    clearable
                    :disabled="disabled || mcandsbhFlag"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="所有权人名称" name="qsrnsrmc">
                  <t-input
                    v-model="formData.qsrnsrmc"
                    placeholder="请填写所有权人名称"
                    clearable
                    :disabled="disabled || mcandsbhFlag"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="不动产权证类型" name="bdcqzlxDm">
                  <t-select
                    v-model="formData.bdcqzlxDm"
                    placeholder="请选择不动产权证类型"
                    clearable
                    :disabled="disabled"
                    @change="bdcqzlxChange"
                  >
                    <t-option
                      v-for="item in bdcqzlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="不动产单元号" name="bdcdyh">
                  <t-input
                    :maxlength="30"
                    v-model="formData.bdcdyh"
                    placeholder="请填写不动产单元号"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="不动产权证书号" name="fwcqzsh">
                  <t-input
                    :maxlength="30"
                    v-model="formData.fwcqzsh"
                    placeholder="请填写不动产权证书号"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="资产编号" name="zcbh">
                  <t-input
                    :maxlength="30"
                    v-model="formData.zcbh"
                    placeholder="请填写资产编号"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房屋坐落地址(行政区划)" name="xzqhszDm">
                  <t-select v-model="formData.xzqhszDm" placeholder="请选择行政区划" clearable :disabled="disabled">
                    <t-option
                      v-for="item in xzqhDmList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房屋坐落地址(街道乡镇)" name="jdxzDm">
                  <t-select v-model="formData.jdxzDm" placeholder="请选择街道乡镇" clearable :disabled="disabled">
                    <t-option
                      v-for="item in jdxzDmList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房屋坐落详细地址" name="fwzldz">
                  <t-input
                    :maxlength="30"
                    v-model="formData.fwzldz"
                    placeholder="请填写房屋坐落详细地址"
                    clearable
                    :disabled="disabled"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="房产所属主管税务所(科、分局)" name="zgswskfjDm">
                  <t-select
                    v-model="formData.zgswskfjDm"
                    placeholder="请选择主管税务所(科、分局)"
                    clearable
                    :disabled="disabled"
                  >
                    <t-option
                      v-for="item in zgswskfjDmList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </t-form-item>
              </t-col>
            </t-row>
          </div>

          <!-- </t-form> -->
          <!-- </t-tab-panel>
          <t-tab-panel :value="2" label="从价信息"> -->
          <!-- <label style="font-size: 16px; color: blue">从价信息</label> -->
          <div class="zxsy-bg-new">
            <p class="xzsy-header">从价信息</p>
            <!-- <t-divider style="margin: 2px 0"></t-divider> -->
            <!-- <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules"> -->
            <t-row :gutter="16">
              <t-col :span="3">
                <t-form-item label="房产原值" name="fcyz">
                  <gt-input-money v-model="formData.fcyz" theme="normal" align="left" clearable :disabled="disabled" />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="其中：出租房产原值" name="qzczfcyz">
                  <gt-input-money
                    v-model="formData.qzczfcyz"
                    theme="normal"
                    align="left"
                    clearable
                    :disabled="disabled"
                    :onChange="czfcyzChange"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="其中：出租房产面积" name="qzczfcmj">
                  <gt-input-money
                    v-model="formData.qzczfcmj"
                    theme="normal"
                    align="left"
                    clearable
                    :disabled="disabled"
                    :onChange="czfcmjChange"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="计税比例" name="jsbl">
                  <t-select v-model="formData.jsbl" placeholder="请选择计税比例" clearable :disabled="disabled">
                    <t-option
                      v-for="item in jsblList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option> </t-select
                ></t-form-item>
              </t-col>
            </t-row>
            <!-- </t-form> -->
            <!-- </t-tab-panel>
          <t-tab-panel :value="3" label="从租信息"> -->
            <!-- <label style="font-size: 16px; color: blue">从租信息</label> -->
          </div>
          <div class="zxsy-bg-new">
            <p class="xzsy-header">从租信息</p>
            <!-- <t-divider style="margin: 2px 0"></t-divider> -->
            <!-- <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules"> -->
            <t-row :gutter="16">
              <t-col :span="3">
                <t-form-item label="出租面积" name="czmj">
                  <gt-input-money v-model="formData.czmj" theme="normal" align="left" clearable :disabled="true" />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="承租方名称" name="czfmc">
                  <t-input
                    :maxlength="30"
                    v-model="formData.czfmc"
                    placeholder="请填写承租方名称"
                    clearable
                    :disabled="disabled || fcczDisable"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="承租方纳税人识别号" name="czfnsrsbh">
                  <t-input
                    :maxlength="30"
                    v-model="formData.czfnsrsbh"
                    placeholder="请填写承租方纳税人识别号"
                    clearable
                    :disabled="disabled || fcczDisable"
                  ></t-input>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="申报租金收入" name="sbzjsr">
                  <gt-input-money
                    v-model="formData.sbzjsr"
                    theme="normal"
                    align="left"
                    clearable
                    :disabled="disabled || fcczDisable"
                  />
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="申报期起" name="sbqq">
                  <t-date-picker
                    v-model="formData.sbqq"
                    placeholder="请填写申报期起"
                    style="width: 276px; height: 32px"
                    clearable
                    :disabled="disabled || fcczDisable"
                    :disableDate="(date) => getDisableDate(date, 'sbqz', 'start')"
                  ></t-date-picker>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item label="申报期止" name="sbqz">
                  <t-date-picker
                    v-model="formData.sbqz"
                    placeholder="请填写申报期止"
                    style="width: 276px; height: 32px"
                    clearable
                    :disabled="disabled || fcczDisable"
                    :disableDate="(date) => getDisableDate(date, 'sbqq', 'end')"
                  ></t-date-picker>
                </t-form-item>
              </t-col>
            </t-row>
          </div>

          <!-- </t-form> -->
          <!-- </t-tab-panel>
        </t-tabs> -->
        </template>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getXzqhJdxzSwjg, queryFcjsblList } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
// import { Input, InputNumber, DatePicker } from 'tdesign-vue';
import { saveFcs, updateFcs, initbQueryXq } from '@/pages/index/api/tzzx/fcstz/fcjcxxtz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { disabledDate } from './config.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      qdsj: [{ required: true, message: '必填', type: 'error' }],
      fcqdsj: [{ required: true, message: '必填', type: 'error' }],
      jzmj: [{ required: true, message: '必填', type: 'error' }],
      fcsnsrlx: [{ required: true, message: '必填', type: 'error' }],
      bdcqzlxDm: [{ required: true, message: '必填', type: 'error' }],
      xzqhszDm: [{ required: true, message: '必填', type: 'error' }],
      jdxzDm: [{ required: true, message: '必填', type: 'error' }],
      fwzldz: [{ required: true, message: '必填', type: 'error' }],
      zgswskfjDm: [{ required: true, message: '必填', type: 'error' }],
      fcytDm: [{ required: true, message: '必填', type: 'error' }],
      qzczfcyz: [{ required: true, message: '必填', type: 'error' }],
      qzczfcmj: [{ required: true, message: '必填', type: 'error' }],
      jsbl: [{ required: true, message: '必填', type: 'error' }],
      fcyz: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      fcytList: [], // 房产用途
      bdcqzlxList: [], // 不动产权证类型
      nsrlxList: [], // 纳税人类型
      fcsnsrlx: '',
      xzqhDmList: [], // 房屋坐落地址(行政区划)
      jdxzDmList: [], // 房屋坐落地址(街道乡镇)
      zgswskfjDmList: [], // 房产所属主管税务所(科、分局)列表
      jsblList: [], // 计税比例list
      jsbl: '', // 计税比例
      zgswskfjDm: '', // 房产所属主管税务所(科、分局)
      syqrnsrsbh: '', // 所有权人纳税人识别号
      syqrmc: '', // 所有权人名称
      disabled: false,
      mcandsbhFlag: true,
      fcczDisable: true,
      isVisible: true,
      confirmLoading: false,
      rules: {},
      selectedRowKeys: [],
      checkBox: [],
      value: 1,
      disabledDate,
      formData: {
        uuid: '',
        fybh: '',
        fcmc: '',
        fwzldz: '',
        jzmj: '',
        qdsj: '',
        fcytDm: '',
        fcsnsrlx: '',
        qsrnsrsbh: '',
        qsrnsrmc: '',
        bdcqzlxDm: '',
        bdcdyh: '',
        fwcqzsh: '',
        xzqhszDm: '',
        jdxzDm: '',
        zgswskfjDm: '',
        czfmc: '',
        czfnsrsbh: '',
        sbqq: '',
        sbqz: '',
        qzczfcyz: '',
        czmj: '',
        sbzjsr: '',
        jsbl: '',
        fcyz: '',
        qzczfcmj: '',
        zcbh: '',
      },
    };
  },
  created() {
    // 初始化是否显示保存和取消按钮
    this.footerFlag = this.visible.otherObj.footerFlag;
    // this.init();
  },
  mounted() {
    this.init();
    this.formData.fcsnsrlx = '01';
    this.formData.qsrnsrsbh = this.$store.state.zzstz.userInfo?.nsrsbh;
    this.formData.qsrnsrmc = this.$store.state.zzstz.userInfo?.jgmc;
    if (this.visible.row?.uuid) {
      this.formData = this.visible.row;
      this.formData.fcyz = this.visible.otherObj.fcyz;
      this.formData.qzczfcyz = this.visible.otherObj.qzczfcyz;
      this.formData.qzczfcmj = this.visible.otherObj.qzczfcmj;
      this.formData.jsbl = this.visible.otherObj.jsbl;
      this.formData.czmj = this.visible.otherObj.czmj;
      this.formData.czfmc = this.visible.otherObj.czfmc;
      this.formData.czfnsrsbh = this.visible.otherObj.czfnsrsbh;
      this.formData.sbzjsr = this.visible.otherObj.sbzjsr;
      this.formData.sbqq = this.visible.otherObj.sbqq;
      this.formData.sbqz = this.visible.otherObj.sbqz;
    }
  },
  methods: {
    async init() {
      this.fcczNotRequire();
      // 初始化房屋坐落地址行政区划和街道乡镇
      this.initFcsXzqhandJdxz();
      // 基本条件
      this.rules = this.baseRules;
      if (this.footerFlag) {
        this.disabled = false;
        // 房产用途
        this.fcytList = [
          { value: '01', label: '工业' },
          { value: '02', label: '商业及办公' },
          { value: '03', label: '住房' },
          { value: '04', label: '其他' },
        ];
        // 不动产权证类型
        this.bdcqzlxList = [
          { value: '01', label: '新证' },
          { value: '02', label: '其他证书' },
          { value: '03', label: '暂未取得' },
        ];
        // 纳税人类型
        this.nsrlxList = [
          { value: '01', label: '产权所有人' },
          { value: '02', label: '经营管理人' },
          { value: '03', label: '承典人' },
          { value: '04', label: '房屋代管人' },
          { value: '05', label: '房屋使用人' },
          { value: '06', label: '融资租赁承租人' },
        ];
      } else {
        // 查询详情
        // 所有编辑框状态为不可用
        this.formData = this.visible.row;
        this.disabled = true;
      }
    },
    async initFcsXzqhandJdxz() {
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
      const { data } = await getXzqhJdxzSwjg(params);
      this.xzqhDmList = data.DM_GY_XZQH;
      this.jdxzDmList = data.DM_GY_JDXZ;
      this.zgswskfjDmList = data.DM_GY_SWJG;
      this.zgswskfjDm = data.zgswskfjDm;
      if (this.xzqhDmList.length === 1) {
        this.formData.xzqhszDm = this.xzqhDmList[0].value;
      }
      if (this.jdxzDmList.length === 1) {
        this.formData.jdxzDm = this.jdxzDmList[0].value;
      }
      if (this.zgswskfjDmList.length === 1) {
        this.formData.zgswskfjDm = this.zgswskfjDmList[0].value;
      }
      // 获取房产税计税比例下拉框
      const mparam = {
        swjgDm: this.zgswskfjDm,
        czlx: 'add',
      };
      const cxjg = await queryFcjsblList(mparam);
      if (!cxjg.error) {
        this.jsblList = cxjg.data.jsblList;
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      if (val === true) {
        this.confirmLoading = true;
        const p = {};
        [
          'uuid',
          'fybh',
          'fcmc',
          'fwzldz',
          'jzmj',
          'qdsj',
          'fcytDm',
          'fcsnsrlx',
          'qsrnsrsbh',
          'qsrnsrmc',
          'bdcqzlxDm',
          'bdcdyh',
          'fwcqzsh',
          'xzqhszDm',
          'jdxzDm',
          'zgswskfjDm',
          'zcbh',
          'jsbl',
          'sbqq',
          'sbqz',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const czvo = {
          czf1: this.formData.czfmc,
          spmc1: this.formData.czfnsrsbh,
          czsjq: this.formData.sbqq,
          czsjz: this.formData.sbqz,
          czyz: this.formData.qzczfcyz,
          czmj: this.formData.qzczfcmj,
          zjsr1: this.formData.sbzjsr,
        };
        const zyvo = {
          jsbl: this.formData.jsbl,
          fcyz: this.formData.fcyz,
        };
        const qita = {
          sfyscsy: 'N',
          cjbz2: 'N',
          czbz2: 'N',
          ly: '0',
        };
        const params = { ...p, ...this.visible.otherObj, ...qita, czfreqvo: czvo, zyfreqvo: zyvo };
        try {
          if (this.visible.pageType) {
            await updateFcs(params);
          } else {
            await saveFcs(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          this.$emit('saveFcs');
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(2) : '0.00';
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    czfcyzChange(val) {
      Number(val) > 0 && Number(this.formData.qzczfcmj) > 0 ? this.fcczRequire() : this.fcczNotRequire();
    },
    czfcmjChange(val) {
      Number(val) > 0 && Number(this.formData.qzczfcyz) > 0 ? this.fcczRequire() : this.fcczNotRequire();
    },
    fcczRequire() {
      this.fcczDisable = false;
      this.rules = {
        ...this.baseRules,
        czmj: [{ required: true, message: '必填', type: 'error' }],
        sbzjsr: [{ required: true, message: '必填', type: 'error' }],
        sbqq: [{ required: true, message: '必填', type: 'error' }],
        sbqz: [{ required: true, message: '必填', type: 'error' }],
      };
      this.formData.czmj = this.formData.qzczfcmj;
    },
    fcczNotRequire() {
      this.fcczDisable = true;
      this.rules = this.baseRules;
      this.formData.czmj = '';
    },
    // 不动产权证类型变化时
    bdcqzlxChange(val) {
      if (val === '01') {
        // 不动产单元号和不动产证书号为必填项
        this.rules = {
          ...this.baseRules,
          fwcqzsh: [{ required: true, message: '必填', type: 'error' }],
          bdcdyh: [{ required: true, message: '必填', type: 'error' }],
        };
      } else if (val === '02') {
        // 不动产证书号为非必填项
        this.rules = {
          ...this.baseRules,
          fwcqzsh: [{ required: true, message: '必填', type: 'error' }],
        };
      } else {
        this.rules = this.baseRules;
      }
    },
    // 纳税人类型变化时
    nsrlxChange(val) {
      if (val === '01') {
        // 直接带出所有权人纳税人识别号和所有权人名称且不可修改
        this.formData.qsrnsrsbh = this.$store.state.zzstz.userInfo?.nsrsbh;
        this.formData.qsrnsrmc = this.$store.state.zzstz.userInfo?.jgmc;
        this.mcandsbhFlag = true;
      } else {
        // 所有权人纳税人识别号和所有权人名称可输入
        this.mcandsbhFlag = false;
        this.formData.qsrnsrsbh = '';
        this.formData.qsrnsrmc = '';
      }
    },
    getDisableDate(date, relation, timeRange) {
      const selectData = this.formData[relation];
      if (!selectData) return false;
      const formatDate = dayjs(selectData).hour(0).minute(0).second(0);
      if (timeRange === 'start') {
        // 大于选中结束时间的都不可选
        return date > new Date(formatDate);
      }
      if (timeRange === 'end') {
        // 小于选中开始时间的都不可选
        return date < new Date(formatDate);
      }
      if (timeRange === 'Today') {
        // 大于选中结束时间的都不可选
        return date > new Date();
      }
      return false;
    },
    query() {
      this.tableLoading = true;
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        uuid: this.formData.uuid,
      };
      try {
        const { data } = initbQueryXq(params);
        this.tableData = data.list || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.formData.fcyz = data.zyfresvo.fcyz;
        console.log('this.formData.fcyz', this.formData.fcyz);
        this.formData.qzczfcyz = data.czfresvo.czyz;
        this.formData.qzczfcmj = data.czfresvo.czmj;
        this.formData.jsbl = data.zyfresvo.jsbl;
        this.formData.czmj = data.czfresvo.czmj;
        this.formData.czfmc = data.czfresvo.czf1;
        this.formData.czfnsrsbh = data.czfresvo.spmc1;
        this.formData.sbzjsr = data.czfresvo.zjsr1;
        this.formData.sbqq = data.czfresvo.czsjq;
        this.formData.sbqz = data.czfresvo.czsjz;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
.zxsy-bg-new {
  width: 100%;
  padding: 15px;
  margin-bottom: 20px;
  background: #f8fbfc;
  border-radius: 4px;
}
.xzsy-header {
  width: 100%;
  padding-bottom: 12px;
  padding-left: 6px;
  margin-bottom: 24px;
  font-family: 'PingFang SC';
  font-size: 15px;
  font-weight: 400;
  line-height: 13px;
  letter-spacing: 0;
  color: #333;
  border-bottom: 2px solid #eef1f2;
}
.xzsy-header::before {
  display: inline-block;
  width: 12px;
  height: 13px;
  line-height: 13px;
  border-left: 4px solid #0084f3;
  content: '';
}
</style>
