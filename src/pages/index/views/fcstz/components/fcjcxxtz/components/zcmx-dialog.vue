<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['资产明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
    :footer="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <template>
        <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
          <t-row :gutter="16">
            <t-col :span="4">
              <t-form-item label="公司号" name="gsh">
                <t-input v-model="formData.gsh" placeholder="请填写公司号" clearable></t-input>
              </t-form-item>
            </t-col>
            <t-col :span="4">
              <t-form-item label="税源编号" name="sybh">
                <t-input v-model="formData.sybh" placeholder="请填写税源编号" clearable></t-input>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </template>
      <template>
        <t-table
          ref="tableRef"
          row-key="uuid"
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          lazyLoad
          :loading="tableLoading"
          :pagination="pagination"
          @page-change="pageChange"
        >
        </t-table>
      </template>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
// import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { initZcmxbQuery } from '@/pages/index/api/tzzx/fcstz/fcjcxxtz.js';
import { dataColumns } from './config.js';

export default {
  components: { CssDialog },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      // lrzx: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      dataColumns,
      checkBox: [],
      formData: {
        uuid: '',
        lrzx: '',
      },
    };
  },
  created() {
    this.init();
    this.query();
  },
  methods: {
    async init() {
      console.log('a');
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      console.log('a');
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        this.$refs.queryControl.setParams(p);
      } else {
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await initZcmxbQuery(params);
        console.log(data.records, 'data.records');
        this.tableData = data.records || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.pageTotal;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
