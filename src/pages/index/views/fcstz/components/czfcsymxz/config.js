export const querySearchConfig = [
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '商铺名称',
    key: 'spmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '',
    key: 't1',
  },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    fixed: 'left',
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 180,
  },
  {
    colKey: 'czf1',
    title: '承租方',
    width: 180,
  },
  {
    colKey: 'spmc1',
    title: '商铺名称',
    width: 180,
  },
  {
    colKey: 'czsjq',
    title: '承租时间起',
    width: 120,
  },
  {
    colKey: 'czsjz',
    title: '承租时间止',
    width: 120,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'czyz',
    title: '出租房产原值',
  },
  {
    align: 'right',
    width: 140,
    colKey: 'czmj',
    title: '出租面积（㎡）',
  },
  {
    align: 'right',
    width: 140,
    colKey: 'zjsr1',
    title: '月租金',
  },
  {
    align: 'right',
    colKey: 'sl1',
    title: '税率',
    width: 80,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'fcs1',
    title: '应纳税额',
  },
  // {
  //   align: 'center',
  //   colKey: 'cz',
  //   title: '操作',
  //   width: 120,
  // },
];
