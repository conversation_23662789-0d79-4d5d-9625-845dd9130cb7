<!--
 * @Descripttion: 台账-房产税出租明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-07-19 20:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
      <template #t1><span></span></template>
    </search-control-panel>

    <div class="queryBtns">
      <gt-space size="10px">
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
      >
        <!-- <template #czyz="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.czyz) }}</span>
          </div>
        </template>
        <template #czmj="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.czmj) }}</span>
          </div>
        </template>
        <template #zjsr1="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.zjsr1) }}</span>
          </div>
        </template> -->
        <template #sl1="{ row }">
          <div style="float: right">
            <span>{{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}</span>
          </div>
        </template>
        <!-- <template #fcs1="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.fcs1) }}</span>
          </div>
        </template> -->
        <!-- <template #cz="{ row }">
          <t-link v-show="row.fybh" theme="primary" hover="color" @click="openCkndyzj(row)"> 查看年度月租金 </t-link>
        </template> -->
      </t-table>
    </div>
    <MxDialog :visible.sync="mxDialogVisible" v-if="mxDialogVisible" />
  </div>
</template>
<script>
import { getCzfsymx } from '@/pages/index/api/tzzx/fcstz/czfcsymxz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon } from 'tdesign-icons-vue';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { querySearchConfig, dataColumns } from './config.js';
import MxDialog from './components/mx-dialog.vue';

export default {
  components: {
    MxDialog,
    SearchControlPanel,
    DownloadIcon,
  },
  data() {
    return {
      userInfo: {},
      querySearchConfig,
      mxDialogVisible: false,
      tableLoading: false,
      dcLoading: false,
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      dataColumns,
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {},
  mounted() {},
  methods: {
    openCkndyzj(row) {
      this.mxDialogVisible = {
        ckpzh: row.ckpzh,
        lrzx: row.lrzx,
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        this.$refs.queryControl.setParams(p);
      } else {
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await getCzfsymx(params);
        this.tableData = data.list || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.pageTotal;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610791',
        tzlx: 'fcsczf',
        fileName: '出租房源明细账',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/.filter-btns {
  float: right;
}
</style>
