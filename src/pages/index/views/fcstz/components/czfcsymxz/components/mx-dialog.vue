<template>
  <css-dialog
    class="dialogCss"
    header="年度月租金明细"
    :visible.sync="isVisible"
    @close="onClose"
    :cancelBtn="'关闭'"
    :confirmBtn="null"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button theme="primary" :loading="dcLoading" @click="exportExcl"><DownloadIcon slot="icon" />导出</t-button>
        </gt-space>
      </div>
      <div>
        <t-table row-key="uuid" height="440px" :data="tableData" :foot-data="footData" :columns="mainColumns" />
      </div>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getNdyzj } from '@/pages/index/api/tzzx/fcstz/czfcsymxz.js';
import { downloadBlobFile } from '@/core/download';

export default {
  components: { CssDialog },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.mainColumns = [
      {
        colKey: 'serial-number',
        title: '序号',
        align: 'center',
        width: 60,
        foot: '合计',
      },
      {
        align: 'left',
        colKey: 'fybh',
        title: '房源编号',
        width: 120,
      },
      {
        align: 'center',
        colKey: 'czfYsrList',
        title: '租金收入',
        children: [
          {
            align: 'right',
            colKey: 'zjsr1',
            title: '一月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr2',
            title: '二月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr3',
            title: '三月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr4',
            title: '四月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr5',
            title: '五月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr6',
            title: '六月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr7',
            title: '七月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr8',
            title: '八月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr9',
            title: '九月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr10',
            title: '十月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr11',
            title: '十一月',
            width: 100,
          },
          {
            align: 'right',
            colKey: 'zjsr12',
            title: '十二月',
            width: 100,
          },
        ],
      },
      {
        align: 'center',
        width: 120,
        colKey: 'nd',
        title: '年度',
      },
      {
        align: 'right',
        colKey: 'zjsrhj',
        title: '租金收入合计',
        width: 140,
      },
      {
        align: 'right',
        width: 80,
        colKey: 'sl1',
        title: '税率',
      },
      {
        align: 'right',
        colKey: 'fcs1',
        title: '房产税合计',
        width: 140,
      },
    ];
    return {
      isVisible: true,
      dcLoading: false,
      tableData: [],
      footData: [],
      kjfpList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'JxseQueryCy',
        fileName: '进项税额转出会计凭证明细导出',
        cxParam: {
          nsrsbh: this.visible?.nsrsbh,
          sszq: this.visible?.sszq,
          jxsezcxmDm: this.visible?.jxsezcxmDm,
        },
      };
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async init() {
      const { data } = await getNdyzj({
        nsrsbh: this.visible?.nsrsbh,
        sszq: this.visible?.sszq,
        jxsezcxmDm: this.visible?.jxsezcxmDm,
      });
      this.tableData = data.list || [];
      this.footData =
        this.tableData.length > 0
          ? [
              {
                se: data.list?.hj.se,
              },
            ]
          : [];
    },
    onClose() {
      this.isVisible = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/ .t-table__content {
  // 表格高度修正
  height: 408px !important;
}
</style>
