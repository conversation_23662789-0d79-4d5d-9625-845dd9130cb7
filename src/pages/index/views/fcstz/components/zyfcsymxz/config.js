export const querySearchConfig = [
  {
    label: '房源编号',
    key: 'fybh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房产名称',
    key: 'fcmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '房屋坐落地址',
    key: 'fwzldz',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    fixed: 'left',
  },
  {
    colKey: 'fybh',
    title: '房源编号',
    width: 180,
  },
  {
    colKey: 'fcmc',
    title: '房产名称',
    width: 180,
  },
  {
    colKey: 'fwzldz',
    title: '房屋坐落地址',
    width: 220,
  },
  {
    colKey: 'jsbl',
    title: '计税比例',
    width: 80,
  },
  {
    colKey: 'sl1',
    title: '税率',
    width: 80,
  },
  {
    colKey: 'yxqq',
    title: '有效期起',
    width: 120,
  },
  {
    colKey: 'yxqz',
    title: '有效期止',
    width: 120,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'fcyz',
    title: '房产原值',
  },
  {
    align: 'right',
    width: 140,
    colKey: 'fcs1',
    title: '应纳税额',
  },
];
