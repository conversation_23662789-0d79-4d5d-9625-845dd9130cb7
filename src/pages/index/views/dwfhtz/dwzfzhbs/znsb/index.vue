<template>
  <div class="page">
    <Steps v-if="fromPage == 'zfba'" current="forth" />
    <div class="page-con">
      <div class="con-info">
        <div class="info-title">非居民扣缴智能申报</div>
        <div class="info-c">
          <span>{{ pageInfo.htmc }}</span>
          <span style="margin: 0 8px">|</span>
          <span>{{ pageInfo.htbh }}</span>
        </div>
      </div>
      <t-table
        :columns="columns"
        :data="tableData"
        :pagination="null"
        rowKey="ywlsUuid"
        :selected-row-keys="selectedRowKeys"
        @select-change="selectChange"
      >
        <template #operate="{ row }">
          <div class="table__operate">
            <t-link
              v-if="row.sbbztDm === '07' || row.sbbztDm === '03' || row.sbbztDm === '02'"
              theme="primary"
              hover="color"
              @click="handleClickJump(row, true, false)"
              >{{ row.zsxmDm === '10101' ? '打开报表' : '预览表单' }}</t-link
            >
            <t-link
              v-if="row.sbbztDm === '06'"
              theme="primary"
              hover="color"
              @click="handleClickError(row, false, false)"
              >处理异常</t-link
            >
            <t-link v-if="row.sbbztDm === '04'" theme="primary" hover="color" @click="handleClickErrInfo(row)"
              >处理异常</t-link
            >
            <t-link
              v-if="row.sbbztDm === '05'"
              theme="primary"
              hover="color"
              @click="handleClickError(row, false, true)"
              >处理异常</t-link
            >
            <t-link v-if="row.sbbztDm === '00'" theme="primary" hover="color">已申报</t-link>
            <t-link v-if="row.sbbztDm === '01'" theme="primary" hover="color">申报中</t-link>
          </div>
        </template>
      </t-table>
    </div>
    <gt-layout-footerbar>
      <gt-submit-footer :money="money" title="税款合计">
        <template slot="rightAction">
          <gt-button-group>
            <t-button :disabled="!selectedRowKeys.length" @click="onSubmit">提交申报</t-button>
          </gt-button-group>
        </template>
      </gt-submit-footer>
    </gt-layout-footerbar>
  </div>
</template>

<script>
import api from '@/pages/index/api/dwzfzhbs/index';
import { getDate } from '@gt4/common-front';
import { numToPercentage, format } from '@gt/components';
import { createStore } from '@/pages/index/views/sb/common/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import Steps from '@/pages/index/views/dwfhtz/dwzfzhbs/components/steps';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  mixins: [pageMixin, storeMixin],
  components: { Steps },
  data() {
    return {
      // 从那个页面跳转而来
      fromPage: this.$route.query ? this.$route.query.fromPage : '',
      pageInfo: {},
      columns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: 46,
          checkProps: ({ row }) => ({ disabled: row.sbbztDm !== '07' && row.sbbztDm !== '03' && row.sbbztDm !== '02' }),
        },
        {
          title: '税（费）种',
          cell: (h, { row }) => <span>{this.zsxmMap[row.zsxmDm] ? this.zsxmMap[row.zsxmDm] : ''}</span>,
        },
        {
          title: '支付日期',
          colKey: 'zfrq',
          cell: () => <span>{this.pageInfo.zfrq}</span>,
        },
        {
          title: '计税（费）金额',
          colKey: 'jsyj',
          cell: (h, { row }) => <span>{format(row.jsyj, 2)}</span>,
        },
        {
          title: '税率',
          colKey: 'sl',
          cell: (h, { row }) => this.calcuSl(row.sl),
        },
        {
          title: '税（费）额',
          colKey: 'yjsf',
          cell: (h, { row }) => <span>{format(row.yjsf, 2)}</span>,
        },
        {
          title: '操作',
          colKey: 'operate',
        },
      ],
      zsxmMap: {},
      tableData: [],
      selectedRowKeys: [],
      selectedRowData: [],
    };
  },
  computed: {
    money() {
      return this.selectedRowData.reduce((prev, cur) => {
        return prev + Number(cur.yjsf);
      }, 0);
    },
  },
  async created() {
    this.zsxmMap = this.formCT.zsxmCT;
    await this.getJsjgById();
  },
  methods: {
    async getJsjgById() {
      const { body } = await api.getJsjgById({
        jsjgUuid: this.$route.query.JsjgUuid,
      });
      this.pageInfo = body;
      this.tableData = body.znsbJsjgMx;
    },
    calcuSl(value) {
      // eslint-disable-next-line eqeqeq
      if (value == 0.1) return '减按10%';
      return numToPercentage(value);
    },
    selectChange(value, options) {
      this.selectedRowKeys = value;
      this.selectedRowData = options.selectedRowData;
    },
    handleClickJump(row, lookBackFlag, gotoFormFlag) {
      let path;
      const { sssqQ, sssqZ, ywlsUuid, sbbztDm, kjlx = '' } = row;
      const { xthtbh } = this.pageInfo;
      const jsjgUuid = this.$route.query.JsjgUuid;
      const query = {
        SssqQ: sssqQ,
        SssqZ: sssqZ,
        Ysqxxid: ywlsUuid,
        JsjgUuid: jsjgUuid,
      };
      if (sbbztDm === '04') {
        query.Xthtbh = xthtbh;
        query.Kjywlx = kjlx.substring(0, 1);
      }
      switch (row.zsxmDm) {
        case '10101':
          if (lookBackFlag) {
            // 预览   TODO 需要税友另外提供url
            window.location.href = `${window.location.origin}/sbzx/view/lzsfjssb/#/declare/dkdj?snapshotId=${ywlsUuid}&jsjgUuid=${jsjgUuid}`;
          } else if (sbbztDm === '04') {
            // 未暂存的情况，没有snapshotId
            window.location.href = `${window.location.origin}/sbzx/view/lzsfjssb/#/declare/dkdj?xthtbh=${xthtbh}&jsjgUuid=${jsjgUuid}`;
          } else {
            window.location.href = `${window.location.origin}/sbzx/view/lzsfjssb/#/declare/dkdj?snapshotId=${ywlsUuid}&jsjgUuid=${jsjgUuid}`;
          }
          return;
        case '10104':
          path = '/yyzx/kjqysdsbg';
          break;
        case '30217':
          path = '/yyzx/whsyjsfdkdjsb';
          break;
        default:
          break;
      }

      if (lookBackFlag) {
        query.lookBack = 'Y';
      }

      if (gotoFormFlag) {
        query.GotoForm = 'Y';
      }

      if (path) {
        this.$router.push({
          path,
          query,
        });
      }
    },
    handleClickErrInfo(row) {
      this.$gtDialog.warning({
        header: '未生成申报表原因',
        body: '当前业务办理繁忙，暂未能完成您的操作请求，请您稍后重试',
        cancelBtn: null,
        closeOnOverlayClick: false,
        confirmBtn: '修改报表',
        onConfirm: () => {
          this.handleClickJump(row, false, false);
        },
      });
    },
    handleClickError(row, lookBackFlag, gotoFormFlag) {
      const errorMessageList = [];
      const { zsxmDm, verifyInfo } = row;

      if (zsxmDm === '10101') {
        // 增值税的校验结果是一个个去校验的，没有汇总校验结果
        if (verifyInfo) {
          errorMessageList.push(verifyInfo);
        }
      } else {
        const errorList = JSON.parse(verifyInfo);
        Object.values(errorList).forEach((errors) => {
          errors.forEach((errorItem) => {
            if (errorItem.type === 'error') {
              errorMessageList.push(errorItem.message);
            }
          });
        });
      }

      if (errorMessageList.length) {
        const data = [];
        errorMessageList.forEach((item, index) => {
          data.push({
            index: index + 1,
            detail: item,
          });
        });

        const styleObj = {
          tableHeader: 'color: #333333; font-size: 14px; font-weight: 600',
        };
        const columns = [
          {
            colKey: 'index',
            width: '60px',
            title: () => <span style={styleObj.tableHeader}>序号</span>,
            attrs: {
              style: {
                color: '#666666',
              },
            },
          },
          {
            colKey: 'detail',
            width: '100%',
            title: () => <span style={styleObj.tableHeader}>详细说明</span>,
            attrs: {
              style: {
                color: '#666666',
              },
            },
          },
        ];

        this.$gtDialog.show({
          header: '校验提示',
          width: '80%',
          body: () => (
            <div>
              <t-alert theme="error" style="margin-bottom: 16px">
                以下内容校验不通过，请点击右下方“修改报表”修改后再提交申报
              </t-alert>
              <t-table row-key="index" maxHeight={500} data={data} columns={columns} />
            </div>
          ),
          cancelBtn: null,
          closeOnOverlayClick: false,
          confirmBtn: '修改报表',
          onConfirm: () => {
            this.handleClickJump(row, lookBackFlag, gotoFormFlag);
          },
        });
      }
    },
    async onSubmit() {
      // 弹出“真实”、“责任”弹框
      await this.beforeSubmitForm();
      // 构造申报提交报文
      const nowDate = getDate.getDate(new Date(), 'YYYY-MM-DD');
      const params = {
        JsjgUuid: this.$route.query.JsjgUuid,
        Ysqxxid: '',
        SubYwbm: '',
        SssqQ: nowDate,
        SssqZ: nowDate,
      };

      let ysqxxid = '';
      let ywbm = '';
      this.selectedRowData.forEach((item) => {
        const { ywlsUuid, subYwbm } = item;
        ysqxxid = `${ysqxxid + ywlsUuid}_`;
        ywbm = `${ywbm + subYwbm}_`;
      });
      // 去掉最后一个'_'
      ysqxxid = ysqxxid.length > 1 ? ysqxxid.substring(0, ysqxxid.length - 1) : '';
      ywbm = ywbm.length > 1 ? ywbm.substring(0, ywbm.length - 1) : '';

      params.Ysqxxid = ysqxxid;
      params.SubYwbm = ywbm;
      // 调用申报提交接口
      let res;
      if (ysqxxid.indexOf('_') === -1) {
        res = await api.submit(params);
      } else {
        res = await api.mulSubmit(params);
      }
      // 解析申报提交接口返回结果
      const { body } = res;
      if (body.ysqxxid) {
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, {
          pjhs: body.pjhs,
          djxh: this.pageInfo.djxh,
          zgswskfjDm: this.pageInfo.zgswskfjDm,
        });
        // 跳转回执页
        this.$router.push({
          name: this.resultRouterName,
          query: {
            ...this.$route.query,
            SubYwbm: ywbm,
          },
        });
      }
    },
    // 数字格式化
    format(value, num = 2) {
      return format(value, num);
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  &-con {
    padding: 24px;
    .con-info {
      margin-bottom: 16px;
      .info-title {
        margin-bottom: 4px;
        font-size: 18px;
      }
    }
    .table__operate {
      .t-link:not(:nth-child(1)) {
        margin-left: 12px;
      }
    }
  }
}
</style>
