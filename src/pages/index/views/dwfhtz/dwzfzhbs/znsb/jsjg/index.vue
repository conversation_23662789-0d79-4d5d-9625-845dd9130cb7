<template>
  <div class="page">
    <t-alert v-if="showTips" theme="info" close style="margin-bottom: 16px">
      <template #message>
        <span
          >此页面仅展示通过智能辅助申报工具计算的结果，已提交的申报表可在<span class="ele-item__color"
            >申报信息查询</span
          >中查询</span
        >
      </template>
    </t-alert>
    <div class="page-con">
      <gt-more-search
        :formOptions="formOptions"
        :segment="4"
        :showCount="3"
        @on-search="onSearchOrReset"
        @on-reset="onSearchOrReset"
      />
      <t-table :columns="columns" :data="tableData" :pagination="pagination" rowKey="_id" @page-change="onPageChange">
        <template #operate="{ row }">
          <gt-space>
            <t-link theme="primary" hover="color" @click="onZnsb(row)">智能申报</t-link>
            <t-popconfirm theme="warning" content="确认删除吗？" @confirm="onDel(row)">
              <t-link theme="primary" hover="color">删除</t-link>
            </t-popconfirm>
          </gt-space>
        </template>
      </t-table>
    </div>
  </div>
</template>

<script>
import api from '@/pages/index/api/dwzfzhbs/index';
import { format } from '@gt/components';
import { createStore } from '@/pages/index/views/sb/common/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  mixins: [pageMixin, storeMixin],
  data() {
    return {
      showTips: true,
      bzMap: {},
      sbbztOptions: [],
      searchForm: {
        htmc: '',
        htbh: '',
        fjmNsrmc: '',
        sbbztDm: undefined,
      },
      columns: [
        {
          title: '合同名称',
          colKey: 'htmc',
          width: 180,
          ellipsis: true,
        },
        {
          title: '合同编号',
          colKey: 'htbh',
          width: 180,
          ellipsis: true,
        },
        {
          title: '非居民企业名称',
          colKey: 'fjmNsrmc',
          width: 180,
          ellipsis: true,
        },
        {
          title: '应扣缴税费种',
          colKey: 'ykjsfz',
          width: 180,
          ellipsis: true,
          cell: (h, { row }) => <span class="ele-item__color">{row.ykjsfz}</span>,
        },
        {
          title: '支付日期',
          colKey: 'zfrq',
          width: 150,
        },
        {
          title: '币种',
          colKey: 'bzDm',
          width: 100,
          cell: (h, { row }) => <span>{this.bzMap[row.bzDm] ? this.bzMap[row.bzDm] : ''}</span>,
        },
        {
          title: '支付金额',
          colKey: 'zfje',
          width: 120,
          cell: (h, { row }) => <span>{format(row.zfje, 2)}</span>,
        },
        {
          title: '税款合计（人民币）',
          colKey: 'skhj',
          width: 180,
          cell: (h, { row }) => <span>{format(row.skhj, 2)}</span>,
        },
        {
          title: '操作',
          colKey: 'operate',
          width: 140,
          fixed: 'right',
        },
      ],
      zsxmMap: {},
      tableData: [],
      pagination: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    formOptions() {
      return [
        {
          label: '合同名称',
          prop: 'htmc',
          element: 't-input',
          initValue: '',
        },
        {
          label: '合同编号',
          prop: 'htbh',
          element: 't-input',
          initValue: '',
        },
        {
          label: '非居民企业名称',
          prop: 'fjmNsrmc',
          element: 't-input',
          initValue: '',
        },
        {
          label: '申报表状态',
          prop: 'sbbztDm',
          element: 't-select',
          initValue: '',
          options: this.sbbztOptions,
        },
      ];
    },
  },
  created() {
    this.initOptions();
    this.queryList();
  },
  methods: {
    initOptions() {
      this.sbbztOptions = this.formCT.sbztCT;
      this.zsxmMap = this.formCT.zsxmCT;
      // 初始化bzMap。{key:dm,value:mc}
      Object.keys(this.formCT.bzCT).forEach((key) => {
        const obj = this.formCT.bzCT[key];
        const { dm, mc } = obj;
        this.bzMap[dm] = mc;
      });
    },
    handleClose() {
      this.showTips = false;
    },
    onSearchOrReset(params) {
      this.searchForm = {
        ...params,
      };
      this.queryList();
    },
    async queryList() {
      const { pageNum, pageSize } = this.pagination;
      const data = {
        ...this.searchForm,
        znsbYwlx: '60012',
        pageNum,
        pageSize,
      };
      const { body } = await api.listJsjg(data);
      this.pagination.total = body.total;
      this.tableData = body.jsjg;
    },
    onPageChange({ current, pageSize }) {
      this.pagination.pageNum = current;
      this.pagination.pageSize = pageSize;
      this.queryList();
    },
    onZnsb(row) {
      this.$router.push({
        name: 'dwzfzhbs/znsb',
        query: {
          JsjgUuid: row._id,
        },
      });
    },
    async onDel(row) {
      const { bizCode } = await api.zfJsjg({
        jsjgUuid: row._id,
      });
      if (bizCode === '00') {
        this.$message.success('删除成功');
        this.queryList();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  background-color: #f5f8fa;
  &-con {
    padding: 24px;
    background-color: #fff;
  }
  .ele-item__color {
    padding: 0 0.5em;
    color: #4285f4;
  }
}
</style>
