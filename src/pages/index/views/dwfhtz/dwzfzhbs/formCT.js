export default {
  bzCT: {
    '001': {
      dm: '156',
      mc: '人民币元',
      ywmc: 'CNY',
    },
    '002': {
      dm: '840',
      mc: '美元',
      ywmc: 'USD',
    },
    '003': {
      dm: '826',
      mc: '英镑',
      ywmc: 'GBP',
    },
    '004': {
      dm: '978',
      mc: '欧元',
      ywmc: 'EUR',
    },
    '005': {
      dm: '344',
      mc: '香港元',
      ywmc: 'HKD',
    },
    '006': {
      dm: '446',
      mc: '澳门元',
      ywmc: 'MOP',
    },
    '007': {
      dm: '901',
      mc: '新台湾元',
      ywmc: 'TWD',
    },
    '008': {
      dm: '643',
      mc: '俄罗斯卢布',
      ywmc: 'RUB',
    },
    '009': {
      dm: '392',
      mc: '日元',
      ywmc: 'JPY',
    },
    '010': {
      dm: '959',
      mc: '黄金',
      ywmc: 'XAU',
    },
    '011': {
      dm: '232',
      mc: '纳克法',
      ywmc: 'ERN',
    },
    '012': {
      dm: '233',
      mc: '克罗姆',
      ywmc: 'EEK',
    },
    '013': {
      dm: '230',
      mc: '埃塞俄比亚比尔',
      ywmc: 'ETB',
    },
    '014': {
      dm: '238',
      mc: '福克兰群岛镑',
      ywmc: 'FKP',
    },
    '015': {
      dm: '208',
      mc: '丹麦克朗',
      ywmc: 'DKK',
    },
    '016': {
      dm: '242',
      mc: '斐济元',
      ywmc: 'FJD',
    },
    '017': {
      dm: '246',
      mc: '马克',
      ywmc: 'FIM',
    },
    '018': {
      dm: '250',
      mc: '法国法郎',
      ywmc: 'FRF',
    },
    '019': {
      dm: '270',
      mc: '达拉西',
      ywmc: 'GMD',
    },
    '020': {
      dm: '981',
      mc: '拉里',
      ywmc: 'GEL',
    },
    '021': {
      dm: '276',
      mc: '德国马克',
      ywmc: 'DEM',
    },
    '022': {
      dm: '288',
      mc: '塞地',
      ywmc: 'GHC',
    },
    '023': {
      dm: '292',
      mc: '直布罗陀镑',
      ywmc: 'GIP',
    },
    '024': {
      dm: '300',
      mc: '德拉克马',
      ywmc: 'GRD',
    },
    '025': {
      dm: '320',
      mc: '格查尔',
      ywmc: 'GTQ',
    },
    '026': {
      dm: '324',
      mc: '几内亚法郎',
      ywmc: 'GNF',
    },
    '027': {
      dm: '624',
      mc: '几内亚比绍比索',
      ywmc: 'GWP',
    },
    '028': {
      dm: '328',
      mc: '圭亚那元',
      ywmc: 'GYD',
    },
    '029': {
      dm: '332',
      mc: '古德',
      ywmc: 'HTG',
    },
    '030': {
      dm: '340',
      mc: '伦皮拉',
      ywmc: 'HNL',
    },
    '031': {
      dm: '203',
      mc: '捷克克朗',
      ywmc: 'CZK',
    },
    '032': {
      dm: '348',
      mc: '福林',
      ywmc: 'HUF',
    },
    '033': {
      dm: '352',
      mc: '冰岛克朗',
      ywmc: 'ISK',
    },
    '034': {
      dm: '356',
      mc: '印度卢比',
      ywmc: 'INR',
    },
    '035': {
      dm: '960',
      mc: '特别提款权',
      ywmc: 'XDR',
    },
    '036': {
      dm: '364',
      mc: '伊朗里亚尔',
      ywmc: 'IRR',
    },
    '037': {
      dm: '368',
      mc: '伊拉克第纳尔',
      ywmc: 'IQD',
    },
    '038': {
      dm: '372',
      mc: '爱尔兰镑',
      ywmc: 'IEP',
    },
    '039': {
      dm: '376',
      mc: '新锡克尔',
      ywmc: 'ILS',
    },
    '040': {
      dm: '388',
      mc: '牙买加元',
      ywmc: 'JMD',
    },
    '041': {
      dm: '626',
      mc: '东帝汶埃斯库多',
      ywmc: 'TPE',
    },
    '042': {
      dm: '400',
      mc: '约旦第纳尔',
      ywmc: 'JOD',
    },
    '043': {
      dm: '398',
      mc: '坚戈',
      ywmc: 'KZT',
    },
    '044': {
      dm: '404',
      mc: '肯尼亚先令',
      ywmc: 'KES',
    },
    '045': {
      dm: '408',
      mc: '北朝鲜圆',
      ywmc: 'KPW',
    },
    '046': {
      dm: '410',
      mc: '圆',
      ywmc: 'KRW',
    },
    '047': {
      dm: '414',
      mc: '科威特第纳尔',
      ywmc: 'KWD',
    },
    '048': {
      dm: '417',
      mc: '索姆',
      ywmc: 'KGS',
    },
    '049': {
      dm: '418',
      mc: '基普',
      ywmc: 'LAK',
    },
    '050': {
      dm: '428',
      mc: '拉脱维亚拉特',
      ywmc: 'LVL',
    },
    '051': {
      dm: '360',
      mc: '卢比',
      ywmc: 'IDR',
    },
    '052': {
      dm: '422',
      mc: '黎巴嫩镑',
      ywmc: 'LBP',
    },
    '053': {
      dm: '426',
      mc: '罗提',
      ywmc: 'LSL',
    },
    '054': {
      dm: '430',
      mc: '利比里亚元',
      ywmc: 'LRD',
    },
    '055': {
      dm: '434',
      mc: '利比亚第纳尔',
      ywmc: 'LYD',
    },
    '056': {
      dm: '440',
      mc: '立陶宛',
      ywmc: 'LTL',
    },
    '057': {
      dm: '442',
      mc: '卢森堡法郎',
      ywmc: 'LUF',
    },
    '058': {
      dm: '818',
      mc: '埃及镑',
      ywmc: 'EGP',
    },
    '059': {
      dm: '807',
      mc: '第纳尔',
      ywmc: 'MKD',
    },
    '060': {
      dm: '450',
      mc: '马尔加什法郎',
      ywmc: 'MGF',
    },
    '061': {
      dm: '458',
      mc: '马来西亚林吉特',
      ywmc: 'MYR',
    },
    '062': {
      dm: '462',
      mc: '卢菲亚',
      ywmc: 'MVR',
    },
    '063': {
      dm: '470',
      mc: '马尔他里拉',
      ywmc: 'MTL',
    },
    '064': {
      dm: '454',
      mc: '克瓦查',
      ywmc: 'MWK',
    },
    '065': {
      dm: '478',
      mc: '乌吉亚',
      ywmc: 'MRO',
    },
    '066': {
      dm: '480',
      mc: '毛里求斯卢比',
      ywmc: 'MUR',
    },
    '067': {
      dm: '484',
      mc: '墨西哥比索',
      ywmc: 'MXN',
    },
    '068': {
      dm: '979',
      mc: '墨西哥发展单位',
      ywmc: 'MXV',
    },
    '069': {
      dm: '498',
      mc: '摩尔瓦多列伊',
      ywmc: 'MDL',
    },
    '070': {
      dm: '496',
      mc: '图格里克',
      ywmc: 'MNT',
    },
    '071': {
      dm: '508',
      mc: '麦梯卡尔',
      ywmc: 'MZM',
    },
    '072': {
      dm: '104',
      mc: '缅元',
      ywmc: 'MMK',
    },
    '073': {
      dm: '516',
      mc: '纳米比亚元',
      ywmc: 'NAD',
    },
    '074': {
      dm: '524',
      mc: '尼泊尔卢比',
      ywmc: 'NPR',
    },
    '075': {
      dm: '528',
      mc: '荷兰盾',
      ywmc: 'NLG',
    },
    '076': {
      dm: '532',
      mc: '荷属安的列斯盾',
      ywmc: 'ANG',
    },
    '077': {
      dm: '558',
      mc: '金科多巴',
      ywmc: 'NIO',
    },
    '078': {
      dm: '566',
      mc: '奈拉',
      ywmc: 'NGN',
    },
    '079': {
      dm: '512',
      mc: '阿曼里亚尔',
      ywmc: 'OMR',
    },
    '080': {
      dm: '586',
      mc: '巴基斯坦卢比',
      ywmc: 'PKR',
    },
    '081': {
      dm: '590',
      mc: '巴波亚',
      ywmc: 'PAB',
    },
    '082': {
      dm: '598',
      mc: '基那',
      ywmc: 'PGK',
    },
    '083': {
      dm: '600',
      mc: '瓜拉尼',
      ywmc: 'PYG',
    },
    '084': {
      dm: '604',
      mc: '索尔',
      ywmc: 'PEN',
    },
    '085': {
      dm: '608',
      mc: '非律宾比索',
      ywmc: 'PHP',
    },
    '086': {
      dm: '985',
      mc: '兹罗提',
      ywmc: 'PLN',
    },
    '087': {
      dm: '620',
      mc: '葡萄牙埃斯库多',
      ywmc: 'PTE',
    },
    '088': {
      dm: '634',
      mc: '卡塔尔里亚尔',
      ywmc: 'QAR',
    },
    '089': {
      dm: '642',
      mc: '列伊',
      ywmc: 'ROL',
    },
    '090': {
      dm: '214',
      mc: '多米尼加比索',
      ywmc: 'DOP',
    },
    '092': {
      dm: '646',
      mc: '卢旺达法郎',
      ywmc: 'RWF',
    },
    '093': {
      dm: '654',
      mc: '圣赫勒拿磅',
      ywmc: 'SHP',
    },
    '094': {
      dm: '950',
      mc: 'CFA法郎BEAC',
      ywmc: 'XAF',
    },
    '095': {
      dm: '951',
      mc: '东加勒比元',
      ywmc: 'XCD',
    },
    '096': {
      dm: '882',
      mc: '塔拉',
      ywmc: 'WST',
    },
    '097': {
      dm: '380',
      mc: '意大利里拉',
      ywmc: 'ITL',
    },
    '098': {
      dm: '678',
      mc: '多布拉',
      ywmc: 'STD',
    },
    '099': {
      dm: '682',
      mc: '沙特里亚尔',
      ywmc: 'SAR',
    },
    100: {
      dm: '690',
      mc: '塞舌尔卢比',
      ywmc: 'SCR',
    },
    101: {
      dm: '694',
      mc: '利昂',
      ywmc: 'SLL',
    },
    102: {
      dm: '702',
      mc: '新加坡元',
      ywmc: 'SGD',
    },
    103: {
      dm: '703',
      mc: '斯洛伐克克朗',
      ywmc: 'SKK',
    },
    104: {
      dm: '705',
      mc: '托拉尔',
      ywmc: 'SIT',
    },
    105: {
      dm: '090',
      mc: '所罗门群岛元',
      ywmc: 'SBD',
    },
    106: {
      dm: '706',
      mc: '索马里先令',
      ywmc: 'SOS',
    },
    107: {
      dm: '710',
      mc: '兰特',
      ywmc: 'ZAR',
    },
    108: {
      dm: '724',
      mc: '西班牙比塞塔',
      ywmc: 'ESP',
    },
    109: {
      dm: '144',
      mc: '斯里兰卡卢比',
      ywmc: 'LKR',
    },
    110: {
      dm: '736',
      mc: '苏丹第纳尔',
      ywmc: 'SDD',
    },
    111: {
      dm: '740',
      mc: '苏里南盾',
      ywmc: 'SRG',
    },
    112: {
      dm: '578',
      mc: '挪威克朗',
      ywmc: 'NOK',
    },
    113: {
      dm: '748',
      mc: '里兰吉尼',
      ywmc: 'SZL',
    },
    114: {
      dm: '752',
      mc: '瑞典克朗',
      ywmc: 'SEK',
    },
    115: {
      dm: '756',
      mc: '瑞士法郎',
      ywmc: 'CHF',
    },
    116: {
      dm: '760',
      mc: '叙利亚镑',
      ywmc: 'SYP',
    },
    117: {
      dm: '262',
      mc: '吉布提法郎',
      ywmc: 'DJF',
    },
    118: {
      dm: '972',
      mc: '索莫尼',
      ywmc: 'TJS',
    },
    119: {
      dm: '834',
      mc: '坦桑尼亚先令',
      ywmc: 'TZS',
    },
    120: {
      dm: '764',
      mc: '铢',
      ywmc: 'THB',
    },
    121: {
      dm: '952',
      mc: 'CFA 法郎BCEAO',
      ywmc: 'XOF',
    },
    122: {
      dm: '554',
      mc: '新西兰元',
      ywmc: 'NZD',
    },
    123: {
      dm: '776',
      mc: '邦加',
      ywmc: 'TOP',
    },
    124: {
      dm: '780',
      mc: '特立尼达和多巴哥元',
      ywmc: 'TTD',
    },
    125: {
      dm: '788',
      mc: '突尼斯第纳尔',
      ywmc: 'TND',
    },
    126: {
      dm: '792',
      mc: '土耳其里拉',
      ywmc: 'TRL',
    },
    127: {
      dm: '795',
      mc: '马纳特',
      ywmc: 'TMM',
    },
    128: {
      dm: '036',
      mc: '澳大利亚元',
      ywmc: 'AUD',
    },
    129: {
      dm: '800',
      mc: '乌干达先令',
      ywmc: 'UGX',
    },
    130: {
      dm: '980',
      mc: '格里夫纳',
      ywmc: 'UAH',
    },
    131: {
      dm: '784',
      mc: 'UAE 迪拉姆',
      ywmc: 'AED',
    },
    132: {
      dm: '192',
      mc: '古巴比索',
      ywmc: 'CUP',
    },
    133: {
      dm: '998',
      mc: '(同日)',
      ywmc: 'USS',
    },
    134: {
      dm: '997',
      mc: '(次日)',
      ywmc: 'USN',
    },
    135: {
      dm: '858',
      mc: '乌拉圭比索',
      ywmc: 'UYU',
    },
    136: {
      dm: '860',
      mc: '乌兹别克斯坦苏姆',
      ywmc: 'UZS',
    },
    137: {
      dm: '548',
      mc: '瓦图',
      ywmc: 'VUV',
    },
    138: {
      dm: '704',
      mc: '盾',
      ywmc: 'VND',
    },
    139: {
      dm: '191',
      mc: '克罗地亚库纳',
      ywmc: 'HRK',
    },
    140: {
      dm: '953',
      mc: 'CFP 法郎',
      ywmc: 'XPF',
    },
    141: {
      dm: '504',
      mc: '摩洛哥迪拉姆',
      ywmc: 'MAD',
    },
    142: {
      dm: '886',
      mc: '也门里亚尔',
      ywmc: 'YER',
    },
    143: {
      dm: '891',
      mc: '南斯拉夫第纳尔',
      ywmc: 'YUM',
    },
    144: {
      dm: '894',
      mc: '克瓦查',
      ywmc: 'ZMK',
    },
    145: {
      dm: '716',
      mc: '津巴布韦元',
      ywmc: 'ZWD',
    },
    146: {
      dm: '222',
      mc: '萨尔瓦多科郎',
      ywmc: 'SVC',
    },
    147: {
      dm: '955',
      mc: '欧洲货币合成单',
      ywmc: 'XBA',
    },
    148: {
      dm: '956',
      mc: '欧洲货币单位(E.M.U.-6)',
      ywmc: 'XBB',
    },
    149: {
      dm: '957',
      mc: '欧洲帐户9 单位 (E.U.A.-9)',
      ywmc: 'XBC',
    },
    150: {
      dm: '958',
      mc: '欧洲帐户17 单位(E.U.A-17)',
      ywmc: 'XBD',
    },
    151: {
      dm: '964',
      mc: '钯',
      ywmc: 'XPD',
    },
    152: {
      dm: '962',
      mc: '铂白金',
      ywmc: 'XPT',
    },
    153: {
      dm: '961',
      mc: '银',
      ywmc: 'XAG',
    },
    154: {
      dm: 'Nil',
      mc: '黄金法郎',
      ywmc: 'XFO',
    },
    155: {
      dm: '963',
      mc: '为测试特别保留的代码',
      ywmc: 'XTS',
    },
    156: {
      dm: '999',
      mc: '未包括的交易货币代码指定为',
      ywmc: 'XXX',
    },
    157: {
      dm: '196',
      mc: '塞浦路斯镑',
      ywmc: 'CYP',
    },
    158: {
      dm: '004',
      mc: '阿富汗尼',
      ywmc: 'AFA',
    },
    159: {
      dm: '008',
      mc: '列克',
      ywmc: 'ALL',
    },
    160: {
      dm: '012',
      mc: '阿尔及利亚第纳尔',
      ywmc: 'DZD',
    },
    161: {
      dm: '020',
      mc: '安道尔比塞塔',
      ywmc: 'ADP',
    },
    162: {
      dm: '973',
      mc: '宽扎',
      ywmc: 'AOA',
    },
    163: {
      dm: '032',
      mc: '阿根廷比索',
      ywmc: 'ARS',
    },
    164: {
      dm: '051',
      mc: '亚美尼亚达姆',
      ywmc: 'AMD',
    },
    165: {
      dm: '533',
      mc: '阿鲁巴盾',
      ywmc: 'AWG',
    },
    166: {
      dm: '040',
      mc: '先令',
      ywmc: 'ATS',
    },
    167: {
      dm: '031',
      mc: '阿塞拜疆马纳特',
      ywmc: 'AZM',
    },
    168: {
      dm: '044',
      mc: '巴哈马元',
      ywmc: 'BSD',
    },
    169: {
      dm: '048',
      mc: '巴林第纳尔',
      ywmc: 'BHD',
    },
    170: {
      dm: '050',
      mc: '塔卡',
      ywmc: 'BDT',
    },
    171: {
      dm: '052',
      mc: '巴巴多斯元',
      ywmc: 'BBD',
    },
    172: {
      dm: '974',
      mc: '白俄罗斯卢布',
      ywmc: 'BYR',
    },
    173: {
      dm: '056',
      mc: '比利时法郎',
      ywmc: 'BEF',
    },
    174: {
      dm: '084',
      mc: '伯利兹元',
      ywmc: 'BZD',
    },
    175: {
      dm: '060',
      mc: '百慕大元',
      ywmc: 'BMD',
    },
    176: {
      dm: '064',
      mc: '努尔特鲁姆',
      ywmc: 'BTN',
    },
    177: {
      dm: '068',
      mc: '玻利瓦尔',
      ywmc: 'BOB',
    },
    178: {
      dm: '984',
      mc: 'Mvdol',
      ywmc: 'BOV',
    },
    179: {
      dm: '977',
      mc: '可自由兑换标记',
      ywmc: 'BAM',
    },
    180: {
      dm: '072',
      mc: '普拉',
      ywmc: 'BWP',
    },
    181: {
      dm: '986',
      mc: '巴西瑞尔',
      ywmc: 'BRL',
    },
    182: {
      dm: '096',
      mc: '文莱元',
      ywmc: 'BND',
    },
    183: {
      dm: '100',
      mc: '列弗',
      ywmc: 'BGL',
    },
    184: {
      dm: '975',
      mc: '保加利亚列弗',
      ywmc: 'BGN',
    },
    185: {
      dm: '108',
      mc: '布隆迪法郎',
      ywmc: 'BIF',
    },
    186: {
      dm: '116',
      mc: '瑞尔',
      ywmc: 'KHR',
    },
    187: {
      dm: '124',
      mc: '加元',
      ywmc: 'CAD',
    },
    188: {
      dm: '132',
      mc: '佛得角埃斯库多',
      ywmc: 'CVE',
    },
    189: {
      dm: '136',
      mc: '开曼群岛元',
      ywmc: 'KYD',
    },
    190: {
      dm: '152',
      mc: '智利比索',
      ywmc: 'CLP',
    },
    191: {
      dm: '990',
      mc: '发展单位',
      ywmc: 'CLF',
    },
    192: {
      dm: '170',
      mc: '哥伦比亚比索',
      ywmc: 'COP',
    },
    193: {
      dm: '174',
      mc: '科摩罗法郎',
      ywmc: 'KMF',
    },
    194: {
      dm: '976',
      mc: '刚果法郎',
      ywmc: 'CDF',
    },
    195: {
      dm: '188',
      mc: '哥斯达黎加科郎',
      ywmc: 'CRC',
    },
  },
  gjhdqCT: {
    '001': { dm: '156', mc: '中华人民共和国' },
    '002': { dm: '344', mc: '中国香港特别行政区' },
    '003': { dm: '446', mc: '中国澳门特别行政区' },
    '004': { dm: '158', mc: '中国台湾' },
    '005': { dm: '016', mc: '美属萨摩亚' },
    '006': { dm: '020', mc: '安道尔公国' },
    '007': { dm: '024', mc: '安哥拉共和国' },
    '008': { dm: '028', mc: '安提瓜和巴布达' },
    '009': { dm: '031', mc: '阿塞拜疆共和国' },
    '010': { dm: '032', mc: '阿根廷共和国' },
    '011': { dm: '036', mc: '澳大利亚联邦' },
    '012': { dm: '040', mc: '奥地利共和国' },
    '013': { dm: '044', mc: '巴哈马联邦' },
    '014': { dm: '048', mc: '巴林国' },
    '015': { dm: '050', mc: '孟加拉人民共和国' },
    '016': { dm: '051', mc: '亚美尼亚共和国' },
    '017': { dm: '052', mc: '巴巴多斯' },
    '018': { dm: '056', mc: '比利时王国' },
    '019': { dm: '060', mc: '百慕大' },
    '020': { dm: '064', mc: '不丹王国' },
    '021': { dm: '068', mc: '玻利维亚共和国' },
    '022': { dm: '070', mc: '波斯尼亚和黑塞哥维那' },
    '023': { dm: '072', mc: '博茨瓦纳共和国' },
    '024': { dm: '074', mc: '布维岛' },
    '025': { dm: '076', mc: '巴西联邦共和国' },
    '026': { dm: '084', mc: '伯利兹' },
    '027': { dm: '086', mc: '英属印度洋领地' },
    '028': { dm: '090', mc: '所罗门群岛' },
    '029': { dm: '092', mc: '英属维尔京群岛' },
    '030': { dm: '096', mc: '文莱达鲁萨兰国' },
    '031': { dm: '100', mc: '保加利亚共和国' },
    '032': { dm: '104', mc: '缅甸联邦' },
    '033': { dm: '108', mc: '布隆迪共和国' },
    '034': { dm: '112', mc: '白俄罗斯共和国' },
    '035': { dm: '116', mc: '柬埔寨王国' },
    '036': { dm: '120', mc: '喀麦隆共和国' },
    '037': { dm: '124', mc: '加拿大' },
    '038': { dm: '132', mc: '佛得角共和国' },
    '039': { dm: '136', mc: '开曼群岛' },
    '040': { dm: '140', mc: '中非共和国' },
    '041': { dm: '144', mc: '斯里兰卡民主社会主义共和国' },
    '042': { dm: '148', mc: '乍得共和国' },
    '043': { dm: '152', mc: '智利共和国' },
    '044': { dm: '004', mc: '阿富汗' },
    '045': { dm: '012', mc: '阿尔及利亚民主人民共和国' },
    '046': { dm: '162', mc: '圣诞岛' },
    '047': { dm: '166', mc: '科科斯（基林）群岛' },
    '048': { dm: '170', mc: '哥伦比亚共和国' },
    '049': { dm: '174', mc: '科摩罗伊斯兰联邦共和国' },
    '050': { dm: '175', mc: '马约特' },
    '051': { dm: '178', mc: '刚果共和国' },
    '052': { dm: '180', mc: '刚果民主共和国' },
    '053': { dm: '184', mc: '库克群岛' },
    '054': { dm: '188', mc: '哥斯达黎加共和国' },
    '055': { dm: '191', mc: '克罗地亚共和国' },
    '056': { dm: '192', mc: '古巴共和国' },
    '057': { dm: '196', mc: '塞浦路斯共和国' },
    '058': { dm: '203', mc: '捷克共和国' },
    '059': { dm: '204', mc: '贝宁共和国' },
    '060': { dm: '208', mc: '丹麦王国' },
    '061': { dm: '212', mc: '多米尼克国' },
    '062': { dm: '214', mc: '多米尼加共和国' },
    '063': { dm: '218', mc: '厄瓜多尔共和国' },
    '064': { dm: '222', mc: '萨尔瓦多共和国' },
    '065': { dm: '226', mc: '赤道几内亚共和国' },
    '066': { dm: '231', mc: '埃塞俄比亚联邦民主共和国' },
    '067': { dm: '232', mc: '厄立特里亚国' },
    '068': { dm: '233', mc: '爱沙尼亚共和国' },
    '069': { dm: '234', mc: '法罗群岛' },
    '070': { dm: '238', mc: '福克兰群岛（马尔维纳斯）' },
    '071': { dm: '239', mc: '南乔治亚岛和南桑德韦奇岛' },
    '072': { dm: '242', mc: '斐济群岛共和国' },
    '073': { dm: '246', mc: '芬兰共和国' },
    '074': { dm: '250', mc: '法兰西共和国' },
    '075': { dm: '254', mc: '法属圭亚那' },
    '076': { dm: '258', mc: '法属波利尼西亚' },
    '077': { dm: '260', mc: '法属南部领地' },
    '078': { dm: '262', mc: '吉布提共和国' },
    '079': { dm: '266', mc: '加蓬共和国' },
    '080': { dm: '268', mc: '格鲁吉亚' },
    '081': { dm: '270', mc: '冈比亚共和国' },
    '082': { dm: '275', mc: '巴勒斯坦国' },
    '083': { dm: '276', mc: '德意志联邦共和国' },
    '084': { dm: '288', mc: '加纳共和国' },
    '085': { dm: '292', mc: '直布罗陀' },
    '086': { dm: '296', mc: '基里巴斯共和国' },
    '087': { dm: '300', mc: '希腊共和国' },
    '088': { dm: '304', mc: '格陵兰' },
    '089': { dm: '308', mc: '格林纳达' },
    '090': { dm: '312', mc: '瓜德罗普' },
    '091': { dm: '316', mc: '关岛' },
    '092': { dm: '320', mc: '危地马拉共和国' },
    '093': { dm: '324', mc: '几内亚共和国' },
    '094': { dm: '328', mc: '圭亚那合作共和国' },
    '095': { dm: '332', mc: '海地共和国' },
    '096': { dm: '334', mc: '赫德岛和麦克唐纳岛' },
    '097': { dm: '336', mc: '梵蒂冈城国' },
    '098': { dm: '340', mc: '洪都拉斯共和国' },
    '099': { dm: '008', mc: '阿尔巴尼亚共和国' },
    100: { dm: '348', mc: '匈牙利共和国' },
    101: { dm: '352', mc: '冰岛共和国' },
    102: { dm: '356', mc: '印度共和国' },
    103: { dm: '360', mc: '印度尼西亚共和国' },
    104: { dm: '364', mc: '伊朗伊斯兰共和国' },
    105: { dm: '368', mc: '伊拉克共和国' },
    106: { dm: '372', mc: '爱尔兰' },
    107: { dm: '376', mc: '以色列国' },
    108: { dm: '380', mc: '意大利共和国' },
    109: { dm: '384', mc: '科特迪瓦共和国' },
    110: { dm: '388', mc: '牙买加' },
    111: { dm: '392', mc: '日本国' },
    112: { dm: '398', mc: '哈萨克斯坦共和国' },
    113: { dm: '400', mc: '约旦哈希姆王国' },
    114: { dm: '404', mc: '肯尼亚共和国' },
    115: { dm: '408', mc: '朝鲜民主主义人民共和国' },
    116: { dm: '410', mc: '大韩民国' },
    117: { dm: '414', mc: '科威特国' },
    118: { dm: '417', mc: '吉尔吉斯共和国' },
    119: { dm: '418', mc: '老挝人民民主共和国' },
    120: { dm: '422', mc: '黎巴嫩共和国' },
    121: { dm: '426', mc: '莱索托王国' },
    122: { dm: '428', mc: '拉脱维亚共和国' },
    123: { dm: '430', mc: '利比里亚共和国' },
    124: { dm: '434', mc: '大阿拉伯利比亚人民社会主义民众国' },
    125: { dm: '438', mc: '列支敦士登公国' },
    126: { dm: '440', mc: '立陶宛共和国' },
    127: { dm: '442', mc: '卢森堡大公国' },
    128: { dm: '010', mc: '南极洲' },
    129: { dm: '450', mc: '马达加斯加共和国' },
    130: { dm: '454', mc: '马拉维共和国' },
    131: { dm: '458', mc: '马来西亚' },
    132: { dm: '462', mc: '马尔代夫共和国' },
    133: { dm: '466', mc: '马里共和国' },
    134: { dm: '470', mc: '马耳他共和国' },
    135: { dm: '474', mc: '马提尼克' },
    136: { dm: '478', mc: '毛里塔尼亚伊斯兰共和国' },
    137: { dm: '480', mc: '毛里求斯共和国' },
    138: { dm: '484', mc: '墨西哥合众国' },
    139: { dm: '492', mc: '摩纳哥公国' },
    140: { dm: '496', mc: '蒙古国' },
    141: { dm: '498', mc: '摩尔多瓦共和国' },
    142: { dm: '499', mc: '黑山' },
    143: { dm: '500', mc: '蒙特塞拉特' },
    144: { dm: '504', mc: '摩洛哥王国' },
    145: { dm: '508', mc: '莫桑比克共和国' },
    146: { dm: '512', mc: '阿曼苏丹国' },
    147: { dm: '516', mc: '纳米比亚共和国' },
    148: { dm: '520', mc: '瑙鲁共和国' },
    149: { dm: '524', mc: '尼泊尔王国' },
    150: { dm: '528', mc: '荷兰王国' },
    151: { dm: '530', mc: '荷属安的列斯' },
    152: { dm: '533', mc: '阿鲁巴' },
    153: { dm: '540', mc: '新喀里多尼亚' },
    154: { dm: '548', mc: '瓦努阿图共和国' },
    155: { dm: '554', mc: '新西兰' },
    156: { dm: '558', mc: '尼加拉瓜共和国' },
    157: { dm: '562', mc: '尼日尔共和国' },
    158: { dm: '566', mc: '尼日利亚联邦共和国' },
    159: { dm: '570', mc: '纽埃' },
    160: { dm: '574', mc: '诺福克岛' },
    161: { dm: '578', mc: '挪威王国' },
    162: { dm: '580', mc: '北马里亚纳自由联邦' },
    163: { dm: '581', mc: '美国本土外小岛屿' },
    164: { dm: '583', mc: '密克罗尼西亚联邦' },
    165: { dm: '584', mc: '马绍尔群岛共和国' },
    166: { dm: '585', mc: '帕劳共和国' },
    167: { dm: '586', mc: '巴基斯坦伊斯兰共和国' },
    168: { dm: '591', mc: '巴拿马共和国' },
    169: { dm: '598', mc: '巴布亚新几内亚独立国' },
    170: { dm: '600', mc: '巴拉圭共和国' },
    171: { dm: '604', mc: '秘鲁共和国' },
    172: { dm: '608', mc: '菲律宾共和国' },
    173: { dm: '612', mc: '皮特凯恩' },
    174: { dm: '616', mc: '波兰共和国' },
    175: { dm: '620', mc: '葡萄牙共和国' },
    176: { dm: '624', mc: '几内亚比绍共和国' },
    177: { dm: '626', mc: '东帝汶' },
    178: { dm: '630', mc: '波多黎各' },
    179: { dm: '634', mc: '卡塔尔国' },
    180: { dm: '638', mc: '留尼汪' },
    181: { dm: '642', mc: '罗马尼亚' },
    182: { dm: '643', mc: '俄罗斯联邦' },
    183: { dm: '646', mc: '卢旺达共和国' },
    184: { dm: '654', mc: '圣赫勒拿' },
    185: { dm: '659', mc: '圣基茨和尼维斯联邦' },
    186: { dm: '660', mc: '安圭拉' },
    187: { dm: '662', mc: '圣卢西亚' },
    188: { dm: '666', mc: '圣皮埃尔和密克隆' },
    189: { dm: '670', mc: '圣文森特和格林纳丁斯' },
    190: { dm: '674', mc: '圣马力诺共和国' },
    191: { dm: '678', mc: '圣多美和普林西比民主共和国' },
    192: { dm: '682', mc: '沙特阿拉伯王国' },
    193: { dm: '686', mc: '塞内加尔共和国' },
    194: { dm: '688', mc: '塞尔维亚' },
    195: { dm: '690', mc: '塞舌尔共和国' },
    196: { dm: '694', mc: '塞拉利昂共和国' },
    197: { dm: '702', mc: '新加坡共和国' },
    198: { dm: '703', mc: '斯洛伐克共和国' },
    199: { dm: '704', mc: '越南社会主义共和国' },
    200: { dm: '705', mc: '斯洛文尼亚共和国' },
    201: { dm: '706', mc: '索马里共和国' },
    202: { dm: '710', mc: '南非共和国' },
    203: { dm: '716', mc: '津巴布韦共和国' },
    204: { dm: '724', mc: '西班牙王国' },
    205: { dm: '728', mc: '南苏丹' },
    206: { dm: '732', mc: '西撒哈拉' },
    207: { dm: '736', mc: '苏丹共和国' },
    208: { dm: '740', mc: '苏里南共和国' },
    209: { dm: '744', mc: '斯瓦尔巴岛和扬马延岛' },
    210: { dm: '748', mc: '斯威士兰王国' },
    211: { dm: '752', mc: '瑞典王国' },
    212: { dm: '756', mc: '瑞士联邦' },
    213: { dm: '760', mc: '阿拉伯叙利亚共和国' },
    214: { dm: '762', mc: '塔吉克斯坦共和国' },
    215: { dm: '764', mc: '泰王国' },
    216: { dm: '768', mc: '多哥共和国' },
    217: { dm: '772', mc: '托克劳' },
    218: { dm: '776', mc: '汤加王国' },
    219: { dm: '780', mc: '特立尼达和多巴哥共和国' },
    220: { dm: '784', mc: '阿拉伯联合酋长国' },
    221: { dm: '788', mc: '突尼斯共和国' },
    222: { dm: '792', mc: '土耳其共和国' },
    223: { dm: '795', mc: '土库曼斯坦' },
    224: { dm: '796', mc: '特克斯和凯科斯群岛' },
    225: { dm: '798', mc: '图瓦卢' },
    226: { dm: '800', mc: '乌干达共和国' },
    227: { dm: '804', mc: '乌克兰' },
    228: { dm: '807', mc: '前南斯拉夫马其顿共和国' },
    229: { dm: '818', mc: '阿拉伯埃及共和国' },
    230: { dm: '826', mc: '大不列颠及北爱尔兰联合王国' },
    231: { dm: '831', mc: '根西岛' },
    232: { dm: '834', mc: '坦桑尼亚联合共和国' },
    233: { dm: '840', mc: '美利坚合众国' },
    234: { dm: '850', mc: '美属维尔京群岛' },
    235: { dm: '854', mc: '布基纳法索' },
    236: { dm: '858', mc: '乌拉圭东岸共和国' },
    237: { dm: '860', mc: '乌兹别克斯坦共和国' },
    238: { dm: '862', mc: '委内瑞拉共和国' },
    239: { dm: '876', mc: '瓦利斯和富图纳' },
    240: { dm: '882', mc: '萨摩亚独立国' },
    241: { dm: '887', mc: '也门共和国' },
    242: { dm: '891', mc: '南斯拉夫联盟共和国' },
    243: { dm: '894', mc: '赞比亚共和国' },
  },
  tkCT: [
    { label: '股息', value: '01' },
    { label: '利息', value: '02' },
    { label: '特许权使用费', value: '03' },
    { label: '财产收益', value: '04' },
  ],
  // 指定扣缴下的条款名称
  tkZdkjCT: [
    { label: '营业利润', value: '05' },
    { label: '国际运输', value: '06' },
  ],
  skcdCT: [
    { label: '居民', value: '0' },
    { label: '非居民', value: '1' },
  ],
  sbsdlx1CT: {
    12: '特许权使用费所得-3',
    '07': '承包工程、提供劳务所得-6',
    '08': '国际运输-7',
    11: '利息所得-2',
    31: '租金—5',
    10: '股息红利所得-1',
    13: '转让财产所得-4',
    32: '担保费所得—8',
  },
  sbsdlxCT: {
    12: '特许权使用费',
    '07': '承包工程、提供劳务所得',
    '08': '国际运输',
    11: '利息所得',
    31: '租金',
    10: '股息红利所得',
    13: '转让财产所得',
    32: '担保费所得',
  },
  kjlxMap: {
    10: '法定源泉扣缴',
    20: '指定扣缴（从事承包工程作业、设计和咨询劳务）',
    21: '指定扣缴（从事其他劳务或劳务以外经营活动）',
    22: '指定扣缴（从事管理服务）',
  },
  ssxdsdlxMap: {
    '01': { dm: '10', mc: '股息' },
    '02': { dm: '11', mc: '利息' },
    '03': { dm: '12', mc: '特许权使用费' },
    '04': { dm: '13', mc: '财产收益' },
    '05': { dm: '07', mc: '营业利润' },
    '06': { dm: '08', mc: '国际运输' },
  },
  xsxddyMap: {
    '01': {
      dm: '2001',
      mc: '我国对外签订的避免双重征税协定及内地对香港和澳门签订的避免双重征税安排税收协定中股息条款',
    },
    '02': {
      dm: '2002',
      mc: '我国对外签订的避免双重征税协定及内地对香港和澳门签订的避免双重征税安排税收协定中利息条款',
    },
    '03': {
      dm: '2003',
      mc: '我国对外签订的避免双重征税协定及内地对香港和澳门签订的避免双重征税安排税收协定中特许权使用费条款',
    },
    '04': {
      dm: '2004',
      mc: '我国对外签订的避免双重征税协定及内地对香港和澳门签订的避免双重征税安排税收协定中财产收益条款',
    },
  },
  sbztCT: [
    // { label: '申报成功', value: '00' },
    // { label: '申报中', value: '01' },
    // { label: '已暂停', value: '02' },
    // { label: '申报失败', value: '03' },
    { label: '未生成申报表', value: '04' },
    { label: '填写附表', value: '05' },
    // { label: '校验异常', value: '06' },
    { label: '可申报', value: '07' },
  ],
  kjlxCT: [
    { label: '源泉扣缴', value: '10' },
    { label: '指定扣缴', value: '20' },
  ],
  zzsslCT: [
    { label: '5%', value: 0.05 },
    { label: '6%', value: 0.06 },
    { label: '9%', value: 0.09 },
    { label: '13%', value: 0.13 },
  ],
  zsxmCT: {
    10101: '增值税',
    10104: '企业所得税',
    10110: '房产税',
    10111: '印花税',
    30217: '文化事业建设费',
  },
  // 税款智能计算-是否享受税收协定
  xsssxdCT: [
    { label: '是', value: 'Y' },
    { label: '否', value: 'N' },
  ],
};
