<template>
  <div class="fb-1">
    <gt-space direction="vertical">
      <gt-space size="24px">
        <t-button @click="toPageByName('dwzfzhbs/znjsq/nsywpd')">智能辅助申报</t-button>
        <t-button @click="toPageByName('dwzfzhbs/znsb/jsjg')">智能计算结果</t-button>
        <t-button @click="toPageByName('dwzfzhbs/znjsq/cyfa')">常用申报方案</t-button>
        <t-button @click="toPageByName('dwzfzhbs/znjsq/skjsq')">税款计算器</t-button>
      </gt-space>
    </gt-space>
  </div>
</template>

<script>
const storeName = 'dwfhtz/dwzfzhbs/form';
export default {
  data() {
    return {
      storeName,
    };
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 渲染页面
    toPageByName(name) {
      this.$router.push({
        name,
        query: this.$route.query,
      });
    },
  },
};
</script>

<style lang="less" scoped>
@namespace: fb-1;
.@{namespace} {
  width: 82.2%;
  height: 100%;
  margin: 24px auto;
  text-align: center;
  background: #fff;
  border-radius: 2px;

  .btn {
    padding-top: 10%;
    padding-bottom: 10%;
  }
}
</style>
