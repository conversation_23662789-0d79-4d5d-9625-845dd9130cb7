<template>
  <div style="margin-top: -20px">
    <div class="plr-24">
      <div class="content-label">
        <div class="decorate"></div>
        <span>享受优惠情况</span>
      </div>
      <div>{{ famxxx.fylx }}</div>
    </div>

    <div class="plr-24 mt-16">
      <div class="content-label">
        <div class="decorate"></div>
        <span>问题与选择情况</span>
      </div>
      <template v-for="(item, index) of famxxx.wtdaxxList">
        <div :key="index">{{ `${index + 1}.${item.wtms}${item.dams}` }}</div>
      </template>
    </div>

    <div class="plr-24 mt-16">
      <div class="content-label">
        <div class="decorate"></div>
        <span>纳税义务判断结果</span>
      </div>
      <template v-if="famxxx.ywxxList && famxxx.ywxxList.length > 1">
        <!-- stripe 斑马线  -->
        <t-table row-key="uuid" :showHeader="false" :columns="columns" :data="famxxx.ywxxList"></t-table>
      </template>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  name: 'famx',
  mixins: [pageMixin, storeMixin],
  props: {
    famxxx: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      sbsdlxCT: {},
      columns: [
        {
          colKey: 'ywmc',
          width: '30%',
          render: (h, { row }) => {
            const { zsxmDm } = row;
            return this.zsxmCT[zsxmDm] ? this.zsxmCT[zsxmDm] : '';
          },
        },
        {
          colKey: 'zsxmmc',
          width: '40%',
          render: (h, { row }) => {
            const { zsxmDm, zspmmc = '', sbsdlxDm = '' } = row;
            let content = '';
            if (zsxmDm === '10104') {
              // 10104 所得税
              content = `所得项目：${this.sbsdlxCT[sbsdlxDm] ? this.sbsdlxCT[sbsdlxDm] : ''}`;
            } else if (zsxmDm === '10111') {
              // 10111 印花税
              content = `印花税项目：${zspmmc}`;
            } else {
              content = `征收品目：${zspmmc}`;
            }
            return content;
          },
        },
        {
          colKey: 'sl',
          width: '30%',
          render(h, { row }) {
            const { zsxmmc } = row;
            let { sl } = row;
            // TODO 税种名称判断
            if (zsxmmc === '企业所得税') {
              if (sl === 0.1) {
                sl = '减按10%';
              } else {
                sl = `${sl * 100}%`;
              }
            } else if (zsxmmc === '印花税') {
              sl = `${sl * 1000}‰`;
            } else {
              sl = `${sl * 100}%`;
            }
            return `税率：${sl}`;
          },
        },
      ],
    };
  },
  created() {
    // 申报所得类型
    this.sbsdlxCT = this.formCT.sbsdlxCT;
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.content-label {
  padding: 17px 0;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);
  .decorate {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 9px;
    background: #4285f4;
    transform: translate(0, 3px);
  }
  span {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
  }
}
</style>
