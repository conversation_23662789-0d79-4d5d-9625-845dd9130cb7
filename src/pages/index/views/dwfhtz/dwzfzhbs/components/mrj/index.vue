<template>
  <div>
    <div style="margin-bottom: 16px">
      <t-button variant="base" theme="primary" @click="addMrj">増行</t-button>
    </div>

    <div class="table_wrap">
      <table class="gt-table--normal gt-table--fixed">
        <colgroup>
          <col style="width: 20%" />
          <col style="width: 40%" />
          <col style="width: 40%" />
        </colgroup>
        <thead>
          <tr>
            <th>买入次数</th>
            <th>买入价</th>
            <th>买入股权比例%</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in mrjData" :key="index">
            <td>{{ index + 1 }}</td>
            <td><gt-input-money type="nonnegative" v-model.number="item.mrj" /></td>
            <td><gt-input-money type="percent" v-model.number="item.mrgqbl" /></td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  name: 'mrj',
  mixins: [pageMixin, storeMixin],
  data() {
    return {
      mrjData: [
        {
          mrj: 0,
          mrgqbl: 0,
        },
      ],
    };
  },
  created() {
    this.stateObj.mrjData = this.mrjData;
  },
  methods: {
    // 増行
    addMrj() {
      this.mrjData.push({
        mrj: 0,
        mrgqbl: 0,
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
