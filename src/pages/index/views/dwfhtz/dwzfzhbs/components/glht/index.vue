<template>
  <div>
    <div class="mt-24 mb-24 plr-16">
      <gt-more-search
        :formOptions="formOptions"
        :collapsed="false"
        :segment="4"
        :showCount="3"
        searchText="查询"
        @on-search="searchRowData"
        @on-reset="resetSearchData"
      />
    </div>
    <t-table
      rowKey="id"
      :columns="columns"
      :data="rowData"
      :selected-row-keys="stateObj.selectedRowKeysGlht"
      @select-change="rehandleSelectChange"
      select-on-row-click
      :pagination="pagination"
      @page-change="onPageChange"
    >
    </t-table>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { cloneDeep } from 'lodash';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

const styleObj = {
  tableHeader: 'color: #333333; font-size: 14px; font-weight: 600',
};
export default {
  name: 'glht',
  mixins: [pageMixin, storeMixin],
  data() {
    return {
      searchData: {
        htbh: '',
        htmc: '',
        sdxm: '',
        cjrqz: '',
      },
      formOptions: [
        {
          label: '合同名称',
          prop: 'htmc',
          element: 't-input',
          initValue: '',
        },
        {
          label: '合同编号',
          prop: 'htbh',
          element: 't-input',
          initValue: '',
        },
        {
          label: '所得项目',
          prop: 'sdxm',
          element: 't-select',
          initValue: '',
          options: [
            { label: '所得项目1', value: '所得项目1' },
            { label: '所得项目2', value: '所得项目2' },
          ],
        },
      ],
      columns: [
        {
          // title: '单选',
          // align: 'center',
          colKey: 'row-select',
          type: 'multiple',
          // 允许单选(Radio)取消行选中
          checkProps: { allowUncheck: false },
          width: 64,
        },
        {
          colKey: 'htmc',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>合同名称</span>,
          width: 240,
          ellipsis: true,
        },
        {
          colKey: 'htbh',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>合同编号</span>,
          width: 200,
          ellipsis: true,
        },
        {
          colKey: 'sdxm',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>所得项目</span>,
          width: 200,
          ellipsis: true,
        },
      ],
      glhtlb: [],
      rowData: [],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
        showJumper: true,
      },
    };
  },

  created() {
    this.setRowData(this.searchData);
  },
  methods: {
    //  根据对象多条件筛选数组
    multiFilter(array, filters) {
      const filterKeys = Object.keys(filters);
      return array.filter((item) => {
        return filterKeys.every((key) => {
          if (!filters[key].length) return true;
          return String(item[key]).includes(filters[key]);
        });
      });
    },
    resetSearchData(data) {
      console.log(data);
      this.setRowData(this.searchData);
    },

    // 根据输入的条件筛选显示符合条件的列表
    searchRowData(data) {
      console.log(data);
      this.setRowData(data);
    },
    // 改变分页页数
    onPageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    // 设置分页参数
    setPagination() {
      //  根据
      this.pagination = {
        current: 1,
        pageSize: 5,
        total: this.rowData.length,
        showJumper: true,
      };
    },
    setRowData(data) {
      let glhtlb = [
        {
          htmc: '合同名称1',
          htbh: '合同编号1',
          sdxm: '所得项目1',
        },
        {
          htmc: '合同名称2',
          htbh: '合同编号2',
          sdxm: '所得项目2',
        },
        {
          htmc: '合同名称3',
          htbh: '合同编号3',
          sdxm: '所得项目3',
        },
        {
          htmc: '合同名称4',
          htbh: '合同编号4',
          sdxm: '所得项目4',
        },
        {
          htmc: '合同名称5',
          htbh: '合同编号5',
          sdxm: '所得项目5',
        },
      ];
      glhtlb = this.multiFilter(cloneDeep(glhtlb), data);
      const rowData = [];
      glhtlb.forEach((item, index) => {
        rowData.push({
          id: item.htbh,
          htmc: item.htmc,
          htbh: item.htbh,
          sdxm: item.sdxm,
          currentObj: item,
        });
      });
      this.rowData = rowData;
      this.setPagination();
    },

    rehandleSelectChange(value, { selectedRowData }) {
      this.stateObj.selectedRowKeysGlht = value;
    },
  },
};
</script>

<style lang="less" scoped></style>
