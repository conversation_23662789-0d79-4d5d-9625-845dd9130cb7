<template>
  <div class="title-wrap">
    <t-row>
      <t-col :span="4">
        <div class="txt one-line-text">
          合同名称：
          <t-tooltip :content="htmcTxt" placement="top" show-arrow>
            <span class="txt">{{ htmcTxt }}</span>
          </t-tooltip>
        </div>
      </t-col>
      <t-col :span="4" class="text-center">
        <div>
          合同编号：
          <span class="txt">{{ htbhTxt }}</span>
        </div>
      </t-col>
      <t-col v-if="showXzhtBtn" :span="4" class="text-right">
        <t-button style="margin-top: -2px" @click="xzhtClick" class="right-btn" theme="primary" variant="outline"
          >选择合同</t-button
        >
      </t-col>
    </t-row>
  </div>
</template>

<script>
export default {
  props: {
    htmcTxt: {
      type: String,
      default: '',
    },
    htbhTxt: {
      type: String,
      default: '',
    },
    showXzhtBtn: {
      type: Boolean,
      default: false,
    },
  },
  computed: {},
  methods: {
    xzhtClick() {
      this.$emit('xzhtClick');
    },
  },
};
</script>

<style lang="less" scoped>
.title-wrap {
  height: 54px;
  margin: 24px 24px 0;
  background: #f9fafd;
  .t-row {
    padding-right: 16px;
    padding-left: 16px;
    font-size: 14px;
    line-height: 54px;
    color: #666;
    .txt {
      font-weight: 550;
      color: #333;
    }
  }
}
</style>
