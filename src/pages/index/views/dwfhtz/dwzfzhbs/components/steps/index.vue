<template>
  <div class="steps-wrap">
    <t-steps v-model="current" :options="steps" readonly class="steps"></t-steps>
  </div>
</template>

<script>
export default {
  props: {
    current: {
      type: String,
      default: 'third',
    },
  },
  data() {
    return {
      steps: [
        { title: '合同及支付信息采集', value: 'first' },
        { title: '对外支付备案', value: 'second' },
        { title: '智能辅助计算', value: 'third' },
        { title: '扣缴申报', value: 'forth' },
      ],
    };
  },
};
</script>

<style lang="less" scoped>
.steps-wrap {
  padding: 27px 0;
  background-color: white;
  border-bottom: 1px solid #eeeeef;
  .steps {
    width: 1000px;
    margin: auto;
  }
}
</style>
