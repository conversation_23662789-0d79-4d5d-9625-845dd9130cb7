<template>
  <div>
    <t-alert
      theme="info"
      message="股权净值是股权转让人投资入股时实际支付的出资成本，或购买该项股权时实际支付的股权受让成本。多次投资或收购的,同项股权被部分转让的，按比例计算确定被转让股权对应的成本。分次支付同一财产转让所得，可在投资成本全部收回后，再计算并扣缴应扣税款。"
    >
    </t-alert>

    <div style="margin-top: 16px; margin-bottom: 16px">
      <t-button variant="base" theme="primary" @click="addGqjj">増行</t-button>
    </div>

    <div>
      <table class="gt-table--normal gt-table--fixed">
        <colgroup>
          <col style="width: 10%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 25%" />
          <col style="width: 25%" />
        </colgroup>
        <thead>
          <tr>
            <th>投资次数</th>
            <th>计价币种</th>
            <th>投资成本</th>
            <th>换算汇率</th>
            <th>投资成本折合人民币金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in gqjjData.tzData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>
              <t-select
                clearable
                filterable
                @change="jsbzChange(index, $event)"
                v-model="item.jsbz"
                :options="bzOption"
              />
            </td>
            <td><gt-input-money type="nonnegative" v-model.number="item.tzcb" @blur="tzcbBlur(index)" /></td>
            <td>
              <gt-input-money
                :disabled="item.jsbz == '156'"
                type="nonnegative"
                v-model.number="item.hshl"
                :digit="5"
                @blur="hshlBlur(index)"
              />
            </td>
            <td><gt-input-money :disabled="true" type="nonnegative" v-model.number="item.tzcbzhrmbje" /></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="gray-card plr-24 mt-16">
      <gt-form labelAlign="top">
        <t-row :gutter="16">
          <t-col :span="6">
            <gt-form-item class="mt-16" label="此次转让前持有股权比例%">
              <gt-input-money type="percent" v-model.number="gqjjData.cczrqcygqbl"
            /></gt-form-item>
          </t-col>
          <t-col :span="6">
            <gt-form-item class="mt-16" label="本次转让股权比例%">
              <gt-input-money type="percent" v-model.number="gqjjData.bzzrgqbl"
            /></gt-form-item>
          </t-col>
        </t-row>
      </gt-form>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { formulaFunction } from '@gt/components';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  name: 'gqjj',
  mixins: [pageMixin, storeMixin],
  data() {
    return {
      bzOption: [],
      gqjjData: {
        cczrqcygqbl: 0,
        bzzrgqbl: 0,
        tzData: [
          {
            jsbz: '',
            tzcb: 0,
            hshl: 0,
            tzcbzhrmbje: 0,
          },
        ],
      },
    };
  },
  created() {
    this.stateObj.gqjjData = this.gqjjData;
    // 初始化币种下拉
    this.bzOption = this.getOptions(this.formCT.bzCT, 2);
  },
  methods: {
    // 増行
    addGqjj() {
      this.gqjjData.tzData.push({
        jsbz: '',
        tzcb: 0,
        hshl: 0,
        tzcbzhrmbje: 0,
      });
    },
    // 币种下拉change事件
    jsbzChange(index, value) {
      const item = this.gqjjData.tzData[index];
      let hshl = 0;
      if (value === '156') {
        hshl = 1;
      }
      item.hshl = hshl;

      this.jsTzcbzhrmbje(index);
    },
    // 投资成本blur事件
    tzcbBlur(index) {
      this.jsTzcbzhrmbje(index);
    },
    // 换算汇率blur事件
    hshlBlur(index) {
      this.jsTzcbzhrmbje(index);
    },
    // 计算投资成本折合人民币金额
    jsTzcbzhrmbje(index) {
      const item = this.gqjjData.tzData[index];
      const { tzcb, hshl } = item;
      const tzcbzhrmbje = formulaFunction.ROUND(tzcb * hshl);
      item.tzcbzhrmbje = tzcbzhrmbje;
    },
  },
};
</script>

<style lang="less" scoped>
.gray-card {
  background: #f9fafd;
}
</style>
