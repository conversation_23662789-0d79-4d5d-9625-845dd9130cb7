<template>
  <div>
    <template v-if="sbqzsxblData.KjskdjList.length > 0">
      <div class="description">
        您未办理扣缴税款登记，请确认如下信息，系统将自动为您完成扣缴税款登记，对应的税（费）种认定。
      </div>

      <div class="table_wrap">
        <gt-form :errorStates="stateObj.sbqzsxblErrors" @blur="onBlur">
          <table class="gt-table--normal gt-table--fixed">
            <colgroup>
              <col style="width: 40%" />
              <col style="width: 30%" />
              <col style="width: 30%" />
            </colgroup>
            <thead>
              <tr>
                <th>代扣代缴、代收代缴税款业务内容</th>
                <th>代扣代缴、代收代缴税种</th>
                <th>征收品目</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in sbqzsxblData.KjskdjList" :key="index">
                <gt-td :name="`KjskdjList[${index}].Dkdjdsdjskywnr`"
                  ><t-input v-model="item.Dkdjdsdjskywnr" @change="nrChnage($event, index)"
                /></gt-td>
                <td>{{ idxZsxmDm2Ywxx[item.ZsxmDm] ? idxZsxmDm2Ywxx[item.ZsxmDm].zsxmmc : '' }}</td>
                <td>
                  {{
                    item.ZsxmDm == '10104'
                      ? '应纳税所得税'
                      : item.ZsxmDm === '30217'
                      ? '广告业文化事业建设费'
                      : idxZsxmDm2Ywxx[item.ZsxmDm]
                      ? idxZsxmDm2Ywxx[item.ZsxmDm].zspmmc
                      : ''
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </gt-form>
      </div>
    </template>

    <template v-if="sbqzsxblData.Wblwhsyjsfjfxxbgbz == 'Y'">
      <div class="description">您未办理文化事业建设费缴费信息报告，请确认以下信息，即可完成信息报告。</div>
      <div class="table_wrap">
        <table class="gt-table--normal gt-table--fixed">
          <colgroup>
            <col style="width: 20%" />
            <col style="width: 30%" />
            <col style="width: 20%" />
            <col style="width: 30%" />
          </colgroup>
          <thead>
            <tr>
              <th>报告人</th>
              <th>征收品目</th>
              <th>缴纳期限</th>
              <th>申报期限</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>扣缴人</td>
              <td>广告业文化事业建设费</td>
              <td>次</td>
              <td>期满之日起15日内</td>
            </tr>
          </tbody>
        </table>
      </div>
    </template>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { cloneDeep } from 'lodash';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  name: 'sbqzsxblqr',
  mixins: [pageMixin, storeMixin],
  props: {
    sbqzsxblData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      idxZsxmDm2Ywxx: {},
    };
  },
  created() {
    this.setData();
  },
  methods: {
    // 初始化数据
    setData() {
      const { ywxxList } = this.nsywpdjg;
      if (ywxxList && ywxxList.length > 0) {
        ywxxList.forEach((item) => {
          const { zsxmDm } = item;
          this.idxZsxmDm2Ywxx[zsxmDm] = item;
        });
      }
    },
    // 表单的onBlur回调函数
    onBlur(path) {
      if (path) {
        const index = path.substring(path.indexOf('[') + 1, path.indexOf(']'));
        const nopass = cloneDeep(this.stateObj.sbqzsxblErrors);
        if (!this.sbqzsxblData.KjskdjList[index].Dkdjdsdjskywnr) {
          nopass[path] = [{ type: 'error', message: '代扣代缴、代收代缴税款业务内容不能为空' }];
        } else {
          delete nopass[path];
        }

        this.stateObj.sbqzsxblErrors = cloneDeep(nopass);
      }
    },
    // 内容变化
    nrChnage(vaule, index) {
      this.stateObj.sbqzsxblData.KjskdjList[index].Dkdjdsdjskywnr = vaule;
    },
  },
};
</script>

<style lang="less" scoped>
.description {
  height: 48px;
  margin-top: 16px;
  font-size: 16px;
  line-height: 48px;
  color: #333;
}
</style>
