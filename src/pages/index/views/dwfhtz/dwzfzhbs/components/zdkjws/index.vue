<template>
  <div>
    <t-table
      rowKey="rowKeyId"
      :columns="columns"
      :data="zdkjwsxx"
      @select-change="handleSelectChange"
      select-on-row-click
      :pagination="pagination"
      @page-change="onPageChange"
    >
    </t-table>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { formulaFunction } from '@gt/components';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  name: 'zdkjws',
  mixins: [pageMixin, storeMixin],
  props: {
    zdkjwsxx: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      columns: [
        {
          colKey: 'row-select',
          type: 'single',
          // 允许单选(Radio)取消行选中
          checkProps: { allowUncheck: false },
          width: '5%',
        },
        {
          colKey: 'wszg',
          width: '20%',
          title: '文书编号',
        },
        {
          colKey: 'zsfs',
          width: '25%',
          title: '征收方式',
        },
        {
          colKey: 'jsyj',
          width: '20%',
          title: '计算依据',
        },
        {
          colKey: 'hdlrl',
          width: '15%',
          title: '核定利润率',
          render(h, { row }) {
            const { hdlrl } = row;
            return hdlrl !== undefined && hdlrl !== '' ? `${formulaFunction.ROUND(hdlrl * 100)}%` : '';
          },
        },
        {
          colKey: 'sl1',
          width: '15%',
          title: '税率',
          render(h, { row }) {
            const { sl1 } = row;
            return sl1 !== undefined && sl1 !== '' ? `${formulaFunction.ROUND(sl1 * 100)}%` : '';
          },
        },
      ],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
        showJumper: true,
      },
    };
  },
  created() {
    this.setPagination();
  },
  methods: {
    // 改变分页页数
    onPageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    // 设置分页参数
    setPagination() {
      //  根据
      this.pagination = {
        current: 1,
        pageSize: 5,
        total: this.zdkjwsxx.length,
        showJumper: true,
      };
    },
    // 选中事件
    handleSelectChange(value, { currentRowData }) {
      if (value.length > 0) {
        this.stateObj.selectedZdkjwsxx = { ...currentRowData };
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content-label {
  padding: 17px 0;

  .decorate {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 9px;
    background: #4285f4;
    transform: translate(0, 3px);
  }
  span {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
  }
}
</style>
