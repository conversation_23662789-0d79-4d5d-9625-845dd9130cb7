<template>
  <t-table rowKey="id" :columns="columns" :data="rowData" :pagination="pagination" @page-change="onPageChange">
    <template #cz="{ rowIndex }">
      <gt-space>
        <t-link hover="color" theme="primary" @click="skjs(rowIndex)">税款计算</t-link>
        <t-link hover="color" theme="primary" @click="jcgl(rowIndex)">解除关联</t-link>
      </gt-space>
    </template>
  </t-table>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { cloneDeep } from 'lodash';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

const styleObj = {
  tableHeader: 'color: #333333; font-size: 14px; font-weight: 600',
};
export default {
  name: 'yglht',
  mixins: [pageMixin, storeMixin],
  data() {
    return {
      columns: [
        {
          colKey: 'htmc',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>合同名称</span>,
          width: 240,
          ellipsis: true,
        },
        {
          colKey: 'htbh',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>合同编号</span>,
          width: 200,
          ellipsis: true,
        },
        {
          colKey: 'sdxm',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>所得项目</span>,
          width: 200,
          ellipsis: true,
        },
        {
          colKey: 'cz',
          title: (h, { colIndex }) => <span style={styleObj.tableHeader}>操作</span>,
          width: 200,
          ellipsis: true,
        },
      ],
      glhtlb: [],
      rowData: [],
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0,
        showJumper: true,
      },
    };
  },

  created() {
    this.setRowData(this.searchData);
  },
  methods: {
    // 根据输入的条件筛选显示符合条件的列表
    searchRowData(data) {
      console.log(data);
      this.setRowData(data);
    },
    // 改变分页页数
    onPageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    // 设置分页参数
    setPagination() {
      //  根据
      this.pagination = {
        current: 1,
        pageSize: 5,
        total: this.rowData.length,
        showJumper: true,
      };
    },
    setRowData(data) {
      const glhtlb = [
        {
          htmc: '合同名称1',
          htbh: '合同编号1',
          sdxm: '所得项目1',
        },
        {
          htmc: '合同名称2',
          htbh: '合同编号2',
          sdxm: '所得项目2',
        },
        {
          htmc: '合同名称3',
          htbh: '合同编号3',
          sdxm: '所得项目3',
        },
        {
          htmc: '合同名称4',
          htbh: '合同编号4',
          sdxm: '所得项目4',
        },
        {
          htmc: '合同名称5',
          htbh: '合同编号5',
          sdxm: '所得项目5',
        },
      ];
      const rowData = [];
      glhtlb.forEach((item, index) => {
        rowData.push({
          id: item.htbh,
          htmc: item.htmc,
          htbh: item.htbh,
          sdxm: item.sdxm,
          currentObj: item,
        });
      });
      this.rowData = rowData;
      this.setPagination();
    },
    skjs(index) {
      console.log('税款计算');
    },
    jcgl(index) {
      console.log('解除关联');
    },
  },
};
</script>

<style lang="less" scoped></style>
