<template>
  <div>
    <div class="plr-24">
      <div class="b-bottom">
        <div class="title1">纳税义务判断方案</div>
        <t-button v-if="showQhBtn" @click="qhlbkp" class="right-btn" theme="primary" variant="outline"
          >切换{{ isLb ? '卡片' : '列表' }}</t-button
        >
      </div>

      <div v-if="isLb" class="mt-16 mb-24">
        <gt-space class="pb-16">
          <t-button @click="toXjfa" theme="primary">新建方案</t-button>
        </gt-space>
        <t-table
          table-layout="fixed"
          row-key="uuid"
          :columns="columns"
          :data="yyfaList"
          :pagination="pagination"
          @page-change="onPageChange"
        >
          <template #fylx="{ rowIndex }">
            {{ yyfaList[rowIndex].fylx }}
            <t-popup placement="top" showArrow>
              <template #content>
                <div class="plr-16 pb-16 pt-16">
                  <div class="popup-title">
                    {{ yyfaList[rowIndex].fylxDm ? idxFylxDm2Fylx[yyfaList[rowIndex].fylxDm].mc : '' }}
                  </div>
                  <div>
                    {{ yyfaList[rowIndex].fylxDm ? idxFylxDm2Fylx[yyfaList[rowIndex].fylxDm].desc : '' }}
                  </div>
                </div>
              </template>
              <gt-icon v-if="yyfaList[rowIndex].fylxDm" icon-name="info-circle1" color="#999" class="info-icon" />
            </t-popup>
          </template>
          <template #pdjg="{ rowIndex }">
            <t-space :size="['8px', '8px']" break-line>
              <t-tag v-for="(item, index) in yyfaList[rowIndex].pdjg" :key="index">{{ item }}</t-tag>
            </t-space>
          </template>
          <template #yxbz="{ rowIndex }">
            <t-tag v-if="yyfaList[rowIndex].yxbz === 'Y'" variant="light-outline" theme="success">未作废</t-tag>
            <t-tag v-else variant="light-outline">已作废</t-tag>
          </template>
          <template #cz="{ rowIndex }">
            <gt-space>
              <t-link hover="color" theme="primary" @click="showDetal(rowIndex)">查看</t-link>
              <t-link v-if="yyfaList[rowIndex].yxbz === 'Y'" hover="color" theme="primary" @click="deleteItem(rowIndex)"
                >删除</t-link
              >
            </gt-space>
          </template>
        </t-table>
      </div>

      <!--方案明细-->
      <gt-dialog
        :visible="visibleObj.famx"
        :header="currentFaxx.famc"
        width="50%"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('famx')"
        @close="closeDialog('famx')"
      >
        <famx :famxxx="currentFaxx"></famx>
      </gt-dialog>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import { Input } from 'tdesign-vue';
import famx from '@/pages/index/views/dwfhtz/dwzfzhbs/components/famx';
import api from '@/pages/index/api/dwzfzhbs';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  mixins: [pageMixin, storeMixin],
  components: { famx },
  data() {
    return {
      // 是否显示切换卡片按钮
      showQhBtn: false,
      // 是否为列表模式
      isLb: true,
      // 已有方案列表
      yyfaList: [],
      // 方案列表索引{key:uuid,value:数组下标}
      idxUuid2Index: {},
      // 费用类型索引{key:fylxDm,value:费用类型对象}
      idxFylxDm2Fylx: {},
      // 列表的列属性定义
      columns: [
        {
          colKey: 'famc',
          title: '方案名称',
          width: '20%',
          cell: 'famc',
          edit: {
            component: Input,
            props: {
              clearable: true,
              autofocus: true,
            },
            abortEditOnEvent: ['onEnter'],
            // 编辑完成，退出编辑态后触发
            onEdited: async (context) => {
              const { newRowData, row } = context;
              const { uuid, famc: newFamc } = newRowData;
              const { famc } = row;

              if (newFamc !== famc) {
                const index = this.idxUuid2Index[uuid];
                this.yyfaList[index].famc = newFamc;
                // 更新合同名称
                await api.updateScheme({ fabh: uuid, famc: newFamc });
              }
            },
            // 校验规则
            rules: [
              { required: true, message: '不能为空' },
              // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
            ],
            // 默认是否为编辑状态
            defaultEditable: false,
            // 校验时机：exit | change
            validateTrigger: 'change',
          },
        },
        {
          colKey: 'fylx',
          title: '费用类型',
          width: '25%',
          cell: 'fylx',
        },
        {
          colKey: 'pdjg',
          title: '判定结果',
          width: '30%',
          cell: 'pdjg',
        },
        {
          colKey: 'yxbz',
          title: '状态',
          width: '10%',
          cell: 'yxbz',
        },
        {
          colKey: 'cz',
          title: '操作',
          width: '15%',
          cell: 'cz',
        },
      ],
      // 当前查看的方案
      currentFaxx: {},
      // 列表的分页属性定义
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showJumper: true,
      },
    };
  },
  computed: {},
  async created() {
    // 请求方案列表、费用类型
    const apiList = [api.getSchemes({ kjsbbm: this.kjsbbm }), api.getZffylxData({})];
    const res = await Promise.all(apiList);

    // 方案列表数据处理
    const schemes = res[0];
    this.setFa(schemes);

    // 费用类型数据处理
    const zffylxData = res[1];
    this.setFylx(zffylxData);
  },
  mounted() {},
  methods: {
    // 改变分页页数
    onPageChange(pageInfo) {
      this.pagination.current = pageInfo.current;
      this.pagination.pageSize = pageInfo.pageSize;
    },
    // 设置总记录数
    setPagination() {
      //  根据
      this.pagination = { ...this.pagination, total: this.yyfaList.length };
    },
    // 设置方法
    setFa(schemes) {
      // 方案数据处理
      schemes.forEach((item, index) => {
        const { uuid, famc, fylx2jmc, fylx2jDm, htbh, yxbz, ywxxList, wtdaxxList } = item;

        const yyfaObject = {};
        yyfaObject.uuid = uuid;
        yyfaObject.famc = famc;
        yyfaObject.fylx = fylx2jmc;
        yyfaObject.fylxDm = fylx2jDm;
        yyfaObject.yxbz = yxbz;
        yyfaObject.htbh = htbh;
        yyfaObject.ywxxList = ywxxList;
        yyfaObject.wtdaxxList = wtdaxxList;
        yyfaObject.pdjg = [];

        ywxxList.forEach((ywxxObj) => {
          let { sl } = ywxxObj;
          const { zsxmDm } = ywxxObj;
          const ywmc = this.zsxmCT[zsxmDm];

          if (zsxmDm === '10104') {
            // 10104 所得税
            if (sl === 0.1) {
              sl = '减按10%';
            } else {
              sl = `${sl * 100}%`;
            }
          } else if (zsxmDm === '10111') {
            // 10111 印花税
            sl = `${sl * 1000}‰`;
          } else {
            sl = `${sl * 100}%`;
          }

          yyfaObject.pdjg.push(`${ywmc}：${sl}`);
        });
        this.yyfaList.push(yyfaObject);
        this.idxUuid2Index[uuid] = index;
      });

      this.setPagination();
    },
    // 设置费用类型
    setFylx(zffylxData) {
      zffylxData.forEach((item) => {
        const obj = {
          sjdm: item.sJZFFYLXBM,
          mc: item.zFFYLXMC,
          dm: item.zFFYLXBM,
          desc: item.zFFYLXDYHFW,
        };
        this.idxFylxDm2Fylx[item.zFFYLXBM] = obj;
      });
    },
    //  切换类别/卡片
    qhlbkp() {
      this.isLb = !this.isLb;
    },
    // 修改方案名称
    resetName(cardIndex) {
      let newName = this.yyfaList[cardIndex].famc;
      const inputChange = (value) => {
        newName = value;
        this.$refs.nameRef.value = value;
      };
      this.$gtDialog.show({
        header: '重命名',
        width: '680px',
        body: () => (
          <t-form>
            <t-form-item label={'方案名称'}>
              <t-input ref="nameRef" value={newName} onchange={inputChange.bind(this)} />
            </t-form-item>
          </t-form>
        ),
        confirmBtn: '确定',
        onConfirm: () => {
          this.yyfaList[cardIndex].famc = newName;
        },
      });
    },
    // 删除方法
    async deleteItem(cardIndex) {
      const faObj = this.yyfaList[cardIndex];
      const { uuid } = faObj;
      // 删除方案
      await api.delScheme({ fabh: uuid });
      // 修改有效标记
      this.yyfaList[cardIndex].yxbz = 'N';
    },
    // “查看”按钮点击事件
    showDetal(cardIndex) {
      this.currentFaxx = this.yyfaList[cardIndex];
      this.visibleObj.famx = true;
    },
    // 新建方法
    toXjfa() {
      this.$router.push({
        name: `dwzfzhbs/znjsq/nsywpd`,
        query: {
          ...this.$route.query,
          fromPage: 'cyfa',
        },
      });
    },
    // 新增计算
    xzjs(cardIndex) {
      this.$router.push({
        name: `dwzfzhbs/znjsq/sfznjs`,
        query: {
          ...this.$route.query,
          fromPage: 'yyfa',
        },
      });
    },
    // 弹框确定事件
    confirmDialog(type) {
      this.visibleObj[type] = false;
    },
    // 弹框关闭事件
    closeDialog(type) {
      this.visibleObj[type] = false;
    },
  },
};
</script>

<style lang="less" scoped>
.b-bottom {
  border-bottom: 1px solid #27282e14;
  .title1 {
    height: 28px;
    margin: 24px 0 17px;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0;
    color: #27282e;
  }
  .right-btn {
    float: right;
    margin-top: -47px;
    clear: both;
  }
}

.gray-card {
  width: 100%;
  height: 360px;
  background: #f9fafd;
  .actions-icon {
    margin-top: -28px;
    font-size: 18px;
    cursor: pointer;
  }
  .card-content {
    height: 160px;
    margin-top: -16px;
    margin-bottom: -32px;
    overflow-y: auto;
  }
  .add-btn-box {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -50%);
    .add-icon-wrap {
      display: inline-block;
      width: 64px;
      height: 64px;
      cursor: pointer;
      background: #e0eaff;
      border-radius: 50%;
      .add-icon {
        margin: 3px;
        font-size: 58px;
        font-weight: bold;
        color: #4285f4;
      }
    }
    .text {
      margin-top: 12px;
      font-size: 20px;
      line-height: 28px;
      color: #999;
    }
  }
}
.popup-title {
  font-size: 14px;
  font-weight: bold;
}
.info-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #ccc;
  vertical-align: middle;
  cursor: pointer;
}
</style>
