<template>
  <div>
    <t-loading :loading="showLoading" showOverlay>
      <Steps v-if="fromPage == 'zfba'" />
      <!-- 合同信息 -->
      <htmcbh
        :htmc-txt="modelData.htmc"
        :htbh-txt="modelData.htbh"
        :showXzhtBtn="modelData.showXzhtBtn"
        @xzhtClick="xzhtClick"
      ></htmcbh>

      <!-- “纳税义务重新判断”、“计算结果列表” 按钮 -->
      <div class="plr-24 pb-24">
        <div class="wrap-header">
          <gt-space>
            <div class="title1">税费智能计算</div>
            <gt-button-group>
              <t-button
                theme="primary"
                variant="outline"
                @click="toPageByName('dwzfzhbs/znjsq/nsywpd', { fromPage: 'sfznjs' })"
                >纳税义务重新判断</t-button
              >
              <t-button theme="primary" variant="outline" @click="toPageByName('dwzfzhbs/znsb/jsjg')"
                >计算结果列表</t-button
              >
            </gt-button-group>
          </gt-space>
        </div>
        <!-- 享受优惠情况 -->
        <div class="gray-card plr-24 mt-16">
          <div class="content-label">
            <div class="decorate"></div>
            <span class="title2">享受优惠情况</span>
          </div>
          <gt-form labelAlign="left" :errorStates="errorStates" @blur="onBlur">
            <!-- 国内优惠政策 -->
            <template>
              <t-row :gutter="16">
                <t-col :span="12">
                  <gt-form-item label-width="100px" :name="`yhDm`">
                    <template #label>
                      <div>国内优惠政策</div>
                    </template>
                    <div style="flex: 1; width: 0">
                      <t-select
                        v-model="modelData.yhDm"
                        :disabled="jmxzOption.length == 0 || isYjs || isLsb"
                        :options="jmxzOption"
                        @change="changeJmxz"
                        multiple
                        clearable
                        filterable
                        placeholder="请选择"
                        style="width: 100%"
                      />
                    </div>
                    <t-button
                      :disabled="jmxzOption.length == 0 || isYjs"
                      theme="primary"
                      variant="text"
                      @click="showCkzc"
                      >查看政策</t-button
                    >
                  </gt-form-item>
                </t-col>
              </t-row>
              <t-row v-show="hasDyns" :gutter="16">
                <t-col :span="4">
                  <gt-form-item label="享受国内优惠减免税额" name="xsgnyhjmse" label-width="160px">
                    <gt-input-money v-model="modelData.xsgnyhjmse" />
                  </gt-form-item>
                </t-col>
              </t-row>
            </template>
            <!-- 税收协定 -->
            <template>
              <t-row :gutter="16">
                <t-col :span="12">
                  <gt-form-item label="享受税收协定" :name="`gjhdqDm`" label-width="100px">
                    <t-radio-group @change="onXsssxdChange" v-model="modelData.xsssxd">
                      <t-radio v-for="(item, index) in formCT.xsssxdCT" :key="index" :value="item.value">{{
                        item.label
                      }}</t-radio>
                    </t-radio-group>
                  </gt-form-item>
                </t-col>
              </t-row>
              <t-row :gutter="16">
                <t-col :span="4">
                  <gt-form-item label="国家/地区" :name="`gjhdqDm`">
                    <t-select
                      :disabled="isYjs || modelData.xsssxd !== 'Y'"
                      v-model="modelData.gjhdqDm"
                      :options="gjhdqOption"
                      @change="changeGjhdq"
                      filterable
                      clearable
                      :placeholder="modelData.xsssxd === 'Y' ? '请选择' : '— —'"
                    ></t-select>
                  </gt-form-item>
                </t-col>
                <t-col :span="4">
                  <gt-form-item label="条款名称" :name="`tkDm`">
                    <t-select
                      :disabled="isYjs || modelData.xsssxd !== 'Y'"
                      v-model="modelData.tkDm"
                      :options="tkOption"
                      @change="changeTk"
                      clearable
                      filterable
                      :placeholder="modelData.xsssxd === 'Y' ? '请选择' : '— —'"
                    ></t-select>
                  </gt-form-item>
                </t-col>
                <t-col :span="4">
                  <gt-form-item label="适用情况" :name="`syqkDm`">
                    <t-select
                      :disabled="isYjs || isZdkj || modelData.xsssxd !== 'Y'"
                      v-model="modelData.syqkDm"
                      :options="syqkOption"
                      :keys="{ label: 'ssxdsyqk', value: 'uuid' }"
                      @change="changeSyqk"
                      clearable
                      filterable
                      :placeholder="isZdkj || modelData.xsssxd !== 'Y' ? '— —' : '请选择'"
                    ></t-select>
                  </gt-form-item>
                </t-col>
              </t-row>
            </template>
          </gt-form>
          <div class="pb-24 description" style="margin-top: -10px">
            {{ modelData.ssxdMsg }}
          </div>
        </div>
        <!-- 支付信息 -->
        <div class="gray-card plr-24 mt-16">
          <div class="content-label">
            <div class="decorate"></div>
            <span class="title2">支付信息</span>
          </div>
          <div class="mt-16">
            <gt-form labelAlign="top" :errorStates="errorStates" @blur="onBlur">
              <t-row :gutter="16">
                <t-col :span="4">
                  <gt-form-item label="币种" :name="`bzdm`">
                    <t-select
                      :disabled="isYjs"
                      v-model="modelData.bzdm"
                      :options="bzOption"
                      @change="changeBz"
                      placeholder="请选择"
                      filterable
                    />
                  </gt-form-item>
                </t-col>
                <t-col :span="4">
                  <gt-form-item label="支付日期" :name="`zfrq`">
                    <t-date-picker
                      :disabled="isYjs"
                      v-model="modelData.zfrq"
                      clearable
                      placeholder=""
                      @change="onZfrqChange"
                    />
                  </gt-form-item>
                </t-col>
                <t-col :span="4">
                  <gt-form-item label="支付金额" :name="`zfje`">
                    <gt-input-money :disabled="isYjs" type="nonnegative" v-model.number="modelData.zfje" />
                  </gt-form-item>
                </t-col>
              </t-row>
            </gt-form>
          </div>
        </div>
        <!-- 税种信息 -->
        <div class="mt-16">
          <gt-form labelAlign="left" :errorStates="errorStates" @blur="onBlur">
            <t-row v-if="modelData.ywxxList.length > 0" :gutter="16">
              <template v-for="(item, index) of modelData.ywxxList">
                <!-- 代扣代缴企业所得税 -->
                <t-col
                  v-if="item.zsxmDm == qysdsZsxmDm"
                  :key="item.zsxmDm"
                  :span="modelData.ywxxList.length === 4 ? 3 : 4"
                >
                  <div class="gray-card mt-16 plr-24 pb-16" :style="{ 'min-height': minHeight }">
                    <div class="content-label">
                      <div class="decorate"></div>
                      <span class="title2">代扣代缴企业所得税</span>
                    </div>
                    <gt-form-item label-width="5rem" :name="`ywxxList[${index}].kjlxDm`">
                      <template #label>
                        <div>扣缴类型：</div>
                      </template>
                      <div>
                        <div>{{ kjlxCT[item.kjlxDm] ? kjlxCT[item.kjlxDm] : '' }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item :name="`ywxxList[${index}].sbsdlxDm`" label-width="7rem">
                      <template #label>
                        <div>申报所得类型：</div>
                      </template>
                      <div>
                        <div>{{ sbsdlxCT[item.sbsdlxDm] ? sbsdlxCT[item.sbsdlxDm] : '' }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item label-width="8rem" :name="`ywxxList[${index}].sl`">
                      <template #label>
                        <div>企业所得税税率：</div>
                      </template>
                      <div v-if="!isZdkj">
                        <div>{{ item.sl == 0.1 ? '减按10%' : `${ROUND(item.sl * 100)}%` }}</div>
                      </div>
                      <div v-else style="display: flex; width: 100%; justify-content: space-between">
                        <div>{{ hasSelectZdkjwsOrEmpty(`${ROUND(item.sl * 100)}%`) }}</div>
                        <!-- 扣缴类型为指定扣缴，则显示选择指定扣缴文书-->
                        <a
                          style="color: #4285f4; text-decoration: none"
                          href="javascript:void(0);"
                          @click="xzzdkjws(index)"
                          >选择指定扣缴文书</a
                        >
                      </div>
                    </gt-form-item>
                    <!-- 扣缴类型为指定扣缴，则显示核定利润率-->
                    <gt-form-item v-if="isZdkj" label-width="6rem" :name="`ywxxList[${index}].hdlrl`">
                      <template #label>
                        <div>核定利润率：</div>
                      </template>
                      <div>
                        <div>{{ hasSelectZdkjwsOrEmpty(`${ROUND(item.hdlrl * 100)}%`) }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item label-width="5rem" :name="`ywxxList[${index}].sfcd`">
                      <template #label>
                        <div>税费承担：</div>
                      </template>
                      <t-radio-group :disabled="isYjs" :options="sfcdOption" v-model="item.sfcd"></t-radio-group>
                    </gt-form-item>
                    <!-- 费用类型选择股权转让，则显示股权净值-->
                    <gt-form-item v-if="fylx2jDm == gqzrDm" label-width="5rem" :name="`ywxxList[${index}].gqjj`">
                      <template #label>
                        <div>股权净值：</div>
                      </template>
                      <gt-input-money
                        :disabled="isYjs || item.checkedHelp"
                        type="nonnegative"
                        v-model.number="item.gqjj"
                        :digit="2"
                        :style="{ width: '120px' }"
                      />
                      <t-checkbox :disabled="isYjs" @change="gqjjHelp($event, index)" style="margin-left: 8px"
                        >帮助</t-checkbox
                      >
                    </gt-form-item>
                    <gt-form-item label-width="3rem" :name="`ywxxList[${index}].hl`">
                      <template #label>
                        <div>汇率：</div>
                      </template>
                      <gt-input-money
                        :disabled="isYjs || modelData.bzdm == rmbDm"
                        type="nonnegative"
                        v-model.number="item.hl"
                        :digit="6"
                        :style="{ width: '120px' }"
                      />
                      <t-tooltip content="实际支付或到期支付之日的人民币汇率中间价">
                        <gt-icon icon-name="info-circle1" color="#999" class="info-icon" />
                      </t-tooltip>
                    </gt-form-item>
                    <gt-form-item v-if="item.zsxmDm == firstZsxmDm && !isYjs" label-width="3rem">
                      <template #label>
                        <div></div>
                      </template>
                      <a style="color: #4285f4; text-decoration: none" href="javascript:void(0);" @click="ckhl"
                        >查看汇率</a
                      >
                    </gt-form-item>
                    <template v-if="isYjs">
                      <gt-form-item label-width="7rem" :name="`ywxxList[${index}].jsje`">
                        <template #label>
                          <div>应纳税所得额：</div>
                        </template>
                        <div>
                          <div>{{ emptyZdkjwsxx(format(item.jsje)) }}</div>
                        </div>
                      </gt-form-item>
                      <gt-form-item v-if="modelData.syqkDm && modelData.xdsl < 0.1" label-width="9rem">
                        <template #label>
                          <div>减免企业所得税额：</div>
                        </template>
                        <div>
                          <div>{{ emptyZdkjwsxx(format(xsxddyJmse)) }}</div>
                        </div>
                      </gt-form-item>
                      <gt-form-item label-width="5rem" :name="`ywxxList[${index}].yjse`">
                        <template #label>
                          <div>应交税额：</div>
                        </template>
                        <div>
                          <div>{{ emptyZdkjwsxx(format(item.yjse)) }}</div>
                        </div>
                      </gt-form-item>
                    </template>
                  </div>
                </t-col>
                <!-- 代扣代缴增值税 -->
                <t-col
                  v-if="item.zsxmDm == zzsZsxmDm"
                  :key="item.zsxmDm"
                  :span="modelData.ywxxList.length === 4 ? 3 : 4"
                >
                  <div class="gray-card mt-16 plr-24 pb-16" :style="{ 'min-height': minHeight }">
                    <div class="content-label">
                      <div class="decorate"></div>
                      <span class="title2">代扣代缴增值税</span>
                    </div>
                    <gt-form-item label-width="5rem" :name="`ywxxList[${index}].zspmDm`">
                      <template #label>
                        <div>征收品目：</div>
                      </template>
                      <div>
                        <div>{{ item.zspmmc }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item :name="`ywxxList[${index}].sl`" label-width="6rem">
                      <template #label>
                        <div>增值税税率：</div>
                      </template>
                      <div>
                        <div>{{ `${ROUND(item.sl * 100)}%` }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item label-width="7rem" :name="`ywxxList[${index}].sssqQ`">
                      <template #label>
                        <div>税款所属期起：</div>
                      </template>
                      <t-date-picker
                        :disable-date="{ after: dayjs().add(0, 'day').format() }"
                        v-model="item.sssqQ"
                        @change="item.sssqZ = item.sssqQ"
                      />
                    </gt-form-item>
                    <gt-form-item label-width="7rem" :name="`ywxxList[${index}].sssqZ`">
                      <template #label>
                        <div>税款所属期止：</div>
                      </template>
                      <t-date-picker disabled v-model="item.sssqZ" />
                    </gt-form-item>
                    <gt-form-item label-width="5rem" :name="`ywxxList[${index}].sfcd`">
                      <template #label>
                        <div>税费承担：</div>
                      </template>
                      <t-radio-group :disabled="isYjs" :options="sfcdOption" v-model="item.sfcd"></t-radio-group>
                    </gt-form-item>
                    <!-- 费用类型选择股权转让，则显示买入价-->
                    <gt-form-item v-if="fylx2jDm == gqzrDm" label-width="4rem" :name="`ywxxList[${index}].mrj`">
                      <template #label>
                        <div>买入价：</div>
                      </template>
                      <gt-input-money
                        :disabled="isYjs || item.checkedHelp"
                        type="nonnegative"
                        v-model.number="item.mrj"
                        :digit="2"
                        :style="{ width: '120px' }"
                      />
                      <t-checkbox :disabled="isYjs" @change="mrjHelp($event, index)" style="margin-left: 8px"
                        >帮助</t-checkbox
                      >
                    </gt-form-item>
                    <gt-form-item label-width="3rem" :name="`ywxxList[${index}].hl`">
                      <template #label>
                        <div>汇率：</div>
                      </template>
                      <gt-input-money
                        :disabled="isYjs || modelData.bzdm == rmbDm"
                        type="nonnegative"
                        v-model.number="item.hl"
                        :digit="6"
                        :style="{ width: '120px' }"
                      />
                      <t-tooltip content="销售额发生当天或当月1日的人民币汇率中间价">
                        <gt-icon icon-name="info-circle1" color="#999" class="info-icon" />
                      </t-tooltip>
                    </gt-form-item>
                    <gt-form-item v-if="item.zsxmDm == firstZsxmDm && !isYjs" label-width="3rem">
                      <template #label>
                        <div></div>
                      </template>
                      <a style="color: #4285f4; text-decoration: none" href="javascript:void(0);" @click="ckhl"
                        >查看汇率</a
                      >
                    </gt-form-item>
                    <template v-if="isYjs">
                      <gt-form-item label-width="5rem" :name="`ywxxList[${index}].jsje`">
                        <template #label>
                          <div>计税依据：</div>
                        </template>
                        <div>
                          <div>{{ format(item.jsje) }}</div>
                        </div>
                      </gt-form-item>
                      <gt-form-item label-width="5rem" :name="`ywxxList[${index}].yjse`">
                        <template #label>
                          <div>应交税额：</div>
                        </template>
                        <div>
                          <div>{{ format(item.yjse) }}</div>
                        </div>
                      </gt-form-item>
                    </template>
                  </div>
                </t-col>
                <!-- 印花税 -->
                <t-col
                  v-if="item.zsxmDm == yhsZsxmDm"
                  :key="item.zsxmDm"
                  :span="modelData.ywxxList.length === 4 ? 3 : 4"
                >
                  <div class="gray-card mt-16 plr-24 pb-16" :style="{ 'min-height': minHeight }">
                    <div class="content-label">
                      <div class="decorate"></div>
                      <span class="title2">印花税</span>
                    </div>
                    <gt-form-item label-width="6rem" :name="`ywxxList[${index}].zspmDm`">
                      <template #label>
                        <div>印花税项目：</div>
                      </template>
                      <div>
                        <div>{{ item.zspmmc }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item :name="`ywxxList[${index}].sl`" label-width="3rem">
                      <template #label>
                        <div>税率：</div>
                      </template>
                      <div>
                        <div>{{ yhsslDisplay(item.sl) }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item>
                      <div class="zxsb-tips">
                        <div>
                          <span
                            >非居民企业纳税人自行申报或由代理人代理申报，请前往“财产和行为税税源采集及合并申报”办理</span
                          >
                        </div>
                      </div>
                    </gt-form-item>
                  </div>
                </t-col>
                <!-- 代扣代缴文化事业建设费 -->
                <t-col
                  v-if="item.zsxmDm == whsyjsfZsxmDm"
                  :key="item.zsxmDm"
                  :span="modelData.ywxxList.length === 4 ? 3 : 4"
                >
                  <div class="gray-card mt-16 plr-24" :style="{ 'min-height': minHeight }">
                    <div class="content-label">
                      <div class="decorate"></div>
                      <span class="title2">代扣代缴文化事业建设费</span>
                    </div>
                    <gt-form-item label-width="10rem" :name="`ywxxList[${index}].sl`">
                      <template #label>
                        <div>文化事业建设费费率：</div>
                      </template>
                      <div>
                        <div>{{ `${ROUND(item.sl * 100)}%` }}</div>
                      </div>
                    </gt-form-item>
                    <template v-if="isYjs">
                      <gt-form-item label-width="5rem" :name="`ywxxList[${index}].jsje`">
                        <template #label>
                          <div>计税金额：</div>
                        </template>
                        <div>
                          <div>{{ format(item.jsje) }}</div>
                        </div>
                      </gt-form-item>
                      <gt-form-item label-width="5rem" :name="`ywxxList[${index}].yjse`">
                        <template #label>
                          <div>应交税额：</div>
                        </template>
                        <div>
                          <div>{{ format(item.yjse) }}</div>
                        </div>
                      </gt-form-item>
                    </template>
                  </div>
                </t-col>
                <!-- 房产税 -->
                <t-col
                  v-if="item.zsxmDm == fcsZsxmDm"
                  :key="item.zsxmDm"
                  :span="modelData.ywxxList.length === 4 ? 3 : 4"
                >
                  <div class="gray-card mt-16 plr-24" :style="{ 'min-height': minHeight }">
                    <div class="content-label">
                      <div class="decorate"></div>
                      <span class="title2">房产税</span>
                    </div>
                    <gt-form-item :name="`ywxxList[${index}].sl`" label-width="6rem">
                      <template #label>
                        <div>房产税税率：</div>
                      </template>
                      <div>
                        <div>依照房产租金收入计算缴纳，税率为{{ `${ROUND(item.sl * 100)}%` }}</div>
                      </div>
                    </gt-form-item>
                    <gt-form-item>
                      <div class="zxsb-tips">
                        <div>
                          <span
                            >由产权所有人、承典人或代管/使用人在房产所在地申报，请前往“财产和行为税税源采集及合并申报”办理</span
                          >
                        </div>
                      </div>
                    </gt-form-item>
                  </div>
                </t-col>
              </template>
            </t-row>
          </gt-form>
        </div>
        <div v-if="!isYjs" class="mt-24">
          <gt-submit-footer :showPreview="false" type="center" reportTxt="计算" @report="jsBtnClick" />
        </div>

        <gt-layout-footerbar v-if="isYjs">
          <gt-submit-footer
            :money="modelData.skhj"
            :title="modelData.skMsg"
            previewTxt="重置"
            reportTxt="按此结果申报"
            @preview="resetSfjs"
            @report="acjgsb"
          />
        </gt-layout-footerbar>
      </div>

      <!--查看政策-->
      <gt-dialog
        :visible="visibleObj.ckcz"
        header="查看政策"
        width="50%"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('ckcz')"
        @close="closeDialog('ckcz')"
      >
        <div v-for="(item, index) in jmxzOption" :key="index">
          <span>{{ item.label }}</span>
          <t-button theme="primary" variant="text" @click="ckzc(item.jmzcwz)">查看</t-button>
        </div>
      </gt-dialog>

      <!--指定扣缴文书-->
      <gt-dialog
        :visible="visibleObj.zdkjws"
        header="选择指定扣缴文书"
        width="50%"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('zdkjws')"
        @close="closeDialog('zdkjws')"
      >
        <zdkjws :zdkjwsxx="zdkjwsxx"></zdkjws>
      </gt-dialog>

      <!--股权净值-->
      <gt-dialog
        :visible="visibleObj.gqjj"
        header="股权净值"
        width="50%"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('gqjj')"
        @close="closeDialog('gqjj')"
      >
        <gqjj></gqjj>
      </gt-dialog>

      <!--买入价-->
      <gt-dialog
        :visible="visibleObj.mrj"
        header="按加权平均计算买入价"
        width="50%"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('mrj')"
        @close="closeDialog('mrj')"
      >
        <mrj></mrj>
      </gt-dialog>

      <!--申报前置事项办理确认-->
      <gt-dialog
        :visible="visibleObj.sbqzsxblqr"
        header="申报前置事项办理确认"
        width="92%"
        confirmBtn="确认办理"
        :closeOnOverlayClick="false"
        @confirm="confirmDialog('sbqzsxblqr')"
        @close="closeDialog('sbqzsxblqr')"
      >
        <t-loading :loading="showSbqzsxblLoading" showOverlay>
          <sbqzsxblqr :sbqzsxblData="sbqzsxblData"></sbqzsxblqr>
        </t-loading>
      </gt-dialog>
    </t-loading>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import htmcbh from '@/pages/index/views/dwfhtz/dwzfzhbs/components/htmcbh';
import zdkjws from '@/pages/index/views/dwfhtz/dwzfzhbs/components/zdkjws';
import gqjj from '@/pages/index/views/dwfhtz/dwzfzhbs/components/gqjj';
import mrj from '@/pages/index/views/dwfhtz/dwzfzhbs/components/mrj';
import sbqzsxblqr from '@/pages/index/views/dwfhtz/dwzfzhbs/components/sbqzsxblqr';
import api from '@/pages/index/api/dwzfzhbs';
import { format, formulaFunction, closeWindow, round, getNamedSession } from '@gt/components';
import { getDate } from '@gt4/common-front';
import { cloneDeep } from 'lodash';
import customFunction from '@/pages/index/components/formulaEngine/customerFunction/kjqysdsbg';
import kqqysdsbgApi from '@/pages/index/api/kjqysdsbg/index';
import Steps from '@/pages/index/views/dwfhtz/dwzfzhbs/components/steps';
import { requireYsVueYwbms } from '@/pages/index/views/sb/common/app-wrap/utils/ysConfig.js';
import { getBsms4Init } from '@/utils';
import dayjs from 'dayjs';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);
// 临时枚举类
const szMcMap = {
  qs: '企业所得税',
  wh: '文化事业建设费',
  zz: '增值税',
};

export default {
  mixins: [pageMixin, storeMixin],
  components: { htmcbh, zdkjws, gqjj, mrj, sbqzsxblqr, Steps },
  data() {
    return {
      // 整个页面转圈圈
      showLoading: true,
      // 申报前置事项办理转圈圈
      showSbqzsxblLoading: false,
      // 从那个页面跳转而来
      fromPage: this.$route.query ? this.$route.query.fromPage : '',
      // 页面使用模型数据
      modelData: {
        // 合同信息
        htmc: '',
        htbh: '',
        showXzhtBtn: false,
        yhDm: [],
        // 享受优惠情况
        xsssxd: 'N',
        gjhdqDm: '',
        tkDm: '',
        syqkDm: '',
        xdsl: 0,
        ssxdmc: '',
        ssxdMsg: '',
        // 付汇信息
        bzdm: '',
        zfrq: '',
        zfje: 0,
        // 税种列表
        ywxxList: [],
        // 税款信息
        skMsg: '税款合计',
        // 税款合计
        skhj: 0,
        // 享受国内优惠减免税额
        xsgnyhjmse: 0,
      },
      // 校验不通过对象
      nopass: {},
      // 校验不通过信息
      errorMsg: {
        tkDm: '国税（地区）不为空时，税收协定待遇条款必填！',
        syqkDm: '请选择税收协定的适用情况',
        bzdm: '币种必填！',
        zfrq: '支付日期必填！',
        zfje: `支付金额必填！`,
        gqjj: '股权净值必填！',
        mrj: '买入价必填！',
        sfcd: '税费承担必填！',
        hl: '汇率必填！',
        hdlrl: '核定利润率必填！',
        xsgnyhjmse: '存在国内税收优惠减免性质时，对应减免税额应大于0！',
        sssqQ: '税款所属期起必填！',
        sssqZ: '税款所属期止必填！',
      },
      // 是否包含企业所得税
      hasQysds: false,
      // 是否包含增值税
      hasZzs: false,
      // 是否包含文化事业建设费
      hasWhsyjsf: false,
      // 是否包含印花税
      hasYhs: false,
      // 是否包含房产税
      hasFcs: false,
      // 索引{key:zsxmDm,value:税种信息}
      idxZsxmDm2Ywxx: {},
      // zsxmDm用,拼接
      zsxmDms: '',
      // 文化事业建设费减征比例
      whsyjsfJzbl: {
        zyjbl: 0,
        jzblcsz: 0,
      },
      // 合同信息
      htxx: {},
      // 非居民企业身份信息
      fjmqysfxxcjVO: {},
      // 指定扣缴文书信息
      zdkjwsxx: [],
      // 优惠信息列表
      yhxxList: [],
      // 索引{key:zsxmDm,value:优惠信息}
      idxZsxmDm2Yhxx: {},
      // 评定结果uuid
      fjmkjbsfypdljbuuid: '',
      // 税种列表，第一条记录的zsxmDm
      firstZsxmDm: '',
      // 减免性质下拉
      jmxzOption: [],
      // 是否包含企业所得税减免
      hasQysdsJm: false,
      // 是否为指定扣缴
      isZdkj: false,
      // 企业所得税减免税额
      qysdsJmse: 0,
      // 是否包含增值税减免
      hasZzsJm: false,
      // 增值税减免税额
      zzsJmse: 0,
      // 是否包含递延纳税
      hasDyns: false,
      // 国家和地区下拉
      gjhdqOption: [],
      // 条款名称下拉
      tkOption: [],
      // 税收协定适用情况map
      ssxdsyqkMap: {},
      // 税收协定名称map
      ssxdmcMap: {},
      // 适用情况下拉
      syqkOption: [],
      // 享受协定待遇减免税额
      xsxddyJmse: 0,
      // 税收协定所得类型map
      ssxdsdlxMap: {},
      // 享受协定待遇map
      xsxddyMap: {},
      // 币种下拉
      bzOption: [],
      // 税费承担选项
      sfcdOption: [],
      // 申报所得类型
      sbsdlxCT: {},
      // 扣缴类型
      kjlxCT: {},
      // 是否已计算
      isYjs: false,
      // 查看汇率url
      ckhlUrl: 'https://www.chinamoney.com.cn/chinese/bkccpr/',
      // 申报前置事项办理数据
      sbqzsxblData: {
        KjskdjList: [],
        Wblwhsyjsfjfxxbgbz: 'N',
      },
      // 企业所得税ysqxxid
      qysdsYsqxxid: '',
      // 企业所得税校验结果
      qysdsJyjg: {},
      // 企业所得税zcbwId
      qysdsZcbwId: '',
      // 增值税snapshotId
      zzsUUid: '',
      // 增值税校验结果
      zzsJyjg: '',
      // 增值税sbxh
      zzsSbxh: '',
      // 增值税pch
      zzsPch: '',
      // 文化事业建设费ysqxxid
      whsyjsfYsqxxid: '',
      // 文化事业建设费校验结果
      whsyjsfJyjg: {},
      // 文化事业建设费zcbwId
      whsyjsfZcbwId: '',
      // 计算结果uuid
      jsjgUuid: '',
      // 最小高度
      minHeight: '483px',
      // 50.股权转让（不含限售股情况）对应二级费用类型代码
      gqzrDm: '023001',
      // 递延纳税对应的减免性质代码
      dynsDm: '1016',
      // 人民币对应的币种代码
      rmbDm: '156',
      // 法定源泉扣缴代码
      fdyqkjDm: '10',
      // 企业所得税征收项目代码
      qysdsZsxmDm: '10104',
      // 增值税征收项目代码
      zzsZsxmDm: '10101',
      // 印花税征收项目代码
      yhsZsxmDm: '10111',
      // 房产税征收项目代码
      fcsZsxmDm: '10110',
      // 文化事业建设费征收项目代码
      whsyjsfZsxmDm: '30217',
      // 记录接口返回报文
      apiRes: {
        // 文化没有税费种认定的状态码
        whsyjsfZdrdCode: '',
        // 文化没有税费种认定且状态码为2时，具体失败原因
        whsyjsfZdrdMsg: '',
        // 企税有税费种认定的状态码
        qysdsYrdCode: '',
        // 企业所得税已认定情况下，当前的纳费期限
        qysdsNsqxMc: '',
        // 企税没有税费种认定的状态码
        qysdsZdrdCode: '',
        // 企税没有税费种认定且状态码为2时，具体失败原因
        qysdsZdrdMsg: '',
      },
      dayjs,
    };
  },
  computed: {
    // 特殊情况1：零申报，默认政策，不展示卡片
    isLsb() {
      const { ejlx, wtxx } = this.nsywpdInfo;
      // 1. 二级费用类型：贷款利息，答题选择CA
      // 2. 二级费用类型：从属于贷款的费用，答题选择ACA
      const idxEjlx2Wtxx = {
        '005002': 'C21_A79',
        '005003': 'A23_C21_A79',
      };
      return idxEjlx2Wtxx[ejlx] === wtxx;
    },
    // 指定扣缴文书时，若未选择文书，带出为空
    hasSelectZdkjwsOrEmpty() {
      return function (item) {
        if (this.isZdkj && JSON.stringify(this.stateObj.selectedZdkjwsxx) === '{}') {
          return '';
        }
        return item;
      };
    },
    // 指定扣缴文书时，无可选文书 或 选择的文书字轨为空时，显示 -- ，计算逻辑有兼容为 0
    emptyZdkjwsxx() {
      return function (item) {
        const { isZdkj, isYjs, zdkjwsxx } = this;
        const { wszg } = this.stateObj.selectedZdkjwsxx;
        if (isYjs && isZdkj && (zdkjwsxx.length === 0 || !wszg)) {
          return '--';
        }
        return item;
      };
    },
    hasValidQysds() {
      // 是否包含有效的企业所说税：如果指定扣缴且无扣缴文书可选时，为无效
      const { isZdkj, zdkjwsxx } = this;
      const { wszg } = this.stateObj.selectedZdkjwsxx;
      const zdkjLimit = isZdkj ? zdkjwsxx.length > 0 && wszg : true;
      return this.hasQysds && zdkjLimit;
    },
    // 印花税展示
    yhsslDisplay() {
      return (slList) => {
        if (Array.isArray(slList)) {
          return slList?.length > 0
            ? [...new Set(slList)].reduce((prev, cur) => {
                return `${prev}${prev.length > 0 ? '或' : ''}${round(cur * 1000)}‰`;
              }, '')
            : '‰';
        }
        return `${round(slList * 1000)}‰`;
      };
    },
  },
  async created() {
    // 获取路由参数
    const { Xthtbh = '', bz = '', zfrq = '', zfje = 0 } = this.$route.query;
    this.modelData.zfrq = zfrq;
    this.modelData.bzdm = bz;
    this.modelData.zfje = zfje;

    const apiList = [];
    // 获取税收协定信息
    apiList.push(api.getTaxTreaty({}, false));
    // 查询文化事业建设费减征比例
    apiList.push(api.cxWhsyjsfJzbl({}, false));
    if (Xthtbh) {
      // 根据xthtbh获取合同信息（合同名称、合同编号）
      apiList.push(api.cxHtxx({ Xthtbh }, false));
    }
    const res = await Promise.all(apiList);
    console.log('res', res);
    // 处理税收协定信息数据
    const taxTreaty = res[0];
    this.setSsxd(taxTreaty);
    // 处理文化事业建设费减征比例
    const { body: whsyjsfJzbl } = res[1];
    console.log('whsyjsfJzbl', whsyjsfJzbl);
    this.whsyjsfJzbl = this.parseApiBody(whsyjsfJzbl, '文化事业建设费减征比例数据');
    console.log('Xthtbh', Xthtbh);

    if (Xthtbh) {
      // 处理合同信息数据
      const { body } = res[2];
      console.log('body', body);
      console.log('body类型:', typeof body);
      
      // 使用统一的body解析函数
      this.htxx = this.parseApiBody(body, '合同信息数据');
      this.modelData.htmc = this.htxx.hthxymc ? this.htxx.hthxymc : '';
      this.modelData.htbh = this.htxx.hth ? this.htxx.hth : '';
      this.modelData.gjhdqDm = this.ssxdsyqkMap[this.htxx.gjhdqszDm] ? this.htxx.gjhdqszDm : '';
      if (!bz) {
        this.modelData.bzdm = this.htxx.htbz ? this.htxx.htbz : '';
      }

      if (this.htxx?.bh) {
        // 查询指定扣缴文书相关信息，带出对应税率等信息
        const zdkjwsxxRes = await api.cxZdkjwsxx({ bh: this.htxx.bh }, false);
        const { bizMsg: zdkjwsxxBizMsg, body: zdkjwsxxBody } = zdkjwsxxRes;
        if (!zdkjwsxxBizMsg) {
          const parsedZdkjwsxxBody = this.parseApiBody(zdkjwsxxBody, '指定扣缴文书信息数据');
          this.zdkjwsxx = (Array.isArray(parsedZdkjwsxxBody) ? parsedZdkjwsxxBody : []).map((item, index) => {
            return {
              ...item,
              rowKeyId: index,
              // hdlrl 计算值，展示时需要 * 100 + %，提交报文时需要 * 100 为原值
              hdlrl: item.hdlrl !== undefined && item.hdlrl !== '' ? this.ROUND(item.hdlrl / 100, 6) : item.hdlrl,
            };
          });
        }
      }

      // 获取非居民身份信息
      if (!(await this.getFjmqysfxxcjVO())) {
        return;
      }
    } else {
      // 如果没有合同编号，确保htxx为空对象，避免后续访问undefined
      this.htxx = {};
    }
console.log('66666');
    // 处理纳税义务评定结果。根据评定结果获取要税种、国内优惠、税收协定、扣缴类型等信息
    this.handleNsywpdjg();
console.log('77777');
    if (this.isZdkj) {
      // 初始化条款名称下拉
      this.tkOption = this.formCT.tkZdkjCT;
      // 已经在前置业务选过指定扣缴文书
      const { wsbh } = this.$route.query;
      const selectedWs = this.zdkjwsxx.find(({ wsbh: val }) => wsbh === val) ?? {};
      if (Object.keys(selectedWs).length > 0) {
        // handleSelectChange 逻辑
        this.stateObj.selectedZdkjwsxx = { ...selectedWs };
        // confirmDialog 逻辑
        const { hdlrl, sl } = selectedWs;
        const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
        // hdlrl 计算值
        hdlrl !== undefined && (qysdsObj.hdlrl = hdlrl);
        sl !== undefined && (qysdsObj.sl = sl);
        // 触发校验
        const { index } = qysdsObj;
        this.onBlur(`ywxxList[${index}].hdlrl`);
      }
    } else {
      // 初始化条款名称下拉
      this.tkOption = this.formCT.tkCT;
      // 初始化适用情况下拉
      if (this.ssxdsyqkMap[this.modelData.gjhdqDm] && this.ssxdsyqkMap[this.modelData.gjhdqDm][this.modelData.tkDm]) {
        this.syqkOption = this.ssxdsyqkMap[this.modelData.gjhdqDm][this.modelData.tkDm];
        if (this.modelData.xsssxd === 'Y' && this.syqkOption.length === 1) {
          const { uuid, ssxdsysl } = this.syqkOption[0];
          this.modelData.syqkDm = uuid;
          this.modelData.xdsl = ssxdsysl;
        }
      }
    }
    // 初始化币种下拉
    this.bzOption = this.getOptions(this.formCT.bzCT, 2);
    // 初始化税费承担
    this.sfcdOption = this.formCT.skcdCT;
    // 税收协定所得类型map
    this.ssxdsdlxMap = this.formCT.ssxdsdlxMap;
    // 享受协定待遇map
    this.xsxddyMap = this.formCT.xsxddyMap;
    // 申报所得类型
    this.sbsdlxCT = this.formCT.sbsdlxCT;
    // 扣缴类型
    this.kjlxCT = this.formCT.kjlxMap;
    // 初始化税收协定名称
    this.modelData.ssxdmc = this.ssxdmcMap[this.modelData.gjhdqDm]
      ? this.ssxdmcMap[this.modelData.gjhdqDm].sSXD_DM
      : '';

    this.showLoading = false;
  },
  mounted() {},
  methods: {
    /**
     * 解析API返回的body数据
     * 如果body是字符串则尝试JSON解析，否则直接返回
     * @param {any} body - API返回的body数据
     * @param {string} errorPrefix - 错误日志前缀
     * @returns {object} 解析后的对象
     */
    parseApiBody(body, errorPrefix = 'API返回数据') {
      if (typeof body === 'string') {
        try {
          return JSON.parse(body);
        } catch (error) {
          console.error(`${errorPrefix}解析失败:`, error);
          return {};
        }
      }
      return body || {};
    },
    // 表单的onBlur回调函数
    onBlur(name) {
      if (name) {
        this.checkValidated(name);
        // 改变国税（地区）需要触发税收协定待遇条款的校验
        if (name === 'gjhdqDm') {
          this.onBlur('tkDm');
        }
        // 改变税收协定待遇条款需要触发税收协定适用情况的校验
        if (name === 'tkDm') {
          this.onBlur('syqkDm');
        }
        // 改变币种需要触发汇率的校验
        if (name === 'bzdm') {
          this.onBlur('hl');
        }
      }
    },
    // 选择合同
    xzhtClick() {},
    // 获取非居民身份信息
    async getFjmqysfxxcjVO() {
      // 使用统一的body解析函数处理htxx
      const htxxObj = this.parseApiBody(this.htxx, '合同信息htxx数据');
      
      // 确保htxx对象存在后再进行解构
      const { fjmdjxh, fjmnsrsbh, gjhdqszDm, lwht } = htxxObj || {};
      if (fjmdjxh) {
        console.log('11111');
        let fjmqysfxxcjVO;
        if (lwht === 'Y') {
          console.log('22222');
          const { body } = await api.cxNsrxxByFjmdjxh(
            {
              xthtbh: this.$route.query.Xthtbh,
              fjmdjxh,
            },
            false,
          );
          fjmqysfxxcjVO = this.getFjmqysfxxcjVOByHx(body);
        } else {
          console.log('333333');
          const {
            body,
          } = await api.cxFjmsfxx(
            {
              fjmnsrsbh,
              fjmdjxh,
              gjhdqszDm,
              xthtbh: this.$route.query.Xthtbh,
            },
            false,
          );
          // 使用统一的body解析函数
          const parsedBody = this.parseApiBody(body, '非居民身份信息数据');
           console.log('parsedBody',parsedBody);
          fjmqysfxxcjVO = parsedBody.fjmqysfxxcjVO;
        }
        console.log('fjmqysfxxcjVO',fjmqysfxxcjVO);
        console.log('444444');
        this.fjmqysfxxcjVO = fjmqysfxxcjVO;
      }
      console.log('5555555');
      return true;
    },
    // 处理纳税义务评定结果
    handleNsywpdjg() {
      console.log('nsywpdjg',this.nsywpdjg);
      const parsedBody = this.parseApiBody(this.nsywpdjg, '纳税义务评定结果');
      const { ywxxList, yhxxList, fjmkjbsfypdljbuuid } = parsedBody;
      console.log('ywxxList',ywxxList);
      this.yhxxList = yhxxList;
      this.fjmkjbsfypdljbuuid = fjmkjbsfypdljbuuid;

      if (ywxxList && ywxxList.length > 0) {
        let zsxmDms = '';
        ywxxList.forEach((item, index) => {
          const { zsxmDm } = item;
          zsxmDms = `${zsxmDms + zsxmDm},`;

          if (index === 0) {
            this.firstZsxmDm = zsxmDm;
          }
          const ywxxObj = { ...item, jsje: 0, yjse: 0, index };
          if (zsxmDm === this.qysdsZsxmDm) {
            // 企业所得税
            ywxxObj.hdlrl = 0; // 核定利润率
            ywxxObj.gqjj = 0; // 股权净值
            ywxxObj.checkedHelp = false; // 是否勾选帮助
            ywxxObj.sfcd = ''; // 税费承担
            ywxxObj.hl = this.modelData.bzdm === this.rmbDm ? 1 : 0; // 汇率
            ywxxObj.sbsr = 0; // 申报收入
            ywxxObj.kce = 0; // 扣除额
            this.hasQysds = true;
            // 税收协定情况
            const { syssxdtklbDm } = item;
            if (syssxdtklbDm) {
              // 初始化条款名称
              this.modelData.tkDm = syssxdtklbDm;
            }
            // 是否为指定扣缴
            if (ywxxObj.kjlxDm !== this.fdyqkjDm) {
              this.isZdkj = true;
            }
            if (this.isLsb) {
              // 特殊情况1：零申报时，默认为非居民，卡片不展示，所以不需要修正 modelData
              ywxxObj.sfcd = '1';
            }
          } else if (zsxmDm === this.zzsZsxmDm) {
            // 增值税
            ywxxObj.mrj = 0; // 买入价
            ywxxObj.checkedHelp = false; // 是否勾选帮助
            ywxxObj.sfcd = ''; // 税费承担
            ywxxObj.hl = this.modelData.bzdm === this.rmbDm ? 1 : 0; // 汇率
            ywxxObj.jsjk = 0; // 计税价款
            ywxxObj.sssqQ = this.modelData.zfrq;
            ywxxObj.sssqZ = this.modelData.zfrq;
            this.hasZzs = true;
          } else if (zsxmDm === this.yhsZsxmDm) {
            // 印花税
            this.hasYhs = true;
          } else if (zsxmDm === this.fcsZsxmDm) {
            // 房产税
            this.hasFcs = true;
          } else if (zsxmDm === this.whsyjsfZsxmDm) {
            // 文化事业建设费
            this.hasWhsyjsf = true;
          }

          this.idxZsxmDm2Ywxx[zsxmDm] = ywxxObj;
          // 特殊情况1：零申报时不展示企业所得税卡片
          if (zsxmDm !== this.qysdsZsxmDm || !this.isLsb) {
            this.modelData.ywxxList.push(ywxxObj);
          }
        });

        if (zsxmDms.length > 1) {
          zsxmDms = zsxmDms.substr(0, zsxmDms.length - 1);
        }
        this.zsxmDms = zsxmDms;

        // 处理税款合计显示信息
        if (this.hasYhs && this.hasFcs) {
          this.modelData.skMsg = '税款合计（不含印花税、房产税）';
        } else if (this.hasYhs) {
          this.modelData.skMsg = '税款合计（不含印花税）';
        } else if (this.hasFcs) {
          this.modelData.skMsg = '税款合计（不含房产税）';
        }

        if (this.fylx2jDm === this.gqzrDm || this.isZdkj) {
          this.minHeight = '450px';
        }
      } else {
        // 用户刷新当前页面会导致业务信息列表丢失，路由回义务判断重新选择
        this.$router.replace({
          name: 'dwzfzhbs/znjsq/nsywpd',
          query: this.$route.query,
        });
        return;
      }

      if (yhxxList && yhxxList.length > 0) {
        yhxxList.forEach((item) => {
          const { zsxmDm, jmxzmc, ssjmxzDm, ssyhxm } = item;

          if (this.idxZsxmDm2Ywxx[zsxmDm]) {
            // 初始化国内优惠下拉， ssjmxzDm 10位用作显示，zsxmDm 做项目关联判断，ssyhxm 做实际国内税收优惠赋值
            this.jmxzOption.push({ ...item, label: `${ssjmxzDm} ${jmxzmc}`, value: `${zsxmDm}|${ssyhxm}` });
            if (this.isLsb) {
              // 特殊情况1：默认享受该政策
              this.modelData.yhDm = [`${zsxmDm}|${ssyhxm}`];
            }
          }
          this.idxZsxmDm2Yhxx[zsxmDm] = item;
        });
      }
    },
    // 设置税收协定
    setSsxd(taxTreaty) {
      // 检查formCT是否已初始化
      if (!this.formCT || !this.formCT.gjhdqCT) {
        console.error('formCT.gjhdqCT未初始化，无法设置税收协定');
        return;
      }
      
      const gjhdqMap = {};
      Object.keys(this.formCT.gjhdqCT).forEach((key) => {
        const item = this.formCT.gjhdqCT[key];
        const { dm, mc } = item;
        gjhdqMap[dm] = { value: dm, label: `${dm}|${mc}`, xh: key * 1 };
      });

      const { ssxdxx, ssxdb } = taxTreaty;
      // 解析 适用情况
      const ssxdsyqkMap = {};
      Object.keys(ssxdxx).forEach((key) => {
        // 根据查询的税收协定信息，能被静态资源匹配的组装成 可选国家/地区下拉
        gjhdqMap[key] && this.gjhdqOption.push(gjhdqMap[key]);

        const ssxdArr = ssxdxx[key];
        !ssxdsyqkMap[key] && (ssxdsyqkMap[key] = {});
        // 根据 国家/地区代码 归集 条款对象
        const ssxdtkMap = ssxdsyqkMap[key];

        // 根据 条款名称 归集为 适用情况数组
        ssxdArr.forEach((item) => {
          const { syssxdtkDm, ssxdsysl, ssxdsyqk, ssxdywlj, uuid } = item;
          const ssxdsyqkObj = { ssxdywlj, ssxdsysl, ssxdsyqk, uuid };
          if (!ssxdtkMap[syssxdtkDm]) {
            ssxdtkMap[syssxdtkDm] = [ssxdsyqkObj];
          } else {
            ssxdtkMap[syssxdtkDm].push(ssxdsyqkObj);
          }
        });
      });
      this.ssxdsyqkMap = ssxdsyqkMap;

      // 税收协定名称（在选国家/地区时需要确认税收协定代码）
      Object.keys(ssxdb).forEach((key) => {
        let arr = ssxdb[key];
        arr = arr.filter((item) => {
          const { xYBZ, yXBZ, sSXDMC = '' } = item;
          return xYBZ === 'Y' && yXBZ === 'Y' && sSXDMC.indexOf('双重征税') > -1;
        });
        // 该国家/地区代码 存在有效 税收协定名称 才归集
        if (arr.length === 1) {
          [this.ssxdmcMap[key]] = arr;
        }
      });

      // 排序国家和地区下拉
      this.gjhdqOption = this.gjhdqOption.sort((item1, item2) => {
        return item1.xh - item2.xh;
      });
    },
    // 纳税义务重新判断、计算结果列表 页面跳转
    toPageByName(name, param) {
      const query = {
        ...this.$route.query,
        ...param,
      };

      this.$router.push({
        name,
        query,
      });
    },
    // 国内税收优惠下拉change事件
    changeJmxz(value) {
      // 免税 直接得出税额为0
      // 递延纳税 本次计算的税额为0，需填报《境外投资者递延缴纳预提所得税报告》
      if (value.length > 0) {
        const idxZsxmDm2JmxzDm = {};
        let hasQysds = false;
        let hasZzs = false;
        value.forEach((item) => {
          const [zsxmDm, jmxzDm] = item.split('|');
          idxZsxmDm2JmxzDm[zsxmDm] = jmxzDm;
          if (zsxmDm === this.qysdsZsxmDm) {
            hasQysds = true;
          } else if (zsxmDm === this.zzsZsxmDm) {
            hasZzs = true;
          }
        });

        this.hasZzsJm = hasZzs;
        this.hasQysdsJm = hasQysds;
        if (hasQysds) {
          const jmxzDm = idxZsxmDm2JmxzDm[this.qysdsZsxmDm];
          this.hasDyns = jmxzDm === this.dynsDm;
        }
      } else {
        this.hasQysdsJm = false;
        this.hasDyns = false;
        this.hasZzsJm = false;
      }
    },
    // 显示查看政策弹框
    showCkzc() {
      // 显示查看政策弹框
      this.visibleObj.ckcz = true;
    },
    // 查看政策
    ckzc(url) {
      // 跳转url
      window.open(url);
    },
    // 国家和地区下拉change事件
    changeGjhdq(value) {
      if (!value) {
        this.modelData.ssxdmc = '';
        this.modelData.syqkDm = '';
        this.modelData.xdsl = 0;
        return;
      }
      this.modelData.ssxdmc = this.ssxdmcMap[value]?.sSXD_DM ?? '';

      if (this.ssxdsyqkMap[value] && this.modelData.tkDm && this.ssxdsyqkMap[value][this.modelData.tkDm]) {
        this.syqkOption = this.ssxdsyqkMap[value][this.modelData.tkDm];
        if (this.syqkOption.length === 1) {
          const { uuid, ssxdsysl } = this.syqkOption[0];
          this.modelData.syqkDm = uuid;
          this.modelData.xdsl = ssxdsysl;
        } else {
          this.modelData.syqkDm = '';
          this.modelData.xdsl = 0;
        }
      } else {
        this.modelData.syqkDm = '';
        this.modelData.xdsl = 0;
      }
    },
    // 条款名称下拉change事件
    changeTk(value) {
      const resetFunc = () => {
        this.modelData.syqkDm = '';
        this.modelData.xdsl = 0;
        this.modelData.ssxdMsg = '';
      };
      if (!value) {
        resetFunc();
        return;
      }
      if (!this.isZdkj) {
        const { [value]: syqkOption = [] } = this.ssxdsyqkMap?.[this.modelData.gjhdqDm];
        this.syqkOption = syqkOption;
        if (this.syqkOption.length === 1) {
          const [{ uuid, ssxdsysl }] = this.syqkOption;
          this.modelData.syqkDm = uuid;
          this.modelData.xdsl = ssxdsysl;
          this.changeSyqk(ssxdsysl);
        } else {
          resetFunc();
        }
      } else {
        // 指定扣缴时默认适用情况
        this.modelData.syqkDm = '';
        this.modelData.xdsl = 0;
        this.modelData.ssxdMsg =
          '需在申报时填写《非居民纳税人享受协定待遇信息报告表》，请确认非居民企业符合享受协定待遇条件，并提醒其留存相关资料备查（详见国家税务总局2019年第35号公告）';
      }
    },
    // 适用情况下拉change事件
    changeSyqk(value) {
      const finded = this.syqkOption.find((item) => item.uuid === value);
      if (finded) {
        this.modelData.xdsl = finded.ssxdsysl;
      }

      // 若选择的“适用情况”对应的税率<10%，本区域下方显示提示信息①；若“适用情况”对应的税率≥10%，则显示提示信息②。
      if (this.modelData.xdsl < 0.1) {
        this.modelData.ssxdMsg =
          '需在申报时填写《非居民纳税人享受协定待遇信息报告表》，请确认非居民企业符合享受协定待遇条件，并提醒其留存相关资料备查（详见国家税务总局2019年第35号公告）';
      } else {
        this.modelData.ssxdMsg = '该协定待遇不低于国内法规定税率，将按照10%税率为您计算企业所得税';
      }

      // 当同时勾选国内税收优惠（除递延纳税外）与协定待遇时，输出结果均为国内法免税，显示提示信息③；
      // 选择递延纳税时，若输出结果为协定不征税，则无需申报递延纳税，显示提示信息④；
      // 选择递延纳税且协定税率<10%时，仍可申报享受协定待遇，即需在扣缴企业所得税报告表中同时填报两张附表，显示提示信息⑤。
      if (this.hasQysdsJm) {
        if (this.hasDyns) {
          if (this.modelData.xdsl === 0) {
            this.modelData.ssxdMsg =
              '您勾选享受的协定待遇为不征企业所得税，无需申报享受再投资递延纳税政策，请在申报时填写《非居民纳税人享受协定待遇信息报告表》，并提醒非居民企业留存相关资料备查（详见国家税务总局2019年第35号公告）';
          } else if (this.modelData.xdsl < 0.1) {
            this.modelData.ssxdMsg =
              '您同时勾选了再投资递延纳税和协定待遇，本次应纳所得税额为0，需在申报时填写《境外投资者递延缴纳预提所得税报告》和《非居民纳税人享受协定待遇信息报告表》，请确认非居民企业符合享受优惠条件，并提醒其留存相关资料备查（详见财税〔2018〕102号、国家税务总局2019年第35号公告）';
          }
        } else {
          this.modelData.ssxdMsg = '您勾选享受的国内税收优惠可免征企业所得税，无需重复享受协定待遇';
        }
      }
    },
    // 币种下拉change事件
    changeBz(value) {
      let hl = 0;
      // 币种选择人民币，需要将汇率赋值为1
      if (value === this.rmbDm) {
        hl = 1;
      }

      if (this.hasZzs) {
        // 增值税
        const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
        zzsObj.hl = hl;
      }

      if (this.hasQysds) {
        // 企业所得税
        const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
        qysdsObj.hl = hl;
      }
    },
    // 选择指定扣缴文书
    xzzdkjws() {
      if (this.zdkjwsxx.length === 0) {
        this.$gtDialog.warning(
          {
            header: '未进行指定扣缴义务人登记',
            body: '未查询到指定扣缴文书，请确认是否需要进行非居民企业扣缴义务人指定',
            onConfirm: async (e) => {
              const { fjmnsrsbh, fjmdjxh, bh } = this.htxx;
              // 调用接口发起非居民扣缴义务人的指定
              try {
                const { body } = await kqqysdsbgApi.scZdkjwsDb({
                  xthtbh: this.$route.query.Xthtbh,
                  fjmqysfm: fjmnsrsbh,
                  fjmdjxh,
                  bh,
                });
                if (body.code === '00') {
                  this.$gtDialog.success({
                    header: '信息推送成功',
                    body: '请等待主管税务机关发放指定扣缴文书，并在查收文书后继续申报。',
                    closeOnOverlayClick: false,
                  });
                } else {
                  this.$message.error(body.message);
                }
              } catch (e) {
                console.log(e);
              }
            },
          },
          {
            type: 'confirm',
          },
        );
        return;
      }
      // 文书编号、文书名称、征收方式、计算依据、核定利润率、税率，其中税率如果不为25%，则带出文书中的“其他税率”
      this.visibleObj.zdkjws = true;
    },
    // 查看汇率
    ckhl() {
      // 跳转url
      window.open(this.ckhlUrl);
    },
    // 股权净值帮助
    gqjjHelp(value, index) {
      if (!value) {
        this.modelData.ywxxList[index].checkedHelp = false;
        return;
      }
      this.modelData.ywxxList[index].checkedHelp = true;
      // 显示股权净值弹框
      this.visibleObj.gqjj = true;
    },
    // 买入价帮助
    mrjHelp(value, index) {
      if (!value) {
        this.modelData.ywxxList[index].checkedHelp = false;
        return;
      }
      this.modelData.ywxxList[index].checkedHelp = true;
      // 显示买入价弹框
      this.visibleObj.mrj = true;
    },
    // 计算按钮
    jsBtnClick() {
      // 1、必填字段校验
      if (this.checkValidated()) {
        // 修改最小高度
        this.minHeight = '480px';
        if (this.fylx2jDm === this.gqzrDm || this.isZdkj) {
          this.minHeight = '510px';
        }
        // 2、修改标记
        this.isYjs = true;
        // 3、税费计算
        this.sfjs();
      }
    },
    // 计算前校验
    checkValidated(name) {
      let path;
      if (name) {
        path = name.substring(name.indexOf('.') + 1);
      }

      let flag = true;
      this.nopass = cloneDeep(this.errorStates);

      // 校验对象
      let checkObj = {};
      if (path) {
        checkObj[path] = path;
      } else {
        checkObj = this.errorMsg;
      }

      const { hasQysds, isLsb, hasDyns } = this;
      const { xsssxd, gjhdqDm, tkDm, bzdm, zfrq, syqkDm, zfje, xsgnyhjmse } = this.modelData;

      // 遍历校验对象，触发具体校验
      // 特殊情况1： && !isLsb 表示该情况下，不需要进行校验
      Object.keys(checkObj).forEach((path) => {
        switch (path) {
          case 'xsgnyhjmse':
            if (hasQysds && hasDyns) {
              if (xsgnyhjmse <= 0) {
                this.nopass.xsgnyhjmse = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[path];
              }
            }
            break;
          case 'tkDm':
            if (hasQysds) {
              // 享受税收协定 且 国税（地区）不为空时，税收协定待遇条款必填
              if (xsssxd === 'Y' && gjhdqDm && !tkDm && !isLsb) {
                this.nopass.tkDm = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[path];
              }
            }
            break;
          case 'syqkDm':
            if (hasQysds && !this.isZdkj) {
              // 享受税收协定 且 当税务协定待遇条款不为空时，税收协定适用情况必填
              if (xsssxd === 'Y' && tkDm && !syqkDm && !isLsb) {
                this.nopass.syqkDm = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[path];
              }
            }
            break;
          case 'bzdm':
            // 币种必填
            if (!bzdm) {
              this.nopass.bzdm = [{ type: 'error', message: this.errorMsg[path] }];
            } else {
              delete this.nopass[path];
            }
            break;
          case 'zfrq':
            // 支付日期必填
            if (!zfrq) {
              this.nopass.zfrq = [{ type: 'error', message: this.errorMsg[path] }];
            } else {
              delete this.nopass[path];
            }
            break;
          case 'zfje':
            // 支付金额必填
            if (zfje === 0) {
              this.nopass.zfje = [{ type: 'error', message: this.errorMsg[path] }];
            } else {
              delete this.nopass[path];
            }
            break;
          case 'gqjj':
            // 股权净值必填
            if (this.ylx2jDm === this.gqzrDm && hasQysds) {
              const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
              const { index, gqjj } = qysdsObj;
              if (gqjj === 0 && !isLsb) {
                this.nopass[`ywxxList[${index}].gqjj`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].gqjj`];
              }
            }
            break;
          case 'mrj':
            // 买入价必填
            if (this.ylx2jDm === this.gqzrDm && this.hasZzs) {
              const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
              const { index, mrj } = zzsObj;
              if (mrj === 0) {
                this.nopass[`ywxxList[${index}].mrj`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].mrj`];
              }
            }
            break;
          case 'sssqQ':
            if (this.hasZzs) {
              const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
              const { index, sssqQ } = zzsObj;
              if (sssqQ === '') {
                this.nopass[`ywxxList[${index}].sssqQ`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].sssqQ`];
              }
            }
            break;
          case 'sssqZ':
            if (this.hasZzs) {
              const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
              const { index, sssqZ } = zzsObj;
              if (sssqZ === '') {
                this.nopass[`ywxxList[${index}].sssqZ`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].sssqZ`];
              }
            }
            break;
          case 'sfcd':
            // 税费承担必填
            if (hasQysds) {
              const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
              const { index, sfcd } = qysdsObj;
              if (sfcd === '' && !isLsb) {
                this.nopass[`ywxxList[${index}].sfcd`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].sfcd`];
              }
            }
            if (this.hasZzs) {
              const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
              const { index, sfcd } = zzsObj;
              if (sfcd === '') {
                this.nopass[`ywxxList[${index}].sfcd`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].sfcd`];
              }
            }
            break;
          case 'hl':
            // 汇率必填
            if (hasQysds) {
              const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
              const { index, hl } = qysdsObj;
              if (hl === 0 && !isLsb) {
                this.nopass[`ywxxList[${index}].hl`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].hl`];
              }
            }
            if (this.hasZzs) {
              const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
              const { index, hl } = zzsObj;
              if (hl === 0) {
                this.nopass[`ywxxList[${index}].hl`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].hl`];
              }
            }
            break;
          case 'hdlrl':
            // 核定利润率必填
            if (hasQysds) {
              const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
              const { index } = qysdsObj;
              // 指定扣缴的情况，并且存在指定扣缴文书信息，则要求必须选择一条指定扣缴文书
              if (this.isZdkj && this.zdkjwsxx.length > 0 && JSON.stringify(this.stateObj.selectedZdkjwsxx) === '{}') {
                this.nopass[`ywxxList[${index}].hdlrl`] = [{ type: 'error', message: this.errorMsg[path] }];
              } else {
                delete this.nopass[`ywxxList[${index}].hdlrl`];
              }
            }
            break;
          default:
        }
      });

      if (JSON.stringify(this.nopass) !== '{}') {
        flag = false;
      }
      this.errorStates = cloneDeep(this.nopass);

      // 业务阻断性校验
      if (flag && name === undefined) {
        flag = this.htxxValidated();
      }
      return flag;
    },
    // 合同的“支付所得类型”与纳税义务判断结果（扣缴类型）一致性校验
    htxxValidated() {
      let flag = true;
      const { qysdssdlxDm, babz } = this.htxx;
      const { isZdkj } = this;
      const { Xthtbh = '' } = this.$route.query;
      if ((qysdssdlxDm === '' || (qysdssdlxDm !== '07' && qysdssdlxDm !== '08')) && isZdkj) {
        flag = false;
        this.$gtDialog.warning(
          {
            header: '提示',
            body: '合同的“支付所得类型”与纳税义务判断结果（扣缴类型）不一致，点击“确定”前往【我要办税->税费申报及缴纳->扣缴企业所得税报告】自行填写申报信息，点击“返回”前往合同信息采集页面修改支付所得类型。',
            cancelBtn: '返回',
            closeOnOverlayClick: false,
            closeOnEscKeydown: false,
            onConfirm: () => {
              const nowDate = getDate.getDate(new Date(), 'YYYY-MM-DD');
              this.$router.replace({
                name: 'kjqysdsbg/tb/yssb',
                query: {
                  Xthtbh,
                  SssqQ: nowDate,
                  SssqZ: nowDate,
                  Kjywlx: '1',
                },
              });
            },
            onClose: () => {
              window.location.href = `${window.location.origin}/xxbg/view/sdsxgxxbg/#/yyzx/kjjy/htxxcj?fromPage=sfznjs&modify=true&babz=${babz}&xthtbh=${Xthtbh}`;
            },
          },
          { type: 'confirm' },
        );
      }
      return flag;
    },
    // 税费计算
    sfjs() {
      // f1、f2数据来源：根据开发表3输出值1“代扣代缴增值税”，若输出“否”则f1=0，若输出“是”则f1=1；输出值4“代扣代缴企业所得税”若输出“否”则f2=0，若输出“是”则f2=1。
      const f1 = this.hasZzs ? 1 : 0;
      const f2 = this.hasQysds ? 1 : 0;
      // f3、f4数据来源：手动选择，若“增值税承担者”或“企业所得税承担者”选择“居民企业”，则f3=1，f4=1，若选择“非居民企业”则为0。
      let f3 = 0;
      if (this.hasZzs) {
        const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
        const { sfcd } = zzsObj;
        f3 = sfcd === '0' ? 1 : 0;
      }
      let f4 = 0;
      if (this.hasQysds) {
        const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
        const { sfcd } = qysdsObj;
        f4 = sfcd === '0' ? 1 : 0;
      }

      if (this.fylx2jDm === this.gqzrDm) {
        // 第50类的情况
        let zzsSl = 0;
        let sdsSl = 0;
        if (this.hasQysds) {
          const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
          sdsSl = qysdsObj.sl;
        }
        if (this.hasZzs) {
          const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
          zzsSl = zzsObj.sl;
        }

        if (this.hasQysds) {
          // （1）企业所得税
          //    转让收入 = 转让总金额*企业所得税汇率
          //    股权净值 = (投资成本1*企业所得税汇率1+投资成本2*企业所得税汇率2+……投资成本i*企业所得税汇率i)*本次转让的股权比例/转让前持有股权比例
          //    应纳税所得额 = (转让收入 - 股权净值) / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率*f2*f4)
          //    应交企业所得税= 应纳税所得额 × 企业所得税税率
          const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
          const { gqjj, hl: sdsHl } = qysdsObj;
          const zrsr = this.modelData.zfje * sdsHl;
          // 应纳税所得额
          const ynssde = (zrsr - gqjj) / (1 + zzsSl * f1 * (1 - f3) - sdsSl * f2 * f4);
          const sbsr = this.modelData.zfje / (1 + zzsSl * f1 * (1 - f3) - sdsSl * f2 * f4);
          const kce = gqjj / (1 + zzsSl * f1 * (1 - f3) - sdsSl * f2 * f4);
          // 应交企业所得税
          let yjqysde = ynssde * sdsSl;
          // 计算税收减免
          if (!this.isZdkj) {
            // 计算享受协定待遇减免税额
            this.jsXsxddyJmse(ynssde);
            // 应纳税所得额=应纳税所得额-享受协定待遇减免税额
            yjqysde -= this.xsxddyJmse;
            // 计算国内税收优惠
            this.jsQysdsJmse(yjqysde);
            // 应纳税所得额=应纳税所得额-国内优惠政策减免税额
            yjqysde -= this.qysdsJmse;
          }
          // 应交企业所得税为负数改成零
          if (yjqysde < 0) {
            yjqysde = 0;
          }
          qysdsObj.sbsr = sbsr;
          qysdsObj.kce = kce;
          qysdsObj.jsje = this.ROUND(ynssde);
          qysdsObj.yjse = yjqysde >= 0 ? this.ROUND(yjqysde) : yjqysde;
          this.modelData.skhj += qysdsObj.yjse;
        }

        if (this.hasZzs) {
          // （2）增值税
          //    卖出价取“转让总额”
          //    买入价 = (买入价1*买入股权比例1+买入价2*买入股权比例2+……买入价i*买入股权比例i)/(入股权比例1+买入股权比例2+……买入股权比例i)
          //    计税价款 = (卖出价 - 买入价) / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率*f2*f4)
          //    应交增值税 = 计税价款 × 增值税汇率 × 增值税税率
          const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
          const { mrj, hl: zzsHl } = zzsObj;
          const mcj = this.modelData.zfje;
          const jsjk = (mcj - mrj) / (1 + zzsSl * f1 * (1 - f3) - sdsSl * f2 * f4);
          // 计算依据
          const zzsJsyj = this.ROUND(jsjk * zzsHl);
          // 应交增值税
          let yjzze = zzsJsyj * zzsSl;
          // 计算税收减免
          if (this.hasZzsJm) {
            this.zzsJmse = yjzze;
          }
          // 应交增值税=应交增值税-税收减免
          yjzze -= this.zzsJmse;

          zzsObj.jsjk = jsjk;
          zzsObj.jsje = zzsJsyj >= 0 ? zzsJsyj : 0;
          zzsObj.yjse = yjzze >= 0 ? this.ROUND(yjzze) : 0;
          this.modelData.skhj += zzsObj.yjse;
        }
      } else {
        // 实际征收税率
        let sjzsSl = 0;
        if (this.hasQysds) {
          const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
          if (!this.isZdkj) {
            // 企业所得税税率：源泉扣缴时，直接取税率
            sjzsSl = qysdsObj.sl;
          } else {
            // 实际征收率：指定扣缴时使用，实际征收率=企业所得税税率×核定利润率，税率和核定利润率从指定扣缴文书中取数
            sjzsSl = qysdsObj.sl * qysdsObj.hdlrl;
          }
        }
        // 增值税税率
        let zzsSl = 0;
        // 增值税汇率
        let zzsHl = 0;
        if (this.hasZzs) {
          const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
          zzsSl = zzsObj.sl;
          zzsHl = zzsObj.hl;
        }

        // 支付金额
        const { zfje } = this.modelData;
        // 计税价款 = 支付金额 / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率(或实际征收率)*f2*f4)
        const jsjk = zfje / (1 + zzsSl * f1 * (1 - f3) - sjzsSl * f2 * f4);

        if (this.hasZzs) {
          // （1）增值税，增值税计税依据 = 计税价款 × 增值税汇率，应交增值税 = 计税价款 × 增值税汇率 × 增值税税率
          const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
          // 计算依据
          const zzsJsyj = this.ROUND(jsjk * zzsHl);
          // 应交增值税
          let yjzze = zzsJsyj * zzsSl;
          // 计算税收减免
          if (this.hasZzsJm) {
            this.zzsJmse = yjzze;
          }
          // 应交增值税=应交增值税-税收减免
          yjzze -= this.zzsJmse;

          zzsObj.jsjk = jsjk;
          zzsObj.jsje = zzsJsyj >= 0 ? zzsJsyj : 0;
          zzsObj.yjse = yjzze >= 0 ? this.ROUND(yjzze) : 0;
          this.modelData.skhj += zzsObj.yjse;
        }

        if (this.hasQysds) {
          // （2）企业所得税，应纳税所得额 = 计税价款 × 企业所得税汇率，应交企业所得税 = 计税价款 × 企业所得税汇率 × 企业所得税税率（或实际征收率）
          const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
          const { hl: sdsHl } = qysdsObj;
          // 应纳税所得额
          const ynssde = jsjk * sdsHl;
          // 应交企业所得税
          let yjqysde = ynssde * sjzsSl;
          // 计算税收减免
          if (!this.isZdkj) {
            // 计算享受协定待遇减免税额
            this.jsXsxddyJmse(ynssde);
            // 应纳税所得额=应纳税所得额-享受协定待遇减免税额
            yjqysde -= this.xsxddyJmse;
            // 计算国内税收优惠
            this.jsQysdsJmse(yjqysde);
            // 应纳税所得额=应纳税所得额-国内优惠政策减免税额
            yjqysde -= this.qysdsJmse;
          }
          // 应交企业所得税为负数改成零
          if (yjqysde < 0) {
            yjqysde = 0;
          }

          qysdsObj.sbsr = this.ROUND(jsjk);
          qysdsObj.jsje = this.ROUND(ynssde);
          qysdsObj.yjse = yjqysde >= 0 ? this.ROUND(yjqysde) : 0;
          this.modelData.skhj += qysdsObj.yjse;
        }

        if (this.hasWhsyjsf) {
          // （5）文化事业建设费，计费依据 = 计税价款×（1+增值税税率）×增值税汇率，应纳文化事业建设费 = 计费依据×文化事业建设费费率
          const whsyjsfObj = this.idxZsxmDm2Ywxx[this.whsyjsfZsxmDm];
          const { sl: whsyjsfSl } = whsyjsfObj;
          const whsyjsfJsyj = jsjk * (1 + zzsSl * 1) * zzsHl;
          let ynWhsyjsf = whsyjsfJsyj * whsyjsfSl;
          // 文化事业建设费应补退税额，增加减征比例规则
          if (this.whsyjsfJzbl.jzblcsz === 0.5) {
            ynWhsyjsf *= this.whsyjsfJzbl.jzblcsz;
          } else {
            ynWhsyjsf =
              ynWhsyjsf * this.whsyjsfJzbl.zyjbl * 0.5 +
              ynWhsyjsf * (1 - this.whsyjsfJzbl.zyjbl) * (1 - this.whsyjsfJzbl.jzblcsz);
          }
          whsyjsfObj.jsje = whsyjsfJsyj > 0 ? this.ROUND(whsyjsfJsyj) : 0;
          whsyjsfObj.yjse = ynWhsyjsf >= 0 ? this.ROUND(ynWhsyjsf) : 0;
          this.modelData.skhj += whsyjsfObj.yjse;
        }
      }

      this.modelData.skhj = this.ROUND(this.modelData.skhj);
    },
    // 计算享受协定待遇减免税额 ynssde === qysdsObj.jsje
    jsXsxddyJmse(ynssde) {
      const { isZdkj, idxZsxmDm2Ywxx, qysdsZsxmDm } = this;
      const { xsssxd, syqkDm, xdsl } = this.modelData;
      if (isZdkj && xsssxd === 'Y') {
        // 指定扣缴，选择享受协定待遇：享受协定待遇减免税额 = 所得金额 × 核定利润率 × 核定税率（25%）
        const { hdlrl = 0 } = idxZsxmDm2Ywxx[qysdsZsxmDm];
        this.xsxddyJmse = this.ROUND(ynssde * hdlrl * 0.25);
      } else if (syqkDm && xdsl < 0.1) {
        // 四、协定税率<10%，“适用税收协定及条款减免税额”计算规则：
        // 适用税收协定及条款减免税额 = 应纳税所得额 × （10% - 适用税率）
        this.xsxddyJmse = this.ROUND(ynssde * (0.1 - xdsl));
      } else {
        this.xsxddyJmse = 0;
      }
    },
    // 计算国内税收优惠政策，即企业所得税减免税额
    jsQysdsJmse(yjqysde) {
      if (this.hasQysdsJm) {
        if (this.hasDyns) {
          this.qysdsJmse = this.modelData.xsgnyhjmse;
        } else {
          this.qysdsJmse = yjqysde;
        }
      }
    },
    // 重置按钮
    resetSfjs() {
      // 修改最小高度
      this.minHeight = '420px';
      if (this.fylx2jDm === this.gqzrDm || this.isZdkj) {
        this.minHeight = '450px';
      }
      this.isYjs = false;
      this.modelData.skhj = 0;
    },
    // 按次结果申报按钮
    async acjgsb() {
      if (!this.hasKjsbSz()) return;
      this.showLoading = true;
      // 1、保存计算结果
      let res = await api.saveJsjgMx(this.scJsjgMx(false), false);
      let { bizCode, body } = res;
      if (bizCode !== '00') {
        return;
      }
      // 使用统一的body解析函数
      const parsedBody = this.parseApiBody(body, '保存计算结果数据');
      this.jsjgUuid = parsedBody.jsjgUuid;

      // 解析返回的ysqxxid
      let { ysqxxid, subYwbm } = parsedBody;
      subYwbm = subYwbm.split('_');
      ysqxxid = ysqxxid.split('_');
      subYwbm.forEach((ywbm, index) => {
        if (ywbm === 'kjqysdsbg') {
          this.qysdsYsqxxid = ysqxxid[index];
        } else if (ywbm === 'whsyjsfdkdjsb') {
          this.whsyjsfYsqxxid = ysqxxid[index];
        }
      });

      // 2、申报前置校验
      res = await api.verifyBegin({ zsxmDms: this.zsxmDms }, false);
      bizCode = res.bizCode;
      if (bizCode === '00') {
        body = res.body;
        // 使用统一的body解析函数
        const parsedVerifyBody = this.parseApiBody(body, '申报前置校验数据');
        const {
          KjskdjList = [],
          Wblwhsyjsfjfxxbgbz,
          whsyjsfZdrdCode,
          whsyjsfZdrdMsg,
          qysdsZdrdCode,
          qysdsZdrdMsg,
          qysdsYrdCode,
          qysdsNsqxMc,
        } = parsedVerifyBody;
        // 记录各种情况，后端不返回的情况默认为 ''
        this.apiRes.qysdsZdrdCode = qysdsZdrdCode;
        this.apiRes.qysdsZdrdMsg = qysdsZdrdMsg;
        this.apiRes.qysdsYrdCode = qysdsYrdCode;
        this.apiRes.qysdsNsqxMc = qysdsNsqxMc;
        this.apiRes.whsyjsfZdrdCode = whsyjsfZdrdCode;
        this.apiRes.whsyjsfZdrdMsg = whsyjsfZdrdMsg;
        if (Wblwhsyjsfjfxxbgbz === 'Y' || KjskdjList.length > 0) {
          // 弹出《申报前置事项办理确认》
          this.sbqzsxblData = body;
          this.stateObj.sbqzsxblData = body;
          this.visibleObj.sbqzsxblqr = true;
        } else {
          await this.afterSbqzsxbl(false);
        }
      }
    },
    // 是否有可扣缴申报的税种
    hasKjsbSz() {
      const { hasValidQysds, hasZzs, hasWhsyjsf } = this;
      if (hasValidQysds || hasZzs || hasWhsyjsf) {
        return true;
      }
      this.$gtDialog.info({
        body: '当前结果没有可扣缴申报的税种，请核查。',
        closeOnOverlayClick: false,
      });
      return false;
    },
    // 生成计算结果明细 sfyzc:是否已暂存
    scJsjgMx(sfyzc) {
      // 计算结果对象
      const jsjgObj = {
        ZnsbJsjgMx: [],
        ZnsbYwlx: '60012',
        Xthtbh: this.$route.query.Xthtbh,
        Nsywjgbh: this.fjmkjbsfypdljbuuid,
        BzDm: this.modelData.bzdm,
        Htbh: this.modelData.htbh,
        Htmc: this.modelData.htmc,
        Zfrq: this.modelData.zfrq,
        Zfje: this.modelData.zfje,
        Skhj: this.modelData.skhj,
        Yddjxh: (this.htxx && this.htxx.fjmdjxh) ? this.htxx.fjmdjxh : '',
        FjmGjhdqDm: this.fjmqysfxxcjVO.gjhdqszDm ? this.fjmqysfxxcjVO.gjhdqszDm : '',
        SbbztDm: sfyzc ? '02' : '04',
      };
      if (this.jsjgUuid) {
        jsjgObj.JsjgUuid = this.jsjgUuid;
      }

      // 获取当前时间
      const nowDate = getDate.getDate(new Date(), 'YYYY-MM-DD');
      // 计税结果明细对象
      const jsjgMxObj = {
        YwlsUuid: '',
        ZsxmDm: '',
        ZspmDm: '',
        Jsyj: 0,
        Sl: 0,
        Yjsf: 0,
        SssqQ: nowDate,
        SssqZ: nowDate,
        Hl: 0,
        SubYwbm: '',
        VerifyInfo: '',
        JmxzDm: '',
        Jmse: 0,
        Sfcd: '',
        SbbztDm: '04',
        EjfylxMc: this.fylx2jMc, // 二级费用类型名称
      };

      const { hasValidQysds, hasZzs, hasWhsyjsf } = this;
      if (hasValidQysds) {
        const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
        const { zsxmDm, sl, hl, jsje, yjse, sfcd, kjlxDm, sbsdlxDm } = qysdsObj;
        const subYwbm = 'kjqysdsbg';

        let jyjg = true;
        if (JSON.stringify(this.qysdsJyjg) !== '{}') {
          jyjg = false;
        }

        let sbbztDm = '04';
        if ((this.hasDyns && !(this.modelData.syqkDm && this.modelData.xdsl === 0)) || this.xsxddyJmse > 0) {
          // 有递延纳税 或 享受协定待遇减免税额大于0，则需要填写附表
          sbbztDm = '05';
        } else if (!jyjg) {
          sbbztDm = '06';
        } else if (this.qysdsZcbwId) {
          sbbztDm = '07';
        }

        const qysdsJsjgMxObj = cloneDeep(jsjgMxObj);
        qysdsJsjgMxObj.YwlsUuid = sfyzc ? this.qysdsYsqxxid : '';
        qysdsJsjgMxObj.ZsxmDm = zsxmDm;
        qysdsJsjgMxObj.Jsyj = jsje;
        qysdsJsjgMxObj.Sl = sl;
        qysdsJsjgMxObj.Yjsf = yjse;
        qysdsJsjgMxObj.Hl = hl;
        qysdsJsjgMxObj.SubYwbm = subYwbm;
        qysdsJsjgMxObj.VerifyInfo = jyjg ? '' : JSON.stringify(this.qysdsJyjg);
        qysdsJsjgMxObj.SbbztDm = sbbztDm;
        qysdsJsjgMxObj.JmxzDm = this.qysdsJmse > 0 ? this.idxZsxmDm2Yhxx[zsxmDm].ssyhxm : '';
        qysdsJsjgMxObj.Jmse = this.ROUND(this.qysdsJmse);
        qysdsJsjgMxObj.Sfcd = sfcd;
        qysdsJsjgMxObj.Kjlx = kjlxDm;
        qysdsJsjgMxObj.Sdlx = sbsdlxDm;
        jsjgObj.ZnsbJsjgMx.push(qysdsJsjgMxObj);
      }

      if (hasZzs) {
        const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
        const { zsxmDm, zspmDm, sl, hl, jsje, yjse, sfcd, sssqQ, sssqZ } = zzsObj;
        const subYwbm = 'dkdjzzs';

        let sbbztDm = '04';
        if (this.zzsJyjg) {
          sbbztDm = '06';
        } else if (this.zzsUUid) {
          sbbztDm = '07';
        }

        const zzsJsjgMxObj = cloneDeep(jsjgMxObj);
        zzsJsjgMxObj.YwlsUuid = sfyzc ? this.zzsUUid : '';
        zzsJsjgMxObj.ZsxmDm = zsxmDm;
        zzsJsjgMxObj.ZspmDm = zspmDm;
        zzsJsjgMxObj.Jsyj = jsje;
        zzsJsjgMxObj.Sl = sl;
        zzsJsjgMxObj.Yjsf = yjse;
        zzsJsjgMxObj.Hl = hl;
        zzsJsjgMxObj.SubYwbm = subYwbm;
        zzsJsjgMxObj.sssqQ = sssqQ;
        zzsJsjgMxObj.sssqZ = sssqZ;
        zzsJsjgMxObj.VerifyInfo = this.zzsJyjg;
        zzsJsjgMxObj.SbbztDm = sbbztDm;
        zzsJsjgMxObj.JmxzDm = this.zzsJmse > 0 ? this.idxZsxmDm2Yhxx[zsxmDm].ssyhxm : '';
        zzsJsjgMxObj.Jmse = this.ROUND(this.zzsJmse);
        zzsJsjgMxObj.Sfcd = sfcd;
        zzsJsjgMxObj.Sdlx = this.htxx.qysdssdlxDm ? this.htxx.qysdssdlxDm : '';
        zzsJsjgMxObj.Sbxh = this.zzsSbxh;
        zzsJsjgMxObj.Pch = this.zzsPch;
        jsjgObj.ZnsbJsjgMx.push(zzsJsjgMxObj);
      }

      if (hasWhsyjsf) {
        const whsyjsfdkdjsbObj = this.idxZsxmDm2Ywxx[this.whsyjsfZsxmDm];
        const { zsxmDm, sl, jsje, yjse } = whsyjsfdkdjsbObj;
        const subYwbm = 'whsyjsfdkdjsb';

        let jyjg = true;
        if (JSON.stringify(this.whsyjsfJyjg) !== '{}') {
          jyjg = false;
        }

        let sbbztDm = '04';
        if (!jyjg) {
          sbbztDm = '06';
        } else if (this.whsyjsfZcbwId) {
          sbbztDm = '07';
        }

        const whsyjsfJsjgMxObj = cloneDeep(jsjgMxObj);
        whsyjsfJsjgMxObj.YwlsUuid = sfyzc ? this.whsyjsfYsqxxid : '';
        whsyjsfJsjgMxObj.ZsxmDm = zsxmDm;
        whsyjsfJsjgMxObj.Jsyj = jsje;
        whsyjsfJsjgMxObj.Sl = sl;
        whsyjsfJsjgMxObj.Yjsf = yjse;
        whsyjsfJsjgMxObj.SubYwbm = subYwbm;
        whsyjsfJsjgMxObj.VerifyInfo = jyjg ? '' : JSON.stringify(this.whsyjsfJyjg);
        whsyjsfJsjgMxObj.SbbztDm = sbbztDm;
        jsjgObj.ZnsbJsjgMx.push(whsyjsfJsjgMxObj);
      }
      return jsjgObj;
    },
    // 申报前置校验通过后回调
    async afterSbqzsxbl(hasSxbl) {
      let res;
      // 若 verifyBegin 时文化进行自动认定时，已经返回了认定失败的原因为2/3，则没必要重复处理
      if (hasSxbl && !['2', '3'].includes(this.apiRes.whsyjsfZdrdCode)) {
        // 自动扣缴登记
        res = await api.zdblKjskdjxx({ ...this.stateObj.sbqzsxblData }, false);
        const { bizCode } = res;
        if (bizCode !== '00') {
          return;
        }

        // 文化事业建设费缴费登记
        await this.whsyjsfjfdj();
      }
      // 生成提交报文
      if (!(await this.sctjbw())) return;
      // 更新计算结果
      res = await api.saveJsjgMx(this.scJsjgMx(true), false);
      const { bizCode, body } = res;
      if (bizCode !== '00') {
        return;
      }
      this.jsjgUuid = body.jsjgUuid;
      this.showLoading = false;
      // 跳转到申报页
      this.toPageByName('dwzfzhbs/znsb', { JsjgUuid: this.jsjgUuid, fromPage: this.fromPage });
    },
    // 文化事业建设费缴费登记
    // TODO 缴费登记校验是查核心分发库、缴费登记是先调中软接口，再由中软接口转调核心的缴费登记接口。核心分发库同步生产库有延迟，因此需要轮询
    async whsyjsfjfdj() {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve) => {
        let times = 5;
        const pjhs = 2000;

        // 轮询方法定义
        const next = async () => {
          times -= 1;
          const res = await api.saveWhsyjsfjfdj({ ...this.stateObj.sbqzsxblData }, false);
          const { bizCode, bizMsg } = res;
          if (bizCode === '00') {
            // 进行下一步
            resolve();
          } else if (times <= 0) {
            // 循环次数已到，提示异常
            this.$gtDialog.error({
              header: '提示',
              body: bizMsg,
              closeOnOverlayClick: false,
              closeOnEscKeydown: false,
            });
          } else {
            // 需要继续轮询接口
            setTimeout(() => {
              next();
            }, pjhs);
          }
        };

        // 方法调用
        await next();
      });
    },
    // 增值税报表校验接口
    // TODO 增值税校验接口会校验税（费）种认定，校验是查核心分发库，核心分发库同步生产库有延迟，因此需要轮询。
    async zzsbbjy() {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve) => {
        let times = 5;
        const pjhs = 2000;

        // 轮询方法定义
        const next = async () => {
          times -= 1;

          let res = {};
          try {
            res = await api.bbjyDkdjzzs(
              {
                JsjgUuid: this.jsjgUuid,
                SubYwbm: 'dkdjzzs',
              },
              false,
            );
          } catch (e) {
            res = e;
          }

          const { bizMsg = '' } = res;
          if (bizMsg.indexOf('征收品目有误') > -1 && times > 0) {
            // 需要继续轮询接口
            setTimeout(() => {
              next();
            }, pjhs);
          } else {
            // 进行下一步
            resolve(res);
          }
        };

        // 方法调用
        await next();
      });
    },
    // 生成提交报文
    async sctjbw() {
      // 获取当前时间
      const nowDate = getDate.getDate(new Date(), 'YYYY-MM-DD');
      // 上个月的时间，返回数组[第一天,最后一天]
      // const lastMonth = getDate.getLastMonthRange();
      // 要素数据对接
      const { jtbsSbuuid } = this.$route.query;
      const jtbsSession = getNamedSession('jtbsSbuuid', jtbsSbuuid);
      // 是否需要调用要素数据进行预填
      const requireYsData = (vueYwbm) => jtbsSession || requireYsVueYwbms.includes(vueYwbm);
      if (this.hasQysds) {
        const qsRdTipsMsg = this.getRdTipsMsg(szMcMap.qs, {
          yrdCode: this.apiRes.qysdsYrdCode,
          wrdCode: this.apiRes.qysdsZdrdCode,
          wrdMsg: this.apiRes.qysdsZdrdMsg,
        });
        if (qsRdTipsMsg !== '') {
          // 自动认定失败
          const { bizCode } = await api.zfJsjg({
            jsjgUuid: this.jsjgUuid,
          });
          if (bizCode === '00') {
            this.$gtDialog.error({
              header: '提示',
              body: qsRdTipsMsg,
              closeOnOverlayClick: false,
              closeOnEscKeydown: false,
              onConfirm: () => {
                closeWindow();
              },
              onClose: () => {
                closeWindow();
              },
            });
          }
          return false;
        }
        const dzbdbmList = 'BDA0610780---BDA0611121---fplrdjnjmqytbxx---jwtzztbxx---';
        const param = {
          SssqQ: nowDate,
          SssqZ: nowDate,
        };
        param.dzbdbmList = dzbdbmList;
        param.xthtbh = this.$route.query.Xthtbh;
        param.reset = 'Y';
        if (this.qysdsYsqxxid) {
          param.ysqxxid = this.qysdsYsqxxid;
        }

        const apiList = [];
        // 1、请求初始化数据、公式、根据识别号查非居民信息
        const bsms = getBsms4Init(param);
        apiList.push(
          api.getKjqysdsbgInitData(
            {
              ...param,
              bsms,
            },
            false,
          ),
        );
        apiList.push(api.getKjqysdsbgFormulas(param, false));
        const kjRequireYsData = requireYsData('kjqysdsbg');
        if (kjRequireYsData) {
          apiList.push(
            api.getKjqysdsbgYsData(
              {
                ...param,
                ywbm: 'kjqysdsbg',
                bsms,
              },
              false,
            ),
          );
        }
        const res = await Promise.all(apiList);
        const { body: formData, ysqxxid, otherParams } = res[0];
        const { body: formulas } = res[1];
        // 企业所得税ysqxxid
        this.qysdsYsqxxid = ysqxxid;
        // 处理要素数据
        if (kjRequireYsData) {
          const { body: ysBody } = res[2];
          otherParams.wbcsh = ysBody;
        }
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, {
          Ysqxxid: ysqxxid,
          otherParams,
          formData,
          formulas,
          dzbdbmList,
        });
        // 补充纳税人信息到formData
        this.setNsrjbxx2Kjqysdsbg();
        // 2、执行公式
        this.customFunction = customFunction;
        this.initFormulasEngine();
        // 3、合并页面上填写的数据到扣缴企业所得税的formData
        this.setData2Kjqysdsbg();
        // 4、执行校验公式
        const types = ['12', '02'];
        window.formulaEngine.applyImportFormulasBytypes(types);
        // 企业所得税校验结果
        const qysdsJyjg = window.formulaEngine.getVerifyResults();
        // 删除info类型的校验
        Object.keys(qysdsJyjg).forEach((jpath) => {
          const errors = qysdsJyjg[jpath];
          errors.forEach((errorItem, index) => {
            if (errorItem.type !== 'error') {
              errors.splice(index, 1);
            }
          });
          if (errors.length === 0) {
            delete qysdsJyjg[jpath];
          }
        });
        // 处理校验结果。不需填写递延缴纳，需要删除递延缴纳相关校验不通过信息
        if (this.hasDyns && !(this.modelData.syqkDm && this.modelData.xdsl === 0)) {
          // 需要填写递延缴纳
        } else {
          // 删除非居民企业递延缴纳预提所得税信息报告表相关校验
          Object.keys(qysdsJyjg).forEach((jpath) => {
            if (jpath.indexOf('ht_.jwtzzdyjnytsdsbg.') > -1) {
              delete qysdsJyjg[jpath];
            }
          });
        }
        this.qysdsJyjg = qysdsJyjg;
        param.Ysqxxid = this.Ysqxxid;
        // 5、元子业务在创新场景下的特性化处理
        // 固定删除递延纳税追加节点，业务上存在境外投资时无法直接提交，都要到元子业务里申报，交由提交前再重新补充节点
        delete this.formData.ht_.kjqysdssbvo.DybgsyqkGrid;
        param.Zcbw = JSON.stringify(this.formData);
        const { reset, ...paramNoReset } = param;
        // 6、调用暂存接口
        const saveRes = await api.saveKjqysdsbgForm(paramNoReset, false);
        const { bizCode, body } = saveRes;
        if (bizCode === '00') {
          const { zcbwId } = body;
          this.qysdsZcbwId = zcbwId;
        }
      }

      if (this.hasWhsyjsf) {
        const whRdTipsMsg = this.getRdTipsMsg(szMcMap.wh, {
          wrdCode: this.apiRes.whsyjsfZdrdCode,
          wrdMsg: this.apiRes.whsyjsfZdrdMsg,
        });
        if (whRdTipsMsg !== '') {
          // 自动认定失败
          const { bizCode } = await api.zfJsjg({
            jsjgUuid: this.jsjgUuid,
          });
          if (bizCode === '00') {
            this.$gtDialog.error({
              header: '提示',
              body: whRdTipsMsg,
              closeOnOverlayClick: false,
              closeOnEscKeydown: false,
              onConfirm: () => {
                closeWindow();
              },
              onClose: () => {
                closeWindow();
              },
            });
          }
          return false;
        }
        const param = {
          SssqQ: nowDate,
          SssqZ: nowDate,
        };
        const dzbdbmList = 'BDA0610333---';
        param.dzbdbmList = dzbdbmList;
        param.reset = 'Y';
        if (this.whsyjsfYsqxxid) {
          param.ysqxxid = this.whsyjsfYsqxxid;
        }

        const apiList = [];
        const bsms = getBsms4Init(param);
        apiList.push(
          api.getWhsyjsfdkdjsbInitData(
            {
              ...param,
              bsms,
            },
            false,
          ),
        );
        apiList.push(api.getWhsyjsfdkdjsbFormulas(param, false));
        const whRequireYsData = requireYsData('whsyjsfdkdjsb');
        if (whRequireYsData) {
          apiList.push(
            api.getWhsyjsfdkdjsbYsData(
              {
                ...param,
                ywbm: 'whsyjsfdkdjsb',
                bsms,
              },
              false,
            ),
          );
        }
        const res = await Promise.all(apiList);
        const { body: formData, ysqxxid, otherParams } = res[0];
        const { body: formulas } = res[1];
        // 文化事业建设费ysqxxid
        this.whsyjsfYsqxxid = ysqxxid;
        // 处理要素数据
        if (whRequireYsData) {
          const { body: ysBody } = res[2];
          otherParams.wbcsh = this.parseApiBody(ysBody, '要素数据');
        }
        
        // 统一处理API返回的body数据
        const parsedFormData = this.parseApiBody(formData, '表单数据');
        const parsedFormulas = this.parseApiBody(formulas, '公式数据');
        // 2、执行公式
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, {
          Ysqxxid: ysqxxid,
          otherParams,
          formData: parsedFormData,
          formulas: parsedFormulas,
          dzbdbmList,
        });
        // 2、执行公式
        this.customFunction = {};
        this.initFormulasEngine();
        // 3、合并页面上填写的数据到代扣代缴文化事业建设费的formData
        this.setData2Whsyjsfdkdjsb();
        // 4、执行校验公式
        const types = ['12', '02'];
        window.formulaEngine.applyImportFormulasBytypes(types);
        // 文化事业建设费校验结果
        const whsyjsfJyjg = window.formulaEngine.getVerifyResults();
        // 删除info类型的校验
        Object.keys(whsyjsfJyjg).forEach((jpath) => {
          const errors = whsyjsfJyjg[jpath];
          errors.forEach((errorItem, index) => {
            if (errorItem.type !== 'error') {
              errors.splice(index, 1);
            }
          });
          if (errors.length === 0) {
            delete whsyjsfJyjg[jpath];
          }
        });
        this.whsyjsfJyjg = whsyjsfJyjg;
        // 5、调用暂存接口
        param.Ysqxxid = this.Ysqxxid;
        param.Zcbw = JSON.stringify(this.formData);
        const { reset, ...paramNoReset } = param;
        const saveRes = await api.saveWhsyjsfdkdjsbForm(paramNoReset, false);
        const { bizCode, body } = saveRes;
        if (bizCode === '00') {
          const { zcbwId } = body;
          this.whsyjsfZcbwId = zcbwId;
        }
      }

      if (this.hasZzs) {
        // 调用增值税暂存接口。zcDkdjzzs接口加了noMessage
        // 增值税已认定情况
        let zzsrdPmPpjg;
        // 当前已认定的征收品目信息
        let zzsrdPmDm;
        let zzsrdPmMc;
        // 增值税自动认定情况
        let returnCode;
        let returnMsg;
        const { jsjgUuid } = this;
        try {
          const saveRes = await api.zcDkdjzzs(
            {
              jsjgUuid,
              subYwbm: 'dkdjzzs',
            },
            false,
          );
          const { bizCode, body } = saveRes;
          if (bizCode === '00') {
            // 使用统一的body解析函数
            const parsedSaveBody = this.parseApiBody(body, '增值税保存数据');
            const {
              snapshotId,
              sbxh,
              pch,
              returnCode: returnCodeRes,
              returnMsg: returnMsgRes,
              zzsrdPmPpjg: zzsrdPmPpjgRes,
              zzsrdPmDm: zzsrdPmDmRes,
              zzsrdPmMc: zzsrdPmMcRes,
            } = parsedSaveBody;
            // 增值税snapshotId
            this.zzsUUid = snapshotId;
            // 增值税sbxh
            this.zzsSbxh = sbxh;
            // 增值税pch
            this.zzsPch = pch;
            zzsrdPmPpjg = zzsrdPmPpjgRes;
            zzsrdPmDm = zzsrdPmDmRes;
            zzsrdPmMc = zzsrdPmMcRes;
            returnCode = returnCodeRes;
            returnMsg = returnMsgRes;
          }
        } catch (e) {
          console.log(e);
        }
        const zzsRdTipsMsg = this.getRdTipsMsg(szMcMap.zz, {
          zzsrdPmPpjg,
          zzsrdPmDm,
          zzsrdPmMc,
          wrdCode: returnCode,
          wrdMsg: returnMsg,
        });
        if (zzsRdTipsMsg !== '') {
          const { bizCode: zfCode } = await api.zfJsjg({
            jsjgUuid,
          });
          if (zfCode === '00') {
            this.$gtDialog.error({
              header: '提示',
              body: zzsRdTipsMsg,
              closeOnOverlayClick: false,
              closeOnEscKeydown: false,
              onConfirm: () => {
                closeWindow();
              },
              onClose: () => {
                closeWindow();
              },
            });
          }
          return false;
        }

        // 调用增值税校验接口。bbjyDkdjzzs接口加了noMessage
        const jyRes = await this.zzsbbjy();
        const { bizMsg = '', message = '', orgMsg = '' } = jyRes;
        if (message || orgMsg) {
          this.zzsJyjg = message || orgMsg;
        } else {
          this.zzsJyjg = bizMsg;
        }
      }
      return true;
    },
    // 合并纳税人基本信息扣缴企业所得税的formData
    setNsrjbxx2Kjqysdsbg() {
      const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
      const { sbsdlxDm } = qysdsObj;

      // 纳税人基本信息（被扣缴义务人） 模块赋值
      const { nsrjbxxForm } = this.formData.ht_.kjqysdssbvo;
      nsrjbxxForm.bkjdjxh = this.htxx.fjmdjxh;
      nsrjbxxForm.bkjnsrsbh = this.htxx.skfnsrsbh;

      if (JSON.stringify(this.fjmqysfxxcjVO) !== '{}') {
        nsrjbxxForm.nsrzqjmgDqMcjdm = this.modelData.gjhdqDm ? this.modelData.gjhdqDm : this.fjmqysfxxcjVO.gjhdqszDm; // 境外成立地代码
        nsrjbxxForm.nsrzqjmgnssbh = this.fjmqysfxxcjVO.fjmnsrzjmgnsrsbh; // 境外成立地纳税人识别号
        nsrjbxxForm.nsrzwmc = this.fjmqysfxxcjVO.fjmqyzwmc; // 境内名称（中文）
        nsrjbxxForm.nsrywmc = this.fjmqysfxxcjVO.fjmqyywmc; // 境内名称（外文）
        nsrjbxxForm.jmgnsrZwmc = this.fjmqysfxxcjVO.fjmqyzwmc; // 在境外成立地法定名称（中文）
        nsrjbxxForm.jmgnsrYwmc = this.fjmqysfxxcjVO.fjmqyywmc; // 在境外成立地法定名称（外文）
        nsrjbxxForm.nsrzqjmgzwdz = this.fjmqysfxxcjVO.fjmqyzqjmgzcdz; // 在境外成立地地址（中文）
        nsrjbxxForm.nsrzqjmgywdz = this.fjmqysfxxcjVO.fjmqyzqjmgzcdz; // 在境外成立地地址（外文）
        nsrjbxxForm.lxr = this.fjmqysfxxcjVO.lxrxm; // 联系人
        nsrjbxxForm.lxdh = this.fjmqysfxxcjVO.lxfs; // 联系方式
        // 同时传加密后的中间节点到ht，便于解密
        const { sftmsb } = this.formData.fq_;
        if (sftmsb === 'Y') {
          nsrjbxxForm.lxdh_encode = this.fjmqysfxxcjVO.lxfs_encode;
          nsrjbxxForm.lxr_encode = this.fjmqysfxxcjVO.lxr_encode;
        }

        let sdlx = '';
        const qysdssdlxDm = sbsdlxDm; // 申报所得类型及代码
        if (!this.isZdkj) {
          const qysdssdlxDmArray = ['10', '11', '12', '13', '21', '31', '32'];
          if (qysdssdlxDmArray.includes(qysdssdlxDm)) {
            sdlx = `${qysdssdlxDm}`;
          } else if (qysdssdlxDm === '07' || qysdssdlxDm === '08') {
            sdlx = '12';
          }
        } else if (qysdssdlxDm === '07' || qysdssdlxDm === '08') {
          sdlx = `${sbsdlxDm}`;
        }
        nsrjbxxForm.sbsdlxjdm = sdlx; // 申报所得类型及代码
        nsrjbxxForm.yzbm = ''; // 邮政编码
      }
    },
    // 合并页面上填写的数据到扣缴企业所得税的formData
    setData2Kjqysdsbg() {
      const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
      const { sbsr, kce, sl, hl, jsje, yjse } = qysdsObj;
      if (!this.isZdkj) {
        const { kjywrjbxxForm } = this.formData.ht_.kjqysdssbvo;
        kjywrjbxxForm.t_kjywlx = '1'; // 扣缴义务类型
        kjywrjbxxForm.kjywlx = '1'; // 扣缴义务类型

        const { fdyqkjqkForm } = this.formData.ht_.kjqysdssbvo;
        // 合同信息赋值
        fdyqkjqkForm.htmc = window.isEmptyObject(this.htxx.hthxymc) ? '' : this.htxx.hthxymc; // 合同名称
        fdyqkjqkForm.htbh = window.isEmptyObject(this.htxx.hth) ? '' : this.htxx.hth; // 合同编号
        fdyqkjqkForm.bh = window.isEmptyObject(this.htxx.bh) ? '' : this.htxx.bh; // 编号
        fdyqkjqkForm.htzxqssj = window.isEmptyObject(this.htxx.htzxqssj) // 合同执行起始时间
          ? ''
          : this.htxx.htzxqssj.replace(/\s/g, '').substring(0, 10);
        fdyqkjqkForm.htzxzzsj = window.isEmptyObject(this.htxx.htzxzzsj) // 合同执行终止时间
          ? ''
          : this.htxx.htzxzzsj.replace(/\s/g, '').substring(0, 10);

        fdyqkjqkForm.sfzfbz1 = this.htxx.sfzfbz; // 税费支付标志
        fdyqkjqkForm.htjewz = this.htxx.htje; // 合同总金额
        fdyqkjqkForm.htbz = this.modelData.bzdm; // 合同币种
        fdyqkjqkForm.xthtbh = window.isEmptyObject(this.htxx.xthtbh) ? '' : this.htxx.xthtbh; // 系统合同编号
        fdyqkjqkForm.kjywfssjBz = '1'; // 扣缴义务发生时间。默认支付
        fdyqkjqkForm.kjywfssjZfrq = this.modelData.zfrq; // 支付日期

        // 法定源泉扣缴情况
        if (this.modelData.bzdm === this.rmbDm) {
          fdyqkjqkForm.bcsbsrwbmc = ''; // 本次申报收入 外币 名称
          fdyqkjqkForm.bcsbsrwbje = 0; // 本次申报收入 外币 金额
          fdyqkjqkForm.bcsbsrwbzsrmbje = 0; // 本次申报收入 折算人民币金额
          fdyqkjqkForm.bcsbsrrmbje = sbsr; // 本次申报收入 人民币金额
          fdyqkjqkForm.bcsbsrrmbjehj = sbsr; // 本次申报收入 收入合计
        } else {
          fdyqkjqkForm.bcsbsrwbmc = this.modelData.bzdm; // 本次申报收入 外币 名称
          fdyqkjqkForm.bcsbsrwbje = sbsr; // 本次申报收入 外币 金额
          fdyqkjqkForm.bcsbsrwbhl = hl; // 本次申报收入 外币 汇率
          fdyqkjqkForm.bcsbsrrmbje = 0; // 本次申报收入 人民币金额
          fdyqkjqkForm.bcsbsrrmbjehj = sbsr * hl; // 本次申报收入 收入合计
        }
        fdyqkjqkForm.kce = kce; // 扣除额
        fdyqkjqkForm.fdyqKjsysl = sl; // 适用税率
        fdyqkjqkForm.ynssde = jsje; // 应纳税所得额
        fdyqkjqkForm.yjnQysdse = this.ROUND(jsje * sl); // 应纳税所得额*税率
        fdyqkjqkForm.syssxdtkJmse = this.xsxddyJmse; // 享受协定待遇 减免税额
        fdyqkjqkForm.syssxdtk = this.xsxddyJmse > 0 ? this.xsxddyMap?.[this.modelData.tkDm]?.dm ?? '' : ''; // 享受协定待遇 项目

        fdyqkjqkForm.gnsfyhxmJmse = this.qysdsJmse; // 享受国内税收优惠减免税额
        if (this.isLsb) {
          fdyqkjqkForm.gnsfyhxm = this.idxZsxmDm2Yhxx[this.qysdsZsxmDm].ssyhxm; // 享受国内税收优惠项目
        } else if (this.qysdsJmse > 0) {
          fdyqkjqkForm.gnsfyhxm = this.idxZsxmDm2Yhxx[this.qysdsZsxmDm]?.ssyhxm ?? ''; // 享受国内税收优惠项目
        } else {
          fdyqkjqkForm.gnsfyhxm = ''; // 享受国内税收优惠 项目
        }
        fdyqkjqkForm.fdyqKjjmqysdse = this.xsxddyJmse + this.qysdsJmse; // 减免企业所得税额
        fdyqkjqkForm.fdyqKjsjyjnqysdse = yjse; // 应交税额
      } else {
        const { kjywrjbxxForm } = this.formData.ht_.kjqysdssbvo;
        kjywrjbxxForm.t_kjywlx = '2'; // 扣缴义务类型
        kjywrjbxxForm.kjywlx = '2'; // 扣缴义务类型

        // 指定扣缴情况
        const { zgswjgzdkjqkForm } = this.formData.ht_.kjqysdssbvo;
        const { wszg, hdlrl, jsyj } = this.stateObj.selectedZdkjwsxx;
        const { hthxymc, hth } = this.htxx;
        zgswjgzdkjqkForm.htmc = hthxymc ?? ''; // 指定扣缴合同名称
        zgswjgzdkjqkForm.htbh = hth ?? ''; // 指定扣缴合同编号
        zgswjgzdkjqkForm.zdkjwsbh = wszg; // 指定扣缴文书编号
        zgswjgzdkjqkForm.zdkjfssjBz = '1'; // 扣缴义务发生时间。默认支付
        zgswjgzdkjqkForm.zdskjsff = jsyj === '应纳税所得额' ? '1' : '2'; // 税款计算方法
        // hdlrl 提交值
        zgswjgzdkjqkForm.swjghdLrl = this.ROUND(hdlrl * 100) || 0; // 核定利润率
        zgswjgzdkjqkForm.zdkjfssjZfrq = this.modelData.zfrq; // 支付日期
        zgswjgzdkjqkForm.zdkjfssjZfje = this.modelData.zfje * hl; // 支付金额
        zgswjgzdkjqkForm.zdkjSjyjnQysdse = yjse; // 本次扣缴税款金额（人民币）
      }

      // 协定待遇表赋值
      const { xddyxx } = this.formData.ht_.kjqysdssbvo.xddyYwbd;
      if (this.xsxddyJmse > 0 || this.isZdkj) {
        xddyxx.ssxdmc = this.modelData.ssxdmc;
        xddyxx.ssxdsdlxDm = this.ssxdsdlxMap?.[this.modelData.tkDm]?.dm ?? '';

        xddyxx.xsxddysdje = jsje;
        xddyxx.xsxddyjmse = this.xsxddyJmse;
      } else {
        xddyxx.xsxddysdje = jsje;
        xddyxx.ssxdmc = '';
        xddyxx.ssxdsdlxDm = '';
        xddyxx.xsxddyjmse = 0;
      }

      // 触发公式执行
      const types = ['11', '01'];
      window.formulaEngine.applyImportFormulasBytypes(types, true);

      const { xsssxd } = this.modelData;
      if (this.isZdkj && xsssxd === 'Y') {
        // 覆盖指定扣缴的协定公式结果，因无法从法定对象取值
        xddyxx.xsxddysdje = jsje;
        xddyxx.xsxddyjmse = this.xsxddyJmse > 0 ? this.xsxddyJmse : 0;
      }
    },
    // 合并页面上填写的数据到代扣代缴文化事业建设费的formData
    setData2Whsyjsfdkdjsb() {
      const whsyjsfdkdjsbObj = this.idxZsxmDm2Ywxx[this.whsyjsfZsxmDm];
      const { jsje } = whsyjsfdkdjsbObj;
      const { dkdjwhsyjsfGridlb } =
        this.formData.ht_.dkdjwhsyjsfsbsbxx.dkdjwhsyjsfsbsbSbb.dkdjwhsyjsfsb.dkdjwhsyjsfGrid;
      dkdjwhsyjsfGridlb[0].bys = jsje; // 计税依据为：支付的广告服务含税价款

      const { nsrxxForm } = this.formData.ht_.dkdjwhsyjsfsbsbxx.dkdjwhsyjsfsbsbSbb.dkdjwhsyjsfsb;
      nsrxxForm.bkjnsrsbh = this.htxx.fjmnsrsbh ? this.htxx.fjmnsrsbh : ''; // 被扣缴纳税人识别号 取合同收款方登记序号对应的纳税人识别号
      nsrxxForm.bkjnsrmc = this.htxx.fjmqyzwmc ? this.htxx.fjmqyzwmc : ''; // 被扣缴纳税人名称 取合同收款方登记序号对应的纳税人名称
      // 触发公式执行
      window.formulaEngine.apply(
        'ht_.dkdjwhsyjsfsbsbxx.dkdjwhsyjsfsbsbSbb.dkdjwhsyjsfsb.dkdjwhsyjsfGrid.dkdjwhsyjsfGridlb[0].bys',
      );
    },
    // 弹框确认事件
    async confirmDialog(type) {
      if (type === 'zdkjws') {
        if (this.zdkjwsxx.length > 0) {
          if (JSON.stringify(this.stateObj.selectedZdkjwsxx) === '{}') {
            this.$gtDialog.info({
              header: '提示',
              body: '未选择指定扣缴文书，请选择一条指定扣缴文书！',
            });
            return;
          }
          const { hdlrl, sl1 } = this.stateObj.selectedZdkjwsxx;
          const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
          qysdsObj.sl = sl1;
          // hdlrl 计算值
          qysdsObj.hdlrl = hdlrl;
          // 触发校验
          const { index } = qysdsObj;
          this.onBlur(`ywxxList[${index}].hdlrl`);
        }
      } else if (type === 'gqjj') {
        // 股权净值 = (投资成本1*企业所得税汇率1+投资成本2*企业所得税汇率2+……投资成本i*企业所得税汇率i)*本次转让的股权比例/转让前持有股权比例
        const { cczrqcygqbl, bzzrgqbl, tzData } = this.stateObj.gqjjData;
        let tzcbzhrmbjeSum = 0;
        tzData.forEach((item) => {
          const { tzcbzhrmbje } = item;
          tzcbzhrmbjeSum += tzcbzhrmbje;
        });
        const gqjj = cczrqcygqbl === 0 ? 0 : (tzcbzhrmbjeSum * bzzrgqbl) / cczrqcygqbl;
        const qysdsObj = this.idxZsxmDm2Ywxx[this.qysdsZsxmDm];
        qysdsObj.gqjj = gqjj;
        // 触发校验
        const { index } = qysdsObj;
        this.onBlur(`ywxxList[${index}].gqjj`);
      } else if (type === 'mrj') {
        // 买入价 = (买入价1*买入股权比例1+买入价2*买入股权比例2+……买入价i*买入股权比例i)/(入股权比例1+买入股权比例2+……买入股权比例i)
        let mrjSum = 0;
        let mrgqblSum = 0;
        this.stateObj.mrjData.forEach((item) => {
          const { mrj, mrgqbl } = item;
          mrjSum += mrj * mrgqbl;
          mrgqblSum += mrgqbl;
        });
        const pjmrj = mrgqblSum === 0 ? 0 : mrjSum / mrgqblSum;
        const zzsObj = this.idxZsxmDm2Ywxx[this.zzsZsxmDm];
        zzsObj.mrj = pjmrj;
        // 触发校验
        const { index } = zzsObj;
        this.onBlur(`ywxxList[${index}].mrj`);
      } else if (type === 'sbqzsxblqr') {
        const nopass = {};
        this.sbqzsxblData.KjskdjList.forEach((item, index) => {
          const { Dkdjdsdjskywnr } = item;
          const path = `KjskdjList[${index}].Dkdjdsdjskywnr`;
          if (!Dkdjdsdjskywnr) {
            nopass[path] = [{ type: 'error', message: '代扣代缴、代收代缴税款业务内容不能为空' }];
          }
        });
        this.stateObj.sbqzsxblErrors = cloneDeep(nopass);

        if (JSON.stringify(this.stateObj.sbqzsxblErrors) === '{}') {
          this.showSbqzsxblLoading = true;
          await this.afterSbqzsxbl(true);
          this.showSbqzsxblLoading = false;
        } else {
          return;
        }
      }
      this.visibleObj[type] = false;
    },
    // 弹框关闭事件
    closeDialog(type) {
      this.visibleObj[type] = false;
    },
    // 享受税收协定为否
    onXsssxdChange(value) {
      if (value === 'N') {
        this.modelData.tkDm = '';
        this.modelData.syqkDm = '';
        this.modelData.ssxdMsg = '';
        this.modelData.ssxdmc = '';
        this.modelData.xdsl = 0;
        this.checkValidated('tkDm');
        this.checkValidated('syqkDm');
      }
    },
    // #region 辅助函数
    // 转换核心登记信息为非居民信息
    getFjmqysfxxcjVOByHx(body) {
      return {
        gjhdqszDm: body.gjhdqszDm,
        fjmnsrzjmgnsrsbh: body.szgjdqnsrsbh,
        fjmqyzwmc: body.nsrmc,
        fjmqyywmc: body.nsrywmc,
        fjmqyzqjmgzcdz: body.zcdz,
        lxrxm: body.lxr,
        lxfs: body.lxdh,
        lxfs_encode: body.lxdh_encode,
        lxr_encode: body.lxr_encode,
      };
    },
    /*
    查税费种认定信息
    2.0 【增值税】特殊对接zzsrdPmPpjg，非空视为2.1已认定：Y：匹配成功，N：匹配失败；空：走的自动认定2.2
    2.1 【企业所得税】yrdCode有税费种认定信息。
      2.1.1 判断征收代理方式为代扣代缴(2)、纳税期限为按次(11)
      2.1.2 yrdCode为2，存在代扣代缴，不存在按次。
      2.1.3 （成功）yrdCode为3，存在代扣代缴和按次。
    2.2 （3种税都可能）没有税费种认定信息。直接调中软税费种认定接口。
      2.2.1 （成功）returnCode为1，认定成功。4：认定成功，发送门户消息提醒失败。
      2.2.2 returnCode为2，认定失败，提示具体的失败原因。
      2.2.3 returnCode为3，认定失败-会统校验失败，会给税务人端推送待办。提示“您未认定代扣代缴企业所得税税种，系统自动认定失败，已给主管税务机关推送税费种认定待办任务，请等待主管税务机关认定后再进行办理！”
      2.2.4 (Error级别)returnCode为其他，提示服务不可用
    */
    // 税费种认定判定逻辑
    getRdTipsMsg(szMc, { yrdCode = '', wrdCode = '', wrdMsg = '', zzsrdPmPpjg = '', zzsrdPmDm = '', zzsrdPmMc = '' }) {
      if (szMcMap.zz === szMc && zzsrdPmPpjg) {
        // 2.0 情况
        if (zzsrdPmPpjg === 'N') {
          const { zspmDm, zspmmc } = this.nsywpdjg.ywxxList.find(({ zsxmDm }) => zsxmDm === this.zzsZsxmDm);
          return `您已认定代扣代缴${szMc}，但是征收品目为“${zzsrdPmDm}${zzsrdPmMc}”，不符合本场景办税要求，征收品目应更正为“${zspmDm}${zspmmc}”，请联系主管税务机关处理后再进行申报！`;
        }
      } else if (szMcMap.qs === szMc && yrdCode) {
        // 2.1 情况
        const idxYrdCode2Msg = {
          2: '纳税期限应为“次”，',
        };
        if (idxYrdCode2Msg[yrdCode]) {
          return `您已认定代扣代缴${szMc}，但是纳税期限为“${
            this.apiRes.qysdsNsqxMc ?? ''
          }”，不符合本场景办税要求，纳税期限应更正为“次”，请联系主管税务机关处理后再进行申报！`;
        }
        // 3 为都存在，成功
      } else if (wrdCode) {
        // 否则走无认定情况，判断2.2，只需要处理失败阻断情况
        if (wrdCode === '2') {
          return `您未认定${szMc}税种，系统自动认定失败，失败原因：${wrdMsg}，无法自动认定！请联系主管税务机关办理税种按次认定信息！`;
        }
        if (wrdCode === '3') {
          return `您未认定${szMc}税种，系统自动认定失败，已给主管税务机关推送税费种认定待办任务，请等待主管税务机关认定后再进行办理！`;
        }
        // 1、4为成功，其他后端 Error 类阻断
      }
      // 认定信息满足申报条件
      return '';
    },
    // 数字格式化
    format(value, num = 2) {
      return format(value, num);
    },
    // 四舍五入
    ROUND(value, num = 2) {
      return formulaFunction.ROUND(value, num);
    },
    // #endregion
    // 选择支付日期
    onZfrqChange(value) {
      if (this.hasZzs) {
        this.modelData.ywxxList.forEach((item, index) => {
          if (item.zsxmDm === '10101') {
            item.sssqQ = value;
            item.sssqZ = value;
            this.onBlur(`ywxxList[${index}].sssqQ`);
            this.onBlur(`ywxxList[${index}].sssqZ`);
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.wrap-header {
  height: 70px;
  line-height: 70px;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);
}
.title1 {
  font-size: 20px;
  letter-spacing: 0;
  color: #27282e;
}
.gray-card {
  background: #f9fafd;
}
.content-label {
  padding: 17px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);
  .decorate {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 9px;
    background: #4285f4;
    transform: translate(0, 3px);
  }
  span {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
  }
}
.zxsb-tips {
  display: flex;
  flex-direction: column;
  span {
    font-size: 14px;
    color: #f90;
  }
  &::before {
    height: 6px;
    margin-bottom: 24px;
    background-color: #fff;
    content: '';
  }
}
.description {
  font-size: 12px;
  color: #999;
}
.info-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #ccc;
  vertical-align: middle;
  cursor: pointer;
}

.tdgv-wrapper .t-form__controls .t-form__controls-content .t-radio-group /deep/ .t-radio {
  margin-right: 10px;
}

.tdgv-wrapper .gt-form-item /deep/.t-form__label {
  padding-right: 0;
  white-space: normal;
}
</style>
