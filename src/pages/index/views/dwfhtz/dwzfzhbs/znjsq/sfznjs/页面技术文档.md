# 税费智能计算页面技术文档

## 页面概述

**页面路径**: `/src/pages/index/views/sb/dwzfzhbs/znjsq/sfznjs/index.vue`

**页面功能**: 非居民企业税费智能计算页面，支持企业所得税、增值税、印花税、房产税、文化事业建设费等多种税种的智能计算和申报。

## 路由参数和入参

### 必需的路由参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `Xthtbh` | String | 系统合同编号 | "HT202312010001" |
| `fromPage` | String | 来源页面标识 | "zfba" / "nsywpd" |

### 可选的路由参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `bz` | String | 币种代码 | "156" (人民币) |
| `zfrq` | String | 支付日期 | "2023-12-01" |
| `zfje` | Number | 支付金额 | 100000 |
| `wsbh` | String | 文书编号(指定扣缴时使用) | "WS202312010001" |
| `jtbsSbuuid` | String | 集团报送业务UUID | "uuid-string" |

## Store数据依赖

### 主要Store模块

```javascript
// 使用的store模块
import store from '@/pages/index/views/sb/dwzfzhbs/form.store';
const { storeMixin } = createStore('sb/dwzfzhbs/form', store);
```

### 关键Store数据

#### 通过后端接口获取的数据

| 数据字段 | 类型 | 说明 | 数据来源 |
|----------|------|------|----------|
| `nsywpdjg` | Object | 纳税义务评定结果 | 从路由参数或store中获取，包含税种信息、优惠政策等 |
| `nsywpdInfo` | Object | 纳税义务评定信息 | 从纳税义务评定结果中解析获取，包含二级费用类型等信息 |

**nsywpdjg数据结构：**
```javascript
{
  ywxxList: [        // 业务信息列表
    {
      zsxmDm: '',      // 征收项目代码
      zsxmMc: '',      // 征收项目名称
      kjlxDm: '',      // 扣缴类型代码
      sl: 0,           // 税率
      syssxdtklbDm: '', // 税收协定条款类别代码
      // 页面计算时会动态添加的字段：
      jsje: 0,         // 计算金额
      yjse: 0,         // 应缴税额
      index: 0,        // 索引
      hdlrl: 0,        // 核定利润率（企业所得税）
      gqjj: 0,         // 股权净值（企业所得税）
      checkedHelp: false, // 是否勾选帮助
      sfcd: '',        // 税费承担
      hl: 0,           // 汇率
      sbsr: 0,         // 申报收入
      kce: 0           // 扣除额
    }
  ],
  yhxxList: [        // 优惠信息列表
    {
      zsxmDm: '',      // 征收项目代码
      jmxzDm: '',      // 减免性质代码
      jmxzMc: '',      // 减免性质名称
      jmzcwz: ''       // 减免政策文字
    }
  ],
  fjmkjbsfypdljbuuid: '' // 非居民扣缴备案身份评定流程业务UUID
}
```

**nsywpdInfo数据结构：**
```javascript
{
  ejlx: '',          // 二级费用类型代码（如：'023001'股权转让）
  wtxx: '',          // 问题信息（答题选择结果，如：'C21_A79'）
  // 其他评定相关信息字段...
}
```
| `fylx2jDm` | String | 二级费用类型代码 | 从`nsywpdInfo.ejlx`中提取 |
| `fylx2jMc` | String | 二级费用类型名称 | 从纳税义务评定结果中的费用类型信息提取 |

**相关接口信息：**

1. **纳税义务评定结果获取**
   - 通常在纳税义务评定页面完成后存储在store中
   - 或通过路由参数传递评定结果ID后调用相关查询接口

2. **合同信息查询接口**
   ```javascript
   api.cxHtxx({ Xthtbh }, false)
   ```
   - **接口路径**: `/api/cxHtxx`
   - **入参**: 
     - `Xthtbh`: 系统合同编号 (String, 必填)
   - **返回**: 合同详细信息，包含合同名称、编号、币种、非居民登记序号等

3. **非居民身份信息查询接口**
   ```javascript
   api.cxFjmsfxx({
     fjmnsrsbh,
     fjmdjxh, 
     gjhdqszDm,
     xthtbh
   }, false)
   ```
   - **接口路径**: `/api/cxFjmsfxx`
   - **入参**:
     - `fjmnsrsbh`: 非居民纳税人识别号 (String)
     - `fjmdjxh`: 非居民登记序号 (String)
     - `gjhdqszDm`: 国家或地区代码 (String)
     - `xthtbh`: 系统合同编号 (String)
   - **返回**: 非居民身份信息，包含评定结果等

4. **根据登记序号查询纳税人信息接口**
   ```javascript
   api.cxNsrxxByFjmdjxh({
     xthtbh,
     fjmdjxh
   }, false)
   ```
   - **接口路径**: `/api/cxNsrxxByFjmdjxh`
   - **入参**:
     - `xthtbh`: 系统合同编号 (String, 必填)
     - `fjmdjxh`: 非居民登记序号 (String, 必填)
   - **返回**: 纳税人基本信息

#### 本地配置或计算生成的数据

| 数据字段 | 类型 | 说明 | 数据来源 |
|----------|------|------|----------|
| `formCT` | Object | 表单常量数据 | 本地静态配置，包含各种下拉选项、常量映射等 |
| `stateObj` | Object | 页面状态对象 | 本地计算生成，存储页面交互状态和临时数据 |
| `visibleObj` | Object | 弹框显示状态 | 本地计算生成，控制各种弹框的显示与隐藏 |
| `errorStates` | Object | 表单校验错误状态 | 本地计算生成，记录表单字段的校验结果 |

**详细说明：**
- `formCT`: 包含币种选项(bzCT)、税费承担选项(skcdCT)、条款选项(tkCT)等静态配置数据
- `stateObj`: 存储如选中的指定扣缴文书(selectedZdkjwsxx)、申报前置事项办理数据等状态信息
- `visibleObj`: 控制查看政策(ckcz)、指定扣缴文书(zdkjws)、股权净值(gqjj)等弹框的显示状态
- `errorStates`: 通过表单校验逻辑生成，记录各字段的错误状态

## API接口调用详情

### 1. 初始化阶段接口

#### 获取税收协定信息
```javascript
api.getTaxTreaty({}, false)
```
**入参**: 无  
**返回**: 税收协定信息，包含国家地区、条款、适用情况等

#### 查询文化事业建设费减征比例
```javascript
api.cxWhsyjsfJzbl({}, false)
```
**入参**: 无  
**返回**: 文化事业建设费减征比例数据

#### 根据合同编号获取合同信息
```javascript
api.cxHtxx({ Xthtbh }, false)
```
**入参**:
- `Xthtbh`: 系统合同编号

**返回**: 合同详细信息，包含合同名称、编号、币种等

#### 查询指定扣缴文书信息
```javascript
api.cxZdkjwsxx({ bh: this.htxx.bh }, false)
```
**入参**:
- `bh`: 合同编号

**返回**: 指定扣缴文书列表信息

### 2. 非居民身份信息接口

#### 根据登记序号查询纳税人信息
```javascript
api.cxNsrxxByFjmdjxh({
  xthtbh: this.$route.query.Xthtbh,
  fjmdjxh
}, false)
```
**入参**:
- `xthtbh`: 系统合同编号
- `fjmdjxh`: 非居民登记序号

#### 查询非居民身份信息
```javascript
api.cxFjmsfxx({
  fjmnsrsbh,
  fjmdjxh,
  gjhdqszDm,
  xthtbh: this.$route.query.Xthtbh
}, false)
```
**入参**:
- `fjmnsrsbh`: 非居民纳税人识别号
- `fjmdjxh`: 非居民登记序号
- `gjhdqszDm`: 国家或地区代码
- `xthtbh`: 系统合同编号

### 3. 计算和申报相关接口

#### 保存计算结果明细
```javascript
api.saveJsjgMx(this.scJsjgMx(false), false)
```
**入参**: 计算结果明细对象，包含:
- `ZnsbJsjgMx`: 智能申报计算结果明细数组
- `ZnsbYwlx`: 智能申报业务类型
- `Xthtbh`: 系统合同编号
- `Nsywjgbh`: 纳税义务结果编号
- `BzDm`: 币种代码
- `Htbh`: 合同编号
- `Htmc`: 合同名称
- `Zfrq`: 支付日期
- `Zfje`: 支付金额
- `Skhj`: 税款合计

#### 申报前置校验
```javascript
api.verifyBegin({ zsxmDms: this.zsxmDms }, false)
```
**入参**:
- `zsxmDms`: 征收项目代码列表(逗号分隔)

**返回**: 校验结果，包含扣缴登记信息、认定状态等

#### 自动扣缴登记
```javascript
api.zdblKjskdjxx({ ...this.stateObj.sbqzsxblData }, false)
```
**入参**: 申报前置事项办理数据

#### 文化事业建设费缴费登记
```javascript
api.saveWhsyjsfjfdj({ ...this.stateObj.sbqzsxblData }, false)
```
**入参**: 申报前置事项办理数据

### 4. 企业所得税相关接口

#### 获取扣缴企业所得税初始化数据
```javascript
api.getKjqysdsbgInitData({
  ...param,
  bsms
}, false)
```
**入参**:
- `SssqQ`: 税款所属期起
- `SssqZ`: 税款所属期止
- `dzbdbmList`: 电子表单编码列表
- `xthtbh`: 系统合同编号
- `reset`: 重置标志
- `ysqxxid`: 申请信息ID(可选)
- `bsms`: 业务模式

#### 获取扣缴企业所得税公式
```javascript
api.getKjqysdsbgFormulas(param, false)
```
**入参**: 同上

#### 获取扣缴企业所得税要素数据
```javascript
api.getKjqysdsbgYsData({
  ...param,
  ywbm: 'kjqysdsbg',
  bsms
}, false)
```
**入参**: 包含业务编码的参数对象

#### 保存扣缴企业所得税表单
```javascript
api.saveKjqysdsbgForm(paramNoReset, false)
```
**入参**:
- `Ysqxxid`: 申请信息ID
- `Zcbw`: 暂存报文(JSON字符串)
- 其他表单参数

### 5. 文化事业建设费相关接口

#### 获取文化事业建设费初始化数据
```javascript
api.getWhsyjsfdkdjsbInitData({
  ...param,
  bsms
}, false)
```

#### 获取文化事业建设费公式
```javascript
api.getWhsyjsfdkdjsbFormulas(param, false)
```

#### 获取文化事业建设费要素数据
```javascript
api.getWhsyjsfdkdjsbYsData({
  ...param,
  ywbm: 'whsyjsfdkdjsb',
  bsms
}, false)
```

#### 保存文化事业建设费表单
```javascript
api.saveWhsyjsfdkdjsbForm(paramNoReset, false)
```

### 6. 增值税相关接口

#### 增值税暂存
```javascript
api.zcDkdjzzs({
  jsjgUuid,
  subYwbm: 'dkdjzzs'
}, false)
```
**入参**:
- `jsjgUuid`: 计算结果UUID
- `subYwbm`: 子业务编码

#### 增值税报表校验
```javascript
api.bbjyDkdjzzs({
  JsjgUuid: this.jsjgUuid,
  SubYwbm: 'dkdjzzs'
}, false)
```

### 7. 其他接口

#### 生成指定扣缴文书待办
```javascript
kqqysdsbgApi.scZdkjwsDb({
  xthtbh: this.$route.query.Xthtbh,
  fjmqysfm: fjmnsrsbh,
  fjmdjxh,
  bh
})
```

#### 作废计算结果
```javascript
api.zfJsjg({
  jsjgUuid: this.jsjgUuid
})
```

## 页面初始化流程

### 1. 路由参数获取
```javascript
const { Xthtbh = '', bz = '', zfrq = '', zfje = 0 } = this.$route.query;
```

### 2. 并行获取基础数据
- 税收协定信息
- 文化事业建设费减征比例
- 合同信息(如果有合同编号)

### 3. 处理合同相关信息
- 设置合同名称、编号
- 获取指定扣缴文书信息
- 获取非居民身份信息

### 4. 处理纳税义务评定结果
- 解析税种信息
- 初始化优惠政策选项
- 设置税种相关参数

### 5. 初始化表单选项
- 币种下拉选项
- 税费承担选项
- 国家地区选项
- 条款名称选项

## 主要业务逻辑

### 税费计算逻辑

#### 股权转让情况计算
- **企业所得税**: `应纳税所得额 = (转让收入 - 股权净值) / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率*f2*f4)`
- **增值税**: `计税价款 = (卖出价 - 买入价) / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率*f2*f4)`

#### 一般情况计算
- **计税价款**: `支付金额 / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率*f2*f4)`
- **企业所得税**: `应纳税所得额 = 计税价款 × 企业所得税汇率`
- **增值税**: `计税依据 = 计税价款 × 增值税汇率`
- **文化事业建设费**: `计费依据 = 计税价款×(1+增值税税率)×增值税汇率`

### 减免税额计算

#### 享受协定待遇减免
- 指定扣缴: `享受协定待遇减免税额 = 所得金额 × 核定利润率 × 核定税率(25%)`
- 协定税率<10%: `减免税额 = 应纳税所得额 × (10% - 适用税率)`

#### 国内税收优惠
- 递延纳税: 使用填报的减免税额
- 其他优惠: 减免全部应纳税额

## 表单校验规则

### 必填字段校验
- 币种、支付日期、支付金额
- 税费承担、汇率
- 股权净值(股权转让时)
- 买入价(股权转让增值税时)
- 税款所属期(增值税)

### 业务逻辑校验
- 享受税收协定时条款和适用情况的关联校验
- 指定扣缴时文书选择校验
- 合同支付所得类型与扣缴类型一致性校验

## 依赖组件

### 子组件
- `htmcbh`: 合同名称编号组件
- `zdkjws`: 指定扣缴文书选择组件
- `gqjj`: 股权净值计算组件
- `mrj`: 买入价计算组件
- `sbqzsxblqr`: 申报前置事项办理确认组件
- `Steps`: 步骤条组件

### 工具函数
- `format`: 数字格式化
- `formulaFunction.ROUND`: 四舍五入
- `getDate`: 日期处理
- `cloneDeep`: 深拷贝

## 错误处理

### API调用错误处理
- 统一的body解析函数处理字符串和对象类型
- 接口调用失败时的错误提示
- 轮询接口的重试机制

### 业务逻辑错误处理
- 税费种认定失败的处理
- 校验不通过时的错误提示
- 数据异常时的兜底处理

## 页面状态管理

### 加载状态
- `showLoading`: 整个页面加载状态
- `showSbqzsxblLoading`: 申报前置事项办理加载状态

### 弹框状态
- `visibleObj.ckcz`: 查看政策弹框
- `visibleObj.zdkjws`: 指定扣缴文书弹框
- `visibleObj.gqjj`: 股权净值弹框
- `visibleObj.mrj`: 买入价弹框
- `visibleObj.sbqzsxblqr`: 申报前置事项办理确认弹框

### 计算状态
- `isYjs`: 是否已计算
- `isZdkj`: 是否为指定扣缴
- `isLsb`: 是否为零申报

## 注意事项

1. **数据解析**: 所有API返回的body数据需要统一解析，支持字符串和对象类型
2. **轮询机制**: 文化事业建设费缴费登记和增值税校验使用轮询机制处理异步认定
3. **公式引擎**: 使用公式引擎进行复杂的税费计算和校验
4. **状态同步**: 页面状态与store状态需要保持同步
5. **错误兜底**: 各种异常情况都有相应的错误处理和用户提示