<template>
  <div>
    <div class="plr-24 pb-24">
      <div class="wrap-header">
        <div class="title1">税款计算器</div>
      </div>
      <!-- 支付信息 -->
      <div>
        <div class="mt-16">
          <gt-form labelAlign="top">
            <t-row :gutter="16">
              <t-col :span="4">
                <gt-form-item label="支付金额">
                  <gt-input-money :disabled="isYjs" type="nonnegative" v-model.number="modelData.zfje" />
                </gt-form-item>
              </t-col>
            </t-row>
          </gt-form>
        </div>
      </div>
      <!-- 税种信息 -->
      <gt-form labelAlign="left">
        <t-row :gutter="16">
          <!-- 代扣代缴企业所得税 -->
          <t-col :span="4">
            <div class="gray-card plr-24 pb-16" :style="{ 'min-height': minHeight }">
              <div class="content-label">
                <t-checkbox
                  :disabled="isYjs"
                  v-model="modelData.checkQysds"
                  @change="onCheckChanged('checkQysds')"
                  style="margin-left: 8px; vertical-align: middle"
                ></t-checkbox>
                <span class="title2">代扣代缴企业所得税</span>
              </div>
              <gt-form-item label-width="5rem">
                <template #label>
                  <div>税费承担：</div>
                </template>
                <t-radio-group
                  :disabled="!modelData.checkQysds || isYjs"
                  @change="changeKjlx"
                  :options="kjlxOption"
                  v-model="modelData.qysdsObj.kjlxDm"
                ></t-radio-group>
              </gt-form-item>
              <gt-form-item label-width="8rem">
                <template #label>
                  <div>企业所得税税率：</div>
                </template>
                <div>
                  <div>{{ `${ROUND(modelData.qysdsObj.sl * 100)}%` }}</div>
                </div>
              </gt-form-item>
              <gt-form-item v-if="modelData.qysdsObj.kjlxDm == '10'" label-width="5rem">
                <template #label>
                  <div>协定税率：</div>
                </template>
                <div>
                  <gt-input-money
                    :disabled="!modelData.checkQysds || isYjs"
                    type="percent"
                    v-model.number="modelData.qysdsObj.xdsl"
                    :maxValue="modelData.qysdsObj.sl"
                    :digit="2"
                  />
                </div>
              </gt-form-item>
              <gt-form-item v-else label-width="6rem">
                <template #label>
                  <div>核定利润率：</div>
                </template>
                <div>
                  <gt-input-money
                    :disabled="!modelData.checkQysds || isYjs"
                    type="percent"
                    v-model.number="modelData.qysdsObj.hdlrl"
                    :digit="2"
                  />
                </div>
              </gt-form-item>
              <gt-form-item label-width="5rem">
                <template #label>
                  <div>税费承担：</div>
                </template>
                <t-radio-group
                  :disabled="!modelData.checkQysds || isYjs"
                  :options="sfcdOption"
                  v-model="modelData.qysdsObj.sfcd"
                ></t-radio-group>
              </gt-form-item>
              <gt-form-item label-width="3rem">
                <template #label>
                  <div>汇率：</div>
                </template>
                <div>
                  <div class="like-t-form__controls-content">
                    <gt-input-money
                      :disabled="!modelData.checkQysds || isYjs"
                      type="nonnegative"
                      v-model.number="modelData.qysdsObj.hl"
                      :digit="6"
                      :style="{ width: '160px' }"
                    />
                    <t-tooltip content="实际支付或到期支付之日的人民币汇率中间价">
                      <gt-icon icon-name="info-circle1" color="#999" class="info-icon" />
                    </t-tooltip>
                  </div>
                  <div v-if="!isYjs" class="a--mt-4">
                    <a href="javascript:void(0);" @click="ckhl">查看汇率</a>
                  </div>
                </div>
              </gt-form-item>
              <template v-if="isYjs">
                <gt-form-item label-width="7rem">
                  <template #label>
                    <div>应纳税所得额：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.qysdsObj.jsje) }}</div>
                  </div>
                </gt-form-item>
                <gt-form-item label-width="5rem">
                  <template #label>
                    <div>应交税额：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.qysdsObj.yjse) }}</div>
                  </div>
                </gt-form-item>
              </template>
            </div>
          </t-col>
          <!-- 代扣代缴增值税 -->
          <t-col :span="4">
            <div class="gray-card plr-24 pb-16" :style="{ 'min-height': minHeight }">
              <div class="content-label">
                <t-checkbox
                  :disabled="isYjs"
                  v-model="modelData.checkZzs"
                  @change="onCheckChanged('checkZzs')"
                  style="margin-left: 8px; vertical-align: middle"
                ></t-checkbox>
                <span class="title2">代扣代缴增值税</span>
              </div>
              <gt-form-item label-width="6rem">
                <template #label>
                  <div>增值税税率：</div>
                </template>
                <t-radio-group
                  :disabled="!modelData.checkZzs || isYjs"
                  :options="zzsslOption"
                  v-model="modelData.zzsObj.sl"
                ></t-radio-group>
              </gt-form-item>
              <gt-form-item label-width="5rem">
                <template #label>
                  <div>税费承担：</div>
                </template>
                <t-radio-group
                  :disabled="!modelData.checkZzs || isYjs"
                  :options="sfcdOption"
                  v-model="modelData.zzsObj.sfcd"
                ></t-radio-group>
              </gt-form-item>
              <gt-form-item label-width="3rem">
                <template #label>
                  <div>汇率：</div>
                </template>
                <gt-input-money
                  :disabled="!modelData.checkZzs || isYjs"
                  type="nonnegative"
                  v-model.number="modelData.zzsObj.hl"
                  :digit="6"
                  :style="{ width: '160px' }"
                />
                <t-tooltip content="销售额发生当天或当月1日的人民币汇率中间价">
                  <gt-icon icon-name="info-circle1" color="#999" class="info-icon" />
                </t-tooltip>
              </gt-form-item>
              <template v-if="isYjs">
                <gt-form-item label-width="5rem">
                  <template #label>
                    <div>计税依据：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.zzsObj.jsje) }}</div>
                  </div>
                </gt-form-item>
                <gt-form-item label-width="5rem">
                  <template #label>
                    <div>应交税额：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.zzsObj.yjse) }}</div>
                  </div>
                </gt-form-item>
              </template>
            </div>
          </t-col>
          <!-- 代扣代缴文化事业建设费 -->
          <t-col :span="4">
            <div class="gray-card plr-24" :style="{ 'min-height': minHeight }">
              <div class="content-label">
                <t-checkbox
                  :disabled="isYjs"
                  v-model="modelData.checkWhsyjsf"
                  @change="onCheckChanged('checkWhsyjsf')"
                  style="margin-left: 8px; vertical-align: middle"
                ></t-checkbox>
                <span class="title2">代扣代缴文化事业建设费</span>
              </div>
              <gt-form-item label-width="10rem">
                <template #label>
                  <div>文化事业建设费费率：</div>
                </template>
                <div>
                  <div>{{ `${ROUND(modelData.whsyjsfObj.sl * 100)}%` }}</div>
                </div>
              </gt-form-item>
              <template v-if="isYjs">
                <gt-form-item label-width="5rem">
                  <template #label>
                    <div>计税金额：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.whsyjsfObj.jsje) }}</div>
                  </div>
                </gt-form-item>
                <gt-form-item label-width="5rem">
                  <template #label>
                    <div>应交税额：</div>
                  </template>
                  <div>
                    <div>{{ format(modelData.whsyjsfObj.yjse) }}</div>
                  </div>
                </gt-form-item>
              </template>
            </div>
          </t-col>
        </t-row>
      </gt-form>
      <div v-if="!isYjs" class="mt-24">
        <gt-submit-footer :showPreview="false" type="center" reportTxt="计算" @report="jsBtnClick" />
      </div>

      <gt-layout-footerbar v-if="isYjs">
        <gt-submit-footer
          :money="modelData.skhj"
          :title="modelData.skMsg"
          :showPreview="false"
          reportTxt="重新计算"
          @report="resetSfjs"
        />
      </gt-layout-footerbar>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import api from '@/pages/index/api/dwzfzhbs';
import { format, formulaFunction } from '@gt/components';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);
export default {
  mixins: [pageMixin, storeMixin],
  components: {},
  data() {
    return {
      // 页面使用模型数据
      modelData: {
        zfje: 0,
        // 企业所得税
        qysdsObj: {
          kjlxDm: '10',
          sl: 0.1,
          xdsl: 0.1,
          hdlrl: 0,
          sfcd: '0',
          hl: 0,
          jsje: 0,
          yjse: 0,
        },
        // 增值税
        zzsObj: {
          sl: 0.05,
          sfcd: '0',
          hl: 0,
          jsje: 0,
          yjse: 0,
        },
        // 文化事业建设费
        whsyjsfObj: {
          sl: 0.03,
          jsje: 0,
          yjse: 0,
        },
        // 是否勾选企业所得税
        checkQysds: false,
        // 是否勾选增值税
        checkZzs: false,
        // 是否勾选文化事业建设费
        checkWhsyjsf: false,
        // 税款信息
        skMsg: '税款合计',
        // 税款合计
        skhj: 0,
      },
      // 用于重置数据
      modelDataDefault: {},
      // 最小高度
      minHeight: '420px',
      // 享受协定待遇减免税额
      xsxddyJmse: 0,
      // 税费承担
      sfcdOption: [],
      // 扣缴类型
      kjlxOption: [],
      // 增值税税率
      zzsslOption: [],
      // 查看汇率url
      ckhlUrl: 'https://www.chinamoney.com.cn/chinese/bkccpr/',
      // 是否已计算
      isYjs: false,
      // 文化事业建设费减征比例
      whsyjsfJzbl: {
        zyjbl: 0,
        jzblcsz: 0,
      },
    };
  },
  async created() {
    // 保留初始化数据
    this.modelDataDefault = {
      qysdsObj: { ...this.modelData.qysdsObj },
      zzsObj: { ...this.modelData.zzsObj },
      whsyjsfObj: { ...this.modelData.whsyjsfObj },
    };
    // 初始化税费承担
    this.sfcdOption = this.formCT.skcdCT;
    // 初始化扣缴类型
    this.kjlxOption = this.formCT.kjlxCT;
    // 初始化增值税税率
    this.zzsslOption = this.formCT.zzsslCT;
    // 查询文化事业建设费减征比例
    const { body: whsyjsfJzbl } = await api.cxWhsyjsfJzbl({}, false);
    // 处理文化事业建设费减征比例
    this.whsyjsfJzbl = whsyjsfJzbl;
  },
  methods: {
    // 取消勾选税种卡片时，进行数据重置
    onCheckChanged(type) {
      const idxType2DataObj = {
        checkQysds: 'qysdsObj',
        checkZzs: 'zzsObj',
        checkWhsyjsf: 'whsyjsfObj',
      };
      this.$set(this.modelData, idxType2DataObj[type], { ...this.modelDataDefault[idxType2DataObj[type]] });
    },
    // 查看汇率
    ckhl() {
      // 跳转url
      window.open(this.ckhlUrl);
    },
    // 扣缴类型change事件
    changeKjlx(value) {
      if (value === '10') {
        this.modelData.qysdsObj.sl = 0.1;
      } else {
        this.modelData.qysdsObj.sl = 0.25;
      }
    },
    // 计算按钮
    jsBtnClick() {
      // 修改最小高度
      this.minHeight = '480px';
      // 修改标记
      this.isYjs = true;
      // 税费计算
      this.sfjs();
    },
    // 税费计算
    sfjs() {
      // f1、f2数据来源：根据开发表3输出值1“代扣代缴增值税”，若输出“否”则f1=0，若输出“是”则f1=1；输出值4“代扣代缴企业所得税”若输出“否”则f2=0，若输出“是”则f2=1。
      const f1 = this.modelData.checkZzs ? 1 : 0;
      const f2 = this.modelData.checkQysds ? 1 : 0;
      // f3、f4数据来源：手动选择，若“增值税承担者”或“企业所得税承担者”选择“居民企业”，则f3=1，f4=1，若选择“非居民企业”则为0。
      let f3 = 0;
      if (this.modelData.checkZzs) {
        const { sfcd } = this.modelData.zzsObj;
        f3 = sfcd === '0' ? 1 : 0;
      }
      let f4 = 0;
      if (this.modelData.checkQysds) {
        const { sfcd } = this.modelData.qysdsObj;
        f4 = sfcd === '0' ? 1 : 0;
      }
      // 实际征收税率
      let sjzsSl = 0.1;
      const { kjlxDm, sl, hdlrl } = this.modelData.qysdsObj;
      if (kjlxDm !== '10') {
        // 实际征收率：指定扣缴时使用，实际征收率=企业所得税税率×核定利润率，税率和核定利润率从指定扣缴文书中取数
        sjzsSl = sl * hdlrl;
      }

      // 增值税税率
      let zzsSl = 0;
      // 增值税汇率
      let zssHl = 0;
      zzsSl = this.modelData.zzsObj.sl;
      zssHl = this.modelData.zzsObj.hl;

      // 支付金额
      const { zfje } = this.modelData;
      // 计税价款 = 支付金额 / (1 + 增值税税率*f1*(1-f3) - 源泉扣缴税率(或实际征收率)*f2*f4)
      const jsjk = zfje / (1 + zzsSl * f1 * (1 - f3) - sjzsSl * f2 * f4);

      // （1）增值税，增值税计税依据 = 计税价款 × 增值税汇率，应交增值税 = 计税价款 × 增值税汇率 × 增值税税率
      // 计算依据
      const zzsJsyj = this.ROUND(jsjk * zssHl);
      // 应交增值税
      const yjzze = zzsJsyj * zzsSl;
      this.modelData.zzsObj.jsje = zzsJsyj;
      this.modelData.zzsObj.yjse = this.ROUND(yjzze);

      if (this.modelData.checkZzs) {
        this.modelData.skhj += this.modelData.zzsObj.yjse;
      }

      // （2）企业所得税，应纳税所得额 = 计税价款 × 企业所得税汇率，应交企业所得税 = 计税价款 × 企业所得税汇率 × 企业所得税税率（或实际征收率）
      const { hl: sdsHl } = this.modelData.qysdsObj;
      // 应纳税所得额
      const ynssde = jsjk * sdsHl;
      // 应交企业所得税
      let yjqysde = ynssde * sjzsSl;
      // 计算享受协定待遇减免税额
      this.jsXsxddyJmse(ynssde);
      // 应纳税所得额=应纳税所得额-享受协定待遇减免税额
      yjqysde -= this.xsxddyJmse;
      if (yjqysde < 0) {
        yjqysde = 0;
      }
      this.modelData.qysdsObj.jsje = this.ROUND(ynssde);
      this.modelData.qysdsObj.yjse = this.ROUND(yjqysde);

      if (this.modelData.checkQysds) {
        this.modelData.skhj += this.modelData.qysdsObj.yjse;
      }

      // （5）文化事业建设费，计费依据 = 计税价款×（1+增值税税率）×增值税汇率，应纳文化事业建设费 = 计费依据×文化事业建设费费率
      const { sl: whsyjsfSl } = this.modelData.whsyjsfObj;
      const whsyjsfJsyj = jsjk * (1 + zzsSl * 1) * zssHl;
      let ynWhsyjsf = whsyjsfJsyj * whsyjsfSl;
      // 文化事业建设费应补退税额，增加减征比例规则
      if (this.whsyjsfJzbl.jzblcsz === 0.5) {
        ynWhsyjsf *= this.whsyjsfJzbl.jzblcsz;
      } else {
        ynWhsyjsf =
          ynWhsyjsf * this.whsyjsfJzbl.zyjbl * 0.5 +
          ynWhsyjsf * (1 - this.whsyjsfJzbl.zyjbl) * (1 - this.whsyjsfJzbl.jzblcsz);
      }
      this.modelData.whsyjsfObj.jsje = this.ROUND(whsyjsfJsyj);
      this.modelData.whsyjsfObj.yjse = this.ROUND(ynWhsyjsf);

      if (this.modelData.checkWhsyjsf) {
        this.modelData.skhj += this.modelData.whsyjsfObj.yjse;
      }

      this.modelData.skhj = this.ROUND(this.modelData.skhj);
    },
    // 计算享受协定待遇减免税额
    jsXsxddyJmse(ynssde) {
      const { xdsl, hdlrl, kjlxDm } = this.modelData.qysdsObj;
      // 税费智能计算在 jsXsxddyJmse 计算时，外部链路排除了指定扣缴的情况，这里做等价修改
      // if (kjlxDm === '20') {
      //   // 指定扣缴，选择享受协定待遇：享受协定待遇减免税额 = 所得金额 × 核定利润率 × 核定税率（25%）
      //   this.xsxddyJmse = this.ROUND(ynssde * hdlrl * 0.25);
      // } else
      if (kjlxDm === '10' && xdsl < 0.1) {
        // 四、协定税率<10%，“适用税收协定及条款减免税额”计算规则：
        // 适用税收协定及条款减免税额 = 应纳税所得额 × （10% - 适用税率）
        this.xsxddyJmse = this.ROUND(ynssde * (0.1 - xdsl));
      } else {
        this.xsxddyJmse = 0;
      }
    },
    // 重置按钮
    resetSfjs() {
      // 修改最小高度
      this.minHeight = '420px';
      // 修改标记
      this.isYjs = false;
      // 修改税款合计
      this.modelData.skhj = 0;
    },
    // 数字格式化
    format(value, num = 2) {
      return format(value, num);
    },
    // 四舍五入
    ROUND(value, num = 2) {
      return formulaFunction.ROUND(value, num);
    },
  },
};
</script>

<style lang="less" scoped>
.wrap-header {
  height: 70px;
  line-height: 70px;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);
}
.title1 {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0;
  color: #27282e;
}
.gray-card {
  background: #f9fafd;
  /deep/.t-form__item {
    margin-bottom: 16px;
  }
  .like-t-form__controls-content {
    display: flex;
    min-height: 32px;
    box-align: center;
    flex-align: center;
    align-items: center;
    & + .a--mt-4 {
      margin-top: 4px;
      a {
        color: #4285f4;
        text-decoration: none;
      }
    }
  }
}
.gt-form-item /deep/.t-form__controls {
  margin-bottom: 0;
}
.content-label {
  display: flex;
  padding: 17px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);
  align-items: center;
  span {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
  }
}
.info-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #ccc;
  vertical-align: middle;
  cursor: pointer;
}

.tdgv-wrapper .t-form__controls .t-form__controls-content .t-radio-group /deep/ .t-radio {
  margin-right: 10px;
}

.tdgv-wrapper .gt-form-item /deep/.t-form__label {
  padding-right: 0;
  white-space: normal;
}
</style>
