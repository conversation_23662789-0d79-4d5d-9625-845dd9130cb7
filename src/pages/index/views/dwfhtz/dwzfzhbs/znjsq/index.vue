<template>
  <component :is="dynamicComponent"></component>
</template>

<script>
export default {
  data() {
    return {
      dynamicComponent: null,
    };
  },
  computed: {},
  created() {
    this.showPage();
  },
  mounted() {},
  methods: {
    // 渲染页面
    showPage() {
      //  根据浏览器传入的字段来判断跳转到哪个页面
      //  常用方案
      // import('@/pages/index/views/dwfhtz/dwzfzhbs/znjsq/cyfa/index').then((module) => {
      //   this.dynamicComponent = module.default;
      // });

      //  纳税义务判断
      import('@/pages/index/views/dwfhtz/dwzfzhbs/znjsq/nsywpd/index').then((module) => {
        this.dynamicComponent = module.default;
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
