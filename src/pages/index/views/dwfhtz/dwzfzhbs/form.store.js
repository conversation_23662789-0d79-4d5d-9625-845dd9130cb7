import baseStore from '@/pages/index/views/sb/common/store';
import api from '@/pages/index/api/dwzfzhbs';

const baseActions = baseStore.getActions(api);

const state = {
  ...baseStore.state,
  ywbm: 'dwzfzhbs',
  kjsbbm: '2', // 扣缴申报编码【0 自行申报 1 代收代缴 2 代扣代缴 3 委托代征  9 其他征收代理方式】
  fylx1jDm: '',
  fylx2jDm: '',
  fylx2jMc: '',
  nsywpdjg: {},
  nsywpdInfo: {
    ejlx: '', // 二级费用类型码值
    wtxx: '', // 选择问题答案 选择+问题编号_ 如：A15_A08
  },
  selectedZdkjwsxx: {},
  visibleObj: {
    visiblePdjg: false,
    famx: false,
    ckcz: false,
    sbqzsxblqr: false,
    zdkjws: false,
    gqjj: false,
    mrj: false,
  },
  stateObj: {
    selectedZdkjwsxx: {},
    gqjjData: {},
    mrjData: [],
    sbqzsxblData: {},
    sbqzsxblErrors: {},
  },
  zsxmCT: {
    10101: '代扣代缴增值税',
    10104: '代扣代缴企业所得税',
    10110: '房产税',
    10111: '印花税',
    30217: '代扣代缴文化事业建设费',
  },
};

/**
 * 无需接口请求，只是更新数据时使用mutations函数
 * mutations函数名使用纯大写字母
 */
const mutations = {
  ...baseStore.mutations,
};

const actions = {
  ...baseActions,

  // 获取方案
  async getSchemes({ state, commit }, params) {
    const res = await api.getSchemes(params);
    return res;
  },
  // 纳税人义务判断
  async judgeDuty({ state, commit }, params) {
    const res = await api.judgeDuty(params);
    const { body } = res;
    const { ywxxList } = body;

    if (ywxxList && ywxxList.length > 0) {
      let pdljbuuid = '';
      ywxxList.forEach((item) => {
        const { fjmkjbsfypdljbuuid } = item;
        pdljbuuid = `${pdljbuuid + fjmkjbsfypdljbuuid},`;
      });
      pdljbuuid = pdljbuuid.length > 1 ? pdljbuuid.substring(0, pdljbuuid.length - 1) : '';
      body.fjmkjbsfypdljbuuid = pdljbuuid;
    }

    commit('UPDATE_PAYLOAD', {
      nsywpdjg: body,
    });
    return res;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
