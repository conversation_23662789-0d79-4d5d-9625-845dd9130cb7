<template>
  <div>
    <ResultPage
      :height="height"
      :loading="showSubmitLoading"
      :btn-group="btnGroup"
      :more-operation="moreOperation"
      :title="title"
      :status="status"
      :sub-title="subTitle"
      :back-btn-config="backBtnConfig"
      :show-evaluation-btn="showEvaluationBtn"
      :evaluate-config="evaluateConfig"
      :useOverrideSubTitleSlot="useOverrideSubTitleSlot"
      :show-update-btn="showUpdateBtn"
      :on-update="initSubmitResult"
    >
      <template #subTitle>
        <div class="subj-title">
          <template v-if="submitResult.returnFlag !== 'N'">
            <p>{{ submitResult.tipsMsg }}</p>
            <div class="subj-title__p">
              <div>
                实际应补（退）税费额：<span>{{ format(submitResult.ybtse) }}</span> 元。
              </div>
              <div class="label-box" @click="onDetailLinkToggle">
                收起明细<ChevronUpIcon v-if="showDataModel.detailIconShow" class="label-icon" /><ChevronDownIcon
                  v-else
                  class="label-icon"
                />
              </div>
            </div>
          </template>
          <template v-else>
            <div class="subj-title__p">
              <div>
                {{ submitResult.tipsMsg }}
              </div>
              <div class="label-box" @click="onDetailLinkToggle">
                收起明细<ChevronUpIcon v-if="showDataModel.detailIconShow" class="label-icon" /><ChevronDownIcon
                  v-else
                  class="label-icon"
                />
              </div>
            </div>
          </template>
        </div>
        <SfzDetailTable
          v-show="showDataModel.detailIconShow"
          :list="submitResult.sbResultList"
          :resultFlag="submitResult.returnFlag"
        />
      </template>
      <template #btn-group>
        <template v-if="submitResult.returnFlag !== 'N' && submitResult.ybtse > 0">
          <t-button variant="outline" theme="primary" @click="orderPayment">预约缴款</t-button>
          <t-button theme="primary" @click="handelPayment">立即缴款</t-button>
        </template>
        <t-button v-else theme="primary" @click="goHome">返回首页</t-button>
      </template>
    </ResultPage>

    <!-- “预约缴款” 组件 -->
    <gt-order-pay-dialog
      :visible.sync="visibleOrder"
      :yzpzxhList="yzpzxhList"
      :zgswskfjDm="zgswskfjDm"
      :djxh="djxh"
      @confirmOrder="confirmOrder"
    ></gt-order-pay-dialog>
  </div>
</template>

<script>
import { format, loadZnhd } from '@gt/components';
import ResultPage from '@/pages/index/components/result-page';
import useSbResult from '@/pages/index/views/sb/common/result/useSbResult';
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue';
import { genUUID } from '@/pages/index/utils';
import formCT from '@/pages/index/views/dwfhtz/dwzfzhbs/formCT';
import api from '@/pages/index/api/dwzfzhbs/index';
import SfzDetailTable from './components/SfzDetailTable.vue';

export default {
  name: 'app-jg',
  components: {
    ChevronDownIcon,
    ChevronUpIcon,
    SfzDetailTable,
    ResultPage,
  },
  mixins: [useSbResult()],
  data() {
    return {
      // 是否显示“预约缴款” 组件
      visibleOrder: false,
      // 应征凭证序号集合
      yzpzxhList: [],
      // 主管税务所科分局代码
      zgswskfjDm: '',
      // 登记序号
      djxh: '',
      customFetchApi: true,
      showDataModel: {
        detailIconShow: true,
      },
      useOverrideSubTitleSlot: false,
      backBtnConfig: {
        hidden: true,
      },
    };
  },
  computed: {
    title() {
      const { submitResultTitle = '申报' } = this.$route.meta;
      const { returnFlag } = this.submitResult;
      const values = {
        Y: `${submitResultTitle}成功`,
        PW: '部分失败',
        N: `${submitResultTitle}失败`,
        W: submitResultTitle.includes('提交') ? '提交中' : `${submitResultTitle}提交中`,
      };
      return values[returnFlag || 'N'];
    },
    status() {
      const { returnFlag } = this.submitResult;
      const values = {
        Y: 'success',
        N: 'failed',
        PW: 'warning',
        W: 'warning',
      };
      return values[returnFlag || 'N'];
    },
  },
  created() {
    if (!this.$route.meta.hideZnhd) {
      loadZnhd();
    }
    const { djxh, zgswskfjDm } = this.$store.state[this.storeName];
    this.djxh = djxh;
    this.zgswskfjDm = zgswskfjDm;
  },
  methods: {
    // 初始化
    initSubmitResult() {
      this.getSubmitResult(api.getSbResult);
    },
    beforeGetSubmitResult() {
      const JsjgUuid = this.$store.state[this.storeName].JsjgUuid || this.$route.query.JsjgUuid;
      const SubYwbm = this.$route.query.SubYwbm || '';
      return { JsjgUuid, SubYwbm };
    },
    // 解析结果
    afterGetSubmitResult() {
      this.showSubmitLoading = false;

      // 获取结果本身是失败或超时，则不进行结果二次解析
      if (this.submitResult.returnFlag === 'N' || this.submitResult.returnFlag === 'W') {
        return;
      }
      const body = this.submitResult;
      let submitResult = {};
      // 多笔结果遍历列表合并结果
      if (Array.isArray(body)) {
        const { zsxmCT } = formCT;
        // 不小于 1 元的缴费相关信息
        let pzxh = '';
        let ybtseSum = 0;
        // 提示语
        const tipsMcWithY = [];
        const tipsMcWithN = [];
        // 所有报错信息
        const errInfoList = [];
        // 用于判断 returnFlag
        let flag = 'Y';
        let allNFlag = true;
        body.forEach((res) => {
          const { returnFlag, yzpzxh, ybtse, zsxmDm, errInfo } = res;
          const sfzMc = zsxmCT[zsxmDm] ?? '';
          res.sfzMc = sfzMc;
          if (returnFlag === 'Y') {
            tipsMcWithY.push(sfzMc);
            if (Number(ybtse) >= 1) {
              pzxh = `${pzxh + yzpzxh},`;
              ybtseSum += Number(ybtse);
              this.yzpzxhList.push(yzpzxh);
            }
            allNFlag = false;
          } else {
            flag = 'PW';
            tipsMcWithN.push(sfzMc);
            errInfoList.push(errInfo);
          }
        });
        if (flag === 'PW' && allNFlag) {
          flag = 'N';
        }
        const tipsMsgMap = {
          Y: `您申请的${tipsMcWithY.join('、')}申报成功。`,
          PW: `您申请的${tipsMcWithN.join('、')}申报失败。`,
          N: `您申请的${tipsMcWithN.join('、')}申报失败。`,
        };
        // 有不小于1元的申报成功需要缴费
        pzxh = pzxh ? pzxh.substring(0, pzxh.length - 1) : pzxh;

        const successObj = {
          yzpzxh: pzxh,
          ybtse: ybtseSum,
        };
        const basicObj = {
          returnFlag: flag,
          errInfoList,
          tipsMsg: tipsMsgMap[flag] ?? '',
          sbResultList: body,
        };
        if (flag === 'N') {
          submitResult = basicObj;
        } else {
          submitResult = { ...basicObj, ...successObj };
        }
      } else if (body) {
        submitResult = body;
      }
      this.updatePayload({ submitResult });
    },
    // 返回首页
    goHome() {
      window.location.href = `${window.location.origin}${this.appHomeUrl}`;
    },
    // 预约缴款
    orderPayment() {
      this.visibleOrder = true;
    },
    // 预约成功后对页面的操作
    confirmOrder() {},
    // 立即缴款
    handelPayment() {
      const uuid = genUUID();
      const url = `${window.top.location.origin}/skzx/view/skzs/jkkp?from=other&randomKey=${uuid}`;
      localStorage.setItem(`jkkpParam${uuid}`, JSON.stringify({ pzxh: this.submitResult.yzpzxh }));
      window.open(url);
    },
    // 格式化
    format(value) {
      return format(value, 2);
    },
    onDetailLinkToggle() {
      this.showDataModel.detailIconShow = !this.showDataModel.detailIconShow;
    },
  },
};
</script>

<style lang="less" scoped>
.subj-title {
  margin-top: 16px;
  text-align: center;
  p,
  .subj-title__p {
    font-size: 16px;
    line-height: 2rem;
    color: #666;
    span {
      color: rgb(237, 83, 83);
    }
  }
  .subj-title__p {
    display: flex;
    align-items: baseline;
    justify-content: center;
  }
}
.label-box {
  margin-left: 16px;
  font-weight: 600;
  color: #4285f4;
  cursor: pointer;
  .label-icon {
    font-size: 22px;
  }
}
</style>
