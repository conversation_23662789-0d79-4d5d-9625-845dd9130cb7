<template>
  <t-table class="sb-table" row-key="sbuuid" :data="list" :columns="columns" />
</template>

<script>
import { format } from '@gt/components';
import { idxYwbm2YzpzzlDm } from '@/pages/index/utils/urlMap';

export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    resultFlag: {
      type: String,
      default: '', // Y|W|N
    },
  },
  data() {
    return {};
  },
  computed: {
    columns() {
      const errorColumn =
        this.resultFlag !== 'Y'
          ? [
              {
                colKey: 'returnFlag',
                title: (h, { colIndex }) => <strong style="color: #000">申报状态</strong>,
                width: 92,
                cell: (h, { col, row }) => (
                  <t-tag size="small" theme={row.returnFlag === 'Y' ? 'success' : 'warning'} variant="light-outline">
                    {row.returnFlag === 'Y' ? '成功' : '失败'}
                  </t-tag>
                ),
              },
            ]
          : [];
      const czColumn =
        this.resultFlag !== 'Y'
          ? [
              {
                colKey: 'cz',
                title: (h, { colIndex }) => <strong style="color: #000">操作</strong>,
                width: 118,
                cell: (h, { col, row }) => {
                  if (row.returnFlag === 'Y') {
                    return <span>——</span>;
                  }
                  return (
                    <t-link theme="primary" hover="color" onClick={() => this.toModifyPage(row)}>
                      修改报表
                    </t-link>
                  );
                },
              },
            ]
          : [];
      const basic = [
        {
          colKey: 'sfzMc',
          title: (h, { colIndex }) => <strong style="color: #000">税费种</strong>,
          width: 153,
        },
        {
          colKey: 'sssqQ',
          title: (h, { colIndex }) => <strong style="color: #000">税款所属期</strong>,
          width: 236,
          cell: (h, { col, row }) => (
            <span>
              {row.sssqQ}至{row.sssqZ}
            </span>
          ),
        },
        ...errorColumn,
        // {
        //   title: (h, { colIndex }) => <strong style="color: #000">缴款期限</strong>,
        //   width: 130,
        //   cell: (h, { col, row }) => <span>——</span>,
        // },
        {
          colKey: 'ybtse',
          title: (h, { colIndex }) => <strong style="color: #000">应补(退)税费额(元)</strong>,
          width: 151,
          align: 'right',
          cell: (h, { col, row }) => <span>{this.ybtsfeRender(row.ybtse)}</span>,
        },
        ...czColumn,
      ];
      return basic;
    },
  },
  methods: {
    ybtsfeRender(ybtsfe) {
      if (this.resultFlag === 'N') {
        return '——';
      }
      const formatNumber = format(ybtsfe, 2);
      return Number(ybtsfe) < 1 ? `${formatNumber}（不予征收）` : formatNumber;
    },
    toModifyPage(row) {
      let path = '';
      const { sssqQ, sssqZ, ywlsUuid, zsxmDm, subYwbm } = row;
      const jsjgUuid = this.$route.query.JsjgUuid;
      switch (zsxmDm) {
        case '10101':
          window.location.href = `${window.location.origin}/sbzx/view/lzsfjssb/#/declare/dkdj?snapshotId=${ywlsUuid}&jsjgUuid=${jsjgUuid}`;
          return;
        case '10104':
        case '30217':
          path = idxYwbm2YzpzzlDm[subYwbm]?.url.split('#')[1];
          break;
        default:
          break;
      }
      const query = {
        SssqQ: sssqQ,
        SssqZ: sssqZ,
        Ysqxxid: ywlsUuid,
        JsjgUuid: jsjgUuid,
      };
      if (path) {
        this.$router.replace({
          path,
          query,
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.sb-table {
  width: 880px;
  margin: 16px auto 0;
}
</style>
