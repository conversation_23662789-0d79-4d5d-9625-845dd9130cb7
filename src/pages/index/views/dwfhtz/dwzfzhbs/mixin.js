import { commonMethods, commonData } from '@/pages/index/views/sb/common/mixin';
import formCT from '@/pages/index/views/dwfhtz/dwzfzhbs/formCT';
import { loadZnhd } from '@gt/components';

const toolsMethods = {
  /**
   * @param
   * type: 0 label为obj[key], value为key;
   * type: 1 label为 key|obj[key], value为key；
   * type: 2 label为obj[key].dm|obj[key].mc，value为obj[key].dm
   * type: 3
   * * */
  getOptions(obj, type, labelObj) {
    const array = [];
    Object.keys(obj)
      .sort()
      .forEach((key) => {
        if (type === 0) {
          array.push({
            value: key,
            label: obj[key],
          });
        } else if (type === 1) {
          array.push({
            value: key,
            label: `${key}|${obj[key]}`,
          });
        } else if (type === 2) {
          array.push({
            value: obj[key].dm,
            label: `${obj[key].dm}|${obj[key].mc}`,
          });
        } else if (type === 3) {
          let label = '';
          const labelArray = Object.keys(labelObj);
          labelArray.sort().forEach((item, index) => {
            label += obj[key][labelObj[item]] || '';
            if (index !== labelArray.length - 1) {
              label += '|';
            }
          });
          array.push({
            value: key,
            label,
          });
        } else if (type === 4) {
          array.push({
            value: obj[key].dm,
            label: `${obj[key].ywmc} ${obj[key].mc}`,
          });
        }
      });
    return array;
  },
};

const pageMixin = {
  data() {
    return {
      ...commonData,
      storeName: 'dwfhtz/dwzfzhbs/form',
      resultRouterName: 'dwzfzhbs/jg',
      formCT,
    };
  },
  async created() {
    if (!this.$route.meta.hideZnhd) {
      loadZnhd();
    }
  },
  methods: {
    ...commonMethods,
    ...toolsMethods,
  },
};
export { pageMixin };
