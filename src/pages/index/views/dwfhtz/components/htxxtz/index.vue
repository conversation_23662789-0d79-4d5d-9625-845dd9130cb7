<!-- eslint-disable no-param-reassign -->
<!--
 * @Descripttion: 台账-合同信息台账
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        @changeSelect="htlxChange"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow"><AddIcon slot="icon" />新增合同</t-button>
          <t-button theme="primary" @click="extractContract"><FileIcon slot="icon" />提取合同</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
      </div>

      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
          :rowClassName="rowClassName"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #htzxqssj="{ row }">
            <span>{{ formatDate(row.htzxqssj) }}</span>
          </template>
          <template #htzxzzsj="{ row }">
            <span>{{ formatDate(row.htzxzzsj) }}</span>
          </template>
          <template #cjrq="{ row }">
            <span>{{ formatDate(row.cjrq) }}</span>
          </template>
          <template #htzje="{ row }">
            <span>{{ numberToPrice(row.htzje) }}</span>
          </template>
          <template #htzje_hs="{ row }">
            <span>{{ numberToPrice(row.htzje_hs) }}</span>
          </template>
          <template #htzje_bhs="{ row }">
            <span>{{ numberToPrice(row.htzje_bhs) }}</span>
          </template>
          <template #yzfje_hs="{ row }">
            <span>{{ numberToPrice(row.yzfje_hs) }}</span>
          </template>
          <template #yzfje_bhs="{ row }">
            <span>{{ numberToPrice(row.yzfje_bhs) }}</span>
          </template>
          <template #sfycj="{ row }">
            <t-tag v-if="row.sfycj === '已采集'" theme="success" variant="light-outline">已采集</t-tag>
            <t-tag v-else-if="row.sfycj === '采集中'" theme="primary" variant="light-outline">采集中</t-tag>
            <t-tag v-else theme="default" variant="light-outline">未采集</t-tag>
          </template>
          <template #sfywj="{ row }">
            <t-tag v-if="row.sfywj === '已完结'" theme="success" variant="light-outline">已完结</t-tag>
            <t-tag v-else theme="warning" variant="light-outline">未完结</t-tag>
          </template>
          <template #operation="{ row }">
            <t-space size="8px">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)">
                修改
              </t-link>
              <t-link theme="danger" hover="color" @click="invalidateRow(row)">
                作废
              </t-link>
              <t-link theme="primary" hover="color" @click="viewPaymentDetail(row)">
                合同支付明细
              </t-link>
            </t-space>
          </template>
        </t-table>

      </div>

        <!-- 新增/编辑弹窗 -->
        <t-dialog
          :visible.sync="dialogVisible"
          :header="dialogTitle"
          width="900px"
          :on-confirm="handleConfirm"
          :on-cancel="handleCancel"
        >
          <t-form
            ref="formRef"
            :data="formData"
            :rules="formRules"
            label-align="right"
            :label-width="120"
          >
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="合同名称" name="htmc">
                  <t-input v-model="formData.htmc" placeholder="请输入合同名称" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="合同编号" name="htbh">
                  <t-input v-model="formData.htbh" placeholder="请输入合同编号" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="合同执行起始时间" name="htzxqssj">
                  <t-date-picker v-model="formData.htzxqssj" placeholder="请选择合同执行起始时间" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="合同执行终止时间" name="htzxzzsj">
                  <t-date-picker v-model="formData.htzxzzsj" placeholder="请选择合同执行终止时间" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="非居民企业身份码" name="fjmqysfm">
                  <t-input v-model="formData.fjmqysfm" placeholder="请输入非居民企业身份码" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="收款方名称" name="skfmc">
                  <t-input v-model="formData.skfmc" placeholder="请输入收款方名称" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="合同总金额" name="htzje">
                  <t-input-number v-model="formData.htzje" placeholder="请输入合同总金额" :min="0" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="合同总金额币种" name="htzbz">
                  <t-select v-model="formData.htzbz" placeholder="请选择币种">
                    <t-option value="CNY" label="人民币" />
                    <t-option value="USD" label="美元" />
                    <t-option value="EUR" label="欧元" />
                    <t-option value="JPY" label="日元" />
                  </t-select>
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="合同总金额（含税）" name="htzje_hs">
                  <t-input-number v-model="formData.htzje_hs" placeholder="请输入含税金额" :min="0" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="合同总金额（不含税）" name="htzje_bhs">
                  <t-input-number v-model="formData.htzje_bhs" placeholder="请输入不含税金额" :min="0" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="已支付金额（含税）" name="yzfje_hs">
                  <t-input-number v-model="formData.yzfje_hs" placeholder="请输入已支付含税金额" :min="0" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="已支付金额（不含税）" name="yzfje_bhs">
                  <t-input-number v-model="formData.yzfje_bhs" placeholder="请输入已支付不含税金额" :min="0" />
                </t-form-item>
              </t-col>
            </t-row>

          </t-form>
        </t-dialog>
    </div>
  </div>
</template>

<script>
// TODO: 启用API接口时取消注释
// import { downloadBlobFile } from '@/core/download';
// import {
//   queryHtxxtzList,
//   queryHtxxtzSum,
//   addHtxxtz,
//   updateHtxxtz,
//   deleteHtxxtz,
//   exportHtxxtz,
//   downloadHtxxtzTemplate,
//   importHtxxtz,
// } from '@/pages/index/api/dwfhtz/htxxtz.js';

import { numberToPrice } from '@/utils/numberToCurrency';
import { formatDate } from '@/pages/index/views/util/tzzxTools.js';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, DeleteIcon, DownloadIcon, FileIcon, UploadIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';

export default {
  name: 'HtxxtzIndex',
  components: {
    SkeletonFrame,
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
    AddIcon,
    DeleteIcon,
    FileIcon,
    UploadIcon,
  },
  data() {
    return {
      loading: true,
      tableLoading: false,
      dialogVisible: false,
      dialogTitle: '新增',
      isEdit: false,
      currentRow: null,
      fromName: '',
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      files: [],
      dcLoading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      querySearchConfig: [
        {
          label: '合同名称',
          key: 'htmc',
          type: 'input',
          placeholder: '请输入合同名称',
        },
        {
          label: '合同编号',
          key: 'htbh',
          type: 'input',
          placeholder: '请输入合同编号',
        },
        {
          label: '合同执行时间起',
          key: 'htzxsj_q',
          type: 'date',
          placeholder: '请选择合同执行起始时间',
        },
        {
          label: '合同执行时间止',
          key: 'htzxsj_z',
          type: 'date',
          placeholder: '请选择合同执行终止时间',
        },
        {
          label: '非居民企业身份码',
          key: 'fjmqysfm',
          type: 'input',
          placeholder: '请输入非居民企业身份码',
        },
      ],
      querySearchConfigOneRules: {},
      dataColumns: [
        { colKey: 'xh', title: '序号', width: 60, align: 'center' },
        { colKey: 'htmc', title: '合同名称', width: 180, ellipsis: true },
        { colKey: 'htbh', title: '合同编号', width: 140, ellipsis: true },
        { colKey: 'htzxqssj', title: '合同执行起始时间', width: 120, align: 'center' },
        { colKey: 'htzxzzsj', title: '合同执行终止时间', width: 120, align: 'center' },
        { colKey: 'fjmqysfm', title: '非居民企业身份码', width: 140, ellipsis: true },
        { colKey: 'skfmc', title: '收款方名称', width: 150, ellipsis: true },
        { colKey: 'cjrq', title: '采集日期', width: 100, align: 'center' },
        { colKey: 'htzje', title: '合同总金额', width: 120, align: 'right' },
        { colKey: 'htzbz', title: '合同总金额币种', width: 80, align: 'center' },
        { colKey: 'htzje_hs', title: '合同总金额（含税）', width: 130, align: 'right' },
        { colKey: 'htzje_bhs', title: '合同总金额（不含税）', width: 130, align: 'right' },
        { colKey: 'yzfje_hs', title: '已支付金额（含税）', width: 130, align: 'right' },
        { colKey: 'yzfje_bhs', title: '已支付金额（不含税）', width: 130, align: 'right' },
        { colKey: 'sfycj', title: '是否已采集', width: 100, align: 'center' },
        { colKey: 'sfywj', title: '是否已完结', width: 100, align: 'center' },
        { colKey: 'operation', title: '操作', width: 200, align: 'center', fixed: 'right' },
      ],
      formRules: {
        htmc: [{ required: true, message: '请输入合同名称', type: 'error' }],
        htbh: [{ required: true, message: '请输入合同编号', type: 'error' }],
        htzxqssj: [{ required: true, message: '请选择合同执行起始时间', type: 'error' }],
        htzxzzsj: [{ required: true, message: '请选择合同执行终止时间', type: 'error' }],
        fjmqysfm: [{ required: true, message: '请输入非居民企业身份码', type: 'error' }],
        skfmc: [{ required: true, message: '请输入收款方名称', type: 'error' }],
        htzje: [{ required: true, message: '请输入合同总金额', type: 'error' }],
        htzbz: [{ required: true, message: '请选择合同总金额币种', type: 'error' }],
        htzje_hs: [{ required: true, message: '请输入合同总金额（含税）', type: 'error' }],
        htzje_bhs: [{ required: true, message: '请输入合同总金额（不含税）', type: 'error' }],
      },
    };
  },
  methods: {
    numberToPrice,
    formatDate,
    htlxChange(val, item) {
      // 处理下拉选择变化
      console.log('选择变化:', val, item);
    },
    rowClassName() {
      // 可以根据需要添加行样式
      return '';
    },

    async query(pm = { flag: false }) {
      const { flag } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;

      // TODO: 替换为实际的API调用
      // let params = {
      //   djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      //   nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      //   nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      //   pageNo: this.pagination.current,
      //   pageSize: this.pagination.pageSize,
      // };
      // params = {
      //   ...this.formData,
      //   ...params,
      // };

      try {
        // TODO: 替换为实际的API调用
        // const { data } = await queryHtxxtzList(params);
        // this.tableData = data.list || [];
        // this.pagination.total = data.total;

        // 使用模拟数据
        await this.mockQuery();

        // TODO: 查询合计数据
        // if (this.pagination.total > 0) {
        //   const { data } = await queryHtxxtzSum(params);
        //   this.footData = [
        //     {
        //       htje: numberToPrice(data?.htje),
        //     },
        //   ] || [];
        // } else {
        //   this.footData = [];
        // }
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    mockQuery() {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟数据 - 符合新字段结构的合同信息
          this.tableData = [
            {
              uuid: '1',
              htmc: '软件开发服务合同',
              htbh: 'HT202410001',
              htzxqssj: '2024-10-01',
              htzxzzsj: '2024-12-31',
              fjmqysfm: 'FJQY202410001',
              skfmc: '北京科技有限公司',
              cjrq: '2024-10-15',
              htzje: 500000.00,
              htzbz: 'CNY',
              htzje_hs: 565000.00,
              htzje_bhs: 500000.00,
              yzfje_hs: 282500.00,
              yzfje_bhs: 250000.00,
              sfycj: '已采集',
              sfywj: '未完结',
            },
            {
              uuid: '2',
              htmc: '技术咨询服务合同',
              htbh: 'HT202410002',
              htzxqssj: '2024-10-15',
              htzxzzsj: '2024-11-30',
              fjmqysfm: 'FJQY202410002',
              skfmc: '上海信息技术公司',
              cjrq: '2024-10-20',
              htzje: 200000.00,
              htzbz: 'CNY',
              htzje_hs: 226000.00,
              htzje_bhs: 200000.00,
              yzfje_hs: 226000.00,
              yzfje_bhs: 200000.00,
              sfycj: '已采集',
              sfywj: '已完结',
            },
            {
              uuid: '3',
              htmc: '国际技术转让合同',
              htbh: 'HT202410003',
              htzxqssj: '2024-11-01',
              htzxzzsj: '2025-01-31',
              fjmqysfm: 'FJQY202410003',
              skfmc: 'Tech Solutions Inc.',
              cjrq: '2024-10-25',
              htzje: 100000.00,
              htzbz: 'USD',
              htzje_hs: 113000.00,
              htzje_bhs: 100000.00,
              yzfje_hs: 0.00,
              yzfje_bhs: 0.00,
              sfycj: '采集中',
              sfywj: '未完结',
            },
            {
              uuid: '4',
              htmc: '系统维护服务合同',
              htbh: 'HT202410004',
              htzxqssj: '2024-11-01',
              htzxzzsj: '2025-10-31',
              fjmqysfm: 'FJQY202410004',
              skfmc: '深圳智能科技公司',
              cjrq: '2024-10-30',
              htzje: 150000.00,
              htzbz: 'CNY',
              htzje_hs: 169500.00,
              htzje_bhs: 150000.00,
              yzfje_hs: 84750.00,
              yzfje_bhs: 75000.00,
              sfycj: '未采集',
              sfywj: '未完结',
            },
            {
              uuid: '5',
              htmc: '云服务技术支持合同',
              htbh: 'HT202411001',
              htzxqssj: '2024-11-05',
              htzxzzsj: '2025-05-04',
              fjmqysfm: 'FJQY202411001',
              skfmc: '广州云计算公司',
              cjrq: '2024-11-05',
              htzje: 80000.00,
              htzbz: 'CNY',
              htzje_hs: 90400.00,
              htzje_bhs: 80000.00,
              yzfje_hs: 45200.00,
              yzfje_bhs: 40000.00,
              sfycj: '已采集',
              sfywj: '未完结',
            },
          ];
          this.pagination.total = 5;
          resolve();
        }, 800); // 减少模拟延迟时间
      });
    },
    newOrEditRow(row) {
      if (row) {
        this.isEdit = true;
        this.dialogTitle = '编辑合同';
        this.currentRow = row;
        this.formData = { ...row };
      } else {
        this.isEdit = false;
        this.dialogTitle = '新增合同';
        this.currentRow = null;
        this.formData = {};
      }
      this.dialogVisible = true;
    },
    extractContract() {
      // TODO: 替换为实际的API调用
      // const params = {
      //   djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      //   nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      //   // 其他提取参数
      // };
      // await extractContractData(params);

      // 模拟提取合同操作
      console.log('提取合同数据');
      this.$message.success('合同数据提取成功');
      this.query();
    },
    delRow(row) {
      if (row) {
        const confirmDia = this.$dialog({
          header: '警示',
          body: '请确认是否删除该明细',
          onConfirm: async () => {
            try {
              // TODO: 替换为实际的API调用
              // await deleteHtxxtz([row.uuid]);

              // 模拟删除操作
              console.log('删除合同:', row);
              this.$message.success('删除成功');
              this.query();
            } catch (error) {
              console.error('删除失败:', error);
              this.$message.error('删除失败');
            } finally {
              confirmDia.hide();
            }
          },
          onClose: () => {
            confirmDia.hide();
          },
        });
      } else if (this.selectedRowKeys.length > 0) {
        const confirmDia = this.$dialog({
          header: '警示',
          body: `请确认是否删除选中的${this.selectedRowKeys.length}条记录`,
          onConfirm: async () => {
            try {
              // TODO: 替换为实际的API调用
              // await deleteHtxxtz(this.selectedRowKeys);

              // 模拟批量删除操作
              console.log('批量删除合同:', this.selectedRowKeys);
              this.$message.success(`成功删除${this.selectedRowKeys.length}条记录`);
              this.selectedRowKeys = [];
              this.query();
            } catch (error) {
              console.error('批量删除失败:', error);
              this.$message.error('批量删除失败');
            } finally {
              confirmDia.hide();
            }
          },
          onClose: () => {
            confirmDia.hide();
          },
        });
      } else {
        this.$message.warning('请选择要删除的记录');
      }
    },
    invalidateRow(row) {
      const confirmDia = this.$dialog({
        header: '警示',
        body: '请确认是否作废该合同',
        onConfirm: async () => {
          try {
            // TODO: 替换为实际的API调用
            // await invalidateHtxxtz(row.uuid);

            // 模拟作废操作
            console.log('作废合同:', row);
            this.$message.success('合同作废成功');
            this.query();
          } catch (error) {
            console.error('作废失败:', error);
            this.$message.error('作废失败');
          } finally {
            confirmDia.hide();
          }
        },
        onClose: () => {
          confirmDia.hide();
        },
      });
    },
    viewPaymentDetail(row) {
      this.$emit('openPage', { type: 'htzfmx', data: { htbh: row.htbh }, from: 'htxxtz' });
    },
    async handleConfirm() {
      try {
        const result = await this.$refs.formRef.validate();
        if (result === true) {
          // TODO: 替换为实际的API调用
          // if (this.isEdit) {
          //   await updateHtxxtz(this.formData);
          // } else {
          //   await addHtxxtz(this.formData);
          // }

          // 模拟保存操作
          console.log('保存合同数据:', this.formData);
          const action = this.isEdit ? '更新' : '新增';
          this.$message.success(`${action}成功`);

          this.dialogVisible = false;
          this.query();
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      }
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    downloadTemplate() {
      // TODO: 替换为实际的模板下载API
      // const params = {
      //   templateType: 'htxxtz',
      //   fileName: '合同信息台账导入模板.xlsx'
      // };
      // downloadBlobFile({ baseURL: `/api/template/download`, method: 'get', params });

      console.log('下载模板 - 功能待实现');
      this.$message.info('模板下载功能待实现');
    },
    handleSuccess(response) {
      // TODO: 处理实际的上传响应
      console.log('上传成功:', response);

      // 模拟上传成功处理
      this.$message.success('导入成功');
      this.files = [];
      this.query();
    },
    handleFail(error) {
      console.log('上传失败:', error);
      this.$message.error('上传失败，请检查文件格式');
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }

      this.dcLoading = true;

      try {
        // TODO: 替换为实际的导出API调用
        // const djParam = {
        //   djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        //   nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        //   nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        //   pageNo: this.pagination.current,
        //   pageSize: this.pagination.pageSize,
        // };
        // const params = {
        //   yzpzzlDm: 'BDA0610795', // 合同信息台账导出编码
        //   tzlx: 'htxxtz',
        //   fileName: '合同信息台账导出',
        //   cxParam: {
        //     ...this.formData,
        //     ...djParam,
        //   },
        // };
        // if (isAll === 'all') {
        //   params.cxParam.pageNo = 1;
        //   params.cxParam.pageSize = 1000000;
        // }
        // const res = await downloadBlobFile({
        //   baseURL: `/gyExport/exportTz`,
        //   method: 'post',
        //   data: params
        // });
        // if (res.status === 200) {
        //   this.$message.success('导出成功');
        // } else {
        //   this.$message.error('导出失败');
        // }

        // 模拟导出操作
        const exportType = isAll === 'all' ? '所有页' : '当前页';
        console.log(`导出${exportType}数据:`, this.tableData);

        // 模拟导出延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        this.$message.success(`${exportType}数据导出成功`);

      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      } finally {
        this.dcLoading = false;
      }
    },
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    init() {
      // 初始化页面数据
      this.query();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}

/deep/.active-row > td {
  background: #d8e6fa !important;
}
.dialogMiddleCss {
  /deep/ .t-dialog--default {
    width: 750px !important;
    height: 30% !important;
  }
  /deep/ .t-dialog__body .nr {
    height: 80% !important;
  }
}
</style>
