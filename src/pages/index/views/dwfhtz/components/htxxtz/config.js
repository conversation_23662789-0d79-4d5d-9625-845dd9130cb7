/**
 * @Descripttion: 合同信息台账配置
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 */

export const querySearchConfig = [
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入合同编号',
    clearable: true,
  },
  {
    label: '合同名称',
    key: 'htmc',
    type: 'input',
    placeholder: '请输入合同名称',
    clearable: true,
  },
  {
    label: '对方公司名称',
    key: 'dfgsmc',
    type: 'input',
    placeholder: '请输入对方公司名称',
    clearable: true,
  },
  {
    label: '合同签订日期起',
    key: 'htqdrqq',
    type: 'datepicker',
    placeholder: '请选择开始日期',
    clearable: true,
    relation: 'htqdrqz',
    timeRange: 'start',
  },
  {
    label: '合同签订日期止',
    key: 'htqdrqz',
    type: 'datepicker',
    placeholder: '请选择结束日期',
    clearable: true,
    timeRange: 'end',
    relation: 'htqdrqq',
  },
  {
    label: '币种',
    key: 'bz',
    type: 'select',
    placeholder: '请选择币种',
    clearable: true,
    selectList: [
      { value: 'CNY', label: '人民币' },
      { value: 'USD', label: '美元' },
      { value: 'EUR', label: '欧元' },
      { value: 'JPY', label: '日元' },
    ],
  },
];

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 60,
  },
  {
    width: 80,
    align: 'center',
    colKey: 'xh',
    title: '序号',
  },
  {
    width: 150,
    colKey: 'htbh',
    title: '合同编号',
    ellipsis: true,
  },
  {
    width: 200,
    colKey: 'htmc',
    title: '合同名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'htje',
    title: '合同金额',
    align: 'right',
  },
  {
    width: 120,
    colKey: 'htqdrq',
    title: '合同签订日期',
    align: 'center',
  },
  {
    width: 200,
    colKey: 'dfgsmc',
    title: '对方公司名称',
    ellipsis: true,
  },
  {
    width: 180,
    colKey: 'dftyshxydm',
    title: '对方统一社会信用代码',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'bz',
    title: '币种',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'hl',
    title: '汇率',
    align: 'right',
  },
  {
    width: 200,
    colKey: 'operation',
    title: '操作',
    align: 'center',
    fixed: 'right',
  },
];

export const formRules = {
  htbh: [{ required: true, message: '请输入合同编号', type: 'error' }],
  htmc: [{ required: true, message: '请输入合同名称', type: 'error' }],
  htje: [{ required: true, message: '请输入合同金额', type: 'error' }],
  htqdrq: [{ required: true, message: '请选择合同签订日期', type: 'error' }],
  dfgsmc: [{ required: true, message: '请输入对方公司名称', type: 'error' }],
  dftyshxydm: [{ required: true, message: '请输入对方统一社会信用代码', type: 'error' }],
  bz: [{ required: true, message: '请选择币种', type: 'error' }],
  hl: [{ required: true, message: '请输入汇率', type: 'error' }],
};
