/**
 * @Descripttion: 合同支付明细配置
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 */

export const querySearchConfig = [
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入合同编号',
    clearable: true,
  },
  {
    label: '合同名称',
    key: 'htmc',
    type: 'input',
    placeholder: '请输入合同名称',
    clearable: true,
  },
  {
    label: '支付状态',
    key: 'zfzt',
    type: 'select',
    placeholder: '请选择支付状态',
    clearable: true,
    selectList: [
      { value: '1', label: '已支付' },
      { value: '2', label: '待支付' },
      { value: '3', label: '支付中' },
    ],
  },
  {
    label: '支付日期起',
    key: 'zfrqq',
    type: 'datepicker',
    placeholder: '请选择开始日期',
    clearable: true,
    relation: 'zfrqz',
    timeRange: 'start',
  },
  {
    label: '支付日期止',
    key: 'zfrqz',
    type: 'datepicker',
    placeholder: '请选择结束日期',
    clearable: true,
    timeRange: 'end',
    relation: 'zfrqq',
  },
  {
    label: '支付方式',
    key: 'zffs',
    type: 'select',
    placeholder: '请选择支付方式',
    clearable: true,
    selectList: [
      { value: '1', label: '银行转账' },
      { value: '2', label: '现金支付' },
      { value: '3', label: '支票支付' },
      { value: '4', label: '其他' },
    ],
  },
];

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 60,
  },
  {
    width: 80,
    align: 'center',
    colKey: 'xh',
    title: '序号',
  },
  {
    width: 150,
    colKey: 'htbh',
    title: '合同编号',
    ellipsis: true,
  },
  {
    width: 200,
    colKey: 'htmc',
    title: '合同名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'zfje',
    title: '支付金额',
    align: 'right',
  },
  {
    width: 120,
    colKey: 'zfrq',
    title: '支付日期',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'zffs',
    title: '支付方式',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'zfzt',
    title: '支付状态',
    align: 'center',
  },
  {
    width: 80,
    colKey: 'bz',
    title: '币种',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'hl',
    title: '汇率',
    align: 'right',
  },
  {
    width: 150,
    colKey: 'operation',
    title: '操作',
    align: 'center',
    fixed: 'right',
  },
];

export const formRules = {
  htbh: [{ required: true, message: '请选择合同编号', type: 'error' }],
  zfje: [{ required: true, message: '请输入支付金额', type: 'error' }],
  zfrq: [{ required: true, message: '请选择支付日期', type: 'error' }],
  zffs: [{ required: true, message: '请选择支付方式', type: 'error' }],
  zfzt: [{ required: true, message: '请选择支付状态', type: 'error' }],
  bz: [{ required: true, message: '请选择币种', type: 'error' }],
  hl: [{ required: true, message: '请输入汇率', type: 'error' }],
};
