<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增支付明细', '编辑支付明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="合同名称" name="htmc">
              <t-input :maxlength="50" v-model="formData.htmc" placeholder="请填写合同名称" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同编号" name="htbh">
              <t-input :maxlength="30" v-model="formData.htbh" placeholder="请填写合同编号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同执行起始时间" name="htzxqssj">
              <t-date-picker
                v-model="formData.htzxqssj"
                placeholder="请选择合同执行起始时间"
                style="width: 276px; height: 32px"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同执行终止时间" name="htzxzzsj">
              <t-date-picker
                v-model="formData.htzxzzsj"
                placeholder="请选择合同执行终止时间"
                style="width: 276px; height: 32px"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="非居民企业身份码" name="fjmqysfm">
              <t-input :maxlength="30" v-model="formData.fjmqysfm" placeholder="请填写非居民企业身份码" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本次支付金额（含税）" name="bczfje_hs">
              <gt-input-money v-model="formData.bczfje_hs" theme="normal" align="left" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本次支付金额（不含税）" name="bczfje_bhs">
              <gt-input-money v-model="formData.bczfje_bhs" theme="normal" align="left" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="币种" name="bz">
              <t-select v-model="formData.bz" placeholder="请选择币种" clearable>
                <t-option value="CNY" label="人民币" />
                <t-option value="USD" label="美元" />
                <t-option value="EUR" label="欧元" />
                <t-option value="JPY" label="日元" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="支付日期" name="zfrq">
              <t-date-picker
                v-model="formData.zfrq"
                placeholder="请选择支付日期"
                style="width: 276px; height: 32px"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="纳税判断方案" name="nspdfal">
              <t-select v-model="formData.nspdfal" placeholder="请选择纳税判断方案" clearable>
                <t-option value="一般纳税人" label="一般纳税人" />
                <t-option value="小规模纳税人" label="小规模纳税人" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="享受国内优惠政策列表" name="xsgnyhlc">
              <t-input :maxlength="100" v-model="formData.xsgnyhlc" placeholder="请填写享受的优惠政策" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="是否享受税收协定" name="sfxssxzd">
              <t-select v-model="formData.sfxssxzd" placeholder="请选择" clearable>
                <t-option value="是" label="是" />
                <t-option value="否" label="否" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="增值税税费承担" name="zzssfcd">
              <t-select v-model="formData.zzssfcd" placeholder="请选择" clearable>
                <t-option value="境内承担" label="境内承担" />
                <t-option value="境外承担" label="境外承担" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="增值税汇率" name="zzshl">
              <t-input-number v-model="formData.zzshl" placeholder="请输入增值税汇率" :min="0" :decimal-places="2" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="企业所得税税费承担" name="qysdssfcd">
              <t-select v-model="formData.qysdssfcd" placeholder="请选择" clearable>
                <t-option value="境内承担" label="境内承担" />
                <t-option value="境外承担" label="境外承担" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="企业所得税汇率" name="qysdshl">
              <t-input-number v-model="formData.qysdshl" placeholder="请输入企业所得税汇率" :min="0" :decimal-places="2" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="增值税税额" name="zzzse">
              <gt-input-money v-model="formData.zzzse" theme="normal" align="left" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="企业所得税税额" name="qysdse">
              <gt-input-money v-model="formData.qysdse" theme="normal" align="left" />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>

<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
// import { addHtzfmx, updateHtzfmx } from '@/pages/index/api/dwfhtz/htzfmx.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {
        htmc: [{ required: true, message: '请输入合同名称', type: 'error' }],
        htbh: [{ required: true, message: '请输入合同编号', type: 'error' }],
        htzxqssj: [{ required: true, message: '请选择合同执行起始时间', type: 'error' }],
        htzxzzsj: [{ required: true, message: '请选择合同执行终止时间', type: 'error' }],
        fjmqysfm: [{ required: true, message: '请输入非居民企业身份码', type: 'error' }],
        bczfje_hs: [{ required: true, message: '请输入本次支付金额（含税）', type: 'error' }],
        bczfje_bhs: [{ required: true, message: '请输入本次支付金额（不含税）', type: 'error' }],
        bz: [{ required: true, message: '请选择币种', type: 'error' }],
        zfrq: [{ required: true, message: '请选择支付日期', type: 'error' }],
      },
      formData: {
        uuid: '',
        htmc: '',
        htbh: '',
        htzxqssj: '',
        htzxzzsj: '',
        fjmqysfm: '',
        bczfje_hs: '',
        bczfje_bhs: '',
        bz: '',
        zfrq: '',
        nspdfal: '',
        xsgnyhlc: '',
        sfxssxzd: '',
        zzssfcd: '',
        zzshl: '',
        qysdssfcd: '',
        qysdshl: '',
        zzzse: '',
        qysdse: '',
        htxxsfycj: '未采集',
        sfyba: '未备案',
      },
    };
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async init() {
      if (this.visible.row?.uuid) {
        // 编辑模式
        this.formData = { ...this.visible.row };
      } else {
        // 新增模式
        this.formData = {
          uuid: '',
          htmc: '',
          htbh: '',
          htzxqssj: '',
          htzxzzsj: '',
          fjmqysfm: '',
          bczfje_hs: '',
          bczfje_bhs: '',
          bz: '',
          zfrq: '',
          nspdfal: '',
          xsgnyhlc: '',
          sfxssxzd: '',
          zzssfcd: '',
          zzshl: '',
          qysdssfcd: '',
          qysdshl: '',
          zzzse: '',
          qysdse: '',
          htxxsfycj: '未采集',
          sfyba: '未备案',
        };
      }
    },
    async confirm() {
      try {
        const result = await this.$refs.forms.validate();
        if (result === true) {
          this.confirmLoading = true;
          
          // TODO: 替换为实际的API调用
          // if (this.visible.pageType === 1) {
          //   await updateHtzfmx(this.formData);
          // } else {
          //   await addHtzfmx(this.formData);
          // }
          
          // 模拟保存操作
          console.log('保存支付明细数据:', this.formData);
          const action = this.visible.pageType === 1 ? '更新' : '新增';
          this.$message.success(`${action}成功`);
          
          this.$emit('refresh');
          this.onClose();
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.confirmLoading = false;
      }
    },
    onClose() {
      this.isVisible = false;
      this.$emit('close');
    },
  },
};
</script>

<style scoped lang="less">
@import '../../../../../styles/dialog.less';

.dialogCss {
  /deep/ .t-dialog--default {
    width: 1200px !important;
    max-width: 90vw !important;
  }
  
  .nr {
    max-height: 60vh;
    overflow-y: auto;
  }
}
</style>
