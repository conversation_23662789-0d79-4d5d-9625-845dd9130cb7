<!-- eslint-disable no-param-reassign -->
<!--
 * @Descripttion: 台账-合同支付明细
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        @changeSelect="htlxChange"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow"><AddIcon slot="icon" />新增支付明细</t-button>
          <t-button theme="primary" @click="extractPayment"><FileIcon slot="icon" />提取支付明细</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
      </div>

      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
          :rowClassName="rowClassName"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #htzxqssj="{ row }">
            <span>{{ formatDate(row.htzxqssj) }}</span>
          </template>
          <template #htzxzzsj="{ row }">
            <span>{{ formatDate(row.htzxzzsj) }}</span>
          </template>
          <template #zfrq="{ row }">
            <span>{{ formatDate(row.zfrq) }}</span>
          </template>
          <template #bczfje_hs="{ row }">
            <span>{{ numberToPrice(row.bczfje_hs) }}</span>
          </template>
          <template #bczfje_bhs="{ row }">
            <span>{{ numberToPrice(row.bczfje_bhs) }}</span>
          </template>
          <template #zzshl="{ row }">
            <span>{{ row.zzshl }}%</span>
          </template>
          <template #qysdshl="{ row }">
            <span>{{ row.qysdshl }}%</span>
          </template>
          <template #zzzse="{ row }">
            <span>{{ numberToPrice(row.zzzse) }}</span>
          </template>
          <template #qysdse="{ row }">
            <span>{{ numberToPrice(row.qysdse) }}</span>
          </template>
          <template #htxxsfycj="{ row }">
            <t-tag v-if="row.htxxsfycj === '已采集'" theme="success" variant="light-outline">已采集</t-tag>
            <t-tag v-else-if="row.htxxsfycj === '采集中'" theme="primary" variant="light-outline">采集中</t-tag>
            <t-tag v-else theme="default" variant="light-outline">未采集</t-tag>
          </template>
          <template #sfyba="{ row }">
            <t-tag v-if="row.sfyba === '已备案'" theme="success" variant="light-outline">已备案</t-tag>
            <t-tag v-else-if="row.sfyba === '无需备案'" theme="default" variant="light-outline">无需备案</t-tag>
            <t-tag v-else theme="warning" variant="light-outline">未备案</t-tag>
          </template>
          <template #operation="{ row }">
            <t-space size="8px">
              <t-link theme="primary" hover="color" @click="viewPaymentInfo(row)">
                支付信息
              </t-link>
              <t-link theme="primary" hover="color" @click="declarePayment(row)">
                申报
              </t-link>
            </t-space>
          </template>
        </t-table>

      </div>

        <!-- 新增/编辑弹窗 -->
        <edit-dialog
          v-if="dialogVisible"
          :visible="{ pageType: isEdit ? 1 : 0, row: currentRow }"
          @close="handleDialogClose"
          @refresh="handleDialogRefresh"
        />
    </div>
  </div>
</template>
<script>
// TODO: 启用API接口时取消注释
// import { downloadBlobFile } from '@/core/download';
// import {
//   queryHtzfmxList,
//   queryHtzfmxSum,
//   addHtzfmx,
//   updateHtzfmx,
//   deleteHtzfmx,
//   exportHtzfmx,
//   downloadHtzfmxTemplate,
//   importHtzfmx,
// } from '@/pages/index/api/dwfhtz/htzfmx.js';

import { numberToPrice } from '@/utils/numberToCurrency';
import { formatDate } from '@/pages/index/views/util/tzzxTools.js';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, DeleteIcon, DownloadIcon, FileIcon, UploadIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import EditDialog from './components/edit-dialog.vue';

export default {
  name: 'HtzfmxIndex',
  components: {
    SkeletonFrame,
    QsbButton,
    SearchControlPanel,
    DownloadIcon,
    AddIcon,
    DeleteIcon,
    FileIcon,
    UploadIcon,
    EditDialog,
  },
  data() {
    return {
      loading: true,
      tableLoading: false,
      dialogVisible: false,
      isEdit: false,
      currentRow: null,
      selectedRowKeys: [],
      tableData: [],
      dcLoading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      querySearchConfig: [
        {
          label: '合同名称',
          key: 'htmc',
          type: 'input',
          placeholder: '请输入合同名称',
        },
        {
          label: '合同编号',
          key: 'htbh',
          type: 'input',
          placeholder: '请输入合同编号',
        },
        {
          label: '合同执行时间起',
          key: 'htzxsj_q',
          type: 'date',
          placeholder: '请选择合同执行起始时间',
        },
        {
          label: '合同执行时间止',
          key: 'htzxsj_z',
          type: 'date',
          placeholder: '请选择合同执行终止时间',
        },
        {
          label: '非居民企业身份码',
          key: 'fjmqysfm',
          type: 'input',
          placeholder: '请输入非居民企业身份码',
        },
      ],
      querySearchConfigOneRules: {},
      dataColumns: [
        { colKey: 'xh', title: '序号', width: 60, align: 'center' },
        { colKey: 'htmc', title: '合同名称', width: 150, ellipsis: true },
        { colKey: 'htbh', title: '合同编号', width: 120, ellipsis: true },
        { colKey: 'htzxqssj', title: '合同执行起始时间', width: 120, align: 'center' },
        { colKey: 'htzxzzsj', title: '合同执行终止时间', width: 120, align: 'center' },
        { colKey: 'fjmqysfm', title: '非居民企业身份码', width: 140, ellipsis: true },
        { colKey: 'bczfje_hs', title: '本次支付金额（含税）', width: 140, align: 'right' },
        { colKey: 'bczfje_bhs', title: '本次支付金额（不含税）', width: 140, align: 'right' },
        { colKey: 'bz', title: '币种', width: 80, align: 'center' },
        { colKey: 'zfrq', title: '支付日期', width: 100, align: 'center' },
        { colKey: 'nspdfal', title: '纳税判断方案', width: 120, ellipsis: true },
        { colKey: 'xsgnyhlc', title: '享受国内优惠政策列表', width: 160, ellipsis: true },
        { colKey: 'sfxssxzd', title: '是否享受税收协定', width: 120, align: 'center' },
        { colKey: 'zzssfcd', title: '增值税税费承担', width: 120, align: 'center' },
        { colKey: 'zzshl', title: '增值税汇率', width: 100, align: 'right' },
        { colKey: 'qysdssfcd', title: '企业所得税税费承担', width: 140, align: 'center' },
        { colKey: 'qysdshl', title: '企业所得税汇率', width: 120, align: 'right' },
        { colKey: 'zzzse', title: '增值税税额', width: 120, align: 'right' },
        { colKey: 'qysdse', title: '企业所得税税额', width: 120, align: 'right' },
        { colKey: 'htxxsfycj', title: '合同信息是否已采集', width: 140, align: 'center' },
        { colKey: 'sfyba', title: '是否已备案', width: 100, align: 'center' },
        { colKey: 'operation', title: '操作', width: 150, align: 'center', fixed: 'right' },
      ],
    };
  },
  methods: {
    numberToPrice,
    formatDate,
    htlxChange(val, item) {
      // 处理下拉选择变化
      console.log('选择变化:', val, item);
    },
    rowClassName() {
      // 可以根据需要添加行样式
      return '';
    },

    async query(pm = { flag: false }) {
      const { flag } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;

      try {
        // 使用模拟数据
        await this.mockQuery();
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    mockQuery() {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟支付明细数据
          this.tableData = [
            {
              uuid: '1',
              htmc: '软件开发服务合同',
              htbh: 'HT202410001',
              htzxqssj: '2024-10-01',
              htzxzzsj: '2024-12-31',
              fjmqysfm: 'FJQY202410001',
              bczfje_hs: 282500.00,
              bczfje_bhs: 250000.00,
              bz: 'CNY',
              zfrq: '2024-10-15',
              nspdfal: '一般纳税人',
              xsgnyhlc: '高新技术企业优惠',
              sfxssxzd: '是',
              zzssfcd: '境外承担',
              zzshl: 6.00,
              qysdssfcd: '境外承担',
              qysdshl: 6.50,
              zzzse: 32500.00,
              qysdse: 62500.00,
              htxxsfycj: '已采集',
              sfyba: '已备案',
            },
            {
              uuid: '2',
              htmc: '技术咨询服务合同',
              htbh: 'HT202410002',
              htzxqssj: '2024-10-15',
              htzxzzsj: '2024-11-30',
              fjmqysfm: 'FJQY202410002',
              bczfje_hs: 113000.00,
              bczfje_bhs: 100000.00,
              bz: 'CNY',
              zfrq: '2024-10-20',
              nspdfal: '一般纳税人',
              xsgnyhlc: '无',
              sfxssxzd: '否',
              zzssfcd: '境内承担',
              zzshl: 6.00,
              qysdssfcd: '境内承担',
              qysdshl: 6.50,
              zzzse: 13000.00,
              qysdse: 25000.00,
              htxxsfycj: '已采集',
              sfyba: '无需备案',
            },
            {
              uuid: '3',
              htmc: '国际技术转让合同',
              htbh: 'HT202410003',
              htzxqssj: '2024-11-01',
              htzxzzsj: '2025-01-31',
              fjmqysfm: 'FJQY202410003',
              bczfje_hs: 56500.00,
              bczfje_bhs: 50000.00,
              bz: 'USD',
              zfrq: '2024-10-25',
              nspdfal: '一般纳税人',
              xsgnyhlc: '研发费用加计扣除',
              sfxssxzd: '是',
              zzssfcd: '境外承担',
              zzshl: 7.26,
              qysdssfcd: '境外承担',
              qysdshl: 7.26,
              zzzse: 6500.00,
              qysdse: 12500.00,
              htxxsfycj: '采集中',
              sfyba: '未备案',
            },
          ];
          this.pagination.total = 3;
          resolve();
        }, 800);
      });
    },
    newOrEditRow(row) {
      if (row) {
        this.isEdit = true;
        this.currentRow = row;
      } else {
        this.isEdit = false;
        this.currentRow = null;
      }
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
    },
    handleDialogRefresh() {
      this.dialogVisible = false;
      this.query();
    },
    extractPayment() {
      // TODO: 替换为实际的API调用
      console.log('提取支付明细数据');
      this.$message.success('支付明细数据提取成功');
      this.query();
    },
    viewPaymentInfo(row) {
      // TODO: 实现支付信息查看功能
      console.log('查看支付信息:', row);
      this.$message.info('支付信息查看功能待实现');
    },
    declarePayment(row) {
      // TODO: 实现申报功能
      console.log('申报支付明细:', row);
      this.$message.info('申报功能待实现');
    },

    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }

      this.dcLoading = true;

      try {
        // 模拟导出操作
        const exportType = isAll === 'all' ? '所有页' : '当前页';
        console.log(`导出${exportType}数据:`, this.tableData);

        // 模拟导出延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
        this.$message.success(`${exportType}数据导出成功`);

      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      } finally {
        this.dcLoading = false;
      }
    },
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    init() {
      // 初始化页面数据
      this.query();
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}

/deep/.active-row > td {
  background: #d8e6fa !important;
}
.dialogMiddleCss {
  /deep/ .t-dialog--default {
    width: 750px !important;
    height: 30% !important;
  }
  /deep/ .t-dialog__body .nr {
    height: 80% !important;
  }
}
</style>