/**
 * @Descripttion: 纳税义务判断台账配置
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 */

export const querySearchConfig = [
  {
    label: '方案名称',
    key: 'famc',
    type: 'input',
    placeholder: '请输入方案名称',
    clearable: true,
  },
  {
    label: '方案适用范围',
    key: 'fasylx',
    type: 'select',
    placeholder: '请选择方案适用范围',
    clearable: true,
    selectList: [
      { value: '0', label: '公用' },
      { value: '1', label: '私用' },
    ],
  },
];

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    width: 60,
  },
  {
    width: 60,
    align: 'center',
    colKey: 'xh',
    title: '序号',
  },
  {
    width: 140,
    colKey: 'zffyyjlx',
    title: '支付费用一级类型',
    ellipsis: true,
  },
  {
    width: 140,
    colKey: 'zffyejlx',
    title: '支付费用二级类型',
    ellipsis: true,
  },
  {
    width: 150,
    colKey: 'famc',
    title: '方案名称',
    ellipsis: true,
  },
  {
    width: 150,
    colKey: 'fasylx',
    title: '方案适用范围',
    align: 'center',
  },
  {
    title: '代扣代缴判断结果',
    align: 'center',
    children: [
      { colKey: 'qysdssl', title: '企业所得税税率', width: 100, align: 'center' },
      { colKey: 'zzssl', title: '增值税税率', width: 100, align: 'center' },
      { colKey: 'whsyjsfsl', title: '文化事业建设费税率', width: 120, align: 'center' },
      { colKey: 'yhssl', title: '印花税税率', width: 100, align: 'center' },
      { colKey: 'fcssl', title: '房产税税率', width: 100, align: 'center' },
    ],
  },
  {
    width: 150,
    colKey: 'operation',
    title: '操作',
    align: 'center',
    fixed: 'right',
  },
];

export const formRules = {
  zffyyjlx: [{ required: true, message: '请输入支付费用一级类型', type: 'error' }],
  zffyejlx: [{ required: true, message: '请输入支付费用二级类型', type: 'error' }],
  famc: [{ required: true, message: '请输入方案名称', type: 'error' }],
  fasylx: [{ required: true, message: '请选择方案适用范围', type: 'error' }],
};
