<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="minbox">
      <div class="tzzt">
        <gt-collapse-menu
          class="ggMenu"
          theme="primary"
          title="台账列表"
          :list="dynamicList"
          expandMutex
          :defaultExpanded="expanded"
          :default-value="defaultValue"
          :toolbar="false"
          :value="active"
          @change="collapseHandle"
        >
          <template #panel>
            <dsnsywpd ref="dsnsywpd" v-show="active === 'dsnsywpd'" @openPage="openPage" />
            <htxxtz ref="htxxtz" v-show="active === 'htxxtz'" @openPage="openPage" />
            <htzfmx ref="htzfmx" v-show="active === 'htzfmx'" @openPage="openPage" />
          </template>
        </gt-collapse-menu>
      </div>
    </div>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import dsnsywpd from '@/pages/index/views/dwfhtz/components/dsnsywpd/index.vue';
import htxxtz from '@/pages/index/views/dwfhtz/components/htxxtz/index.vue';
import htzfmx from '@/pages/index/views/dwfhtz/components/htzfmx/index.vue';

import { CollapseMenu } from '@gt4/common-front';
import { initComputeSszq, isProductEnv, isYsEnv } from '@/pages/index/views/util/tzzxTools.js';

export default {
  components: { GtCollapseMenu: CollapseMenu, dsnsywpd, htxxtz, htzfmx, Mybreadcrumb },
  data() {
    return {
      expanded: ['1'],
      defaultValue: 'dsnsywpd',
      active: 'dsnsywpd',
      sbrwmxbz: this.$route.query.sbrwmxBz || false,
      sbrwmxAbbbz: this.$route.query.sbrwmxAbbBz || false,
    };
  },
  async created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    console.log('userInfo', userInfo);
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    initComputeSszq();
    isProductEnv();
    isYsEnv();
    
  },
  computed: {
    isYsEnv() {
      return this.$store.state.isYs.envValue;
    },
    dynamicList() {
      const baseItems = [
        {
          id: 'dsnsywpd',
          title: '纳税义务判断台账',
          required: false,
        },
        {
          id: 'htxxtz',
          title: '合同信息台账',
          required: false,
        },
        {
          id: 'htzfmx',
          title: '合同支付明细',
          required: false,
        },
      ];

      return [
        {
          id: '1',
          title: '对外付汇台账',
          children: [...baseItems],
        },
      ];
    },
  },
  methods: {
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true };
      this.$refs[val].query(params);
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true };
      this.$refs[this.active].query(params);
    },
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) this.$refs[type].query({ flag, p: data, from });
      this.active = type;
    },
  },
  mounted() {
    const parmObjDhcd = {
      mybreadList: ['首页', '对外付汇台账'],
      iframeGoBack: true,
      isCanGoBack: true,
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    
    const parmObjSbrw = {
      mybreadList: ['首页', '申报概览', '税费申报', '对外付汇台账'],
      iframeGoBack: false,
      isCanGoBack: true,
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmx'],
      goBackPath: '/znsb/view/nssb/sbrwmx',
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    
    const parmObjSbrwAbb = {
      mybreadList: ['首页', '申报概览', '税费申报', '对外付汇台账'],
      iframeGoBack: false,
      isCanGoBack: true,
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmxAbb'],
      goBackPath: '/znsb/view/nssb/sbrwmxAbb',
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    
    if (this.sbrwmxbz) {
      this.$refs.myBre.initMyBre(parmObjSbrw);
    } else if (this.sbrwmxAbbbz) {
      this.$refs.myBre.initMyBre(parmObjSbrwAbb);
    } else {
      this.$refs.myBre.initMyBre(parmObjDhcd);
    }
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  height: 100vh;
  &-content {
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
  /deep/.gt-collapse-menu-content-unfold {
    margin-left: 0 !important;
  }
  /deep/.gt-collapse-menu-sidebar-unfold {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content .t-default-menu {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar {
    display: block;
  }
}
.tzzt {
  width: 100%;
}
.ggMenu {
  border-left: 1px solid #eee;
  /deep/.gt-collapse-menu-sidebar-header__title {
    font-size: 14px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header {
    line-height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header-unfold {
    height: 55px !important;
  }
  /deep/.t-menu__item {
    height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content {
    height: calc(100% - 55px) !important;
  }
}
</style>
