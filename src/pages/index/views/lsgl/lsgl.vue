<template>
  <div>
    <!-- <Mybreadcrumb ref="myBre" style="width: 100%; background-color: #fff" /> -->
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid" />
    <div style="height: 682px; padding: 10px; margin-bottom: 10px; background-color: rgb(238, 238, 238)">
      <div class="lsgl">
        <div
          style="
            display: flex;
            padding-top: 30px;
            padding-right: 24px;
            padding-bottom: 12px;
            padding-left: 24px;
            justify-content: space-between;
          "
        >
          <div class="cx" style="display: flex; width: 60%; align-items: center">
            <div style="width: 100px; margin-right: 12px">所属账期</div>
            <t-date-picker
              class="userselectform"
              v-model="sszq"
              placeholder="请选择所属账期"
              mode="month"
              able-time-picker
              allow-input
              clearable
            />
            <div style="margin-right: 12px" />
            <div style="width: 100px; margin-right: 12px">台账类型</div>
            <t-select class="userselectform" v-model="tzlxDm" placeholder="请选择台账类型" clearable>
              <t-option v-for="item in tzlxList" :value="item.value" :label="item.label" :key="item.value"></t-option>
            </t-select>
          </div>
          <div class="btns">
            <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="query">查询</t-button>
            <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="save"
              >一键生成台账</t-button
            >
          </div>
        </div>
        <div class="tableCon">
          <t-table
            row-key="tid"
            :columns="columns"
            :bordered="false"
            :data="data"
            :rowspanAndColspan="mergeSameRows"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination"
          >
            <template #ztDm="{ row }">
              <div class="options">
                <p v-if="row.ztDm === 'ysc'" class="ysc">已生成</p>
                <p v-if="row.ztDm === 'wsc'" class="wsc">未生成</p>
                <!-- <div class="ysc">已生成</div>
            <div class="wsc">未生成</div> -->
              </div>
            </template>
            <template #option="{ row }">
              <div class="options">
                <div>
                  <span @click="openTzym(row)">调整台账</span>
                </div>
                <div>
                  <span @click="openTzym(row)">查看台帐</span>
                </div>
                <div>
                  <span @click="showPdfModal1(row)">我要计提</span>
                </div>
                <div>
                  <span @click="showPdfModal2(row)">查看底稿</span>
                </div>
                <div>
                  <span @click="openSsym(row)">我要试算</span>
                </div>
              </div>
            </template></t-table
          >
          <Modal :visible="isModalVisible1" @close="isModalVisible1 = false">
            <PdfViewer :pdfUrl="pdfUrl1" />
          </Modal>
          <Modal :visible="isModalVisible2" @close="isModalVisible2 = false">
            <PdfViewer :pdfUrl="pdfUrl2" />
          </Modal>

          <!-- <div class="pagination">
        <t-pagination
          v-model="pagination.current"
          show-jumper
          :total="data.length"
          :page-size.sync="pagination.pageSize"
          @current-change="onCurrentChange"
          @page-size-change="onPageSizeChange"
          @change="onChange"
        />
      </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/pages/index/api/tzzx/lsgl/lsgl.js';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import dayjs from 'dayjs';

export default {
  components: { Mybreadcrumb, PdfViewer, Modal },
  data() {
    return {
      sbtzbz: false,
      mrjguuid: JSON.parse(window.sessionStorage.getItem('jgxxList')).jguuid,
      isModalVisible1: false,
      isModalVisible2: false,
      pdfUrl1: `${document.location.origin}/znsb/view/tzzx/${this.jtpdfName}`,
      pdfUrl2: `${document.location.origin}/znsb/view/tzzx/${this.dgpdfName}`,
      jtpdfName: '',
      dgpdfName: '',
      companyList: [],
      // pagination: { defaultPageSize: 10, defaultCurrent: 1, total: 10, current: 1, pageSize: 10 },
      skssqq: '',
      skssqz: '',
      nsrsbh: '',
      djxh: '',
      zsxmDm: '',
      data: [],
      res_data: {},
      selectedRowKeys: [],
      sszq: '',
      tzlxDm: '',
      tzlxList: [
        { value: '01', label: '销项发票总账' },
        { value: '02', label: '即征即退总账' },
        { value: '03', label: '销项发票明细' },
        { value: '04', label: '未开具发票明细' },
        { value: '05', label: '纳税检查调整明细' },
      ],
      ztList: [
        { value: 'ysc', label: '已生成' },
        { value: 'wsc', label: '未生成' },
      ],
    };
  },
  computed: {
    columns() {
      return [
        {
          align: 'center',
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: 50,
        },
        {
          title: '申报表类型',
          colKey: 'sbblxmc',
          width: 160,
          align: 'center',
        },
        {
          title: '台帐类型',
          colKey: 'tzlxmc',
          width: 120,
          align: 'center',
        },
        {
          title: '影响表单',
          colKey: 'yxbd',
          width: 200,
          align: 'center',
        },
        {
          title: '状态',
          colKey: 'ztDm',
          width: 60,
          align: 'center',
        },
        {
          title: '操作',
          colKey: 'option',
          width: 280,
          align: 'center',
        },
      ];
    },
    pagination() {
      return {
        defaultCurrent: 1,
        defaultPageSize: 10,
        total: 0,
        showJumper: true,
        onChange: (pageInfo) => {
          this.pagination.defaultCurrent = pageInfo.current;
          this.pagination.defaultPageSize = pageInfo.pageSize;
          this.query();
          console.log(pageInfo, 'query');
        },
      };
    },
  },
  watch: {},

  created() {
    this.companyList = JSON.parse(window.sessionStorage.getItem('companyList'));
    console.log('companyList', this.companyList);
    if (this.companyList !== null) {
      this.djxh = this.companyList[0].djxh;
      this.nsrsbh = this.companyList[0].nsrsbh;
    }
    this.getRouteData();
    this.initData();
    this.getInitializeDate();
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    const parmObj = {
      mybreadList: ['首页', '理税概览'], // 【必填】面包屑层级
      isCanGoBack: this.sbtzbz, // 【选填】是否可以回退，不传参数则默认不可
      goBackPath: 'znsb/view/nssb/sbrwgl', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: [
      //   { jguuid: '5f5dd426d8ce411db5ccfb99a2490e0c', jgmc: '北京分公司1', nsrsbh: '91310000MA1FL70BCS' },
      //   { jguuid: '5f5dd426d8ce411db5ccfb99a2490e11', jgmc: '北京分公司2', nsrsbh: '91310000MA1FL70BAA' },
      // ], // 【测试用】 自己组装下拉选list
      selectedCompany: this.mrjguuid, // 【测试用】 下拉选默认展示
      xlxShow: true, // 下拉选展示
    };
    this.$refs.myBre.initMyBre(parmObj);
  },
  methods: {
    showPdfModal1(row) {
      this.isModalVisible1 = true;
      this.pdfUrl1 = `${document.location.origin}/znsb/view/tzzx/${row.wyjt}`;
      console.log(this.pdfUrl1, 'pdfUrl1');
    },
    showPdfModal2(row) {
      this.isModalVisible2 = true;
      this.pdfUrl2 = `${document.location.origin}/znsb/view/tzzx/${row.ckdg}`;
      console.log(this.pdfUrl2, 'pdfUrl2');
    },
    openTzym(row) {
      console.log('row', row);
      // this.$router.push(row.tzlj);
      // 自刷新
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/tzzx${row.tzlj}?lsgltzbz=true`; // 路径
        // menuParams.menuParmasStr = '&lsgltzbz=true'; // 参数
        window.parent.goSelfChange(menuParams);
      }
    },
    openSsym(row) {
      console.log('row', row);
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/lzssb/zzsybnsrsb?djxh=${this.djxh}&skssqq=${this.skssqq}&skssqz=${this.skssqz}&nsrsbh=${this.nsrsbh}&zsxmDm=${this.zsxmDm}&lsylbz=Y`;
        console.log('menuParams.menuPath', menuParams.menuPath);
        window.parent.goSelfChange(menuParams);
      }
    },
    getJguuid(data) {
      console.log('下拉选data', data);
      this.nsrsbh = data[0].nsrsbh;
      this.initData();
    },
    mergeSameRows({ row, col, rowIndex, colIndex }) {
      if (colIndex === 1) {
        // 根据第二列（sbblxmc）的值来合并行
        const cells = this.data
          .slice(rowIndex)
          .map((item, index) => ({ ...item, rowIndextemp: rowIndex + index }))
          .filter((item) => item.sbblxmc === row.sbblxmc);
        const firstCell = cells[0];
        if (cells.length > 1 && firstCell.rowIndextemp === rowIndex) {
          return {
            colspan: 0,
            rowspan: cells.length,
          }; // 行合并数，列合并数
        }
      }
      return {
        colspan: 1,
        rowspan: 1,
      };
    },
    getInitializeDate() {
      const now = dayjs();
      this.sszq = now.subtract(1, 'month').format('YYYY-MM');
      this.skssqq = now.subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
      this.skssqz = now.subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
      console.log('当前所属账期', this.sszq);
      console.log('当前税款所属期起', this.skssqq);
      console.log('当前税款所属期止', this.skssqz);
    },
    getRouteData() {
      this.date = this.getNowFormatDate();
      this.skssqq = this.$route.query.skssqq || this.skssqq;
      this.skssqz = this.$route.query.skssqz || this.skssqz;
      this.nsrsbh = this.$route.query.nsrsbh || this.nsrsbh;
      this.djxh = this.$route.query.djxh || this.djxh;
      this.zsxmDm = this.$route.query.zsxmDm || this.zsxmDm;
      this.yzpzzlDm = this.$route.query.yzpzzlDm || this.yzpzzlDm;
      this.sbrwuuid = this.$route.query.sbrwuuid || this.sbrwuuid;
      this.mrjguuid = this.$route.query.jguuid || this.mrjguuid;
      if (this.sbrwuuid !== '') {
        this.sbtzbz = true;
      }
    },
    initData() {
      const data = {
        skssqq: this.skssqq,
        nsrsbh: this.nsrsbh,
        skssqz: this.skssqz,
        djxh: this.djxh,
        zsxmDm: this.zsxmDm,
      };

      api.init(data).then((res) => {
        console.log('res.data.records', res.data.records);
        const data = [];
        let tid = 1;
        res.data.records.forEach((e) => {
          const obj = {
            tid,
            sbblxmc: e.sbblxmc || '',
            tzlxmc: e.tzlxmc || '',
            yxbd: e.yxbd || '',
            ztDm: e.ztDm || '',
            options: e.options || '',
            tzlj: e.tzlj || '',
            wyjt: e.wyjt || '',
            ckdg: e.ckdg || '',
          };
          tid += 1;
          data.push(obj);
        });
        this.data = data;
        this.pagination.total = res.data.pageTotal;
      });
    },
    getNowFormatDate() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month < 10) month = `0${month}`;
      if (strDate < 10) strDate = `0${strDate}`;

      return `${year}-${month}-${strDate}`;
    },
    query() {},
    save() {},
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    onPageSizeChange(size) {
      this.pagination.pageSize = size;
    },
    onCurrentChange(current) {
      this.pagination.current = current;
    },
    onChange(pageInfo) {
      console.log(pageInfo);
    },
  },
};
</script>

<style scoped lang="less">
.lsgl {
  width: 100%;
  background-color: #fff;
  .header {
    width: 100%;
    padding-top: 24px;
    padding-bottom: 29px;
    /deep/.t-input {
      width: 308px;
    }
    .t-form {
      /deep/.t-form__label {
        height: 32px;
        padding-right: 8px;
        font-size: 14px;
        line-height: 32px;
        color: #666;
      }
    }
    .row {
      width: 100%;
    }
  }
  .addBtn {
    margin-left: 24px;
  }
  .tableCon {
    width: 100%;
    height: 100%;
    padding: 16px 24px;
    .options {
      div {
        display: inline-block;
        margin-right: 16px;
        color: #4285f4;
        cursor: pointer;
      }
      .ysc {
        padding: 0 3px;
        color: #18b274;
        text-align: center;
        background: #dbf9ea;
        border: 1px solid #88eabc;
        border-radius: 2px;
      }
      .wsc {
        padding: 0 3px;
        color: #ff9838;
        text-align: center;
        background: #ffefd9;
        border: 1px solid #fbe6c7;
        border-radius: 2px;
      }
    }
  }
}
.pagination {
  margin-top: 17px;
}
.cx /deep/ .t-input.t-is-readonly {
  width: 161px;
}
.cx /deep/ .t-input__wrap {
  width: 161px;
}
</style>
