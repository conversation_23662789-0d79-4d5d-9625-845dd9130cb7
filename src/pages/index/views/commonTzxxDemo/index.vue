<template>
  <div>
    <gt-collapse-menu
      theme="primary"
      title="台账列表"
      :list="list"
      default-value="1"
      :toolbar="false"
      :value="active"
      @change="collapseHandle"
    >
      <template #panel>
        <common-tz :jsonData="jsonData" :djxh="djxh" />
      </template>
    </gt-collapse-menu>
  </div>
</template>

<script>
import commonTz from '@/pages/index/components/common-tz/index.vue';
import { CollapseMenu } from '@gt4/common-front';
import api from '@/pages/index/api/demo/commonTzxxDemo.js';
import { MessagePlugin } from 'tdesign-vue';

export default {
  components: {
    commonTz,
    GtCollapseMenu: CollapseMenu,
  },
  data() {
    return {
      active: '1',
      list: [
        {
          id: '1',
          title: '公共台账',
          tzbm: '1',
          djxh: '10111525000001030001',
        },
        {
          id: '2',
          title: '公共台账2',
          tzbm: 'TZBM000001MAIN',
          djxh: '10111525000001030001',
        },
      ],
      jsonData: {},
      jsonDataCache: {},
      tzbm: '1',
      djxh: '1',
    };
  },
  created() {
    this.getJsonData();
  },
  methods: {
    async getJsonData() {
      // 调接口查页面展示的json数据
      const { code, data, msg } = await api.initTzxxJson({ tzbm: this.tzbm });
      if (code === 1) {
        this.jsonData = data;
        this.jsonDataCache[this.active] = data;
      } else {
        MessagePlugin.error(msg);
      }
    },
    collapseHandle(val) {
      if (val !== this.active) {
        this.active = val;
        if (this.jsonDataCache[val]) {
          this.jsonData = this.jsonDataCache[val];
        } else {
          const activeItem = this.list.filter((item) => item.id === val)[0];
          this.tzbm = activeItem.tzbm;
          this.djxh = activeItem.djxh;
          this.getJsonData();
        }
      }
    },
  },
};
</script>
