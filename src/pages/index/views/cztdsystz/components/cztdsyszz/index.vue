<!-- eslint-disable no-param-reassign -->
<!--
 * @Descripttion: 台账-城镇土地使用税总账
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-07-26 14:45:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    />

    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="scsy"><FileAddIcon slot="icon" />生成税源</t-button>
        <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"><ChartIcon slot="icon" />查看底稿</t-button>
        <t-button variant="outline" theme="primary" @click="check"><UploadIcon slot="icon" />导入</t-button>
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <t-button variant="outline" theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
        <t-button variant="outline" theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        :foot-data="footData"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #operation="{ row }">
          <t-link theme="primary" hover="color" @click="newOrEditRow(row)" v-show="row.sfyscsy === '否'"> 编辑 </t-link>
        </template>
      </t-table>
    </div>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @saveCztdsys="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import { downloadBlobFile } from '@/core/download';
import { syCztdsysSy, initCztdsystzQuery, deleteSelected } from '@/pages/index/api/tzzx/cztdsystz/cztdsystz.js';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, FileAddIcon, ChartIcon, DeleteIcon, DownloadIcon, UploadIcon } from 'tdesign-icons-vue';
import { getCztdsysTdyt, getCztdsysQllx, getXzqhJdxzSwjg } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { querySearchConfig, dataColumns, sfyscsyList } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    SearchControlPanel,
    AddIcon,
    DeleteIcon,
    UploadIcon,
    DownloadIcon,
    FileAddIcon,
    ChartIcon,
    EditDialog,
  },
  data() {
    this.dataColumns = dataColumns;
    this.sfyscsyList = sfyscsyList;
    return {
      userInfo: {},
      editDialogVisible: false,
      isProduct: this.$store.state.isProduct.envValue,
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      boxvisible: false,
      formData: {},
      querySearchConfigOneRules: {
        // tdyt: [{ required: true, message: '必填项', type: 'error' }],
        // qllx: [{ required: true, message: '必填项', type: 'error' }],
        // sfyscsy: [{ required: true, message: '必填项', type: 'error' }],
      },
      selectedRowKeys: [],
      checkBox: [],
      tableData: [],
      delformData: [],
      footData: [],
      uuidList: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      zgswskfjDm: '',
    };
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    async init() {
      this.querySearchConfig[6].selectList = this.sfyscsyList; // 是否已生成税源
      this.initCztdsysTdyt(); // 土地用途下拉菜单
      this.initCztdsysQllx(); // 权力类型下拉菜单
      this.initCztdsysXzqh(); // 初始化行政区划，获取zgswskfjDm
    },
    // 初始化土地坐落地址（行政区划）和街道乡镇
    async initCztdsysXzqh() {
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
      const { data } = await getXzqhJdxzSwjg(params);
      this.tdzldzxzqhDmList = data.DM_GY_XZQH;
      this.tdzldzscjxDmList = data.DM_GY_JDXZ;
      this.zgswskfjDm = data.zgswskfjDm;
    },
    // 初始化土地用途
    async initCztdsysTdyt() {
      const params = { tableName: 'dm_sb_tdyt' };
      const { data } = await getCztdsysTdyt(params);
      this.querySearchConfig[1].selectList = data.map((d) => ({ value: d.tdytDm, label: d.tdytmc }));
    },
    // 初始化权力类型
    async initCztdsysQllx() {
      const params = { tableName: 'dm_sb_tdxz' };
      const { data } = await getCztdsysQllx(params);
      this.querySearchConfig[2].selectList = data.map((d) => ({ value: d.tdxzDm, label: d.tdxzmc }));
    },
    // 增行
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    // 删除-调用后台接口
    async delete(params) {
      try {
        const { msg } = await deleteSelected(params);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    // 删行
    async delRow() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    closeBox() {
      this.boxvisible = false;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    // 查询
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params }; // 起始时间待解决
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
          this.$refs.queryControl.setParams({
            sszq: p.sszq,
            lrzx: p.lrzx,
            srlxDm: p.srlxDm,
            jsfsDm1: p.jsfsDm1,
            zsxmDm1: p.zsxmDm1,
            sl1: String(p.sl1),
            kmdm: p.kmdm,
          });
        }
      } else {
        params = {
          ...this.formData,
          ...params,
        };
      }
      try {
        const { data } = await initCztdsystzQuery(params);
        this.tableData = data.list || [];
        this.pagination.total = data.total;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    // 查看底稿
    check() {
      this.$router.push('/lsgl');
    },
    // 生成税源
    async scsy() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      if (!this.checkBox.length) {
        this.$message.warning('请选择要生成税源的数据');
        return;
      }
      this.checkBox.forEach((item) => {
        this.uuidList.push(item.uuid);
      });
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        uuidList: this.uuidList,
        zgswskfjDm: this.zgswskfjDm,
        sjjg: this.zgswskfjDm,
      };
      try {
        const { msg } = await syCztdsysSy(params);
        this.$message.success(msg);
        this.checkBox = [];
        this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      } finally {
        this.query();
      }
    },
    // 导出
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        // djxh: '10111525000001030001',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610790',
        tzlx: 'cztdsystzxx',
        fileName: '城镇土地使用税台账导出信息',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
