<!-- eslint-disable import/named -->
<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增城镇土地税明细账', '编辑城镇土地税明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="3">
            <t-form-item label="企业名称" name="nsrmc">
              <t-input
                :maxlength="30"
                v-model="formData.nsrmc"
                placeholder="请填写企业名称"
                clearable
                disabled
              ></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="企业税号" name="nsrsbh">
              <t-input
                :maxlength="30"
                v-model="formData.nsrsbh"
                placeholder="请填写企业税号"
                clearable
                disabled
              ></t-input>
            </t-form-item>
          </t-col>
          <!-- <t-col :span="3">
            <t-form-item label="是否已生成税源" name="sfyscsy">
              <t-select v-model="formData.sfyscsy" placeholder="请选择是否已生成税源" disabled>
                <t-option
                  v-for="item in sfyscsyList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col> -->
          <t-col :span="3">
            <t-form-item label="发证机关" name="fzjgmc">
              <t-input :maxlength="30" v-model="formData.fzjgmc" placeholder="请填发证机关" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="不动产权证类型" name="bdcqzlx">
              <t-select v-model="formData.bdcqzlx" placeholder="请选择不动产权证类型" clearable @change="bdcqzlxChange">
                <t-option
                  v-for="item in bdcqzlxList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="不动产权证书号" name="tdsyzbh">
              <t-input
                :maxlength="30"
                v-model="formData.tdsyzbh"
                placeholder="请填写不动产权证书号"
                clearable
              ></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="不动产单元号" name="bdcdyh">
              <t-input :maxlength="30" v-model="formData.bdcdyh" placeholder="请填写不动产单元号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地名称" name="tdmc1">
              <t-input :maxlength="30" v-model="formData.tdmc1" placeholder="请填写土地名称" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="宗地号" name="dh1">
              <t-input :maxlength="30" v-model="formData.dh1" placeholder="请填写宗地号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地坐落地址（行政区划）" name="xzqhszDm">
              <t-select
                v-model="formData.xzqhszDm"
                placeholder="请选择土地坐落地址（行政区划）"
                readonly
                @change="xzqhChange"
              >
                <t-option
                  v-for="item in tdzldzxzqhDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地坐落地址（所处街乡）" name="jdxzDm">
              <t-select
                v-model="formData.jdxzDm"
                placeholder="请选择土地坐落地址（所处街乡）"
                clearable
                @change="jdxzChange"
              >
                <t-option
                  v-for="item in tdzldzscjxDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地位置" name="tdzldz">
              <t-input :maxlength="30" v-model="formData.tdzldz" placeholder="请填写土地位置" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="用途" name="tdytDm">
              <t-select v-model="formData.tdytDm" placeholder="请选择用途类型" clearable>
                <t-option
                  v-for="item in tdytDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地面积" name="zytdmj1">
              <t-input :maxlength="30" v-model="formData.zytdmj1" placeholder="请填写土地面积" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="地价" name="tddj">
              <!-- <t-input :maxlength="30" v-model="formData.tddj" placeholder="请填写地价" clearable></t-input> -->
              <gt-input-money v-model="formData.tddj" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="使用权期限起" name="syqqxq" relation="syqqxz" timeRange="start">
              <t-date-picker
                v-model="formData.syqqxq"
                placeholder="请填写使用权期限起"
                style="width: 276px; height: 32px"
                clearable
                :disableDate="(date) => getDisableDate(date, 'syqqxz', 'start')"
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="使用权期限止" name="syqqxz">
              <t-date-picker
                v-model="formData.syqqxz"
                placeholder="请填写使用权期限止"
                style="width: 276px; height: 32px"
                clearable
                :disableDate="(date) => getDisableDate(date, 'syqqxq', 'end')"
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="权力类型" name="tdxzDm">
              <t-select v-model="formData.tdxzDm" placeholder="请选择权力类型" clearable>
                <t-option
                  v-for="item in qllxDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="权力性质" name="tdqdfsDm">
              <t-select v-model="formData.tdqdfsDm" placeholder="请选择权力性质" clearable>
                <t-option
                  v-for="item in qlxzDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="权利人信息" name="qlr">
              <t-input :maxlength="30" v-model="formData.qlr" placeholder="请填写权利人信息" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="土地等级" name="tddjDm">
              <t-select v-model="formData.tddjDm" placeholder="请选择土地等级" clearable @change="tddjChange">
                <t-option
                  v-for="item in tddjDmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="3">
            <t-form-item label="税额标准" name="dwse">
              <!-- <t-input
                :maxlength="30"
                v-model="formData.dwse"
                placeholder="请填写税额标准"
                clearable
                disabled
              ></t-input> -->
              <gt-input-money v-model="formData.dwse" theme="normal" align="left" clearable disabled />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import {
  getCztdsysQlxz,
  getCztdsysTdyt,
  getCztdsysQllx,
  getXzqhJdxzSwjg,
  getTddjListByXzqh,
} from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { saveCztdsys, getSebzByTddj } from '@/pages/index/api/tzzx/cztdsystz/cztdsystz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      nsrmc: [{ required: true, message: '必填', type: 'error' }],
      nsrsbh: [{ required: true, message: '必填', type: 'error' }],
      // sfyscsy: [{ required: true, message: '必填', type: 'error' }],
      bdcqzlx: [{ required: true, message: '必填', type: 'error' }],
      xzqhszDm: [{ required: true, message: '必填', type: 'error' }],
      jdxzDm: [{ required: true, message: '必填', type: 'error' }],
      tdzldz: [{ required: true, message: '必填', type: 'error' }],
      tdytDm: [{ required: true, message: '必填', type: 'error' }],
      zytdmj1: [{ required: true, message: '必填', type: 'error' }],
      syqqxq: [{ required: true, message: '必填', type: 'error' }],
      syqqxz: [{ required: true, message: '必填', type: 'error' }],
      tdxzDm: [{ required: true, message: '必填', type: 'error' }],
      tdqdfsDm: [{ required: true, message: '必填', type: 'error' }],
      tddjDm: [{ required: true, message: '必填', type: 'error' }],
      dwse: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      zgswskfjDm: '',
      isVisible: true,
      confirmLoading: false,
      zspmDm: '', // 征收品目代码
      rules: {},
      sfyscsyList: [], // 是否已生成税源
      bdcqzlxList: [], // 不动产权证类型下拉菜单
      qlxzDmList: [], // 权力性质下拉菜单
      tdytDmList: [], // 土地用途下拉菜单
      qllxDmList: [], // 权力类型下拉菜单
      tdzldzxzqhDmList: [], // 行政区划下拉菜单
      tdzldzscjxDmList: [], // 街道乡镇下拉菜单
      tddjDmList: [], // 土地等级下拉菜单
      formData: {
        uuid: '',
        nsrmc: '',
        nsrsbh: '',
        // sfyscsy: '',
        fzjgmc: '',
        bdcqzlx: '',
        tdsyzbh: '',
        bdcdyh: '',
        tdmc1: '',
        dh1: '',
        xzqhszDm: '',
        jdxzDm: '',
        tdzldz: '',
        tdytDm: '',
        zytdmj1: '',
        tddj: '',
        syqqxq: '',
        syqqxz: '',
        tdxzDm: '',
        tdqdfsDm: '',
        qlr: '',
        tddjDm: '',
        dwse: '',
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 获取指定月份次月的首末日
    getPreMonthStartEndDay(_date) {
      const time = _date ? new Date(_date).setDate(1) : new Date().setDate(1);
      const date = {};
      date.startDay = dayjs(time).add(1, 'month').startOf('month').format('YYYY-MM-DD');
      date.endDay = dayjs(time).add(1, 'month').endOf('month').format('YYYY-MM-DD');
      return [date.startDay, date.endDay];
    },
    getDisableDate(date, relation, timeRange) {
      const selectData = this.formData[relation];
      if (!selectData) return false;
      const formatDate = dayjs(selectData).hour(0).minute(0).second(0);
      if (timeRange === 'start') {
        // 大于选中结束时间的都不可选
        return date > new Date(formatDate);
      }
      if (timeRange === 'end') {
        // 小于选中开始时间的都不可选
        return date < new Date(formatDate);
      }
      return false;
    },
    async init() {
      // 基本条件
      this.rules = this.baseRules;
      // 获取企业名称和企业税号
      this.formData.nsrmc = this.$store.state.zzstz.userInfo?.jgmc;
      this.formData.nsrsbh = this.$store.state.zzstz.userInfo?.nsrsbh;
      // 是否已生成税源
      this.sfyscsyList = [
        { value: 'Y', label: '是' },
        { value: 'N', label: '否' },
      ];
      // this.formData.sfyscsy = 'N';
      // 不动产权证类型下拉菜单
      this.bdcqzlxList = [
        { value: '0', label: '暂未取得' },
        { value: '1', label: '不动产权证' },
        { value: '2', label: '土地使用证' },
      ];
      // 初始化权力性质
      this.initCztdsysQlxz();
      // 初始化土地用途
      this.initCztdsysTdyt();
      // 初始化权力类型
      this.initCztdsysQllx();
      // 初始化行政区划
      this.initCztdsysXzqh();
      // 从修改入口进入，界面赋值
      if (this.visible.row?.uuid) {
        this.formData = this.visible.row;
        this.initCztdsysTddj();
        this.formData = this.visible.row;
      }
    },
    // 初始化权力性质--需调试-已完成
    async initCztdsysQlxz() {
      const params = { tableName: 'dm_sb_tdqdfs' };
      const { data } = await getCztdsysQlxz(params);
      this.qlxzDmList = data.map((d) => ({ value: d.tdqdfsDm, label: d.tdqdfsmc })); // 注意修改
    },
    // 初始化土地用途
    async initCztdsysTdyt() {
      const params = { tableName: 'dm_sb_tdyt' };
      const { data } = await getCztdsysTdyt(params);
      this.tdytDmList = data.map((d) => ({ value: d.tdytDm, label: d.tdytmc }));
    },
    // 初始化权力类型
    async initCztdsysQllx() {
      const params = { tableName: 'dm_sb_tdxz' };
      const { data } = await getCztdsysQllx(params);
      this.qllxDmList = data.map((d) => ({ value: d.tdxzDm, label: d.tdxzmc }));
    },
    // 初始化土地坐落地址（行政区划）和街道乡镇
    async initCztdsysXzqh() {
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
      const { data } = await getXzqhJdxzSwjg(params);
      this.tdzldzxzqhDmList = data.DM_GY_XZQH;
      this.tdzldzscjxDmList = data.DM_GY_JDXZ;
      this.zgswskfjDm = data.zgswskfjDm;
      if (this.tdzldzxzqhDmList.length > 0) {
        this.formData.xzqhszDm = data.DM_GY_XZQH[0].value;
      }
    },
    // 初始化土地土地等级
    async initCztdsysTddj() {
      const params = {
        xzqhszDm: this.formData.xzqhszDm || '',
        jdxzDm: this.formData.jdxzDm || '',
      };
      const { data } = await getTddjListByXzqh(params);
      this.tddjDmList = data.DM_DJ_TDDJ;
    },
    // 初始化获取税额标准
    async initCztdsysSebz() {
      const params = {
        tddjDm: this.formData.tddjDm || '',
        xzqhszDm: this.formData.xzqhszDm || '',
        jdxzDm: this.formData.jdxzDm || '',
        yxqq: this.getPreMonthStartEndDay(this.formData.syqqxq)[0],
        yxqz: '2099-12-31',
        zgswskfjDm: this.zgswskfjDm,
      };
      const { data } = await getSebzByTddj(params);
      this.zspmDm = data.zspmDm;
      this.formData.dwse = data.dwse;
    },
    onClose() {
      // // 重新查询一下页面
      // this.$emit('saveCztdsys');
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      if (val === true) {
        this.confirmLoading = true;
        const p = {};
        [
          'uuid',
          'nsrmc',
          'nsrsbh',
          // 'sfyscsy',
          'fzjgmc',
          'bdcqzlx',
          'tdsyzbh',
          'bdcdyh',
          'tdmc1',
          'dh1',
          'xzqhszDm',
          'jdxzDm',
          'tdzldz',
          'tdytDm',
          'zytdmj1',
          'tddj',
          'syqqxq',
          'syqqxz',
          'tdxzDm',
          'tdqdfsDm',
          'qlr',
          'tddjDm',
          'dwse',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = { ...p, ...this.visible.otherObj, sfyscsy: 'N', zspmDm: this.zspmDm };
        try {
          await saveCztdsys(params);
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          // 重新查询一下页面
          this.$emit('saveCztdsys');
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    xzqhChange(val) {
      if (val) {
        console.log('xzqhChange-val', val);
      }
      console.log(this.formData);
    },
    jdxzChange() {
      if (this.formData.xzqhszDm && this.formData.jdxzDm) {
        this.initCztdsysTddj();
      }
    },
    // 不动产权证类型变化时
    bdcqzlxChange(val) {
      if (val === '1' || val === '2') {
        // 不动产单元号和不动产证书号为必填项
        this.rules = {
          ...this.baseRules,
          tdsyzbh: [{ required: true, message: '必填', type: 'error' }],
          bdcdyh: [{ required: true, message: '必填', type: 'error' }],
        };
      } else {
        // 不动产单元号和不动产证书号为非必填项
        this.rules = this.baseRules;
      }
    },
    // 土地等级变化
    tddjChange() {
      if (this.formData.xzqhszDm && this.formData.jdxzDm && this.formData.tddjDm) {
        // 获取税额标准
        this.initCztdsysSebz();
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
</style>
