export const querySearchConfig = [
  {
    label: '宗地号',
    key: 'dh1',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '土地用途',
    key: 'tdytDm',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '权力类型',
    key: 'tdxzDm',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '土地位置',
    key: 'tdzldz',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动产权证书号',
    key: 'tdsyzbh',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '不动单元号',
    key: 'bdcdyh',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '是否已生成税源',
    key: 'sfyscsy',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const sfyscsyList = [
  { value: 'Y', label: '是' },
  { value: 'N', label: '否' },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.sfyscsy === '否') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    ellipsis: true,
  },
  {
    width: 200,
    colKey: 'nsrmc',
    title: '企业名称',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>企业名称
          </div>
        );
      return <div style="text-align:left">{row.nsrmc}</div>;
    },
  },
  {
    width: 200,
    colKey: 'nsrsbh',
    title: '企业税号',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>企业税号
          </div>
        );
      return <div style="text-align:left">{row.nsrsbh}</div>;
    },
  },
  {
    width: 120,
    colKey: 'sfyscsy',
    title: '是否生成税源',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>是否生成税源
          </div>
        );
      return <div style="text-align:left">{row.sfyscsy}</div>;
    },
  },
  {
    width: 160,
    colKey: 'fzjgmc',
    title: '发证机关',
  },
  {
    width: 160,
    colKey: 'bdcqzlxMc',
    title: '不动产权证类型',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>不动产权证类型
          </div>
        );
      return <div style="text-align:left">{row.bdcqzlxMc}</div>;
    },
  },
  {
    width: 160,
    colKey: 'tdsyzbh',
    title: '不动产权证书号',
  },
  {
    width: 160,
    colKey: 'bdcdyh',
    title: '不动产单元号',
  },
  {
    width: 120,
    colKey: 'tdmc1',
    title: '土地名称',
  },
  {
    width: 120,
    colKey: 'dh1',
    title: '宗地号',
  },
  {
    width: 200,
    colKey: 'xzqhszMc',
    title: '土地坐落地址（行政区划）',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>土地坐落地址（行政区划）
          </div>
        );
      return <div style="text-align:left">{row.xzqhszMc}</div>;
    },
  },
  {
    width: 200,
    colKey: 'jdxzMc',
    title: '土地坐落地址（所处街乡）',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>土地坐落地址（所处街乡）
          </div>
        );
      return <div style="text-align:left">{row.jdxzMc}</div>;
    },
  },
  {
    width: 180,
    colKey: 'tdzldz',
    title: '土地位置',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>土地位置
          </div>
        );
      return <div style="text-align:left">{row.tdzldz}</div>;
    },
  },
  {
    width: 120,
    colKey: 'tdytMc',
    title: '用途',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>用途
          </div>
        );
      return <div style="text-align:left">{row.tdytMc}</div>;
    },
  },
  {
    width: 80,
    colKey: 'zytdmj1',
    title: '土地面积',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>土地面积
          </div>
        );
      return <div style="text-align:left">{row.zytdmj1}</div>;
    },
  },
  {
    align: 'right',
    width: 100,
    colKey: 'tddj',
    title: '地价',
  },
  {
    width: 120,
    colKey: 'syqqxq',
    title: '使用权期限起',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>使用权期限起
          </div>
        );
      return <div style="text-align:left">{row.syqqxq}</div>;
    },
  },
  {
    width: 120,
    colKey: 'syqqxz',
    title: '使用权期限止',
  },
  {
    width: 100,
    colKey: 'tdxzMc',
    title: '权力类型',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>权力类型
          </div>
        );
      return <div style="text-align:left">{row.tdxzMc}</div>;
    },
  },
  {
    width: 100,
    colKey: 'tdqdfsMc',
    title: '权力性质',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>权力性质
          </div>
        );
      return <div style="text-align:left">{row.tdqdfsMc}</div>;
    },
  },
  {
    width: 100,
    colKey: 'qlr',
    title: '权力人信息',
  },
  {
    width: 100,
    colKey: 'tddjMc',
    title: '土地等级',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>土地等级
          </div>
        );
      return <div style="text-align:left">{row.tddjMc}</div>;
    },
  },
  {
    align: 'right',
    width: 100,
    colKey: 'dwse',
    title: '税额标准',
    render(h, context) {
      const { type, row } = context;
      if (type === 'title')
        return (
          <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
            <span style="color:red">*</span>税额标准
          </div>
        );
      return <div style="text-align:center">{row.dwse}</div>;
    },
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    checkProps: ({ row }) => ({ disabled: !(row.sfyscsy === '否') }),
    width: 70,
    foot: '-',
    fixed: 'right',
  },
];
