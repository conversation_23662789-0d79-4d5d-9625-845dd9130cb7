import { baseStore } from '@gt/components';
import { getBsms } from '@/utils';
import { getNamedSession } from '@/utils/proactor.js';
import { routeParamMap } from './iframe-view/routeMap';
import { requireYsVueYwbms } from './app-wrap/ysConfig.js';

const defaultState = {
  ...baseStore.state,
  // verifyBegin 前置检查接口返回结果
  verifyList: [],
};
const state = {
  ...defaultState,
  Ysqxxid: '',
  sbms: '', // 申报模式
  bsms: '', // 报送模式，用于填表式申报重置时初始化保持一致
  sburl: '', //  前置检查页未通过校验事项点击跳转的url
  sbzxCtl: window.STATIC_ENV_CONFIG.API_PREFIX ? '' : '/sbzx/api/sdsfsgjssb', // 接口前缀
  flagNoExecuteFormula: false,
  ysData: null, // 要素数据（预填/集团办税）
  isVerified: false, // 前置验证完成
  verifyTime: 0, // 前置校验接口耗费时间（单位ms）
  formulas: [{ desc: '', formula: '', id: '1', tips: '', type: '10' }],
};

/**
 * 无需接口请求，只是更新数据时使用mutations函数
 * mutations函数名使用纯大写字母
 */
const mutations = {
  ...baseStore.mutations,
  RESET_STATE: (state, defaultState) => {
    Object.assign(state, defaultState);
  },

  //  重写外层store方法
  UPDATE_PAYLOAD(state, payload) {
    try {
      const keys = Object.keys(payload);
      keys.forEach((key) => {
        state[key] = payload[key];
        //  store中的formData必须和window的formData一致
        if (key === 'formData') {
          window.formData = payload[key];
        }
      });
    } catch (e) {
      throw new Error(`调用UPDATE_PAYLOAD时错误，payload应该是一个对象， 当前payload为：${payload}。\n${e}`);
    }
  },
};

// 获取公共action
function getActions(api) {
  return {
    ...baseStore.getActions(api),
    // 申报前置检查
    async verifyBegin({ state }, params) {
      const start = Date.now();
      const res = await api.verifyBegin(params);
      const verifyTime = Date.now() - start;
      const { body } = res;
      state.verifyList = body;
      state.isVerified = true;
      state.verifyTime = verifyTime;
      res.verifyTime = verifyTime;
      return res;
    },
    // 要素数据（预填/集团办税）
    async YsData({ state, commit }, params) {
      // 集团办税
      const jtbsSession = getNamedSession('jtbsSbuuid', params.jtbsSbuuid);
      // 必须要调用要素接口的用例
      const { ywbm } = state;
      if (!jtbsSession && !requireYsVueYwbms.includes(ywbm)) return false;

      const routeParam = routeParamMap[`/yyzx/${ywbm}/tb/iframe`];
      const obj = {
        ...params,
        ywbm: params?.ywbm?.toLowerCase?.() ?? routeParam?.ywbm,
      };
      const res = await api.YsData(obj);
      if (res.bizCode && res.bizCode !== '00') return false;
      const { body } = res;
      // 提供给填表式引擎的要素数据
      commit('UPDATE_PAYLOAD', { ysData: JSON.stringify(body) });
      return res;
    },
    async getSburl({ state }, params) {
      const { ywbm = '' } = state;
      let sev = '/qysdssb'; //  企业所得税申报
      const fsgjssb = [
        'whsyjsf',
        'cbj',
        'tysb',
        'dllfssrmxcj',
        'fqdqdzcpcljjsb',
        'fssrtysb',
        'sytbsyjsb',
        'kqsyfyjsb',
        'kqsyfndsb',
        'yjtkfxzbjsb',
        'whsyjsfdkdjsb',
      ]; // 非税国际税申报
      if (fsgjssb.includes(ywbm)) {
        sev = '/fsgjssb'; // 非税国际税申报
      }
      let res = '';
      if (sev === '/qysdssb') {
        res = await api.getQysdssbSburl(params);
      } else {
        res = await api.getFsgjssbSburl(params);
      }
      state.sburl = res;
      console.log('getSburl---res : ', res);
      return res;
    },
    //  获取申报模式
    async getSbms({ state, commit }, params) {
      const obj = { ...params };
      const { body } = await api.getSbms(obj);
      if (body) {
        state.sbms = body.sbms;
      }
      return body;
    },
    // 提交表单
    async SubmitForm({ state, commit, dispatch }, params) {
      let zcbw = JSON.stringify(state.formData);
      // 执行21公式
      window.formulaEngine.Calculate2SubmitFormulas();
      let dclbw = JSON.stringify(state.formData);

      if (zcbw === dclbw) {
        // 当zcbw报文和dclbw报文都有时只保存zcbw报文
        dclbw = '';
      }
      // 报送模式
      const bsms = getBsms(true);
      // 渠道来源，0：pc端，1：移动端
      const qdlx = '0';
      // 将特殊字符转为全角字符
      zcbw = zcbw
        .replace(/&/g, '＆')
        .replace(/%/g, '％')
        .replace(/</g, '＜')
        .replace(/>/g, '＞')
        .replace(/\\\\/g, '＼')
        .replace(/\\\\"/g, '＂')
        .replace(/'/g, '＇');

      if (dclbw !== '') {
        dclbw = dclbw
          .replace(/&/g, '＆')
          .replace(/%/g, '％')
          .replace(/</g, '＜')
          .replace(/>/g, '＞')
          .replace(/\\\\/g, '＼')
          .replace(/\\\\"/g, '＂')
          .replace(/'/g, '＇');
      }

      const res = await api.SubmitForm({
        ysqxxid: state.Ysqxxid,
        zcbw,
        dclbw,
        bsms,
        qdlx,
        zcbwVersion: state.otherParams?.zcbwVersion,
        ...params,
      });
      const { body } = res;
      const { pjhs, ysqxxid } = body;
      state.pjhs = pjhs;
      state.Ysqxxid = ysqxxid;
      return res;
    },
  };
}

export default { state, mutations, getActions };
