<template>
  <div>
    <gt-dialog
      header="请选择报告年度"
      width="448px"
      :visible="isInitData"
      @close="setShowDialog(false, true)"
      @cancel="setShowDialog(false, true)"
      @confirm="onConfirm"
    >
      <gt-form labelAlign="top" statusIcon :errorStates="errorStates">
        <t-row>
          <t-col :span="12">
            <gt-form-item requiredMark label="报告年度" name="year">
              <t-date-picker
                v-model="year"
                :disable-date="disableDateRender"
                format="YYYY年"
                mode="year"
                :timePickerProps="{
                  hideDisabledTime: true,
                }"
                placeholder="请选择"
                clearable
                allow-input
                @change="onYearChange"
              />
            </gt-form-item>
          </t-col>
        </t-row>
      </gt-form>
    </gt-dialog>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import apiFn from '@/pages/index/api/common/commonApi.js';
import useSqxzPage from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/common/mixin.js';

export default {
  name: 'sqxz-year-mode',
  mixins: [useSqxzPage()],
  data() {
    return {
      // 校验节点和信息
      idxVm2ErrMsg: {
        year: '请填写报告年度！',
      },
      year: dayjs().subtract(1, 'year').format(), // 报告年度，默认取去年年度
      isInitData: false, // 初始化完成展示弹窗
      qsztDm: '06', // 清算状态代码
      disableDate: {
        // 有效范围从 2016年至当前年上一年（清算状态下到本年）
        before: '2016-01-01',
        after: dayjs().add(-1, 'year').format(),
      },
    };
  },
  async created() {
    await this.getNsrQszt();
    this.isInitData = true;
    // 初始化结束
    typeof afterCreated === 'function' && (await this.afterCreated());
  },
  methods: {
    // 获取纳税人清算状态
    async getNsrQszt() {
      const api = apiFn(`${this.$route.meta.sqxzPre}`);
      const { body } = await api.getNsrQszt();
      const { nsrzt } = body;
      nsrzt === this.qsztDm && this.$set(this.disableDate, 'after', dayjs().add(0, 'year').format());
    },
    onYearChange(value, { dayjsValue: { $y: trueVal } } = { dayjsValue: {} }) {
      this.startDate = value ? `${trueVal}-01-01` : '';
      this.endDate = value ? `${trueVal}-12-31` : '';
      this.errorStates = this.getVerifyResults();
    },
  },
};
</script>
