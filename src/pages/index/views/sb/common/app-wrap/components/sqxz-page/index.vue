<template>
  <div>
    <component
      v-if="showComponent"
      :is="dynamicComponent"
      @startcheck="startcheck"
      @set-show-dialog="setShowComponent"
    ></component>
  </div>
</template>
<script>
import { getYwbmByURL } from '@/utils';
import idxYwbm2SqxzComponent from './config.js';

export default {
  name: 'sqxz-page',
  data() {
    return {
      dynamicComponent: false,
      showComponent: false,
    };
  },
  async created() {
    if (await !this.beforeInit()) return;
    // 加载业务组件前的准备
    await this.initData();
    // 加载业务属期选择组件
    const ywbm = getYwbmByURL();
    if (idxYwbm2SqxzComponent[ywbm]) {
      this.showComponent = true;
      idxYwbm2SqxzComponent[ywbm]().then((module) => {
        this.dynamicComponent = module.default;
      });
    }
  },
  methods: {
    /**
     * 属期选择的通用前置处理逻辑
     * @returns {boolean} 不阻断后续初始化 true
     */
    async beforeInit() {
      const { SssqQ, SssqZ, NsqxDm, GotoForm } = this.$route.query;
      // 综合关联式申报跳转元事项
      if (SssqQ && SssqZ && NsqxDm && GotoForm === 'Y') {
        this.$emit('startcheck');
        return false;
      }
      return true;
    },
    // 轮询等待前摄器预备数据
    async initData() {
      return new Promise((resolve) => {
        let times = 20;
        const isProactorReady = () => {
          times -= 1;
          const { proactorMode: pm } = window;
          if (!pm.SssqQ && times > 0) {
            setTimeout(() => {
              isProactorReady();
            }, 100);
          } else {
            resolve();
          }
        };

        isProactorReady();
      });
    },
    // 通知app-wrap触发前置检查
    startcheck() {
      this.$emit('startcheck');
    },
    setShowComponent(val) {
      this.showComponent = val;
    },
  },
};
</script>
