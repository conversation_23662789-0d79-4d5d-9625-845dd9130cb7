<template>
  <div class="fzrdxx-container">
    <RootListIcon size="16px" />
    <div class="fzrdxx-span"><span>费种认定信息：</span></div>
    <t-link theme="primary" hover="color" @click="goNsrxxcxPage">去查看</t-link>
  </div>
</template>
<script>
import { RootListIcon } from 'tdesign-icons-vue';

export default {
  name: 'Fzrdxx',
  components: {
    RootListIcon,
  },
  methods: {
    goNsrxxcxPage() {
      // 纳税人信息查询
      window.open(`${window.location.origin}/szc/szzh/sjswszzh/spHandler?cdlj=/szzh/zhcx/nsrxxcx`);
    },
  },
};
</script>
<style lang="less" scoped>
.fzrdxx-container {
  display: flex;
  height: 46px;
  padding-left: 12px;
  background: #f9fafd;
  align-items: center;
  justify-content: flex-start;
  .fzrdxx-span {
    margin-left: 3px;
  }
}
</style>
