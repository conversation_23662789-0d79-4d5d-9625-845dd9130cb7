import dayjs from 'dayjs';

// 通用属期选择器框架
export default function useSqxzPage() {
  return {
    data() {
      return {
        errorStates: {}, // 自行拼接校验信息
        idxVm2ErrMsg: {}, // 复写校验提示 {node: msg}
        startDate: '', // 所属期启
        endDate: '', // 所属期止
        disableDate: '', // 自定义禁用日期范围
      };
    },
    computed: {
      disableDateRender() {
        return (
          this.disableDate || {
            after: dayjs().add(0, 'day').format(), // 默认禁用当天后
          }
        );
      },
    },
    methods: {
      // 业务提供额外刷新路由参数
      beforeReplaceRouterQuery() {
        return {};
      },
      // 选择完毕
      onConfirm() {
        this.errorStates = {};
        // 1、必填像校验
        this.errorStates = this.getVerifyResults();
        if (JSON.stringify(this.errorStates) !== '{}') {
          return;
        }
        // 2、修改路由
        const { startDate, endDate, routeQuery, setShowDialog, beforeReplaceRouterQuery } = this;
        // 路由参数约定用大写
        const params = {
          SssqQ: startDate,
          SssqZ: endDate,
          ...beforeReplaceRouterQuery(),
        };
        this.$router.replace({
          query: {
            ...routeQuery,
            ...params,
          },
        });
        // 3、关闭弹框
        setShowDialog(false);
        // 4、通知app-wrap触发前置检查
        this.$emit('startcheck');
      },
      // 生成校验
      getVerifyResults() {
        const errorStates = {};
        const defaultHandler = (errorStates, vm, msg) => {
          if (vm === 'customRange') {
            // 纳税期限为其他的情况下，费款所属期起止必须在同一个年度校验
            const { startDate, endDate } = this;
            if (startDate && endDate) {
              const skssqqYear = startDate.substring(0, 4);
              const skssqzYear = endDate.substring(0, 4);
              if (skssqqYear !== skssqzYear) {
                errorStates.startDate = [{ type: 'error', message: msg }];
              }
            }
          } else {
            !this[vm] && (errorStates[vm] = [{ type: 'error', message: msg }]);
          }
        };
        const { handler4Verify } = this;
        Object.entries(this.idxVm2ErrMsg).forEach(([vm, msg]) => {
          if (typeof handler4Verify === 'function') {
            /**
             * 遍历校验信息时，自定义处理校验结果的生成
             * @param {object} errorStates 校验结果对象
             * @param {string} vm 属性名
             * @param {string} msg 对应的校验信息
             * @param {function} defaultHandler 通用处理回调
             */
            handler4Verify.call(this, errorStates, vm, msg, defaultHandler);
          } else {
            defaultHandler(errorStates, vm, msg);
          }
        });
        return errorStates;
      },
      // 控制弹窗是否显示
      setShowDialog(val, test) {
        this.$emit('set-show-dialog', val);
      },
    },
  };
}
