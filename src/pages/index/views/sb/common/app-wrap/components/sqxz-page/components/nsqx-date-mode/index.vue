<template>
  <div>
    <gt-dialog
      header="请选择"
      width="448px"
      :visible="isInitData"
      @close="setShowDialog(false, true)"
      @cancel="setShowDialog(false, true)"
      @confirm="onConfirm"
    >
      <gt-form labelAlign="top" statusIcon :errorStates="errorStates">
        <t-row>
          <t-col :span="12">
            <gt-form-item requiredMark label="纳费期限" name="nsqxDm">
              <t-select
                clearable
                filterable
                :options="nsqxOptions"
                @change="onNsqxChange"
                @clear="onNsqxClear"
                v-model="nsqxDm"
              />
            </gt-form-item>
          </t-col>
        </t-row>
        <t-row>
          <t-col :span="12">
            <gt-form-item requiredMark label="费款所属期起止" name="startDate">
              <gt-tax-date-range
                :disable-date="disableDateRender"
                :disabled="isDisabled"
                :startDate="startDate"
                :endDate="endDate"
                :mode="getDateMode"
                @change="onSkssqzChange"
              />
            </gt-form-item>
          </t-col>
        </t-row>
        <t-row v-if="showFzrdxx">
          <t-col :span="12">
            <fzrdxx />
          </t-col>
        </t-row>
      </gt-form>
    </gt-dialog>
  </div>
</template>
<script>
import apiFn from '@/pages/index/api/common/commonApi.js';
import { closeWindow } from '@gt/components';
import { getDate } from '@gt4/common-front';
import { strToDateRange } from '@/pages/index/utils';
import fzrdxx from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/components/fzrdxx/index.vue';
import extraDateFunctions from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/common/tools.js';
import useSqxzPage from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/common/mixin.js';
import dayjs from 'dayjs';

export default {
  name: 'sqxz-nsqx-date-mode',
  components: {
    fzrdxx,
  },
  mixins: [useSqxzPage()],
  data() {
    return {
      // 校验节点和信息
      idxVm2ErrMsg: {
        nsqxDm: '请填写纳费期限！',
        startDate: '请填写费款所属期起！',
        endDate: '请填写费款所属期止！',
        customRange: '“纳费期限”为“其他”，【费款所属期起】和【费款所属期止】必须在同一个年度！',
      },
      showFzrdxx: true, // 显示“费种认定信息”：去查看
      nsqxOptions: [], // 纳税期限下拉选项
      nsqxDm: '', // 纳税期限值
      // 纳税期限名值对
      idxNsqxMc2Dm: {
        month: '06', // 月
        quarter: '08', // 季度
        semiannual: '09', // 半年
        year: '10', // 年
        date: '11', // 按次
        custom: '99',
      },
      isDisabled: false, // 禁用所属期选择器
      isInitData: false, // 初始化完成展示弹窗
    };
  },
  computed: {
    // 纳税期限值名对映射
    idxNsqxDm2Mc() {
      return Object.fromEntries(Object.entries(this.idxNsqxMc2Dm).map(([key, value]) => [value, key]));
    },
    // 所属期选择器模式
    getDateMode() {
      return this.idxNsqxDm2Mc[this.nsqxDm] ?? 'date';
    },
  },
  async created() {
    const body = await this.listNsqxDm();
    if (!body || !body.length) return;
    this.isInitData = true;
    // 初始化结束
    typeof this.afterCreated === 'function' && (await this.afterCreated());
  },
  methods: {
    ...extraDateFunctions,
    // 刷新路由时增加纳税期限
    beforeReplaceRouterQuery() {
      const { nsqxDm } = this;
      return {
        NsqxDm: nsqxDm,
      };
    },
    // 特殊处理校验信息
    handler4Verify(errorStates, vm, msg, defaultHandler) {
      // 开始日期和结束日期共用一个校验信息，开始日期的校验要在结束日期之后
      if (vm === 'startDate' && errorStates.startDate) {
        errorStates.startDate = [
          {
            type: 'error',
            message: `${msg}  ${errorStates.startDate[0].message}`,
          },
        ];
      } else if (vm === 'endDate' && errorStates.startDate) {
        errorStates.startDate = [
          {
            type: 'error',
            message: `${errorStates.startDate[0].message}  ${msg}`,
          },
        ];
      } else if (vm === 'customRange' && errorStates.customRange) {
        errorStates.startDate = [
          {
            type: 'error',
            message: `${errorStates.startDate[0].message}  ${msg}`,
          },
        ];
      } else {
        defaultHandler(errorStates, vm, msg);
      }
    },
    // 获取纳税期限
    async listNsqxDm() {
      const { proactorMode: pm } = window;
      const api = apiFn(`${this.routeMeta.sqxzPre}`);
      const { body } = await api.listNsqxDm({
        sssqQ: pm.SssqQ,
        sssqZ: pm.SssqZ,
      });
      const { onNsqxChange, afterListNsqxDmWithSfsfzrd, setShowDialog } = this;
      if (body && body.length > 0) {
        this.nsqxOptions = body;
        const [{ sfsfzrd: isRd, value }] = body;
        // 下拉只有一个值的时候默认选中
        if (body.length === 1) {
          this.nsqxDm = value;
          onNsqxChange();
        }
        // 存在税费种认定的情况（全部都有 sfsfzrd 标志或全无，取一判断）
        if (isRd) {
          const defaultHandler = () => {
            this.$emit('startcheck');
          };
          if (typeof afterListNsqxDmWithSfsfzrd === 'function') {
            /**
             * 税费种已认定时的自定义处理
             * @param {object[]} body 纳税期限信息
             * @param {function} defaultHandler 通用处理回调
             * @returns {[string, string]} date 处理好的值，用于更新所属期选择器
             */
            afterListNsqxDmWithSfsfzrd.call(this, body, defaultHandler);
          } else {
            defaultHandler();
          }
        } else {
          setShowDialog(true);
        }
      } else {
        this.$gtDialog.error({
          header: '提示',
          body: '尊敬的纳税人，您当前属期不存在可申报的项目，无需申报。',
          closeOnOverlayClick: false,
          confirmBtn: '关闭',
          onConfirm: () => {
            closeWindow();
          },
          onClose: () => {
            closeWindow();
          },
        });
      }
      return body;
    },
    // 选择纳税期限
    onNsqxChange(value, context) {
      // @returns {[string, string]} 赋值时间
      const defaultHandler = () => {
        const { idxNsqxMc2Dm, nsqxDm, getLastQuarterRange, getLastHalfYearRange } = this;
        // 根据纳税期限模式计算自动返回的日期
        const idxNsqx2Date = {
          [idxNsqxMc2Dm.month]: () => getDate.getLastMonthRange(),
          [idxNsqxMc2Dm.quarter]: () => getLastQuarterRange(),
          [idxNsqxMc2Dm.semiannual]: () => getLastHalfYearRange(),
          [idxNsqxMc2Dm.year]: () => getDate.getLastYearRange(),
        };
        // 默认取当天
        const nowDate = getDate.getDate(new Date(), 'YYYY-MM-DD');
        return idxNsqx2Date[nsqxDm]?.() ?? [nowDate, nowDate];
      };
      const { handler4NsqxChange, getVerifyResults } = this;
      if (typeof handler4NsqxChange === 'function') {
        /**
         * 自定义处理纳税期限
         * @param {any} value 选择器选择值
         * @param { option?: T, selectedOptions: T[], trigger: SelectValueChangeTrigger; e?: MouseEvent | KeyboardEvent } context 选择器选择详情
         * @param {function} defaultHandler 通用处理回调
         * @returns {[string, string]} date 处理好的值，用于更新所属期选择器
         */
        [this.startDate, this.endDate] = handler4NsqxChange.call(this, value, context, defaultHandler);
      } else {
        [this.startDate, this.endDate] = defaultHandler();
      }
      // 计算费款所属期起止禁用时间
      const nowDate = dayjs().format('YYYY-MM-DD');
      const disabledDate = this.nsqxDm === '99' ? '2099-12-31' : nowDate;
      this.disableDate = {
        before: dayjs('2000-01-01').add(0, 'day').format(),
        after: dayjs(disabledDate).add(0, 'day').format(),
      };
      this.errorStates = getVerifyResults();
    },
    // 清空纳税期限
    onNsqxClear() {
      this.nsqxDm = '';
      this.startDate = '';
      this.endDate = '';
      this.errorStates = this.getVerifyResults();
    },
    // 所属期选择器
    onSkssqzChange(value) {
      let [skssqq, skssqz] = value;
      const { idxNsqxMc2Dm, nsqxDm, idxNsqxDm2Mc, getVerifyResults } = this;
      // 纳税期限为 “按次 半年 其他” 类型时，费款所属期起止不进行自动修正
      const { date, semiannual, custom } = idxNsqxMc2Dm;
      const notChangeList = [date, semiannual, custom];
      if (notChangeList.includes(nsqxDm)) {
        this.startDate = skssqq;
        this.endDate = skssqz;
        this.errorStates = getVerifyResults();
        return;
      }
      const mode = idxNsqxDm2Mc[nsqxDm] ?? 'date';
      // 计算年度或月度或季度的时间范围
      const range = strToDateRange(skssqq, mode);
      [skssqq, skssqz] = range;
      this.startDate = skssqq;
      this.endDate = skssqz;
    },
  },
};
</script>
