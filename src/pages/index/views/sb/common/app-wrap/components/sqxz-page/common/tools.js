import dayjs from 'dayjs';

// 通用处理区间时间选择器的函数
export default {
  // 获取上一个季度的暑期区间
  getLastQuarterRange() {
    const curMonth = dayjs().month();
    let range = [];
    if (curMonth < 3) {
      range = [
        dayjs().add(-1, 'year').month(9).date(1).format('YYYY-MM-DD'),
        dayjs().add(-1, 'year').month(11).endOf('month').format('YYYY-MM-DD'),
      ];
    } else if (curMonth < 6) {
      range = [dayjs().month(0).date(1).format('YYYY-MM-DD'), dayjs().month(2).endOf('month').format('YYYY-MM-DD')];
    } else if (curMonth < 9) {
      range = [dayjs().month(3).date(1).format('YYYY-MM-DD'), dayjs().month(5).endOf('month').format('YYYY-MM-DD')];
    } else if (curMonth < 12) {
      range = [dayjs().month(6).date(1).format('YYYY-MM-DD'), dayjs().month(8).endOf('month').format('YYYY-MM-DD')];
    }
    return range;
  },
  // 获取上一个半年报的暑期区间
  getLastHalfYearRange() {
    const curMonth = dayjs().month();
    let range = [];
    if (curMonth < 6) {
      range = [
        dayjs().add(-1, 'year').month(6).date(1).format('YYYY-MM-DD'),
        dayjs().add(-1, 'year').month(11).endOf('month').format('YYYY-MM-DD'),
      ];
    } else {
      range = [dayjs().month(0).date(1).format('YYYY-MM-DD'), dayjs().month(5).endOf('month').format('YYYY-MM-DD')];
    }
    return range;
  },
};
