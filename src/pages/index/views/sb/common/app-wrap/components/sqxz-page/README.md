# 组件目录说明
```shell
├─common // 选择器模式
│  ├─mixin.js // 公共子组件中通用混入
│  ├─tools.js // 工具类函数
│  └─...   
├─components // 公共子组件
│  ├─nsqx-and-date // 选择纳税期限和所属期
│  │    └─index.vue
│  ├─fzrdxx // 税费种认定信息跳转按钮
│  │    └─index.vue
│  └─...   
├─index.vue // 属期选择外框架，负责子组件映射、公共处理和AppWrap通信
└─config.js // 配置业务和属期选择组件的映射
```

# 如何使用该组件
## 开启入口
### 路由信息配置
对应业务的 router.js 中配置前置校验 AppWrap 页面的 meta 信息。
```javascript
const meta = {
  // 前置检查前的组件。属期选择弹框
  beforeComponent: 'SqxzPage',
  // 属期选择弹框中请求的前缀
  sqxzPre: '/xxx[Service]/ywController',
  ...
};
```

### 属期选择通用组件配置
在 config.js 中配置页面路由 name 和 属期选择组件 的映射关系。

> config.js 路径：`src/pages/index/views/sb/common/app-wrap/components/sqxz-page/config.js`
> name 对应 router.js 中最外层的 name，也就是 vueYwbm

`./components/xxx-mode` 下存储通用模式组件，说明如下：
+ 选纳税期限带出所属期：nsqx-date-mode `() => import('@/pages/index/views/sb/common/app-wrap/components/sqxz-page/components/nsqx-date-mode/index.vue')`
+ 直接选择年度：date-mode `() => import('@/pages/index/views/sb/common/app-wrap/components/sqxz-page/components/year-mode/index.vue')`
+ 业务复写通用模式：`() => import('@/pages/index/views/sb/[ywbm]/common/sqxz.vue')`


## 业务复写自定义逻辑
暂时不存在修改页面元素的场景，可以直接通过如下写法，复写通用组件提供的钩子去做业务个性化处理。
```vue
<script>
import NsqxDateMode from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/components/nsqx-date-mode/index.vue';

export default {
  name: '[ywbm]-sqxz',
  mixins: [NsqxDateMode],
  methods: {
    async afterCreated() {
      ...
    },
    // 自定义：查询纳税期限时返回税费种已认定
    afterListNsqxDmWithSfsfzrd(body, defaultHandler) {
      ...
    },
  },
};
</script>
```

## 复写内容
### 公共部分
1. idxVm2ErrMsg [data] 复写校验提示
> 对象，键值对，key 是节点，value 是该节点对应的校验错误信息
2. beforeReplaceRouterQuery [methods] 跳转路由时携带的额外参数
3. handler4Verify [methods] 遍历 idxVm2ErrMsg 需要校验的元素的循环过程支持自定义

### nsqx-date-mode
1. afterCreated [methods] 初始化完成之后的钩子
2. afterListNsqxDmWithSfsfzrd [methods] 查询完纳税期限后，属于税费种已认定情况的自定义
3. handler4NsqxChange [methods] 修改纳税期限是，解析出起止日期的逻辑自定义

### year-mode
1. afterCreated [methods] 初始化完成之后的钩子