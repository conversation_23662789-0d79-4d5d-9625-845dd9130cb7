<template>
  <div class="app-wrap">
    <template v-if="!routeQuery.Sbuuid && routeMeta.beforeComponent && !startCheck">
      <component :is="routeMeta.beforeComponent" @startcheck="setupCheck"></component>
    </template>
    <template v-else>
      <template v-if="verifyBegin">
        <gt-qzjc v-if="loading" @complete="completeHandler"></gt-qzjc>
        <t-skeleton
          v-else
          :class="`skeleton${!hasInitData ? ' padding' : ''}`"
          :loading="!hasInitData"
          :rowCol="rowCol"
          theme="article"
          animation="gradient"
        >
          <router-view v-if="hasInitData"></router-view>
        </t-skeleton>
      </template>
      <template v-else>
        <t-skeleton
          :class="`skeleton${!hasInitData ? ' padding' : ''}`"
          :loading="!hasInitData"
          :rowCol="rowCol"
          theme="article"
          animation="gradient"
        >
          <router-view v-if="hasInitData"></router-view>
        </t-skeleton>
      </template>
    </template>
  </div>
</template>

<script>
import Vue from 'vue';
import { loadZnhd, redirectHome } from '@gt/components';
import { xwcjToQzjyHandle } from '@/pages/index/utils/xwcjUtils';
import { getBsms } from '@/utils';
import SqxzPage from '@/pages/index/views/sb/common/app-wrap/components/sqxz-page/index.vue';
import GtQzjc from '../qzjc.vue';

export default Vue.component('gt-app-wrap', {
  components: {
    GtQzjc,
    SqxzPage,
  },
  data() {
    return {
      loading: true,
      rowCol: [
        { height: 30 },
        { height: 30 },
        { height: 30 },
        { height: 30 },
        { height: 30 },
        { height: 30 },
        { height: 30 },
        { height: 30 },
      ],
      hasInitData: false, // 是否已经初始化完成
      isGotoForm: false, // 是否跳转填表页
      startCheck: false, // 开始校验
    };
  },
  computed: {
    storeName() {
      if (!this.$route.meta.storeName) this.$gtDialog.error({ body: '路由中的meta必须要有storeName字段' });
      return this.$route.meta.storeName;
    },
    verifyBegin() {
      const { name = '', meta } = this.$route;
      return meta.verifyBegin || name.slice(-3) !== '/jg';
    },
    state() {
      return this.$store.state[this.storeName];
    },
  },
  async created() {
    if (!this.$route.meta.hideZnhd) {
      loadZnhd();
    }
    if (this.routeQuery.Sbuuid || !this.routeMeta.beforeComponent) {
      this.setupCheck();
    }
    // @gt/components 表单引擎需要获取 业务编码进行用例范围判断
    const { name } = this.$route;
    window.ywbm = name?.split('/')[0] ?? '';
  },
  methods: {
    updatePayload(payload) {
      this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, payload);
    },
    // 是否是结果页
    isResultPage() {
      const { name } = this.$route;
      return name.slice(-3) === '/jg';
    },
    // 是否需要校验
    shouldVerify() {
      const { meta = {}, query } = this.$route;
      const { verifyBegin, verifyWithCorrect } = meta;
      const { isVerified } = this.state;
      //  如果是结果页就不刷新
      const isJG = this.isResultPage();
      // url存在Sbuuid（申报uuid）和 Pzxh（批准序号），则当前是更正场景
      // 更正场景下默认不进行前置校验，在meta上配置了verifyWithCorrect为true，则需要进行前置校验
      const isCorrect = !!query.Sbuuid && !!query.Pzxh;
      // url中传入GotoForm=Y，则直接进填表页
      const gotoForm = query.GotoForm === 'Y';
      // 更正场景和GotoForm=Y，都是接进填表页
      this.isGotoForm = gotoForm || isCorrect;
      // 是否需要前置校验
      const shouldVerify = !isCorrect || (isCorrect && verifyWithCorrect);
      // 未校验过 且 不是结果页
      return verifyBegin && !isVerified && !isJG && shouldVerify;
    },
    // 是否需要初始化
    // 是否初始化数据的条件：前置校验通过，router-meta未配置了unCallInitData为true（不需要在校验完成后初始化），且未初始化过(isInitializeData为false)
    // 或者没配置前置校验 且 没不关闭校验完成后初始化
    shouldInitData(isVerifyOk, canVerify) {
      const { meta = {} } = this.$route;
      const { unCallInitData } = meta;
      const shouldInit = !unCallInitData;
      return shouldInit && (!canVerify || (canVerify && isVerifyOk));
    },
    // 开启校验
    async setupCheck() {
      this.startCheck = true;
      // 校验函数，封装为函数是为了兼容前摄器接口还没返回时，把校验函数设置为前摄器的成功回调
      const checkNextFn = async () => {
        let isVerifyOk = false;
        const canVerify = this.shouldVerify();
        if (canVerify) {
          isVerifyOk = await this.beforeCheck();
        } else {
          this.loading = false;
        }
        const canInitData = this.shouldInitData(isVerifyOk, canVerify);
        canInitData && (await this.initData());
        this.hasInitData = true;
        // 不要校验时，直接执行
        !canVerify && this.afterCheck();
      };

      // 不存在属期且前摄器已经启动，此时从前摄器结果中取属期更新到路径中
      // 此时需要兼容前摄器接口未返回的情况，需要把回调设置到pratorModel的success和error
      // 前摄器接口响应时，会触发相应的回调
      const { proactorMode: pm } = window;
      const { query = {} } = this.$route;
      if (pm.open && (!query.SssqQ || !query.SssqZ)) {
        const successFn = () => {
          const params = {
            ...query,
            SssqQ: pm.SssqQ,
            SssqZ: pm.SssqZ,
          };
          this.$router.replace({
            query: params,
          });
          checkNextFn(params);
        };

        const errorFn = (response) => {
          const { Error } = response;
          const body = Error ? Error.Message : '获取默认属期失败';
          window.$vue.$gtDialog.error({
            header: '提示',
            width: '50%',
            closeOnOverlayClick: false,
            body,
            confirmBtn: '刷新',
            onClose: () => {
              // 返回首页
              redirectHome();
            },
            onConfirm: () => {
              setTimeout(() => {
                window.location.reload();
              }, 400);
            },
          });
          console.log('获取默认属期失败', Error);
        };

        if (pm.isCompleted) {
          const { responseBody, status } = pm;
          if (!status) {
            errorFn(responseBody);
            return;
          }
          successFn();
        } else {
          pm.success = successFn;
          pm.error = errorFn;
        }
      } else {
        // 没开启前摄器，正常往后执行
        checkNextFn(query);
      }
    },
    // 前置检查
    async beforeCheck() {
      let result = true;
      const { query = {} } = this.$route;
      const { bizCode, body = [] } = await this.$store.dispatch(`${this.storeName}/verifyBegin`, {
        ...query,
        loading: false,
      });
      if (bizCode && bizCode !== '00') {
        result = false;
      }
      // body返回为数组，则为无前置校验项，不算校验失败。
      // 假如出现异常时，后端应该返回错误状态码，不应该返回空数组。
      if (body.length === 0) {
        this.loading = false;
      }
      // 行为采集 前置校验 处理
      xwcjToQzjyHandle(this.$route, body);
      for (let index = 0; index < body.length; index++) {
        if (body[index].tslxBm === 'zd') {
          result = false;
          break;
        }
      }
      return result;
    },
    // 获取页面初始化数据
    async initData() {
      const { query } = this.$route;
      const params = { ...query, loading: false };
      await this.$store.dispatch(`${this.storeName}/GetSheets`, params);
      if (!this.$store.state[this.storeName].dzbdbmList) {
        return;
      }
      params.dzbdbmList = this.$store.state[this.storeName].dzbdbmList;
      const bsms = getBsms();
      const InitData = this.$store.dispatch(`${this.storeName}/InitData`, {
        ...params,
        bsms,
      });
      // 更新 vuex 报送模式，填表式重置时以此为准
      this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, { bsms });
      const GetFormulas = this.$store.dispatch(`${this.storeName}/GetFormulas`, params);
      const YsData = this.$store.dispatch(`${this.storeName}/YsData`, params);
      const [ysRes] = await Promise.all([YsData, InitData, GetFormulas]);
      // 要素数据要最后覆盖外部初始化
      if (ysRes) {
        const { body: ysBody } = ysRes;
        const { otherParams = {} } = this.$store.state[this.storeName];
        otherParams.wbcsh = ysBody;
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, { otherParams });
      }
    },
    // 校验完成后处理
    afterCheck() {
      if (!this.isGotoForm) {
        this.redirectDefaultView();
        return;
      }
      this.redirectCorrectView();
    },
    // 默认跳转
    redirectDefaultView(params = {}) {
      if (this.isResultPage()) return;
      const { meta = {}, query, name } = this.$route;
      let { redirectName } = meta;
      const ywbm = name.split('/')[0];
      const defaultName = `${ywbm}/tb/yssb`;
      redirectName = redirectName || defaultName;
      if (redirectName && redirectName !== name) {
        this.$router.replace({
          name: redirectName,
          query: {
            ...query,
            ...params,
          },
        });
      }
    },
    // 更正场景下的跳转
    redirectCorrectView(params = {}) {
      if (this.isResultPage()) return;
      const { meta = {}, query, name } = this.$route;
      const { redirectNameWithCorrect } = meta;
      const ywbm = name.split('/')[0];
      const defaultName = `${ywbm}/tb/iframe`;
      if (defaultName || redirectNameWithCorrect) {
        this.$router.replace({
          name: redirectNameWithCorrect || defaultName,
          query: {
            ...query,
            ...params,
          },
        });
      }
    },
    // 前置校验通过时，触发该回调
    completeHandler() {
      this.afterCheck();
      this.loading = false;
    },
  },
});
</script>

<style lang="less" scoped>
.app-wrap {
  height: 100%;
  .skeleton {
    height: inherit;
    &.padding {
      padding: 24px;
    }
  }
}
</style>
