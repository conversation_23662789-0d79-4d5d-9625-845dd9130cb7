## app-wrap组件
该组件可通过路由的meta对象设置是否需要走前置检查的逻辑，详情看路由配置说明。
组件被渲染后会根据路由meta中的verifyBegin属性判断是否需要前置检查页，前置检查接口调用后会调用初始化相关的接口。
watch中会监听hasInit属性，如果该属性为true，组件将渲染子路由。一般在初始化函数【initData】结束后修改该属性为true。
【注意】gt-qzjc组件在播完动画后，如果没有阻断的项目就会调用 completeHandler 函数。

### meta配置示例
src/pages/router/sb/*.route.js

```javascript
const meta = {
  // vuex的命名空间
  storeName: 'sb/qysds_a_nd/form',
  // 面包屑配置
  breadCrumbs: ['居民企业（查账征收）企业所得税年度申报'],
  // 弃用
  // isShowSideBar: false,
  //  自定义返回按钮事件，返回到首页
  isSelfBack: false,
  // 是否全屏显示页面内容。为false时页面最大宽度为1184px并居中显示
  isWidthScreen: false,

  // 是否需要前置检查页
  // TODO: 后续修改改成为手动配置禁用前置校验更合适，现有场景是100%都需要前置校验的。
  // 结果页路由已经默认加到黑名单中，即使配置了verifyBegin 为 true，也不会触发前置校验
  verifyBegin: true,

  // 是否不需要初始化数据（不调用initData），在更多数的情况下，
  // 都需要执行initData的方法，在某些场景需要手动关闭该方法的执行时，需要设置该属性为true。
  unCallInitData: false,

  // 非更正场景下 前置校验完成后跳转的路由name，默认跳转到要素页 [业务编码]/tb/yssb
  // 如果路由定义不符合约定时，则需要手动配置该值
  redirectName: ''
  // 更正的场景下需要前置校验，目前更正时通常是不需要前置校验。在特定的时候，如果需要开启，则配置该属性为true
  verifyWithCorrect: false,

  // 更正场景下默认跳转的路由name属性，默认会自动跳转到填表页，即 [业务编码]/tb/iframe
  // 如果路由定义不符合约定时，则需要手动配置该值
  redirectNameWithCorrect: '',
};
```


## 重写示例
src/pages/index/sb/views/*/app-wrap.js

```javascript
import AppWrap from '@/pages/index/views/sb/common/app-wrap/index.vue';

export default AppWrap.extend({
  methods: {
    // 可选择性的重写beforeCheck函数，改函数是调用前置校验接口地方。
    async beforeCheck(){
      const { query = {} } = this.$route;
      const { bizCode, body = [] } = await this.$store.dispatch(`${this.storeName}/verifyBegin`, {
        ...query,
        loading: false,
      });
      if (bizCode && bizCode !== '00') {
        return false;
      }
      for (let index = 0; index < body.length; index++) {
        // 
        if (body[index].tslxBm === 'zd') return false;
      }
      return true;
    },
     
    // tips: 可选择性的重写initData函数，默认是GetSheets、initData、GetFormulas、YsData
    // app-wrap中会await这个方法的执行，直到该方法执行完成后 才会 渲染子路由，不然会在前置校验完成和initData未完成前，都会展示tu。 一定要关注到这个细节。
    // 通常我们可以在这个函数中完成一下初始化操作，还可以根据初始化数据进行路由跳转。
    async initData(){
       const { query } = this.$route;
      const params = { ...query, loading: false };
      await this.$store.dispatch(`${this.storeName}/GetSheets`, params);
      if (!this.$store.state[this.storeName].dzbdbmList) {
        return;
      }
      params.dzbdbmList = this.$store.state[this.storeName].dzbdbmList;
      const InitData = this.$store.dispatch(`${this.storeName}/InitData`, params);
      const GetFormulas = this.$store.dispatch(`${this.storeName}/GetFormulas`, params);
      const YsData = this.$store.dispatch(`${this.storeName}/YsData`, params);
      const [ysRes] = await Promise.all([YsData, InitData, GetFormulas]);
      // 要素数据要最后覆盖外部初始化
      if (ysRes) {
        const { body: ysBody } = ysRes;
        const { otherParams } = this.$store.state[this.storeName];
        otherParams.wbcsh = ysBody;
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, { otherParams });
      }
    },
  },
})
```