<template>
  <gt-layout-content>
    <div class="fx-page-content fx-body-content">
      <gt-inspection
        completeText="检查完成"
        :list="infoList"
        :duration="delay"
        :level="level"
        @action="goBack"
        @complete="handleComplete"
        mode="order"
      >
        <template #default="{ item }">
          <span class="handle-btn" @click="handleBtnClick(item)">{{ item.btnText }}</span>
        </template>
      </gt-inspection>
    </div>
  </gt-layout-content>
</template>

<script>
/**
 * 前置检查页
 */
export default {
  name: 'gt-qzjc',
  components: {},
  data() {
    return {
      delay: 500,
      zd: false, // 是否有阻断
      status: 'tg', //
      urlBm2McMap: {
        cbjWqwsb: '残疾人就业保障金缴费申报表',
        syWqwsb: '石油特别收益金申报表',
        whsyjsfWqwsb: '文化事业建设费申报表（营改增）',
      },
      zspmDm2McMap: {
        302170100: '娱乐业文化事业建设费',
        302170200: '广告业文化事业建设费',
        302180100: '残疾人就业保障金',
        307100100: '石油特别收益金',
      },
    };
  },
  watch: {
    verifyList: {
      handler(newVal, oldVal) {
        if (newVal.length < 1) return;
        let zd = false; // 是否存在阻断
        const { verifyList } = this;
        for (let index = 0; index < verifyList.length; index++) {
          const item = this.verifyList[index];
          let { tslxBm } = item;
          tslxBm = tslxBm || 'tx';
          if (tslxBm === 'zd') {
            this.status = 'zd';
            zd = true;
            break;
          }
          if (this.status !== 'zd' && tslxBm === 'tx') {
            this.status = 'tx';
          }
        }
        // 没有阻断的情况下，校验接口耗时（超过2000ms），则播放动画总时间长为500ms，否则为1000ms
        if (!zd) {
          const { verifyTime } = this.state;
          const isOverload = verifyTime >= 2000;
          const totalDelay = isOverload ? 500 : 1000;
          this.delay = totalDelay / verifyList.length;
        } else {
          // 如果有阻断的记录，动画间隔为90ms，总时间为1.5s
          const totalDelay = verifyList.length * 90;
          this.delay = totalDelay > 2500 ? 2500 / verifyList.length : 200;
        }
      },
      immediate: true,
    },
  },
  computed: {
    storeName() {
      if (!this.$route.meta.storeName) this.$gtDialog.error({ body: '路由中的meta必须要有storeName字段' });
      return this.$route.meta.storeName;
    },
    level() {
      return ['warning', 'error'].includes(this.$route.meta.level) ? this.$route.meta.level : 'error';
    },
    state() {
      return this.$store.state[this.storeName];
    },
    verifyList() {
      return this.state.verifyList;
    },
    infoList({ $store }) {
      return this.verifyList.map((item) => {
        // anMc: 按钮名称,有的提示信息需要做跳转，按钮名称根据跳转业务确定
        // jkxmlx: 监控项目类型
        // jkxmlxMc: 监控项目类型名称
        // tslxBm: 提示类型编码, “zd”为阻断类型，”tx”为提醒提示类型，”tg”为通过校验
        // tsxx: 提示信息
        // xh: 序号
        const obj = {
          id: item.xh,
          ruleName: item.jkxmlxMc,
          ruleResult: item.tslxBm === 'tg',
          // 是否强制性的。 true时， ruleResult为false时，当前记录为错误状态。当false时，ruleResult为false时，当前记录为警告状态
          mandatoryFlag: item.tslxBm === 'zd',
          midMsg: item.tsxx,
          resultTip: item.tsxx,
          loadingMsg: `正在进行【${item.jkxmlxMc}】事项校验...`,
          btnText: item.urlBm ? item.anMc : ' ',
          urlBm: item.urlBm,
          url: ' ',
          sssqQ: item.sssqQ,
          sssqZ: item.sssqZ,
          jkxmlx: item.jkxmlx,
        };
        //  codding #771 非居民年度申报存在往期未申报，增加未申报信息节点，点击去办理按钮跳转到最近一期未申报
        if (item.urlBm === 'fjmqysdsndsbWqwsb' && item.wqwsbxx) {
          obj.wqwsbxx = item.wqwsbxx;
        } else if (this.urlBm2McMap[item.urlBm] && item.wqwsbxx) {
          // 禅道 #23021 残保金不能申报往期
          obj.wqwsbxx = item.wqwsbxx;
        }
        return obj;
      });
    },
  },
  methods: {
    // 校验展示完成显示下一步
    handleComplete(state) {
      // 业务要求前置校验接口返回的结果中没有阻断和提示的情况下，正常跳转
      if (this.status === 'tg') {
        const { verifyTime } = this.state;
        const isOverload = verifyTime >= 2000;
        const delay = isOverload ? 200 : 500;
        // 校验接口耗时（超过2000ms），则延迟200ms，否则为500ms
        setTimeout(() => {
          this.$emit('complete');
        }, delay);
      }
    },
    goBack() {
      // 当前是提醒的状态，点击继续办理，相当于前置校验完成
      if (this.status === 'tx') {
        this.$emit('complete');
        return;
      }
      // 重定向到门户页面
      window.location.href = `${window.location.origin}${this.appHomeUrl}/`;
    },
    async handleBtnClick(item) {
      const { SssqQ, SssqZ } = this.$route.query;
      const { ywbm } = this.state;
      const { urlBm, sssqQ, sssqZ, jkxmlx } = item;
      // 跳转税费欠缴提醒
      if (jkxmlx === 'sfqj') {
        window.location.href = '/skzx/view/skzs/qsbg?edit=Y';
        return;
      }
      const params = {
        SssqQ,
        SssqZ,
        ywbm,
        urlBm,
      };

      if (sssqQ && sssqZ) {
        params.SssqQ = sssqQ;
        params.SssqZ = sssqZ;
      }

      if (item.urlBm === 'fjmqysdsndsbWqwsb' && item.wqwsbxx) {
        const { ysbNd, snSbnd, minYxqqNd } = item.wqwsbxx;
        const wsbndList = [];
        for (let i = snSbnd; i >= minYxqqNd; i--) {
          if (ysbNd.indexOf(i) === -1) {
            wsbndList.push(String(i));
          }
        }
        if (wsbndList.length > 0) {
          window.location.href = `${window.location.href.split('?')[0]}?SssqQ=${wsbndList[0]}-01-01&SssqZ=${
            wsbndList[0]
          }-12-31`;
          window.location.reload();
        }
      } else if (this.urlBm2McMap[item.urlBm] && item.wqwsbxx) {
        if (item.urlBm === 'whsyjsfWqwsb' && item.wqwsbxx.length > 0) {
          this.showWqwsbList(item.wqwsbxx, item.urlBm);
        } else if (item.wqwsbxx.ysbtjxx && item.wqwsbxx.ysbtjxx.length > 0) {
          this.showWqwsbList(item.wqwsbxx.ysbtjxx, item.urlBm);
        }
      } else if (item.urlBm === 'ssscztsfxxbg') {
        const url = `${
          window.top.location.origin
        }/xxbg/view/ztxxbg/ssscztsfbg?selectParam=3&dkdjdsdjskywqkList="${JSON.stringify([
          { dkdjdsdjskywnr: '代扣代缴企业所得税', dkdjdsdjzsxmDm: '10104' },
        ])}"`;
        window.location.href = url;
      } else {
        await this.$store.dispatch(`${this.storeName}/getSburl`, params);
        const { sburl } = this.state;
        if (!sburl) {
          return;
        }
        window.location.href = sburl;
        const isSameYw = (url) => {
          const appPath = window.STATIC_ENV_CONFIG.ROUTER_PREFIX;
          let { hash } = window.location;
          const matchs = hash.match(/#\/[A-Za-z_\-\d]*\/[A-Za-z_\-\d]*/);
          if (matchs && matchs[0]) {
            const match = matchs[0];
            hash = match;
          }
          return hash ? url.indexOf(`${appPath}/${hash}`) > -1 : false;
        };

        if (isSameYw(sburl)) {
          window.location.reload();
        }
      }
    },
    showWqwsbList(wqwsbxx, urlBm) {
      const data = [];
      wqwsbxx.forEach((item, index) => {
        data.push({
          sbbdMc: this.urlBm2McMap[urlBm] ?? '',
          sssqQ: item.SKSSQQ?.substring(0, 10),
          sssqZ: item.SKSSQZ?.substring(0, 10),
          sbqx: item.SBQX?.substring(0, 10),
          // zspmMc: this.zspmDm2McMap[item.ZSPM_DM] ?? '',
        });
      });

      const columns = [
        {
          colKey: 'xh',
          title: '序号',
          width: 80,
          cell: (h, { row, rowIndex }) => <span>{rowIndex + 1}</span>,
          align: 'center',
        },
        {
          colKey: 'sbbdMc',
          width: 200,
          title: '申报表单',
          ellipsis: true,
        },
        // {
        //   colKey: 'zspmMc',
        //   title: '征收品目',
        //   width: 180,
        //   ellipsis: true,
        // },
        {
          colKey: 'sssqQ',
          title: '费款所属期起',
        },
        {
          colKey: 'sssqZ',
          title: '费款所属期止',
        },
        {
          colKey: 'sbqx',
          title: '申报期限',
        },
      ];

      if (urlBm !== 'whsyjsfWqwsb') {
        columns.push({
          width: 80,
          title: () => <span>操作</span>,
          render(h, context) {
            const { row } = context;
            const clickFun = () => {
              const { sssqQ, sssqZ } = row;
              window.location.href = `${window.location.href.split('?')[0]}?SssqQ=${sssqQ}&SssqZ=${sssqZ}`;
              window.location.reload();
            };
            return (
              <t-link theme="primary" hover="color" onClick={clickFun}>
                办理
              </t-link>
            );
          },
        });
      }

      const message = `您（单位）存在${wqwsbxx.length}期逾期未申报记录，请先办理完逾期未申报后，再进行当期的正常申报，具体明细如下：`;
      const style = {};
      style.marginBottom = '15px';

      this.$gtDialog.confirm({
        header: '逾期未申报记录',
        placement: 'center',
        width: '1000',
        body: () => (
          <div>
            <t-alert theme="info" style={style} message={message} />
            <t-table row-key="index" maxHeight={500} data={data} columns={columns} />
          </div>
        ),
        cancelBtn: null,
        closeOnOverlayClick: false,
        confirmBtn: '去申报',
        onConfirm: () => {
          // 点击“去申报”，跳转至最早的申报日期
          data.sort((a, b) => {
            const dateA = new Date(a.sssqQ);
            const dateB = new Date(b.sssqQ);
            return dateA - dateB;
          });
          window.location.href = `${window.location.href.split('?')[0]}?SssqQ=${data[0].sssqQ}&SssqZ=${data[0].sssqZ}`;
          window.location.reload();
        },
      });
    },
    // post方式打开页面
    openPostWindow(url, params) {
      const newWin = window.open();
      let formStr = '';
      // 设置样式为隐藏，打开新标签再跳转页面前，如果有可现实的表单选项，用户会看到表单内容数据
      formStr += `<form style="" method="POST" action="${url}">`;
      Object.keys(params).forEach((name) => {
        let value = params[name];
        if (typeof value === 'object') {
          value = JSON.stringify(value);
        }
        formStr += `<input type="hidden" name="${name}" value="${value}" />`;
      });
      formStr += `</form>`;

      newWin.document.body.innerHTML = formStr;
      newWin.document.forms[0].submit();

      return newWin;
    },
  },
};
</script>
<style scoped>
.fx-body-content {
  padding-top: 6px;
  margin-top: 0;
}
.handle-btn {
  color: #4285f4;
  cursor: pointer;
}
</style>
