import { format, baseMixin, FormulaEngine, formulaFunction, numToPercentage } from '@gt/components';
import router from '@/pages/index/router';
import commonFunction from '@/pages/index/components/formulaEngine/customerFunction/common';
import sbCommonFunction from '@/pages/index/components/formulaEngine/customerFunction/sbCommon';
import sbzxFunction from '@/pages/index/components/formulaEngine/customerFunction/sbzx';
import IframeView from '@/pages/index/components/iframe-view';
import { routeParamMap } from '@/pages/index/views/sb/common/iframe-view/routeMap';
import { xwcjOptions } from '@gt4/common-front';
import { cloneDeep as deepCopy } from 'lodash';
// import { mountAdapterGlobalFns } from '@fs/tax-engine';
import commonApi from '@/pages/index/api/common/commonApi.js';
// import commonSubmitFuncs from '@/custom-formula-funcs/common/submitDeclare.js';

const { formulasEngine, submitDeclare: submitDeclareBase, createStore } = baseMixin;
// const {
//   beforeValidatorForm,
//   afterValidatorForm,
//   validatorForm,
//   generateErrorMesList,
//   submitDeclare,
//   beforeSubmitForm,
//   afterSubmitForm,
// } = commonSubmitFuncs;

// .vue文件中的公共data属性
const commonData = {
  errorStates: {},
  ctrls: {},
  currentPath: null,
  //  t-select下拉框内容如果过长，需要自适应当前select的宽度
  popupProps: {
    overlayInnerStyle: (trigger) => {
      return {
        width: `${trigger.offsetWidth}px`,
      };
    },
  },
};

// .vue文件中的公共computed
const commonComputed = {};

const commonMethods = {
  ...formulasEngine,
  ...submitDeclareBase,
  // 重写format方法 <div>{{ format(formData.ht_.ywbw.A200000Ywbd.sbxx.yysrLj) }}</div>
  format(value, num) {
    if (num) {
      return format(value, num);
    }
    return format(value, 2);
  },
  numToPercentage(value) {
    return numToPercentage(value);
  },
  // 初始化公式引擎并更新校验和控制结果
  async setInitData() {
    this.initFormulasEngine();
    // 【禅道#215】初始化不带出校验
    setTimeout(() => {
      this.getControlResults();
      // this.getVerifyResults();
    }, 0);
  },
  //  预览需要传参true，填表不需要传参
  async preview(isLookBack, routerFunc = 'push') {
    const { name, query = {} } = this.tbsRouterConfig;
    query.lookBack = isLookBack === true ? 'Y' : 'N';
    const storeName = this.storeName ? this.storeName : this.$route.meta.storeName;
    this.$store.commit(`${storeName}/UPDATE_PAYLOAD`, { formData: this.formData, flagNoExecuteFormula: true });
    if (this.sbms) {
      query.sbms = this.sbms;
    }

    if (isLookBack === true) {
      const path = `/yyzx/${name}`;
      const routeParam = routeParamMap[path];
      const { ywbm, vueYwbm } = routeParam;
      const url = this.getUrl(ywbm, vueYwbm, query);
      const height = document.documentElement.clientHeight - 280;
      const style = {};
      style.minWidth = '1232px';
      style.height = `${height}px`;
      // 预览 需要弹框
      this.$gtDialog.show({
        width: '95%',
        top: '60px',
        closeOnOverlayClick: false,
        header: '预览表单',
        body: () => (
          <div style={style}>
            <IframeView src={url}></IframeView>
          </div>
        ),
        cancelBtn: null,
        confirmBtn: '关闭',
      });
    } else {
      router[routerFunc]({
        name,
        query,
      });
    }
  },
  getUrl(ywbm, vueYwbm, routeQuery) {
    const { SssqQ, SssqZ, lookBack, tbbd, sbms, ZspmDmList, Sbuuid, Pzxh } = routeQuery;
    let url = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/static/sb/index.html?ywlx=sb&ywbm=${ywbm}&vueYwbm=${vueYwbm}&SssqQ=${SssqQ}&SssqZ=${SssqZ}`;

    if (ywbm === 'qysds_a_yjd') {
      url = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/static/sb/index_new.html?ywlx=sb&ywbm=${ywbm}&vueYwbm=${vueYwbm}&SssqQ=${SssqQ}&SssqZ=${SssqZ}`;
    }

    if (Sbuuid && Pzxh) {
      // url增加申报更正相关参数
      url = `${url}&Sbuuid=${Sbuuid}&Pzxh=${Pzxh}`;
    }

    if (lookBack === 'Y') {
      url = `${url}&lookBack=${lookBack}`;
    }

    if (ywbm === 'glywwlndbgsb' || ywbm === 'qysds_a_nd' || ywbm === 'qysds_a_24nd') {
      url = `${url}&tbbd=${tbbd}`;
    }

    if (ZspmDmList) {
      url = `${url}&ZspmDmList=${ZspmDmList}`;
    }
    if (sbms) {
      url = `${url}&sbms=${sbms}`;
    }
    return url;
  },
  // 面包屑返回按钮的回调函数。
  handleBackEntry(target) {
    const { path } = this.$route;
    // path中如果找到了/iframe关键字时，说明是在填表式iframe页面点击返回按钮，这时不做任何跳转
    if (path.indexOf('/iframe') > -1) return;
    window.location.href = target || window.location.origin;
  },
  // #region 提交钩子函数
  beforeValidatorForm() {
    beforeValidatorForm.call(this);
  },
  // 检查表单校验状态，如果还有校验失败的弹框提示
  validatorForm() {
    return validatorForm.call(this);
  },
  generateErrorMesList() {
    return generateErrorMesList.call(this);
  },
  afterValidatorForm() {
    return afterValidatorForm.call(this);
  },
  async submitDeclare(config) {
    this.checkWindowFuncs();

    await submitDeclare.call(this, config);
    this.afterSubmitForm();
  },
  beforeSubmitForm() {
    return beforeSubmitForm.call(this);
  },
  afterSubmitForm() {
    afterSubmitForm.call(this);
  },
  // #endregion
  checkWindowFuncs() {
    // #region 兼容不引入公式引擎，但复用提交链路的用例
    // 同 @fs/gt-adapter
    if (!window.fsCallInVueOnly) {
      // 金四vue层执行，但计税组件不执行
      window.fsCallInVueOnly = async (cb) => {
        typeof cb === 'function' && (await cb());
      };
    }
    if (!window.fsCallInTax) {
      // 计税组件下执行的特定逻辑
      window.fsCallInTax = () => {};
    }
    // #endregion
  },
  // 申报应用复写：表单的onBlur回调函数
  onFormBlur(names) {
    // #region 同 fx-component 逻辑
    if (Array.isArray(names)) {
      names.forEach((name) => {
        const path = name.replace(/^formData\./, '');
        window.formulaEngine.apply(path);
      });
    } else {
      const path = names.replace(/^formData\./, '');
      window.formulaEngine.apply(path);
    }
    this.getControlResults();
    this.getVerifyResults();
    // #endregion

    this.afterOnFormBlur && this.afterOnFormBlur();
  },
  // 填写触发级联时，若与锚点时间差超过约定值，则刷新用户状态
  afterOnFormBlur() {
    const { timestamp } = this.$globalState.formRefresh;
    if (!timestamp) {
      // 记录刷新锚点
      this.$globalState.formRefresh.timestamp = new Date().getTime();
    } else {
      // 获取当前时间与锚点的分钟差
      const currentTime = new Date().getTime();
      const { gapMin } = this.$globalState.formRefresh;
      const minDiff = (currentTime - timestamp) / (1000 * 60);
      if (minDiff > gapMin) {
        // 避免重复刷新
        if (!this.$globalState.formRefresh.flag) {
          this.$globalState.formRefresh.flag = true;
          // 刷新用户状态
          const api = commonApi('/wfcz');
          api.getFrontConfig({ loading: false }).finally(() => {
            // 更新时间戳和标志
            this.$globalState.formRefresh.flag = false;
            this.$globalState.formRefresh.timestamp = new Date().getTime();
          });
        }
      }
    }
  },
  initFormulasEngine() {
    const customFunction = this.customFunction || {};
    const formulaEngine = new FormulaEngine();
    // 将store中的formData赋值给window下的formData。目前公式引擎计算时取值是使用window.formData下的值
    window.formData = this.formData;
    window.otherParams = this.otherParams || {};
    window.formulaEngine = formulaEngine;
    window.formCT = this.formCT || {};

    // mountAdapterGlobalFns();

    const funcs = {
      ...formulaFunction,
      ...commonFunction,
      ...sbCommonFunction,
      ...sbzxFunction,
      ...customFunction,
    };
    Object.keys(funcs).forEach((key) => {
      window[key] = funcs[key];
    });
    window.serverTime = new Date().format('yyyy-MM-dd hh:mm:ss');

    if (this.formulas && this.formulas.length > 0) {
      try {
        typeof this.beforeGetYsWbcsh === 'function' && this.beforeGetYsWbcsh();
        // 优先取 ysData 中的 wbcsh
        const { wbcsh = {} } = this.otherParams ?? {};
        window.formulaEngine.otherParams.wbcsh = JSON.stringify(wbcsh);
        if (window.formData.wbcshInit) {
          if (!wbcsh || (typeof wbcsh === 'object' && JSON.stringify(wbcsh) === '{}')) {
            // 优先级低于 ysData，获取formData.wbcshInit节点生成wbcsh，并删掉wbcshInit节点
            window.formulaEngine.otherParams.wbcsh = JSON.stringify(window.formData.wbcshInit);
          }
          delete window.formData.wbcshInit;
        }

        // TODO 按需求要求要素模式 初始化时才不执行校验公式 这里需要有一个要素模式的判断
        formulaEngine.initialNoExecuteVerifyFormula = true;
        if (this.flagNoExecuteFormula) {
          formulaEngine.initialNoExecuteCalculateFormula = true;
          // formulaEngine.initialNoExecuteControlFormula = true;
        }
        formulaEngine.basename = 'formData';
        formulaEngine.loadFormulas(this.formulas);
        formulaEngine.initialize('formData', this.otherParams?.useZcbwFlag !== 'Y', {
          beforeInitialize:
            typeof customFunction.beforeInitialize === 'function' ? customFunction.beforeInitialize : undefined,
          afterApplyInitialFormulas:
            typeof customFunction.afterApplyInitialFormulas === 'function'
              ? customFunction.afterApplyInitialFormulas
              : undefined,
        });
      } catch (e) {
        // 公式执行报错
        throw new Error(e);
      }
    }
  },
  // 根据浏览器SssqQ, SssqZ参数获取月报month、季报quarter、年报year
  getSbMode() {
    const { SssqQ, SssqZ } = this.routeQuery;
    const sssqqYear = SssqQ.substring(0, 4);
    const sssqzYear = SssqZ.substring(0, 4);
    const sssqqMonth = SssqQ.substring(5, 7);
    const sssqzMonth = SssqZ.substring(5, 7);
    const jcMonthList = ['01', '04', '07', '10'];
    const jmMonthList = ['03', '06', '09', '12'];
    let mode = '';
    if (sssqqYear === sssqzYear) {
      if (sssqqMonth === '01' && sssqzMonth === '12') {
        mode = 'year';
      } else if (SssqQ === SssqZ) {
        mode = 'date';
      } else if (
        sssqzMonth - sssqqMonth === 2 &&
        jcMonthList.includes(sssqqMonth) &&
        jmMonthList.includes(sssqzMonth)
      ) {
        mode = 'quarter';
      } else if (sssqzMonth - sssqqMonth === 5) {
        mode = 'semiannual';
      } else if (sssqqMonth === sssqzMonth) {
        mode = 'month';
      } else {
        this.$gtDialog.info({
          body: '您的所属期不为月报、季报、年报',
          closeOnOverlayClick: false,
        });
      }
    } else {
      this.$gtDialog.info({
        body: '您的所属期不为月报、季报、年报',
        closeOnOverlayClick: false,
      });
    }
    return mode;
  },
  // 上传附送资料
  scfszl(swsxDm, ywbm) {
    const sxuuid = this.Ysqxxid;
    this.$gtDialog.show({
      header: '上传附送资料',
      width: '1184',
      className: 'gt-upload--dialog',
      body: () => <gt-upload sxuuid={sxuuid} swsxDm={swsxDm} ywbm={ywbm} isPreview={false} />,
      confirmBtn: '确定',
    });
  },
  /*  t-select等组件宽度不够导致文字显示不全，鼠标移入时显示完整文字提示
   *  value: '123', options: [ { value: '123', label: 'test' } ]
   * */

  getOptionsTitle(value, options = [], valueKey = 'value', lableKey = 'label') {
    let title = '';
    options.forEach((item) => {
      if (item[valueKey] === value) {
        title = item[lableKey];
      }
    });
    return title;
  },
  /**
   * 行为采集提交前公共处理函数
   * @param {string} 行为采集场景类型
   */
  xwcjInSubmitDeclare(type) {
    // 提供了行为采集函数则进行处理
    if (typeof this.getXwcjParams === 'function') {
      let params = [];
      if (window.formulaEngine && window.formulaEngine.idxVariable2NoPass) {
        // 获取去重后的不通过的公式 id
        const formulaIds = new Set();
        Object.values(window.formulaEngine.idxVariable2NoPass).forEach((formulaObjs) => {
          Object.keys(formulaObjs).forEach((key) => {
            formulaIds.add(key);
          });
        });
        const formDataTemp = deepCopy(this.formData);
        // 业务 mixin 中提供 getXwcjParams 方法进行行为采集对接
        params = this.getXwcjParams(Array.from(formulaIds), formDataTemp, type);
      }

      if (params.length > 0) {
        for (let i = 0, len = params.length; i < len; i++) {
          try {
            const jsonItem = params[i];
            console.log('行为采集', jsonItem);
            xwcjOptions.trackXwcjUploadNoGnCheck(jsonItem).then((res) => {
              console.log(`行为采集（${jsonItem?.sjcjkey ?? ''}）`, res);
            });
          } catch (error) {
            console.error('行为采集出错', error);
          }
        }
      }
    }
  },
};

const pageMixin = {
  data() {
    return {
      ...commonData,
    };
  },
  created() {
    // 监听面包屑返回按钮发出来的事件
    this.$bus.$on('backEntry', this.handleBackEntry);
  },
  methods: {
    ...commonMethods,
    // // 重写获取校验结果函数(函数名相同时commonMethods中的函数将会被重写)
    // getVerifyResults() {
    //   this.errorStates = window.formulaEngine.getVerifyResults();
    // },
  },
};
export { pageMixin, commonMethods, commonData, commonComputed, createStore };
