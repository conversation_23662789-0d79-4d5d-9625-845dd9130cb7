<template>
  <div class="iframe-view-demo" style="overflow-y: hidden">
    <div style="height: 100%">
      <iframe-view
        style="position: relative; z-index: 98; min-width: 1232px"
        id="frmFrame"
        name="frmFrame"
        :src="url"
        @handleMessage="handleMessage"
      />
    </div>
    <gt-layout-footerbar full-width style="z-index: 1">
      <gt-submit-footer
        :money="ybtse"
        :showPreview="showPreview"
        :showReport="showReport"
        :disabledPreviewBtn="disabledPreviewBtn"
        :disabledReportBtn="disabledReportBtn"
        :previewTxt="previewTxt"
        :reportTxt="reportTxt"
        @preview="preview"
        @report="report"
        :title="title"
        :type="footType"
      />
    </gt-layout-footerbar>

    <t-drawer
      class="drawer"
      :visible="visible"
      :showOverlay="false"
      :preventScrollThrough="false"
      :footer="false"
      :showInAttachedElement="true"
    >
      <template #header>
        <div class="drawer-head">
          <icon name="menu-fold" @click="handleClose"></icon>
          <h3>错误提示</h3>
        </div>
      </template>
      <div class="drawer-body">
        <ul :key="name" v-for="(obj, name) in tips">
          <h3>{{ name }}</h3>
          <li :key="item.dzbdbm" v-for="item in obj.list" @click="findError(obj.dzbdbm, item.jpath)">
            <p>{{ item.tip }}</p>
            <icon name="arrow-right"></icon>
          </li>
        </ul>
      </div>
    </t-drawer>

    <gt-dialog
      header="导入报表"
      :visible.sync="importFormVisible"
      :width="ywbm === 'whsyjsf' ? 640 : 680"
      :closeOnOverlayClick="false"
      :destroyOnClose="ywbm === 'whsyjsf'"
    >
      <div v-if="ywbm === 'sytbsyjsb'">
        <SytbsyjsbUpload v-if="importFormVisible" isDialog @success="getUploadResult"></SytbsyjsbUpload>
      </div>
      <div v-if="ywbm === 'yjtkfxzbjsb'">
        <YjtkfxzbjsbUpload v-if="importFormVisible" @success="getUploadResult"></YjtkfxzbjsbUpload>
      </div>
      <div v-if="ywbm === 'glywwlndbgsb'">
        <GlywwlndbgsbUpload v-if="importFormVisible" @success="getUploadResult"></GlywwlndbgsbUpload>
      </div>
      <div v-if="ywbm === 'whsyjsf'">
        <YsfwkcxmqdUpload v-if="importFormVisible" ref="importOutRef" />
      </div>
      <div v-if="ywbm === 'qysds_a_yjd'">
        <gt-import-report
          v-if="importFormVisible"
          :style="{ marginTop: '24px' }"
          ref="import_ref_qysds_a_yjd"
          :uploadProps="{ autoUpload: false }"
          :uploadMethod="uploadMethods"
        >
          <template #btn-group>
            <gt-space>
              <t-link theme="primary" hover="color" @click="handleDownload">下载模板</t-link>
            </gt-space>
          </template>
        </gt-import-report>
      </div>
      <template #footer>
        <gt-space>
          <t-button theme="default" @click="importFormVisible = false">取消</t-button>
          <t-button @click="importForm">{{ ywbm === 'whsyjsf' ? '确定' : '导入' }}</t-button>
        </gt-space>
      </template>
    </gt-dialog>
    <!--    上传资料-->
    <gt-dialog
      class="gt-upload--dialog"
      visible
      :cancelBtn="null"
      v-if="scfszlVisible"
      header="上传附送资料"
      width="1184"
      @confirm="confirmDialog"
      @close="closeDialog"
    >
      <gt-upload
        ref="uploadRef"
        :sxuuid="sxuuid"
        :swsxDm="swsxDm"
        :ywbm="vueYwbm"
        :isPreview="false"
        :configure-setter="configureSetter"
      />
    </gt-dialog>
  </div>
</template>

<script>
import { Icon } from 'tdesign-icons-vue';
import YsfwkcxmqdUpload from '@/pages/index/views/sb/whsyjsf/common/ysfwkcxmqdUpload.vue';
import IframeView from '@/pages/index/components/iframe-view';
import SytbsyjsbUpload from '@/pages/index/views/sb/sytbsyjsb/tb/upload/index.vue';
import GlywwlndbgsbUpload from '@/pages/index/views/sb/glywwlndbgsb/components/import-file/index.vue';
import YjtkfxzbjsbUpload from '@/pages/index/views/sb/yjtkfxzbjsb/components/import-file/index.vue';
import commonApi from '@/pages/index/api/common/commonApi.js';
import baseStore from '@/pages/index/views/sb/common/store';
import { cloneDeep } from 'lodash';
import { saveFileAsXlsx } from '@gt/components';
import GT4Excel from '@/pages/index/utils/gt4-excel.js';
import { throttle } from '@/utils/index';
import { routeParamMap } from './routeMap';

export default {
  name: 'IframeViewDemo',
  components: {
    IframeView,
    Icon,
    SytbsyjsbUpload,
    YjtkfxzbjsbUpload,
    YsfwkcxmqdUpload,
    GlywwlndbgsbUpload,
  },
  props: {
    name: String,
  },
  data() {
    return {
      ybtse: 0.0,
      ywbm: '',
      vueYwbm: '',
      url: '',
      tips: '',
      visible: false,
      resultRouterConfig: {},
      showReport: true,
      showPreview: false,
      previewTxt: '风险提示服务',
      footType: 'space-between',
      disabledPreviewBtn: true,
      disabledReportBtn: true,
      reportTxt: '提交申报',
      reportTxtObj: {
        dllfssrmxcj: '提交',
      },
      title: '本期应补（退）税额',
      titleObj: {
        qysds_a_yjd: '实际应补（退）所得税额',
        cbj: '本期应补（退）费额',
        whsyjsf: '本期应补（退）费额',
        tysb: '本期应补（退）税费额',
        fqdqdzcpcljjsb: '本期应缴金额',
        sytbsyjsb: '本期应补（退）费额',
        qysdsqs: '实际应补（退）所得税额',
      },
      swsxDmObj: {
        fjmqysdsyjsb: 'SXN022008401',
        qsqysds: 'SXA061036002',
        qysds_a_yjd: 'SXN022005301',
        qysds_a_nd: 'SXN022005401',
        kjqysdsbg: 'SXN022008201',
        fjmqysdszxsb: 'SXN022008301',
        fjmqysdsndsb: 'SXN022008501',
        qysdskdqjyndsb: 'SXN022015701',
      },
      importFormVisible: false,
      importResult: {},
      disabledImportBtn: true,
      //  上传附列资料相关字段
      scfszlVisible: false,
      sxuuid: '',
      swsxDm: '',
      importOrExportByFrontEnd: false, // 导入导出前端版
    };
  },
  created() {
    const { path, name } = this.$route;
    const routeParam = routeParamMap[path || name];
    this.ywbm = routeParam.ywbm;
    this.vueYwbm = routeParam.vueYwbm;

    const { ywbm } = this.$route.query;
    if (ywbm) {
      this.ywbm = ywbm.toLowerCase();
    }
    this.resultRouterConfig = { name: `${this.vueYwbm}/jg`, query: this.$route.query };
    this.$bus.$on('backEntry', this.backEntry);

    // 禅道951 修改文字显示问题
    this.title = this.titleObj[this.ywbm] || '本期应补（退）税额';
    this.reportTxt = this.reportTxtObj[this.ywbm] || '提交申报';

    if (
      this.vueYwbm === 'qysds_a_yjd' ||
      this.vueYwbm === 'qysds_b_yjd' ||
      this.vueYwbm === 'qysds_a_nd' ||
      this.vueYwbm === 'qsqysds' ||
      this.vueYwbm === 'cwbbbs' ||
      this.vueYwbm === 'glywwlndbgsb'
    ) {
      this.showPreview = true;
      // 财务报送填表页，预览按钮需要为上一步
      if (this.vueYwbm === 'cwbbbs' || this.vueYwbm === 'glywwlndbgsb') {
        this.previewTxt = '上一步';
        this.disabledPreviewBtn = false;
      }
    }
  },
  mounted() {
    const { SssqQ, SssqZ, lookBack, drlx, fileKey, tbbd, sbms, ZspmDmList, Sbuuid, Pzxh, ZspmDm, cfsbBz } =
      this.$route.query;
    let url = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/static/sb/index.html?ywlx=sb&ywbm=${this.ywbm}&vueYwbm=${this.vueYwbm}&SssqQ=${SssqQ}&SssqZ=${SssqZ}`;

    if (this.ywbm === 'qysds_a_yjd') {
      url = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/static/sb/index_new.html?ywlx=sb&ywbm=${this.ywbm}&vueYwbm=${this.vueYwbm}&SssqQ=${SssqQ}&SssqZ=${SssqZ}`;
    }

    if (Sbuuid && Pzxh) {
      // url增加申报更正相关参数
      url = `${url}&Sbuuid=${Sbuuid}&Pzxh=${Pzxh}`;
      if (this.ywbm !== 'glywwlndbgsb' && this.ywbm !== 'qysds_a_nd') {
        // 更正时，禁用面包屑的返回按钮
        this.$store.commit('index/SET_DISABLEBACK', true);
      }
    }

    if (lookBack === 'Y') {
      url = `${url}&lookBack=${lookBack}`;
      this.showReport = false;
    }
    if (this.ywbm.indexOf('cwbb') > -1) {
      url = `${url}&drlx=${drlx}&fileKey=${fileKey}&cfsbBz=${cfsbBz}`;
      this.footType = 'center';
    }
    if (this.ywbm === 'glywwlndbgsb' || this.ywbm === 'qysds_a_nd') {
      url = `${url}&tbbd=${tbbd}`;
      if (this.ywbm === 'glywwlndbgsb') {
        this.footType = 'center';
      }
    }
    if (this.ywbm === 'dllfssrmxcj') {
      this.footType = 'center';
    }
    if (ZspmDmList) {
      url = `${url}&ZspmDmList=${ZspmDmList}`;
    }
    if (sbms) {
      url = `${url}&sbms=${sbms}`;
    }
    this.url = url;
  },
  methods: {
    // 风险扫描服务
    async preview() {
      // 财务报送填表页，预览按钮需要为上一步
      if (this.vueYwbm === 'cwbbbs') {
        this.$router.back();
        return;
      }
      // 关联业务往来报告填表页，预览按钮需要为上一步
      if (this.vueYwbm === 'glywwlndbgsb') {
        // 跳转回记录的选表页
        const { backXbObj } = this.$store.state['sb/glywwlndbgsb/form'];
        this.$router.replace({
          name: backXbObj.name,
          query: { ...backXbObj.query },
        });
        return;
      }

      const prefix = `${baseStore.state.sbzxCtl}/qysdssb/${this.vueYwbm}`;
      const api = commonApi(prefix);

      const { SssqQ, SssqZ } = this.$route.query;
      let bw;
      let ysqxxid;
      if (this.vueYwbm === 'qysds_a_nd') {
        const frmFrame = document.getElementById('frmFrame');
        const frmMain = frmFrame.contentWindow.document.getElementById('frmMain');
        bw = frmMain.contentWindow.formData;
        ysqxxid = frmMain.contentWindow.document.getElementById('ysqxxid').value;
      } else {
        const state = this.$store.state[`sb/${this.vueYwbm}/form`];
        bw = state.formData;
        ysqxxid = state.Ysqxxid;
      }

      const handleBw = (bw) => {
        const formData = JSON.parse(JSON.stringify(bw));
        const { jmxxGrid, yjxxGrid, sbxxGrid } = formData.ht_;
        if (jmxxGrid != null && jmxxGrid.jmxxGridlb != null) {
          formData.ht_.jmxxGrid = {};
        }

        if (yjxxGrid != null && yjxxGrid.yjxxGridlb != null) {
          formData.ht_.yjxxGrid = {};
        }

        if (sbxxGrid != null && sbxxGrid.sbxxGridlb != null) {
          formData.ht_.sbxxGrid = {};
        }
        formData.fq_ = {};
        formData.hq_ = {};

        return formData;
      };
      const qqbwData = JSON.stringify(handleBw(bw));
      const obj = {
        sssqQ: SssqQ,
        sssqZ: SssqZ,
        ysqxxid,
      };

      if (this.vueYwbm === 'qysds_a_nd' || this.vueYwbm === 'qsqysds') {
        obj.qqbwData = qqbwData;
      } else if (this.vueYwbm === 'qysds_a_yjd' || this.vueYwbm === 'qysds_b_yjd') {
        obj.zcbw = qqbwData;
      }

      const res = await api.fxsm(obj);
      if (!res.body) {
        // TODO 按超时情况处理
        const clickFun = () => {
          window.open(`${window.location.origin}/xxbg/view/sdsxgxxbg/#/yyzx/qysdssszczcfxtssmjgcx/cx`);
        };
        this.$gtDialog.info({
          header: '提示',
          body: () => (
            <div>
              已发送扫描申请，请点击“
              <span style="color:#4285F4;cursor: pointer" onClick={clickFun}>
                居民企业所得税税收政策遵从风险提示扫描结果查询
              </span>
              ”获取反馈结果
            </div>
          ),
          closeOnOverlayClick: false,
          cancelBtn: false,
          confirmBtn: '确定',
        });
        return;
      }

      const fxsxList = res.body.fxsxList || [];
      if (!fxsxList.length) {
        // 扫描通过
        this.$gtDialog.show({
          width: '740px',
          header: '风险提示服务',
          body: () => (
            <gt-result-info
              title="扫描通过"
              subTitle="您填写的申报数据经系统扫描检查，未发现风险疑点。"
              status="success"
              style="padding: 48px 32px;"
            />
          ),
          closeOnOverlayClick: false,
          cancelBtn: null,
          confirmBtn: '关闭',
        });
        return;
      }

      // 存在风险数据
      fxsxList.forEach((item) => {
        const map = {
          0: '无',
          1: '高',
          2: '中',
          3: '低',
        };
        item.fxdlxDm = map[item.fxdlxDm] || '低';
      });
      // fxdlxDm：0:无风险 1:高风险；2:中风险；3:低风险
      const columns = [
        {
          colKey: 'fxdlxDm',
          width: '15%',
          title: () => <span style="font-weight: 600;color:#666">风险等级</span>,
          attrs: {
            style: {
              color: '#666666',
              textAlign: 'center',
            },
          },
        },
        {
          colKey: 'fxdmc',
          width: '25%',
          title: () => <span style="font-weight: 600;color:#666">风险指标</span>,
          attrs: {
            style: {
              color: '#666666',
            },
          },
        },
        {
          colKey: 'tstxnsr',
          width: '60%',
          title: () => <span style="font-weight: 600;color:#666">风险扫描提示</span>,
          attrs: {
            style: {
              color: '#666666',
            },
          },
          render(h, context) {
            const { row, col } = context;
            const tstxnsr = row[col.colKey];
            const tstxnsrArr = tstxnsr.split('<br>');
            return (
              <div>
                {tstxnsrArr.map((t) => {
                  return <li>{t}</li>;
                })}
              </div>
            );
          },
        },
      ];
      this.$gtDialog.confirm(
        {
          width: '740px',
          theme: 'info',
          header: '你填写的申报数据中存在以下风险疑点，请您自查核实。风险疑点详情如下：',
          body: () => (
            <div>
              <t-table bordered row-key={'index'} maxHeight={210} data={fxsxList} columns={columns} />
            </div>
          ),
          cancelBtn: null,
          closeOnOverlayClick: false,
          confirmBtn: '修改表单',
        },
        { type: 'confirm' },
      );
    },
    // 申报提交
    report: throttle(function () {
      const iframe = document.getElementById('frmFrame');
      const message = { type: 'validatorForm' };
      iframe.contentWindow.postMessage(message, '*');
    }),

    // 关闭右边抽屉
    handleClose() {
      this.visible = false;
    },

    // 错误跳转
    findError(dzbdbm, jpath) {
      const iframe = document.getElementById('frmFrame');
      const message = { type: 'findError', dzbdbm, jpath };
      iframe.contentWindow.postMessage(message, '*');
    },

    // 处理内嵌填表页发送过来的消息
    handleMessage(e) {
      const { data } = e;
      const { type, title } = data;
      const enums = {
        pageOperable: this.handlePageOperable,
        ybtse: this.handleYbtse,
        tips: this.handleTips,
        result: this.handleResult,
        previewForm: this.handlePreviewForm,
        openDllfssrmxcj: this.handleOpenDllfssrmxcj,
        gotoTzxm: this.handleGotoTzxm,
        pushRouter: this.handlePushRouter,
        getFrmData: this.handleGetFrmData,
        beforeSubmitForm: this.handleBeforeSubmitForm,
        scfszl: this.handleScfszl,
        import: this.handleImport,
        export: this.handleExport,
        importFrontEnd: this.handleImportFrontEnd,
        exportFrontEnd: this.handleExportFrontEnd,
        updatePayload: this.handleUpdatePayload,
      };

      if (data.ywbm === this.ywbm) {
        enums[type](data, title);
      }
    },
    backEntry() {
      const iframe = document.getElementById('frmFrame');
      const message = { type: 'goBack' };
      iframe.contentWindow.postMessage(message, '*');
    },
    getUploadResult(result) {
      this.importResult = result;
      this.disabledImportBtn = false;
    },
    importForm() {
      if (this.ywbm === 'whsyjsf') {
        this.$refs.importOutRef.whsyjsHandleConfirmImport();
        this.importFormVisible = false;
      } else {
        const { importOrExportByFrontEnd, vueYwbm } = this;
        if (importOrExportByFrontEnd) {
          // 触发上传组件的上传方法
          this.$refs[`import_ref_${vueYwbm}`].confirmUpload();
        } else {
          // 通过后端返回的fileKey调用后端接口获取导入的数据
          this.importFormVisible = false;
          const iframe = document.getElementById('frmFrame');
          const message = { type: 'getImportData', fileKey: this.importResult.fileKey };
          iframe.contentWindow.postMessage(message, '*');
        }
      }
    },
    /** * handleMessage START * * */
    handlePageOperable(data, title) {
      // 页面可操作设置，operable为false则禁用提交申报按钮
      this.disabledReportBtn = !data.operable;
      this.disabledPreviewBtn = !data.operable;
    },
    handleYbtse(data, title) {
      // 左下角显示的应补退税额
      this.ybtse = data.ybtse;
      // 修改文字显示问题
      title && (this.title = title);
    },
    handleTips(data, title) {
      if (JSON.stringify(data.tips) === '{}') {
        this.visible = false;
      } else {
        // 申报提交前校验不通过
        this.tips = data.tips;
        this.visible = this.visible || data.showError;
      }
    },
    handleResult(data, title) {
      // 申报提交请求返回的结果
      const result = JSON.parse(data.result);
      // afterSubmitForm方法补充的额外参数
      const { extParam } = data;
      let ysqxxid;
      if (Array.isArray(result)) {
        ysqxxid = `${result[0].ysqxxid}_${result[1].ysqxxid}`;
        // 传参给回执页
        this.$store.commit(`sb/${this.vueYwbm}/form/UPDATE_PAYLOAD`, {
          pjhs: result[0].pjhs,
          Ysqxxid: ysqxxid,
        });
      } else {
        ysqxxid = result.ysqxxid;
        // 传参给回执页
        this.$store.commit(`sb/${this.vueYwbm}/form/UPDATE_PAYLOAD`, {
          pjhs: result.pjhs,
          Ysqxxid: ysqxxid,
        });
      }
      const query = {
        ...this.$route.query,
        ysqxxid,
        ...extParam,
      };
      if (this.ywbm.indexOf('cwbb') > -1) {
        // 跳转到回执页
        this.$router.push({
          name: `cwbbbs/jg`,
          query,
        });
      } else {
        // 跳转到回执页
        this.$router.push({
          name: `${this.vueYwbm}/jg`,
          query,
        });
      }
    },
    handlePreviewForm(data, title) {
      const query = { ...this.$route.query, lookBack: 'Y' };
      this.$router.push({
        name: `${this.vueYwbm}/tb/iframe`,
        query,
      });
    },
    handleOpenDllfssrmxcj(data, title) {
      const { openYwbm } = data;
      const { SssqQ, SssqZ } = this.$route.query;
      const url = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/#/yyzx/${openYwbm}/tb/tbsb?SssqQ=${SssqQ}&SssqZ=${SssqZ}`;
      window.open(url);
    },
    handleGotoTzxm(data, title) {
      this.$router.push({
        name: `${this.vueYwbm}/adjust`,
        query: this.$route.query,
      });
    },
    handlePushRouter(data, title) {
      // 填表式自定义返回逻辑
      // 默认返回填报检查页
      // 需要自定义返回页面，需要定义路由的meta的backPath属性
      if (this.vueYwbm === 'qysds_a_nd') {
        // 所得税A年报填表页返回按钮，跳转回记录的选表页
        const { backXbObj } = this.$store.state['sb/qysds_a_nd/form'];
        this.$router.replace({
          name: backXbObj.name,
          query: { ...backXbObj.query },
        });
        return;
      }

      const { disableBack } = this.$store.state.index;
      if (disableBack) {
        return;
      }

      const { formData, sbms } = data;
      if (formData) {
        this.$store.commit(`sb/${this.vueYwbm}/form/UPDATE_PAYLOAD`, {
          formData,
          flagNoExecuteFormula: true,
        });
      }
      const { query, meta = {} } = this.$route;
      let { backPath: path } = meta;
      if (path) {
        this.$router.replace({
          path,
          query,
        });
        return;
      }
      if (sbms && sbms !== 'tb') {
        path = `/yyzx/${this.vueYwbm}/tb/${sbms}`;
        this.$router.replace({
          path,
          query,
        });
      } else {
        // 默认跳转到门户首页
        path = `${window.location.origin}${this.appHomeUrl}/`;
        window.location.href = path;
      }
    },
    handleGetFrmData(data, title) {
      // 传数据到iframe页面
      const {
        ywbm,
        sheets,
        formulas,
        formData: fm,
        otherParams,
        Ysqxxid,
        flagNoExecuteFormula,
        sbms,
      } = this.$store.state[`sb/${this.vueYwbm}/form`];
      const iframe = document.getElementById('frmFrame');
      const message = {
        type: 'setFrmData',
        ywbm,
        sheets,
        formulas,
        formData: fm,
        otherParams,
        flagNoExecuteFormula,
        ysqxxid: Ysqxxid,
        sbms,
      };
      iframe.contentWindow.postMessage(message, '*');
    },
    handleBeforeSubmitForm(data, title) {
      this.$gtConfirmation.show({
        onConfirm() {
          const iframe = document.getElementById('frmFrame');
          const message = { type: 'submitForm' };
          iframe.contentWindow.postMessage(message, '*');
        },
        onClose() {
          const iframe = document.getElementById('frmFrame');
          const message = { type: 'cancelSubmit' };
          iframe.contentWindow.postMessage(message, '*');
        },
      });
    },
    handleScfszl(data, title) {
      // 上传附送资料
      let { Ysqxxid } = this.$store.state[`sb/${this.vueYwbm}/form`];
      if (!Ysqxxid) {
        const iframe = document.getElementById('frmFrame');
        const frmMain = iframe.contentWindow.document.getElementById('frmMain');
        Ysqxxid = frmMain.contentWindow.document.getElementById('ysqxxid').value;
      }
      this.sxuuid = Ysqxxid;
      this.swsxDm = this.swsxDmObj[this.vueYwbm];
      this.scfszlVisible = true;
    },
    // #region 后端版导入导出
    handleImport(data, title) {
      this.importOrExportByFrontEnd = false;
      this.importFormVisible = true;
    },
    // 导出模板
    async handleExport() {
      this.importOrExportByFrontEnd = false;

      /**
       * 提取对应业务域的导出 export文件 或 api文件的默认内容
       * @param {string} context 符合检索条件的所有文件
       * @param {string} vueYwbm 业务编码
       * @param {string?} upperDirectory 忽略上层路径的部分路径
       * @returns {any} 检索文件的默认导出内容
       */
      const importYwbm2Resource = (context, vueYwbm, upperDirectory) => {
        let moduleDefault; // 导出内容
        const keys = context.keys(); // 文件集对象
        for (let i = 0, len = keys.length; i < len; i++) {
          const key = keys[i]; // 文件路径名
          const modulePath = key.replace(/^\.\//, ''); // 将相对路径转换为绝对路径
          let moduleName; // 提取文件路径中业务域名称
          if (upperDirectory) {
            // 提取检索文件的上层(upperDirectory)目录的再上一层，即是业务域名称
            const regexPattern = new RegExp(`(.*/)?${upperDirectory}/`);
            moduleName = modulePath.match(regexPattern)[1].replace(/\/$/, ''); // 提取业务域名称
          } else {
            // 提取检索文件的上层目录，即是业务域名称
            moduleName = modulePath.match(/(.*)\/index\.js$/)?.[1]; // 提取业务域名称
          }
          // 业务域名称比对
          if (moduleName === vueYwbm) {
            moduleDefault = context(key).default;
            break;
          }
        }
        return moduleDefault;
      };

      const { vueYwbm } = this;
      // 获取 ywbm/export/index.js
      const exportContext = require.context('@/pages/index/views/sb', true, /\/export\/index\.js$/);
      const generateExportParams = importYwbm2Resource(exportContext, vueYwbm, 'export');

      // 获取 api/ywbm/index.js
      const apiContext = require.context('@/pages/index/api', true, /\/index\.js$/);
      const ywApi = importYwbm2Resource(apiContext, vueYwbm);

      if (generateExportParams && ywApi) {
        const state = this.$store.state[`sb/${this.vueYwbm}/form`];
        const bw = cloneDeep(state.formData);
        // 业务域复写函数提供导出 ywbw 和 sheetNameList
        const { ywbw, sheetNameList, ...others } = generateExportParams(bw, vueYwbm);
        const { bizCode, body } = await ywApi.downloadExportFile({
          sheetNameList,
          ywbw: JSON.stringify(ywbw),
          ...others,
        });
        if (bizCode === '00') {
          const { content, fileName } = body;
          saveFileAsXlsx(content, fileName);
        }
      } else {
        this.$gtDialog.error({
          header: '提示',
          body: '导出失败！',
          closeOnOverlayClick: false,
        });
        console.log('业务域的 export/index.js 和 api 引入失败，请检查！');
      }
    },
    // #endregion
    // #region 前端版导入导出
    handleImportFrontEnd() {
      this.importOrExportByFrontEnd = true;
      this.importFormVisible = true;
    },
    // 下载模板
    handleDownload() {
      this.downloadFileByUrl(`/excel-config/${this.ywbm?.toUpperCase?.()}.xls`);
    },
    // 导入excel
    uploadMethods({ raw }) {
      return new Promise((resolve) => {
        const files = [raw];
        if (files.length) {
          const reader = new FileReader();
          reader.readAsArrayBuffer(files[0]);
          reader.addEventListener('load', (e) => {
            // 初始化excel组件
            const excel = new GT4Excel({
              ywbm: this.vueYwbm.toUpperCase(),
              template: e.target.result,
            });
            // 调用导入方法，解析excel数据
            excel
              .import()
              .then((res) => {
                resolve({ status: 'success', response: { url: '123' }, res });
                // 将解析后的excel数据传回填表页
                const iframe = document.getElementById('frmFrame');
                const message = { type: 'setExcelData', excelData: res };
                iframe.contentWindow.postMessage(message, '*');
              })
              .catch((err) => {
                console.error(err);
                resolve({ status: 'fail', err });
              })
              .finally(() => {
                this.importFormVisible = false;
              });
          });
        }
      });
    },
    // 导出excel
    handleExportFrontEnd(data, title) {
      this.importOrExportByFrontEnd = true;
      const { excelData } = data;
      const ywbm = this.vueYwbm.toUpperCase();
      const suffix = 'xls';
      const excel = new GT4Excel({
        ywbm,
        formData: excelData,
        templateExt: suffix,
      });
      const fileName = `${ywbm}.${suffix}`;
      excel.export(fileName);
    },
    // #endregion
    // 下载模板公共方法
    downloadFileByUrl(url) {
      if (!url) return;
      const fullUrl = `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}${url}`;
      const nameIndex = fullUrl.lastIndexOf('/');
      const name = fullUrl.slice(nameIndex + 1);
      const a = document.createElement('a');
      a.download = `${name.includes('.xls') ? name : `${name.slice(0, name.lastIndexOf('.'))}.xlsx`}`;
      a.href = fullUrl;
      document.body.appendChild(a);
      a.click();
    },
    // 更新 vuex
    handleUpdatePayload(data, title) {
      const { payload = {} } = data;
      this.$store.commit(`sb/${this.vueYwbm}/form/UPDATE_PAYLOAD`, {
        ...payload,
      });
    },
    /** * handleMessage END ** */

    //  上传附送资料弹窗相关方法
    configureSetter(item, index, array) {
      if (this.vueYwbm === 'kjqysdsbg') {
        if (item.flzlDm === '003501') {
          // 设置为隐藏
          item.hidden = true;
        } else {
          //  如果需要填写享受协定待遇表 则 003474 为必传资料
          if (window.formData.ht_.kjqysdssbvo.fdyqkjqkForm.syssxdtkJmse > 0) {
            ['003474'].includes(item.flzlDm) && (item.flzlbslxDm = '1');
          }
          // 如果需要填写填写递延纳税报告表 则003771为必传资料
          if (window.formData.ht_.kjqysdssbvo.fdyqkjqkForm.gnsfyhxm === '0004081524') {
            ['003771'].includes(item.flzlDm) && (item.flzlbslxDm = '1');
          }
        }
      }
    },
    confirmDialog() {
      const { result } = this.$refs.uploadRef.validate();
      // 对于必报文件没上传时，UI给的界面交互方案 错误轻提示：存在必报的附送资料未上传，请上传
      // 可通过调用validate时传入true，如果校验不通过会默认提示，如 this.$refs.uploadRef.validate(true);
      if (!result) {
        this.$message.error('存在必报的附送资料未上传，请上传');
      } else {
        this.scfszlVisible = false;
      }
    },
    closeDialog(e, e2) {
      this.scfszlVisible = false;
    },
  },
  // 销毁前钩子函数
  beforeDestroy() {
    // 销毁前将面包屑的返回按钮禁用状态还原
    this.$store.commit('index/SET_DISABLEBACK', false);
  },
};
</script>
<style scoped lang="less">
@namespace: iframe-view-demo;
.@{namespace} {
  width: 100%;
  height: 100%;
  /deep/ .gt-layout-footerbar {
    .gt-layout-footerbar_fix {
      z-index: 1;
    }
  }
  /deep/ .drawer {
    z-index: 5000;
  }
}

// 右边抽屉样式
.drawer {
  position: fixed;
  top: 64px;
  z-index: 998;
  height: calc(100% - 64px);
  .drawer-head {
    display: flex;
    align-items: center;
    .t-icon {
      cursor: pointer;
    }
    h3 {
      margin-left: 10px;
    }
  }
  .drawer-body {
    li {
      display: flex;
      cursor: pointer;
      align-items: center;
      justify-content: space-between;
      &:hover {
        color: #4285f4;
      }

      p {
        width: 0;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
