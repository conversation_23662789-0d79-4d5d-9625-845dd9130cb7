import { getPageContentHeight } from '@gtff/tdesign-gt-vue';
import { getJycfResult } from '@/pages/index/api/common/commonApi';
import { getYwbmByURL } from '@/utils';
import { genUUID } from '@/pages/index/utils';
import { idxYwbm2YzpzzlDm } from '@/pages/index/utils/urlMap.js';
import { getCommonTip } from '@gt/components';

export default function () {
  return {
    data() {
      return {
        height: getPageContentHeight(),
        showSubmitLoading: true, // 是否展示loading
        moreOperation: {}, // 更多操作
        customFetchApi: false, // 是否自定义获取结果api。如果需要配合刷新，则复写 initSubmitResult() 并给 getSubmitResult 传入 api
        allowUpdate: true, // 是否允许刷新
        backBtnConfig: {
          text: '返回首页',
        },

        idxYwbm2YzpzzlDm, // 业务编码转代码
        // 不需要简易处罚的业务列表
        notGetJycfYwbmList: [
          'fssrtysb',
          'cbj',
          'yjtkfxzbjsb',
          'sytbsyjsb',
          'kqsyfyjsb',
          'kqsyfndsb',
          'fqdqdzcpcljjsb',
          'tysb',
          'whsyjsfdkdjsb',
        ],

        useOverrideSubTitleSlot: true,
      };
    },
    computed: {
      storeName() {
        if (!this.$route.meta.storeName) this.$gtDialog.error({ body: '路由中的meta必须要有storeName字段' });
        return this.$route.meta.storeName;
      },
      submitResult() {
        return this.$store.state[this.storeName].submitResult;
      },
      status() {
        const { returnFlag } = this.submitResult;
        const values = {
          Y: 'success',
          N: 'failed',
          W: 'warning',
        };
        return values[returnFlag || 'N'];
      },
      title() {
        const { submitResultTitle = '申报' } = this.$route.meta;
        const { returnFlag } = this.submitResult;
        const values = {
          Y: `${submitResultTitle}成功`,
          N: `${submitResultTitle}失败`,
          W: submitResultTitle.includes('提交') ? '提交中' : `${submitResultTitle}提交中`,
        };
        return values[returnFlag || 'N'];
      },
      subTitle() {
        const { submitResult } = this;
        return submitResult.returnFlag === 'Y' ? submitResult.message : submitResult.errInfo;
      },
      // 默认参数
      defaultSubmitParams() {
        const { storeName } = this;
        const { Ysqxxid } = this.$store.state[storeName];
        return { ysqxxid: Ysqxxid };
      },
      // 通过申报按钮
      btnGroupInCommonAfter() {
        return [
          {
            btnTxt: '立即缴款',
            isNewPage: true,
            generateUrl: () => {
              const uuid = genUUID();
              const url = `${window.top.location.origin}/skzx/view/skzs/jkkp?from=other&randomKey=${uuid}`;
              localStorage.setItem(`jkkpParam${uuid}`, JSON.stringify({ pzxh: this.submitResult.yzpzxh }));
              return url;
            },
          },
        ];
      },
      btnGroup() {
        const { btnMsgs } = this.$route.query;
        if (Array.isArray(btnMsgs)) {
          return btnMsgs;
        }
        const { returnFlag, ybtse } = this.submitResult;
        if (returnFlag === 'Y' && ybtse > 0) {
          // 立即缴款
          return [...this.btnGroupInCommonAfter];
        }
        return [];
      },
      // 是否展示评价按钮
      showEvaluationBtn() {
        const { allowEvaluate = false } = this.$route.meta;
        return allowEvaluate;
      },
      // 结果页配置
      evaluateConfig() {
        const state = this.$store.state[this.storeName];
        const { Ysqxxid, swsxDm = '', evaluateTcbz = '0' } = state;
        return {
          tcbz: evaluateTcbz,
          pjswsxList: [
            {
              sqlsh: Ysqxxid,
              swsxDm,
            },
          ],
        };
      },
      // 获取提交超时才显示刷新按钮
      showUpdateBtn() {
        const { returnFlag } = this.submitResult;
        const { allowUpdate } = this;
        return allowUpdate && returnFlag === 'W';
      },
      Ysqxxid({ $store }) {
        return $store.state[this.storeName].Ysqxxid || this.$route.query.ysqxxid;
      },
      // 保留
      state() {
        return this.$store.state[this.storeName];
      },
      // 保留
      nsrxx({ $store }) {
        return $store.state[this.storeName].nsrxx || '';
      },
    },
    created() {
      this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, this.$route.query);
      // 一事一评
      if (this.showEvaluationBtn && !this.$route.query.Ysqxxid) {
        // 兼容申报之前传的都是 ysqxxid，支持刷新
        this.$router.push({
          query: { ...this.$route.query, Ysqxxid: this.state.Ysqxxid || this.$route.query.ysqxxid },
        });
      }
    },
    mounted() {
      this.initSubmitResult();
    },
    methods: {
      /**
       * 检查提交是否成功，如果提交还没有成功，继续检查
       * 获取结果完成后调用 afterGetSubmitResult 函数
       */
      async getSubmitResult(apiFn) {
        const { submitResultTitle = '申报' } = this.$route.meta;
        const timeoutTip = `您发起的${submitResultTitle}正在提交中，稍后可刷新界面查看提交结果`;
        const errorTip = '您的申报提交超时，请稍后到“我要查询-一户式查询-申报信息查询”页面查询申报结果。';

        this.showSubmitLoading = true;
        this.submitResult.returnFlag = 'N';
        const { storeName } = this;
        const { pjhs } = this.$store.state[storeName];
        const api = apiFn || null;
        const params = {
          ...this.defaultSubmitParams,
          ...(await this.beforeGetSubmitResult()),
        };

        const maxTimes = 5; // 最多轮询次数
        let times = 0; // 轮询次数
        const intervalTimeList = [1.5, 1.5, 3, 4, 5];
        // 当提交接口返回pjhs的数值时，如果是在 0 到 5000 ms内，才算合理值
        const pjhsNum = Number(pjhs);
        const firstTime = pjhsNum > 0 && pjhsNum < 5000 ? (pjhsNum / 1000).toFixed(1) : 1.5;
        intervalTimeList[0] = firstTime;
        const lastTime = 5 - (firstTime - 1.5);
        // 确保最后一次轮询间隔大于等于 4s
        intervalTimeList[4] = lastTime >= 4 ? lastTime : 5;

        const loopFetchResult = () => {
          setTimeout(async () => {
            let res = null;
            // 如果设置了 customFetchApi 为 true 并传入了获取结果的api函数，则使用该函数获取提交结果
            if (api) {
              res = await this.fetchCheckResult(api, params);
            } else {
              res = await this.$store.dispatch(`${storeName}/GetSbResult`, params);
            }
            times += 1;
            if (res.bizCode === '00') {
              // 一事一评
              let swsxDm;
              let needPdf;
              if (Array.isArray(this.submitResult)) {
                swsxDm = this.submitResult[0].swsxDm;
                needPdf = this.submitResult[0].needPdf;
              } else {
                swsxDm = this.submitResult.swsxDm;
                needPdf = this.submitResult.needPdf;
              }

              if (this.showEvaluationBtn && (this.$route.query.swsxDm || this.$route.query.swsxDm !== swsxDm)) {
                this.$router.push({
                  query: { ...this.$route.query, swsxDm },
                });
                this.updatePayload({ swsxDm });
              }
              // 下载查看 pdf
              if (needPdf === 'Y') {
                const ywbm = getYwbmByURL();
                const { SssqQ, SssqZ } = this.$route.query;
                let path;
                if (ywbm === 'cwbbbs') {
                  const routerYwbm = this.$route.query.ywbm?.toLowerCase();
                  path = `/sbzx/view/sdsfsgjssb/#/yyzx/${ywbm}/pdf?SssqQ=${SssqQ}&SssqZ=${SssqZ}&ysqxxid=${this.Ysqxxid}&ywbm=${routerYwbm}`;
                } else if (ywbm === 'whsyjsf') {
                  const { ZspmDmList } = this.$route.query;
                  path = `/sbzx/view/sdsfsgjssb/#/yyzx/${ywbm}/pdf?SssqQ=${SssqQ}&SssqZ=${SssqZ}&ysqxxid=${this.Ysqxxid}&ZspmDmList=${ZspmDmList}`;
                } else {
                  path = `/sbzx/view/sdsfsgjssb/#/yyzx/${ywbm}/pdf?SssqQ=${SssqQ}&SssqZ=${SssqZ}&ysqxxid=${this.Ysqxxid}`;
                }
                // 将router上的ywbm传到下一个页面
                const pdfTitle = '您可能需要';
                const btnText = '查看';
                const desc = '查看、下载或打印本次申报表信息';
                const pdfOptions = {
                  btnText,
                  desc,
                  path,
                };
                if (Object.keys(this.moreOperation).length === 0) {
                  this.moreOperation = {
                    title: this.moreOperation.title || pdfTitle,
                    list: [pdfOptions],
                  };
                } else if (this.moreOperation.list?.length > 0) {
                  if (!this.moreOperation.list.find((item) => item.btnText === btnText && item.desc === desc)) {
                    this.moreOperation.title = this.moreOperation.title || pdfTitle;
                    this.moreOperation.list.push(pdfOptions);
                  }
                }
              }
              // 成功获取结果
              this.afterGetSubmitResult();
            } else if (res.bizCode === '99') {
              // 该情况下隐藏刷新按钮
              this.allowUpdate = false;
              // 发生异常
              this.afterGetSubmitResult();
            } else if (times >= maxTimes) {
              // 轮询结束
              this.$store.commit(`${storeName}/UPDATE_PAYLOAD`, {
                submitResult: {
                  returnFlag: 'W',
                  errInfo: timeoutTip,
                },
              });
              this.afterGetSubmitResult();
            } else {
              loopFetchResult();
            }
          }, intervalTimeList[times] * 1000);
        };

        loopFetchResult();
      },
      // 自定义获取提交结果
      async fetchCheckResult(apiFn, params) {
        const sbErrorTip = '您的申报提交超时，请稍后到“我要查询-一户式查询-申报信息查询”页面查询申报结果。';

        const res = await apiFn({
          ...params,
        });
        if (!res) {
          this.$store.commit('UPDATE_PAYLOAD', {
            submitResult: {
              returnFlag: 'N',
              errInfo: sbErrorTip,
            },
          });
          return {
            bizCode: '99',
          };
        }
        const { body, bizCode, bizMsg } = res;
        if (bizCode === '00' || bizCode === '01') {
          if (bizCode === '00') {
            const { returnFlag, errInfo, csbz } = body;
            if (returnFlag === 'N') {
              if (csbz === 'Y') {
                this.updatePayload({
                  submitResult: {
                    returnFlag: 'W',
                    errInfo,
                  },
                });
                return {
                  bizCode: '99',
                };
              }
              this.updatePayload({
                submitResult: {
                  returnFlag: 'N',
                  errInfo,
                },
              });
              return {
                bizCode: '99',
              };
            }
            this.updatePayload({
              submitResult: body,
            });
            return {
              bizCode,
            };
          }
          return {
            bizCode,
          };
        }
        this.updatePayload({
          submitResult: {
            returnFlag: 'N',
            errInfo: bizMsg || getCommonTip(),
          },
        });
        return {
          bizCode: '99',
        };
      },
      updatePayload(payload) {
        this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, payload);
      },
      // 获取结果前置回调，可以在这里返回获取结果需要的附加参数
      beforeGetSubmitResult() {
        return {};
      },
      // 检查完成后
      async afterGetSubmitResult() {
        this.showSubmitLoading = false;

        // 获取申报结果之后的回调
        this.afterCheckResult && this.afterCheckResult();

        // 判断是否申报成功
        if (this.submitResult && this.submitResult.returnFlag === 'Y') {
          // 多缴税款提示
          this.djskts && this.djskts();

          // 禅道21848 残保金不进行逾期处罚
          const ywbm = getYwbmByURL();
          if (this.submitResult.jycfBz === 'Y' && !this.notGetJycfYwbmList.includes(ywbm)) {
            // 获取简易处罚结果
            this.getJycf();
          }

          // 申报成功之后的回调
          this.afterSuccess && this.afterSuccess();
        }
      },
      // 刷新时会重新运行
      initSubmitResult() {
        !this.customFetchApi && this.getSubmitResult();
      },
      // 获取简易处罚结果
      getJycf() {
        let times = 5;
        const params = {
          Jycfrzlx: 'jycfrw',
          Sxuuid: this.Ysqxxid,
        };

        const next = async () => {
          times -= 1;
          const res = await getJycfResult(params);
          if (res.bizCode !== '00') {
            return;
          }

          if (res.body.retCode === '10' && times > 0) {
            setTimeout(() => {
              next();
            }, 3000);
          } else if (
            res.body.retCode === '01' ||
            res.body.retCode === '02' ||
            res.body.retCode === '04' ||
            res.body.retCode === '06'
          ) {
            // 01-简易处罚，02-首违不罚，03-到大厅办理，04-普通程序处罚,06-特殊情形到税务大厅办理，10-提交中，99-失败
            const { retxx, message, dbLink } = res.body;
            const dialog = this.$dialog.confirm({
              theme: 'info',
              width: '600px',
              header: '简易处罚',
              body: retxx || message,
              closeOnOverlayClick: false,
              closeOnEscKeydown: false,
              cancelBtn: '取消',
              confirmBtn: '确定',
              onConfirm: () => {
                dialog.hide();

                if (dbLink) {
                  window.open(dbLink);
                }
              },
              onClose: () => {
                dialog.hide();
              },
            });
          } else if (res.body.retCode === '03' || res.body.retCode === '99') {
            // 03-到大厅办理、99-失败
            const { error } = console;
            error(`调用简易处罚失败：${res.body.retxx}`);
          }
        };

        // 方法调用
        next();
      },
    },
  };
}
