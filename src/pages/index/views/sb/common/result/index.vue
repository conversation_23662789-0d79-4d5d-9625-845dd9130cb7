<template>
  <ResultPage
    :height="height"
    :loading="showSubmitLoading"
    :btn-group="btnGroup"
    :more-operation="moreOperation"
    :title="title"
    :status="status"
    :sub-title="subTitle"
    :back-btn-config="backBtnConfig"
    :show-evaluation-btn="showEvaluationBtn"
    :evaluate-config="evaluateConfig"
    :useOverrideSubTitleSlot="useOverrideSubTitleSlot"
    :show-update-btn="showUpdateBtn"
    :on-update="initSubmitResult"
  >
  </ResultPage>
</template>

<script>
import useSbResult from '@/pages/index/views/sb/common/result/useSbResult';
import ResultPage from '@/pages/index/components/result-page';

export default {
  name: 'app-jg',
  components: {
    ResultPage,
  },
  mixins: [useSbResult()],
};
</script>
