<template>
  <div style="display: flex; overflow-y: hidden; background-color: #f3f3f3; flex: 1; flex-direction: column">
    <div style="height: 60px">
      <Mybreadcrumb ref="myBre" style="width: 100%; background-color: #fff" />
    </div>

    <div class="content">
      <table border="0" class="my-table">
        <tr>
          <td class="lable_ct" colspan="14">
            <strong><span style="font-size: 18px">研发支出辅助账</span></strong>
          </td>
        </tr>
        <tr class="row">
          <td class="lable_ct" colspan="3"><strong>项目编号：</strong></td>
          <td class="lable_ct" colspan="2"><strong>202416106310000001</strong></td>
          <td class="lable_ct"><strong>项目名称</strong></td>
          <td class="lable_ct"><strong>研发项目1</strong></td>
          <td class="lable_ct"><strong>完成情况:</strong></td>
          <td class="lable_ct"><strong>未完成</strong></td>
          <td class="lable_ct"><strong>支出类型：</strong></td>
          <td class="lable_ct"><strong>费用化</strong></td>
          <td class="lable_ct"><strong></strong></td>
          <td class="lable_ct"><strong>金额单位：元</strong></td>
          <td class="lable_ct"><strong></strong></td>
        </tr>

        <tr class="row">
          <td class="lable_ct" colspan="4" rowspan="2"><strong>凭证信息</strong></td>
          <td class="lable_ct" rowspan="3"><strong>会计凭证记账金额</strong></td>
          <td class="lable_ct" rowspan="3"><strong>税收规定的归集金额</strong></td>
          <td class="lable_ct" colspan="8"><strong>费用明细（税收法规）</strong></td>
        </tr>
        <tr class="row">
          <td class="lable_ct" rowspan="2"><strong>人员人工费用</strong></td>
          <td class="lable_ct" rowspan="2"><strong>直接投入费用</strong></td>
          <td class="lable_ct" rowspan="2"><strong>折旧费用</strong></td>
          <td class="lable_ct" rowspan="2"><strong>无形资产摊销</strong></td>
          <td class="lable_ct" rowspan="2"><strong>新产品设计费等</strong></td>
          <td class="lable_ct" rowspan="2"><strong>其他相关费用</strong></td>
          <td class="lable_ct" colspan="2"><strong>委托研发费用</strong></td>
        </tr>
        <tr class="row">
          <td class="lable_ct"><strong>日期</strong></td>
          <td class="lable_ct"><strong>种类</strong></td>
          <td class="lable_ct"><strong>号数</strong></td>
          <td class="lable_ct"><strong>摘要</strong></td>
          <td class="lable_ct"><strong>委托境内机构或个人惊醒研发活动所发生的费用</strong></td>
          <td class="lable_ct"><strong>委托境外机构进行研发活动所发生的费用</strong></td>
        </tr>

        <template v-for="item in dataArr">
          <tr>
            <td class="lable_lf">{{ item.rq }}</td>
            <td class="lable_lf">{{ item.zl }}</td>
            <td class="lable_lf">{{ item.hs }}</td>
            <td class="lable_lf">{{ item.zy }}</td>
            <td class="lable_lf">{{ item.kjpzjzje }}</td>
            <td class="lable_lf">{{ item.sfgddzzje }}</td>
            <td class="lable_lf">{{ item.ryrgfy }}</td>
            <td class="lable_lf">{{ item.zjtrfy }}</td>
            <td class="lable_lf">{{ item.zjfy }}</td>
            <td class="lable_lf">{{ item.wxzctx }}</td>
            <td class="lable_lf">{{ item.xcpsjfy }}</td>
            <td class="lable_lf">{{ item.qtxgfy }}</td>
            <td class="lable_lf">{{ item.wtjnfy }}</td>
            <td class="lable_lf">{{ item.wtjwfy }}</td>
          </tr>
        </template>
        <tr>
          <td class="lable_ct" colspan="4">合计</td>
          <td class="lable_lf">600,000,00</td>
          <td class="lable_lf">600,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">1,000,000,00</td>
          <td class="lable_lf">0</td>
          <td class="lable_lf">0</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb.vue';

export default {
  name: 'zxlr',
  components: { Mybreadcrumb },

  data() {
    return {
      dataArr: [
        {
          rq: '2024-05-10',
          zl: '收',
          hs: '233',
          zy: 'xxx',
          kjpzjzje: '100,000,00',
          sfgddzzje: '100,000,00',
          ryrgfy: '1,000,000,00',
          zjtrfy: '1,000,000,00',
          zjfy: '1,000,000,00',
          wxzctx: '1,000,000,00',
          xcpsjfy: '1,000,000,00',
          qtxgfy: '1,000,000,00',
          wtjnfy: '0',
          wtjwfy: '0',
        },
        {
          rq: '2024-05-09',
          zl: '收',
          hs: '232',
          zy: 'xxx',
          kjpzjzje: '200,000,00',
          sfgddzzje: '200,000,00',
          ryrgfy: '0',
          zjtrfy: '0',
          zjfy: '0',
          wxzctx: '0',
          xcpsjfy: '0',
          qtxgfy: '0',
          wtjnfy: '0',
          wtjwfy: '0',
        },
        {
          rq: '2024-05-06',
          zl: '收',
          hs: '231',
          zy: 'xxx',
          kjpzjzje: '300,000,00',
          sfgddzzje: '300,000,00',
          ryrgfy: '0',
          zjtrfy: '0',
          zjfy: '0',
          wxzctx: '0',
          xcpsjfy: '0',
          qtxgfy: '0',
          wtjnfy: '0',
          wtjwfy: '0',
        },
      ],
    };
  },

  mounted() {
    // 通知面包屑组件
    const parmObj = {
      mybreadList: ['首页', '辅助台账明细'], // 【必填】面包屑层级
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      goBackPath: '/yfjjkctzgl', // 【选填】点击回退时，跳转的路径。一般是项目内，
    };
    this.$refs.myBre.initMyBre(parmObj);
  },
};
</script>

<style lang="less" scoped>
.content {
  position: fixed;
  top: 72px;
  right: 16px;
  bottom: 16px;
  left: 16px;
  padding: 16px;
  background-color: #fff;
}
.my-table {
  width: 100%;
  background-color: #fff;
  border: 0;
  border-collapse: collapse;
  table-layout: fixed;

  tr {
    height: 40px;
  }

  td {
    padding: 10px;
    vertical-align: middle;
    border: 1px solid #dcdcdd;
    box-sizing: border-box;
  }
}
.lable_ct {
  text-align: center;
}
.lable_lf {
  text-align: left;
}
</style>
