<template>
  <div>
    <t-dialog
      :visible="visible"
      header="新增"
      confirmBtn="提交"
      cancelBtn="关闭"
      width="500px"
      placement="center"
      :closeOnOverlayClick="false"
      :onConfirm="confirm"
      :onClose="close1"
      :onCancel="close1"
    >
      <t-form>
        <t-form-item label="研发项目名称" name="yfxmmc">
          <t-select
            v-model="formData.yfxmmc"
            :options="[
              { label: '研发项目1', value: '1' },
              { label: '研发项目2', value: '2' },
              { label: '研发项目3', value: '3' },
            ]"
          />
        </t-form-item>
        <t-form-item label="优惠事项名称" name="yhsxmc">
          <t-select v-model="formData.yhsxmc" :options="yhsxmcArr" @change="change" />
        </t-form-item>
        <t-form-item label="优惠事项代码" name="yhsxdm">
          <t-input disabled placeholder="请输入" v-model="formData.yhsxdm" />
        </t-form-item>
        <t-form-item label="减税收入比例" name="jssrbl">
          <t-input placeholder="请输入" v-model="formData.jssrbl" />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script>
export default {
  name: 'qsgzDia.vue',
  data() {
    return {
      formData: {},
      visible: false,
      yhsxmcArr: [
        {
          label: '企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除（集成电路和工业母机企业按120%加计扣除）',
          value: 'JJKC014',
        },
        {
          label: '企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除（按100%加计扣除）',
          value: 'JJKC015',
        },
        {
          label:
            '企业为获得创新性、创意性、突破性的产品进行创意设计活动发生的相关费用加计扣除（集成电路和工业母机企业按120%加计扣除）',
          value: 'JJKC016',
        },
        {
          label: '企业为获得创新性、创意性、突破性的产品进行创意设计活动发生的相关费用加计扣除（按100%加计扣除）',
          value: 'JJKC025',
        },
        {
          label: '企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除（集成电路和工业母机企业按120%加计扣除）',
          value: 'JJKC014',
        },
        {
          label: '企业投入基础研究支出加计扣除（按100%加计扣除）',
          value: 'JJKC031',
        },
        {
          label: '直接从事研发活动人员工资薪金',
          value: 'NDKC001',
        },
        {
          label: '直接从事研发活动人员五险一金',
          value: 'NDKC002',
        },
        {
          label: '外聘研发人员的劳务费用',
          value: 'NDKC003',
        },
        {
          label: '研发活动直接消耗材料费用',
          value: 'NDKC004',
        },
        {
          label: '新产品设计费',
          value: 'NDKC005',
        },
      ],
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    confirm() {
      console.log(this.formData);
      // this.$emit('update:visible', false);
      this.visible = false;
    },
    close1() {
      this.visible = false;
    },
    change(value) {
      this.formData.yhsxdm = value;
    },
  },
};
</script>

<style scoped></style>
