<template>
  <div style="background: #f9fafd; flex: 1">
    <pdf v-for="i in numPages" :key="i" :page="i" :src="pdfUrl" style="width: 100%; height: 100%" />
  </div>
</template>

<script>
import pdf from 'vue-pdf';

export default {
  components: {
    pdf,
  },
  data() {
    return {
      pageCount: 0, // 当前页
      pdfUrl: '',
      src: '', // pdf文件地址
      numPages: 0, // 总页数
    };
  },
  mounted() {
    console.log(this.$route.query, '0--');
    const ff = this.$route.query.type === 'nd' ? '/tz_ndb.pdf' : '/tz_ydb.pdf';
    this.src = `${process.env.VUE_APP_CDN_PATH}/${ff}`;
    console.log('src', this.src);

    console.log('src', this.src);
    this.loadPdfHandler();
  },
  methods: {
    // loadPdfHandler() {
    //   this.pdfUrl = pdf.createLoadingTask(this.src);
    //   this.pdfUrl.promise.then((pdf) => {
    //     console.log('pdf', pdf);
    //     this.numPages = pdf.numPages;
    //   });
    // },
    loadPdfHandler() {
      this.pdfUrl = pdf.createLoadingTask(this.src);
      this.pdfUrl.promise.then((pdf) => {
        console.log('pdf', pdf);
        this.numPages = pdf.numPages;
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
