<template>
  <div>
    <t-dialog
      :closeOnOverlayClick="false"
      :header="title"
      :visible="visible"
      width="800px"
      :onConfirm="onConfirm"
      :onCancel="onCancel"
      :onClose="onCancel"
      confirmBtn="保存"
      :destroyOnClose="true"
    >
      <t-divider style="margin-top: 0"></t-divider>
      <div style="padding: 0 16px">
        <t-form labelWidth="112px" ref="forms" :data="formData" :rules="rules" @submit="submit">
          <t-row>
            <t-col :span="6">
              <t-form-item label="取数来源" name="qsly">
                <t-select v-model="formData.qsly">
                  <t-option v-for="(item, index) in qslyArr" :value="item.value" :label="item.label" :key="index">
                    {{ item.label }}
                  </t-option>
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="科目名称" name="kmmc">
                <t-tree-select
                  style="width: 500px"
                  :data="kmmcArr"
                  v-model="formData.kmmc"
                  :treeProps="{ expandAll: true, valueMode: 'onlyLeaf' }"
                  filterable
                  clearable
                  placeholder="请选择"
                  @blur="onBlurTrigger"
                  @change="onChange"
                />
              </t-form-item>
            </t-col>
          </t-row>

          <t-row>
            <t-col :span="6">
              <t-form-item label="科目编码" name="kmmb">
                <t-input disabled v-model="formData.knbm" placeholder="请输入内容" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="借贷方" name="jdf">
                <t-select v-model="formData.jdf">
                  <t-option v-for="(item, index) in jdftArr" :value="item.value" :label="item.label" :key="index">
                    {{ item.label }}
                  </t-option>
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>

          <t-row>
            <t-col :span="6">
              <t-form-item label="计算类型" name="jslx">
                <t-select v-model="formData.jslx">
                  <t-option v-for="(item, index) in jslxArr" :value="item.value" :label="item.label" :key="index">
                    {{ item.label }}
                  </t-option>
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </div>

      <t-divider style="margin-top: 24px; margin-bottom: 0"></t-divider>
    </t-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs';

const BASE_DATA = {
  qsly: '',
  kmmc: '',
  knbm: '',
  jdf: '',
  jslx: '',
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: '规则设置',
      rules: {
        jrdwtyshxydm: [{ required: true, message: '接入单位统一社会信用代码必填', type: 'error' }],
        jrdwnsrmc: [{ required: true, message: '接入单位纳税人名称必填', type: 'error' }],
        jryyuuid: [{ required: true, message: '接入系统必填', type: 'error' }],
        yxqq: [{ required: true, message: '有效期起必填', type: 'error' }],
        yxqz: [{ required: true, message: '有效期止必填', type: 'error' }],
      },
      formData: { ...BASE_DATA },
      qslyArr: [
        { label: '辅助明细账', value: '0' },
        { label: '科目余额表', value: '1' },
      ],
      jdftArr: [
        { label: '本期借方', value: '0' },
        { label: '本期贷方', value: '1' },
        { label: '本期借方减贷方', value: '2' },
        { label: '本期贷方减借方', value: '3' },
        { label: '期初借方', value: '4' },
        { label: '期初贷方', value: '5' },
        { label: '期末借方', value: '6' },
        { label: '期末贷方', value: '7' },
      ],
      jslxArr: [
        { label: '累加', value: '0' },
        { label: '相减', value: '1' },
      ],
      kmmcArr: [
        {
          label: '一 、直接成本',
          value: 'zjcb',
          children: [
            {
              label: '研发活动直接消耗的材料',
              value: '101001',
            },
            {
              label: '研发活动直接消耗的燃料',
              value: '101002',
            },
            {
              label: '研发活动直接消耗的动力费用',
              value: '101003',
            },
            {
              label: '用于中间试验和产品试制的模具、工艺装备开发及制造费',
              value: '101004',
            },
            {
              label: '用于不构成固定资产的样品、样机及一般测试手段购置费',
              value: '101005',
            },
            {
              label: '新产品设计费',
              value: '101005',
            },
          ],
        },
        {
          label: '二、折旧数据',
          value: 'zjsh',
          children: [
            {
              label: '用于研发活动的仪器的折旧费',
              value: '102001',
            },
            {
              label: '用于研发活动的设备的折旧费',
              value: '102002',
            },
            {
              label: '用于研发活动的软件的摊销费用',
              value: '102003',
            },
            {
              label: '用于研发活动的专利权的摊销费用',
              value: '102004',
            },
            {
              label: '用于研发活动的非专利技术（包括许可证、专有技术、设计和计算方法等）的摊销费用',
              value: '102005',
            },
          ],
        },
        {
          label: '三、薪资数据',
          value: 'xzsj',
          children: [
            {
              label: '直接从事研发活动人员的工资薪金',
              value: '103001',
            },
            {
              label: '直接从事研发活动人员的五险一金',
              value: '103002',
            },
            {
              label: '外聘研发人员的劳务费用',
              value: '103003',
            },
          ],
        },
      ],
    };
  },

  computed: {},

  methods: {
    initData() {
      this.title = '新增接入应用';
      this.formData = { ...BASE_DATA };
    },

    onCancel() {
      this.$emit('update:visible', false);
    },
    submit({ validateResult }) {
      this.$emit('update:visible', false);
    },
    onBlurTrigger(context) {
      console.log('blur:', context);
    },
    onChange(value, context) {
      this.formData.knbm = value;
    },
    onConfirm() {
      this.$refs.forms.submit();
    },
  },
};
</script>
<style scoped lang="less"></style>
