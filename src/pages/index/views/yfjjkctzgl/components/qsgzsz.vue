<template>
  <div style="overflow: hidden; background: #f9fafd; flex: 1">
    <Mybreadcrumb ref="myBre" />

    <div class="mainContentDiv">
      <div class="topSearch" style="display: flex; align-items: center; margin: 24px 0">
        <span>研发项目名称</span>
        <t-input clearable placeholder="请输入" style="width: 300px; margin-right: 24px; margin-left: 10px" />
        <t-button theme="primary">查询</t-button>
      </div>
      <t-button theme="primary" style="margin-bottom: 24px" @click="addItem">新增</t-button>
      <t-table
        row-key="index"
        :data="tableData"
        :columns="columns"
        :loading="tableLoading"
        table-layout="fixed"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        @select-change="selectChange"
        @change="pagiChage"
      >
        <template #xh="{ rowIndex }">
          {{ pagination.pageSize * (pagination.current - 1) + rowIndex + 1 }}
        </template>
        <template #cz="{ row }">
          <span style="margin-right: 8px; color: #4285f4; cursor: pointer" @click="gzsz(row)">规则设置</span>
          <span style="margin-right: 8px; color: #4285f4; cursor: pointer" @click="checkItem(row)">编辑</span>
          <span style="margin-right: 8px; color: #4285f4; cursor: pointer" @click="checkItem(row)">删除</span>
        </template>
      </t-table>
    </div>
    <qsgzDiaView ref="qsgzRef"></qsgzDiaView>
    <gzszDialog :visible.sync="showDialog" ref="editRefs"></gzszDialog>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import qsgzDiaView from '@/pages/index/views/yfjjkctzgl/components/qsgzDia.vue';
import gzszDialog from '@/pages/index/views/yfjjkctzgl/components/gzsz-dialog.vue';

export default {
  components: {
    Mybreadcrumb,
    qsgzDiaView,
    gzszDialog,
  },
  data() {
    return {
      showDialog: false,
      tableData: [],
      tableLoading: false,
      columns: [
        {
          width: 150,
          colKey: 'yfxmmc',
          align: 'center',
          title: '研发项目名称',
          ellipsis: true,
        },
        {
          width: 150,
          colKey: 'yhsxdm',
          align: 'center',
          title: '优惠事项代码',
          ellipsis: true,
        },
        {
          width: 150,
          colKey: 'yhsxmc',
          align: 'center',
          title: '优惠事项名称',
          ellipsis: true,
        },
        {
          width: 100,
          colKey: 'jssrbl',
          align: 'center',
          title: '减税收入比例',
          ellipsis: true,
        },
        {
          colKey: 'cz',
          title: '操作',
          width: 100,
          ellipsis: true,
          fixed: 'right',
        },
      ],
      pagination: {
        pageSizeOptions: [10, 20, 50],
        current: 1,
        pageSize: 10,
        total: 0,
      },
      selectedRowKeys: [],
      selectDatas: [],
    };
  },
  mounted() {
    const parmObj = {
      mybreadList: ['首页', '取数规则设置'],
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      goBackPath: '/yfjjkctzgl', // 【选填】点击回退时，跳转的路径。一般是项目内，
    };
    this.$refs.myBre.initMyBre(parmObj);

    this.getData();
  },
  methods: {
    getData() {
      const dm = ['NDKC004', 'JJKC022', 'JJKC022'];
      const mc = [
        '直接从事研发活动人员工资薪金',
        '企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除（按100%加计扣除）',
        '企业投入基础研究支出加计扣除（按100%加计扣除）',
      ];
      for (let i = 0; i < 3; i++) {
        const obj = {
          yfxmmc: `研发项目${i + 1}`,
          yhsxdm: dm[i],
          yhsxmc: mc[i],
          jssrbl: '0',
        };
        this.tableData.push(obj);
      }
      this.pagination.total = this.tableData.length;
    },
    addItem() {
      this.$refs.qsgzRef.show();
      console.log(123);
    },
    pagiChage(params) {
      this.pagination.current = params.pagination.current;
      this.pagination.pageSize = params.pagination.pageSize;
      console.log(params);
    },
    selectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.selectDatas = selectedRowData;
      console.log(value, this.selectDatas, '-------');
    },
    gzsz(item) {
      this.showDialog = true;
    },
  },
};
</script>

<style scoped>
.mainContentDiv {
  position: fixed;
  top: 60px;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 0 24px;
  margin: 24px;
  background: #fff;
  border-radius: 4px;
}
</style>
