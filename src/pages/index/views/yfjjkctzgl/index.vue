<template>
  <div style="overflow: hidden; background: #f9fafd; flex: 1">
    <Mybreadcrumb ref="myBre" />

    <div class="mainContentDiv">
      <div class="topSearch" style="display: flex; align-items: center; margin: 24px 0">
        <div style="flex: 1; display: flex; align-items: center">
          <span>研发项目名称</span>
          <t-input clearable placeholder="请输入" style="width: 250px; margin-left: 10px" />
        </div>
        <t-button theme="primary" @click="btnAction('1')">查询</t-button>
        <t-button theme="primary" @click="btnAction('2')">提取项目</t-button>
        <t-button theme="primary" @click="btnAction('3')">取数规则设置</t-button>
        <t-button theme="primary" @click="btnAction('4')">提取辅助明细表</t-button>
        <t-button theme="primary" @click="btnAction('5')">生成并查看月季预填</t-button>
        <t-button theme="primary" @click="btnAction('6')">生成并查看年报预填</t-button>
      </div>
      <t-table
        row-key="xmbm"
        :data="yfjjkctzglArr"
        :columns="columns"
        hover
        :loading="tableLoading"
        table-layout="fixed"
        :pagination="pagination"
        :selected-row-keys="selectedRowKeys"
        @select-change="selectChange"
        @change="pagiChage"
      >
        <template #xh="{ rowIndex }">
          {{ pagination.pageSize * (pagination.current - 1) + rowIndex + 1 }}
        </template>
        <template #cz="{ row }">
          <div class="options">
            <div style="margin-right: 22px">
              <span @click="checkItem(row)">查看</span>
            </div>
            <div>
              <t-upload
                :on-success="onSuccess"
                :on-error="onError"
                action="https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo"
                :auto-upload="true"
                :multiple="true"
                :show-upload-progress="true"
              >
                <span>上传附件</span>
              </t-upload>
            </div>
            <div>
              <span @click="downloadFile('ckdg.pdf')">下载附件</span>
            </div>
          </div>
        </template>
      </t-table>
    </div>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import { download } from '@gtff/tdesign-gt-vue/icon/icon-map';
import { mapMutations, mapState } from 'vuex';

export default {
  name: 'index',
  components: {
    Mybreadcrumb,
  },
  data() {
    return {
      tableData: [],
      files: [],
      tableLoading: false,
      urltt: 'https://service-bv448zsw-1257786608.gz.apigw.tencentcs.com/api/upload-demo',
      columns: [
        {
          colKey: 'row-select',
          type: 'single',
          title: '选择',
          width: 48,
          fixed: 'left',
        },
        {
          width: 200,
          colKey: 'xmbm',
          align: 'center',
          title: '研发项目编码',
          ellipsis: true,
        },
        {
          width: 150,
          colKey: 'xmmc',
          align: 'center',
          title: '研发项目名称',
          ellipsis: true,
        },
        {
          colKey: 'ryrgfy',
          title: '人员人工费用',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'zjtrfy',
          title: '直接投入费用',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'zjfy',
          title: '折旧费用',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'wxzctx',
          title: '无形资产摊销',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'xcpsjfy',
          title: '新产品设计费用',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'qtxgfy',
          title: '其他相关费用',
          width: 150,
          align: 'center',
          ellipsis: true,
        },
        {
          colKey: 'cz',
          title: '操作',
          width: 220,
          ellipsis: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      pagination: {
        pageSizeOptions: [10, 20, 50],
        current: 1,
        pageSize: 10,
        total: 0,
      },
      selectedRowKeys: [],
      selectDatas: [],
    };
  },
  computed: {
    ...mapState('yfjjkctzgl', ['yfjjkctzglArr']),
  },
  mounted() {
    this.pagination.total = this.yfjjkctzglArr.length;

    const parmObj = {
      mybreadList: ['首页', '研发加计扣除台账管理'],
      // 【必填】面包屑层级
      // isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // goBackPath:'/jsqxgl/jsqxglmain',//【选填】点击回退时，跳转的路径。一般是项目内，
    };
    this.$refs.myBre.initMyBre(parmObj);
  },
  methods: {
    ...mapMutations('yfjjkctzgl', ['inityfjjkctzglData']),

    btnAction(key) {
      switch (key) {
        case '1':
          this.initData();
          break;
        case '2':
          this.initData();
          break;
        case '3':
          this.$router.push({ path: '/qsgzsz' });
          break;
        case '4':
          this.setPageData();
          break;
        case '5':
          // this.$router.push({ path: '/tz_pdf', query: { type: 'jd' } });
          // window.open('/tz_pdf?type=jd','_blank')

          window.open('/znsb/view/tzzx/tz_pdf?type=jd', '_blank');

          // const url1 = this.$router.resolve({ path: '/tz_pdf?type=jd' });
          // window.open(url1.href, '_blank');
          break;
        case '6':
          // this.$router.push({ path: '/tz_pdf', query: { type: 'nd' } });
          // window.open('/tz_pdf?type=nd','_blank')
          // const url = this.$router.resolve({ path: '/tz_pdf?type=nd' });
          // window.open(url.href, '_blank');

          window.open('/znsb/view/tzzx/tz_pdf?type=nd', '_blank');

          break;
      }
    },
    onSuccess() {
      console.log('上传成功');
      this.$message.success('上传成功');
    },
    onError() {
      console.log('上传失败');
      this.$message.success('上传成功');
    },
    downloadFile(fileName) {
      const folderPath = '/znsb/view/tzzx/'; // 文件所在的文件夹路径
      const fileUrl = folderPath + fileName; // 拼接文件夹路径和文件名
      console.log('fileUrl', fileUrl);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.setAttribute('download', fileName);
      link.click();
      this.$message.success('下载成功');
    },
    initData() {
      this.tableData = [];
      for (let i = 1; i < 4; i++) {
        const obj = {
          xmbm: `2024161063100000000${i}`,
          xmmc: `研发项目${i}`,
          ryrgfy: '0',
          zjtrfy: '0',
          zjfy: '0',
          wxzctx: '0',
          xcpsjfy: '0',
          qtxgfy: '0',
        };
        this.tableData.push(obj);
      }
      this.inityfjjkctzglData(this.tableData);
      this.pagination.total = this.yfjjkctzglArr.length;
    },
    setPageData() {
      this.$message.success('提取辅助明细信息成功');
      this.$set(this.yfjjkctzglArr[0], 'ryrgfy', '1,000,000,00');
      this.$set(this.yfjjkctzglArr[0], 'zjtrfy', '1,000,000,00');
      this.$set(this.yfjjkctzglArr[0], 'zjfy', '1,000,000,00');
      this.$set(this.yfjjkctzglArr[0], 'wxzctx', '1,000,000,00');
      this.$set(this.yfjjkctzglArr[0], 'xcpsjfy', '1,000,000,00');
      this.$set(this.yfjjkctzglArr[0], 'qtxgfy', '1,000,000,00');
      this.pagination.total = this.yfjjkctzglArr.length;

      console.log(this.yfjjkctzglArr);
    },
    checkItem(item) {
      console.log(item);
      this.$router.push('/fztzmx');
    },
    upload(item) {
      // 上传附件
      console.log(item);
    },
    download(item) {
      // 下载附件
      console.log(item);
    },
    pagiChage(params) {
      this.pagination.current = params.pagination.current;
      this.pagination.pageSize = params.pagination.pageSize;
      console.log(params);
    },
    selectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.selectDatas = selectedRowData;
      console.log(value, this.selectDatas, '-------');
    },
  },
};
</script>

<style lang="less" scoped>
.mainContentDiv {
  position: fixed;
  top: 60px;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 0 24px;
  margin: 24px;
  background: #fff;
  border-radius: 4px;
}
.topSearch button {
  margin-left: 10px;
}
.options {
  div {
    display: inline-block;
    margin-right: 10px;
    color: #4285f4;
    cursor: pointer;
  }
}
</style>
