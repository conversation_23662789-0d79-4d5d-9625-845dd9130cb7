import dayjs from 'dayjs';

export const querySearchConfig = [
  {
    label: '企业名称',
    key: 'gshList',
    type: 'select',
    multiple: true,
    value: [],
    selectList: [],
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '所属期起',
    key: 'ssqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '所属期止',
    key: 'ssqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmbm',
    type: 'select',
    value: '',
    selectList: [
      { value: '52060102', label: '其他收益-增值税加计抵减' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
export const tableColumns = [
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 180,
    colKey: 'gsh',
    align: 'center',
    title: '公司号',
  },
  {
    width: 240,
    colKey: 'qymc',
    align: 'center',
    title: '企业名称',
  },
  {
    width: 100,
    colKey: 'sszqq',
    title: '所属期起',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'sszqz',
    title: '所属期止',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'qcye',
    align: 'center',
    title: '期初余额',
  },
  {
    width: 200,
    colKey: 'bqfse',
    align: 'center',
    title: '本期发生额',
  },
  {
    width: 200,
    colKey: 'bqtje',
    align: 'center',
    title: '本期调减额',
  },
  {
    width: 100,
    colKey: 'bqkdje',
    align: 'center',
    title: '本期可抵减额',
  },
  {
    width: 150,
    colKey: 'bqsjdje',
    align: 'center',
    title: '本期实际抵减额',
  },
  {
    width: 150,
    colKey: 'qmye',
    align: 'center',
    title: '期末余额',
  },
  {
    width: 200,
    colKey: 'kmbm',
    align: 'center',
    title: '科目编码',
  },
  {
    width: 150,
    colKey: 'kmmc',
    align: 'center',
    title: '科目名称',
  },
  {
    width: 200,
    colKey: 'pzbh',
    align: 'center',
    title: '凭证编号',
  },
  {
    width: 150,
    colKey: 'jzje',
    align: 'center',
    title: '记账金额',
  },
];
