import dayjs from 'dayjs';

export const querySearchConfig = [
  {
    label: '企业名称',
    key: 'gshList',
    type: 'select',
    multiple: true,
    value: [],
    selectList: [],
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '所属期起',
    key: 'ssqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '所属期止',
    key: 'ssqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '抵减项目',
    key: 'kmbm',
    type: 'select',
    value: '',
    selectList: [
      { value: '21710109', label: '应交税金—应交增值税—减免税款' },
      { value: '21710150', label: '应交税金—预交增值税' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
export const tableColumns = [
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 180,
    colKey: 'gsh',
    align: 'center',
    title: '公司号',
  },
  {
    width: 240,
    colKey: 'qymc',
    align: 'center',
    title: '企业名称',
  },
  {
    width: 100,
    colKey: 'sszqq',
    title: '所属期起',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'sszqz',
    title: '所属期止',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'djxm',
    align: 'center',
    title: '抵减项目',
  },
  {
    width: 100,
    colKey: 'qcye',
    align: 'center',
    title: '期初余额',
  },
  {
    width: 200,
    colKey: 'bqye',
    align: 'center',
    title: '本期余额',
  },
  {
    width: 100,
    colKey: 'bqkdje',
    align: 'center',
    title: '本期可抵减额',
  },
  {
    width: 150,
    colKey: 'bqsjdje',
    align: 'center',
    title: '本期实际抵减额',
  },
  {
    width: 150,
    colKey: 'qmye',
    align: 'center',
    title: '期末余额',
  },
  {
    width: 200,
    colKey: 'kmbm',
    align: 'center',
    title: '科目编码',
  },
  {
    width: 150,
    colKey: 'kmmc',
    align: 'center',
    title: '科目名称',
  },
  {
    width: 150,
    colKey: 'pzbh',
    align: 'center',
    title: '凭证编号',
  },
  {
    width: 150,
    colKey: 'jzje',
    align: 'center',
    title: '记账金额',
  },
];
