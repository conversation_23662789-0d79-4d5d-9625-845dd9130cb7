<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="ggMenu">
      <div :style="{ height: '100%' }">
        <div class="znsbBackGroupDiv adaption-wrap">
          <search-control-panel
            class="znsbHeadqueryDiv"
            ref="queryControl"
            :config="querySearchConfig"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
          />
          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">        
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
              <QsbButton />
            </gt-space>
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="uuid"
              height="100%"
              hover
              :data="tableData"
              :columns="tableColumns"
              :selected-row-keys="selectedRowKeys"
              :pagination="pagination"
              @page-change="pageChange($event)"
              :loading="tableLoading"
              :foot-data="footData"
            >
              <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
              <template #sfzzshzsb="{ row }">
                {{ { Y: '是', N: '否' }[row.sfzzshzsb] || '' }}
              </template>
            </t-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import QsbButton from '@/pages/index/components/QsbButton';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  queryByCondition,
  queryQyList,   
} from '@/pages/index/api/dlzg/ydyjtz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { querySearchConfig, tableColumns } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
    RollbackIcon,
    Mybreadcrumb,
  },
  data() {
    this.tableColumns = tableColumns;
    return {
      formData: {}, 
      dcLoading: false,
      querySearchConfig,
      tableLoading: false,
      queryParams: {},
      delformData: [],
      checkBox: [],
      selectedRowKeys: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      tableData: [],
      footData: [],
      tips: '',
      gsh: '',
      qyList: [],
    };
  },
  async created() {
    // const userInfo = window.sessionStorage.getItem('jgxxList');
    // this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    // if (process.env.VUE_APP_MODEL === 'local') {
    //   this.$store.commit('zzstz/setUserInfoData', {
    //     jguuid: 'sichuanlqcs000000000000000000003',
    //     jgmc: '1234321|南充南百大珠宝有限公司',
    //     xzqhmc: '四川省',
    //     nsrsbh: '91310112773731630K',
    //     djxh: '10013101001120017369',
    //     xzqhszDm: '510000',
    //     qydmz: '1100',
    //     nsrlx: '1',
    //   });
    // }
    // console.log(1111111111111111111111111);
    // try {
    //   const res = await companyDifferentiationConfigApi({
    //     nsrsbh: JSON.parse(userInfo)?.nsrsbh,
    //     djxh: JSON.parse(userInfo)?.djxh,
    //   });
    //   this.gsh = res.data?.jtbm || '';
    // } catch (e) {
    //   console.error('企业类型查询失败:', e);
    // }
    this.initQyList();
    //this.query();
  },
  mounted() {
    const paramObj = {
      mybreadList: ['首页', '异地预缴台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
      isxlxShow: false,
    };
    this.$refs.myBre.initMyBre(paramObj);
  },
  watch: {
    formData: {
      handler(newVal) {
        console.log('formData changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
  },
  computed: {
  },
  methods: {
    async initQyList() {
      try {
        const { data } = await queryQyList();
        this.querySearchConfig[0].selectList = data.map((d) => ({ value: d.xgsdm, label: d.gsmc }));
      } catch (e) {
        console.error('查询企业列表失败:', e);
      }
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      // stopPollingYhs();
      // // 使用 await 等待 getCompanyDifferentiationConfig 完成
      // await getCompanyDifferentiationConfig({
      //   djxh: this.$store.state.zzstz.userInfo.djxh,
      //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      // });
      // // 确保状态更新完成
      // await this.$nextTick();
      // const params = { flag: true };
      // this.$refs[this.active].query(params);
      // if (this.active === 'pzmx') {
      //   this.$refs[this.active].getLrzx();
      // }
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      
      const djParam = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'dl',
        tzlx: 'ydyjtz',
        fileName: '异地预缴台账',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      // flag标志为ture时，需要重置查询，分页重置为首页
      if (flag) {
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      params = {
        ...params,
        ...this.formData,
      };
      try {
        this.queryParams = params;
        const { data } = await queryByCondition(params);
        this.pagination.total = data?.total || 0;
        this.tableData = data.pageResult.list;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        const totalDTO = data.totalDTO;
        this.footData = this.tableData.length > 0 ? [
            {
            qcye: totalDTO?.qcyeTotal,
            bqfse: totalDTO?.bqfseTotal,
            bqtje: totalDTO?.bqtjeTotal,
            bqkdje: totalDTO?.bqkdjeTotal,
            bqsjdje: totalDTO?.bqsjdjeTotal,
            qmdje: totalDTO?.qmdjeTotal,
            jzje: totalDTO?.jzjeTotal,
            },
        ] : [];
        // 查询成功后更新对应页签的上次查询记录
        const currentParams = {
          ...params,
        };
        this.lastQueryParamsTab = currentParams;
      } catch (e) {
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    // 分页处理
    pageChange({ current, pageSize }) {
      const pagination = this.pagination;
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;
      this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../styles/dialog.less';
@import '../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
