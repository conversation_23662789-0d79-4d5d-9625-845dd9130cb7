<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="mainbody" v-else>
      <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
      <div class="minbox" style="display: flex">
        <TzMenu />
        <div class="tzzt">
          <gt-collapse-menu
            class="ggMenu"
            theme="primary"
            title="台账列表"
            :list="menulist"
            :defaultExpanded="expanded"
            :default-value="defaultValue"
            :toolbar="false"
            :value="active"
            @change="collapseHandle"
          >
            <template #panel>
              <Hastab ref="tab" v-show="active === 'tab'" @openPage="openPage" />
              <Notab ref="notab" v-show="active === 'notab'" @openPage="openPage" />
              <Xxfpzz ref="xxfpzz" v-show="active === 'xxfpzz'" @openPage="openPage" />
              <Xxfpmx ref="xxfpmx" v-show="active === 'xxfpmx'" @openPage="openPage" />
              <JxfpzzQs v-if="quanshanFlag" ref="dkjxzz" v-show="active === 'dkjxzz'" @openPage="openPage" />
              <Jxfpzz v-else ref="dkjxzz" v-show="active === 'dkjxzz'" @openPage="openPage" />
              <Jxfpmx ref="jxfpmx" v-show="active === 'jxfpmx'" @openPage="openPage" />
              <Dkdjmx ref="dkdjjspzmx" v-show="active === 'dkdjjspzmx'" @openPage="openPage" />
              <Ckhwmx ref="ckhwznxzmmx" v-show="active === 'ckhwznxzmmx'" @openPage="openPage" />
              <Jmszz ref="jmszz" v-show="active === 'jmszz'" @openPage="openPage" />
              <Jmsmxz ref="jmsmxz" v-show="active === 'jmsmxz'" @openPage="openPage" />
              <Ldtstz ref="ldtstz" v-show="active === 'ldtstz'" />
              <Yjtz ref="zzsyjtz" v-show="active === 'zzsyjtz'" />
              <Jxsezczz ref="jxsezczz" v-show="active === 'jxsezczz'" @openPage="openPage" />
              <Jxsezcmx ref="jxsezcmx" v-show="active === 'jxsezcmx'" @openPage="openPage" />
              <Wkpsrmx ref="wkpsrmx" v-show="active === 'wkpsrmx'" @openPage="openPage" />
              <Jzjttz ref="jzjttz" v-show="active === 'jzjttz'" @openPage="openPage" />
              <Nsjctzmx ref="nsjctzmx" v-show="active === 'nsjctzmx'" @openPage="openPage" />
              <SrzzWlj v-if="wanglaojiFlag" ref="srzz" v-show="active === 'srzz'" @openPage="openPage" />
              <Srzz v-else ref="srzz" v-show="active === 'srzz'" @openPage="openPage" />
              <SrmxWlj v-if="wanglaojiFlag" ref="srmx" v-show="active === 'srmx'" @openPage="openPage" />
              <Srmx v-else ref="srmx" v-show="active === 'srmx'" @openPage="openPage" />
              <Xxscymx ref="xxscymx" v-show="active === 'xxscymx'" @openPage="openPage" />
              <Hgjksmx ref="hgjksmx" v-show="active === 'hgjksmx'" @openPage="openPage" />
              <Qtkspzzz ref="qtkspzzz" v-show="active === 'qtkspzzz'" @openPage="openPage" />
              <Lkysfwkspzmx ref="lkysfwkspzmx" v-show="active === 'lkysfwkspzmx'" @openPage="openPage" />
              <Jjdjtz ref="jjdjtz" v-show="active === 'jjdjtz'" />
            </template>
            <!-- <template #menu-item>menu-item</template> -->
          </gt-collapse-menu>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Xxfpzz from '@/pages/index/views/zzstz/components/xxfpzz/index.vue'; // 销项总账 xxfpzz
import Xxfpmx from '@/pages/index/views/zzstz/components/xxfpmxNew/index.vue'; // 销项明细 xxfpmx
import Jxfpmx from '@/pages/index/views/zzstz/components/jxfpmx/index.vue'; // 进项明细 jxfpmx
import Jxfpzz from '@/pages/index/views/zzstz/components/jxfpzz/index.vue'; // 进项总账 dkjxzz
import JxfpzzQs from '@/pages/index/views/zzstz/components/jxfpzz/index-quanshan.vue'; // 进项总账-泉膳分支
import Dkdjmx from '@/pages/index/views/zzstz/components/dkdjmx/index.vue'; // 待开待缴明细 dkdjjspzmx
import Ckhwmx from '@/pages/index/views/zzstz/components/ckhwznxzmmx/index.vue'; // 出口货物转内销明细 ckhwznxzmmx
import Jmszz from '@/pages/index/views/zzstz/components/jmszz'; // 减免税总账 jmszz
import Jmsmxz from '@/pages/index/views/zzstz/components/jmsmxz'; // 减免税明细 jmsmxz
import Ldtstz from '@/pages/index/views/zzstz/components/ldtstz/index.vue'; // 留抵退税台账 ldtstz
import Yjtz from '@/pages/index/views/zzstz/components/yjtz/index.vue'; // 预缴台账 zzsyjtz
import Wkpsrmx from '@/pages/index/views/zzstz/components/wkpsrmx/index.vue'; // 未开票收入明细 wkpsrmx
import Jzjttz from '@/pages/index/views/zzstz/components/jzjttz/index.vue'; // 即征即退台账 jzjttz
import Nsjctzmx from '@/pages/index/views/zzstz/components/nsjctzmx/index.vue'; // 纳税检查调整明细 nsjctzmx
import Srzz from '@/pages/index/views/zzstz/components/srzz/index.vue'; // 收入总账 srzz
import Srmx from '@/pages/index/views/zzstz/components/srmx/index.vue'; // 收入明细 srmx
import SrzzWlj from '@/pages/index/views/zzstz/components/srzz_wljBranch/index.vue'; // 收入总账王老吉分支 srzz
import SrmxWlj from '@/pages/index/views/zzstz/components/srmx_wljBranch/index.vue'; // 收入明细王老吉分支 srmx
import Hgjksmx from '@/pages/index/views/zzstz/components/hgjksmx/index.vue'; // 海关缴款书明细 hgjksmx
import Qtkspzzz from '@/pages/index/views/zzstz/components/qtkspzzz/index.vue'; // 其他扣税凭证总账 qtkspzzz
import Lkysfwkspzmx from '@/pages/index/views/zzstz/components/lkysfwkspzmx/index.vue'; // 旅客运输服务扣税凭证明细 lkysfwkspzmx
import Jjdjtz from '@/pages/index/views/zzstz/components/jjdjtz/index.vue'; // 加计抵减台账 jjdjtz
import Xxscymx from '@/pages/index/views/zzstz/components/xxscymx/index.vue'; // 销项税差异明细 xxscymx

import Jxsezczz from '@/pages/index/views/zzstz/components/jxsezc/jxsezczz.vue';
import Jxsezcmx from '@/pages/index/views/zzstz/components/jxsezc/jxsezcmx.vue';

import { CollapseMenu } from '@gt4/common-front';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
// import api from '@/pages/index/api/mhsy/mhsyRequest.js';
import dayjs from 'dayjs';
import Notab from '@/pages/index/views/zzstz/components/demo/notab.vue'; // 加计抵减台账 jjdjtz
import Hastab from '@/pages/index/views/zzstz/components/demo/hastab.vue'; // 加计抵减台账 jjdjtz
import TzMenu from '@/pages/index/components/NewTzMenu';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { initXxfpZzQuery } from '@/pages/index/api/tzzx/zzstz/xxfp.js';
import {
  isProductEnv,
  isYsEnv,
  initComputeSszq,
  stopPolling,
  getCompanyDifferentiationConfig,
} from '@/pages/index/views/util/tzzxTools.js';
import { sfysb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';

export default {
  components: {
    TzMenu,
    SkeletonFrame,
    Notab,
    Hastab,
    Mybreadcrumb,
    GtCollapseMenu: CollapseMenu,
    Xxfpzz,
    Xxfpmx,
    Jxfpzz,
    JxfpzzQs,
    Jxfpmx,
    Dkdjmx,
    Ckhwmx,
    Ldtstz,
    Jmszz,
    Jmsmxz,
    Yjtz,
    SrzzWlj,
    SrmxWlj,
    Srzz,
    Srmx,
    Wkpsrmx,
    Jzjttz,
    Nsjctzmx,
    Jxsezczz,
    Jxsezcmx,
    Hgjksmx,
    Qtkspzzz,
    Lkysfwkspzmx,
    Jjdjtz,
    Xxscymx,
  },
  data() {
    return {
      loading: true,
      jgbgbz: false,
      expanded: ['1'], // 默认展开
      defaultValue: 'xxfpzz',
      zzList: [],
      lsgltzbz: this.$route.query.lsgltzbz || false,
      sbrwmxbz: this.$route.query.sbrwmxBz || false,
      sbrwmxAbbbz: this.$route.query.sbrwmxAbbBz || false,
      gzsbbz: this.$route.query.gzsbbz || false,
      active: 'xxfpzz',
      list: [
        // {
        //   id: '0',
        //   title: '样例',
        //   children: [
        //     {
        //       id: 'tab',
        //       title: '带选项卡的页面',
        //       required: false,
        //     },
        //     {
        //       id: 'notab',
        //       title: '不带选项卡的页面',
        //       required: false,
        //     },
        //   ],
        // },
        {
          id: '1',
          title: '销项台账',
          children: [
            {
              id: 'xxfpzz',
              title: '销项台账',
              required: false,
            },
            {
              id: 'xxfpmx',
              title: '销项发票明细',
              required: false,
            },
            {
              id: 'wkpsrmx',
              title: '未开票收入明细',
              required: false,
            },
            {
              id: 'jzjttz',
              title: '即征即退台账',
              required: false,
            },
            {
              id: 'nsjctzmx',
              title: '纳税检查调整明细',
              required: false,
            },
            {
              id: 'srzz',
              title: '收入台账',
              required: false,
            },
            {
              id: 'srmx',
              title: '收入明细',
              required: false,
            },
          ],
        },
        {
          id: '2',
          title: '进项台账',
          children: [
            {
              id: 'dkjxzz',
              title: '进项台账',
              required: false,
            },
            {
              id: 'jxfpmx',
              title: '进项发票明细',
              required: false,
            },
            {
              id: 'dkdjjspzmx',
              title: '代扣代缴凭证明细',
              required: false,
            },
            {
              id: 'hgjksmx',
              title: '海关缴款书明细',
              required: false,
            },
            {
              id: 'ckhwznxzmmx',
              title: '出口货物转内销证明明细',
              required: false,
            },
            {
              id: 'qtkspzzz',
              title: '其他扣税凭证台账',
              required: false,
            },
            {
              id: 'lkysfwkspzmx',
              title: '旅客运输服务扣税凭证明细',
              required: false,
            },
            // {
            //   id: 'jxsefqdkmx',
            //   title: '进项税额分期抵扣明细',
            //   required: false,
            // },
            // {
            //   id: 'ncpfpmx',
            //   title: '农产品发票明细',
            //   required: false,
            // },
            {
              id: 'jxsezczz',
              title: '进项税额转出台账',
              required: false,
            },
            {
              id: 'jxsezcmx',
              title: '进项税额转出明细',
              required: false,
            },
          ],
        },
        {
          id: '3',
          title: '其他台账',
          children: [
            // {
            //   id: 'kcxmxxfptz',
            //   title: '扣除项目销项发票台账',
            //   required: false,
            // },
            // {
            //   id: 'cjrhtz',
            //   title: '产教融合台账',
            //   required: false,
            // },
            // {
            //   id: 'cjrhtzmx',
            //   title: '产教融合投资明细',
            //   required: false,
            // },
            // {
            //   id: 'zdrqjtysbtz',
            //   title: '重点人群及退役士兵台账',
            //   required: false,
            // },
            // {
            //   id: 'zdrqjtysbjmmx',
            //   title: '重点人群及退役士兵减免明细',
            //   required: false,
            // },
            // {
            //   id: 'zdrqjtysbrymx',
            //   title: '重点人群及退役士兵人员明细',
            //   required: false,
            // },
            {
              id: 'jmszz',
              title: '增值税减免税台账',
              required: false,
            },
            {
              id: 'jmsmxz',
              title: '增值税减免税明细',
              required: false,
            } /* ,
            {
              id: 'ldtstz',
              title: '增值税留抵退税台账',
              required: false,
            },
            {
              id: 'zzsyjtz',
              title: '增值税预缴台账',
              required: false,
            },
            {
              id: 'jjdjtz',
              title: '增值税加计抵减台账',
              required: false,
            }, */,
          ],
        },
      ],
    };
  },
  created() {
    initComputeSszq();
    // 消息跳转增值税台账时的暂时处理
    if (Object.keys(this.$route.query).length === 1 && 'jguuid' in this.$route.query) {
      // 有且仅有一个jguuid参数时的处理逻辑
      this.sbrwmxbz = true; // 默认为false
    }
    // 用于记录页面是否通过申报更正功能跳转进入
    if (this.gzsbbz) {
      this.$store.commit('sbzt/setSbgzEnterFlag', true);
      const sbgzSsyfInfo = {
        ssyfq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
        ssyfz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
      };
      this.$store.commit('sbzt/setSbgzSsyfInfo', sbgzSsyfInfo);
    } else {
      this.$store.commit('sbzt/setSbgzEnterFlag', false);
      this.$store.commit('sbzt/setSbgzSsyfInfo', {});
    }
    if (this.$route.query.jguuid) {
      const companyList = JSON.parse(window.sessionStorage.getItem('companyList'));
      const userInfo = companyList.find((item) => item.jguuid === this.$route.query.jguuid);
      if (userInfo) {
        this.$store.commit('zzstz/setUserInfoData', userInfo);
      } else {
        this.$store.commit('zzstz/setUserInfoData', {
          jguuid: this.$route.query.jguuid,
          jgmc: this.$route.query.jgmc,
          xzqhmc: this.$route.query.xzqhmc,
          nsrsbh: this.$route.query.nsrsbh,
          djxh: this.$route.query.djxh,
          xzqhszDm: this.$route.query.xzqhszDm,
          qydmz: this.$route.query.qydmz,
          nsrlx: this.$route.query.nsrlx,
        });
      }
    } else {
      const userInfo = window.sessionStorage.getItem('jgxxList');
      this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    }
  },
  mounted() {
    isProductEnv();
    isYsEnv();
    getCompanyDifferentiationConfig({
      djxh: this.$store.state.zzstz.userInfo.djxh,
      nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
    });
    this.xxfpZzQuery();
  },
  computed: {
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    dalianzhonggongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000002';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    wanglaojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000006';
    },
    menulist() {
      // 若菜单有特殊配置则直接取特殊配置
      if (this.$store.state.zdmczh.companyDifferentiationConfig.menulistConfig) {
        return this.$store.state.zdmczh.companyDifferentiationConfig.menulistConfig;
      }
      // 根据企业过滤销项台账下的菜单项
      const filteredList = JSON.parse(JSON.stringify(this.list));
      // 重新设置srzz菜单项的动态标题
      const srzzItem = filteredList.find((item) => item.id === '1')?.children.find((child) => child.id === 'srzz');
      if (srzzItem) {
        srzzItem.title = this.wanglaojiFlag ? '未开票收入台账' : '收入台账';
      }
      if (this.dalianzhonggongFlag) {
        // 去掉收入台账和收入明细
        const xstzItem = filteredList.find((item) => item.id === '1');
        if (xstzItem) {
          xstzItem.children = xstzItem.children.filter((child) => child.id !== 'srzz' && child.id !== 'srmx');
        }
      } else {
        // 去掉未开票收入明细、即征即退台账和纳税检查调整明细
        const xstzItem = filteredList.find((item) => item.id === '1');
        if (xstzItem) {
          xstzItem.children = xstzItem.children.filter(
            (child) => child.id !== 'wkpsrmx' && child.id !== 'jzjttz' && child.id !== 'nsjctzmx',
          );
        }
      }
      if (this.xgmnsrFlag) {
        return filteredList.filter((item) => item.id === '1');
      }
      return filteredList;
    },
  },
  watch: {
    active: {
      handler(newVal) {
        console.log('侦听到active变更，对应newVal为', newVal);
        if (newVal === 'xxscymx') {
          this.$refs.myBre.setXlxShow(false);
        } else {
          this.$refs.myBre.setXlxShow(true);
        }
      },
      immediate: true,
    },
  },
  methods: {
    async mountedMethod() {
      console.log('this.loading ---', this.loading);
      console.log('this.$route.query', this.$route.query);
      if (this.$route.query.active === 'srzz') {
        this.active = this.$route.query.active;
        console.log('当前页面：', this.active);
        const params = { flag: true, p: { tabValue: this.$route.query.tabValue } };
        console.log('zzstzparams', params);
        this.$refs[this.active].query(params);
      } else if (this.$route.query.active) {
        this.active = this.$route.query.active;
        console.log('当前页面：', this.active);
        this.$refs[this.active].query({ flag: true });
        // 默认展开
        this.list.forEach((i1) => {
          i1.children.forEach((i2) => {
            if (this.active === i2.id) {
              this.expanded = [i1.id];
            }
          });
        });
      }
      // 在组件加载完成后，调用子组件方法
      // 通知面包屑组件
      // 从导航栏菜单
      const parmObjDhcd = {
        mybreadList: ['首页', '增值税台账'], // 【必填】面包屑层级
        iframeGoBack: true, // 是否可以返回首页
        isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
        // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
        selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
        xlxShow: true,
      };
      // 从理税概览进
      const parmObjLsgl = {
        mybreadList: ['首页', '理税概览', '增值税台账'], // 【必填】面包屑层级
        iframeGoBack: false, // 是否可以返回首页
        isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
        mbxUrlList: ['/znsb/view/tzzx/lsgl'],
        goBackPath: '/znsb/view/tzzx/lsgl', // 【选填】点击回退时，跳转的路径。一般是项目内，
        // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
        selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
        xlxShow: true,
      };
      // 从申报任务进
      const parmObjSbrw = {
        mybreadList: ['首页', '申报概览', '税费申报', '增值税台账'], // 【必填】面包屑层级
        iframeGoBack: false, // 是否可以返回首页
        isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
        mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmx'],
        goBackPath: '/znsb/view/nssb/sbrwmx', // 【选填】点击回退时，跳转的路径。一般是项目内，
        // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
        selectedCompany: this.$route.query.jguuid || '',
        xlxShow: true,
      };
      // 从申报任务(报表)进
      const parmObjSbrwAbb = {
        mybreadList: ['首页', '申报概览', '税费申报', '增值税台账'], // 【必填】面包屑层级
        iframeGoBack: false, // 是否可以返回首页
        isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
        mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmxAbb'],
        goBackPath: '/znsb/view/nssb/sbrwmxAbb', // 【选填】点击回退时，跳转的路径。一般是项目内，
        // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
        selectedCompany: this.$route.query.jguuid || '',
        xlxShow: true,
      };
      // 从申报更正进
      const parmObjSbgz = {
        mybreadList: ['首页', '申报更正与作废', '增值税台账'], // 【必填】面包屑层级
        iframeGoBack: false, // 是否可以返回首页
        isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
        mbxUrlList: ['/znsb/view/nssb/sbgzyzf'],
        goBackPath: '/znsb/view/nssb/sbgzyzf', // 【选填】点击回退时，跳转的路径。一般是项目内，
        // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
        selectedCompany: this.$route.query.jguuid || '',
        xlxShow: true,
      };
      if (this.lsgltzbz) {
        this.$refs.myBre.initMyBre(parmObjLsgl);
      } else if (this.sbrwmxbz) {
        this.$refs.myBre.initMyBre(parmObjSbrw);
      } else if (this.sbrwmxAbbbz) {
        this.$refs.myBre.initMyBre(parmObjSbrwAbb);
      } else if (this.gzsbbz) {
        this.$refs.myBre.initMyBre(parmObjSbgz);
      } else {
        this.$refs.myBre.initMyBre(parmObjDhcd);
      }
    },
    async xxfpZzQuery() {
      const params = {
        ssyfq: dayjs().subtract(1, 'month').format('YYYYMM'),
        ssyfz: dayjs().subtract(1, 'month').format('YYYYMM'),
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
      };
      try {
        const sfysbParams = {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          yzpzzlDm: 'BDA0610606',
          sszq: dayjs().subtract(1, 'month').format('YYYYMM'),
        };
        sfysb(sfysbParams).then((value) => {
          const sfysb = value.data;
          if (sfysb === 'YSB') {
            this.$message.warning('本申报期已完成申报，调整台账数据后，请尽快完成更正申报。');
          }
        });

        await initXxfpZzQuery(params);
        this.loading = false;
        this.$nextTick(() => {
          this.mountedMethod();
        });
      } catch (e) {
        console.log('e', e);
      }
    },
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true, from: false, initQuery: true };
      console.log('zzstzparams', params);
      this.$refs[val].query(params);
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true, from: false, initQuery: true };
      stopPolling();
      // 使用 await 等待 getCompanyDifferentiationConfig 完成
      await getCompanyDifferentiationConfig({
        djxh: this.$store.state.zzstz.userInfo.djxh,
        nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      });
      // 确保状态更新完成
      await this.$nextTick();
      // 切换企业后更换对应表头配置
      ['srmx'].forEach((item) => {
        this.$refs[item].fetchTableColumns();
      });
      // 当切换企业时，需要重置对应功能属期查询条件
      ['xxfpzz', 'xxfpmx', 'srzz', 'srmx', 'dkjxzz', 'jxsezczz', 'jmszz'].forEach((item) => {
        this.$refs[item].initQueryConditions();
        this.$refs[item].getQueryParamsList();
      });
      this.$refs.srzz.sfzjg();
      // 切换企业时需要同步更新查询条件中的利润中心
      ['srzz', 'srmx', 'xxscymx'].forEach((item) => {
        this.$refs[item].getLrzx();
      });
      // 对当前打开的页面执行初始化查询（其他页面在切换台账时执行查询）
      ['xxfpmx', 'srzz', 'srmx', 'jxfpmx', 'lkysfwkspzmx'].forEach((item) => {
        if (this.active === item) {
          this.$refs[this.active].initTabValue();
        }
      });
      // 对当前打开的页面执行初始化查询（其他页面在切换台账时执行查询）
      this.$refs[this.active].query(params);
      this.jgbgbz = true;
    },
    // data不传的话默认用子页面的查询参数。notQuery传true时子页面不触发查询。from为父页面activ，用于子页面返回对应父页面，按需将from储存在子页面。
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) {
        this.$refs[type].query({ flag, p: data, from });
      } else if (this.jgbgbz) {
        this.$refs[type].query({ flag, p: data, from });
        this.jgbgbz = false;
      }
      this.active = type;
    },
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
  /deep/.gt-collapse-menu-content-unfold {
    margin-left: 0 !important;
  }
  /deep/.gt-collapse-menu-sidebar-unfold {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content .t-default-menu {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar {
    display: block;
  }
}
.tzzt {
  width: calc(100% - 50px);
}
.ggMenu {
  border-left: 1px solid #eee;
  /deep/.gt-collapse-menu-sidebar-header__title {
    font-size: 14px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header {
    line-height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header-unfold {
    height: 55px !important;
  }
  /deep/.t-menu__item {
    height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content {
    height: calc(100% - 55px) !important;
  }
}
</style>
