const slList = [
  { code: 0.13, caption: '13%' },
  { code: 0.09, caption: '9%' },
  { code: 0.06, caption: '6%' },
  { code: 0.05, caption: '5%' },
  { code: 0.04, caption: '4%' },
  { code: 0.03, caption: '3%' },
];

const zsfsList = [
  { code: '01', caption: '一般计税方法计税' },
  { code: '02', caption: '简易计税方法计税' },
  { code: '03', caption: '免抵退税' },
  { code: '04', caption: '免税' },
];

const zsxmList = [
  { value: '01', label: '货物' },
  { value: '02', label: '劳务' },
  { value: '03', label: '服务' },
  { value: '04', label: '无形资产' },
  { value: '05', label: '不动产' },
];

const fplxList = [
  { value: '01', label: '增值税专用发票' },
  { value: '02', label: '货物运输业增值税专用发票' },
  { value: '03', label: '机动车销售统一发票' },
  { value: '04', label: '增值税普通发票' },
  { value: '08', label: '增值税电子专用发票' },
  { value: '10', label: '增值税电子普通发票' },
  { value: '11', label: '卷式发票' },
  { value: '14', label: '通行费发票' },
  { value: '15', label: '二手车销售统一发票' },
  { value: '51', label: '电子发票(铁路电子客票)' },
  { value: '61', label: '电子发票(航空运输电子客票行程单)' },
  { value: '81', label: '电子发票(增值税专用发票)' },
  { value: '82', label: '电子发票(普通发票)' },
  { value: '83', label: '机动车销售电子统一发票' },
  { value: '84', label: '二手车销售电子统一发票' },
  { value: '85', label: '纸质发票(增值税专用发票)' },
  { value: '86', label: '纸质发票(普通发票)' },
  { value: '87', label: '纸质发票(机动车销售统一发票)' },
  { value: '88', label: '纸质发票(二手车销售统一发票)' },
];

const sfcezsfpList = [
  { code: 'Y', caption: '是' },
  { code: 'N', caption: '否' },
];

const sfjzjtList = [
  { value: 'Y', label: '是' },
  { value: 'N', label: '否' },
];

const dkztList = [
  { label: '待抵扣', value: '0' },
  { label: '抵扣', value: '1' },
  { label: '不抵扣', value: '6' },
];

const bdkyyList = [
  { label: '用于非应税项目', value: '1' },
  { label: '用于免税项目', value: '2' },
  { label: '用于集体福利或者个人消费', value: '3' },
  { label: '遭受非正常损失', value: '4' },
  { label: '其他', value: '5' },
];

export const xmdlList = [
  { value: '01', label: '申报抵扣的进项税额' },
  { value: '02', label: '待抵扣进项税额' },
];
export const hxytList = [
  { value: '1', label: '已申请抵扣' },
  { value: '2', label: '不抵扣' },
  { value: '3', label: '出口退税' },
  { value: '4', label: '代办退税' },
];
export const xmxlList1 = [
  { value: '0101', label: '本期认证相符且本期申报抵扣' },
  { value: '0102', label: '前期认证相符且本期申报抵扣' },
  { value: '0103', label: '海关进口增值税专用缴款书' },
  { value: '0104', label: '农产品收购发票或者销售发票' },
  { value: '0105', label: '代扣代缴税收缴款凭证' },
  { value: '0106', label: '加计扣除农产品进项税额' },
  { value: '0107', label: '本期用于购建不动产的扣税凭证' },
  { value: '0108', label: '本期用于抵扣的旅客运输服务扣税凭证' },
  { value: '0109', label: '外贸企业进项税额抵扣证明' },
  { value: '0110', label: '其他扣税凭证' },
];
export const xmxlList2 = [
  { value: '0201', label: '本期认证相符且本期未申报抵扣' },
  { value: '0202', label: '按照税法规定不允许抵扣' },
  { value: '0203', label: '海关进口增值税专用缴款书' },
  { value: '0204', label: '农产品收购发票或者销售发票' },
  { value: '0205', label: '代扣代缴税收通用缴款书' },
  { value: '0206', label: '其他扣税凭证' },
];
// 利润中心
export const lrzxList = [
  {
    label: '演示公司测试用利润中心001',
    value: 'P000000001',
  },
  {
    label: '演示公司测试用利润中心002',
    value: 'P000000002',
  },
  {
    label: '演示公司测试用利润中心003',
    value: 'P000000003',
  },
];

// 代扣代缴项目
export const dkdjxmList = [
  {
    label: '不动产租金',
    value: '不动产租金',
  },
  {
    label: '股息红利所得-1',
    value: '股息红利所得-1',
  },
  {
    label: '利息所得-2',
    value: '利息所得-2',
  },
  {
    label: '特许权使用费所得-3',
    value: '特许权使用费所得-3',
  },
  {
    label: '转让财产所得-4',
    value: '转让财产所得-4',
  },
  {
    label: '租金—5',
    value: '租金—5',
  },
  {
    label: '承包工程、提供劳务所得-6',
    value: '承包工程、提供劳务所得-6',
  },
  {
    label: '国际运输-7',
    value: '国际运输-7',
  },
  {
    label: '担保费所得—8',
    value: '担保费所得—8',
  },
  {
    label: '其他所得-9',
    value: '其他所得-9',
  },
];
export { slList, zsxmList, zsfsList, fplxList, sfcezsfpList, sfjzjtList, dkztList, bdkyyList };
