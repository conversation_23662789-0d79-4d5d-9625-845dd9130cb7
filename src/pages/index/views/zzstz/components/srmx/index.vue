<!--
 * @Descripttion: 台账-增值税一般纳税人收入明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-06 15:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue">
      <search-control-panel
        ref="queryControl1"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        style="margin-top: 16px"
        :config="querySearchConfigSrmx"
        :formRules="querySearchConfigOneRules1"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <search-control-panel
        ref="queryControl2"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        :config="querySearchConfigXxspztz"
        :formRules="querySearchConfigOneRules2"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
            ><AddIcon slot="icon" />新增</t-button
          >
          <t-button theme="primary" @click="delRow" :disabled="!allowEdit"><DeleteIcon slot="icon" />删除</t-button>
          <ExtractDataButton
            v-if="tabValue === '0'"
            variant="outline"
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            @query="query"
          />
          <t-button
            variant="outline"
            theme="primary"
            @click="check"
            v-if="tabValue === '0' && this.$store.state.isProduct.envValue && !this.xgmnsrFlag"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <t-button v-if="quanshanFlag" variant="outline" theme="primary" @click="downloadTemplate"
            ><FileIcon slot="icon" />下载模版</t-button
          >
          <t-upload
            v-if="quanshanFlag"
            action="/nssb/srzz/v1/uploadExcel"
            :tips="tips"
            v-model="files"
            theme="custom"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            @fail="handleFail"
            @success="handleSuccess"
          >
            <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入数据</t-button>
          </t-upload>
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <t-button
            v-if="tabValue === '0' && this.shanxiyidongButtonShowFlag"
            variant="outline"
            theme="primary"
            @click="onScfxpz"
            :loading="scfxpzLoading"
            :disabled="!allowEdit"
            ><FilterIcon slot="icon" />生成反向凭证</t-button
          >
          <QsbButton />
          <a ref="downloadTemplate" style="display: none" href="./增值税收入明细导入模板.xlsx"></a>
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="tabValue === '0' && fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="0" label="收入明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef1"
            row-key="uuid"
            hover
            :data="tableData1"
            :columns="tableColumnsSrmx"
            height="100%"
            :loading="tableLoading"
            :pagination="pagination1"
            @page-change="(e) => pageChange(e, 0)"
            :scroll="{ type: 'virtual', rowHeight: 56 }"
            lazyLoad
            :selected-row-keys="selectedRowKeys1"
            @select-change="rehandleSelectChange"
            :foot-data="footData1"
          >
            <template #xh="{ rowIndex }">{{
              (pagination1.current - 1) * pagination1.pageSize + rowIndex + 1
            }}</template>
            <template #sl1="{ row }">
              {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
            </template>
            <template #xse="{ row }">
              {{ numberToPrice(row.xse) }}
            </template>
            <template #se="{ row }">
              {{ numberToPrice(row.se) }}
            </template>
            <template #bbje="{ row }">
              {{ numberToPrice(row.bbje) }}
            </template>
            <template #ly="{ row }">
              <span>{{
                (pzzlList.find((d) => d.value === row.ly) && pzzlList.find((d) => d.value === row.ly).label) || ''
              }}</span>
            </template>
            <template #operation="{ row }">
              <t-link
                theme="primary"
                hover="color"
                @click="newOrEditRow(row)"
                v-show="row.ly !== '0'"
                :disabled="!allowEdit"
              >
                编辑
              </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="销项税凭证调整" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef2"
            row-key="uuid"
            hover
            :data="tableData2"
            :columns="tableColumnsXxspztz"
            height="100%"
            lazyLoad
            :selected-row-keys="selectedRowKeys2"
            @select-change="rehandleSelectChange"
            :pagination="pagination2"
            @page-change="(e) => pageChange(e, 1)"
            :loading="tableLoading"
            :foot-data="footData2"
          >
            <template #xh="{ rowIndex }">{{
              (pagination2.current - 1) * pagination2.pageSize + rowIndex + 1
            }}</template>
            <template #sl1="{ row }">
              {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
            </template>
            <template #se="{ row }">
              {{ numberToPrice(row.se) }}
            </template>
            <template #operation="{ row }">
              <t-link
                theme="primary"
                hover="color"
                @click="newOrEditRow(row)"
                v-show="row.ly === '1'"
                :disabled="!allowEdit"
              >
                编辑
              </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <EditDialogQS
        v-if="editDialogVisible1 && quanshanFlag"
        :visible.sync="editDialogVisible1"
        @updateSrmx="(item) => query(item)"
      />
      <EditDialogSXYD
        v-if="editDialogVisible1 && shanxiyidongFlag"
        :visible.sync="editDialogVisible1"
        @updateSrmx="(item) => query(item)"
      />
      <EditDialog
        v-if="editDialogVisible1 && !shanxiyidongFlag && !quanshanFlag"
        :visible.sync="editDialogVisible1"
        @updateSrmx="(item) => query(item)"
      />
      <EditDialogXxspzQS
        :visible.sync="editDialogVisible2"
        v-if="editDialogVisible2 && quanshanFlag"
        @updateSrmx="(item) => query(item)"
      />
      <EditDialogXxspzSXYD
        :visible.sync="editDialogVisible2"
        v-if="editDialogVisible2 && shanxiyidongFlag"
        @updateSrmx="(item) => query(item)"
      />
      <EditDialogXxspz
        :visible.sync="editDialogVisible2"
        v-if="editDialogVisible2 && !shanxiyidongFlag && !quanshanFlag"
        @updateSrmx="(item) => query(item)"
      />
      <div v-show="boxvisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="请确认是否删除所选明细"
          :onConfirm="confirmDelRow"
          :onClose="closeBox"
        >
          <!-- class="confirmDialogCss"
        <div style="text-align: center">
          <div><img src="../../../../assets/error.png" alt="" /></div>
          <div class="tsnr">请确认是否删除所选明细</div>
          <div class="tsnrf">
            <div>xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
          </div>
        </div> -->
        </t-dialog>
      </div>
    </t-tabs>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getAll, getLrzx, getKmbmByywbm, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { sfzjg } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { getInit, srzzmxQuery, srmxDelete, scfxpz, srzzmxQueryHj } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import { deleteXxspz, queryXxspz, queryXxspzHj } from '@/pages/index/api/tzzx/zzstz/xxspz.js';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, FileIcon, UploadIcon, ChartIcon, FilterIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import {
  computeSszq,
  multiSelectHandle,
  jyssReadyStatusFetch,
  getSbztBySsq,
  getSbztBySsqqz,
} from '@/pages/index/views/util/tzzxTools.js';
import EditDialog from './components/edit-dialog.vue';
import EditDialogSXYD from './components/edit-dialog-shanxiyidong.vue';
import EditDialogQS from './components/edit-dialog-quanshan.vue';
import EditDialogXxspz from './components/edit-dialog-xxspz.vue';
import EditDialogXxspzSXYD from './components/edit-dialog-xxspz-shanxiyidong.vue';
import EditDialogXxspzQS from './components/edit-dialog-xxspz-quanshan.vue';
import {
  querySearchConfig1,
  querySearchConfig2,
  querySearchConfig3,
  querySearchConfig4,
  dataColumns1,
  dataColumns2,
  pzzlList,
  kmDmListFzjg,
  kmDmListZjg,
  kmDmListQuanShan,
} from './config.js';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    EditDialog,
    EditDialogSXYD,
    EditDialogQS,
    EditDialogXxspz,
    EditDialogXxspzSXYD,
    EditDialogXxspzQS,
    AddIcon,
    FileIcon,
    UploadIcon,
    DeleteIcon,
    ChartIcon,
    FilterIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      tabValue: '0',
      isProduct: this.$store.state.isProduct.envValue,
      fristQuery: true,
      editDialogVisible1: false,
      editDialogVisible2: false,
      sfzjgFlag: false,
      scfxpzLoading: false,
      boxvisible: false,
      querySearchConfig1,
      querySearchConfig2,
      querySearchConfig3,
      querySearchConfig4,
      tableLoading: false,
      fromName: false,
      formData1: {},
      formData2: {},
      checkBox1: [],
      checkBox2: [],
      selectedRowKeys1: [],
      selectedRowKeys2: [],
      lrzxList: [], // 利润中心
      kjfpList: [], // 分配
      srlxList: [],
      jsfsList: [],
      zsxm1List: [],
      slList: [],
      kmdmList: [],
      tableData1: [],
      tableData2: [],
      delformData: [],
      pzzlList,
      dataColumns1,
      dataColumns2,
      delUnallowRows: [],
      footData1: [],
      footData2: [],
      pagination1: { current: 1, pageSize: 10, total: 0 },
      pagination2: { current: 1, pageSize: 10, total: 0 },
      loadedTabs: { 0: false, 1: false }, // 新增tab加载状态
      remoteColumnsSrmx: [], // 动态列配置-收入明细
      remoteColumnsXxspztz: [], // 动态列配置-销项税凭证调整
      remotequerySearchConfigSrmx: [], // 动态查询条件配置-收入明细
      remotequerySearchConfigXxspztz: [], // 动态查询条件配置-销项税凭证调整
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
      tips: '',
      files: [],
    };
  },
  created() {},
  mounted() {
    this.fetchTableColumns(); // 初始化表头配置
    this.getLrzx();
    this.getQueryParamsList();
    this.initQueryConditions();
    this.checkAllowEdit();
  },
  computed: {
    getQueryParamsKmbmList() {
      // 泉膳分支
      if (this.quanshanFlag) {
        return kmDmListQuanShan;
      }
      // 山西移动分支
      if (this.shanxiyidongFlag) {
        // 校验企业类型（总机构/分支机构）
        return this.shanxiyidongZfjgFlag ? kmDmListZjg : kmDmListFzjg;
      }
      // 通用国际分支
      if (this.tongyongguojiFlag) {
        return [{ value: '22210102', label: '应交税费-应交增值税-销项税额' }];
      }
      // 其他企业，返回默认的科目编码列表
      return this.xgmnsrFlag
        ? [
            { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
            { value: '2221080000', label: '应交税费-增值税简易计税' },
            { value: '2221010000', label: '应交税费-应交增值税' },
          ]
        : [
            { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
            { value: '2221080000', label: '应交税费-增值税简易计税' },
          ];
    },
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    querySearchConfigSrmx() {
      let config = [];
      if (!this.remotequerySearchConfigSrmx.length) {
        // 兼容初始化状态
        if (this.xgmnsrFlag) {
          config = this.querySearchConfig3;
        }
        config = this.querySearchConfig1;
      }
      // 使用动态配置的列
      config = this.remotequerySearchConfigSrmx;
      if (this.senmaQybz) {
        config = [
          ...config,
          {
            label: '分支机构纳税人识别号',
            key: 'fzjgnsrsbh',
            type: 'input',
            value: '',
            placeholder: '请输入',
            clearable: true,
          },
          {
            label: '汇总分配分支机构识别号',
            key: 'bhznsqynsrsbh',
            type: 'input',
            value: '',
            placeholder: '请输入',
            clearable: true,
          },
        ];
      }
      if (this.quanshanFlag) {
        config = [
          ...config,
          {
            label: '文本',
            key: 'wbzlmc',
            type: 'input',
            value: '',
            placeholder: '请输入',
            clearable: true,
          },
        ];
      }
      return config;
    },
    querySearchConfigXxspztz() {
      if (!this.remotequerySearchConfigXxspztz.length) {
        // 兼容初始化状态
        if (this.xgmnsrFlag) {
          return this.querySearchConfig4;
        }
        return this.querySearchConfig2;
      }

      // 使用动态配置的列
      return this.remotequerySearchConfigXxspztz;
    },
    querySearchConfigOneRules1() {
      return this.xgmnsrFlag
        ? {
            sszqq: [{ required: true, message: '必填项', type: 'error' }],
            sszqz: [{ required: true, message: '必填项', type: 'error' }],
          }
        : {
            sszq: [{ required: true, message: '必填项', type: 'error' }],
          };
    },
    querySearchConfigOneRules2() {
      return this.xgmnsrFlag
        ? {
            sszqq: [{ required: true, message: '必填项', type: 'error' }],
            sszqz: [{ required: true, message: '必填项', type: 'error' }],
          }
        : {
            sszq: [{ required: true, message: '必填项', type: 'error' }],
          };
    },
    tableColumnsSrmx() {
      let config = this.xgmnsrFlag
        ? this.dataColumns1.filter(
            (item) =>
              item.colKey !== 'jsfsmc' && item.colKey !== 'kjfpmc' && item.colKey !== 'pztt' && item.colKey !== 'khbh',
          )
        : this.dataColumns1.filter((item) => item.colKey !== 'sszq');
      if (this.remoteColumnsSrmx.length) {
        // 兼容初始化状态
        config = this.remoteColumnsSrmx;
      }
      if (this.shanxiyidongFlag) {
        config = config.filter((item) => item.colKey !== 'pztt');
      }
      if (!this.quanshanFlag) {
        config = config.filter((item) => item.colKey !== 'wbzlmc');
      }
      console.log(this.senmaQybz, 'this.senmaQybz');
      if (this.senmaFlag && !this.senmaQybz) {
        config = config.filter((item) => item.colKey !== 'fzjgnsrsbh' && item.colKey !== 'bhznsqynsrsbh');
      } else if (this.quanshanFlag && this.quanshanQybz !== 'zjgqy') {
        config = config.filter((item) => item.colKey !== 'fzjgnsrsbh' && item.colKey !== 'bhznsqynsrsbh');
      }
      return config;
    },
    tableColumnsXxspztz() {
      if (!this.remoteColumnsXxspztz.length) {
        // 兼容初始化状态
        if (this.xgmnsrFlag) {
          return this.dataColumns2.filter((item) => item.colKey !== 'jsfsmc');
        }
        return this.dataColumns2;
      }

      // 使用动态配置的列
      return this.remoteColumnsXxspztz;
    },
    sszqToExtract() {
      return dayjs(this.xgmnsrFlag ? this.formData1.sszqq : this.formData1.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    srmxTzlx() {
      return this.xgmnsrFlag ? 'xgmnsrSrMxbTz' : 'srMxbTz';
    },
    tzlx() {
      return this.tabValue * 1 ? 'xxspzTz' : this.srmxTzlx;
    },
    exportFileName() {
      return this.tabValue * 1 ? '销项税凭证导出' : '收入明细表导出';
    },
    tzQueryParams() {
      return this.tabValue === '1' ? this.handleXxspzQueryParams : this.handleSrmxQueryParams;
    },
    getBaseQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: 0,
        pageSize: 0,
      };
    },
    handleXxspzQueryParams() {
      const baseParams = this.getBaseQueryParams; // 创建副本，避免直接修改 baseParams
      baseParams.pageNum = this.pagination2.current;
      baseParams.pageSize = this.pagination2.pageSize;

      const cxParam = this.xgmnsrFlag
        ? {
            ...this.formData2,
            sszqq: dayjs(this.formData2.sszqq).format('YYYYMM'),
            sszqz: dayjs(this.formData2.sszqz).format('YYYYMM'),
            zsxmDm: this.formData2.zsxmDm1,
            ...baseParams,
          }
        : {
            ...this.formData2,
            sszq: dayjs(this.formData2.sszq).format('YYYYMM'),
            zsxmDm: this.formData2.zsxmDm1,
            ...baseParams,
          };
      if (this.xgmnsrFlag) {
        this.$delete(cxParam, 'sszq');
      } else {
        this.$delete(cxParam, 'sszqq');
        this.$delete(cxParam, 'sszqz');
      }
      return cxParam;
    },
    handleSrmxQueryParams() {
      const baseParams = this.getBaseQueryParams; // 创建副本，避免直接修改 baseParams
      baseParams.pageNum = this.pagination1.current;
      baseParams.pageSize = this.pagination1.pageSize;

      const cxParam = this.xgmnsrFlag
        ? {
            ...this.formData1,
            sszqq: dayjs(this.formData1.sszqq).format('YYYYMM'),
            sszqz: dayjs(this.formData1.sszqz).format('YYYYMM'),
            srlxDm: multiSelectHandle(this.formData1.srlxDm),
            jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
            zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
            sl1: multiSelectHandle(this.formData1.sl1),
            kmdm: multiSelectHandle(this.formData1.kmdm),
            lrzx: multiSelectHandle(this.formData1.lrzx),
            ...baseParams,
          }
        : {
            ...this.formData1,
            sszq: dayjs(this.formData1.sszq).format('YYYYMM'),
            srlxDm: multiSelectHandle(this.formData1.srlxDm),
            jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
            zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
            sl1: multiSelectHandle(this.formData1.sl1),
            kmdm: multiSelectHandle(this.formData1.kmdm),
            lrzx: multiSelectHandle(this.formData1.lrzx),
            ...baseParams,
          };
      if (this.xgmnsrFlag) {
        this.$delete(cxParam, 'sszq');
      } else {
        this.$delete(cxParam, 'sszqq');
        this.$delete(cxParam, 'sszqz');
      }
      return cxParam;
    },
    exportButtonFlag() {
      return this.tabValue * 1 ? !this.tableData2.length : !this.tableData1.length;
    },
    senmaFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000001';
    },
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    shanxiyidongZfjgFlag() {
      // true 为总机构， false 为分机构
      return this.shanxiyidongFlag && this.$store.state.zdmczh.companyDifferentiationConfig?.fzjgbz === 'zjg';
    },
    shanxiyidongButtonShowFlag() {
      if (!this.shanxiyidongFlag) {
        return true;
      }
      return this.shanxiyidongZfjgFlag;
    },
    quanshanQybz() {
      if (this.quanshanFlag) {
        return this.$store.state.zdmczh.companyDifferentiationConfig?.qybz;
      }
      return '';
    },
    senmaQybz() {
      if (this.senmaFlag) {
        return this.sfzjgFlag;
      }
      return false;
    },
  },
  watch: {
    'formData1.srlxDm': async function () {
      console.log('监测到srlxDm变化');
      if (this.formData1.srlxDm.length > 0) {
        const res = await getKmbmByywbm({ ywlxDm: this.formData1.srlxDm.join(',') });
        this.kmdmList = res.data;
        (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList =
          this.xgmnsrFlag && this.formData1.srlxDm.includes('130')
            ? [...this.kmdmList, { value: '2221010000', label: '应交税费-应交增值税' }].map((d) => ({
                label: `${d.value} | ${d.label}`,
                value: d.value,
              }))
            : this.kmdmList.map((d) => ({
                label: `${d.value} | ${d.label}`,
                value: d.value,
              }));
      } else {
        const res = await getKmbmByywbm({ ywlxDm: '110,120,130' });
        this.kmdmList = res.data;
        (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList = this.xgmnsrFlag
          ? [...this.kmdmList, { value: '2221010000', label: '应交税费-应交增值税' }].map((d) => ({
              label: `${d.value} | ${d.label}`,
              value: d.value,
            }))
          : this.kmdmList.map((d) => ({
              label: `${d.value} | ${d.label}`,
              value: d.value,
            }));
        if (this.shanxiyidongFlag) {
          (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList = [
            ...(this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList,
            { value: '5111100301.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-语音' },
            { value: '5111100302.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-短信' },
            { value: '5111100303.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-数据流量业务' },
            { value: '5111100304.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-其他' },
          ];
        } else if (this.quanshanFlag) {
          (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList = [
            ...(this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList,
            { value: '6009990041', label: '主营业务收入-技术服务-免税' },
            { value: '6070000018', label: '主营业务收入-咨询费收入-免税' },
          ];
        }
      }
    },
    tabValue(newVal) {
      if (!this.loadedTabs[newVal]) {
        this.query({ initQuery: true });
        this.loadedTabs[newVal] = true;
      }
      // // 切换tab时重新获取表头配置
      this.fetchTableColumns();
      this.getQueryParamsList();
      this.checkAllowEdit();
    },
    formData1: {
      deep: true,
      handler() {
        if (this.tabValue === '0') this.checkAllowEdit();
      },
    },
    formData2: {
      deep: true,
      handler() {
        if (this.tabValue === '1') this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$message.error(`导入失败`);
      }
      this.clearFiles();
      this.query({ flag: true });
    },
    async sfzjg() {
      if (this.xgmnsrFlag) {
        this.sfzjgFlag = false;
      } else {
        const params = {
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
          sszqq:
            this.tabValue * 1
              ? dayjs(this.formData2.sszq).format('YYYYMM')
              : dayjs(this.formData1.sszq).format('YYYYMM'),
          sszqz:
            this.tabValue * 1
              ? dayjs(this.formData2.sszq).format('YYYYMM')
              : dayjs(this.formData1.sszq).format('YYYYMM'),
          djxh: this.$store.state.zzstz.userInfo?.djxh,
        };
        const { data } = await sfzjg(params);
        this.sfzjgFlag = data.sfzjg;
      }
    },
    // 下载模板
    async downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const isTab1 = this.tabValue === '0';
      const formData = isTab1 ? this.formData1 : this.formData2;

      // 添加表单数据有效性检查
      if (
        !formData ||
        (this.xgmnsrFlag && (!formData.sszqq || !formData.sszqz)) ||
        (!this.xgmnsrFlag && !formData.sszq)
      ) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          if (this.xgmnsrFlag) {
            this.allowEdit =
              dayjs(formData.sszqq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq &&
              dayjs(formData.sszqz).format('YYYY-MM') === sbgzSsyfInfo.ssyfz;
          } else {
            this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
          }
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = this.xgmnsrFlag
            ? await getSbztBySsqqz(formData.sszqq, formData.sszqz, 'BDA0610611')
            : await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    // 动态配置表头信息
    /**
     * 获取动态表头配置
     * 1. 从store获取企业差异化配置
     * 2. 根据当前tab获取默认列配置
     * 3. 根据配置过滤和更新列信息
     * @returns {Array} 处理后的列配置数组
     */
    fetchTableColumns() {
      this.fetchQueryParamsConfig();
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultColumnsSrmx = JSON.parse(JSON.stringify(this.dataColumns1));
        const defaultColumnsXxspztz = JSON.parse(JSON.stringify(this.dataColumns2));
        // 处理列配置
        this.remoteColumnsSrmx = defaultColumnsSrmx
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列
        this.remoteColumnsXxspztz = defaultColumnsXxspztz
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRMX] 获取表头配置失败:', e);
        // 失败时回退到默认配置
        this.remoteColumnsSrmx = this.dataColumns1;
        this.remoteColumnsXxspztz = this.dataColumns2;
      }
      this.sfzjg();
    },
    // 动态配置查询条件信息
    fetchQueryParamsConfig() {
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultQueryParamsColumnsSrmx = JSON.parse(
          JSON.stringify(this.xgmnsrFlag ? this.querySearchConfig3 : this.querySearchConfig1),
        );
        const defaultQueryParamsColumnsPztz = JSON.parse(
          JSON.stringify(this.xgmnsrFlag ? this.querySearchConfig4 : this.querySearchConfig2),
        );

        // 处理查询条件配置
        this.remotequerySearchConfigSrmx = defaultQueryParamsColumnsSrmx
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列// 处理查询条件配置
        this.remotequerySearchConfigXxspztz = defaultQueryParamsColumnsPztz
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRMX] 获取查询条件配置失败:', e);
        // 失败时回退到默认配置
        const defaultQueryParamsColumnsSrmx = JSON.parse(
          JSON.stringify(this.xgmnsrFlag ? this.querySearchConfig3 : this.querySearchConfig1),
        );
        const defaultQueryParamsColumnsPztz = JSON.parse(
          JSON.stringify(this.xgmnsrFlag ? this.querySearchConfig4 : this.querySearchConfig2),
        );
        this.remotequerySearchConfigSrmx = defaultQueryParamsColumnsSrmx;
        this.remotequerySearchConfigXxspztz = defaultQueryParamsColumnsPztz;
      }
    },
    initTabValue() {
      this.tabValue = '0';
    },
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);

        if (params.get('skssqq')) {
          if (this.xgmnsrFlag) {
            this.$refs.queryControl1.setParams({
              sszqq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
              sszqz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
            });
            this.formData1.sszqq = dayjs(this.$route.query.skssqq).format('YYYY-MM');
            this.formData1.sszqz = dayjs(this.$route.query.skssqz).format('YYYY-MM');
            this.$refs.queryControl2.setParams({
              sszqq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
              sszqz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
            });
            this.formData2.sszqq = dayjs(this.$route.query.skssqq).format('YYYY-MM');
            this.formData2.sszqz = dayjs(this.$route.query.skssqz).format('YYYY-MM');
            return;
          }
          this.$refs.queryControl1.setParams({
            sszq: dayjs(params.get('skssqq')).format('YYYY-MM'),
          });
          this.formData1.sszq = computeSszq();
          this.$refs.queryControl2.setParams({
            sszq: dayjs(params.get('skssqq')).format('YYYY-MM'),
          });
          this.formData2.sszq = computeSszq();
          return;
        }
      }
      if (this.xgmnsrFlag) {
        this.$refs.queryControl1.setParams({
          sszqq: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
          sszqz: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
        });
        this.formData1.sszqq = dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM');
        this.formData1.sszqz = dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM');
        this.$refs.queryControl2.setParams({
          sszqq: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
          sszqz: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
        });
        this.formData2.sszqq = dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM');
        this.formData2.sszqz = dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM');
      } else {
        this.$refs.queryControl1.setParams({
          sszq: computeSszq(),
        });
        this.formData1.sszq = computeSszq();
        this.$refs.queryControl2.setParams({
          sszq: computeSszq(),
        });
        this.formData2.sszq = computeSszq();
      }
      // 在方法末尾添加回调
      this.$nextTick(() => {
        this.checkAllowEdit();
      });
    },
    onScfxpz() {
      if (!this.selectedRowKeys1.length) {
        this.$message.warning('请先选中要生成反向凭证的行');
      } else {
        // 执行删行
        this.checkBox1.forEach((item) => {
          this.delformData.push(item.uuid);
        });
        const params = { uuidList: this.delformData };
        this.scfxpz(params);
        this.checkBox1 = [];
        this.selectedRowKeys1 = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
        // this.$message.success('删除成功');
      }
    },
    async scfxpz(params) {
      try {
        this.scfxpzLoading = true;
        // 先校验申报状态
        const { msg } = await scfxpz(params);
        this.$message.success(msg);
        this.query({ flag: true });
      } catch (e) {
        console.error('[SRMX] 生成反向凭证失败:', e);
      } finally {
        this.delformData = [];
        this.scfxpzLoading = false;
      }
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      if (this.tabValue === '0') {
        const otherObj = {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
          gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
          // eslint-disable-next-line no-nested-ternary
          sszq: pageType
            ? row.sszq
            : this.xgmnsrFlag
            ? dayjs(this.formData1.sszqq).format('YYYYMM')
            : dayjs(this.formData1.sszq).format('YYYYMM'),
          ly: '1',
        };
        let selectedRowData = row;
        if (pageType === 0 && this.selectedRowKeys1.length > 0) {
          console.log('selectedRowKeys1', this.selectedRowKeys1);
          const temp = JSON.stringify(this.tableData1.find((t) => t.uuid === this.selectedRowKeys1[0]));
          selectedRowData = JSON.parse(temp);
          this.$delete(selectedRowData, 'uuid');
        }

        this.editDialogVisible1 = {
          row: JSON.parse(JSON.stringify(selectedRowData)),
          oldkjpzbh: row.kjpzbh,
          otherObj,
          pageType,
          lrzxList: this.lrzxList,
        };
        console.log('editDialogVisible1', this.editDialogVisible1);
      } else {
        const otherObj = {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
          gsh: this.$store.state.zzstz.userInfo?.qydmz || '',
          // eslint-disable-next-line no-nested-ternary
          sszq: pageType
            ? row.sszq
            : this.xgmnsrFlag
            ? dayjs(this.formData2.sszqq).format('YYYYMM')
            : dayjs(this.formData2.sszq).format('YYYYMM'),
          ly: '1',
        };
        let selectedRowData = row;
        if (pageType === 0 && this.selectedRowKeys2.length > 0) {
          console.log('selectedRowKeys2', this.selectedRowKeys2);
          const temp = JSON.stringify(this.tableData2.find((t) => t.uuid === this.selectedRowKeys2[0]));
          selectedRowData = JSON.parse(temp);
          this.$delete(selectedRowData, 'uuid');
        }

        const rowdata = JSON.stringify(selectedRowData);
        this.editDialogVisible2 = {
          row: JSON.parse(rowdata),
          oldkjpzbh: row.kjpzbh,
          otherObj,
          pageType,
          jsfsList: this.jsfsList,
          zsxmList: this.zsxm1List,
        };
      }
    },
    async delRow() {
      if (this.tabValue === '0') {
        if (!this.selectedRowKeys1.length) {
          this.$message.warning('请先选中要删除的行');
        } else if (this.checkSelectedRowsDate()) {
          this.$message.warning(
            `第${this.delUnallowRows.join(`、`)}行的信息为原始凭证信息，不能删除，请取消选中后再进行删除操作。`,
          );
          this.delUnallowRows = [];
        } else {
          this.boxvisible = true;
        }
      } else if (!this.selectedRowKeys2.length) {
        this.$message.warning('请先选中要删除的行');
      } else if (this.checkSelectedRowsDate()) {
        this.$message.warning(
          `第${this.delUnallowRows.join(`、`)}行的信息为原始凭证信息，不能删除，请取消选中后再进行删除操作。`,
        );
        this.delUnallowRows = [];
      } else {
        this.boxvisible = true;
      }
    },
    checkSelectedRowsDate() {
      if (this.tabValue === '0') {
        this.checkBox1.forEach((item) => {
          const index = this.tableData1.findIndex((i) => i.uuid === item.uuid);
          if (this.tableData1[index].ly === '0') {
            this.delUnallowRows.push(index + 1);
          }
        });
      } else {
        this.checkBox2.forEach((item) => {
          const index = this.tableData2.findIndex((i) => i.uuid === item.uuid);
          if (this.tableData2[index].ly === '0') {
            this.delUnallowRows.push(index + 1);
          }
        });
      }
      return this.delUnallowRows.length;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      if (this.tabValue === '0') {
        // 执行删行
        this.checkBox1.forEach((item) => {
          const index = this.tableData1.findIndex((i) => i.uuid === item.uuid);
          this.tableData1.splice(index, 1);
          this.delformData.push(item.uuid);
        });
        const params = this.delformData;
        this.delete(params);
        this.checkBox1 = [];
        this.selectedRowKeys1 = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
        // this.$message.success('删除成功');
      } else {
        // 执行删行
        this.checkBox2.forEach((item) => {
          const index = this.tableData2.findIndex((i) => i.uuid === item.uuid);
          this.tableData2.splice(index, 1);
          this.delformData.push(item.uuid);
        });
        const params = this.delformData;
        this.delete(params);
        this.checkBox2 = [];
        this.selectedRowKeys2 = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
        // this.$message.success('删除成功');
      }
    },
    rehandleSelectChange(value, { selectedRowData }) {
      if (this.tabValue === '0') {
        this.selectedRowKeys1 = value;
        this.checkBox1 = selectedRowData.filter((i) => i);
      } else {
        this.selectedRowKeys2 = value;
        this.checkBox2 = selectedRowData.filter((i) => i);
      }
    },
    async getQueryParamsList() {
      try {
        const res1 = await getAll();
        this.jsfsList = res1.data.jsfsList;
        this.zsxm1List = res1.data.zsxmList;
        this.srlxList = res1.data.srlxList;
        const res2 = await getInit();
        this.kjfpList = res2.data.kjpzList;

        this.kmdmList = res2.data.kmdmList;
        if (this.tongyongguojiFlag) {
          this.slList = res1.data.slList.map((value) => ({
            value,
            label: `${(value * 100).toFixed(0)}%`,
          }));
        } else {
          const res3 = await getJsfssldzb();
          this.slList = this.uniqueObjects(res3.data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
        }

        this.getQueryParamsListSrmx();
        this.getQueryParamsListXxspztz();
        // this.tabValue === '0' ? this.getQueryParamsListSrmx() : this.getQueryParamsListXxspztz();
        // this.querySearchConfig1[2].selectList = this.srlxList;
        // this.querySearchConfig1[3].selectList = this.jsfsList;
        // this.querySearchConfig1[4].selectList = this.zsxm1List;
        // this.querySearchConfig2[2].selectList = this.jsfsList;
        // this.querySearchConfig2[3].selectList = this.zsxm1List;
        // this.querySearchConfig3[2].selectList = this.xgmnsrFlag
        //   ? this.srlxList.filter((item) => item.value !== '130')
        //   : this.srlxList;
        // this.querySearchConfig3[3].selectList = this.zsxm1List.slice(0, 3);
        // this.querySearchConfig4[3].selectList = this.zsxm1List.slice(0, 3);
        // this.querySearchConfig3[4].selectList = this.kmdmList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        // this.querySearchConfig1[8].selectList = this.kmdmList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        // this.querySearchConfig1[5].selectList = this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
        // this.querySearchConfig2[4].selectList = this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
        // this.querySearchConfig4[4].selectList = this.slList
        //   .filter((i) => [0.03, 0.05].includes(i.value))
        //   .map((d) => ({ label: d.label, value: String(d.value) }));

        // this.querySearchConfig1[9].selectList = this.pzzlList;
        // this.querySearchConfig2[6].selectList = this.getQueryParamsKmbmList;
        // this.querySearchConfig4[6].selectList = [
        //   { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
        //   { value: '2221080000', label: '应交税费-增值税简易计税' },
        //   { value: '2221010000', label: '应交税费-应交增值税' },
        // ];
        console.log('[SRMX] 组件初始化完成');
      } catch (e) {
        console.error(e);
      }
    },
    getQueryParamsListSrmx() {
      (this.querySearchConfigSrmx.find((c) => c.key === 'srlxDm') || {}).selectList = this.srlxList;
      (this.querySearchConfigSrmx.find((c) => c.key === 'jsfsDm1') || {}).selectList = this.jsfsList;
      (this.querySearchConfigSrmx.find((c) => c.key === 'zsxmDm1') || {}).selectList = this.xgmnsrFlag
        ? this.zsxm1List.slice(0, 3)
        : this.zsxm1List;
      (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList = this.xgmnsrFlag
        ? [...this.kmdmList, { value: '2221010000', label: '应交税费-应交增值税' }].map((d) => ({
            label: `${d.value} | ${d.label}`,
            value: d.value,
          }))
        : this.kmdmList.map((d) => ({
            label: `${d.value} | ${d.label}`,
            value: d.value,
          }));
      (this.querySearchConfigSrmx.find((c) => c.key === 'sl1') || {}).selectList = this.slList.map((d) => ({
        label: d.label,
        value: String(d.value),
      }));
      (this.querySearchConfigSrmx.find((c) => c.key === 'ly') || {}).selectList = this.pzzlList;
    },
    getQueryParamsListXxspztz() {
      (this.querySearchConfigXxspztz.find((c) => c.key === 'jsfsDm') || {}).selectList = this.jsfsList;
      (this.querySearchConfigXxspztz.find((c) => c.key === 'zsxmDm1') || {}).selectList = this.xgmnsrFlag
        ? this.zsxm1List.slice(0, 3)
        : this.zsxm1List;
      (this.querySearchConfigXxspztz.find((c) => c.key === 'sl1') || {}).selectList = this.xgmnsrFlag
        ? this.slList
            .filter((i) => [0.03, 0.05].includes(i.value))
            .map((d) => ({ label: d.label, value: String(d.value) }))
        : this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
      (this.querySearchConfigXxspztz.find((c) => c.key === 'kmbm') || {}).selectList = this.getQueryParamsKmbmList;
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async getLrzx() {
      try {
        const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = data;
        (this.querySearchConfigSrmx.find((c) => c.key === 'lrzx') || {}).selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        (this.querySearchConfigXxspztz.find((c) => c.key === 'lrzx') || {}).selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        // this.tabValue === '0'
        //   ? (this.querySearchConfigSrmx.find((c) => c.key === 'lrzx').selectList = this.lrzxList.map((d) => ({
        //       label: `${d.value} | ${d.label}`,
        //       value: d.value,
        //     })))
        //   : (this.querySearchConfigXxspztz.find((c) => c.key === 'lrzx').selectList = this.lrzxList.map((d) => ({
        //       label: `${d.value} | ${d.label}`,
        //       value: d.value,
        //     })));
        // this.querySearchConfig1[1].selectList = this.lrzxList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        // this.querySearchConfig2[1].selectList = this.lrzxList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        // this.querySearchConfig4[2].selectList = this.lrzxList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
      } catch (e) {
        console.error('[SRMX] 获取利润中心列表操作失败:', e);
      }
    },
    async query(pm = { flag: false, p: false, fy: false, from: false, initQuery: false }) {
      // this.getLrzx();
      console.log('query', pm);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) {
        this.tabValue * 1 ? (this.pagination2.current = 1) : (this.pagination1.current = 1);
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: 0,
        pageSize: 0,
      };
      if (this.tabValue === '1') {
        params.pageNum = this.pagination2.current;
        params.pageSize = this.pagination2.pageSize;
      } else {
        params.pageNum = this.pagination1.current;
        params.pageSize = this.pagination1.pageSize;
      }
      if (p) {
        this.tabValue = p?.tabValue || '0';
        params = { ...p, ...params, zsxmDm1: p?.zsxmDm1 === '01,02' ? '' : p.zsxmDm1 }; // 起始时间待解决
        if (this.tabValue === '0') {
          if (this.xgmnsrFlag) {
            this.$refs.queryControl1.setParams({
              sszqq: dayjs(p.sszqq).format('YYYY-MM'),
              sszqz: dayjs(p.sszqz).format('YYYY-MM'),
              lrzx: (p?.lrzx ?? '').split(','),
              srlxDm: (p?.srlxDm ?? '').split(','),
              zsxmDm1: p?.zsxmDm1 === '01,02' ? '' : (p?.zsxmDm1 ?? '').split(','),
              // eslint-disable-next-line no-nested-ternary
              sl1: p?.sl1 === 0 ? String(p.sl1 ?? '').split(',') : (p?.sl1 ?? '').toString().split(','),
              kmdm: (p?.kmdm ?? '').split(','),
            });
          } else {
            this.$refs.queryControl1.setParams({
              sszq: String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6))),
              lrzx: (p?.lrzx ?? '').split(','),
              srlxDm: (p?.srlxDm ?? '').split(','),
              jsfsDm1: (p?.jsfsDm1 ?? '').split(','),
              zsxmDm1: p?.zsxmDm1 === '01,02' ? '' : (p?.zsxmDm1 ?? '').split(','),
              sl1: p?.sl1 === 0 ? String(p.sl1 ?? '').split(',') : (p?.sl1 ?? '').toString().split(','),
              kmdm: (p?.kmdm ?? '').split(','),
              bhznsqynsrsbh: p?.bhznsqynsrsbh || '',
            });
          }
        }
      } else {
        if (this.tabValue === '0') {
          params = {
            ...this.formData1,
            ...params,
            sszq: dayjs(this.formData1.sszq).format('YYYYMM'),
            sszqq: dayjs(this.formData1.sszqq).format('YYYYMM'),
            sszqz: dayjs(this.formData1.sszqz).format('YYYYMM'),
            srlxDm: multiSelectHandle(this.formData1.srlxDm),
            zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
            jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
            sl1: multiSelectHandle(this.formData1.sl1),
            kmdm: multiSelectHandle(this.formData1.kmdm),
            lrzx: multiSelectHandle(this.formData1.lrzx),
          };
          if (this.xgmnsrFlag) {
            this.$delete(params, 'sszq');
            this.$delete(params, 'jsfsDm1');
          } else {
            this.$delete(params, 'sszqq');
            this.$delete(params, 'sszqz');
          }
        }
        if (this.tabValue === '1' || this.fristQuery) {
          params = {
            ...this.formData2,
            ...params,
            sszq: dayjs(this.formData2.sszq).format('YYYYMM'),
            sszqq: dayjs(this.formData2.sszqq).format('YYYYMM'),
            sszqz: dayjs(this.formData2.sszqz).format('YYYYMM'),
            zsxmDm: this.formData2.zsxmDm1,
          };
          if (this.xgmnsrFlag) {
            this.$delete(params, 'sszq');
          } else {
            this.$delete(params, 'sszqq');
            this.$delete(params, 'sszqz');
          }
        }
      }
      // 增加缓存判断逻辑
      if (this.loadedTabs[this.tabValue] && !pm.initQuery && !pm.flag) {
        this.tableLoading = false;
        return;
      }
      try {
        // 拆分两个tab的独立查询逻辑
        if (this.tabValue === '0') {
          console.log('srmx-params', params);
          const { data } = await srzzmxQuery(params);

          this.tableData1 = data.records || [];
          this.tableData1.forEach((item, index) => {
            this.$set(item, 'index', index);
            // 收入类型为主营业务收入和其他业务收入时，数据不展示分配
            if (item.srlxDm === '110' || item.srlxDm === '120') {
              this.$set(item, 'kjfpmc', '');
            } else {
              this.$set(item, 'kjfpmc', item.kjfpMc ? `${item.kjfp} | ${item.kjfpMc}` : item.kjfp);
            }
          });
          this.pagination1.total = data.pageTotal;
          this.loadedTabs['0'] = true;

          if (this.pagination1.total > 0) {
            const { data } = await srzzmxQueryHj(params);
            this.footData1 =
              [
                {
                  se: numberToPrice(data?.se),
                  xse: numberToPrice(data?.xse),
                  bbje: numberToPrice(data?.bbje),
                },
              ] || [];
          } else {
            this.footData1 = [];
          }
          jyssReadyStatusFetch(
            this.$store.state.zzstz.userInfo?.djxh || '',
            this.$store.state.zzstz.userInfo?.nsrsbh || '',
            this.formData1.sszq,
          );
        } else if (this.tabValue === '1') {
          const { data } = await queryXxspz(params);

          this.tableData2 = data.records || [];
          this.tableData2.forEach((item, index) => {
            const kjfpmc = this.kjfpList.find((i) => i.value === item.kjfpDm)?.label;
            this.$set(item, 'index', index);
            this.$set(item, 'kjfpmc', kjfpmc || item.kjfpDm);
            this.$set(item, 'jsfsmc', this.jsfsList.find((i) => i.value === item.jsfsDm)?.label || item.jsfsDm);
          });
          this.pagination2.total = data.pageTotal;
          this.loadedTabs['1'] = true;

          if (this.pagination2.total > 0) {
            const { data } = await queryXxspzHj(params);
            this.footData2 =
              [
                {
                  se: numberToPrice(data?.se),
                },
              ] || [];
          } else {
            this.footData2 = [];
          }
          jyssReadyStatusFetch(
            this.$store.state.zzstz.userInfo?.djxh || '',
            this.$store.state.zzstz.userInfo?.nsrsbh || '',
            this.formData2.sszq,
          );
        }
      } catch (e) {
        console.error(e);
        this.tableData1 = [];
        this.footData1 = [];
        this.tableData2 = [];
        this.footData2 = [];
      } finally {
        this.tableLoading = false;
        this.fristQuery = false;
      }
    },
    async delete(params) {
      try {
        let res = {};
        if (this.tabValue === '0') {
          res = await srmxDelete(params);
        } else {
          res = await deleteXxspz(params);
        }

        this.$message.success(res.msg);
      } catch (e) {
        console.error('[SRMX] 操作失败:', e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }, type) {
      // [this.pagination1, this.pagination2][type].current =
      //   pageSize !== [this.pagination1, this.pagination2][type].pageSize ? 1 : current;
      // [this.pagination1, this.pagination2][type].pageSize = pageSize;
      // this.query({ fy: true });
      // 根据type确定要更新的分页对象
      const pagination = type === 0 ? this.pagination1 : this.pagination2;

      // 更新当前页码和每页条数
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;

      // 重置对应tab的加载状态
      const tabKey = type === 0 ? '0' : '1';
      this.loadedTabs[tabKey] = false;

      // 触发查询
      this.query({ fy: true });
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
