import dayjs from 'dayjs';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const pzzlList = [
  { value: '0', label: '原始凭证' },
  { value: '1', label: '手工凭证' },
  { value: '2', label: '手工反向凭证' },
  { value: '3', label: '自动反向凭证' },
  { value: '4', label: '手工尾差凭证' },
  { value: '5', label: '自动尾差凭证' },
];
export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '参考凭证编号',
    key: 'ckpzh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmdm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
  {
    label: '凭证种类',
    key: 'ly',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
  {
    label: '凭证抬头',
    key: 'pztt',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '参考凭证编号',
    key: 'cdefine4',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmbm',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
];
export const querySearchConfig3 = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmdm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const querySearchConfig4 = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '参考凭证编号',
    key: 'cdefine4',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmbm',
    type: 'select',
    // multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
];
export const dataColumns1 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'gsh2',
    title: '公司号',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'lrzx',
    title: '利润中心',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmdm',
    title: '科目编码',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmmc',
    title: '科目名称',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kjpzbh',
    title: '会计凭证编号',
    width: 210,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'ckpzh',
    title: '参考凭证编号',
    width: 210,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'srlxmc',
    title: '收入类型',
    width: 120,
  },
  {
    width: 180,
    colKey: 'jsfsmc',
    title: '计税方式',
  },
  {
    width: 100,
    colKey: 'zsxmmc',
    title: '征税项目',
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'xse',
    title: '自定义销售额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '自定义税额',
    width: 140,
  },
  {
    colKey: 'kjfpmc',
    title: '分配',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'pztt',
    title: '凭证抬头',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'khbh',
    title: '客户编号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'fzjgnsrsbh',
    title: '分支机构纳税人识别号',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'bhznsqynsrsbh',
    title: '汇总分配分支机构识别号',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'ly',
    title: '凭证种类',
    width: 140,
  },
  // 泉膳分支新增文本列
  {
    width: 200,
    colKey: 'wbzlmc',
    title: '文本',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'right',
    colKey: 'bbje',
    title: '本币金额',
    width: 140,
    fixed: 'right',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];
export const dataColumns2 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ly !== '1' }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'gsh',
    title: '公司号',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'lrzx',
    title: '利润中心',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kjpzbh',
    title: '会计凭证编号',
    width: 210,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'cdefine4',
    title: '参考凭证编号',
    width: 210,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmbm',
    title: '科目编码',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmmc',
    title: '科目名称',
    width: 220,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'jsfsmc',
    title: '计税方式',
  },
  {
    width: 100,
    colKey: 'zsxmmc',
    title: '征税项目',
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
  },
  {
    colKey: 'kjfpmc',
    title: '分配',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '本币金额',
    width: 140,
    fixed: 'right',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];

export const kmDmListZjg = [
  { value: '2104020701.070117', label: '应交税费-应交增值税-销项税额-一般销售.13%货物-13%货物税额' },
  { value: '2104020701.070131', label: '应交税费-应交增值税-销项税额-一般销售.13%有形动产租赁-13%货物税额' },
  { value: '2104020701.070121', label: '应交税费-应交增值税-销项税额-一般销售.17%货物-13%货物税额' },
  { value: '2104020702.070117', label: '应交税费-应交增值税-销项税额-视同销售.13%货物-13%货物税额' },
  { value: '2104020701.070130', label: '应交税费-应交增值税-销项税额-一般销售.13%服务税额' },
  { value: '2104020701.070129', label: '应交税费-应交增值税-销项税额-一般销售.9%货物税额' },
  { value: '2104020702.070129', label: '应交税费-应交增值税-销项税额-视同销售.9%货物税额' },
  { value: '2104020701.070125', label: '应交税费-应交增值税-销项税额-一般销售.9%建筑服务-9%服务税额' },
  { value: '2104020701.070126', label: '应交税费-应交增值税-销项税额-一般销售.9%不动产租赁-9%服务税额' },
  { value: '2104020701.070127', label: '应交税费-应交增值税-销项税额-一般销售.9%不动产转让-9%服务税额' },
  { value: '2104020701.070128', label: '应交税费-应交增值税-销项税额-一般销售.9%其他服务-9%服务税额' },
  { value: '2104080100.070102', label: '应交税费-适用简易计税方法应交增值税-应缴.5%服务税额' },
  { value: '2104080100.070101', label: '应交税费-适用简易计税方法应交增值税-应缴.3%货物税额' },
  { value: '5111100301.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-语音' },
  { value: '5111100302.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-短信' },
  { value: '5111100303.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-数据流量业务' },
  { value: '5111100304.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-其他' },
];

export const kmDmListFzjg = [
  { value: '2104080100.0.sx', label: '分支机构收入-9b税额' },
  { value: '2104020000.0.sx', label: '分支机构收入-13a税额' },
];

export const kmdmListSxyd = [
  { value: '5110000000.0.sx', label: '分支机构收入-13a销售额' },
  { value: '2104020000.0.sx', label: '分支机构收入-13a税额' },
  { value: '5110020701.070117.sx', label: '增值税一般销售.13%货物-13%货物销售额' },
  { value: '2104020701.070117', label: '应交税费-应交增值税-销项税额-一般销售.13%货物-13%货物税额' },
  { value: '5110020701.070131.sx', label: '增值税一般销售.13%有形动产租赁-13%货物销售额' },
  { value: '2104020701.070131', label: '应交税费-应交增值税-销项税额-一般销售.13%有形动产租赁-13%货物税额' },
  { value: '5110020701.070121.sx', label: '增值税一般销售.17%货物-13%货物销售额' },
  { value: '2104020701.070121', label: '应交税费-应交增值税-销项税额-一般销售.17%货物-13%货物税额' },
  { value: '5110020701.070130.sx', label: '增值税一般销售.13%服务销售额' },
  { value: '2104020701.070130', label: '应交税费-应交增值税-销项税额-一般销售.13%服务税额' },
  { value: '5110020701.070129.sx', label: '增值税一般销售.9%货物销售额' },
  { value: '2104020701.070129', label: '应交税费-应交增值税-销项税额-一般销售.9%货物税额' },
  { value: '5110020701.070125.sx', label: '增值税一般销售.9%建筑服务-9%服务销售额' },
  { value: '2104020701.070125', label: '应交税费-应交增值税-销项税额-一般销售.9%建筑服务-9%服务税额' },
  { value: '5110020701.070126.sx', label: '增值税一般销售.9%不动产租赁-9%服务销售额' },
  { value: '2104020701.070126', label: '应交税费-应交增值税-销项税额-一般销售.9%不动产租赁-9%服务税额' },
  { value: '5110020701.070127.sx', label: '增值税一般销售.9%不动产转让-9%服务销售额' },
  { value: '2104020701.070127', label: '应交税费-应交增值税-销项税额-一般销售.9%不动产转让-9%服务税额' },
  { value: '5110020701.070128.sx', label: '增值税一般销售.9%其他服务-9%服务销售额' },
  { value: '2104020701.070128', label: '应交税费-应交增值税-销项税额-一般销售.9%其他服务-9%服务税额' },
  { value: '5110020701.070103.sx', label: '增值税电信服务一般销售.6%服务销售额' },
  { value: '2104020701.070103', label: '应交税费-应交增值税-销项税额-一般销售.税率税项-增值税-税率-6%服务' },
  { value: '5110020701.070124.sx', label: '增值税电信服务一般销售.9%基础电信服务销售额' },
  { value: '2104020701.070124', label: '应交税费-应交增值税-销项税额-一般销售.税率税项-增值税-税率-9%基础电信服务' },
  { value: '5171110000.0.sx', label: '分支机构收入-9b销售额' },
  { value: '2104080100.0.sx', label: '分支机构收入-9b税额' },
  { value: '5110080100.070102.sx', label: '增值税简易计税-应缴.5%服务销售额' },
  { value: '2104080100.070102', label: '应交税费-适用简易计税方法应交增值税-应缴.5%服务税额' },
  { value: '5110080100.070101.sx', label: '增值税简易计税-应缴.3%货物销售额' },
  { value: '2104080100.070101', label: '应交税费-适用简易计税方法应交增值税-应缴.3%货物税额' },
  { value: '5110020702.070117.sx', label: '增值税视同销售.13%货物-13%货物销售额' },
  { value: '2104020702.070117', label: '应交税费-应交增值税-销项税额-视同销售.13%货物-13%货物税额' },
  { value: '5110020702.070129.sx', label: '增值税-视同销售.9%货物销售额' },
  { value: '2104020702.070129', label: '应交税费-应交增值税-销项税额-视同销售.9%货物税额' },
  { value: '5110020702.070103.sx', label: '增值税电信服务视同销售.6%服务销售额' },
  { value: '2104020702.070103', label: '应交税费-应交增值税-销项税额-视同销售.税率税项-增值税-税率-6%服务' },
  { value: '5110020702.070124.sx', label: '增值税电信服务视同销售.9%基础电信服务销售额' },
  { value: '2104020702.070124', label: '应交税费-应交增值税-销项税额-视同销售.税率税项-增值税-税率-9%基础电信服务' },
  { value: '2104020102.0', label: '应交税费-应交增值税-进项税额-汇总业务.认证相符增值税专票-本期认证税额' },
  { value: '2104020102.100201', label: '应交税费-应交增值税-进项税额-汇总业务.认证相符增值税专票-其他' },
  { value: '2104020102.100101', label: '应交税费-应交增值税-进项税额-汇总业务.其他扣税凭证-旅客运输服务' },
  { value: '2104020101.0', label: '应交税费-应交增值税-进项税额-属地业务.认证相符增值税专票-本期认证税额' },
  { value: '2104020101.100201', label: '应交税费-应交增值税-进项税额-属地业务.认证相符增值税专票-其他' },
  { value: '2104020101.100101', label: '应交税费-应交增值税-进项税额-属地业务.其他扣税凭证-旅客运输服务' },
  { value: '5502000000.0.sx', label: '进项税额.其他扣税凭证-旅客运输服务金额' },
  { value: '2104020900.070201', label: '应交税费-应交增值税-进项税额转出.免税项目用的进项税转出额' },
  { value: '2104020900.070202', label: '应交税费-应交增值税-进项税额转出.集体福利、个人消费的进项转出额' },
  { value: '2104020900.070203', label: '应交税费-应交增值税-进项税额转出.非正常损失的进项转出额' },
  { value: '2104020900.070204', label: '应交税费-应交增值税-进项税额转出.简易计税方法征税项目用的进项税转出额' },
  { value: '2104020900.070205', label: '应交税费-应交增值税-进项税额转出.红字专用发票信息表注明的进项税额' },
  { value: '2104020900.070206', label: '应交税费-应交增值税-进项税额转出.其他应作进项税额转出的情形' },
  { value: '5111100301.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-语音' },
  { value: '5111100302.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-短信' },
  { value: '5111100303.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-数据流量业务' },
  { value: '5111100304.0', label: '通信与信息服务收入-网间结算收入-国际漫游来访-其他' },
  { value: '5171110001.0.sx', label: '分支机构征税项目-基础电信服务销售额' },
  { value: '5171110002.0.sx', label: '分支机构征税项目-增值电信服务销售额' },
  { value: '5171110003.0.sx', label: '分支机构征税项目-其他应税服务销售额' },
  { value: '5171110004.0.sx', label: '分支机构免税项目-电信服务销售额' },
  { value: '5171110005.0.sx', label: '分支机构免税项目-其他应税服务销售额' },
  { value: '5171110006.0.sx', label: '分支机构预收款' },
];

export const kmDmListQuanShan = [
  { value: '3170000007', label: '应交税费-应交增值税-销项税额-专6' },
  { value: '3170000010', label: '应交税费-应交增值税-销项税额-普6' },
  { value: '3170000061', label: '应交税费-应交增值税-销项税额-专9-货物' },
  { value: '3170000064', label: '应交税费-应交增值税-销项税额-专9-服务' },
  { value: '3170000051', label: '应交税费-应交增值税-销项税额-普9' },
  { value: '3170000052', label: '应交税费-应交增值税-销项税额-普13' },
  { value: '3170000062', label: '应交税费-应交增值税-销项税额-专13-货物及委托加工' },
  { value: '3170000054', label: '应交税费-应交增值税-销项税额-专13-服务及不动产' },
];

export const sl1List = [
  { value: 0.17, label: '17%' },
  { value: 0.16, label: '16%' },
  { value: 0.13, label: '13%' },
  { value: 0.1, label: '10%' },
  { value: 0.09, label: '9%' },
  { value: 0.06, label: '6%' },
  { value: 0.05, label: '5%' },
  { value: 0.03, label: '3%' },
  { value: 0.0, label: '0%' },
];
