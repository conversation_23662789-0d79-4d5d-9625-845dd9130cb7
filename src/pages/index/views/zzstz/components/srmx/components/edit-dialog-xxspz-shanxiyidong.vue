<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增销项税凭证', '编辑销项税凭证'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsDm">
              <t-select v-model="formData.jsfsDm" placeholder="请选择计税方式" clearable @change="onJsfsChange">
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable @change="onSlChange">
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm">
              <t-select v-model="formData.zsxmDm" placeholder="请选择征税项目" clearable @change="onZsxmChange">
                <t-option
                  v-for="item in zsxmList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmbm">
              <t-select
                v-model="formData.kmbm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmbm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmbm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" align="left" :onBlur="checkSlBbje" clearable
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { addXxspz, updateXxspz } from '@/pages/index/api/tzzx/zzstz/xxspz.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { kmdmListSxyd } from '../config.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm: [{ required: true, message: '必填', type: 'error' }],
      kmbm: [{ required: true, message: '必填', type: 'error' }],
      se: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      // jsfsList: [
      //   { value: '01', label: '一般计税方式' },
      //   { value: '02', label: '简易计税方式' },
      // ],
      zsxmList: [],
      slList: [],
      kmdmList: [],
      kmdmListSxyd,
      formData: {
        sszq: '',
        uuid: '',
        gsh: '',
        lrzx: '',
        kjpzbh: '',
        cdefine4: '',
        srlxDm: '',
        sl1: '',
        jsfsDm: '',
        zsxmDm: '',
        kmbm: '',
        kmmc: '',
        se: '',
        tzyy: '',
        ly: '',
      },
      taxConfig: {
        zjg: {
          // 总机构
          month: {
            // 按月
            '01': {
              // 计税方法
              0.13: {
                // 税率
                '01': [
                  // 征税项目
                  '2104020701.070117',
                  '2104020701.070131',
                  '2104020701.070121',
                  '2104020702.070117',
                ],
                '03': ['2104020701.070130'],
              },
              0.09: {
                '01': ['2104020701.070129', '2104020702.070129'],
                '03': ['2104020701.070125', '2104020701.070126', '2104020701.070127', '2104020701.070128'],
              },
            },
            '02': {
              // 简易计税方式
              0.05: {
                '03': ['2104080100.070102'],
              },
              0.03: {
                '01': ['2104080100.070101'],
              },
            },
          },
          quarter: {
            // 按季
            '01': {
              // 计税方法
              0.09: {
                // 税率
                '03': [
                  // 征税项目
                  '2104020701.070124',
                  '2104020702.070124',
                ],
              },
              0.06: {
                '03': ['2104020701.070103', '2104020702.070103'],
              },
            },
          },
        },
        fjg: {
          // 分支机构
          month: {
            // 按月
            '02': {
              // 0.13: {
              //   '01': ['2104020000.0.sx'],
              //   '03': ['2104020000.0.sx'],
              //   '04': ['2104020000.0.sx'],
              // },
              // 0.09: {
              //   '01': ['2104020000.0.sx'],
              //   '03': ['2104020000.0.sx'],
              //   '04': ['2104020000.0.sx'],
              // },
              // 0.06: {
              //   '01': ['2104020000.0.sx'],
              //   '03': ['2104020000.0.sx'],
              //   '04': ['2104020000.0.sx'],
              // },
              0.05: {
                // '01': ['2104020000.0.sx'],
                '03': ['2104080100.0.sx'],
                // '04': ['2104020000.0.sx'],
              },
              0.03: {
                '01': ['2104020000.0.sx'],
                '03': ['2104020000.0.sx'],
                '04': ['2104020000.0.sx'],
              },
            },
          },
        },
      },
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    shanxiyidongZfjg() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.fzjgbz;
    },

    jsfsList() {
      // 获取当前机构类型和期间类型下的配置
      const periodConfig = this.taxConfig[this.shanxiyidongZfjg]?.[this.getMonthOrQuarterFetch];
      if (!periodConfig) return [];

      // 从配置中提取所有计税方式代码
      const jsfsCodes = Object.keys(periodConfig);

      // 映射为选项格式
      return jsfsCodes.map((code) => {
        // 使用预定义的标签映射
        const labelMap = {
          '01': '一般计税方式',
          '02': '简易计税方式',
        };

        return {
          value: code,
          label: labelMap[code] || `计税方式 ${code}`,
        };
      });
    },
    getMonthOrQuarterFetch() {
      // 提取月份部分并转换为整数
      const month = parseInt(this.formData.sszq.split('-')[1], 10);

      // 判断是否为季度末月份（3/6/9/12）
      if ([3, 6, 9, 12].includes(month)) {
        return 'quarter';
      }
      return 'month';
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      const res2 = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = res2.data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.uuid) {
        const formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        this.formData = { ...formDataTemp, sszq: dayjs(String(formDataTemp.sszq)).format('YYYY-MM') };
        this.getSlListByJsfs(this.formData.jsfsDm);
        this.getZsxmListBySl(this.formData.sl1);
        this.getKmdmListByZsxm(this.formData.zsxmDm);
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'gsh',
          'lrzx',
          'sl1',
          'jsfsDm',
          'zsxmDm',
          'kmbm',
          'kmmc',
          'kjfpDm',
          'se',
          'tzyy',
          'kjpzbh',
          'cdefine4',
          'ly',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };
        try {
          if (this.visible.pageType) {
            await updateXxspz(params);
          } else {
            await addXxspz(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateSrmx', { flag: true });
          } else {
            this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    // 通过计税方式查询税率下拉列表
    getSlListByJsfs(jsfs) {
      const config = this.taxConfig[this.shanxiyidongZfjg][this.getMonthOrQuarterFetch];
      this.slList = Object.keys(config[jsfs]).map((v) => ({
        value: v,
        label: `${parseFloat(v) * 100}%`,
      }));
    },
    // 通过税率查询征税项目下拉列表
    getZsxmListBySl(sl) {
      const config = this.taxConfig[this.shanxiyidongZfjg][this.getMonthOrQuarterFetch];
      const jsfs = this.formData.jsfsDm;
      const zsxmLabels = {
        '01': '货物',
        '03': '服务',
        '04': '不动产',
      };
      this.zsxmList = Object.keys(config[jsfs][sl]).map((v) => ({
        value: v,
        label: zsxmLabels[v] || v,
      }));
    },
    // 通过征税项目查询科目下拉列表
    getKmdmListByZsxm(zsxm) {
      const config = this.taxConfig[this.shanxiyidongZfjg][this.getMonthOrQuarterFetch];
      const { jsfsDm, sl1 } = this.formData;
      this.kmdmList = this.kmdmListSxyd.filter((item) => config[jsfsDm][sl1][zsxm].includes(item.value));
    },
    // 计税方式变化时触发
    onJsfsChange(jsfs) {
      this.getSlListByJsfs(jsfs);
      this.$set(this.formData, 'sl1', this.slList[0].value);
      this.onSlChange(this.formData.sl1);
    },
    // 税率变化时触发
    onSlChange(sl) {
      this.getZsxmListBySl(sl);
      this.$set(this.formData, 'zsxmDm', this.zsxmList[0].value);
      this.onZsxmChange(this.formData.zsxmDm);
    },
    // 征税项目变化时触发
    onZsxmChange(zsxm) {
      this.getKmdmListByZsxm(zsxm);
      this.$set(this.formData, 'kmbm', this.kmdmList[0].value);
      this.getXzKmmc(this.formData.kmbm);
    },
    checkSlBbje() {
      if (!this.formData.sl1) {
        this.$set(this.formData, 'se', 0);
      }
    },
    getXzKmmc(kmbm) {
      this.$set(this.formData, 'kmmc', this.kmdmListSxyd.find((item) => item.value === kmbm)?.label);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmbm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
