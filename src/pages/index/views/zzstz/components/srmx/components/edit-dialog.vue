<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增收入明细账', '编辑收入明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="收入类型" name="srlxDm">
              <t-select v-model="formData.srlxDm" placeholder="请选择收入类型" clearable @change="srlxChange">
                <t-option
                  v-for="item in srlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable @change="slChange">
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4" v-if="!this.xgmnsrFlag">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择计税方式" @change="jsfsChange" clearable>
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm1">
              <t-select v-model="formData.zsxmDm1" placeholder="请选择征税项目" clearable>
                <t-option
                  v-for="item in zsxm1List"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <!-- 2025.6.4改 收入明细新增和编辑（森马小规模逻辑）
                          1.110和120 新增和编辑不涉及分配显示 不修改
                          2.并且要新增130，但是新增130时，分配字段显示B21和B25,且为必选字段，编辑同理 -->
          <!-- 2025.6.17改 通用国际分支：新增页面，需要支持维护“业务类型”文本 -->
          <t-col :span="4" v-if="!(this.xgmnsrFlag && this.formData.srlxDm !== '130')">
            <t-form-item :label="formLabels.kjfp" name="kjfp">
              <t-select v-model="formData.kjfp" placeholder="请选择会计" clearable>
                <t-option
                  v-for="item in kjfpList"
                  :value="item.value"
                  :label="tongyongguojiFlag ? item.label : `${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="参考凭证编号" name="ckpzh">
              <t-input :maxlength="30" v-model="formData.ckpzh" placeholder="请填写参考凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="bbje">
              <gt-input-money v-model="formData.bbje" theme="normal" align="left" clearable :disabled="isCustom"
            /></t-form-item>
          </t-col>
          <t-col :span="4" v-if="this.hzfpTabVisible">
            <t-form-item label="分支机构识别号" name="fzjgnsrsbh">
              <t-select
                v-model="formData.fzjgnsrsbh"
                placeholder="请选择分支机构识别号"
                clearable
                @change="getBhznsqynsrsbh"
              >
                <t-option v-for="item in fzjgnsrsbhList" :value="item.value" :label="item.value" :key="item.value">
                  <div>{{ item.value }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4" v-if="this.hzfpTabVisible">
            <t-form-item label="汇总分配分支机构识别号" name="bhznsqynsrsbh">
              <t-input
                :maxlength="100"
                v-model="formData.bhznsqynsrsbh"
                placeholder="选择分支机构识别号后自动带出"
                readonly
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="4" style="top: 25px">
            <t-checkbox v-model="isCustom" @change="isCustomChange">自定义调整</t-checkbox>
          </t-col>
          <t-col :span="4">
            <t-form-item label="销售额" name="xse">
              <gt-input-money
                v-model="formData.xse"
                placeholder="请填写销售额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money
                v-model="formData.se"
                placeholder="请填写税额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import {
  getAll,
  getLrzx,
  getKjfp,
  getKmbmByywbm,
  getJsfssldzb,
  queryAllZsxm1sldzb,
} from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { sfzjg } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { srmxSave, srmxUpdate } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      // ckpzh: [{ required: true, message: '必填', type: 'error' }],
      srlxDm: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm1: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm1: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
      bbje: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isCustom: false,
      isVisible: true,
      confirmLoading: false,
      hzfpTabVisible: false,
      rules: {},
      lrzxList: [], // 利润中心
      kjfpList: [], // 分配
      srlxList: [],
      jsfsList: [],
      jsfsListCls: [],
      zsxm1List: [],
      zsxm1ListCls: [],
      slList: [],
      kmdmList: [],
      jsfsSldzList: [],
      zsxm1SldzList: [],
      kjfpTempData: '',
      formDataTemp: {},
      formData: {},
      fzjgnsrsbhList: [],
    };
  },
  created() {},
  async mounted() {
    await this.getLrzx(); // 确保优先加载利润中心数据
    await this.getJsfssldzb();
    await this.queryAllZsxm1sldzb();
    this.init();
    await this.sfzjg();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
        kjfp: labels.kjfp || '分配', // 如果配置中没有kjfp，使用默认值
      };
    },
  },
  watch: {
    'formData.zsxmDm1': {
      async handler() {
        // 添加await确保科目列表加载完成
        if (this.formData.srlxDm) {
          // 新增条件判断
          if (this.xgmnsrFlag && this.formData.srlxDm === '130') {
            this.kmdmList = [
              { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
              { value: '2221080000', label: '应交税费-增值税简易计税' },
              { value: '2221010000', label: '应交税费-应交增值税' },
            ];
            return;
          }
          await getKmbmByywbm({ ywlxDm: this.formData.srlxDm }).then((res) => {
            this.kmdmList = this.uniqueObjects(res.data.map((d) => ({ label: d.label, value: d.value })));
            // 保留原有科目编码值
            if (this.formDataTemp?.kmdm && this.kmdmList.some((item) => item.value === this.formDataTemp.kmdm)) {
              this.formData.kmdm = this.formDataTemp.kmdm;
              this.formData.kmmc = this.formDataTemp.kmmc;
            }
          });
        }
      },
      immediate: false, // 新增配置项
    },
  },
  methods: {
    async sfzjg() {
      if (this.xgmnsrFlag) {
        this.hzfpTabVisible = false;
      } else if (this.tongyongguojiFlag) {
        // 通用国际不包含Hzfp分支
        this.hzfpTabVisible = false;
      } else {
        const params = {
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
          sszqq: dayjs(this.formData.sszq).format('YYYYMM'),
          sszqz: dayjs(this.formData.sszq).format('YYYYMM'),
          djxh: this.$store.state.zzstz.userInfo?.djxh,
        };
        const { data } = await sfzjg(params);
        this.hzfpTabVisible = data.sfzjg;
        if (data.sfzjg) {
          this.fzjgnsrsbhList = Object.entries(data.dzbList).map(([key, value]) => ({
            value: key, // 使用值作为 value（纳税人识别号）
            label: value, // 使用键作为 label（分支代码）
          }));
        }
      }
    },
    getBhznsqynsrsbh(val) {
      this.$set(this.formData, 'bhznsqynsrsbh', this.fzjgnsrsbhList.find((item) => item.value === val)?.label);
    },
    isCustomChange() {
      if (this.isCustom) {
        this.$set(this.formData, 'bbje', 0.0);
        // 2025.7.16 收入明细-新增，当选择自定义调账时，收入类型默认为视同销售。
        this.$set(this.formData, 'srlxDm', '130');
        this.srlxChange(this.formData.srlxDm);
      } else {
        this.$set(this.formData, 'xse', 0.0);
        this.$set(this.formData, 'se', 0.0);
      }
    },
    // 修改init方法中的异步调用顺序
    async init() {
      this.rules = this.baseRules;
      const { data } = await getAll();

      // 先初始化静态数据
      this.jsfsList = data.jsfsList;
      this.jsfsListCls = data.jsfsList;
      this.zsxm1List = this.xgmnsrFlag ? data.zsxmList.slice(0, 3) : data.zsxmList;
      this.zsxm1ListCls = this.xgmnsrFlag ? data.zsxmList.slice(0, 3) : data.zsxmList;
      this.srlxList = data.srlxList;
      this.slList = data.slList.map((value) => ({
        value,
        label: `${(value * 100).toFixed(0)}%`,
      }));

      // 同步设置表单数据
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(
          this.formData,
          'sszq',
          this.xgmnsrFlag ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM') : computeSszq(),
        );
      }
      if (this.visible.row?.srlxDm) {
        await this.editInitSelectedList(this.visible.row);
        const originData = JSON.parse(JSON.stringify(this.visible.row));
        this.formDataTemp = originData;
        console.log('originData', originData);
        this.formData = { ...originData, sszq: dayjs(String(originData.sszq)).format('YYYY-MM') }; // 保持引用独立
        // 等待异步操作完成后设置kjfp
        this.$nextTick(() => {
          this.formData.kjfp = originData.kjfp;
        });
      }
      if (this.visible.pageType) {
        if (this.visible.row?.se !== 0 || this.visible.row?.xse !== 0) {
          this.isCustom = true;
        }
      }
    },
    async queryAllZsxm1sldzb() {
      const { data } = await queryAllZsxm1sldzb();
      this.zsxm1SldzList = data;
    },
    async getJsfssldzb() {
      if (!this.tongyongguojiFlag) {
        const { data } = await getJsfssldzb();
        this.jsfsSldzList = data;
        this.slList = this.xgmnsrFlag
          ? this.uniqueObjects(
              data
                .filter((i) => [0, 0.03, 0.05].includes(i.sl1))
                .map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })),
            )
          : this.uniqueObjects(data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      }
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      // 移除自动设置逻辑，改为初始化时保留原始值
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      }
    },
    async getKjfpList(srlxDm) {
      try {
        const { data } = await getKjfp({ ywDm: srlxDm });
        this.kjfpList = data || [];
        // 2025.6.4改 收入明细新增和编辑（森马小规模逻辑）
        // 1.110和120 新增和编辑不涉及分配显示 不修改
        // 2.并且要新增130，但是新增130时，分配字段显示B21和B25,且为必选字段，编辑同理
        if (this.xgmnsrFlag && srlxDm === '130') {
          this.kjfpList = this.kjfpList.filter((i) => i.value === 'B21' || i.value === 'B25');
        }
        // 2025.6.18修改 通用国际企业分支在选择收入类型为视同收入时，分配字段作为业务类型提供专属下拉字段
        if (this.tongyongguojiFlag && srlxDm === '130') {
          this.kjfpList = [
            { value: '零退税率货物', label: '零退税率货物' },
            { value: '固定资产处置', label: '固定资产处置' },
            { value: '预收款', label: '预收款' },
            { value: '捐赠支出', label: '捐赠支出' },
            { value: '远期结售汇收益', label: '远期结售汇收益' },
            { value: '其他', label: '其他' },
          ];
        }
        // 2025.2.28修改 收入类型为视同销售时，分配字段必填。
        if (srlxDm === '130') {
          this.rules = {
            ...this.baseRules,
            kjfp: [{ required: true, message: '必填', type: 'error' }],
          };
        } else {
          this.rules = this.baseRules;
        }
        if (this.kjfpList.length === 0) {
          this.$set(this.formData, 'kjfp', '');
        }
      } catch (e) {
        console.error(e);
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      if (this.isCustom && this.formData.se === 0 && this.formData.xse === 0) {
        this.$message.warning('勾选【自定义调整】按钮后，请录入相应“税额”与“销售额”。');
      } else {
        const val = await this.$refs.forms.validate();
        this.confirmLoading = true;
        if (val === true) {
          const p = {};
          [
            'sszq',
            'uuid',
            'lrzx',
            'kjpzbh',
            'ckpzh',
            'srlxDm',
            'sl1',
            'jsfsDm1',
            'zsxmDm1',
            'kmdm',
            'kmmc',
            'kjfp',
            'bbje',
            'tzyy',
            'se',
            'xse',
            'fzjgnsrsbh',
            'bhznsqynsrsbh',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = {
            ...p,
            oldkjpzbh: this.visible.oldkjpzbh,
            ...this.visible.otherObj,
            sszq: Number(dayjs(p.sszq).format('YYYYMM')),
          };
          // 通用国际企业分支：额外添加lrzx和gsh字段
          if (this.tongyongguojiFlag) {
            const lrzxItem = this.lrzxList.find((item) => item.value === this.formData.lrzx);
            params.lrzx = this.formData.lrzx; // 确保使用当前选中的value
            params.gsh2 = lrzxItem ? lrzxItem.label : ''; // 使用对应的label作为gsh
          }
          try {
            if (this.visible.pageType) {
              await srmxUpdate(params);
            } else {
              await srmxSave(params);
            }
            this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
            if (this.visible.pageType) {
              this.$emit('updateSrmx', { flag: true });
            } else {
              this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    editInitSelectedList(tempForm) {
      this.getKjfpList(tempForm.srlxDm);
      if (this.tongyongguojiFlag) {
        this.getKmdmListBySrlx(tempForm.srlxDm);
      } else {
        this.getZsxmJsfsList(tempForm);
        getKmbmByywbm({ ywlxDm: tempForm.srlxDm }).then((res) => {
          this.kmdmList = this.uniqueObjects(res.data.map((d) => ({ label: d.label, value: d.value })));
          // // 仅在列表包含原值且当前未设置时赋值
          // if (this.formDataTemp?.kmdm && !this.formData.kmdm) {
          //   const exists = this.kmdmList.some((item) => item.value === this.formDataTemp.kmdm);
          //   if (exists) {
          //     this.formData.kmdm = this.formDataTemp.kmdm;
          //     this.formData.kmmc = this.formDataTemp.kmmc;
          //   }
          // }
          // 禅道 当科目编码为“6051050200”时，征税项目下拉选项为“03 服务”
          if (tempForm.kmdm === '6051050200') {
            this.$set(this.formData, 'zsxmDm1', '03');
            this.zsxm1List = this.zsxm1ListCls.filter((i) => ['01', '03'].includes(i.value));
          }
        });
      }
    },
    srlxChange(val) {
      this.getKjfpList(val);
      if (val && this.tongyongguojiFlag) {
        this.getKmdmListBySrlx(val);
        if (this.kmdmList.length === 1) {
          this.$set(this.formData, 'kmdm', this.kmdmList[0].value);
          this.$set(this.formData, 'kmmc', this.kmdmList[0].label);
        } else if (!this.kmdmList.includes(this.formData.kmdm)) {
          this.$set(this.formData, 'kmdm', '');
          this.$set(this.formData, 'kmmc', '');
        } else {
          this.$refs.forms.clearValidate();
        }
      } else {
        this.getZsxmJsfsList(this.formData);
        if (val) {
          // 新增条件判断
          if (this.xgmnsrFlag && val === '130') {
            this.kmdmList = [
              { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
              { value: '2221080000', label: '应交税费-增值税简易计税' },
              { value: '2221010000', label: '应交税费-应交增值税' },
            ];
            return;
          }
          console.log('srlxChange-val', val);
          getKmbmByywbm({ ywlxDm: val }).then((res) => {
            this.kmdmList = this.uniqueObjects(res.data.map((d) => ({ label: d.label, value: d.value })));
            if (this.kmdmList.length === 1) {
              this.$set(this.formData, 'kmdm', this.kmdmList[0].value);
              this.$set(this.formData, 'kmmc', this.kmdmList[0].label);
            } else if (!this.kmdmList.includes(this.formData.kmdm)) {
              this.$set(this.formData, 'kmdm', '');
              this.$set(this.formData, 'kmmc', '');
            } else {
              this.$refs.forms.clearValidate();
            }
          });
        }
      }
      console.log(this.formData);
    },
    slChange() {
      if (this.tongyongguojiFlag) {
        // 通用国际环境下设置免税和免抵退情况强关联
        if (this.formData.sl1 === 0 && this.formData.srlxDm === '110') {
          // 计税方式下拉限定为免税和免抵退
          this.jsfsList = this.jsfsListCls.filter((i) => ['03', '04'].includes(i.value));
        } else {
          this.jsfsList = this.jsfsListCls;
        }
        return;
      }
      this.getZsxmJsfsList(this.formData);
      if (this.formData.sl1) {
        this.$set(this.formData, 'jsfsDm1', this.jsfsSldzList.find((item) => item.sl1 === this.formData.sl1)?.jsfsDm);
        this.$set(this.formData, 'zsxmDm1', this.jsfsSldzList.find((item) => item.sl1 === this.formData.sl1)?.zsxmDm1);
      }
      if (this.formData.sl1 && this.formData.srlxDm) {
        this.$set(
          this.formData,
          'zsxmDm1',
          this.zsxm1SldzList.find(
            (item) => item.sl1 === this.formData.sl1 && (!item.ywlxDm || item.ywxlDm === this.formData.srlxDm),
          )?.zsxmDm1,
        );
      }
      if (!this.formData.sl1) {
        this.$set(this.formData, 'jsfsDm1', '');
        this.$set(this.formData, 'zsxmDm1', '');
      }
    },
    jsfsChange(val) {
      if (this.tongyongguojiFlag && val === '03') {
        this.$set(this.formData, 'srlxDm', '110');
        this.$set(this.formData, 'sl1', 0.0);
        // 免抵退税
        this.$set(this.formData, 'kmdm', '60010199');
        this.$set(this.formData, 'kmmc', '主营业务收入_商品销售收入_一般商品销售收入');
      }
      if (this.tongyongguojiFlag && val === '04') {
        this.$set(this.formData, 'srlxDm', '110');
        this.$set(this.formData, 'sl1', 0.0);
        // 免税
        this.$set(this.formData, 'kmdm', '60011202');
        this.$set(this.formData, 'kmmc', '主营业务收入_工程承包收入_海外工程承包收入');
      }
    },
    getZsxmJsfsList(tempForm) {
      // 通用国际环境暂时不设置联动关系
      if (this.tongyongguojiFlag) {
        return;
      }
      // 根据收入类型和税率确定计税方式和征税项目代码，由于无法明确用户点击顺序，这里需要判断对应值获取情况
      // 1.通过税率和收入类型获取征税项目
      if (tempForm.sl1 && tempForm.srlxDm) {
        // if (this.formData.sl1 === 0.1 || this.formData.sl1 === 0.09) {
        //   this.formData.zsxmDm1 = this.jsfsSldzList.find(
        //     (item) => item.sl1 === this.formData.sl1 && item.ywxlDm === this.formData.srlxDm,
        //   )?.zsxmDm1;
        //   this.zsxm1List = this.zsxm1ListCls.filter((i) => i.value === this.formData.zsxmDm1);
        // }
        if (tempForm.sl1 === 0.06) {
          // 禅道813 当税率为“6%”时，征税项目下拉选项为“03 服务”和“04 无形资产”
          this.zsxm1List = this.zsxm1ListCls.filter((i) => ['03', '04'].includes(i.value));
          return;
        }
        if (tempForm.sl1 === 0.03 && tempForm.srlxDm === '120') {
          // 禅道 当税率为“3%”时，征税项目下拉选项为“01 货物”和“03 服务”
          this.zsxm1List = this.zsxm1ListCls.filter((i) => ['01', '03'].includes(i.value));
          return;
        }
        if (tempForm.sl1 === 0.05 && tempForm.srlxDm === '130') {
          // 禅道 对于5%不动产销售场景，申报系统仅需支持调账即可，无需兼容财务凭证部分，在调账中支持5%不动产的选项。
          this.zsxm1List = this.zsxm1ListCls.filter((i) => ['03', '05'].includes(i.value));
          return;
        }
        // this.zsxm1List = this.zsxm1ListCls.filter((i) => i.value === this.formData.zsxmDm1);
        // 修改为获取所有匹配当前税率的征税项目
        const matchedZsxm = this.zsxm1SldzList
          .filter((item) => item.sl1 === tempForm.sl1 && (!item.ywlxDm || item.ywxlDm === tempForm.srlxDm))
          .map((item) => item.zsxmDm1);
        this.zsxm1List = this.zsxm1ListCls.filter((i) => matchedZsxm.includes(i.value));
        return;
      }
      // 2.通过税率获取计税方式
      if (tempForm.sl1) {
        // this.jsfsList = this.jsfsListCls.filter((i) => i.value === this.formData.jsfsDm1);
        // 修改为获取所有匹配当前税率的计税方式
        const matchedJsfs = this.jsfsSldzList.filter((item) => item.sl1 === tempForm.sl1).map((item) => item.jsfsDm);
        this.jsfsList = this.jsfsListCls.filter((i) => matchedJsfs.includes(i.value));
        // this.zsxm1List = this.zsxm1ListCls.filter((i) => i.value === this.formData.zsxmDm1);
        // 修改为获取所有匹配当前税率的征税项目
        const matchedZsxm = this.jsfsSldzList.filter((item) => item.sl1 === tempForm.sl1).map((item) => item.zsxmDm1);
        this.zsxm1List = this.zsxm1ListCls.filter((i) => matchedZsxm.includes(i.value));
        return;
      }
      // 3.清空时要恢复计税方式和征税项目的下拉
      if (!tempForm.sl1) {
        this.jsfsList = this.jsfsListCls;
        this.zsxm1List = this.zsxm1ListCls;
      }
    },
    getXzKmmc(kmdm) {
      this.formData.kmmc = this.kmdmList.find((item) => item.value === kmdm)?.label;
      // 禅道 当科目编码为“6051050200”时，征税项目下拉选项为“03 服务”
      if (kmdm === '6051050200') {
        this.formData.zsxmDm1 = '03';
        this.zsxm1List = this.zsxm1ListCls.filter((i) => ['01', '03'].includes(i.value));
      }
      if (kmdm === '60011202') {
        this.$set(this.formData, 'srlxDm', '110');
        this.$set(this.formData, 'sl1', 0.0);
        this.$set(this.formData, 'jsfsDm1', '04');
      }
      if (kmdm === '60010199') {
        this.$set(this.formData, 'srlxDm', '110');
        this.$set(this.formData, 'sl1', 0.0);
        this.$set(this.formData, 'jsfsDm1', '03');
      }
    },
    // 通用国际企业分支---根据收入类型获取科目代码下拉列表
    getKmdmListBySrlx(srlxDm) {
      switch (srlxDm) {
        case '110':
          this.kmdmList = [
            { value: '60010101', label: '主营业务收入_商品销售收入_机床销售收入' },
            { value: '60010103', label: '主营业务收入_商品销售收入_汽车及相关产品收入' },
            { value: '60010105', label: '主营业务收入_商品销售收入_能源产品销售收入' },
            { value: '60010199', label: '主营业务收入_商品销售收入_一般商品销售收入' },
            { value: '600111', label: '主营业务收入_检验检测收入' },
            { value: '60011201', label: '主营业务收入_工程承包收入_国内工程承包收入' },
            { value: '60011202', label: '主营业务收入_工程承包收入_海外工程承包收入' },
            { value: '60011401', label: '主营业务收入_贸易代理业务收入_代理出口业务收入' },
            { value: '60011402', label: '主营业务收入_贸易代理业务收入_代理进口业务收入' },
            { value: '60011403', label: '主营业务收入_贸易代理业务收入_代理内贸业务收入' },
            { value: '60011404', label: '主营业务收入_贸易代理业务收入_代理招标业务收入' },
            { value: '60011599', label: '主营业务收入_技术服务收入_其他技术服务收入' },
            { value: '60011601', label: '主营业务收入_综合服务收入_物流业务收入' },
            { value: '60011602', label: '主营业务收入_综合服务收入_物业管理收入' },
            { value: '60011603', label: '主营业务收入_综合服务收入_广告展览收入' },
            { value: '60011604', label: '主营业务收入_综合服务收入_设计业务收入' },
            { value: '60011605', label: '主营业务收入_综合服务收入_安装业务收入' },
            { value: '60011608', label: '主营业务收入_综合服务收入_咨询业务收入' },
            { value: '60011609', label: '主营业务收入_综合服务收入_维修业务收入' },
            { value: '60011610', label: '主营业务收入_综合服务收入_加工业务收入' },
            { value: '60011612', label: '主营业务收入_综合服务收入_维保业务收入' },
            { value: '60011613', label: '主营业务收入_综合服务收入_会议服务收入' },
            { value: '60011617', label: '主营业务收入_综合服务收入_商务与租赁服务' },
            { value: '60011619', label: '主营业务收入_综合服务收入_代办服务收入' },
            { value: '600118', label: '主营业务收入_咨询费收入' },
          ];
          break;
        case '120':
          this.kmdmList = [
            { value: '605101', label: '其他业务收入_其他业务收入' },
            { value: '61150101', label: '资产处置损益_处置非流动资产利得或损失_固定资产' },
            { value: '611703', label: '其他收益_个税手续费返还' },
            { value: '611702', label: '其他收益_税收返还' },
            { value: '611701', label: '其他收益_政府补助' },
            { value: '630106', label: '营业外收入_违约金收入' },
            { value: '61110102', label: '投资收益_交易性金融资产投资损益_处置收益' },
            { value: '611108', label: '投资收益_资金运作收益' },
            { value: '66030205', label: '财务费用_利息收入_内部资金融通利息' },
          ];
          break;
        case '130':
          this.kmdmList = [{ value: '22210102', label: '应交税费-应交增值税-销项税额' }];
          break;
        default:
          break;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
