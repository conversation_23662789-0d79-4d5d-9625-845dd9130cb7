<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增销项税凭证', '编辑销项税凭证'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmbm">
              <t-select
                v-model="formData.kmbm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmbm)"
              >
                <t-option v-for="item in kmdmList" :value="item.kmbm" :label="item.kmbm" :key="item.kmbm">
                  <div id="kmbm_options">{{ item.kmbm }} | {{ item.kmmc }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsmc">
              <t-input v-model="formData.jsfsmc" placeholder="选择科目代码后带出" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl">
              <t-input v-model="formData.sl" placeholder="选择科目代码后带出" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmmc">
              <t-input v-model="formData.zsxmmc" placeholder="选择科目代码后带出" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" align="left" :onBlur="checkSlBbje" clearable
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { addXxspz, updateXxspz } from '@/pages/index/api/tzzx/zzstz/xxspz.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      sl: [{ required: true, message: '必填', type: 'error' }],
      jsfsmc: [{ required: true, message: '必填', type: 'error' }],
      zsxmmc: [{ required: true, message: '必填', type: 'error' }],
      kmbm: [{ required: true, message: '必填', type: 'error' }],
      se: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      kmdmList: [],
      formData: {
        sszq: '',
        uuid: '',
        gsh: '',
        lrzx: '',
        kjpzbh: '',
        cdefine4: '',
        srlxDm: '',
        sl1: '',
        jsfsDm: '',
        zsxmDm: '',
        kmbm: '',
        kmmc: '',
        se: '',
        tzyy: '',
        ly: '',
      },
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      this.kmdmList = [
        {
          kmbm: '3170000007',
          kmmc: '应交税费-应交增值税-销项税额-专6',
          sl1: 0.06,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '03',
          zsxmMc: '服务',
        },
        {
          kmbm: '3170000010',
          kmmc: '应交税费-应交增值税-销项税额-普6',
          sl1: 0.06,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '03',
          zsxmMc: '服务',
        },
        {
          kmbm: '3170000061',
          kmmc: '应交税费-应交增值税-销项税额-货物-专9',
          sl1: 0.09,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '01',
          zsxmMc: '货物',
        },
        {
          kmbm: '3170000064',
          kmmc: '应交税费-应交增值税-销项税额-服务-专9',
          sl1: 0.09,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '01',
          zsxmMc: '货物',
        },
        {
          kmbm: '3170000051',
          kmmc: '应交税费-应交增值税-销项税额-普9',
          sl1: 0.09,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '01',
          zsxmMc: '货物',
        },
        {
          kmbm: '3170000052',
          kmmc: '应交税费-应交增值税-销项税额-普13',
          sl1: 0.13,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '01',
          zsxmMc: '货物',
        },
        {
          kmbm: '3170000062',
          kmmc: '应交税费-应交增值税-销项税额-货物及委托加工-专13',
          sl1: 0.13,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '01',
          zsxmMc: '货物',
        },
        {
          kmbm: '3170000054',
          kmmc: '应交税费-应交增值税-销项税额-服务及动产租赁-专13',
          sl1: 0.13,
          jsfsDm: '01',
          jsfsMc: '一般计税',
          zsxmDm: '03',
          zsxmMc: '服务',
        },
      ];
      try {
        const res2 = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = res2.data;
        if (this.lrzxList.length === 1) {
          this.formData.lrzx = this.lrzxList[0].value;
        } else {
          this.formData.lrzx = '';
        }
      } catch (e) {
        console.error(e);
        this.formData.lrzx = '';
      }
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.uuid) {
        const formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        this.formData = { ...formDataTemp, sszq: dayjs(String(formDataTemp.sszq)).format('YYYY-MM') };
        this.getXzKmmc(this.formData.kmbm);
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'gsh',
          'lrzx',
          'sl1',
          'jsfsDm',
          'zsxmDm',
          'kmbm',
          'kmmc',
          'kjfpDm',
          'se',
          'tzyy',
          'kjpzbh',
          'cdefine4',
          'ly',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };
        try {
          if (this.visible.pageType) {
            await updateXxspz(params);
          } else {
            await addXxspz(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateSrmx', { flag: true });
          } else {
            this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    checkSlBbje() {
      if (!this.formData.sl1) {
        this.formData.se = 0;
      }
    },
    getXzKmmc(kmbm) {
      // 优化：只查找一次
      const item = this.kmdmList.find((item) => item.kmbm === kmbm);

      // 使用$set方法赋值，确保响应式更新
      this.$set(this.formData, 'kmmc', item?.kmmc);
      this.$set(this.formData, 'sl1', item?.sl1);
      this.$set(this.formData, 'sl', item?.sl1 ? `${(parseFloat(item.sl1) * 100).toFixed(0)}%` : '-'); // 空值时显示横杠
      this.$set(this.formData, 'jsfsDm', item?.jsfsDm);
      this.$set(this.formData, 'jsfsmc', item?.jsfsMc);
      this.$set(this.formData, 'zsxmDm', item?.zsxmDm);
      this.$set(this.formData, 'zsxmmc', item?.zsxmMc);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmbm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
