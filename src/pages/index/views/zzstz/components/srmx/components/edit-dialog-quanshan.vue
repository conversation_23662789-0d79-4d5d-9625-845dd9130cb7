<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增收入明细账', '编辑收入明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select
                v-model="formData.lrzx"
                :placeholder="`请选择${formLabels.lrzx}`"
                clearable
                @change="lrzxChange"
              >
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="收入类型" name="srlxDm">
              <t-select v-model="formData.srlxDm" placeholder="请选择收入类型" clearable @change="srlxChange">
                <t-option
                  v-for="item in srlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.kmdm" :label="item.kmdm" :key="item.kmdm">
                  <div id="kmdm_options">{{ item.kmdm }} | {{ item.kmmc }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input v-model="formData.kmmc" placeholder="选择科目编码后带出" disabled
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择或通过选定科目编码后带出" clearable>
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4" v-if="!this.xgmnsrFlag">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择或通过选定科目编码后带出" clearable>
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm1">
              <t-select v-model="formData.zsxmDm1" placeholder="请选择或通过选定科目编码后带出" clearable>
                <t-option
                  v-for="item in zsxmList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="文本" name="wbzlmc">
              <t-input v-model="formData.wbzlmc" placeholder="请填写文本"></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="参考凭证编号" name="ckpzh">
              <t-input :maxlength="30" v-model="formData.ckpzh" placeholder="请填写参考凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="bbje">
              <gt-input-money v-model="formData.bbje" theme="normal" align="left" clearable :disabled="isCustom"
            /></t-form-item>
          </t-col>
          <t-col :span="4" v-if="this.quanshanQybz === 'zjgqy'">
            <t-form-item label="分支机构识别号" name="fzjgnsrsbh">
              <t-input v-model="formData.fzjgnsrsbh" placeholder="请填写分支机构识别号" readonly
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="4" style="top: 25px">
            <t-checkbox v-model="isCustom" @change="isCustomChange" :disabled="isCustomDisabled">自定义调整</t-checkbox>
          </t-col>
          <t-col :span="4">
            <t-form-item label="销售额" name="xse">
              <gt-input-money
                v-model="formData.xse"
                placeholder="请填写销售额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money
                v-model="formData.se"
                placeholder="请填写税额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { srmxSave, srmxUpdate } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      // ckpzh: [{ required: true, message: '必填', type: 'error' }],
      srlxDm: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm1: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm1: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
      bbje: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isCustom: false,
      isCustomDisabled: false, // 新增字段，控制是否禁用自定义调整
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      srlxList: [],
      slList: [],
      jsfsList: [],
      zsxmList: [],
      srlxKmdmDz: {}, // 收入类型科目代码对照信息:
      kmdmList: [],
      formData: {},
    };
  },
  created() {},
  async mounted() {
    await this.getLrzx();
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    quanshanQybz() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.qybz;
    },
    lrzxnsrdzlist() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.fjgList;
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
  },
  watch: {},
  methods: {
    isCustomChange() {
      if (this.isCustomDisabled) {
        this.isCustom = true; // 如果禁用，不允许取消
        return;
      }
      if (this.isCustom) {
        this.$set(this.formData, 'bbje', 0.0);
        // 2025.7.16 收入明细-新增，当选择自定义调账时，收入类型默认为视同销售。
        this.$set(this.formData, 'srlxDm', '130');
        this.srlxChange(this.formData.srlxDm);
      } else {
        this.$set(this.formData, 'xse', 0.0);
        this.$set(this.formData, 'se', 0.0);
      }
    },
    async init() {
      this.rules = this.baseRules;
      this.srlxList = [
        { value: '110', label: '主营收入' },
        { value: '120', label: '其他收入' },
        { value: '130', label: '视同销项收入' },
        { value: '140', label: '暂估收入' },
      ];
      this.slList = [
        { value: 0, label: '0%' },
        { value: 0.06, label: '6%' },
        { value: 0.09, label: '9%' },
        { value: 0.13, label: '13%' },
      ];
      this.jsfsList = [
        { value: '01', label: '一般计税' },
        { value: '03', label: '免抵退' },
        { value: '04', label: '免税' },
      ];
      this.zsxmList = [
        { value: '01', label: '货物' },
        { value: '03', label: '服务' },
      ];
      this.srlxKmdmDz = {
        110: [
          {
            kmdm: '6000000005',
            kmmc: '主营业务收入-店铺收入-服务-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6000000006',
            kmmc: '主营业务收入-店铺零售-货物-普9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6000000007',
            kmmc: '主营业务收入-店铺零售-货物-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000013',
            kmmc: '主营业务收入-销售货物收入(食材)-货物-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000007',
            kmmc: '主营业务收入-销售货物收入(食材)-货物-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000008',
            kmmc: '主营业务收入-销售货物收入(食材)-货物-免抵退',
            sl1: 0,
            jsfsDm: '03',
            jsfsMc: '免抵退',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000009',
            kmmc: '主营业务收入-销售货物收入(食材)-货物-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000014',
            kmmc: '主营业务收入-销售货物收入(易耗品备品)-货物-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000010',
            kmmc: '主营业务收入-销售货物收入(易耗品备品)-货物-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000011',
            kmmc: '主营业务收入-销售货物收入(易耗品备品)-货物-免抵退',
            sl1: 0,
            jsfsDm: '03',
            jsfsMc: '免抵退',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000016',
            kmmc: '主营业务收入-易耗品备品-货物-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000015',
            kmmc: '主营业务收入-食品零售-货物-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6001000012',
            kmmc: '主营业务收入-食品零售-服务-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6009990044',
            kmmc: '主营业务收入-技术服务-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6009990041',
            kmmc: '主营业务收入-技术服务-免税',
            sl1: 0,
            jsfsDm: '04',
            jsfsMc: '免税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070000019',
            kmmc: '主营业务收入-咨询费收入-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070000010',
            kmmc: '主营业务收入-咨询费收入-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070000018',
            kmmc: '主营业务收入-咨询费收入-免税',
            sl1: 0,
            jsfsDm: '04',
            jsfsMc: '免税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070000020',
            kmmc: '主营业（务）收入-商标使用权-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6004000002',
            kmmc: '主营业（务）收入-经营租赁-服务-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
        ],
        120: [
          {
            kmdm: '6004000003',
            kmmc: '其他业务收入-房屋租赁-服务-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6004000004',
            kmmc: '其他业务收入-房屋租赁-物业等-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6009990045',
            kmmc: '其他业务收入-货物-水费-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6009990042',
            kmmc: '其他业务收入-货物-电费-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6009990043',
            kmmc: '其他业务收入-委托加工-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '6009990046',
            kmmc: '其他业务收入-会员权益-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070000021',
            kmmc: '其他收入-其他-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '6070009998',
            kmmc: '其他收入-其他-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '7010000014',
            kmmc: '其他收入-借款利息收入-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '7100000042',
            kmmc: '营业外收入-其他-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '7100000041',
            kmmc: '营业外收入-其他-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '1580000004',
            kmmc: '固定资产-暂估收到-货物-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
        ],
        130: [
          {
            kmdm: '3170000007',
            kmmc: '应交税费-应交增值税-销项税额-专6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '3170000010',
            kmmc: '应交税费-应交增值税-销项税额-普6',
            sl1: 0.06,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
          {
            kmdm: '3170000061',
            kmmc: '应交税费-应交增值税-销项税额-货物-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '3170000064',
            kmmc: '应交税费-应交增值税-销项税额-服务-专9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '3170000051',
            kmmc: '应交税费-应交增值税-销项税额-普9',
            sl1: 0.09,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '3170000052',
            kmmc: '应交税费-应交增值税-销项税额-普13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '3170000062',
            kmmc: '应交税费-应交增值税-销项税额-货物及委托加工-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '01',
            zsxmMc: '货物',
          },
          {
            kmdm: '3170000054',
            kmmc: '应交税费-应交增值税-销项税额-服务及动产租赁-专13',
            sl1: 0.13,
            jsfsDm: '01',
            jsfsMc: '一般计税',
            zsxmDm: '03',
            zsxmMc: '服务',
          },
        ],
      };
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(
          this.formData,
          'sszq',
          this.xgmnsrFlag ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM') : computeSszq(),
        );
      }
      if (this.visible.row?.srlxDm) {
        console.log('this.visible.row', this.visible.row);
        const formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        this.formData = { ...formDataTemp, sszq: dayjs(String(formDataTemp.sszq)).format('YYYY-MM') };
        if (['110', '120', '130'].includes(formDataTemp.srlxDm)) {
          // 标准收入类型处理
          this.kmdmList = this.srlxKmdmDz[formDataTemp.srlxDm];
          if (formDataTemp.srlxDm === '130') {
            this.isCustom = true;
            this.isCustomDisabled = true;
          }
        } else if (formDataTemp.srlxDm === '140') {
          // 暂估收入特殊处理
          this.kmdmList = [
            { kmdm: '1999991001', kmmc: '冲销上月食材暂估金额' },
            { kmdm: '1999991002', kmmc: '冲销上月易耗品暂估金额' },
            { kmdm: '1999992001', kmmc: '暂估本月食材销售收入' },
            { kmdm: '1999992002', kmmc: '暂估本月易耗品销售收入' },
          ];
          this.isCustom = true;
          this.isCustomDisabled = true;
        }
      }
      if (this.visible.pageType) {
        if (this.visible.row?.se !== 0 || this.visible.row?.xse !== 0) {
          this.isCustom = true;
        }
      }
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      // this.lrzxList = this.lrzxnsrdzlist.map((item) => {
      //   return { label: item.lrzxmc, value: item.lrzxDm };
      // });
      // 移除自动设置逻辑，改为初始化时保留原始值
      if (this.lrzxList.length === 1) {
        this.$set(this.formData, 'lrzx', this.lrzxList[0].value);
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      if (this.isCustom && this.formData.se === 0 && this.formData.xse === 0) {
        this.$message.warning('勾选【自定义调整】按钮后，请录入相应“税额”与“销售额”。');
      } else {
        const val = await this.$refs.forms.validate();
        this.confirmLoading = true;
        if (val === true) {
          const p = {};
          [
            'sszq',
            'uuid',
            'lrzx',
            'kjpzbh',
            'ckpzh',
            'srlxDm',
            'sl1',
            'jsfsDm1',
            'zsxmDm1',
            'kmdm',
            'kmmc',
            'kjfp',
            'bbje',
            'tzyy',
            'se',
            'xse',
            'fzjgnsrsbh',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = {
            ...p,
            oldkjpzbh: this.visible.oldkjpzbh,
            ...this.visible.otherObj,
            sszq: Number(dayjs(p.sszq).format('YYYYMM')),
          };
          try {
            if (this.visible.pageType) {
              await srmxUpdate(params);
            } else {
              await srmxSave(params);
            }
            this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
            if (this.visible.pageType) {
              this.$emit('updateSrmx', { flag: true });
            } else {
              this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    lrzxChange(val) {
      console.log('lrzxChange', val, this.quanshanQybz);
      if (val && this.quanshanQybz === 'zjgqy') {
        console.log('lrzxChange', this.lrzxnsrdzlist);
        const match = this.lrzxnsrdzlist.find((item) => item.lrzxDm === val);
        console.log('lrzxChange', match);
        if (match) {
          this.$set(this.formData, 'fzjgnsrsbh', match.nsrsbh);
        }
      }
    },
    srlxChange(val) {
      try {
        if (['110', '120', '130'].includes(val)) {
          // 标准收入类型处理
          this.kmdmList = this.srlxKmdmDz[val];
          if (val === '130') {
            // 当收入类型为“视同销售”时，本币金额固定为0，置灰不可编辑。自定义调整选择框自动选中，可以填写“销售额”和“销项税额”。
            this.$set(this.formData, 'bbje', 0.0);
            this.isCustom = true;
            this.isCustomDisabled = true; // 禁用自定义调整取消
          } else {
            this.isCustomDisabled = false; // 其他类型允许自由切换
          }
        } else if (val === '140') {
          // 暂估收入特殊处理
          this.kmdmList = [
            { kmdm: '1999991001', kmmc: '冲销上月食材暂估金额' },
            { kmdm: '1999991002', kmmc: '冲销上月易耗品暂估金额' },
            { kmdm: '1999992001', kmmc: '暂估本月食材销售收入' },
            { kmdm: '1999992002', kmmc: '暂估本月易耗品销售收入' },
          ];
          // 当收入类型为“暂估收入”时，本币金额固定为0，置灰不可编辑。自定义调整选择框自动选中，可以填写“销售额”和“销项税额”。
          this.$set(this.formData, 'bbje', 0.0);
          this.isCustom = true;
          this.isCustomDisabled = true;
        }
        // 延迟执行清空操作确保DOM更新
        this.$nextTick(() => {
          // 使用Vue.set保证响应式更新
          this.$set(this.formData, 'kmdm', '');
          this.$set(this.formData, 'kmmc', '');
        });
      } catch (e) {
        console.error('收入类型切换异常:', e);
        this.kmdmList = [];
      }
    },
    getXzKmmc(kmdm) {
      const selectedItem = this.kmdmList.find((item) => item.kmdm === kmdm);
      if (selectedItem) {
        // 公共字段设置
        // 使用Vue.set强制更新响应式数据
        this.$set(this.formData, 'kmdm', selectedItem.kmdm);
        this.$set(this.formData, 'kmmc', selectedItem.kmmc);

        // 根据收入类型处理其他字段
        if (['110', '120', '130'].includes(this.formData.srlxDm)) {
          // 标准类型自动带出可修改字段
          this.$set(this.formData, 'sl1', selectedItem.sl1 ?? null);
          this.$set(this.formData, 'jsfsDm1', selectedItem.jsfsDm || null);
          this.$set(this.formData, 'zsxmDm1', selectedItem.zsxmDm || null);
        } else if (this.formData.srlxDm === '140') {
          // 类型140时不自动设置税率等字段，保留用户输入
          this.$set(this.formData, 'sl1', null);
          this.$set(this.formData, 'jsfsDm1', null);
          this.$set(this.formData, 'zsxmDm1', null);
        }
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
