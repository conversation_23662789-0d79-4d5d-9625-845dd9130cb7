<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增收入明细账', '编辑收入明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择计税方式" clearable @change="onJsfsChange">
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="收入类型" name="srlxDm">
              <t-select v-model="formData.srlxDm" placeholder="请选择收入类型" clearable @change="onSrlxChange">
                <t-option
                  v-for="item in srlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable @change="onSlChange">
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm1">
              <t-select v-model="formData.zsxmDm1" placeholder="请选择征税项目" clearable @change="onZsxmChange">
                <t-option
                  v-for="item in zsxm1List"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="参考凭证编号" name="ckpzh">
              <t-input :maxlength="30" v-model="formData.ckpzh" placeholder="请填写参考凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="bbje">
              <gt-input-money v-model="formData.bbje" theme="normal" align="left" clearable :disabled="isCustom"
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="4" style="top: 25px">
            <t-checkbox v-model="isCustom" @change="isCustomChange">自定义调整</t-checkbox>
          </t-col>
          <t-col :span="4">
            <t-form-item label="销售额" name="xse">
              <gt-input-money
                v-model="formData.xse"
                placeholder="请填写销售额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money
                v-model="formData.se"
                placeholder="请填写税额"
                theme="normal"
                align="left"
                clearable
                :disabled="!isCustom"
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { srmxSave, srmxUpdate } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { kmdmListSxyd } from '../config.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      // ckpzh: [{ required: true, message: '必填', type: 'error' }],
      srlxDm: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm1: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm1: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
      bbje: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isCustom: false,
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      srlxList: [],
      zsxm1List: [],
      slList: [],
      kmdmList: [],
      kmdmListSxyd,
      // 更新后的联动数据结构
      taxConfig: {
        zjg: {
          // 总机构
          month: {
            // 按月
            '01': {
              // 计税方法
              110: {
                // 收入类型
                0.13: {
                  // 税率
                  '01': [
                    // 征税项目
                    '5110020701.070117.sx',
                    '5110020701.070131.sx',
                    '5110020701.070121.sx',
                  ],
                  '03': ['5110020701.070130.sx'],
                },
                0.09: {
                  '01': ['5110020701.070129.sx'],
                  '03': [
                    '5110020701.070125.sx',
                    '5110020701.070126.sx',
                    '5110020701.070127.sx',
                    '5110020701.070128.sx',
                  ],
                },
              },
              130: {
                0.13: {
                  '01': ['5110020702.070117.sx'],
                },
                0.09: {
                  '01': ['5110020702.070129.sx'],
                },
              },
            },
            '02': {
              120: {
                0.05: {
                  '03': ['5110080100.070102.sx'],
                },
                0.03: {
                  '01': ['5110080100.070101.sx'],
                },
              },
            },
          },
          quarter: {
            // 按季
            '01': {
              // 计税方法
              110: {
                // 收入类型
                0.09: {
                  // 税率
                  '03': [
                    // 征税项目
                    '5110020701.070124.sx',
                  ],
                },
                0.06: {
                  '03': ['5110020701.070103.sx'],
                },
              },
              130: {
                0.09: {
                  '03': ['5110020702.070124.sx'],
                },
                0.06: {
                  '03': ['5110020702.070103.sx'],
                },
              },
            },
          },
        },
        fjg: {
          // 分支机构
          month: {
            // 按月
            '02': {
              110: {
                0.0: {
                  '03': ['5110000000.0.sx'],
                },
              },
              120: {
                0.05: {
                  '05': ['5171110000.0.sx'],
                },
              },
            },
          },
        },
      },
      formDataTemp: {},
      formData: {},
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    shanxiyidongZfjg() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.fzjgbz;
    },

    jsfsList() {
      // 获取当前机构类型和期间类型下的配置
      const periodConfig = this.taxConfig[this.shanxiyidongZfjg]?.[this.getMonthOrQuarterFetch];
      if (!periodConfig) return [];

      // 从配置中提取所有计税方式代码
      const jsfsCodes = Object.keys(periodConfig);

      // 映射为选项格式
      return jsfsCodes.map((code) => {
        // 使用预定义的标签映射
        const labelMap = {
          '01': '一般计税方式',
          '02': '简易计税方式',
        };

        return {
          value: code,
          label: labelMap[code] || `计税方式 ${code}`,
        };
      });
    },
    getMonthOrQuarterFetch() {
      // 提取月份部分并转换为整数
      const month = parseInt(this.formData.sszq.split('-')[1], 10);

      // 判断是否为季度末月份（3/6/9/12）
      if ([3, 6, 9, 12].includes(month)) {
        return 'quarter';
      }
      return 'month';
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
  },
  watch: {},
  methods: {
    isCustomChange() {
      if (this.isCustom) {
        this.$set(this.formData, 'bbje', 0.0);
        // 2025.7.16 收入明细-新增，当选择自定义调账时，收入类型默认为视同销售。
        this.$set(this.formData, 'srlxDm', '130');
        this.onSrlxChange(this.formData.srlxDm);
      } else {
        this.$set(this.formData, 'xse', 0.0);
        this.$set(this.formData, 'se', 0.0);
      }
    },
    init() {
      this.rules = this.baseRules;
      console.log(this.visible);
      this.lrzxList = this.visible.lrzxList;
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.jsfsDm1) {
        this.formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        // 1. 初始化下拉列表信息
        this.initSelectListByEditData(JSON.parse(JSON.stringify(this.visible.row)));
        // 2. 设置表单基础数据
        const formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        this.formData = { ...formDataTemp, sszq: dayjs(String(formDataTemp.sszq)).format('YYYY-MM') };
      }

      if (this.visible.pageType) {
        if (this.visible.row?.se !== 0 || this.visible.row?.xse !== 0) {
          this.isCustom = true;
        }
      }
    },
    // 用于编辑进入时根据带入数据初始化页面上的下拉列表信息
    initSelectListByEditData(value) {
      const config = this.taxConfig[this.shanxiyidongZfjg][this.getMonthOrQuarterFetch];
      const srlxLabels = {
        110: '主营收入',
        130: '视同销售收入',
        120: '其他收入',
      };
      this.srlxList = Object.keys(config[value.jsfsDm1] || {}).map((v) => ({
        value: v,
        label: srlxLabels[v] || v,
      }));
      const currentConfig = config[value.jsfsDm1] || {};
      const slOptions = Object.keys(currentConfig[value.srlxDm] || {});
      this.slList = slOptions.map((v) => {
        const rate = parseFloat(v);
        return {
          value: v,
          label: `${Math.round(rate * 100)}%`, // 使用Math.round确保整数百分比
        };
      });
      const zsxmOptions = Object.keys(currentConfig[value.srlxDm]?.[value.sl1] || {});
      this.zsxm1List = zsxmOptions.map((v) => ({
        value: v,
        label: v === '01' ? '货物' : '服务',
      }));
      const validKmdm = currentConfig[value.srlxDm]?.[value.sl1]?.[value.zsxmDm1] || [];
      this.kmdmList = this.kmdmListSxyd.filter((item) => validKmdm.includes(item.value));
    },
    // 计税方式变化时触发
    updateLinkage(field, value) {
      const config = this.taxConfig[this.shanxiyidongZfjg][this.getMonthOrQuarterFetch];

      switch (field) {
        case 'jsfsDm1': {
          const srlxLabels = {
            110: '主营收入',
            130: '视同销售收入',
            120: '其他收入',
          };
          this.srlxList = Object.keys(config[value] || {}).map((v) => ({
            value: v,
            label: srlxLabels[v] || v,
          }));
          this.$set(this.formData, 'srlxDm', this.srlxList[0].value);
          this.onSrlxChange(this.formData.srlxDm);
          // this.$set(this.formData, 'sl1', '');
          // this.$set(this.formData, 'zsxmDm1', '');
          // this.$set(this.formData, 'kmdm', '');
          break;
        }

        case 'srlxDm': {
          const currentConfig = config[this.formData.jsfsDm1] || {};
          const slOptions = Object.keys(currentConfig[value] || {});
          this.slList = slOptions.map((v) => {
            const rate = parseFloat(v);
            return {
              value: v,
              label: `${Math.round(rate * 100)}%`, // 使用Math.round确保整数百分比
            };
          });
          this.$set(this.formData, 'sl1', this.slList[0].value);
          this.onSlChange(this.formData.sl1);
          // this.$set(this.formData, 'zsxmDm1', '');
          // this.$set(this.formData, 'kmdm', '');
          break;
        }

        case 'sl1': {
          const currentConfig = config[this.formData.jsfsDm1] || {};
          const zsxmOptions = Object.keys(currentConfig[this.formData.srlxDm]?.[value] || {});
          this.zsxm1List = zsxmOptions.map((v) => ({
            value: v,
            label: v === '01' ? '货物' : '服务',
          }));
          this.$set(this.formData, 'zsxmDm1', this.zsxm1List[0].value);
          this.onZsxmChange(this.formData.zsxmDm1);
          // this.$set(this.formData, 'kmdm', '');
          break;
        }

        case 'zsxmDm1': {
          const currentConfig = config[this.formData.jsfsDm1] || {};
          const validKmdm = currentConfig[this.formData.srlxDm]?.[this.formData.sl1]?.[value] || [];
          this.kmdmList = this.kmdmListSxyd.filter((item) => validKmdm.includes(item.value));
          this.$set(this.formData, 'kmdm', this.kmdmList[0].value);
          this.getXzKmmc(this.formData.kmdm);
          break;
        }

        default:
          break;
      }
    },

    // 修改现有方法调用新方法
    onJsfsChange(jsfs) {
      this.updateLinkage('jsfsDm1', jsfs);
    },

    onSrlxChange(srlx) {
      this.updateLinkage('srlxDm', srlx);
    },

    onSlChange(sl) {
      this.updateLinkage('sl1', sl);
    },

    onZsxmChange(zsxm) {
      this.updateLinkage('zsxmDm1', zsxm);
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      if (this.isCustom && this.formData.se === 0 && this.formData.xse === 0) {
        this.$message.warning('勾选【自定义调整】按钮后，请录入相应“税额”与“销售额”。');
      } else {
        const val = await this.$refs.forms.validate();
        this.confirmLoading = true;
        if (val === true) {
          const p = {};
          [
            'sszq',
            'uuid',
            'lrzx',
            'kjpzbh',
            'ckpzh',
            'srlxDm',
            'sl1',
            'jsfsDm1',
            'zsxmDm1',
            'kmdm',
            'kmmc',
            'bbje',
            'tzyy',
            'se',
            'xse',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = {
            ...p,
            oldkjpzbh: this.visible.oldkjpzbh,
            ...this.visible.otherObj,
            sszq: Number(dayjs(p.sszq).format('YYYYMM')),
          };
          try {
            if (this.visible.pageType) {
              await srmxUpdate(params);
            } else {
              await srmxSave(params);
            }
            this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
            if (this.visible.pageType) {
              this.$emit('updateSrmx', { flag: true });
            } else {
              this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.confirmLoading = false;
        }
      }
    },
    getXzKmmc(kmdm) {
      this.$set(this.formData, 'kmmc', this.kmdmListSxyd.find((item) => item.value === kmdm)?.label);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
