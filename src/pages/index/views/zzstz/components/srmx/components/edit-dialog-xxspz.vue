<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增销项税凭证', '编辑销项税凭证'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable @change="getZsxmBysl">
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4" v-if="!this.xgmnsrFlag">
            <t-form-item label="计税方式" name="jsfsDm">
              <t-select v-model="formData.jsfsDm" placeholder="请选择计税方式" clearable>
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm">
              <t-select v-model="formData.zsxmDm" placeholder="请选择征税项目" clearable>
                <t-option
                  v-for="item in zsxmList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmbm">
              <t-select
                v-model="formData.kmbm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmbm)"
              >
                <t-option v-for="item in kmbmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmbm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" align="left" :onBlur="checkSlBbje" clearable
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getAll, getLrzx, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { addXxspz, updateXxspz } from '@/pages/index/api/tzzx/zzstz/xxspz.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm: [{ required: true, message: '必填', type: 'error' }],
      kmbm: [{ required: true, message: '必填', type: 'error' }],
      se: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      jsfsList: [],
      jsfsListCls: [],
      zsxmList: [],
      zsxmListCls: [],
      slList: [],
      kmbmList: [],
      jsfsSldzList: [],
      kjfpTempData: '',
      formData: {
        sszq: '',
        uuid: '',
        gsh: '',
        lrzx: '',
        kjpzbh: '',
        cdefine4: '',
        srlxDm: '',
        sl1: '',
        jsfsDm: '',
        zsxmDm: '',
        kmbm: '',
        kmmc: '',
        kjfpDm: 'B00',
        se: '',
        tzyy: '',
        ly: '',
      },
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    tongyongguojiFlag() {
      // 通用国际标志
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
    // 新增计算属性获取科目编码列表-----此处针对通用国际做特殊处理，后续需要其他稳定方案避免内容写死
    kmbmOptions() {
      if (this.tongyongguojiFlag) {
        return [{ value: '22210102', label: '应交税费-应交增值税-销项税额' }];
      }
      return this.xgmnsrFlag
        ? [
            { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
            { value: '2221080000', label: '应交税费-增值税简易计税' },
            { value: '2221010000', label: '应交税费-应交增值税' },
          ]
        : [
            { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
            { value: '2221080000', label: '应交税费-增值税简易计税' },
          ];
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      if (this.tongyongguojiFlag) {
        const { data } = await getAll();
        this.slList = data.slList.map((value) => ({
          value,
          label: `${(value * 100).toFixed(0)}%`,
        }));
      } else {
        const res1 = await getJsfssldzb();
        this.jsfsSldzList = res1.data;
        this.slList = this.xgmnsrFlag
          ? this.uniqueObjects(
              res1.data
                .filter((i) => [0.03, 0.05].includes(i.sl1))
                .map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })),
            )
          : this.uniqueObjects(res1.data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      }
      // const index0 = this.slList.findIndex((t) => t.value === 0);
      // index0 > 1 ? this.slList.splice(index0, 1) : null;
      const res2 = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = res2.data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
      this.jsfsList = this.visible.jsfsList;
      this.jsfsListCls = this.visible.jsfsList;
      this.zsxmList = this.xgmnsrFlag ? this.visible.zsxmList.slice(0, 3) : this.visible.zsxmList;
      this.zsxmListCls = this.xgmnsrFlag ? this.visible.zsxmList.slice(0, 3) : this.visible.zsxmList;
      // 修改科目编码列表为动态获取
      this.kmbmList = this.kmbmOptions;

      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(
          this.formData,
          'sszq',
          this.xgmnsrFlag ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM') : computeSszq(),
        );
      }
      if (this.visible.row?.uuid) {
        const formDataTemp = JSON.parse(JSON.stringify(this.visible.row));
        this.formData = { ...formDataTemp, sszq: dayjs(String(formDataTemp.sszq)).format('YYYY-MM') };
      }
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'gsh',
          'lrzx',
          'sl1',
          'jsfsDm',
          'zsxmDm',
          'kmbm',
          'kmmc',
          'kjfpDm',
          'se',
          'tzyy',
          'kjpzbh',
          'cdefine4',
          'ly',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
          kjfpDm: this.tongyongguojiFlag ? '销项税额凭证(开具增值税发票)' : p.kjfpDm,
        };
        try {
          if (this.visible.pageType) {
            await updateXxspz(params);
          } else {
            await addXxspz(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateSrmx', { flag: true });
          } else {
            this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    getZsxmBysl() {
      // 通用国际环境暂时不设置联动关系
      if (this.tongyongguojiFlag) {
        return;
      }
      // 根据收入类型和税率确定计税方式和征税项目代码，由于无法明确用户点击顺序，这里需要判断对应值获取情况
      if (this.formData.sl1) {
        this.jsfsList = this.jsfsListCls;
        this.zsxmList = this.zsxmListCls;
        // 1.通过税率获取计税方式
        const tempArr = this.jsfsSldzList.filter((item) => item.sl1 === this.formData.sl1).map((item) => item.jsfsDm);
        this.jsfsList = this.jsfsListCls.filter((i) => tempArr.includes(i.value));
        // 2.通过税率获取征税项目
        if (this.formData.sl1 === 0.1 || this.formData.sl1 === 0.09) {
          const tempArr = this.jsfsSldzList
            .filter((item) => item.sl1 === this.formData.sl1)
            .map((item) => item.zsxmDm1);
          this.zsxmList = this.zsxmListCls.filter((i) => tempArr.includes(i.value));
        }
        if (this.formData.sl1 === 0.06) {
          // 禅道813 当税率为“6%”时，征税项目下拉选项为“03服务”和“04 无形资产”
          this.zsxmList = this.zsxmListCls.filter((i) => ['03', '04'].includes(i.value));
        }
        this.formData.jsfsDm = this.jsfsList[0].value;
        this.formData.zsxmDm = this.zsxmList[0].value;
      }
      // 3.清空时要恢复计税方式和征税项目的下拉
      if (!this.formData.sl1) {
        this.jsfsList = this.jsfsListCls;
        this.zsxmList = this.zsxmListCls;
        // 清空或税率为0时本币金额也置为0
        this.formData.se = 0;
      }
    },
    checkSlBbje() {
      if (!this.formData.sl1) {
        this.formData.se = 0;
      }
    },
    getXzKmmc(kmbm) {
      this.formData.kmmc = this.kmbmList.find((item) => item.value === kmbm)?.label;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmbm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
