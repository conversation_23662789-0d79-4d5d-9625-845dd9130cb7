<!-- * @Descripttion: 台账-增值税一般纳税人-减免税总账 -->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        class="znsbHeadqueryDiv"
        ref="queryControl"
        :config="querySearchConfigFinal"
        :formRules="querySearchConfigRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
      >
        <template #t1><span></span></template>
      </search-control-panel>
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-button
            variant="outline"
            theme="primary"
            @click="$router.push('/lsgl')"
            v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="myTable"
          row-key="uuid"
          hover
          :height="dynamicHeight"
          :data="tableData"
          :columns="tableColumns"
          :pagination="pagination"
          :loading="tableLoading"
          @page-change="pageChange"
          :rowClassName="rowClassName"
          @row-click="(e) => (activeRule = e)"
        >
          <!-- :foot-data="footData" -->
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #mzzzsxmxse="{ row }">
            <span v-if="row.mzzzsxmxse && row.swsxmc === '出口免税'" class="specText" @click="openSrPage(row)">{{
              numberToPrice(row.mzzzsxmxse)
            }}</span>
            <span v-if="row.mzzzsxmxse && row.swsxmc !== '出口免税'" class="specText" @click="openPage(row)">{{
              numberToPrice(row.mzzzsxmxse)
            }}</span>
            <span v-if="!row.mzzzsxmxse">{{ '-' }}</span>
          </template>
          <template #bqfse="{ row }">
            <div
              v-if="row.cybz === 'error'"
              style="display: flex; justify-content: space-between; align-items: center; float: right"
            >
              <t-tooltip :showArrow="false" :destroyOnClose="false">
                <span class="specText" @click="openPage(row)">{{ numberToPrice(row.bqfse) }}</span>
                <template #content>
                  <span>{{ row.cymsg }}</span>
                  <span @click="openJxfpmxView(row)" style="margin-left: 10px; color: lightblue; cursor: pointer">
                    {{ row.zxmsg }}
                  </span>
                </template>
                <ErrorCircleFilledIcon :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }" />
              </t-tooltip>
            </div>
            <div v-else>
              <span v-if="row.bqfse" class="specText" @click="openPage(row)">{{ numberToPrice(row.bqfse) }}</span>
              <span v-else>{{ '-' }}</span>
            </div>
          </template>
        </t-table>
      </div>
      <ValidateDialog
        :validate-rules="validateRules"
        :handleMsg="true"
        :extraHandleMsg="true"
        @ruleClick="ruleClick"
        @toggle="toggle"
        @handleMsg="(item) => openJxfpmxView(item)"
        @extraHandleMsg="
          (item) => {
            this.hlVisible = {
              ...item,
              showHlxmFlag: false, // 不显示忽略项目
            };

            // 使用深拷贝确保响应式更新
            this.hlformData = {
              hlsy: item.hlsy || '',
            };
          }
        "
      />
      <!-- <div v-show="hlVisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="是否确定忽略该疑点差异信息"
          confirmBtn="是"
          cancelBtn="否"
          :onConfirm="qrhlcyxx"
          :onClose="closeBox"
        >
        </t-dialog>
      </div> -->
      <IgnoreDialog :visible="hlVisible" :formData="hlformData" @confirm="qrhlcyxx($event)" @close="closeBox" />
    </div>
  </div>
</template>
<script>
import { initJmszzQuery } from '@/pages/index/api/tzzx/zzstz/jmszz.js';
import { cybdhl } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import dayjs from 'dayjs';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import IgnoreDialog from '@/pages/index/components/IgnoreDialog/index.vue';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import { ErrorCircleFilledIcon, ChartIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { computeSszq, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { querySearchConfig, mainColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    ValidateDialog,
    SkeletonFrame,
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    IgnoreDialog,
    ErrorCircleFilledIcon,
    ChartIcon,
  },
  data() {
    return {
      loading: true,
      isProduct: this.$store.state.isProduct.envValue,
      validateDialogToggle: true,
      userInfo: {},
      formData: {},
      mainColumns,
      querySearchConfig,
      querySearchConfigOneRules,
      tableLoading: false,
      hlVisible: false,
      tableData: [],
      validateRules: [],
      activeRule: '',
      // footData: [],
      hlformData: {
        hlsy: '',
      },
      pagination: { current: 1, pageSize: 10, total: 0 },
      columnsLoading: false, // 列配置加载状态
      remoteColumns: [], // 动态列配置
    };
  },
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
  },
  computed: {
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.mainColumns; // 兼容初始化状态
      }
      return this.remoteColumns;
    },
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    zszfjgFlag() {
      return (
        this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000003' &&
        this.$store.state.zdmczh.companyDifferentiationConfig.fzjgbz === 'zjg'
      );
    },
    querySearchConfigFinal() {
      let querySearchConfigTemp = this.querySearchConfig;
      if (!this.zszfjgFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'hzfzjg');
      }
      if (this.xgmnsrFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'jsfsDm');
      }
      return querySearchConfigTemp;
    },
    querySearchConfigRules() {
      const querySearchConfigOneRulesTemp = JSON.parse(JSON.stringify(this.querySearchConfigOneRules));
      if (!this.zszfjgFlag) {
        delete querySearchConfigOneRulesTemp.hzfzjg;
        delete this.formData.hzfzjg;
      }
      return querySearchConfigOneRulesTemp;
    },
    ssyfqC() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    ssyfzC() {
      return dayjs(this.formData.ssyfz).format('YYYYMM');
    },
    dynamicHeight() {
      let height = '100%';
      if (!this.validateDialogToggle) {
        return height;
      }
      switch (this.validateRules.length) {
        case 0:
          height = '100%';
          break;
        case 1:
          height = '85%';
          break;
        case 2:
          height = '80%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '70%';
          break;
      }
      return height;
    },
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jmsZzTz';
    },
    exportFileName() {
      return '减免税总账台账';
    },
    tzQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...this.formData,
        ssyfq: this.ssyfqC,
        ssyfz: this.ssyfzC,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.querySearchConfig[1].value = computeSszq();
    this.formData.ssyfq = computeSszq();
    this.formData.ssyfz = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.querySearchConfig[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
        this.formData.ssyfq = dayjs(this.$route.query.skssqq).format('YYYYMM');
        this.formData.ssyfz = dayjs(this.$route.query.skssqz).format('YYYYMM');
      }
    }
  },
  mounted() {
    this.fetchTableColumns(); // 初始化表头配置
    this.initQueryConditions();
    this.getQueryParamsList();
  },
  methods: {
    // 动态配置表头信息
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 处理列配置
        this.remoteColumns = this.mainColumns
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        // 失败时回退到默认配置
        this.remoteColumns = this.mainColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    openJxfpmxView(item) {
      console.log('openJxfpmxView', item);
      this.$emit('openPage', {
        from: 'jmszz',
        data: {
          sszq: String(item.sszq),
          type: 1,
          kprqq: dayjs(String(item.sszq)).startOf('month').format('YYYY-MM-DD'),
          kprqz: dayjs(String(item.sszq)).endOf('month').format('YYYY-MM-DD'),
        },
        type: 'jxfpmx',
      });
    },
    async qrhlcyxx(submitData) {
      // 修改方法接收处理后的参数
      console.log('忽略成功');
      try {
        const params = {
          uuid: this.tableData.find((t) => t.index === this.hlVisible.index)?.uuid,
          djxh: this.hlVisible.djxh,
          nsrsbh: this.hlVisible.nsrsbh,
          nsrmc: this.hlVisible.nsrmc,
          sszq: this.hlVisible.sszq,
          ywlxDm: '500',
          hlsy: submitData.hlsy, // 从参数获取处理后的数据
        };
        const { msg } = await cybdhl(params);
        console.log('cybdhl-msg', msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query({ flag: true });
        this.hlVisible = false;
      }
    },
    closeBox() {
      this.hlVisible = false;
    },
    toggle(val) {
      this.validateDialogToggle = val;
    },
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    getQueryParamsList() {},
    initQueryConditions() {
      // 确保不重置hzfzjg参数
      if (this.zszfjgFlag) {
        // 保留原有hzfzjg值
        const hzfzjgIndex = this.querySearchConfig.findIndex((item) => item.key === 'hzfzjg');
        if (hzfzjgIndex !== -1) {
          this.$set(this.querySearchConfig[hzfzjgIndex], 'value', '1');
          this.$set(this.formData, 'hzfzjg', '1');
        }
      } else {
        delete this.formData.hzfzjg;
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        // 确保p参数中的hzfzjg也被正确处理
        if (!this.zszfjgFlag && params.hzfzjg) {
          delete params.hzfzjg;
        }
        if (params.ssyfq) {
          params.ssyfq = dayjs(params.ssyfq).format('YYYYMM');
        }
        if (params.ssyfz) {
          params.ssyfz = dayjs(params.ssyfz).format('YYYYMM');
        }
        this.$refs.queryControl.setParams(p);
      } else {
        const commonParams = {
          ssyfq: this.ssyfqC,
          ssyfz: this.ssyfzC,
          ...params,
        };

        params =
          this.zszfjgFlag && this.formData.hzfzjg ? { hzfzjg: this.formData.hzfzjg, ...commonParams } : commonParams;
      }
      try {
        const { data } = await initJmszzQuery(params);
        this.tableData = data?.records.dataList || [];
        this.pagination.total = data?.pageTotal;
        // this.footData = this.tableData.length > 1 ? [data.records?.hj] || [] : [];
        this.validateRules = [];
        // 由于申报状态存在变更，所以每次查询都要重新获取
        this.$store.commit('sbzt/setSbztCacheInfo', {});
        this.tableData.forEach(async (d, index) => {
          this.$set(d, 'index', index);
          if (d.cymsg) {
            let l = {
              content: d.cymsg,
              handleMsg: d.zxmsg,
              index,
              type: d.cybz,
              djxh: d.djxh,
              nsrsbh: d.nsrsbh,
              nsrmc: d.nsrmc,
              sszq: d.sszq,
            };
            if (d.hlzt) {
              if (await this.getSbztBySsq(d.sszq)) {
                l = {
                  ...l,
                  finalContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。`,
                };
              } else {
                l = {
                  ...l,
                  hlsy: d.hlzt.hlsy,
                  extraContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。若有修改，请点击此“`,
                  extraHandleMsg: '编辑',
                  finalContent: '”。',
                };
              }
            } else {
              l = {
                ...l,
                extraContent: '，若该差异可忽略，请点击此“',
                extraHandleMsg: '忽略',
                finalContent: '”。',
              };
            }
            this.validateRules.push(l);
          }
        });
      } catch (e) {
        this.tableData = [];
        this.validateRules = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    getSbztBySsq,
    openPage(row) {
      this.$emit('openPage', {
        from: 'jmszz',
        data: {
          gsh: row.gsh,
          nsrmc: row.nsrmc,
          nsrsbh: row.nsrsbh,
          djxh: row.djxh,
          lrzx: row.lrzx,
          ssjmxzDm: row.ssjmxzDm,
          sszq: row.sszq,
          zzscbz: 'Y',
        },
        type: 'jmsmxz',
      });
    },
    openSrPage(row) {
      let params = {};
      if (this.shanxiyidongFlag) {
        params = {
          from: 'jmszz',
          data: {
            gsh: row.gsh,
            nsrmc: row.nsrmc,
            nsrsbh: row.nsrsbh,
            djxh: row.djxh,
            lrzx: row.lrzx,
            jsfsDm1: '04',
            zsxmDm1: '01,02',
            sszq: String(row.sszq),
            sl1: '0',
            kmdm: '5111100301.0,5111100302.0,5111100303.0,5111100304.0',
          },
          type: 'srmx',
        };
      } else if (this.quanshanFlag) {
        params = {
          from: 'jmszz',
          data: {
            gsh: row.gsh,
            nsrmc: row.nsrmc,
            nsrsbh: row.nsrsbh,
            djxh: row.djxh,
            lrzx: row.lrzx,
            jsfsDm1: '04',
            zsxmDm1: '01,02',
            sszq: String(row.sszq),
            sl1: '0',
            kmdm: '6009990041,6070000018',
          },
          type: 'srmx',
        };
      } else {
        params = {
          from: 'jmszz',
          data: {
            gsh: row.gsh,
            nsrmc: row.nsrmc,
            nsrsbh: row.nsrsbh,
            djxh: row.djxh,
            lrzx: row.lrzx,
            jsfsDm1: '04',
            zsxmDm1: '01,02',
            sszq: String(row.sszq),
            sl1: '0',
            kmdm: '6001010100,6001010200,6041000000,6051010100,6051020100,6051030000,6051030100,6051010200,6051020200',
          },
          type: 'srmx',
        };
      }

      this.$emit('openPage', params);
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.myTable.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    rowClassName({ rowIndex }) {
      if (rowIndex === this.activeRule.index) return 'active-row';
      return '';
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.filter-btns {
  float: right;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
