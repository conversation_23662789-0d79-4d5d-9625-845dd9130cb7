import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigOneRules = {
  ssyfq: [{ required: true, message: '必填项', type: 'error' }],
  ssyfz: [{ required: true, message: '必填项', type: 'error' }],
  hzfzjg: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '汇总分支机构',
    key: 'hzfzjg',
    type: 'select',
    multiple: false,
    selectList: [
      { label: '是', value: '1' },
      { label: '否', value: '2' },
    ],
    value: 'Y',
    clearable: false,
  },
  {
    label: '',
    key: 't1',
  },
];
export const mainColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
  },
  {
    align: 'center',
    width: 100,
    colKey: 'sszq',
    title: '所属月份',
  },
  {
    align: 'left',
    width: 110,
    colKey: 'gsh',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
  },
  {
    align: 'left',
    width: 120,
    colKey: 'jmzlxmc',
    title: '减免税类型',
  },
  {
    align: 'left',
    width: 130,
    colKey: 'swsxDm',
    title: '优惠事项代码',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'swsxmc',
    title: '优惠事项名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 160,
    colKey: 'bqfse',
    title: '本期发生额',
  },
  {
    align: 'right',
    width: 160,
    colKey: 'mzzzsxmxse',
    title: '免征增值税项目销售额',
    ellipsisTitle: true,
  },
  {
    align: 'right',
    width: 160,
    colKey: 'mzxsekcxmbqsjkcje',
    title: '免税销售额扣除项目本期实际扣除金额',
    ellipsisTitle: true,
  },
];
