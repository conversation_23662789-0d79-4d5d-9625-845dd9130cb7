<!--
 * @Descripttion: 台账-增值税一般纳税人-出口货物转内销证明明细账 dkdjjspzmx
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      class="znsbHeadqueryDiv"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <t-button
          variant="outline"
          theme="primary"
          @click="$router.push('/lsgl')"
          v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        row-key="uuid"
        height="100%"
        hover
        :data="tableData"
        :columns="mainColumns"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #hxytDm="{ row }">
          {{
            (hxytList.find((d) => d.value === row.hxytDm) && hxytList.find((d) => d.value === row.hxytDm).label) || ''
          }}
        </template>
        <template #bdkyyDm="{ row }">
          {{
            (bdkyyList.find((d) => d.value === row.bdkyyDm) && bdkyyList.find((d) => d.value === row.bdkyyDm).label) ||
            ''
          }}
        </template>
        <template #je="{ row }">
          <span>{{ numberToPrice(row.je) }}</span>
        </template>
        <template #se="{ row }">
          <span>{{ numberToPrice(row.se) }}</span>
        </template>
        <template #yxdkse="{ row }">
          <span>{{ numberToPrice(row.yxdkse) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import { initCkhwznxzmMxQuery, initCkhwznxzmMxQueryHj } from '@/pages/index/api/tzzx/zzstz/ckhwznxzm.js';
import dayjs from 'dayjs';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { ChartIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { querySearchConfig, mainColumns, querySearchConfigOneRules } from './config.js';
import { hxytList, bdkyyList } from '../../config';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.bdkyyList = bdkyyList;
    this.hxytList = hxytList;
    this.mainColumns = mainColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    return {
      userInfo: {},
      formData: {},
      isProduct: this.$store.state.isProduct.envValue,
      querySearchConfig,
      tableLoading: false,
      tableData: [],
      footData: [],
      spdmList: [], // 商品代码下拉
      pagination: { current: 1, pageSize: 10, total: 0 },
      fromName: false,
    };
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'ckhwznxzmMxTz';
    },
    exportFileName() {
      return '出口货物转内销证明明细台账';
    },
    tzQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...this.formData,
        sszq: this.sszqC,
        hxytDm: multiSelectHandle(this.formData.hxytDm),
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
    this.init();
  },
  mounted() {},
  methods: {
    async init() {
      this.querySearchConfig[1].selectList = this.hxytList;
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        if (p.sszq) p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
        this.$refs.queryControl.setParams(p);
      } else {
        params = {
          ...this.formData,
          sszq: this.sszqC,
          hxytDm: multiSelectHandle(this.formData.hxytDm),
          ...params,
        };
      }
      try {
        const { data } = await initCkhwznxzmMxQuery(params);
        this.tableData = data.records || [];
        this.pagination.total = data.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await initCkhwznxzmMxQueryHj(params);
          this.footData =
            [
              {
                je: numberToPrice(data?.je),
                se: numberToPrice(data?.se),
                yxdkse: numberToPrice(data?.yxdkse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
</style>
