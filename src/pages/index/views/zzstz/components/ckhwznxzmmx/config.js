import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigOneRules = {};
export const mainColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 60,
    foot: '合计',
  },
  {
    align: 'left',
    width: 170,
    colKey: 'znxzmbh',
    title: '转内销证明编号',
  },
  {
    align: 'left',
    width: 170,
    colKey: 'fphm',
    title: '发票号码',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'krpq',
    title: '开票日期',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'gxrzsj',
    title: '勾选日期',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'hxytDm',
    title: '核选用途',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'bdkyyDm',
    title: '不抵扣原因',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    colKey: 'je',
    title: '金额',
    width: 110,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '税额',
    width: 110,
  },
  {
    align: 'right',
    colKey: 'yxdkse',
    title: '可抵扣税额',
    width: 110,
  },
];
export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '核选用途',
    key: 'hxytDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '转内销证明编号',
    key: 'znxzmbh',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
];
