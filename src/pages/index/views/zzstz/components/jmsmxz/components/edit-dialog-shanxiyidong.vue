<template>
  <css-dialog
    class="dialogCss"
    :header="['新增增值税减免明细账', '编辑增值税减免明细账'][visible.pageType]"
    :visible.sync="isVisible"
    @close="onClose"
    :closeOnOverlayClick="false"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <!-- <t-col :span="4">
            <t-form-item label="公司号" name="gsh">
              <t-input v-model="formData.gsh" placeholder="请输入"></t-input>
            </t-form-item>
          </t-col> -->
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" placeholder="请选择" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
              <!-- <t-input v-model="formData.lrzx" placeholder="请输入"></t-input> -->
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请输入" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-input disabled v-model="formData.tempSl" readonly placeholder=""></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="优惠事项代码" name="swsxDm">
              <t-select v-model="formData.swsxDm" disabled placeholder="" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="优惠事项名称" name="swsxmc">
              <t-select v-model="formData.swsxmc" disabled placeholder="" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本期发生额" name="bqfse">
              <!-- <t-input-number v-model="formData.bqfse" theme="column" :decimal-places="2" :disabled="disabledBqfse" /> -->
              <gt-input-money v-model="formData.bqfse" theme="normal" align="left" :disabled="true" />
              <!-- <gt-input-money v-model="formData.bqfse" /> -->
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="免征增值税项目销售额" name="mzzzsxmxse">
              <!-- <t-input-number
                v-model="formData.mzzzsxmxse"
                theme="column"
                :decimal-places="2"
                :disabled="disabledMzzzsxmxse"
              /> -->
              <gt-input-money v-model="formData.mzzzsxmxse" theme="normal" align="left" :disabled="false" />
              <!-- <gt-input-money v-model="formData.mzzzsxmxse" :disabled="disabledMzzzsxmxse" /> -->
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item name="mzxsekcxmbqsjkcje" label="免税销售额扣除项目本期实际扣除金额">
              <!-- <gt-input-money disabled v-model="formData.mzxsekcxmbqsjkcje" /> -->
              <!-- <t-input-number v-model="formData.mzxsekcxmbqsjkcje" theme="column" :decimal-places="2" disabled /> -->
              <gt-input-money v-model="formData.mzxsekcxmbqsjkcje" theme="normal" align="left" disabled />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="调整原因" name="tzyy">
              <t-input :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable
            /></t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { jmsmxzSaveOrUpdate } from '@/pages/index/api/tzzx/zzstz/jmszz.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      // gsh: [{ required: true, message: '必填', type: 'error' }],
      lrzx: [{ required: true, message: '必选', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      disabledBqfse: true,
      disabledMzzzsxmxse: true,
      confirmLoading: false,
      rules: {},
      kmdmList: [
        { label: '通信与信息服务收入-网间结算收入-国际漫游来访-语音', value: '5111100301.0' },
        { label: '通信与信息服务收入-网间结算收入-国际漫游来访-短信', value: '5111100302.0' },
        { label: '通信与信息服务收入-网间结算收入-国际漫游来访-数据流量业务', value: '5111100303.0' },
        { label: '通信与信息服务收入-网间结算收入-国际漫游来访-其他', value: '5111100304.0' },
      ],
      lrzxList: [],
      selectKjfpObj: {}, // 选中的kjfp
      formData: {
        sszq: '',
        // gsh: '',
        lrzx: '',
        kjpzbh: '',
        sl1: '',
        kmdm: '',
        kmmc: '',
        swsxDm: '',
        swsxmc: '',
        bqfse: '',
        mzzzsxmxse: '',
        mzxsekcxmbqsjkcje: '',
        tzyy: '',
      },
    };
  },
  created() {
    this.init();
  },
  watch: {
    'formData.mzzzsxmxse': {
      handler(newVal) {
        this.formData.bqfse = newVal;
      },
      immediate: true,
    },
  },
  computed: {
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
  },
  methods: {
    getXzKmmc(kmdm) {
      this.formData.kmmc = this.kmdmList.find((item) => item.value === kmdm)?.label;
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
    },
    async init() {
      this.rules = this.baseRules;
      console.log(this.visible);
      this.getLrzx();
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.lrzx) {
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
        this.formData.tempSl = (this.formData.sl1 && `${this.formData.sl1 * 100}%`) || '';
      }
      this.formData.gsh = this.$store.state.zzstz.userInfo?.qydmz || '';
      this.formData.kmdm = this.kmdmList[0].value;
      this.formData.kmmc = this.kmdmList[0].label;
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'gsh',
          'lrzx',
          'kjpzbh',
          'sl1',
          'kmdm',
          'jmzlxDm',
          'swsxDm',
          'bqfse',
          'mzzzsxmxse',
          'mzxsekcxmbqsjkcje',
          'tzlxDm',
          'ly',
          'zzuuid',
          'kmmc',
          'tzyy',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = [
          { ...p, ...this.selectKjfpObj, ...this.visible.otherObj, sszq: Number(dayjs(p.sszq).format('YYYYMM')) },
        ];
        if (this.visible.row?.uuid) params[0].uuid = this.visible.row?.uuid;
        try {
          await jmsmxzSaveOrUpdate(params);
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateJmsmx');
          } else {
            this.$emit('updateJmsmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
