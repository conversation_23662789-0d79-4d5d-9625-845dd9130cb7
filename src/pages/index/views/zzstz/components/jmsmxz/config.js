import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '分配',
    key: 'kjfpDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '',
    key: 't1',
  },
];
export const mainColumns = [
  {
    align: 'center',
    className: 'demo-multiple-select-cell',
    width: 50,
    colKey: 'row-select',
    type: 'multiple',
    checkProps: ({ row }) => ({ disabled: row.ly !== '1' }),
    fixed: 'left',
  },
  {
    align: 'center',
    width: 50,
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    align: 'left',
    width: 110,
    colKey: 'gsh',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 160,
    colKey: 'kmdm',
    title: '科目编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 220,
    colKey: 'kmmc',
    title: '科目名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 210,
    colKey: 'kjpzbh',
    title: '凭证编号',
  },
  {
    align: 'left',
    width: 220,
    colKey: 'kjfpDm',
    title: '分配',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'pztt',
    title: '凭证抬头',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 80,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'left',
    width: 140,
    colKey: 'swsxDm',
    title: '优惠事项代码',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'swsxmc',
    title: '优惠事项名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 160,
    colKey: 'bqfse',
    title: '本期发生额',
  },
  {
    align: 'right',
    width: 160,
    colKey: 'mzzzsxmxse',
    title: '免征增值税项目销售额',
    ellipsisTitle: true,
  },
  {
    align: 'right',
    width: 160,
    colKey: 'mzxsekcxmbqsjkcje',
    title: '免税销售额扣除项目本期实际扣除金额',
    ellipsisTitle: true,
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'center',
    width: 100,
    colKey: 'cz',
    title: '操作',
    fixed: 'right',
  },
];
