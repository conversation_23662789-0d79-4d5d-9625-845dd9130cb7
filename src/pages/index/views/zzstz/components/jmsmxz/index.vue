<!--
 * @Descripttion: 台账-增值税一般纳税人-减免税总账
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      class="znsbHeadqueryDiv"
      ref="queryControl"
      :config="querySearchConfigs"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
    >
      <template #t1><span></span></template>
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="editRow({})" :disabled="!allowEdit"><add-icon slot="icon" />新增</t-button>
        <t-button theme="primary" @click="delRow" :disabled="!allowEdit"><DeleteIcon slot="icon" />删除</t-button>
        <ExtractDataButton variant="outline" :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <t-button
          variant="outline"
          theme="primary"
          @click="$router.push('/lsgl')"
          v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        row-key="uuid"
        height="100%"
        :data="tableData"
        :columns="tableColumns"
        hover
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :selected-row-keys.sync="selectedRowKeys"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #sl1="{ row }"> {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }} </template>
        <template #kjfpDm="{ row }">
          {{
            (kjfpList.find((d) => d.value === row.kjfpDm) && kjfpList.find((d) => d.value === row.kjfpDm).label) || ''
          }}
        </template>
        <template #bqfse="{ row }">
          <span>{{ numberToPrice(row.bqfse) }}</span>
        </template>
        <template #mzzzsxmxse="{ row }">
          <span>{{ numberToPrice(row.mzzzsxmxse) }}</span>
        </template>
        <template #mzxsekcxmbqsjkcje="{ row }">
          <span>{{ numberToPrice(row.mzxsekcxmbqsjkcje) }}</span>
        </template>
        <template #cz="{ row }">
          <t-button v-if="row.ly * 1" variant="text" @click="editRow(row)" theme="primary" :disabled="!allowEdit"
            >编辑</t-button
          >
          <t-button v-else variant="text" disabled>编辑</t-button>
        </template>
      </t-table>
    </div>
    <EditDialogSXYD
      :visible.sync="editDialogVisible"
      v-if="editDialogVisible && shanxiyidongFlag"
      @updateJmsmx="query()"
    />
    <EditDialog
      :visible.sync="editDialogVisible"
      v-if="editDialogVisible && !shanxiyidongFlag"
      @updateJmsmx="query()"
    />
  </div>
</template>
<script>
import { getKjfp } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { initJmsmxQuery, jmsmxzSaveOrUpdate, initJmsmxQueryHj } from '@/pages/index/api/tzzx/zzstz/jmszz.js';
import dayjs from 'dayjs';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, ChartIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { querySearchConfig, mainColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';
import EditDialogSXYD from './components/edit-dialog-shanxiyidong.vue';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    EditDialog,
    EditDialogSXYD,
    AddIcon,
    DeleteIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    return {
      formData: {},
      isProduct: this.$store.state.isProduct.envValue,
      mainColumns,
      querySearchConfig,
      tableLoading: false,
      editDialogVisible: false,
      fromName: false,
      tableData: [],
      kjfpList: [], // 分配
      footData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      selectedRowKeys: [],
      columnsLoading: false, // 列配置加载状态
      remoteColumns: [], // 动态列配置
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
    };
  },
  computed: {
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.mainColumns; // 兼容初始化状态
      }
      return this.remoteColumns;
    },
    querySearchConfigs() {
      let config = this.shanxiyidongFlag
        ? this.querySearchConfig.filter((d) => d.key !== 'kjfpDm')
        : this.querySearchConfig;

      // 添加通用国际分支处理逻辑
      if (this.tongyongguojiFlag) {
        config = config.map((item) => {
          if (item.key === 'kjfpDm') {
            return {
              ...item,
              label: '业务类型',
            };
          }
          return item;
        });
      }

      return config;
    },
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jmsMxzTz';
    },
    exportFileName() {
      return '减免税明细账台账';
    },
    tzQueryParams() {
      return {
        uuidList: this.selectedRowKeys.map((d) => d),
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...this.formData,
        sszq: this.sszqC,
        kjfpDm: multiSelectHandle(this.formData.kjfpDm),
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
  },
  async mounted() {
    this.init();
    this.fetchTableColumns(); // 初始化表头配置
    this.checkAllowEdit();
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    // 动态配置表头信息
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 处理列配置
        this.remoteColumns = this.mainColumns
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        // 失败时回退到默认配置
        this.remoteColumns = this.mainColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },

    async init() {
      const { data } = await getKjfp({ ywDm: '210' });
      this.kjfpList = this.tongyongguojiFlag ? [{ label: '扶贫捐赠', value: 'A01' }] : data;
      this.querySearchConfig[1].selectList = this.kjfpList;
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        sszq: this.sszqC,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, pageNum: this.pagination.current, pageSize: this.pagination.pageSize };
      } else {
        params = {
          ...this.formData,
          kjfpDm: multiSelectHandle(this.formData.kjfpDm),
          ...params,
        };
      }
      try {
        const res = await initJmsmxQuery(params);
        const { data } = res;
        this.tableData = data?.records || [];
        this.pagination.total = data?.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await initJmsmxQueryHj(params);
          this.footData =
            [
              {
                bqfse: numberToPrice(data?.bqfse),
                mzzzsxmxse: numberToPrice(data?.mzzzsxmxse),
                mzxsekcxmbqsjkcje: numberToPrice(data?.mzxsekcxmbqsjkcje),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    editRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        sszq: this.sszqC,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        qydmz: this.$store.state.zzstz.userInfo?.qydmz || '',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys.length > 0) {
        console.log('selectedRowKeys', this.selectedRowKeys);
        const temp = JSON.stringify(this.tableData.find((t) => t.uuid === this.selectedRowKeys[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }
      console.log('selectedRowData', selectedRowData);
      const rowdata = JSON.stringify(selectedRowData);
      this.editDialogVisible = { row: JSON.parse(rowdata), kjfpList: this.kjfpList, otherObj, pageType };
    },
    delRow() {
      if (!this.selectedRowKeys.length) {
        this.$message.error('请选择要删除的项目');
        return;
      }
      const confirmDia = this.$dialog.confirm({
        header: '确定要删除该项么？',
        // body: '你确定要删除该项么？',
        confirmBtn: { content: '确定', variant: 'base', theme: 'danger' },
        cancelBtn: '取消',
        onConfirm: async () => {
          const selectedRowKeys = this.selectedRowKeys.map((d) => ({ uuid: d, scbz: '1' }));
          await jmsmxzSaveOrUpdate(selectedRowKeys); // 删除传scbz 1
          this.$message.success('删除成功');
          this.query();
          confirmDia.destroy();
        },
        onClose: () => {
          confirmDia.destroy();
        },
      });
    },
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.filter-btns {
  float: right;
}
</style>
