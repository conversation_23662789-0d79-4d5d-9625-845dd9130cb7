import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
// 查询搜索配置
export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    type: 'input',
    label: '纳税检查凭证编号',
    key: 'nsjctzlxmc',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '货物', value: '01' },
      { label: '劳务', value: '02' },
      { label: '服务', value: '03' },
      { label: '无形资产', value: '04' },
      { label: '不动产', value: '05' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '13%', value: '0.13' },
      { label: '9%', value: '0.09' },
      { label: '6%', value: '0.06' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];

// 数据列配置
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'zsxmmc',
    title: '征税项目',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    title: '税率',
    colKey: 'sl1',
    width: 80,
  },
  {
    align: 'right',
    title: '金额',
    colKey: 'je',
    width: 140,
  },
  {
    align: 'right',
    title: '税额',
    colKey: 'se',
    width: 140,
  },
  {
    title: '纳税检查凭证编号',
    colKey: 'nsjctzlxmc',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    title: '调整日期',
    colKey: 'xjrq1',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    title: '违法行为名称',
    colKey: 'wfxwmc',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    title: '操作',
    colKey: 'operation',
    width: 100,
    fixed: 'right',
    align: 'center',
  },
];
