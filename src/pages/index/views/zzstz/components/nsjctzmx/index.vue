<!--
 * @Descripttion: 台账-纳税检查调整明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2025-08-14 14:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
          ><add-icon slot="icon" />新增</t-button
        >
        <t-button theme="primary" @click="delRow" :disabled="!allowEdit || selectedRowKeys.length === 0"
          ><DeleteIcon slot="icon" />删除</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="tableColumns"
        height="100%"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #sl1="{ row }">
          {{ row.sl1 ? row.sl1 * 100 + '%' : '' }}
        </template>
        <template #je="{ row }">
          {{ numberToPrice(row.je) }}
        </template>
        <template #se="{ row }">
          {{ numberToPrice(row.se) }}
        </template>
        <template #xjrq1="{ row }">
          {{ row.xjrq1 ? dayjs(row.xjrq1).format('YYYY-MM-DD') : '' }}
        </template>
        <template #operation="{ row }">
          <t-link
            theme="primary"
            hover="color"
            @click="newOrEditRow(row)"
            v-show="row.ly === '1'"
            :disabled="!allowEdit"
          >
            编辑
          </t-link>
        </template>
      </t-table>
      <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateNsjctzmx="(item) => query(item)" />
      <div v-show="boxvisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="请确认是否删除所选明细"
          :onConfirm="confirmDelRow"
          :onClose="closeBox"
        >
        </t-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberUtils.js';
import { nsjctzmxQuery, nsjctzmxQueryHj, nsjctzmxDelete } from '@/pages/index/api/tzzx/zzstz/nsjctzmx.js';
import { computeSszq, multiSelectHandle, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { querySearchConfig, dataColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    QsbButton,
    ExportButton,
    SearchControlPanel,
    EditDialog,
    AddIcon,
    DeleteIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      editDialogVisible: false,
      boxvisible: false,
      querySearchConfig,
      dataColumns,
      tableLoading: false,
      fromName: false,
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      delformData: [],
      footData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      columnsLoading: false,
      remoteColumns: [],
      allowEdit: false,
      editCheckLoading: false,
    };
  },
  created() {},
  mounted() {
    this.initQueryConditions();
    this.fetchTableColumns();
    this.checkAllowEdit();
    this.query({ initQuery: true });
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'nsjctzMxTz';
    },
    exportFileName() {
      return '纳税检查调整明细账';
    },
    tzQueryParams() {
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      djParam.pageNum = this.pagination.current;
      djParam.pageSize = this.pagination.pageSize;
      return {
        ...this.formData,
        sszq: this.sszqToExtract,
        jsfsDm1: '01',
        zsxmDm: multiSelectHandle(this.formData.zsxmDm),
        ...djParam,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.dataColumns;
      }
      return this.remoteColumns;
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);

        if (params.get('skssqq')) {
          this.$refs.queryControl.setParams({
            sszq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
          });
          this.$set(this.formData, 'sszq', dayjs(this.$route.query.skssqq).format('YYYY-MM'));
          return;
        }
      }
      this.$refs.queryControl.setParams({
        sszq: computeSszq(),
      });
      this.$set(this.formData, 'sszq', computeSszq());
      // 在方法末尾添加回调
      this.$nextTick(() => {
        this.checkAllowEdit();
      });
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig || {};

        const defaultColumns = this.dataColumns;

        this.remoteColumns = defaultColumns
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        this.remoteColumns = this.dataColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        sszq: dayjs(this.formData.sszq).format('YYYYMM'),
        ly: '1',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys.length > 0) {
        console.log('selectedRowKeys', this.selectedRowKeys);
        const temp = JSON.stringify(this.tableData.find((t) => t.uuid === this.selectedRowKeys[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }

      this.editDialogVisible = {
        row: JSON.parse(JSON.stringify(selectedRowData)),
        oldkjpzbh: row.kjpzbh,
        otherObj,
        pageType,
      };
      console.log('editDialogVisible', this.editDialogVisible);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.delformData = selectedRowData.filter((i) => i);
    },
    delRow() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的记录');
        return;
      }
      this.boxvisible = true;
    },
    async confirmDelRow() {
      try {
        this.tableLoading = true;
        // 这里需要替换为实际的删除API
        await nsjctzmxDelete(this.delformData.map((item) => item.uuid));
        this.$message.success('删除成功');
        this.query({ flag: true });
        this.boxvisible = false;
        this.selectedRowKeys = [];
      } catch (error) {
        console.error('删除失败', error);
        this.$message.error('删除失败');
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange(e) {
      this.pagination.current = e.current;
      this.pagination.pageSize = e.pageSize;
      this.query();
    },
    async query(pm = { flag: false, p: false, fy: false, from: false, initQuery: false }) {
      console.log('query', pm);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) {
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params, zsxmDm: p.zsxmDm1 }; // 起始时间待解决
        this.$refs.queryControl.setParams({
          sszq: dayjs(p.sszq).format('YYYY-MM'),
          zsxmDm: (p?.zsxmDm1 ?? '').split(','),
          // eslint-disable-next-line no-nested-ternary
          sl1: p?.sl1 === 0 ? String(p.sl1 ?? '').split(',') : (p?.sl1 ?? '').toString().split(','),
        });
      } else {
        params = {
          ...this.formData,
          ...params,
          sszq: this.sszqToExtract,
          jsfsDm1: '01',
          zsxmDm: multiSelectHandle(this.formData.zsxmDm),
          sl1: multiSelectHandle(this.formData.sl1),
        };
      }
      try {
        console.log('nsjctzmx-params', params);
        const { data } = await nsjctzmxQuery(params);

        this.tableData = data.records || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await nsjctzmxQueryHj(params);
          this.footData =
            [
              {
                je: numberToPrice(data?.je),
                se: numberToPrice(data?.se),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.error(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    numberToPrice,
    dayjs,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  // padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
