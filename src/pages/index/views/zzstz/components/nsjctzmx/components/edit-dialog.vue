<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增纳税检查调整明细', '编辑纳税检查调整明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm">
              <t-select v-model="formData.zsxmDm" placeholder="请选择征税项目" clearable>
                <t-option value="01" label="货物"></t-option>
                <t-option value="02" label="劳务"></t-option>
                <t-option value="03" label="服务"></t-option>
                <t-option value="04" label="无形资产"></t-option>
                <t-option value="05" label="不动产"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable>
                <t-option value="0.13" label="13%"></t-option>
                <t-option value="0.09" label="9%"></t-option>
                <t-option value="0.06" label="6%"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="金额" name="je">
              <gt-input-money v-model="formData.je" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="纳税检查凭证编号" name="nsjctzlxmc">
              <t-input :maxlength="30" v-model="formData.nsjctzlxmc" placeholder="请输入纳税检查凭证编号"></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="违法行为名称" name="wfxwmc">
              <t-input :maxlength="100" v-model="formData.wfxwmc" placeholder="请输入违法行为名称" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="调整日期" name="xjrq1">
              <t-date-picker
                v-model="formData.xjrq1"
                placeholder="请输入调整日期"
                style="width: 100%"
                clearable
                type="date"
                format="YYYY-MM-DD"
              />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { computeSszq } from '@/pages/index/views/util/tzzxTools';
import { nsjctzmxSave, nsjctzmxUpdate } from '@/pages/index/api/tzzx/zzstz/nsjctzmx.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  name: 'NsjctzmxEditDialog',
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      sszq: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      je: [{ required: true, message: '必填', type: 'error' }],
      se: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      formData: {
        sszq: '',
        uuid: '',
        zsxmDm: '',
        sl1: '',
        je: '',
        se: '',
        nsjctzlxmc: '',
        wfxwmc: '',
        xjrq1: '',
      },
    };
  },
  created() {},
  async mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt?.sbgzEnterFlag || false;
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.sszq) {
        console.log('this.visible.row', this.visible.row);
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        ['sszq', 'uuid', 'zsxmDm', 'sl1', 'je', 'se', 'nsjctzlxmc', 'wfxwmc', 'xjrq1'].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          jsfsDm1: '01',
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
          sl1: parseFloat(p.sl1),
          je: parseFloat(p.je),
          se: parseFloat(p.se),
        };
        try {
          console.log('params', params);
          if (this.visible.pageType) {
            await nsjctzmxUpdate(params);
          } else {
            await nsjctzmxSave(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          this.$emit('updateNsjctzmx', { flag: true });
          this.isVisible = false;
        } catch (e) {
          console.error(e);
          this.$message.error(['新增失败', '修改失败'][this.visible.pageType]);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
