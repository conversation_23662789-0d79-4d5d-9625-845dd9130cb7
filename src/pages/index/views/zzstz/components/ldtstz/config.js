import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  skssqq: [{ required: true, message: '必填项', type: 'error' }],
  skssqz: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfig = [
  {
    label: '税款所属期起',
    key: 'skssqq',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    relation: 'skssqz',
    timeRange: 'start',
  },
  {
    label: '税款所属期止',
    key: 'skssqz',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'skssqq',
  },
  {
    label: '企业税号',
    key: 'nsrsbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '企业名称',
    key: 'nsrmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const mainColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqq',
    title: '税款所属期起',
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqz',
    title: '税款所属期止',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'nsrsbh',
    title: '企业税号',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'nsrmc',
    title: '企业名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 180,
    colKey: 'pzxh',
    title: '税务凭证序号',
    ellipsis: true,
  },
  {
    align: 'left',
    width: 120,
    colKey: 'ldfsrq',
    title: '留抵发生日期',
  },
  {
    align: 'left',
    width: 120,
    colKey: 'czlxDm',
    title: '操作类型',
  },
  {
    align: 'left',
    width: 120,
    colKey: 'ldlx',
    title: '留抵类型',
  },

  {
    align: 'left',
    width: 120,
    colKey: 'zzslx',
    title: '增值税类型',
  },
  {
    align: 'right',
    width: 160,
    colKey: 'bcldbhs',
    title: '本次留抵变化数',
  },
];
