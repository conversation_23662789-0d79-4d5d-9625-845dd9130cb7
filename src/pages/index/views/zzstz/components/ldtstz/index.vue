<!-- * @Descripttion: 台账-增值税一般纳税人-减免税总账 -->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      class="znsbHeadqueryDiv"
      ref="queryControl"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
    />
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <!-- todo 按需导出 -->
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => dc() },
            { content: '导出所有页', value: 2, onClick: () => dc('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        row-key="uuid"
        height="100%"
        hover
        :data="tableData"
        :columns="mainColumns"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #bcldbhs="{ row }">
          <span>{{ numberToPrice(row.bcldbhs) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { queryZzsldts } from '@/pages/index/api/tzzx/zzstz/ldtstz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { querySearchConfig, mainColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    this.mainColumns = mainColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    return {
      userInfo: {},
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      tableData: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      fromName: '',
    };
  },
  created() {
    this.querySearchConfig[0].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    this.querySearchConfig[1].value = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    this.formData.skssqq = dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.formData.skssqz = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.querySearchConfig[1].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData.skssqq = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData.skssqz = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
      }
    }
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.skssqq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
  },
  mounted() {},
  methods: {
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        this.$refs.queryControl.setParams(p);
      } else {
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await queryZzsldts(params);
        this.tableData = data.records || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data?.pageTotal;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async dc(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'zzsyjFzjgTz',
        fileName: '增值税留底退税',
        cxParam: {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          ...this.formData,
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
</style>
