<!-- 
 1.znsbBackGroupDiv最外层div的类 定义了占满全屏的高度和能缩小的最小宽度  
 2.与包含tab页不同的是  此页面需要自己在最外层定义一个flex纵向布局 才能使table部分的高度自适应
 3.查询部分用组件search-control-panel 内部有定义的样式  上面margin 16  左右24
  -->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel class="znsbHeadqueryDiv" ref="queryControl1" :config="querySearchConfig1" :colNum="4" />
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button theme="primary"> <add-icon slot="icon" />新建</t-button>
          <t-button theme="primary"><DeleteIcon slot="icon" />删除</t-button>
          <t-button theme="primary" variant="outline"><CloudUploadIcon slot="icon" />提取数据</t-button>
          <t-button theme="primary" variant="outline"><CloudUploadIcon slot="icon" />同步数据</t-button>
          <t-button variant="outline" theme="primary"><SearchIcon slot="icon" />查看底稿</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1 },
              { content: '导出所有页', value: 2 },
            ]"
          >
            <t-button variant="outline" theme="primary">
              <DownloadIcon slot="icon" />
              <span>导出</span>
            </t-button>
          </t-dropdown>
        </gt-space>
        <t-button>返回</t-button>
      </div>
      <div class="adaption-body">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            :data="tableData"
            :columns="columns"
            :pagination="pagination"
            @page-change="(e) => pageChange(e, 0)"
          >
          </t-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  AddIcon,
  CloudUploadIcon,
  SearchIcon,
  // DiscountIcon,
  // CloudDownloadIcon,
  DeleteIcon,
  DownloadIcon,
  // CheckIcon,
} from 'tdesign-icons-vue';
import { querySearchConfig1 } from './config.js';
import SkeletonFrame from './SkeletonFrame';

export default {
  name: 'notab',
  components: {
    SkeletonFrame,
    SearchControlPanel,
    AddIcon,
    DeleteIcon,
    DownloadIcon,
    CloudUploadIcon,
    SearchIcon,
    // DiscountIcon,
    // CloudDownloadIcon,
    // CheckIcon,
  },
  data() {
    return {
      loading: true,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        { colKey: 'aa', title: '申请人1', width: '100' },
        { colKey: 'bb', title: '申请人2', width: '100' },
        { colKey: 'cc', title: '申请人3', width: '100' },
        { colKey: 'dd', title: '申请人4', width: '100' },
        { colKey: 'ee', title: '申请人5', width: '100' },
        { colKey: 'ff', title: '申请人6', width: '100' },
      ],
      tableData: [],
      // tableData: [
      //   {
      //     uuid: '14324234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '25345234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '3423424',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '43124124',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '543242',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '6352232',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '752342',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '8523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '9523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '10523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '114324234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '125345234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '13423424',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '143124124',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '1543242',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '16352232',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '1752342',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '18523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '19523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '20523453',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      // ],
      // tableData: [
      //   {
      //     uuid: '14324234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '25345234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '3423424',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '43124124',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '543242',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '6352232',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '752342',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      // ],
      tabValue: '0',
      querySearchConfig1,
    };
  },
  mounted() {
    this.changeLoading();
  },
  methods: {
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    renderIcon() {
      return <SearchIcon />;
    },
    query() {},
    pageChange({ current, pageSize }, type) {
      [this.pagination, this.hwpagination][type].current =
        pageSize !== [this.pagination, this.hwpagination][type].pageSize ? 1 : current;
      [this.pagination, this.hwpagination][type].pageSize = pageSize;
      // this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
.adaption-wrap {
  display: flex;
  flex-direction: column;
}
.adaption-body {
  overflow: hidden;
  flex: 1;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
</style>
