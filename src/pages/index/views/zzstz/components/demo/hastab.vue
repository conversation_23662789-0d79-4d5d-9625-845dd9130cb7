<!--
 * 带有tab的页面  
-->
<template>
  <div class="znsbBackGroupDiv">
    <t-tabs v-model="tabValue">
      <search-control-panel
        class="znsbHeadqueryDiv"
        style="margin-top: 16px"
        ref="queryControl1"
        :config="querySearchConfig1"
        :colNum="4"
      />
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button theme="primary">提取数据</t-button>
          <t-button variant="outline" theme="primary">查看底稿</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1 },
              { content: '导出所有页', value: 2 },
            ]"
          >
            <t-button variant="outline" theme="primary">
              <span>导出 <ChevronDownIcon size="16" /></span>
            </t-button>
          </t-dropdown>
        </gt-space>
        <t-button>返回</t-button>
      </div>
      <t-tab-panel value="0" label="进项发票明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            :data="tableData"
            :columns="columns"
            :pagination="pagination"
            @page-change="(e) => pageChange(e, 0)"
          >
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<script>
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { ChevronDownIcon } from 'tdesign-icons-vue';
import { querySearchConfig1 } from './config.js';

export default {
  name: 'hastab',
  components: {
    SearchControlPanel,
    ChevronDownIcon,
  },
  data() {
    return {
      tabValue: '0',
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      columns: [
        { colKey: 'aa', title: '申请人1', width: '100' },
        { colKey: 'bb', title: '申请人2', width: '100' },
        { colKey: 'cc', title: '申请人3', width: '100' },
        { colKey: 'dd', title: '申请人4', width: '100' },
        { colKey: 'ee', title: '申请人5', width: '100' },
        { colKey: 'ff', title: '申请人6', width: '100' },
      ],
      tableData: [
        {
          uuid: '14324234',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '25345234',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '3423424',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '43124124',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '543242',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '6352232',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '752342',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '8523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '9523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '10523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '114324234',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '125345234',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '13423424',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '143124124',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '1543242',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '16352232',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '1752342',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '18523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '19523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
        {
          uuid: '20523453',
          aa: '1',
          bb: '2',
          cc: '3',
          dd: '4',
          ee: '5',
          ff: '6',
        },
      ],
      // tableData: [
      //   {
      //     uuid: '14324234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '25345234',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '3423424',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '43124124',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '543242',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '6352232',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      //   {
      //     uuid: '752342',
      //     aa: '1',
      //     bb: '2',
      //     cc: '3',
      //     dd: '4',
      //     ee: '5',
      //     ff: '6',
      //   },
      // ],
      querySearchConfig1,
    };
  },
  methods: {
    query() {},
    pageChange({ current, pageSize }, type) {
      [this.pagination, this.hwpagination][type].current =
        pageSize !== [this.pagination, this.hwpagination][type].pageSize ? 1 : current;
      [this.pagination, this.hwpagination][type].pageSize = pageSize;
      // this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
.tableButton {
  color: #0052d9 !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
</style>
