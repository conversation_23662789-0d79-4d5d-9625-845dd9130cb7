<template>
  <div class="znsbBackGroupDiv">
    <div class="skeleton-wrap">
      <t-row>
        <t-col :span="4" v-for="(_, index) in 3" :key="index">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '20px' }]]" />
        </t-col>
      </t-row>
      <t-row>
        <t-col :span="4" v-for="(_, index) in 3" :key="index">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '20px' }]]" />
        </t-col>
      </t-row>
      <t-row>
        <t-col :span="4">
          <t-row>
            <t-col :span="3" v-for="(_, index) in 3" :key="index">
              <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '20px' }]]" />
            </t-col>
          </t-row>
        </t-col>
      </t-row>
      <t-row>
        <t-col :span="12" v-for="(_, index) in 10" :key="index">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '10px 20px' }]]"
        /></t-col>
        <t-col :span="12">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '10px 20px 20px 20px' }]]"
        /></t-col>
      </t-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'search-table',
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
@import '../../../../../styles/sbPageGy.less';
.skeleton-wrap {
  // width: 100%;
  // height: 100%;
  padding: 10px;
  // box-sizing: border-box;
  background: #fff;
}
</style>
