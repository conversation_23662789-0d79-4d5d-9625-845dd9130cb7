<template>
  <component :is="skeletonMap[type]" />
</template>

<script>
const skeletonMap = {
  searchTable: () => import('./search-table.vue'),
  showTable: () => import('./show-table.vue'),
};
export default {
  name: 'skeleton-frame',
  props: {
    type: {
      type: String,
      default: 'searchTable',
    },
  },
  data() {
    return {
      skeletonMap,
    };
  },
};
</script>

<style lang="less" scoped></style>
