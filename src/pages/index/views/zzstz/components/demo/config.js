import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  // sfjzjt: [{ required: true, message: '必填项', type: 'error' }],
};
export const xmList = [
  { value: '01', label: '本期认证相符且本期申报抵扣' },
  { value: '02', label: '前期认证相符且本期申报抵扣' },
  { value: '03', label: '海关进口增值税专用缴款书' },
  { value: '04', label: '农产品收购发票或者销售发票' },
  { value: '05', label: '代扣代缴税收缴款凭证' },
  { value: '06', label: '加计扣除农产品进项税额' },
  { value: '07', label: '本期用于购建不动产的扣税凭证' },
  { value: '08', label: '本期用于抵扣的旅客运输服务扣税凭证' },
  { value: '09', label: '外贸企业进项税额抵扣证明' },
  { value: '10', label: '其他' },
];
export const fpmxColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'left',
    width: 50,
  },
  {
    align: 'left',
    width: 200,
    colKey: 'fplxDm',
    title: '发票类型',
  },
  {
    align: 'left',
    width: 80,
    colKey: 'fpdm',
    title: '发票代码',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'fphm',
    title: '发票号码',
  },
  {
    align: 'right',
    width: 90,
    colKey: 'je',
    title: '金额',
  },
  {
    align: 'right',
    width: 90,
    colKey: 'se',
    title: '税额',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'jshj',
    title: '价税合计',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'kprq',
    title: '开票日期',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'skssq',
    title: '税款所属期',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'xfsh',
    title: '销方税号',
  },
  {
    align: 'left',
    width: 160,
    colKey: 'xfmc',
    title: '销购方名称',
  },
  // {
  //   align: 'left',
  //   width: 110,
  //   colKey: 'dkztDm',
  //   title: '抵扣状态',
  // },
  {
    align: 'left',
    width: 160,
    colKey: 'gxrzsj',
    title: '勾选日期',
  },
  {
    align: 'left',
    width: 110,
    colKey: 'hxytDm',
    title: '核选用途',
  },
  {
    align: 'left',
    width: 120,
    colKey: 'bdkyyDm',
    title: '不抵扣原因',
  },
  {
    align: 'right',
    width: 110,
    colKey: 'dkje',
    title: '可抵扣金额',
  },
  {
    align: 'right',
    width: 110,
    colKey: 'dkse',
    title: '可抵扣税额',
  },
];
export const hwfpmxColumns = [
  // {
  //   colKey: 'row-select',
  //   type: 'multiple',
  //   width: 60,
  //   resize: {
  //     minWidth: 60,
  //     maxWidth: 60,
  //   },
  //   fixed: 'left',
  // },
  {
    colKey: 'xh',
    title: '序号',
    align: 'left',
    width: 50,
  },
  {
    align: 'left',
    width: 200,
    colKey: 'fplxDm',
    title: '发票类型',
  },
  {
    align: 'left',
    width: 80,
    colKey: 'fpdm',
    title: '发票代码',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'fphm',
    title: '发票号码',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'kprq',
    title: '开票日期',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'xsfnsrsbh',
    title: '销方税号',
  },
  {
    align: 'left',
    width: 160,
    colKey: 'xsfmc',
    title: '销方名称',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'hwhyslwfwmc',
    title: '货物或应税劳务、服务名称',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'sphfwssflhbbm',
    title: '税收分类编码',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'ggxh',
    title: '规格型号',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'fpspsl',
    title: '数量',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'fpspdj',
    title: '单价',
  },
  {
    align: 'left',
    width: 80,
    colKey: 'dw',
    title: '单位',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'je',
    title: '金额',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'sl',
    title: '税率',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'se',
    title: '税额',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'jshj',
    title: '价税合计',
  },
  {
    align: 'left',
    width: 120,
    colKey: 'jzjtbz',
    title: '是否为即征即退',
  },
  // {
  //   align: 'left',
  //   colKey: 'operation',
  //   title: '操作',
  //   width: 120,
  // },
];
export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票类型',
    key: 'fplxDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
  {
    label: '核选用途',
    key: 'hxytDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票类型',
    key: 'fplxDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '货物名称',
    key: 'hwhyslwfwmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '税收分类编码',
    key: 'sphfwssflhbbm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
  {
    label: '是否即征即退',
    key: 'jzjtbz',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
