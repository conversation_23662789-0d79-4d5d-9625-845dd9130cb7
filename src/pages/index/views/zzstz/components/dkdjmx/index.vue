<!--
 * @Descripttion: 台账-增值税一般纳税人-代扣代缴发票明细 ckhwznxzmmx
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <ExtractDataButton
          :sszq="sszqToExtract"
          :readyStatus="readyStatus"
          :isJxfpmx="isQuanshanOrTongyongguojiEnv"
          @query="query"
        />
        <t-button
          variant="outline"
          theme="primary"
          @click="$router.push('/lsgl')"
          v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="key"
        height="100%"
        hover
        lazyLoad
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
        :editable-row-keys="editableRowKeys"
        @row-edit="onRowEdit"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #bdkyyDm="{ row }">
          {{
            (bdkyyList.find((d) => d.value === row.bdkyyDm) && bdkyyList.find((d) => d.value === row.bdkyyDm).label) ||
            ''
          }}
        </template>
        <template #hxytDm="{ row }">
          {{
            (hxytList.find((d) => d.value === row.hxytDm) && hxytList.find((d) => d.value === row.hxytDm).label) || ''
          }}
        </template>
        <template #jsje="{ row }">
          <span>{{ numberToPrice(row.jsje) }}</span>
        </template>
        <template #sjje="{ row }">
          <span>{{ numberToPrice(row.sjje) }}</span>
        </template>
        <template #yxdkse="{ row }">
          <span>{{ numberToPrice(row.yxdkse) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import { initDkdjpzMxQuery, initDkdjpzMxQueryHj, updateDkdjpzMx } from '@/pages/index/api/tzzx/zzstz/dkdjmx.js';
import dayjs from 'dayjs';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { ChartIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { MessagePlugin, Select } from 'tdesign-vue';
import { hxytList, bdkyyList, dkdjxmList } from '../../config';
import { querySearchConfig, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.hxytList = hxytList;
    this.bdkyyList = bdkyyList;
    return {
      formData: {},
      isProduct: this.$store.state.isProduct.envValue,
      querySearchConfig,
      tableLoading: false,
      fromName: false,
      tableData: [],
      footData: [],
      // 保存变化过的行信息
      editMap: {},
      pagination: { current: 1, pageSize: 10, total: 0 },
      editableRowKeys: ['1'],
    };
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    columns() {
      return [
        {
          colKey: 'xh',
          title: '序号',
          align: 'center',
          width: 60,
          foot: '合计',
        },
        {
          align: 'left',
          width: 180,
          colKey: 'pzhm',
          title: '代扣代缴缴款书编号',
          ellipsis: {
            theme: 'light',
            placement: 'bottom',
          },
        },
        {
          align: 'left',
          width: 180,
          colKey: 'bkjnsrsbh',
          title: '被扣缴义务人识别号',
          ellipsis: {
            theme: 'light',
            placement: 'bottom',
          },
        },
        {
          align: 'left',
          width: 200,
          colKey: 'bkjnsrmc',
          title: '被扣缴义务人名称',
          ellipsis: {
            theme: 'light',
            placement: 'bottom',
          },
        },
        {
          align: 'left',
          width: 100,
          colKey: 'skssq',
          title: '税款所属期',
        },
        {
          align: 'left',
          width: 100,
          colKey: 'tfrq',
          title: '缴款日期',
        },
        {
          align: 'left',
          colKey: 'gxrzsj',
          title: '勾选日期',
          width: 100,
        },
        {
          align: 'left',
          colKey: 'hxytDm',
          title: '核选用途',
          width: 110,
        },
        {
          align: 'left',
          colKey: 'bdkyyDm',
          title: '不抵扣原因',
          width: 100,
        },
        {
          align: 'right',
          colKey: 'jsje',
          title: '金额',
          width: 120,
        },
        {
          align: 'right',
          colKey: 'sjje',
          title: '税额',
          width: 120,
        },
        {
          align: 'right',
          colKey: 'yxdkse',
          title: '可抵扣税额',
          width: 120,
        },
        {
          colKey: 'dkdjxm',
          title: '代扣代缴项目',
          ellipsis: true,
          width: 260,
          cell: (h, { row }) => {
            if (Array.isArray(row.dkdjxm)) {
              const temp = row.dkdjxm.join('、');
              return <div>{temp}</div>;
            }
            return <div>{row.dkdjxm}</div>;
          },
          edit: {
            component: Select,
            props: {
              clearable: true,
              options: dkdjxmList,
              minCollapsedNum: 1,
              multiple: true,
            },
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 80,
          foot: '-',
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.key);
            return (
              <div>
                {!editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onEdit}>
                    编辑
                  </t-link>
                )}
                {editable && (
                  <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'dkdjpzMxTz';
    },
    exportFileName() {
      return '代扣代缴凭证明细台账';
    },
    tzQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...this.formData,
        sszq: this.sszqC,
        hxytDm: multiSelectHandle(this.formData.hxytDm),
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
    isQuanshanOrTongyongguojiEnv() {
      // 泉膳环境(jtbm === '000004')或通用国际环境(jtbm === '000005')
      const jtbm = this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm;
      return jtbm === '000004' || jtbm === '000005';
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
    this.init();
  },
  mounted() {},
  methods: {
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    dkdjxmList() {
      return dkdjxmList;
    },
    onEdit(e) {
      console.log(this.tableData, 'this.tableData');
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
      } else {
        id = e.currentTarget.dataset.id;
      }
      this.editableRowKeys.push(id);
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            this.tableData.splice(current.rowIndex, 1, current.editedRow);
            // MessagePlugin.success('保存成功');
            this.update(current.editedRow);
          }
          // 关闭编辑/保存折叠按钮
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };
    },
    init() {
      // const { code, msg, data } = await initDkjxzzQuery({ djxh: this.$store.state.zzstz.userInfo.djxh });
      // if (code === 1) {
      this.querySearchConfig[1].selectList = this.hxytList;
      this.querySearchConfig[2].selectList = this.bdkyyList;
      // }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        // 总账只展示“已申请抵扣”的数据，下钻时带回这个查询条件
        params = { ...p, ...params, hxytDm: '1' };
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
        }
        this.$refs.queryControl.setParams({ ...p, hxytDm: '1' });
      } else {
        params = {
          ...this.formData,
          sszq: this.sszqC,
          hxytDm: multiSelectHandle(this.formData.hxytDm),
          ...params,
        };
      }
      try {
        const { data } = await initDkdjpzMxQuery(params);
        this.tableData = data.records || [];
        this.pagination.total = data.pageTotal;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
          if (item.dkdjxm) {
            this.$set(item, 'dkdjxm', item.dkdjxm?.split('、'));
          }
        });
        if (this.pagination.total > 0) {
          const { data } = await initDkdjpzMxQueryHj(params);
          this.footData =
            [
              {
                jsje: numberToPrice(data?.jsje),
                sjje: numberToPrice(data?.sjje),
                yxdkse: numberToPrice(data?.yxdkse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (code) {
        this.tableData = [];
        console.error(code);
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
    async update(params) {
      try {
        console.log('updateData', params);
        const { msg } = await updateDkdjpzMx({
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          uuid: params.uuid,
          dkdjxm: this.dkdjxmHandle(params.dkdjxm),
        });
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
      }
    },
    dkdjxmHandle(dkjdxm) {
      if (dkjdxm) {
        return dkjdxm.join('、');
      }
      return '';
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
</style>
