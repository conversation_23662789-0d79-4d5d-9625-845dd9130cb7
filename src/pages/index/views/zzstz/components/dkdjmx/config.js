import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigOneRules = {};
export const mainColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 60,
    foot: '合计',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'pzhm',
    title: '代扣代缴缴款书编号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 180,
    colKey: 'bkjnsrsbh',
    title: '被扣缴义务人识别号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 200,
    colKey: 'bkjnsrmc',
    title: '被扣缴义务人名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 100,
    colKey: 'skssq',
    title: '税款所属期',
  },
  {
    align: 'left',
    width: 100,
    colKey: 'tfrq',
    title: '缴款日期',
  },
  {
    align: 'left',
    colKey: 'gxrzsj',
    title: '勾选日期',
    width: 100,
  },
  {
    align: 'left',
    colKey: 'hxytDm',
    title: '核选用途',
    width: 110,
  },
  {
    align: 'left',
    colKey: 'bdkyyDm',
    title: '不抵扣原因',
    width: 100,
  },
  {
    align: 'right',
    colKey: 'jsje',
    title: '金额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'sjje',
    title: '税额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'yxdkse',
    title: '可抵扣税额',
    width: 120,
  },
  {
    align: 'left',
    colKey: 'dkdjxm',
    title: '代扣代缴项目',
    width: 180,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    cell: (h, { row }) => {
      const editable = this.editableRowKeys.includes(row.key);
      return (
        <div>
          {!editable && (
            <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onEdit}>
              编辑
            </t-link>
          )}
          {editable && (
            <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
              保存
            </t-link>
          )}
          {editable && (
            <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
              取消
            </t-link>
          )}
        </div>
      );
    },
  },
];
export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '核选用途',
    key: 'hxytDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '不抵扣原因',
    key: 'bdkyyDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '代扣代缴缴款书编号',
    key: 'pzhm',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '被扣缴义务人识别号',
    key: 'bkjnsrsbh',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '被扣缴义务人名称',
    key: 'bkjnsrmc',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '缴款日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '缴款日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
];
