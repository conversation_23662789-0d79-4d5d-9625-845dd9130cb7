<!--
 * @Descripttion: 台账-即征即退台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2025-08-14 14:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
          ><add-icon slot="icon" />新增</t-button
        >
        <t-button theme="primary" @click="delRow" :disabled="!allowEdit || selectedRowKeys.length === 0"
          ><DeleteIcon slot="icon" />删除</t-button
        >
        <t-button variant="outline" theme="primary" @click="downloadTemplate"
          ><FileIcon slot="icon" />下载模版</t-button
        >
        <t-upload
          action="/nssb/jzjttz/v1/uploadExcel"
          :tips="tips"
          v-model="files"
          theme="custom"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          @fail="handleFail"
          @success="handleSuccess"
        >
          <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入数据</t-button>
        </t-upload>
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <a ref="downloadTemplate" style="display: none" href="./jzjttzmb.xlsx"></a>
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="tableColumns"
        height="100%"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">
          {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>
        <template #jsfsmc="{ row }">
          {{ row.jsfsmc === null || row.jsfsmc === '' ? '-' : row.jsfsmc }}
        </template>
        <template #zsxmmc="{ row }">
          {{ row.zsxmmc === null || row.zsxmmc === '' ? '-' : row.zsxmmc }}
        </template>
        <template #sl1="{ row }">
          {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
        </template>
        <template #xxje="{ row }">
          {{ row.xxje === null ? '-' : numberToPrice(row.xxje) }}
        </template>
        <template #xxse="{ row }">
          {{ row.xxse === null ? '-' : numberToPrice(row.xxse) }}
        </template>
        <template #sjkce="{ row }">
          {{ row.sjkce === null ? '-' : numberToPrice(row.sjkce) }}
        </template>
        <template #jxse="{ row }">
          {{ row.jxse === null ? '-' : numberToPrice(row.jxse) }}
        </template>
        <!-- 2025.8.29 《增值税一般纳税人台账需求设计_大连华锐重工v1.2》去掉了进项中的“实际退税额”字段 -->
        <!-- <template #sjtse="{ row }">
          {{ row.sjtse === null ? '-' : numberToPrice(row.sjtse) }}
        </template> -->
        <template #operation="{ row }">
          <t-link
            theme="primary"
            hover="color"
            @click="newOrEditRow(row)"
            v-show="row.ly === '1'"
            :disabled="!allowEdit"
          >
            编辑
          </t-link>
        </template>
      </t-table>
      <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateJzzttz="(item) => query(item)" />
      <div v-show="boxvisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="请确认是否删除所选明细"
          :onConfirm="confirmDelRow"
          :onClose="closeBox"
        >
        </t-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberUtils.js';
import { computeSszq, multiSelectHandle, getSbztBySsqqz } from '@/pages/index/views/util/tzzxTools.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, FileIcon, UploadIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { jzjttzQuery, jzjttzQueryHj, jzjttzDelete } from '@/pages/index/api/tzzx/zzstz/jzjttz.js';
import { querySearchConfig, dataColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    QsbButton,
    ExportButton,
    SearchControlPanel,
    EditDialog,
    AddIcon,
    FileIcon,
    UploadIcon,
    DeleteIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      editDialogVisible: false,
      boxvisible: false,
      querySearchConfig,
      dataColumns,
      tableLoading: false,
      fromName: false,
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      delformData: [],
      footData: [],
      tips: '',
      files: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
      columnsLoading: false,
      remoteColumns: [],
      allowEdit: false,
      editCheckLoading: false,
    };
  },
  created() {},
  mounted() {
    this.initQueryConditions();
    this.fetchTableColumns();
    this.checkAllowEdit();
    this.query({ initQuery: true });
  },
  computed: {
    sszqqToExtract() {
      return dayjs(this.formData.sszqq).format('YYYYMM');
    },
    sszqzToExtract() {
      return dayjs(this.formData.sszqz).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jzjtxxbTz';
    },
    exportFileName() {
      return '即征即退台账';
    },
    tzQueryParams() {
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      djParam.pageNum = this.pagination.current;
      djParam.pageSize = this.pagination.pageSize;
      return {
        ...this.formData,
        sszqq: this.sszqqToExtract,
        sszqz: this.sszqzToExtract,
        zsxmDm: multiSelectHandle(this.formData.zsxmDm),
        jsfsDm1: multiSelectHandle(this.formData.jsfsDm1),
        sl1: multiSelectHandle(this.formData.sl1),
        ...djParam,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.dataColumns;
      }
      return this.remoteColumns;
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);

        if (params.get('skssqq')) {
          this.$refs.queryControl.setParams({
            sszqq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
            sszqz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
          });
          this.$set(this.formData, 'sszqq', dayjs(this.$route.query.skssqq).format('YYYY-MM'));
          this.$set(this.formData, 'sszqz', dayjs(this.$route.query.skssqz).format('YYYY-MM'));
          return;
        }
      }
      this.$refs.queryControl.setParams({
        sszqq: computeSszq(),
        sszqz: computeSszq(),
      });
      this.$set(this.formData, 'sszqq', computeSszq());
      this.$set(this.formData, 'sszqz', computeSszq());
      // 在方法末尾添加回调
      this.$nextTick(() => {
        this.checkAllowEdit();
      });
    },
    clearFiles() {
      this.files = [];
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$message.error(`导入失败`);
      }
      this.clearFiles();
      this.query({ flag: true });
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      if (!formData || !formData.sszqq || !formData.sszqz) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          this.allowEdit =
            dayjs(formData.sszqq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq &&
            dayjs(formData.sszqz).format('YYYY-MM') === sbgzSsyfInfo.ssyfz;
        } else {
          const isDeclared = await getSbztBySsqqz(formData.sszqq, formData.sszqz, 'BDA0610611');
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig || {};

        const defaultColumns = this.dataColumns;

        this.remoteColumns = defaultColumns
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        this.remoteColumns = this.dataColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        sszqq: this.sszqqToExtract,
        sszqz: this.sszqzToExtract,
        ly: '1',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys.length > 0) {
        console.log('selectedRowKeys', this.selectedRowKeys);
        const temp = JSON.stringify(this.tableData.find((t) => t.uuid === this.selectedRowKeys[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }

      this.editDialogVisible = {
        row: JSON.parse(JSON.stringify(selectedRowData)),
        oldkjpzbh: row.kjpzbh,
        otherObj,
        pageType,
      };
      console.log('editDialogVisible', this.editDialogVisible);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.delformData = selectedRowData.filter((i) => i);
    },
    delRow() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请选择要删除的记录');
        return;
      }
      this.boxvisible = true;
    },
    async confirmDelRow() {
      try {
        this.tableLoading = true;
        await jzjttzDelete(this.delformData.map((item) => item.uuid));
        this.$message.success('删除成功');
        this.query({ flag: true });
        this.boxvisible = false;
        this.selectedRowKeys = [];
      } catch (error) {
        console.error('删除失败', error);
        this.$message.error('删除失败');
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange(e) {
      this.pagination.current = e.current;
      this.pagination.pageSize = e.pageSize;
      this.query();
    },
    async query(pm = { flag: false, p: false, fy: false, from: false, initQuery: false }) {
      console.log('query', pm);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) {
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params, zsxmDm: p.zsxmDm1 }; // 起始时间待解决
        this.$refs.queryControl.setParams({
          sszqq: dayjs(p.sszqq).format('YYYY-MM'),
          sszqz: dayjs(p.sszqz).format('YYYY-MM'),
          zsxmDm: (p?.zsxmDm1 ?? '').split(','),
          jsfsDm1: (p?.jsfsDm1 ?? '').split(','),
          // eslint-disable-next-line no-nested-ternary
          sl1: p?.sl1 === 0 ? String(p.sl1 ?? '').split(',') : (p?.sl1 ?? '').toString().split(','),
        });
      } else {
        params = {
          ...this.formData,
          ...params,
          sszqq: this.sszqqToExtract,
          sszqz: this.sszqzToExtract,
          zsxmDm: multiSelectHandle(this.formData.zsxmDm),
          jsfsDm1: multiSelectHandle(this.formData.jsfsDm1),
          sl1: multiSelectHandle(this.formData.sl1),
        };
      }
      try {
        console.log('jzjttz-params', params);
        const { data } = await jzjttzQuery(params);

        this.tableData = data.records || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'sszq', dayjs(item.sszq).format('YYYY-MM'));
        });
        this.pagination.total = data.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await jzjttzQueryHj(params);
          this.footData =
            [
              {
                xxje: data?.xxje === null ? '-' : numberToPrice(data?.xxje),
                xxse: data?.xxse === null ? '-' : numberToPrice(data?.xxse),
                sjkce: data?.sjkce === null ? '-' : numberToPrice(data?.sjkce),
                jxse: data?.jxse === null ? '-' : numberToPrice(data?.jxse),
                sjtse: data?.sjtse === null ? '-' : numberToPrice(data?.sjtse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.error(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    numberToPrice,
    async downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  // padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
