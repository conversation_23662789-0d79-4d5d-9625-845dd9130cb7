import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '一般计税方式', value: '01' },
      { label: '简易计税方式', value: '02' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '货物', value: '01' },
      { label: '劳务', value: '02' },
      { label: '服务', value: '03' },
      { label: '无形资产', value: '04' },
      { label: '不动产', value: '05' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  // {
  //   label: '税率',
  //   key: 'sl1',
  //   type: 'select',
  //   value: '',
  //   multiple: true,
  //   selectList: [
  //     { label: '17%', value: '0.17' },
  //     { label: '16%', value: '0.16' },
  //     { label: '13%', value: '0.13' },
  //     { label: '10%', value: '0.10' },
  //     { label: '9%', value: '0.09' },
  //     { label: '6%', value: '0.06' },
  //     { label: '5%', value: '0.05' },
  //     { label: '3%', value: '0.03' },
  //   ],
  //   placeholder: '请选择',
  //   clearable: true,
  // },
];

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'sszq',
    title: '所属月份',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'jsfsmc',
    title: '计税方式',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zsxmmc',
    title: '征税项目',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'xxje',
    title: '销项金额(不含税)',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'xxse',
    title: '销项税额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'sjkce',
    title: '实际扣除额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'jxse',
    title: '进项税额',
    width: 140,
  },
  // 2025.8.29 《增值税一般纳税人台账需求设计_大连华锐重工v1.2》去掉了进项中的“实际退税额”字段
  // {
  //   align: 'right',
  //   colKey: 'sjtse',
  //   title: '实际退税额',
  //   width: 140,
  // },
  {
    colKey: 'operation',
    title: '操作',
    width: 100,
    fixed: 'right',
    align: 'center',
  },
];
