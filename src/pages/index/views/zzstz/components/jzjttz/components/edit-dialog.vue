<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增即征即退台账', '编辑即征即退台账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择计税方式" clearable>
                <t-option value="01" label="一般计税方式"></t-option>
                <t-option value="02" label="简易计税方式"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm">
              <t-select
                v-model="formData.zsxmDm"
                placeholder="请选择征税项目"
                clearable
                :disabled="!fieldEditableStatus.zsxmDm"
              >
                <t-option value="01" label="货物"></t-option>
                <t-option value="02" label="劳务"></t-option>
                <t-option value="03" label="服务"></t-option>
                <t-option value="04" label="无形资产"></t-option>
                <t-option value="05" label="不动产"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable :disabled="!fieldEditableStatus.sl1">
                <t-option value="0.17" label="17%"></t-option>
                <t-option value="0.16" label="16%"></t-option>
                <t-option value="0.13" label="13%"></t-option>
                <t-option value="0.10" label="10%"></t-option>
                <t-option value="0.09" label="9%"></t-option>
                <t-option value="0.06" label="6%"></t-option>
                <t-option value="0.05" label="5%"></t-option>
                <t-option value="0.03" label="3%"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="销项金额(不含税)" name="xxje">
              <gt-input-money
                v-model="formData.xxje"
                theme="normal"
                align="left"
                :disabled="!fieldEditableStatus.xxje"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="销项税额" name="xxse">
              <gt-input-money
                v-model="formData.xxse"
                theme="normal"
                align="left"
                :disabled="!fieldEditableStatus.xxse"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="实际扣除额" name="sjkce">
              <gt-input-money
                v-model="formData.sjkce"
                theme="normal"
                align="left"
                :disabled="!fieldEditableStatus.sjkce"
                clearable
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="进项税额" name="jxse">
              <gt-input-money
                v-model="formData.jxse"
                theme="normal"
                align="left"
                :disabled="!fieldEditableStatus.jxse"
                clearable
              />
            </t-form-item>
          </t-col>
          <!-- 2025.8.29 《增值税一般纳税人台账需求设计_大连华锐重工v1.2》去掉了进项中的“转出即征即退税额”字段 -->
          <!-- <t-col :span="4">
            <t-form-item label="转出即征即退税额" name="sjtse">
              <gt-input-money
                v-model="formData.sjtse"
                theme="normal"
                align="left"
                :disabled="!isSjtseEditable"
                clearable
              />
            </t-form-item>
          </t-col> -->
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { jzjttzSave, jzjttzUpdate } from '@/pages/index/api/tzzx/zzstz/jzjttz.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    // 初始化基础验证规则
    const initialRules = {
      // 所属月份必填
      sszq: [{ required: true, message: '所属月份必填', type: 'error' }],
    };

    return {
      isVisible: true,
      confirmLoading: false,
      rules: initialRules,
      formData: {
        sszq: '',
        uuid: '',
        jsfsDm1: '',
        zsxmDm: '',
        sl1: '',
        xxje: '',
        xxse: '',
        sjkce: '',
        jxse: '',
        // sjtse: '',
      },
    };
  },
  created() {},
  async mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    // 控制字段可编辑状态的计算属性对象
    fieldEditableStatus() {
      const jsfsDm1Exists = !!this.formData.jsfsDm1;
      return {
        // 计税方式不为空时可编辑的字段
        zsxmDm: jsfsDm1Exists,
        sl1: jsfsDm1Exists,
        xxje: jsfsDm1Exists,
        xxse: jsfsDm1Exists,
        sjkce: jsfsDm1Exists,
        // 计税方式为空时可编辑的字段
        jxse: !jsfsDm1Exists,
        // 注：sjtse字段已在模板中被注释掉
      };
    },
  },
  watch: {
    'formData.jsfsDm1': {
      handler: 'watchJsfsChange',
      immediate: true,
    },
  },
  methods: {
    async init() {
      // 不要覆盖已初始化的验证规则
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.sszq) {
        console.log('this.visible.row', this.visible.row);
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
      }
    },
    // 监听计税方式变化，重置非相关字段
    watchJsfsChange(val) {
      if (val) {
        // 计税方式不为空时，清空进项税额和转出即征即退税额
        this.formData.jxse = '';
        // this.formData.sjtse = '';
        // 添加征税项目必填规则 - 使用响应式方式更新
        this.$set(this.rules, 'zsxmDm', [{ required: true, message: '征税项目必填', type: 'error' }]);
      } else {
        // 计税方式为空时，清空其他字段
        this.formData.zsxmDm = '';
        this.formData.sl1 = '';
        this.formData.xxje = '';
        this.formData.xxse = '';
        this.formData.sjkce = '';
        // 移除征税项目必填规则 - 使用响应式方式更新
        const newRules = { ...this.rules };
        delete newRules.zsxmDm;
        this.rules = newRules;
      }
      // 触发重新验证
      this.$nextTick(() => {
        this.$refs.forms?.clearValidate();
        // 立即触发验证，确保用户能看到验证效果
        this.$refs.forms?.validate('zsxmDm');
      });
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        ['sszq', 'uuid', 'jsfsDm1', 'zsxmDm', 'sl1', 'xxje', 'xxse', 'sjkce', 'jxse'].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });

        // 当计税方式为空时，确保不可编辑字段的值为空
        if (!this.formData.jsfsDm1) {
          p.zsxmDm = '';
          p.sl1 = '';
          p.xxje = '';
          p.xxse = '';
          p.sjkce = '';
        } else {
          // 当计税方式不为空时，确保不可编辑字段的值为空
          p.jxse = '';
          // p.sjtse = '';
        }

        const params = {
          ...p,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };
        try {
          console.log('params', params);
          if (this.visible.pageType) {
            await jzjttzUpdate(params);
          } else {
            await jzjttzSave(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          this.$emit('updateJzzttz', { flag: true });
          this.isVisible = false;
        } catch (e) {
          console.error(e);
          this.$message.error(['新增失败', '修改失败'][this.visible.pageType]);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
