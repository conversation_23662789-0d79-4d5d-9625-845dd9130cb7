<!--
 * @Descripttion: 台账-增值税一般纳税人收入明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-06 15:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl1"
      class="znsbHeadqueryDiv"
      :config="querySearchConfigSrmx"
      :formRules="querySearchConfigOneRules1"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData1 = v)"
    />

    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
          ><AddIcon slot="icon" />新增</t-button
        >
        <t-button theme="primary" @click="delRow" :disabled="!allowEdit"><DeleteIcon slot="icon" />删除</t-button>
        <ExtractDataButton variant="outline" :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef1"
        row-key="uuid"
        hover
        :data="tableData1"
        :columns="tableColumnsSrmx"
        height="100%"
        :loading="tableLoading"
        :pagination="pagination1"
        @page-change="(e) => pageChange(e, 0)"
        :scroll="{ type: 'virtual', rowHeight: 56 }"
        lazyLoad
        :selected-row-keys="selectedRowKeys1"
        @select-change="rehandleSelectChange"
        :foot-data="footData1"
      >
        <template #xh="{ rowIndex }">{{ (pagination1.current - 1) * pagination1.pageSize + rowIndex + 1 }}</template>
        <template #sl1="{ row }">
          {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
        </template>
        <template #xse="{ row }">
          {{ numberToPrice(row.xse) }}
        </template>
        <template #se="{ row }">
          {{ numberToPrice(row.se) }}
        </template>
        <template #bbje="{ row }">
          {{ numberToPrice(row.bbje) }}
        </template>
        <template #operation="{ row }">
          <t-link
            theme="primary"
            hover="color"
            @click="newOrEditRow(row)"
            v-show="row.ly !== '0'"
            :disabled="!allowEdit"
          >
            编辑
          </t-link>
        </template>
      </t-table>
    </div>

    <EditDialog v-if="editDialogVisible1" :visible.sync="editDialogVisible1" @updateSrmx="(item) => query(item)" />

    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { srzzmxQuery, srmxDelete, srzzmxQueryHj } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, ChartIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import {
  computeSszq,
  multiSelectHandle,
  jyssReadyStatusFetch,
  getSbztBySsq,
} from '@/pages/index/views/util/tzzxTools.js';
import EditDialog from './components/edit-dialog.vue';
import { querySearchConfig1, dataColumns1 } from './config.js';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    EditDialog,
    AddIcon,
    DeleteIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      fristQuery: true,
      editDialogVisible1: false,
      sfzjgFlag: false,
      scfxpzLoading: false,
      boxvisible: false,
      querySearchConfig1,
      tableLoading: false,
      fromName: false,
      formData1: {},
      checkBox1: [],
      selectedRowKeys1: [],
      lrzxList: [], // 利润中心
      kjfpList: [], // 分配
      srlxList: [],
      slList: [],
      kmdmList: [],
      tableData1: [],
      delformData: [],
      dataColumns1,
      delUnallowRows: [],
      footData1: [],
      pagination1: { current: 1, pageSize: 10, total: 0 },
      remoteColumnsSrmx: [], // 动态列配置-收入明细
      remotequerySearchConfigSrmx: [], // 动态查询条件配置-收入明细
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
      tips: '',
      files: [],
    };
  },
  created() {},
  mounted() {
    this.fetchTableColumns(); // 初始化表头配置
    this.getQueryParamsList();
    this.initQueryConditions();
    this.checkAllowEdit();
  },
  computed: {
    getQueryParamsKmbmList() {
      // 其他企业，返回默认的科目编码列表
      return [
        { value: '2221010400', label: '应交税费-应交增值税-销项税额' },
        { value: '2221080000', label: '应交税费-增值税简易计税' },
      ];
    },
    querySearchConfigSrmx() {
      let config = [];
      if (!this.remotequerySearchConfigSrmx.length) {
        // 兼容初始化状态
        config = this.querySearchConfig1;
      }
      // 使用动态配置的列
      config = this.remotequerySearchConfigSrmx;
      return config;
    },

    querySearchConfigOneRules1() {
      return {
        sszq: [{ required: true, message: '必填项', type: 'error' }],
      };
    },

    tableColumnsSrmx() {
      let config = this.dataColumns1.filter((item) => item.colKey !== 'sszq');
      if (this.remoteColumnsSrmx.length) {
        // 兼容初始化状态
        config = this.remoteColumnsSrmx;
      }
      return config;
    },

    sszqToExtract() {
      return dayjs(this.formData1.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'srMxwljTz';
    },
    exportFileName() {
      return '收入明细表导出';
    },
    tzQueryParams() {
      const baseParams = this.getBaseQueryParams; // 创建副本，避免直接修改 baseParams
      baseParams.pageNum = this.pagination1.current;
      baseParams.pageSize = this.pagination1.pageSize;

      const cxParam = {
        ...this.formData1,
        sszq: dayjs(this.formData1.sszq).format('YYYYMM'),
        srlxDm: multiSelectHandle(this.formData1.srlxDm),
        jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
        zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
        sl1: multiSelectHandle(this.formData1.sl1),
        kmdm: multiSelectHandle(this.formData1.kmdm),
        lrzx: multiSelectHandle(this.formData1.lrzx),
        ...baseParams,
      };
      return cxParam;
    },
    getBaseQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        pageNum: 0,
        pageSize: 0,
      };
    },
    exportButtonFlag() {
      return !this.tableData1.length;
    },
  },
  watch: {
    formData1: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    // 王老吉分支简化了原始分支的初始化逻辑，这里需要提供一些空函数切换企业时报错
    getLrzx() {},
    initTabValue() {},
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const formData = this.formData1;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    // 动态配置表头信息
    /**
     * 获取动态表头配置
     * 1. 从store获取企业差异化配置
     * 2. 根据当前tab获取默认列配置
     * 3. 根据配置过滤和更新列信息
     * @returns {Array} 处理后的列配置数组
     */
    fetchTableColumns() {
      this.fetchQueryParamsConfig();
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultColumnsSrmx = JSON.parse(JSON.stringify(this.dataColumns1));
        // 处理列配置
        this.remoteColumnsSrmx = defaultColumnsSrmx
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRMX] 获取表头配置失败:', e);
        // 失败时回退到默认配置
        this.remoteColumnsSrmx = this.dataColumns1;
      }
    },
    // 动态配置查询条件信息
    fetchQueryParamsConfig() {
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultQueryParamsColumnsSrmx = JSON.parse(JSON.stringify(this.querySearchConfig1));

        // 处理查询条件配置
        this.remotequerySearchConfigSrmx = defaultQueryParamsColumnsSrmx
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRMX] 获取查询条件配置失败:', e);
        // 失败时回退到默认配置
        const defaultQueryParamsColumnsSrmx = JSON.parse(JSON.stringify(this.querySearchConfig1));
        this.remotequerySearchConfigSrmx = defaultQueryParamsColumnsSrmx;
      }
    },

    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);

        if (params.get('skssqq')) {
          this.$refs.queryControl1.setParams({
            sszq: dayjs(params.get('skssqq')).format('YYYY-MM'),
          });
          this.formData1.sszq = computeSszq();
          return;
        }
      }
      this.$refs.queryControl1.setParams({
        sszq: computeSszq(),
      });
      this.formData1.sszq = computeSszq();
      // 在方法末尾添加回调
      this.$nextTick(() => {
        this.checkAllowEdit();
      });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        // eslint-disable-next-line no-nested-ternary
        sszq: pageType ? row.sszq : dayjs(this.formData1.sszq).format('YYYYMM'),
        ly: '1',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys1.length > 0) {
        console.log('selectedRowKeys1', this.selectedRowKeys1);
        const temp = JSON.stringify(this.tableData1.find((t) => t.uuid === this.selectedRowKeys1[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }

      this.editDialogVisible1 = {
        row: JSON.parse(JSON.stringify(selectedRowData)),
        oldkjpzbh: row.kjpzbh,
        otherObj,
        pageType,
        lrzxList: this.lrzxList,
      };
      console.log('editDialogVisible1', this.editDialogVisible1);
    },
    async delRow() {
      if (!this.selectedRowKeys1.length) {
        this.$message.warning('请先选中要删除的行');
      } else if (this.checkSelectedRowsDate()) {
        this.$message.warning(
          `第${this.delUnallowRows.join(`、`)}行的信息为原始凭证信息，不能删除，请取消选中后再进行删除操作。`,
        );
        this.delUnallowRows = [];
      } else {
        this.boxvisible = true;
      }
    },
    checkSelectedRowsDate() {
      this.delUnallowRows = [];
      this.checkBox1.forEach((item) => {
        const index = this.tableData1.findIndex((i) => i.uuid === item.uuid);
        if (this.tableData1[index].ly === '0') {
          this.delUnallowRows.push(index + 1);
        }
      });
      return this.delUnallowRows.length;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox1.forEach((item) => {
        const index = this.tableData1.findIndex((i) => i.uuid === item.uuid);
        this.tableData1.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox1 = [];
      this.selectedRowKeys1 = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys1 = value;
      this.checkBox1 = selectedRowData.filter((i) => i);
    },
    async getQueryParamsList() {
      try {
        const { lrzxlist, sllist, kmdmlist } =
          this.$store.state.zdmczh.companyDifferentiationConfig?.tablelistConfig[0];

        // 先初始化静态数据
        this.jsfsList = [
          { value: '01', label: '一般计税方法计税' },
          { value: '02', label: '简易计税方法计税' },
          { value: '03', label: '免抵退税' },
          { value: '04', label: '免税' },
        ];
        this.zsxm1List = [
          { value: '01', label: '货物' },
          { value: '02', label: '劳务' },
          { value: '03', label: '服务' },
          { value: '04', label: '无形资产' },
          { value: '05', label: '不动产' },
        ];
        this.srlxList = [
          { value: '110', label: '未开票收入' },
          { value: '120', label: '视同销售收入' },
          { value: '130', label: '主营业务收入' },
          { value: '140', label: '主营业务成本' },
        ];
        this.lrzxList = lrzxlist.map((value) => ({
          value: value.lrzxdm,
          label: value.lrzxmc,
        }));
        this.slList = sllist.map((value) => ({
          value,
          label: `${(value * 100).toFixed(0)}%`,
        }));
        this.kmdmList = kmdmlist.map((value) => ({
          value: value.kmdm,
          label: value.kmmc,
        }));

        this.getQueryParamsListSrmx();
        console.log('[SRMX] 组件初始化完成');
      } catch (e) {
        console.error(e);
      }
    },
    getQueryParamsListSrmx() {
      (this.querySearchConfigSrmx.find((c) => c.key === 'srlxDm') || {}).selectList = this.srlxList;
      (this.querySearchConfigSrmx.find((c) => c.key === 'jsfsDm1') || {}).selectList = this.jsfsList;
      (this.querySearchConfigSrmx.find((c) => c.key === 'zsxmDm1') || {}).selectList = this.zsxm1List;
      (this.querySearchConfigSrmx.find((c) => c.key === 'lrzx') || {}).selectList = this.lrzxList.map((d) => ({
        label: `${d.value} | ${d.label}`,
        value: d.value,
      }));
      (this.querySearchConfigSrmx.find((c) => c.key === 'kmdm') || {}).selectList = this.kmdmList.map((d) => ({
        label: `${d.value} | ${d.label}`,
        value: d.value,
      }));
      (this.querySearchConfigSrmx.find((c) => c.key === 'sl1') || {}).selectList = this.slList.map((d) => ({
        label: d.label,
        value: String(d.value),
      }));
    },
    getQueryParamsListXxspztz() {
      (this.querySearchConfigXxspztz.find((c) => c.key === 'jsfsDm') || {}).selectList = this.jsfsList;
      (this.querySearchConfigXxspztz.find((c) => c.key === 'zsxmDm1') || {}).selectList = this.zsxm1List;
      (this.querySearchConfigXxspztz.find((c) => c.key === 'sl1') || {}).selectList = this.slList.map((d) => ({
        label: d.label,
        value: String(d.value),
      }));
      (this.querySearchConfigXxspztz.find((c) => c.key === 'kmbm') || {}).selectList = this.getQueryParamsKmbmList;
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async query(pm = { flag: false, p: false, fy: false, from: false, initQuery: false }) {
      console.log('query', pm);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) {
        this.pagination1.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination1.current,
        pageSize: this.pagination1.pageSize,
      };
      if (p) {
        params = { ...p, ...params, zsxmDm1: p?.zsxmDm1 === '01,02' ? '' : p.zsxmDm1 }; // 起始时间待解决
        this.$refs.queryControl1.setParams({
          sszq: String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6))),
          lrzx: (p?.lrzx ?? '').split(','),
          srlxDm: (p?.srlxDm ?? '').split(','),
          jsfsDm1: (p?.jsfsDm1 ?? '').split(','),
          zsxmDm1: p?.zsxmDm1 === '01,02' ? '' : (p?.zsxmDm1 ?? '').split(','),
          sl1: p?.sl1 === 0 ? String(p.sl1 ?? '').split(',') : (p?.sl1 ?? '').toString().split(','),
          kmdm: (p?.kmdm ?? '').split(','),
        });
      } else {
        params = {
          ...this.formData1,
          ...params,
          sszq: dayjs(this.formData1.sszq).format('YYYYMM'),
          srlxDm: multiSelectHandle(this.formData1.srlxDm),
          zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
          jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
          sl1: multiSelectHandle(this.formData1.sl1),
          kmdm: multiSelectHandle(this.formData1.kmdm),
          lrzx: multiSelectHandle(this.formData1.lrzx),
        };
      }

      try {
        // 收入明细数据查询
        console.log('srmx-params', params);
        const { data } = await srzzmxQuery(params);

        this.tableData1 = data.records || [];
        this.tableData1.forEach((item, index) => {
          this.$set(item, 'index', index);
          // 收入类型为主营业务收入和其他业务收入时，数据不展示分配
          if (item.srlxDm === '110' || item.srlxDm === '120') {
            this.$set(item, 'kjfpmc', '');
          } else {
            this.$set(item, 'kjfpmc', item.kjfpMc ? `${item.kjfp} | ${item.kjfpMc}` : item.kjfp);
          }
        });
        this.pagination1.total = data.pageTotal;

        if (this.pagination1.total > 0) {
          const { data } = await srzzmxQueryHj(params);
          this.footData1 =
            [
              {
                se: numberToPrice(data?.se),
                xse: numberToPrice(data?.xse),
                bbje: numberToPrice(data?.bbje),
              },
            ] || [];
        } else {
          this.footData1 = [];
        }
        jyssReadyStatusFetch(
          this.$store.state.zzstz.userInfo?.djxh || '',
          this.$store.state.zzstz.userInfo?.nsrsbh || '',
          this.formData1.sszq,
        );
      } catch (e) {
        console.error(e);
        this.tableData1 = [];
        this.footData1 = [];
      } finally {
        this.tableLoading = false;
        this.fristQuery = false;
      }
    },
    async delete(params) {
      try {
        const res = await srmxDelete(params);
        this.$message.success(res.msg);
      } catch (e) {
        console.error('[SRMX] 操作失败:', e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      // 更新当前页码和每页条数
      this.pagination1.current = pageSize !== this.pagination1.pageSize ? 1 : current;
      this.pagination1.pageSize = pageSize;

      // 触发查询
      this.query({ fy: true });
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
// 添加提示文字样式
.tz-tip {
  margin-left: 10px;
  font-size: 14px;
  line-height: 32px; // 与按钮高度对齐
}
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
