import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmdm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
];
export const dataColumns1 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'gsh2',
    title: '公司号',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'lrzx',
    title: '利润中心',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmdm',
    title: '科目编码',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmmc',
    title: '科目名称',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kjpzbh',
    title: '会计凭证编号',
    width: 210,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'srlxmc',
    title: '收入类型',
    width: 120,
  },
  {
    width: 180,
    colKey: 'jsfsmc',
    title: '计税方式',
  },
  {
    width: 100,
    colKey: 'zsxmmc',
    title: '征税项目',
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'xse',
    title: '金额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '税额',
    width: 140,
  },
  {
    width: 200,
    colKey: 'wbzlmc',
    title: '文本',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];

export const sl1List = [
  { value: 0.17, label: '17%' },
  { value: 0.16, label: '16%' },
  { value: 0.13, label: '13%' },
  { value: 0.1, label: '10%' },
  { value: 0.09, label: '9%' },
  { value: 0.06, label: '6%' },
  { value: 0.05, label: '5%' },
  { value: 0.03, label: '3%' },
  { value: 0.0, label: '0%' },
];
