<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增收入明细账', '编辑收入明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="收入类型" name="srlxDm">
              <t-select v-model="formData.srlxDm" placeholder="请选择收入类型" clearable @change="srlxChange">
                <t-option
                  v-for="item in srlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择计税方式" clearable>
                <t-option
                  v-for="item in jsfsList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm1">
              <t-select v-model="formData.zsxmDm1" placeholder="请选择征税项目" clearable>
                <t-option
                  v-for="item in zsxm1List"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="参考凭证编号" name="ckpzh">
              <t-input :maxlength="30" v-model="formData.ckpzh" placeholder="请填写参考凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable>
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="金额" name="xse">
              <gt-input-money v-model="formData.xse" placeholder="请填写金额" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money v-model="formData.se" placeholder="请填写税额" theme="normal" align="left" clearable
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="文本" name="wbzlmc">
              <t-input :maxlength="100" v-model="formData.wbzlmc" placeholder="请填写文本" clearable
            /></t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { srmxSave, srmxUpdate } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      srlxDm: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm1: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm1: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      srlxList: [],
      jsfsList: [],
      zsxm1List: [],
      slList: [],
      kmdmList: [],
      kmdmListCls: [],
      srlxkmdmldlist: [],
      formDataTemp: {},
      formData: {},
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
      };
    },
  },
  watch: {},
  methods: {
    // 收入类型变化时处理科目代码绑定
    srlxChange(val) {
      if (!val) {
        // 如果收入类型为空，清空科目代码
        this.$set(this.formData, 'kmdm', '');
        this.$set(this.formData, 'kmmc', '');
        return;
      }

      // 获取当前收入类型对应的科目代码列表
      const currentSrlx = this.srlxkmdmldlist.find((item) => item.srlx === val);

      if (currentSrlx && currentSrlx.kmdm && currentSrlx.kmdm.length > 0) {
        // 过滤科目代码列表，只显示当前收入类型允许的科目代码
        this.kmdmList = this.kmdmListCls.filter((km) => currentSrlx.kmdm.includes(km.value));

        // 如果只有一个科目代码，则自动选中
        if (currentSrlx.kmdm.length === 1) {
          const defaultKmdm = currentSrlx.kmdm[0];
          this.$set(this.formData, 'kmdm', defaultKmdm);
          this.$set(this.formData, 'kmmc', this.kmdmListCls.find((item) => item.value === defaultKmdm)?.label);
        } else {
          // 有多个科目代码，清空当前选择让用户重新选择
          this.$set(this.formData, 'kmdm', '');
          this.$set(this.formData, 'kmmc', '');
        }
      } else {
        // 如果没有找到对应关系，清空科目代码
        this.$set(this.formData, 'kmdm', '');
        this.$set(this.formData, 'kmmc', '');
      }
    },
    // 修改init方法中的异步调用顺序
    async init() {
      this.rules = this.baseRules;
      const { lrzxlist, sllist, kmdmlist, srlxkmdmldlist } =
        this.$store.state.zdmczh.companyDifferentiationConfig?.tablelistConfig[0];

      // 先初始化静态数据
      this.jsfsList = [
        { value: '01', label: '一般计税方法计税' },
        { value: '02', label: '简易计税方法计税' },
        { value: '03', label: '免抵退税' },
        { value: '04', label: '免税' },
      ];
      this.zsxm1List = [
        { value: '01', label: '货物' },
        { value: '02', label: '劳务' },
        { value: '03', label: '服务' },
        { value: '04', label: '无形资产' },
        { value: '05', label: '不动产' },
      ];
      this.srlxList = [
        { value: '110', label: '未开票收入' },
        { value: '120', label: '视同销售收入' },
        { value: '130', label: '主营业务收入' },
        { value: '140', label: '主营业务成本' },
      ];
      this.lrzxList = lrzxlist.map((value) => ({
        value: value.lrzxdm,
        label: value.lrzxmc,
      }));
      this.slList = sllist.map((value) => ({
        value,
        label: `${(value * 100).toFixed(0)}%`,
      }));
      this.kmdmListCls = kmdmlist.map((value) => ({
        value: value.kmdm,
        label: value.kmmc,
      }));
      this.kmdmList = this.kmdmListCls;
      this.srlxkmdmldlist = srlxkmdmldlist;

      // 同步设置表单数据
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.srlxDm) {
        await this.editInitSelectedList(this.visible.row);
        const originData = JSON.parse(JSON.stringify(this.visible.row));
        this.formDataTemp = originData;
        console.log('originData', originData);
        this.formData = { ...originData, sszq: dayjs(String(originData.sszq)).format('YYYY-MM') }; // 保持引用独立
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'lrzx',
          'kjpzbh',
          'ckpzh',
          'srlxDm',
          'sl1',
          'jsfsDm1',
          'zsxmDm1',
          'kmdm',
          'kmmc',
          'tzyy',
          'wbzlmc',
          'se',
          'xse',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };

        try {
          if (this.visible.pageType) {
            await srmxUpdate(params);
          } else {
            await srmxSave(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateSrmx', { flag: true });
          } else {
            this.$emit('updateSrmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    getXzKmmc(kmdm) {
      this.$set(this.formData, 'kmmc', this.kmdmListCls.find((item) => item.value === kmdm)?.label);
    },
    // 初始化编辑时的已选列表
    async editInitSelectedList(row) {
      if (row && row.srlxDm) {
        // 调用srlxChange方法应用收入类型和科目代码的绑定关系
        this.srlxChange(row.srlxDm);

        // 确保保存的科目代码在当前收入类型允许的列表中
        const currentSrlx = this.srlxkmdmldlist.find((item) => item.srlx === row.srlxDm);

        if (currentSrlx && currentSrlx.kmdm && currentSrlx.kmdm.includes(row.kmdm)) {
          // 如果保存的科目代码在允许的列表中，则保留
          this.$set(this.formData, 'kmdm', row.kmdm);
          this.$set(this.formData, 'kmmc', this.kmdmListCls.find((item) => item.value === row.kmdm)?.label);
        } else if (currentSrlx && currentSrlx.kmdm && currentSrlx.kmdm.length > 0) {
          // 如果保存的科目代码不在允许的列表中，则使用默认值
          const defaultKmdm = currentSrlx.kmdm[0];
          this.$set(this.formData, 'kmdm', defaultKmdm);
          this.$set(this.formData, 'kmmc', this.kmdmListCls.find((item) => item.value === defaultKmdm)?.label);
        }
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
.t-form div:nth-child(7) {
  /deep/.t-popup__content {
    width: 350px !important;
  }
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
