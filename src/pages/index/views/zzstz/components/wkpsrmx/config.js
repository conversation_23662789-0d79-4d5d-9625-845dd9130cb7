import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '会计凭证编号',
    key: 'kjpzbh',
    type: 'input',
    value: '',
    placeholder: '请输入文字',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '一般计税方式', value: '01' },
      { label: '简易计税方式', value: '02' },
      { label: '免抵退税', value: '03' },
      { label: '免税', value: '04' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '货物', value: '01' },
      { label: '劳务', value: '02' },
      { label: '服务', value: '03' },
      { label: '无形资产', value: '04' },
      { label: '不动产', value: '05' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    value: '',
    multiple: true,
    selectList: [
      { label: '13%', value: '0.13' },
      { label: '9%', value: '0.09' },
      { label: '6%', value: '0.06' },
      { label: '5%', value: '0.05' },
      { label: '3%', value: '0.03' },
      { label: '1.5%', value: '0.015' },
      { label: '1%', value: '0.01' },
      { label: '0.5%', value: '0.005' },
      { label: '0%', value: '0' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '文本',
    key: 'wbzlmc',
    type: 'input',
    value: '',
    placeholder: '请输入文字',
    clearable: true,
  },
];

export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'sszq',
    title: '所属月份',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'gsh2',
    title: '公司代码',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'lrzx',
    title: '利润中心',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmdm',
    title: '科目编码',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmmc',
    title: '科目名称',
    width: 300,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kjpzbh',
    title: '会计凭证编号',
    width: 210,
  },
  {
    colKey: 'jsfsmc',
    title: '计税方式',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'zsxmmc',
    title: '征税项目',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'xse',
    title: '金额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '税额',
    width: 140,
  },
  {
    colKey: 'wbzlmc',
    title: '文本',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 100,
    foot: '-',
    fixed: 'right',
  },
];
