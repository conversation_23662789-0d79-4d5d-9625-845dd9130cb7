<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增未开票收入明细', '编辑未开票收入明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="利润中心" name="lrzx">
              <t-select v-model="formData.lrzx" placeholder="请选择利润中心" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <!-- <t-col :span="4">
            <t-form-item label="会计凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写会计凭证编号"></t-input>
            </t-form-item>
          </t-col> -->
          <t-col :span="4">
            <t-form-item label="计税方式" name="jsfsDm1">
              <t-select v-model="formData.jsfsDm1" placeholder="请选择计税方式" clearable>
                <t-option value="01" label="一般计税方式"></t-option>
                <t-option value="02" label="简易计税方式"></t-option>
                <t-option value="03" label="免抵退税"></t-option>
                <t-option value="04" label="免税"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="征税项目" name="zsxmDm1">
              <t-select v-model="formData.zsxmDm1" placeholder="请选择征税项目" clearable>
                <t-option value="01" label="货物"></t-option>
                <t-option value="02" label="劳务"></t-option>
                <t-option value="03" label="服务"></t-option>
                <t-option value="04" label="无形资产"></t-option>
                <t-option value="05" label="不动产"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable>
                <t-option value="0.13" label="13%"></t-option>
                <t-option value="0.09" label="9%"></t-option>
                <t-option value="0.06" label="6%"></t-option>
                <t-option value="0.05" label="5%"></t-option>
                <t-option value="0.03" label="3%"></t-option>
                <t-option value="0.015" label="1.5%"></t-option>
                <t-option value="0.01" label="1%"></t-option>
                <t-option value="0.005" label="0.5%"></t-option>
                <t-option value="0" label="0%"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="金额" name="xse">
              <gt-input-money v-model="formData.xse" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="文本" name="wbzlmc">
              <t-input :maxlength="100" v-model="formData.wbzlmc" placeholder="请填写文本" clearable
            /></t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable
            /></t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { wkpmxSave, wkpmxUpdate } from '@/pages/index/api/tzzx/zzstz/wkpsrmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      // lrzx: [{ required: true, message: '必填', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      jsfsDm1: [{ required: true, message: '必填', type: 'error' }],
      zsxmDm1: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
      kmmc: [{ required: true, message: '必填', type: 'error' }],
      xse: [{ required: true, message: '必填', type: 'error' }],
      se: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      kmdmList: [],
      formData: {
        sszq: '',
        uuid: '',
        gsh2: '',
        lrzx: '',
        // kjpzbh: '',
        jsfsDm1: '',
        zsxmDm1: '',
        sl1: '',
        kmdm: '',
        kmmc: '',
        xse: '',
        se: '',
        wbzlmc: '',
        tzyy: '',
      },
    };
  },
  created() {},
  async mounted() {
    await this.getLrzx();
    this.getKmdm();
    this.init();
  },
  computed: {
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.sszq) {
        console.log('this.visible.row', this.visible.row);
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
      }
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      }
    },
    async getKmdm() {
      // 这里需要根据实际业务获取科目编码
      this.kmdmList = [
        { label: '主营业务收入-主营未开票确认收入（纯外部）', value: '51010125' },
        { label: '未开票收入', value: '51010126' },
        { label: '主营业务收入-暂估收入', value: '51010103' },
        { label: '其他业务未开票确认收入', value: '51021801' },
        { label: '应交税费—应交增值税—销项税额', value: '21710110' },
      ];
      if (this.kmdmList.length > 0) {
        this.formData.kmdm = this.kmdmList[0].value;
        this.getXzKmmc(this.formData.kmdm);
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'lrzx',
          // 'kjpzbh',
          'jsfsDm1',
          'zsxmDm1',
          'sl1',
          'kmdm',
          'kmmc',
          'xse',
          'se',
          'wbzlmc',
          'tzyy',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };
        try {
          console.log('params', params);
          if (this.visible.pageType) {
            await wkpmxUpdate(params);
          } else {
            await wkpmxSave(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          this.$emit('updateWkpsrmx', { flag: true });
          this.isVisible = false;
        } catch (e) {
          console.error(e);
          this.$message.error(['新增失败', '修改失败'][this.visible.pageType]);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    getXzKmmc(kmdm) {
      this.formData.kmmc = this.kmdmList.find((item) => item.value === kmdm)?.label;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
