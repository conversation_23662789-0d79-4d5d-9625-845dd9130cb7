<!--
 * @Descripttion: 台账-增值税一般纳税人进项发票明细账
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue">
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        style="margin-top: 16px"
        ref="queryControl1"
        :config="filteredQuerySearchConfig1"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
      />
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        ref="queryControl2"
        :config="querySearchConfig2"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <ExtractDataButton
            :sszq="sszqToExtract"
            :ywtsMsg="'发票数据'"
            :readyStatus="readyStatus"
            @query="query"
            :isJxfpmx="true"
          />
          <!-- <t-button v-if="tabValue === '1'" theme="primary" @click="patchSave(true)">批量设置为即征即退</t-button>
          <t-button v-if="tabValue === '1'" theme="primary" @click="patchSave(false)">批量设置为非即征即退</t-button> -->
          <t-button variant="outline" theme="primary" @click="ckdg()" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="0" label="进项发票明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            hover
            :data="xxfpmxList"
            :columns="filteredFpmxColumns"
            @select-change="rehandleSelectChange"
            :pagination="pagination"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 0)"
            :foot-data="jxfpmxFootData"
          >
            <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
            <!-- <template #fphm="{ row }">
            <div>
              <t-button class="tableButton" variant="text" theme="primary" @click="showPdf">{{ row.fphm }}</t-button>
              <t-image-viewer v-model="visible" :images="[picSrc]"> </t-image-viewer>
            </div>
          </template> -->
            <template #fplxDm="{ row }">
              {{
                (fplxList.find((d) => d.value === row.fplxDm) && fplxList.find((d) => d.value === row.fplxDm).label) ||
                ''
              }}
            </template>
            <template #hxytDm="{ row }">
              {{
                (hxytList.find((d) => d.value === row.hxytDm) && hxytList.find((d) => d.value === row.hxytDm).label) ||
                ''
              }}
            </template>
            <template #bdkyyDm="{ row }"
              >{{
                (bdkyyList.find((d) => d.value === row.bdkyyDm) &&
                  bdkyyList.find((d) => d.value === row.bdkyyDm).label) ||
                ''
              }}
            </template>
            <template #je="{ row }">
              <span>{{ numberToPrice(row.je) }}</span>
            </template>
            <template #se="{ row }">
              <span>{{ numberToPrice(row.se) }}</span>
            </template>
            <template #jshj="{ row }">
              <span>{{ numberToPrice(row.jshj) }}</span>
            </template>
            <template #dkje="{ row }">
              <span>{{ numberToPrice(row.dkje) }}</span>
            </template>
            <template #dkse="{ row }">
              <span>{{ numberToPrice(row.dkse) }}</span>
            </template>
            <template #cezsbz="{ row }">
              {{ { Y: '是', N: '否' }[row.cezsbz] || '' }}
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="进项发票货物服务明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            hover
            :data="hwfpmxList"
            :columns="hwfpmxColumns"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="hwpagination"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 1)"
            :foot-data="jxfphwmxFootData"
          >
            <template #xh="{ rowIndex }">{{
              (hwpagination.current - 1) * hwpagination.pageSize + rowIndex + 1
            }}</template>
            <!-- <template #fphm="{ row }">
            <span class="specText" @click="showPdf">{{ row.fphm }}</span>
          </template> -->
            <template #fplxDm="{ row }">
              {{
                (fplxList.find((d) => d.value === row.fplxDm) && fplxList.find((d) => d.value === row.fplxDm).label) ||
                ''
              }}
            </template>
            <template #jsfsDm="{ row }">
              {{
                (zsfsList.find((d) => d.code === row.jsfsDm) && zsfsList.find((d) => d.code === row.jsfsDm).caption) ||
                ''
              }}
            </template>
            <template #zsxmDm="{ row }">
              {{
                (zsxmList.find((d) => d.value === row.zsxmDm) && zsxmList.find((d) => d.value === row.zsxmDm).label) ||
                ''
              }}
            </template>
            <template #sl="{ row }">
              {{ (row.sl && row.sl * 100 + '%') || '' }}
            </template>
            <template #fpspdj="{ row }">
              <span>{{ numberToPrice(row.fpspdj) }}</span>
            </template>
            <template #je="{ row }">
              <span>{{ numberToPrice(row.je) }}</span>
            </template>
            <template #se="{ row }">
              <span>{{ numberToPrice(row.se) }}</span>
            </template>
            <template #jshj="{ row }">
              <span>{{ numberToPrice(row.jshj) }}</span>
            </template>
            <template #jzjtbz="{ row }">
              <div v-if="opFlag && row.uuid === currentRowUuid">
                <t-select v-model="currentSfwjzjt">
                  <t-option key="Y" label="是" value="Y" />
                  <t-option key="N" label="否" value="N" />
                </t-select>
              </div>
              <div v-else>
                <span>{{ { Y: '是', N: '否' }[row.jzjtbz] || '-' }}</span>
              </div>
            </template>
            <template #operation="{ row }">
              <div v-if="opFlag && row.uuid === currentRowUuid">
                <t-button
                  style="margin-right: 5px"
                  class="tableButton"
                  variant="text"
                  theme="primary"
                  @click="opSave(row)"
                  >保存</t-button
                >
                <t-button class="tableButton" variant="text" theme="primary" @click="opFlag = false">取消</t-button>
              </div>
              <div v-else>
                <t-button
                  class="tableButton"
                  variant="text"
                  theme="primary"
                  @click="
                    () => {
                      opFlag = true;
                      currentRowUuid = row.uuid;
                    }
                  "
                  >编辑</t-button
                >
              </div>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
    <Modal :visible="isModalVisible" @close="isModalVisible = false">
      <PdfViewer :pdfUrl="pdfUrl" />
    </Modal>
  </div>
</template>
<script>
import {
  initJxfpMxQuery,
  initJxfpHwfwMxQuery,
  initJxfpMxQueryHj,
  initJxfpHwfwMxQueryHj,
} from '@/pages/index/api/tzzx/zzstz/jxfp.js';
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import { ChartIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import {
  fpmxColumns,
  hwfpmxColumns,
  querySearchConfig1,
  querySearchConfig2,
  querySearchConfigOneRules,
} from './config.js';
import { slList, zsxmList, zsfsList, fplxList, dkztList, bdkyyList, hxytList } from '../../config';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    Modal,
    PdfViewer,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.bdkyyList = bdkyyList;
    this.slList = slList;
    this.zsxmList = zsxmList;
    this.zsfsList = zsfsList;
    this.fplxList = fplxList;
    this.dkztList = dkztList;
    this.bdkyyList = bdkyyList;
    this.fpmxColumns = fpmxColumns;
    this.hwfpmxColumns = hwfpmxColumns;
    this.hxytList = hxytList;
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      querySearchConfig1,
      querySearchConfig2,
      querySearchConfigOneRules,
      picSrc: `${document.location.origin}/znsb/view/tzzx/fppy.png`,
      visible: false,
      tabValue: '0',
      formData1: {},
      formData2: {},
      xxfpmxList: [],
      hwfpmxList: [],
      checkBox: [],
      selectedRowKeys: [],
      jxfphwmxFootData: [],
      jxfpmxFootData: [],
      tableLoading: false,
      opFlag: false,
      currentSfwjzjt: '',
      currentRowUuid: '',
      pdfUrl: '',
      isModalVisible: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      hwpagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      fromName: false,
      loadedTabs: { 0: false, 1: false }, // 新增tab加载状态
    };
  },
  created() {
    this.querySearchConfig1[0].value = computeSszq();
    this.querySearchConfig2[0].value = computeSszq();
    this.formData1.sszq = computeSszq();
    this.formData2.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.querySearchConfig2[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData1.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
        this.formData2.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
    this.init();
  },
  computed: {
    sszqC() {
      return this.tabValue * 1
        ? dayjs(this.formData2.sszq).format('YYYYMM')
        : dayjs(this.formData1.sszq).format('YYYYMM');
    },
    sszqToExtract() {
      return dayjs(this.tabValue * 1 ? this.formData2.sszq : this.formData1.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return this.tabValue * 1 ? 'jxfpHwfwMxTz' : 'jxfpMxTz';
    },
    exportFileName() {
      return this.tabValue * 1 ? '进项发票货物服务明细总账' : '进项发票明细台账';
    },
    tzQueryParams() {
      const currentFormData = this.tabValue * 1 ? this.formData2 : this.formData1;
      const cxParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...(this.tabValue * 1
          ? { ...this.formData2, sl: this.formData2.sl ? this.formData2.sl : null }
          : this.formData1),
        sszq: this.sszqC,
        fplxDm: multiSelectHandle(currentFormData.fplxDm),
        hxytDm: multiSelectHandle(currentFormData.hxytDm),
        sl: multiSelectHandle(currentFormData.sl),
        pageNum: this.tabValue * 1 ? this.hwpagination.current : this.pagination.current,
        pageSize: this.tabValue * 1 ? this.hwpagination.pageSize : this.pagination.pageSize,
      };
      return cxParam;
    },
    exportButtonFlag() {
      return this.tabValue * 1 ? !this.hwfpmxList.length : !this.xxfpmxList.length;
    },
    dalianzhonggongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000002';
    },
    // 动态过滤表格列配置，只在dalianzhonggongFlag为true时显示sfkyjjdj字段
    filteredFpmxColumns() {
      if (this.dalianzhonggongFlag) {
        return this.fpmxColumns;
      }
      return this.fpmxColumns.filter((column) => column.colKey !== 'sfkyjjdj');
    },
    // 动态过滤查询条件配置，只在dalianzhonggongFlag为true时显示sfkyjjdj字段
    filteredQuerySearchConfig1() {
      if (this.dalianzhonggongFlag) {
        return this.querySearchConfig1;
      }
      return this.querySearchConfig1.filter((config) => config.key !== 'sfkyjjdj');
    },
  },
  mounted() {},
  // 添加tab切换监听
  watch: {
    tabValue(newVal) {
      if (!this.loadedTabs[newVal]) {
        this.query({ initQuery: true });
        this.loadedTabs[newVal] = true;
      }
    },
  },
  methods: {
    initTabValue() {
      this.tabValue = '0';
      console.log('initTabValue', this.tabValue);
    },
    init() {
      // todo 调接口获取字典
      this.querySearchConfig1[1].selectList = this.fplxList;
      this.querySearchConfig1[5].selectList = this.hxytList;
      this.querySearchConfig2[1].selectList = this.fplxList;
      this.querySearchConfig2[2].selectList = this.slList.map((d) => ({ label: d.caption, value: String(d.code) }));
    },
    async query(pm = { flag: false, p: false, initQuery: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      // p为父页面传参
      if (flag) this.tabValue * 1 ? (this.hwpagination.current = 1) : (this.pagination.current = 1);
      this.tableLoading = true;
      let params = {
        pageNum: this.tabValue * 1 ? this.hwpagination.current : this.pagination.current,
        pageSize: this.tabValue * 1 ? this.hwpagination.pageSize : this.pagination.pageSize,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      };
      if (p) {
        console.log('p', p);
        if (p.type) {
          this.tabValue = '1';
          params = { ...p, ...params };
          if (p.sszq) {
            p.sszq = dayjs(p.sszq).format('YYYY-MM');
          }
          this.$refs.queryControl2.setParams(p);
        } else {
          this.tabValue = '0';
          params = { ...p, ...params };
          if (p.sszq) {
            p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
          }
          this.$refs.queryControl1.setParams(p);
        }
      } else {
        const currentFormData = [this.formData1, this.formData2][this.tabValue * 1];
        params = {
          ...[this.formData1, this.formData2][this.tabValue * 1],
          sszq: this.sszqC,
          fplxDm: multiSelectHandle(currentFormData.fplxDm),
          hxytDm: multiSelectHandle(currentFormData.hxytDm),
          sl: multiSelectHandle(currentFormData.sl),
          ...params,
        };
      }
      // 增加缓存判断逻辑
      if (this.loadedTabs[this.tabValue] && !pm.initQuery && !pm.flag) {
        this.tableLoading = false;
        return;
      }
      try {
        // 拆分两个tab的独立查询逻辑
        if (this.tabValue === '0') {
          const res = await initJxfpMxQuery(params);
          const { data } = res;
          this.xxfpmxList = data.records || [];
          this.pagination.total = data.pageTotal;
          this.loadedTabs['0'] = true;

          if (this.pagination.total > 0) {
            const { data } = await initJxfpMxQueryHj(params);
            this.jxfpmxFootData =
              [
                {
                  je: numberToPrice(data?.je),
                  se: numberToPrice(data?.se),
                  jshj: numberToPrice(data?.jshj),
                  dkje: numberToPrice(data?.dkje),
                  dkse: numberToPrice(data?.dkse),
                },
              ] || [];
          } else {
            this.jxfpmxFootData = [];
          }
        } else if (this.tabValue === '1') {
          const res = await initJxfpHwfwMxQuery(params);
          const { data } = res;
          this.hwfpmxList = data.records || [];
          this.hwpagination.total = data.pageTotal;
          this.loadedTabs['1'] = true;

          if (this.hwpagination.total > 0) {
            const { data } = await initJxfpHwfwMxQueryHj(params);
            this.jxfphwmxFootData =
              [
                {
                  je: numberToPrice(data?.je),
                  se: numberToPrice(data?.se),
                  jshj: numberToPrice(data?.jshj),
                },
              ] || [];
          } else {
            this.jxfphwmxFootData = [];
          }
        }
      } catch (e) {
        [this.xxfpmxList, this.hwfpmxList][this.tabValue * 1] = [];
      } finally {
        this.tableLoading = false;
      }
    },
    showPdf() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.checkBox = selectedRowData;
      this.selectedRowKeys = value;
      // this.checkBox = selectedRowData.filter((i) => i);
    },
    async opSave(row) {
      console.log(row);
      // if (!this.currentSfwjzjt) {
      //   this.$message.warning('请选择');
      //   return;
      // }
      // const params = [
      //   {
      //     ...row,
      //     jzjtbz: this.currentSfwjzjt,
      //     sszq: this.sszqC,
      //     djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      //   },
      // ];
      // const res = await xxfpHwfuMxSaveOrUpdate(params);
      // if (res.code === 1) {
      //   // eslint-disable-next-line array-callback-return
      //   const index = this.hwfpmxList.findIndex((item) => item.uuid === row.uuid);
      //   console.log(index);
      //   if (index > -1) this.hwfpmxList[index].jzjtbz = this.currentSfwjzjt;
      //   this.selectedRowKeys = this.selectedRowKeys.filter((item) => item !== row.uuid);
      // } else {
      //   this.$message.error(res.msg);
      // }
      // this.opFlag = false;
      // this.currentSfwjzjt = '';
    },
    async patchSave(type) {
      console.log(type);
      // const params = this.checkBox.map((d) => {
      //   return {
      //     ...d,
      //     jzjtbz: type ? 'Y' : 'N',
      //     sszq: this.sszqC,
      //     djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      //   };
      // });
      // const res = await xxfpHwfuMxSaveOrUpdate(params);
      // if (res.code === 1) {
      //   this.$message.success('设置成功');
      //   this.selectedRowKeys = [];
      //   this.query();
      // } else {
      //   this.$message.error(res.msg);
      // }
    },
    pageChange({ current, pageSize }, type) {
      // [this.pagination, this.hwpagination][type].current =
      //   pageSize !== [this.pagination, this.hwpagination][type].pageSize ? 1 : current;
      // [this.pagination, this.hwpagination][type].pageSize = pageSize;
      // this.query();
      // 获取对应的分页对象
      const pagination = type === 0 ? this.pagination : this.hwpagination;

      // 更新当前页码和每页条数
      const isPageSizeChanged = pageSize !== pagination.pageSize;
      pagination.current = isPageSizeChanged ? 1 : current;
      pagination.pageSize = pageSize;

      // 重置对应tab的加载状态
      const tabKey = type === 0 ? '0' : '1';
      this.loadedTabs[tabKey] = false;

      // 触发查询
      this.query({ initQuery: true });
    },
    ckdg() {
      this.$router.push('/lsgl');
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
</style>
