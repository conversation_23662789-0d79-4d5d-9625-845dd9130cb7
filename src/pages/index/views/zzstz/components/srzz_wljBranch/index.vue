<!--
 * @Descripttion: 台账-增值税一般纳税人收入总账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-06 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl1"
        class="znsbHeadqueryDiv"
        :config="querySearchConfigXxkm"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true, initQuery: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
          <!-- 添加王老吉分支特殊提示文字 -->
          <span v-if="wanglaojiFlag" class="tz-tip" style="color: #000">
            总销售收入：
            <a href="javascript:void(0)" @click="openSrmxTotal('xxsr')" style="color: #06c">{{
              totalData.xxsr ? totalData.xxsr : '0.00'
            }}</a
            >元， 总销售成本：
            <a href="javascript:void(0)" @click="openSrmxTotal('xscb')" style="color: #06c">{{
              totalData.xscb ? totalData.xscb : '0.00'
            }}</a
            >元， 成本率：{{ totalData.cblrl ? totalData.cblrl * 100 + '%' : '0%' }}
          </span>
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData1"
          :columns="tableColumnsXxkm"
          lazyLoad
          height="100%"
          :foot-data="footData1"
          :pagination="pagination"
          :loading="tableLoading"
          @page-change="pageChange"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #sl1="{ row }">
            {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
          </template>
          <template #xssr="{ row }">
            <div style="float: right">
              <span
                v-if="row.srlxDm === '110' || row.srlxDm === '120'"
                class="specText"
                @click="openSrmx(row, row.xssrkmdm)"
                style="color: #0052d9; text-decoration: underline"
                >{{ numberToPrice(row.xssr) }}</span
              >
              <span v-else>{{ numberToPrice(row.xssr) }}</span>
            </div>
          </template>
          <template #jsje="{ row }">
            <div style="float: right">
              <span>{{ numberToPrice(row.jsje) }}</span>
            </div>
          </template>
          <template #xsse="{ row }">
            <div style="float: right">
              <span
                v-if="row.srlxDm === '130'"
                class="specText"
                @click="openSrmx(row, row.xssekmdm)"
                style="color: #0052d9; text-decoration: underline"
                >{{ numberToPrice(row.xsse) }}</span
              >
              <span v-else>{{ numberToPrice(row.xsse) }}</span>
            </div>
          </template>
        </t-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getAll, getLrzx, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { initSrzzQuery, initSrzzQueryHj } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton/index.vue';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { ChartIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle, jyssReadyStatusFetch } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberToCurrency';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import { querySearchConfig1, xskmColumns } from './config.js';

export default {
  components: {
    SkeletonFrame,
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    ChartIcon,
  },
  data() {
    return {
      loading: true,
      isProduct: this.$store.state.isProduct.envValue,
      userInfo: {},
      formData1: {},
      querySearchConfigOneRules: {
        ssyfq: [{ required: true, message: '必填项', type: 'error' }],
        ssyfz: [{ required: true, message: '必填项', type: 'error' }],
      },
      djxh: '',
      nsrsbh: '',
      querySearchConfig1,
      tableLoading: false,
      firstInit1: true,
      slList: [],
      jsfsList: [],
      zsxm1List: [],
      srlxList: [],
      lrzxList: [],
      kmdmList: [],
      tableData1: [],
      footData1: [],
      // 总消费收入、总销售成本和成本率的数据
      totalData: {
        xxsr: 0.0,
        xscb: 0.0,
        cblrl: 0,
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      remoteColumnsXxkm: [], // 接收后端列配置-按销项科目
      remotequerySearchConfigXxkm: [], // 动态查询条件配置-按销项科目
    };
  },
  created() {},
  watch: {},
  computed: {
    querySearchConfigXxkm() {
      if (!this.remotequerySearchConfigXxkm.length) {
        // 兼容初始化状态
        return this.querySearchConfig1;
      }
      // 使用动态配置的列
      return this.remotequerySearchConfigXxkm;
    },
    sszqToExtract() {
      return dayjs(this.formData1.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'srZzwljTz';
    },
    exportFileName() {
      return '未开票收入台账导出';
    },
    exportButtonFlag() {
      return !this.tableData1.length;
    },
    tzQueryParams() {
      let cxParam = {};
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      djParam.pageNum = this.pagination.current;
      djParam.pageSize = this.pagination.pageSize;
      cxParam = {
        ...this.formData1,
        sszqq: dayjs(this.formData1.ssyfq).format('YYYYMM'),
        sszqz: dayjs(this.formData1.ssyfz).format('YYYYMM'),
        jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
        zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
        sl1: multiSelectHandle(this.formData1.sl1),
        srlxDm: multiSelectHandle(this.formData1.srlxDm),
        lrzx: multiSelectHandle(this.formData1.lrzx),
        ...djParam,
      };
      return cxParam;
    },
    tableColumnsXxkm() {
      const columns = this.remoteColumnsXxkm.length ? [...this.remoteColumnsXxkm] : xskmColumns;

      columns.find((c) => c.colKey === 'sszq').width = 80;
      columns.find((c) => c.title === '科目代码').colKey = 'xssrkmdm';
      columns.find((c) => c.title === '科目名称').colKey = 'xssrkmmc';
      return columns;
    },
    // 王老吉分支标志
    wanglaojiFlag() {
      // return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000006';
      return true;
    },
  },
  mounted() {
    this.initQueryConditions();
    this.fetchTableColumns();
    this.getLrzx();
    this.getQueryParamsList();
  },
  methods: {
    // 王老吉分支简化了原始分支的初始化逻辑，这里需要提供一些空函数切换企业时报错
    sfzjg() {},
    initTabValue() {},
    // 动态配置表头信息
    fetchTableColumns() {
      this.fetchQueryParamsConfig();
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取按销项科目的列配置
        const defaultColumnsXxkm = JSON.parse(JSON.stringify(xskmColumns));

        this.remoteColumnsXxkm = defaultColumnsXxkm
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);
            if (matchedItem && matchedItem.displayName === column.title && matchedItem.displayFlag === false) {
              return null;
            }
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);
        console.log('变更表头配置成功', this.remoteColumnsXxkm);
      } catch (e) {
        console.error('获取表头配置失败', e);
        this.remoteColumnsXxkm = JSON.parse(JSON.stringify(xskmColumns));
      }
    },
    // 动态配置查询条件信息
    fetchQueryParamsConfig() {
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取按销项科目的默认查询条件配置
        const defaultQueryParamsColumnsXxkm = JSON.parse(JSON.stringify(this.querySearchConfigXxkm));

        // 处理查询条件配置
        this.remotequerySearchConfigXxkm = defaultQueryParamsColumnsXxkm
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);
            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }
            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRZZ] 获取查询条件配置失败:', e);
        // 失败时回退到默认配置
        this.remotequerySearchConfigXxkm = JSON.parse(JSON.stringify(this.querySearchConfigXxkm));
      }
    },
    initQueryConditions() {
      if (Object.keys(this.$route.query).length) {
        console.log('Object.keys(this.$route.query)', Object.keys(this.$route.query));
        console.log('url-params', this.$route.query);
        if (this.$route.query.skssqq && this.$route.query.skssqz) {
          this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
          this.querySearchConfig1[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
          this.formData1.ssyfq = dayjs(this.$route.query.skssqq).format('YYYYMM');
          this.formData1.ssyfz = dayjs(this.$route.query.skssqz).format('YYYYMM');
          return;
        }
      }
      this.querySearchConfig1[0].value = computeSszq();
      this.querySearchConfig1[1].value = computeSszq();
      this.formData1.ssyfq = computeSszq();
      this.formData1.ssyfz = computeSszq();
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query({ initQuery: true });
    },

    check() {
      this.$router.push('/lsgl');
    },
    openSrmx(row, kmdm) {
      let data = {};
      data = {
        sszq: String(row.sszq),
        lrzx: row.lrzx,
        srlxDm: row.srlxDm,
        jsfsDm1: row.jsfsDm1,
        zsxmDm1: row.zsxmDm1,
        sl1: String(row.sl1),
        kmdm,
      };
      this.$emit('openPage', {
        from: 'srzz',
        data,
        type: 'srmx',
      });
    },

    // 从总数据跳转到收入明细
    openSrmxTotal(type) {
      let data = {};
      data = {
        sszq: dayjs().subtract(1, 'month').format('YYYYMM'),
        srlxDm: type === 'xxsr' ? '130' : '140',
        totalType: type, // 标识是从总消费收入还是总销售成本跳转
      };
      this.$emit('openPage', {
        from: 'srzz',
        data,
        type: 'srmx',
      });
    },
    async getQueryParamsList() {
      const res1 = await getAll();
      this.jsfsList = res1.data.jsfsList;
      this.zsxm1List = res1.data.zsxmList;
      this.srlxList = res1.data.srlxList;
      this.kmdmList = res1.data.kmList;
      const res2 = await getJsfssldzb();
      this.slList = res2.data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 }));

      if (this.querySearchConfigXxkm.find((c) => c.key === 'jsfsDm1')) {
        this.querySearchConfigXxkm.find((c) => c.key === 'jsfsDm1').selectList = this.jsfsList;
      }

      this.querySearchConfigXxkm.find((c) => c.key === 'zsxmDm1').selectList = this.zsxm1List.slice(0, 5);
      this.querySearchConfigXxkm.find((c) => c.key === 'srlxDm').selectList = this.srlxList;
      this.querySearchConfigXxkm.find((c) => c.key === 'xssrkmdm').selectList = this.kmdmList.map((d) => ({
        label: `${d.value} | ${d.label}`,
        value: d.value,
      }));
      this.querySearchConfigXxkm.find((c) => c.key === 'sl1').selectList = this.slList.map((d) => ({
        label: d.label,
        value: String(d.value),
      }));
    },
    async getLrzx() {
      try {
        const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = data;
        this.querySearchConfigXxkm.find((c) => c.key === 'lrzx').selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));

        if (this.formData1.lrzx) {
          this.formData1.lrzx =
            this.querySearchConfigXxkm
              .find((c) => c.key === 'lrzx')
              .selectList.find((t) => t.value === this.formData1.lrzx)?.label || '';
        }
      } catch (e) {
        console.log('[SRZZ] 获取利润中心列表操作失败:', e);
      }
    },
    async query({ flag = false } = {}) {
      if (this.formData1.ssyfq && this.formData1.ssyfz) {
        const q = dayjs(this.formData1.ssyfq, 'YYYYMM');
        const z = dayjs(this.formData1.ssyfz, 'YYYYMM');
        if (q.isAfter(z)) {
          this.$message.warning('请选择正确的时间范围');
          return;
        }
      }
      if (flag) {
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      const params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        sszqq: dayjs(this.formData1.ssyfq).format('YYYYMM'),
        sszqz: dayjs(this.formData1.ssyfz).format('YYYYMM'),
        jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
        zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
        sl1: multiSelectHandle(this.formData1.sl1),
        srlxDm: multiSelectHandle(this.formData1.srlxDm),
        xssrkmdm: multiSelectHandle(this.formData1.xssrkmdm),
        lrzx: multiSelectHandle(this.formData1.lrzx),
      };
      console.log('params', params);
      try {
        console.log('一般人收入总账初始化');
        const res = await initSrzzQuery(params);
        const { data } = res;
        if (data.records) {
          this.tableData1 = data.records.dataList || [];
          this.firstInit1 = false;
          this.tableData1.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
          if (this.tableData1.length > 0) {
            this.totalData = {
              xxsr: this.numberToPrice(this.tableData1[0]?.xxsr),
              xscb: this.numberToPrice(this.tableData1[0]?.xscb),
              cblrl: this.numberToPrice(this.tableData1[0]?.cblrl),
            };
            const hjRes = await initSrzzQueryHj(params);
            this.footData1 =
              [
                {
                  xssr: this.numberToPrice(hjRes.data?.xssr),
                  jsje: this.numberToPrice(hjRes.data?.jsje),
                  xsse: this.numberToPrice(hjRes.data?.xsse),
                },
              ] || [];
          } else {
            this.footData1 = [];
          }
          this.pagination.total = data.pageTotal;
        } else {
          this.tableData1 = [];
          this.footData1 = [];
          this.pagination.total = 0;
        }
        jyssReadyStatusFetch(
          this.$store.state.zzstz.userInfo?.djxh || '',
          this.$store.state.zzstz.userInfo?.nsrsbh || '',
          this.formData1.ssyfq,
        );
      } catch (error) {
        console.error('[SRZZ] 查询发生异常', {
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
        });
        this.tableData1 = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
// 添加提示文字样式
.tz-tip {
  margin-left: 10px;
  font-size: 14px;
  line-height: 32px; // 与按钮高度对齐
}
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
