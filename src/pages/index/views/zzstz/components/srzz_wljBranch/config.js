export const querySearchConfig1 = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '销售科目',
    key: 'xssrkmdm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const xskmColumns = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'sszq',
    title: '所属月份',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 110,
    colKey: 'gsh2',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'srlxmc',
    title: '收入类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 130,
    colKey: 'jsfsmc',
    title: '计税方式',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 80,
    colKey: 'zsxmmc',
    title: '征税项目',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'xssrkmdm',
    title: '科目代码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 180,
    colKey: 'xssrkmmc',
    title: '科目名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    align: 'right',
    colKey: 'xssr',
    title: '凭证金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    align: 'right',
    colKey: 'jsje',
    title: '计税金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    align: 'right',
    colKey: 'xsse',
    title: '销项税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
