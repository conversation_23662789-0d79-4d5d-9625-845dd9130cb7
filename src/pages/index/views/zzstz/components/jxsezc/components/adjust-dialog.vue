<template>
  <css-dialog
    class="dialogSmallCss"
    :visible="dialogVisible"
    :header="title"
    :on-close="handleClose"
    :on-confirm="handleSubmit"
    :close-on-overlay-click="false"
    width="400px"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="form" :data="formData" labelAlign="top" labelWidth="165px">
        <t-form-item label="转出税额">
          <gt-input-money v-if="formData" v-model="formData.zcse" theme="normal" placeholder="请输入转出税额" />
        </t-form-item>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>

<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { numberToPrice } from '@/utils/numberUtils.js';

export default {
  components: { CssDialog, GtInputMoney },
  name: 'AdjustDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({
        zcse: 0, // 显式定义默认字段，避免访问 undefined
        jxsezcxmMc: '调整转出税额',
      }),
    },
  },
  data() {
    return {
      oldzcse: 0,
    };
  },
  emits: ['confirm', 'update:visible', 'close'],
  watch: {
    visible: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          console.log('捕获原始值:', this.formData.zcse);
          this.oldzcse = this.formData.zcse || 0;
        }
      },
    },
  },
  computed: {
    dialogVisible() {
      return this.visible;
    },
    title() {
      // ✅ 关键修复：防御性检查
      if (!this.formData) return '调整转出税额';
      return this.formData.jxsezcxmMc || '调整转出税额';
    },
    fistEditFlag() {
      // ✅ 关键修复：防御性检查
      if (!this.formData) return false;
      return this.formData.tzlxDm === '1';
    },
  },
  mounted() {
    console.log('AdjustDialog: this.formData', this.formData);
  },
  methods: {
    numberToPrice,
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleSubmit() {
      const confirmDia = this.$dialog({
        header: '提示',
        body: this.fistEditFlag
          ? `调整前数据为乐企获取数据${numberToPrice(this.oldzcse)}，调整后数据为${numberToPrice(
              this.formData.zcse,
            )}，请确认是否进行调整？`
          : `调整前数据为${numberToPrice(this.oldzcse)}，调整后数据为${numberToPrice(
              this.formData.zcse,
            )}，请确认是否进行调整？`,
        onConfirm: () => {
          this.$emit('confirm', { ...this.formData });
          confirmDia.destroy();
        },
        onCancel: () => {
          confirmDia.destroy();
        },
        onClose: () => {
          confirmDia.destroy();
        },
      });
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-dialog {
  height: 300px !important;
}
.dialogMiddleCss {
  height: 300px !important;
}
.nr {
  height: 175px !important;
  padding-top: 36px !important;
  padding-bottom: 36px !important;
}
</style>
