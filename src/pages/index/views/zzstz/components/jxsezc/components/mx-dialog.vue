<template>
  <css-dialog
    class="dialogCss"
    header="会计凭证明细"
    :visible.sync="isVisible"
    @close="onClose"
    :cancelBtn="'关闭'"
    :confirmBtn="null"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
            ><DownloadIcon slot="icon" />导出</t-button
          >
        </gt-space>
      </div>
      <div>
        <t-table row-key="uuid" height="440px" hover :data="tableData" :foot-data="footData" :columns="tableColumns" />
      </div>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getKjfp } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { queryCybdmxb } from '@/pages/index/api/tzzx/zzstz/jxsezczz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon } from 'tdesign-icons-vue';

export default {
  components: { CssDialog, DownloadIcon },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.dataColumns = [
      {
        colKey: 'serial-number',
        title: '序号',
        align: 'center',
        width: 50,
        foot: '合计',
      },
      {
        align: 'left',
        colKey: 'sszq',
        title: '所属账期',
        width: 120,
      },
      {
        align: 'left',
        width: 100,
        colKey: 'gsh',
        title: '公司号',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        width: 120,
        colKey: 'lrzx',
        title: '利润中心',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        colKey: 'kjpzbh',
        title: '凭证编号',
        width: 210,
      },
      {
        align: 'left',
        width: 160,
        colKey: 'kmbm',
        title: '科目编码',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        colKey: 'kmmc',
        title: '科目名称',
        width: 220,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        colKey: 'kjfpmc',
        title: '分配代码',
        width: 180,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'right',
        colKey: 'se',
        title: '进项税额（税额字段）',
        width: 160,
      },
    ];
    return {
      isVisible: true,
      dcLoading: false,
      tableData: [],
      footData: [],
      kjfpList: [],
      columnsLoading: false, // 列配置加载状态
      remoteColumns: [], // 动态列配置
    };
  },
  created() {},
  mounted() {
    this.init();
    this.fetchTableColumns(); // 初始化表头配置
  },
  computed: {
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.dataColumns; // 兼容初始化状态
      }
      return this.remoteColumns;
    },
  },
  methods: {
    // 动态配置表头信息
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 处理列配置
        this.remoteColumns = this.dataColumns
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        // 失败时回退到默认配置
        this.remoteColumns = this.dataColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'JxseQueryCy',
        fileName: '进项税额转出会计凭证明细导出',
        cxParam: {
          nsrsbh: this.visible?.nsrsbh,
          sszq: this.visible?.sszq,
          jxsezcxmDm: this.visible?.jxsezcxmDm,
        },
      };
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async init() {
      const res = await getKjfp({ ywDm: '210' });
      this.kjfpList = res?.data || [];
      console.log(this.visible);
      const { data } = await queryCybdmxb({
        djxh: this.visible?.djxh,
        nsrsbh: this.visible?.nsrsbh,
        sszq: this.visible?.sszq,
        jxsezcxmDm: this.visible?.jxsezcxmDm,
      });
      this.tableData = data.records.dataList || [];
      this.footData =
        this.tableData.length > 0
          ? [
              {
                se: data.records?.hj.se,
              },
            ]
          : [];
    },
    onClose() {
      this.isVisible = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/ .t-table__content {
  // 表格高度修正
  height: 408px !important;
}
</style>
