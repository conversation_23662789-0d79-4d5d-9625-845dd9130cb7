<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增进项税额转出明细账', '编辑进项税额转出明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <!-- <t-col :span="4">
            <t-form-item label="公司号" name="gsh">
              <t-input :maxlength="30" v-model="formData.gsh" placeholder="请输入" disabled></t-input>
            </t-form-item>
          </t-col> -->
          <!-- <t-col :span="4">
            <t-form-item label="所属账期" name="sszq">
              <t-input v-model="formData.sszq" disabled></t-input>
            </t-form-item>
          </t-col> -->
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请输入" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="转出项目" name="jxsezcxmmc">
              <t-select
                clearable
                filterable
                @change="zcxmChange"
                v-model="formData.jxsezcxmmc"
                :options="zcxmList"
                placeholder="请选择转出项目"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="转出日期" name="zcrq">
              <t-date-picker v-model="formData.zcrq" mode="date" clearable style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="转出税额" name="zcse">
              <!-- <t-input-number
                v-model="formData.zcse"
                theme="column"
                :decimal-places="2"
                placeholder="请输入"
                style="width: 100%"
              ></t-input-number> -->
              <gt-input-money v-model="formData.zcse" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmbm">
              <t-select
                v-model="formData.kmbm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmbm)"
              >
                <t-option v-for="item in kmbmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4" v-if="!(quanshanFlag || tongyongguojiFlag || dalianzhonggongFlag)">
            <t-form-item :label="formLabels.kjfp" name="kjfpmc">
              <t-input v-model="formData.kjfpmc" placeholder="请输入" disabled></t-input>
            </t-form-item>
          </t-col>

          <!-- <t-col :span="4">
              <t-form-item label="转出即征即退税额" name="jzjtse">
                <t-input-number
                  v-model="formData.jzjtse"
                  theme="normal"
                  placeholder="请输入"
                  style="width: 100%"
                ></t-input-number>
              </t-form-item>
            </t-col> -->
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable
            /></t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>

<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx, getKmbmByywbm, getKjfpByJxsezcxm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
// import { getLrzx, getKmbmByywbm, getJxsezcxmList, getKjfpByJxsezcxm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { addJxsezcmxz, editJxsezcmxz } from '@/pages/index/api/tzzx/zzstz/jxsezczz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      // gsh: [{ required: true, message: '必填', type: 'error' }],
      lrzx: [{ required: true, message: '必选', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      jxsezcxmmc: [{ required: true, message: '必选', type: 'error' }],
      zcrq: [{ required: true, message: '必填', type: 'error' }],
      zcse: [{ required: true, message: '必选', type: 'error' }],
      // jzjtse: [{ required: true, message: '必填', type: 'error' }],
      kjfpmc: this.quanshanFlag ? [] : [{ required: true, message: '必选', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      formData: {
        sszq: '',
        uuid: '',
        gsh: '',
        lrzx: '',
        kjpzbh: '',
        jxsezcxmDm: '',
        jxsezcxmmc: '',
        zcrq: '',
        zcse: '',
        kjfpDm: '',
        kjfpmc: '',
        kmbm: '',
        kmmc: '',
        tzyy: '',
      },
      lrzxList: [],
      zcxmList: [],
      kmbmList: [],
      zcxmkmbmglList: [],
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  computed: {
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
        kjfp: labels.kjfp || '分配', // 如果配置中没有lrzx，使用默认值
      };
    },
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    dalianzhonggongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000002';
    },
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000004';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000005';
    },
    wanglaojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000006';
    },
  },
  methods: {
    async init() {
      this.rules = this.baseRules;
      console.log(this.visible);
      this.getJxsezcxmList();
      await this.getLrzx();
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.lrzx) {
        this.formData = this.visible.row;
      } else {
        this.formData.gsh = this.visible.otherObj.gsh;
        this.formData.sszq = this.visible.otherObj.zssszq;
        if (this.shanxiyidongFlag) {
          // 山西移动分支
          this.getLkyskmdm();
        } else if (this.quanshanFlag) {
          // 泉膳分支
          this.zcxmkmbmglList = [
            { jxsezcxmDm: '02', value: '3170000063', label: '应交税费-应交增值税-进项税额转出-集体福利' },
            { jxsezcxmDm: '03', value: '3170000056', label: '应交税费-应交增值税-进项税额转出-非正常损失' },
            // { value: '3170000057', label: '应交税费-应交增值税-进项税额转出-红字专用发票' },
            // { value: '3170000058', label: '应交税费-应交增值税-进项税额转出-上期留抵税额退税' },
            { jxsezcxmDm: '01', value: '3170000059', label: '应交税费-应交增值税-进项税额转出-免税项目用' },
            { jxsezcxmDm: '11', value: '3170000060', label: '应交税费-应交增值税-进项税额转出-其他' },
          ];
        } else if (this.dalianzhonggongFlag) {
          // 大连重工
          this.zcxmkmbmglList = [
            { jxsezcxmDm: '01', value: '21711701', label: '应交税金-应交增值税-进项税额转出（免税项目）' },
            { jxsezcxmDm: '02', value: '21711702', label: '应交税金-应交增值税-进项税额转出（非应税项目）' },
            { jxsezcxmDm: '03', value: '21711703', label: '应交税金-应交增值税-进项税额转出（非正常损失）' },
            { jxsezcxmDm: '04', value: '21711704', label: '应交税金-应交增值税-进项税额转出（简易计税）' },
            { jxsezcxmDm: '06', value: '21711705', label: '应交税金-应交增值税-进项税额转出（纳税检查调减）' },
            // { jxsezcxmDm: '07', value: '21711706', label: '应交税金-应交增值税-进项税额转出（红字专用发票）' },
            // { jxsezcxmDm: '09', value: '21711707', label: '应交税金-应交增值税-进项税额转出（留抵退税）' },
            // { jxsezcxmDm: '10', value: '21711708', label: '应交税金-应交增值税-进项税额转出（异常凭证转出）' },
            { jxsezcxmDm: '11', value: '21711709', label: '应交税金-应交增值税-进项税额转出（其他）' },
          ];
        } else if (this.wanglaojiFlag) {
          // 王老吉
          this.zcxmkmbmglList = [
            { jxsezcxmDm: '01', value: '22210105', label: '应交税金-应交增值税-进项税额（免税项目）' },
            { jxsezcxmDm: '02', value: '22210105', label: '应交税金-应交增值税-进项税额（非应税项目）' },
            { jxsezcxmDm: '03', value: '22210105', label: '应交税金-应交增值税-进项税额（非正常损失）' },
            { jxsezcxmDm: '04', value: '22210105', label: '应交税金-应交增值税-进项税额（简易计税）' },
            { jxsezcxmDm: '06', value: '22210105', label: '应交税金-应交增值税-进项税额（纳税检查调减）' },
            // { jxsezcxmDm: '07', value: '22210105', label: '应交税金-应交增值税-进项税额（红字专用发票）' },
            // { jxsezcxmDm: '08', value: '22210105', label: '应交税金-应交增值税-进项税额（留抵税额抵减）' },
            // { jxsezcxmDm: '09', value: '22210105', label: '应交税金-应交增值税-进项税额（留抵退税）' },
            // { jxsezcxmDm: '10', value: '22210105', label: '应交税金-应交增值税-进项税额（异常凭证转出）' },
            { jxsezcxmDm: '11', value: '22210105', label: '应交税金-应交增值税-进项税额（其他）' },
          ];
        } else {
          this.kmbmList = [{ value: this.visible.otherObj.kmbm, label: this.visible.otherObj.kmmc }];
          this.formData.kmbm = this.kmbmList[0].value;
          this.formData.kmmc = this.kmbmList[0].label;
        }
      }
    },
    async getLkyskmdm() {
      const { data } = await getKmbmByywbm({ ywlxDm: '310' });
      this.kmbmList = data;
      if (this.kmbmList.length === 1) {
        this.formData.kmbm = this.kmbmList[0]?.value;
        this.getXzKmmc(this.formData.kmbm);
      }
    },
    getXzKmmc(kmdm) {
      this.formData.kmmc = this.kmbmList.find((item) => item.value === kmdm)?.label;
    },
    async getJxsezcxmList() {
      if (this.quanshanFlag) {
        this.zcxmList = [
          { value: '01', label: '免税项目用的进项税转出额' },
          { value: '02', label: '集体福利、个人消费的进项转出额' },
          { value: '03', label: '非正常损失的进项转出额' },
          { value: '11', label: '其他应作进项税额转出的情形' },
        ];
      } else if (this.tongyongguojiFlag) {
        this.zcxmList = [
          { value: '01', label: '免税项目用的进项税转出额' },
          { value: '02', label: '集体福利、个人消费的进项转出额' },
          { value: '03', label: '非正常损失的进项转出额' },
          { value: '04', label: '简易计税方法征税项目用的进项税转出额' },
          { value: '05', label: '免抵退税办法出口货物不得抵扣进项税额的转出额' },
          { value: '06', label: '纳税检查调减进项税额' },
          { value: '07', label: '红字专用发票信息表注明的进项税额' },
          { value: '08', label: '上期留抵税额抵减欠税' },
          { value: '09', label: '上期留抵税额退税' },
          { value: '10', label: '异常凭证转出进项税额' },
          { value: '11', label: '其他应作进项税额转出的情形' },
        ];
      } else if (this.dalianzhonggongFlag) {
        this.zcxmList = [
          { value: '01', label: '免税项目用的进项税转出额' },
          { value: '02', label: '集体福利、个人消费的进项转出额' },
          { value: '03', label: '非正常损失的进项转出额' },
          { value: '04', label: '简易计税方法征税项目用的进项税转出额' },
          { value: '06', label: '纳税检查调减进项税额' },
          // { value: '07', label: '红字专用发票信息表注明的进项税额' },
          // { value: '09', label: '上期留抵税额退税' },
          // { value: '10', label: '异常凭证转出进项税额' },
          { value: '11', label: '其他应作进项税额转出的情形' },
        ];
      } else {
        this.zcxmList = [
          { value: '01', label: '免税项目用的进项税转出额' },
          { value: '02', label: '集体福利、个人消费的进项转出额' },
          { value: '03', label: '非正常损失的进项转出额' },
          { value: '04', label: '简易计税方法征税项目用的进项税转出额' },
          { value: '05', label: '免抵退税办法出口货物不得抵扣进项税额的转出额' },
          { value: '06', label: '纳税检查调减进项税额' },
          { value: '11', label: '其他应作进项税额转出的情形' },
        ];
      }
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
    },
    async zcxmChange(val) {
      if (!val) return;

      const matchedItem = this.zcxmList.find((item) => item.value === val);
      if (!matchedItem) return;

      // 更新转出项目信息
      this.formData = {
        ...this.formData,
        jxsezcxmDm: matchedItem.value,
        jxsezcxmmc: matchedItem.label,
      };

      // 泉膳环境下自动带出科目编码
      if (this.quanshanFlag || this.dalianzhonggongFlag) {
        const kmItem = this.zcxmkmbmglList.find((item) => item.jxsezcxmDm === val);
        if (kmItem) {
          this.kmbmList = [{ value: kmItem.value, label: kmItem.label }];
          this.formData.kmbm = kmItem.value;
          this.formData.kmmc = kmItem.label;
        }
      }

      // 非泉膳环境下获取分配信息
      if (!this.quanshanFlag) {
        try {
          const { data } = await getKjfpByJxsezcxm({ jxsezcxmDm: matchedItem.value });
          this.formData.kjfpDm = data.value;
          this.formData.kjfpmc = data.label;
        } catch (error) {
          console.error('获取分配信息失败:', error);
        }
      }
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        let p = {};
        [
          'uuid',
          'lrzx',
          'kjpzbh',
          'jxsezcxmDm',
          'jxsezcxmmc',
          'zcrq',
          'zcse',
          'kmbm',
          'kmmc',
          'kjfpDm',
          'kjfpmc',
          'tzyy',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        if (Number(this.formData.zcse) !== Number(this.visible.oldzcse)) {
          p = { ...p, zcseBz: 'Y' };
        }
        const params = { ...this.visible.otherObj, ...p };

        // 通用国际企业分支：额外添加lrzx和gsh字段
        if (this.tongyongguojiFlag) {
          const lrzxItem = this.lrzxList.find((item) => item.value === this.formData.lrzx);
          params.lrzx = this.formData.lrzx; // 确保使用当前选中的value
          params.gsh = lrzxItem ? lrzxItem.label : ''; // 使用对应的label作为gsh
        }
        try {
          if (this.visible.pageType) {
            await editJxsezcmxz(params);
          } else {
            await addJxsezcmxz(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateJxsezcmx');
          } else {
            this.$emit('updateJxsezcmx', { flag: true }); // 新增回到第一页
            console.log('返回查询页并执行一次查询', this.visible.pageType);
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    onClose() {
      this.isVisible = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
