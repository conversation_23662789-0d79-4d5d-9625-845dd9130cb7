<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig2"
      :formRules="querySearchConfigRules2"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
      <template #t1><span></span></template>
      <template #t2><span></span></template>
    </search-control-panel>

    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
          ><add-icon slot="icon" />新增</t-button
        >
        <t-button theme="primary" @click="delRow" :disabled="!allowEdit"><DeleteIcon slot="icon" />删除</t-button>
        <ExtractDataButton variant="outline" :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <!-- <t-button variant="outline" theme="primary" @click="savetz">保存台账</t-button> -->
        <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>

    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        hover
        :key="id"
        row-key="key"
        :data="tableData"
        :columns="tableColumns"
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        height="100%"
        lazyLoad
        :pagination="pagination"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #cz="{ row }">
          <t-button
            v-if="row.ly === '1'"
            variant="text"
            @click="newOrEditRow(row)"
            theme="primary"
            :disabled="!allowEdit"
            >编辑</t-button
          >
        </template>
        <template #zcse="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.zcse) }}</span>
          </div>
        </template>
        <!-- <template #jzjtse="{ row }">
          <t-input
            v-if="row.zcjzjtsekbjbz === 'Y'"
            v-model="row.jzjtse"
            type="number"
            theme="normal"
            align="right"
            clearable
          />
          <span v-else style="float: right">{{ row.jzjtse }}</span>
        </template> -->
      </t-table>
    </div>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateJxsezcmx="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
        <!-- class="confirmDialogCss"
        <div style="text-align: center">
          <div><img src="../../../../assets/error.png" alt="" /></div>
          <div class="tsnr">请确认是否删除所选明细</div>
          <div class="tsnrf">
            <div>xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
          </div>
        </div> -->
      </t-dialog>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getJxsezcxmList } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { queryJxsezcmxzList, deleteJxsezcmxz, queryJxsezcmxzListHj } from '@/pages/index/api/tzzx/zzstz/jxsezczz.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, ChartIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { querySearchConfig2, querySearchConfigRules2, dataColumns } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    EditDialog,
    AddIcon,
    DeleteIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      editDialogVisible: false,
      isProduct: this.$store.state.isProduct.envValue,
      boxvisible: false,
      querySearchConfig2,
      querySearchConfigRules2,
      tableLoading: false,
      fromName: false,
      tableData: [],
      historytableData: [],
      formData: {},
      delformData: [],
      sszq: dayjs().subtract(1, 'month').format('YYYYMM'),
      zcxmList: [],
      checkBox: [],
      footData: [],
      selectedRowKeys: [],
      id: dayjs().unix(),
      dataColumns,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      columnsLoading: false, // 列配置加载状态
      remoteColumns: [], // 动态列配置
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
    };
  },
  created() {
    this.querySearchConfig2[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig2[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
  },
  mounted() {
    this.init();
    this.fetchTableColumns(); // 初始化表头配置
    this.checkAllowEdit();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jxsezcMxzTz';
    },
    exportFileName() {
      return '进项税额转出明细账';
    },
    tzQueryParams() {
      return {
        sszq: Number(this.formData.sszq.replace('-', '')),
        jxsezcxmDm: multiSelectHandle(this.formData.jxsezcxmDm),
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
    tableColumns() {
      if (!this.remoteColumns.length) {
        return this.dataColumns; // 兼容初始化状态
      }
      return this.remoteColumns;
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000005';
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    // 动态配置表头信息
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 处理列配置
        this.remoteColumns = this.dataColumns
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        // 失败时回退到默认配置
        this.remoteColumns = this.dataColumns;
      } finally {
        this.columnsLoading = false;
      }
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh: this.$store.state.zzstz.userInfo?.qydmz || '',
        sszq: this.formData.sszq.substring(0, 4) + this.formData.sszq.substring(5, 7),
        zssszq: this.formData.sszq,
        ly: '1',
        zzuuid: '',
        kmbm: this.tongyongguojiFlag ? '22210104' : '2221010500',
        kmmc: '应交税费-应交增值税-进项税额转出',
        scbz: '0',
        tzlxDm: '1',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys.length > 0) {
        console.log('selectedRowKeys', this.selectedRowKeys);
        const temp = JSON.stringify(this.tableData.find((t) => t.uuid === this.selectedRowKeys[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }
      console.log('selectedRowData', selectedRowData);
      const rowdata = JSON.stringify(selectedRowData);
      this.editDialogVisible = {
        row: JSON.parse(rowdata),
        oldkjpzbh: row?.kjpzbh || '',
        oldzcse: row?.zcse || '',
        otherObj,
        pageType,
      };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.key === item.key);
        this.tableData.splice(index, 1);
        this.delformData.push({ uuid: item.uuid });
      });
      const params = this.delformData;
      this.delete(params);
      this.id = dayjs().unix();
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log(value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async init() {
      this.zcxmList = [
        { value: '01', label: '免税项目用的进项税转出额' },
        { value: '02', label: '集体福利、个人消费的进项转出额' },
        { value: '03', label: '非正常损失的进项转出额' },
        { value: '04', label: '简易计税方法征税项目用的进项税转出额' },
        { value: '05', label: '免抵退税办法出口货物不得抵扣进项税额的转出额' },
        { value: '06', label: '纳税检查调减进项税额' },
        { value: '11', label: '其他应作进项税额转出的情形' },
      ];
      try {
        const { data } = await getJxsezcxmList();
        this.zcxmList = data;
        this.querySearchConfig2[1].selectList = this.zcxmList;
      } catch (e) {
        console.error(e);
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params }; // 起始时间待解决
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
          this.$refs.queryControl.setParams(p);
        }
      } else {
        params = {
          ...this.formData,
          sszq: this.formData.sszq.substring(0, 4) + this.formData.sszq.substring(5, 7),
          jxsezcxmDm: multiSelectHandle(this.formData.jxsezcxmDm),
          ...params,
        };
      }
      try {
        const { data } = await queryJxsezcmxzList(params);
        this.tableData = data.records || [];
        this.defaultLength = this.tableData.length || 0;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
          this.$set(item, 'zcjzjtsekbjbz', item.jzjtse ? 'N' : 'Y');
        });
        this.historytableData = JSON.parse(JSON.stringify(this.tableData));
        this.pagination.total = data.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await queryJxsezcmxzListHj(params);
          this.footData =
            [
              {
                zcse: numberToPrice(data?.zcse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params) {
      try {
        const { msg } = await deleteJxsezcmxz(params);
        console.log('deleteJxsezcmxz-msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }) {
      console.log('pageChange', { current, pageSize });
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
