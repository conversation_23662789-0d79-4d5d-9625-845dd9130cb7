import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigRules2 = {
  sszq: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    label: '汇总分支机构',
    key: 'hzfzjg',
    type: 'select',
    multiple: false,
    selectList: [
      { label: '是', value: '1' },
      { label: '否', value: '2' },
    ],
    value: 'Y',
    clearable: false,
  },
  {
    label: '',
    key: 't1',
  },
  {
    label: '',
    key: 't2',
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    label: '转出项目',
    key: 'jxsezcxmDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '',
    key: 't2',
  },
];
export const dataColumns = [
  {
    type: 'multiple',
    colKey: 'row-select',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    fixed: 'left',
  },
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 110,
    colKey: 'gsh',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'kmbm',
    title: '科目编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 240,
    colKey: 'kmmc',
    title: '科目名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 210,
    colKey: 'kjpzbh',
    title: '凭证编号',
  },
  {
    width: 200,
    colKey: 'kjfpmc',
    title: '分配',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'pztt',
    title: '凭证抬头',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'jxsezcxmmc',
    title: '转出项目',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 100,
    colKey: 'zcrq',
    title: '转出日期',
  },

  // {
  //   align: 'center',
  //   width: 160,
  //   colKey: 'jzjtse',
  //   title: '转出即征即退税额',
  // },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'zcse',
    title: '转出税额',
    fixed: 'right',
  },
  {
    align: 'center',
    width: 100,
    colKey: 'cz',
    title: '操作',
    foot: '-',
    fixed: 'right',
  },
];
