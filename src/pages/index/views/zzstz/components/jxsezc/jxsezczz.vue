<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
        <template #t1><span></span></template>
        <template #t2><span></span></template>
      </search-control-panel>
      <!-- :formRules="querySearchConfigOneRules" -->
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :pagingFlag="false"
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <BatchExportButton
            v-if="quanshanFlag"
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="myTable"
          row-key="key"
          hover
          :data="tableData"
          :columns="dataColumns"
          :height="dynamicHeight"
          :rowClassName="rowClassName"
          @row-click="(e) => (activeRule = e)"
        >
          <!-- :pagination="pagination" -->
          <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
          <!-- <template #jxsezcxmMc="{ row }">
          <div style="text-align: left">{{ row.jxsezcxmMc }}</div>
        </template> -->
          <template #jxsezcxmMc="{ row }">
            <t-tooltip :showArrow="false" :destroyOnClose="false" v-if="row.lymsg">
              <template #content>
                <span>{{ row.lymsg }}</span>
              </template>
              {{ row.jxsezcxmMc }}
              <img :src="infoIcon" />
            </t-tooltip>
            <div v-else>{{ row.jxsezcxmMc }}</div>
          </template>
          <!-- 1进项转出总账，均不可编辑，隐藏操作列
        2进项转出总账，由于明细账之前已经隐藏了转出即征即退税额，故总账的即征即退税额先保留,不需要下砖。
        -->
          <!-- <template #zcse="{ row }">
          <div v-if="row.isEditing !== '0'" style="text-align: right">
            {{ numberToPrice(row.zcse) }}
          </div>
          <div v-if="row.isEditing === '0'" style="color: #4285f4; text-align: right" @click="zcseClick(row)">
            {{ numberToPrice(row.zcse) }}
          </div>
          <div v-if="row.isEditing === '2'">
            <t-input-number v-model="row.zcse" theme="normal" placeholder="请输入" style="width: 100%"></t-input-number>
          </div>
          <div v-if="row.isEditing === '1' || row.isEditing === '3'" style="text-align: right">
            {{ numberToPrice(row.zcse) }}
          </div>
          <div v-if="row.isEditing === '0'" style="color: #4285f4; text-align: right" @click="zcseClick(row)">
            {{ numberToPrice(row.zcse) }}
          </div>
        </template> -->
          <template #zcse="{ row }">
            <div
              v-if="row.cybz === 'error'"
              style="display: flex; justify-content: space-between; align-items: center; float: right"
            >
              <t-tooltip :showArrow="false" :destroyOnClose="false">
                <a
                  v-if="['08', '09', '10'].includes(row.jxsezcxmDm) && senmaFlag"
                  @click="openAdjustDialog(row)"
                  style="color: #4285f4; text-decoration: underline"
                  >{{ numberToPrice(row.zcse) || '0.00' }}</a
                >
                <span v-else-if="row.ly === '0' && row.isEditing === '0'" class="specText" @click="zcseClick(row)">{{
                  numberToPrice(row.zcse) || '0.00'
                }}</span>
                <span v-else>{{ numberToPrice(row.zcse) || '0.00' }}</span>
                <template #content>
                  <span>{{ row.cymsg }}</span>
                  <span @click="mxVisible = row" style="margin-left: 10px; color: lightblue; cursor: pointer">
                    {{ row.zxmsg }}
                  </span>
                </template>
                <ErrorCircleFilledIcon :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }" />
              </t-tooltip>
            </div>
            <a
              v-else-if="['08', '09', '10'].includes(row.jxsezcxmDm) && senmaFlag"
              @click="openAdjustDialog(row)"
              style="float: right; color: #4285f4; text-decoration: underline"
              >{{ numberToPrice(row.zcse) || '0.00' }}</a
            >
            <span class="specText" @click="zcseClick(row)" v-else-if="row.ly === '0' && row.isEditing === '0'">{{
              numberToPrice(row.zcse) || '0.00'
            }}</span>
            <span v-else>{{ numberToPrice(row.zcse) || '0.00' }}</span>
          </template>
          <template #jzjtse="{ row }">
            <div style="float: right">
              <span>{{ numberToPrice(row.jzjtse) }}</span>
            </div>
            <!-- <div v-if="row.isEditing === '2'">
            <t-input-number
              v-model="row.jzjtse"
              theme="normal"
              placeholder="请输入"
              style="width: 100%"
            ></t-input-number>
          </div>
          <div v-if="row.isEditing === '1' || row.isEditing === '3'" style="text-align: right">
            {{ numberToPrice(row.jzjtse) }}
          </div>
          <div v-if="row.isEditing === '0'" style="color: #4285f4; text-align: right" @click="zcseClick(row)">
            {{ numberToPrice(row.jzjtse) }}
          </div> -->
          </template>
          <!-- <template #cz="{ row, rowIndex }">
          <div v-if="row.isEditing === '2'" style="display: flex">
            <t-button variant="text" style="flex: 1" @click="saveRow(row)" theme="primary">保存</t-button>
            <t-button variant="text" style="flex: 1" theme="primary" @click="cancelRow(row)">取消</t-button>
          </div>
          <t-button v-if="row.isEditing === '1'" variant="text" @click="editRow(row, rowIndex)" theme="primary"
            >编辑</t-button
          >
        </template> -->
        </t-table>
      </div>
      <ValidateDialog
        :validate-rules="validateRules"
        :handleMsg="true"
        :extraHandleMsg="true"
        @ruleClick="ruleClick"
        @toggle="toggle"
        @handleMsg="(item) => (mxVisible = item)"
        @extraHandleMsg="
          (item) => {
            this.hlVisible = {
              ...item,
              showHlxmFlag: false, // 不显示忽略项目
            };

            // 使用深拷贝确保响应式更新
            this.hlformData = {
              hlsy: item.hlsy || '',
            };
          }
        "
      />
      <!-- <div v-show="hlVisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="是否确定忽略该疑点差异信息"
          confirmBtn="是"
          cancelBtn="否"
          :onConfirm="qrhlcyxx"
          :onClose="closeBox"
        >
        </t-dialog>
      </div> -->
      <IgnoreDialog :visible="hlVisible" :formData="hlformData" @confirm="qrhlcyxx($event)" @close="closeBox" />
      <mxDialog :visible.sync="mxVisible" v-if="mxVisible" />
      <!-- 弹窗绑定 currentRow -->
      <AdjustDialog
        :visible="adjustVisible"
        :formData="currentRow"
        @confirm="submitAdjustment"
        @close="adjustVisible = false"
      />
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { cybdhl } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import {
  getJxsezczzInit,
  queryJxsezczzList,
  saveJxsezczz,
  editJxsezczzswzd,
} from '@/pages/index/api/tzzx/zzstz/jxsezczz.js';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import ExportButton from '@/pages/index/components/ExportButton/index.vue';
import BatchExportButton from '@/pages/index/components/ExportButton/index-quanshan.vue';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import IgnoreDialog from '@/pages/index/components/IgnoreDialog/index.vue';
import { ChartIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { computeSszq, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { querySearchConfig1 } from './config.js';
import mxDialog from './components/mx-dialog.vue';
import AdjustDialog from './components/adjust-dialog.vue';

export default {
  components: {
    SkeletonFrame,
    QsbButton,
    ExportButton,
    BatchExportButton,
    ExtractDataButton,
    SearchControlPanel,
    IgnoreDialog,
    ErrorCircleFilledIcon,
    ChartIcon,
    mxDialog,
    AdjustDialog,
    ValidateDialog,
  },
  data() {
    return {
      loading: true,
      hlVisible: false,
      isProduct: this.$store.state.isProduct.envValue,
      validateDialogToggle: true,
      infoIcon:
        'data:image/svg+xml;base64,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',
      userInfo: {},
      defaultLength: 10,
      querySearchConfig1,
      querySearchConfigOneRules: {
        hzfzjg: [{ required: true, message: '必填项', type: 'error' }],
      },
      sszq: dayjs().subtract(1, 'month').format('YYYYMM'),
      formData: {},
      tableDataCls: [],
      tableData: [],
      tempTableData: [],
      historyTableData: [],
      validateRules: [],
      activeRule: '',
      dataColumns: [
        {
          width: 50,
          colKey: 'xh',
          title: '序号',
          align: 'center',
        },
        {
          width: 500,
          colKey: 'jxsezcxmMc',
          title: '转出项目',
          align: 'left',
        },
        {
          align: 'right',
          width: 250,
          colKey: 'zcse',
          title: '转出税额',
        },
        {
          align: 'right',
          width: 250,
          colKey: 'jzjtse',
          title: '即征即退税额',
        },
        // 1.进项转出总账，均不可编辑，隐藏操作列
        // {
        //   align: 'center',
        //   width: 100,
        //   colKey: 'cz',
        //   title: '操作',
        // },
      ],
      hlformData: {
        hlsy: '',
      },
      mxVisible: false,
      adjustVisible: false, // 控制调整弹窗是否可见
      currentRow: null, // 存储当前选中的行数据
    };
  },
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
    'tableData.length': {
      deep: true,
      handler() {
        this.tempTableData = [];
        this.tableData.forEach((vv) => {
          this.tempTableData.push(JSON.parse(JSON.stringify(vv)));
        });
      },
    },
  },
  computed: {
    senmaFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000001';
    },
    zszfjgFlag() {
      return (
        this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000003' &&
        this.$store.state.zdmczh.companyDifferentiationConfig.fzjgbz === 'zjg'
      );
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    querySearchConfig() {
      let querySearchConfigTemp = this.querySearchConfig1;
      if (!this.zszfjgFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'hzfzjg');
      }
      if (this.xgmnsrFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'jsfsDm');
      }
      return querySearchConfigTemp;
    },
    querySearchConfigRules() {
      const querySearchConfigOneRulesTemp = JSON.parse(JSON.stringify(this.querySearchConfigOneRules));
      if (!this.zszfjgFlag) {
        delete querySearchConfigOneRulesTemp.hzfzjg;
        delete this.formData.hzfzjg;
      }
      return querySearchConfigOneRulesTemp;
    },
    dynamicHeight() {
      let height = '110%';
      if (!this.validateDialogToggle) {
        return height;
      }
      switch (this.validateRules.length) {
        case 0:
          height = '110%';
          break;
        case 1:
          height = '95%';
          break;
        case 2:
          height = '85%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '65%';
          break;
      }
      return height;
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jxsezcZzTz';
    },
    exportFileName() {
      return '进项税额转出总账';
    },
    tzQueryParams() {
      return {
        sszq: Number(this.formData.sszq.replace('-', '')),
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        // 进项转出总账，行号要一致，要展示11行，要一页展示
        // pageNum: 1,
        // pageSize: 11,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  created() {
    this.querySearchConfig1[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYY-MM');
      }
    }
  },
  async mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
    this.historyTableData = JSON.parse(JSON.stringify(this.tableData));
  },
  methods: {
    openAdjustDialog(row) {
      if (!row || typeof row !== 'object') {
        console.warn('Invalid row data:', row);
        return;
      }
      this.currentRow = { ...row }; // 深拷贝当前行数据
      this.adjustVisible = true;
    },
    async submitAdjustment(submitData) {
      try {
        const params = {
          uuid: this.currentRow.uuid,
          jxsezcxmDm: this.currentRow.jxsezcxmDm,
          zcse: submitData.zcse,
        };
        const { code } = await editJxsezczzswzd(params);

        if (code === 1) {
          // 根据实际返回结构判断成功
          this.$message.success('调整成功');
          this.query({ flag: true }); // 刷新数据
        } else {
          throw new Error('提交失败');
        }
      } catch (error) {
        console.error(error);
        this.$message.error('修改失败，请重试');
      } finally {
        this.adjustVisible = false;
      }
    },
    async qrhlcyxx(submitData) {
      console.log('忽略成功');
      console.log('this.hlVisible', this.hlVisible);
      try {
        const params = {
          uuid: this.tableData.find((t) => t.index === this.hlVisible.index)?.uuid,
          djxh: this.hlVisible.djxh,
          nsrsbh: this.hlVisible.nsrsbh,
          nsrmc: this.hlVisible.nsrmc,
          sszq: this.hlVisible.sszq,
          jxsezcxmDm: this.hlVisible.jxsezcxmDm,
          ywlxDm: '310',
          hlsy: submitData.hlsy, // 从参数获取处理后的数据
        };
        const { msg } = await cybdhl(params);
        console.log('cybdhl-msg', msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query({ flag: true });
        this.hlVisible = false;
      }
    },
    closeBox() {
      this.hlVisible = false;
    },
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    toggle(val) {
      this.validateDialogToggle = val;
    },
    async getQueryParamsList() {
      try {
        const { data } = await getJxsezczzInit();
        this.tableData = [];
        this.tableDataCls = [];
        for (let i = 0; i < data.length; i++) {
          this.tableData.push(data[i]);
          this.tableDataCls.push(data[i]);
        }
      } catch (e) {
        console.error(e);
      }
    },
    initQueryConditions() {
      // 确保不重置hzfzjg参数
      if (this.zszfjgFlag) {
        // 保留原有hzfzjg值
        const hzfzjgIndex = this.querySearchConfig.findIndex((item) => item.key === 'hzfzjg');
        if (hzfzjgIndex !== -1) {
          this.$set(this.querySearchConfig[hzfzjgIndex], 'value', '1');
          this.$set(this.formData, 'hzfzjg', '1');
        }
      } else {
        delete this.formData.hzfzjg;
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { p } = pm;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: 1,
        pageSize: 10,
      };
      if (p) {
        params = { ...p, ...params };
        // 确保p参数中的hzfzjg也被正确处理
        if (!this.zszfjgFlag && params.hzfzjg) {
          delete params.hzfzjg;
        }
        if (p.sszq) {
          p.sszq = dayjs(p.sszq).format('YYYY-MM');
          this.$refs.queryControl.setParams(p);
        }
      } else {
        const commonParams = {
          sszq: dayjs(this.formData.sszq).format('YYYYMM'),
          ...params,
        };

        params =
          this.zszfjgFlag && this.formData.hzfzjg ? { hzfzjg: this.formData.hzfzjg, ...commonParams } : commonParams;
      }
      try {
        const { data } = await queryJxsezczzList(params);
        // this.tableData = tableDataCls;
        this.tableData.splice(0, this.tableData.length, ...this.tableDataCls);
        console.log('this.tableData', this.tableData);
        console.log('this.tableDataCls', this.tableDataCls);
        for (let i = 0; i < this.tableData.length; i++) {
          this.$set(this.tableData[i], 'key', this.getKey());
          for (let j = 0; j < data.records.length; j++) {
            if (this.tableData[i].jxsezcxmDm === data.records[j].jxsezcxmDm) {
              if (['07', '08', '09', '10'].includes(data.records[j].jxsezcxmDm)) {
                this.tableData[i] = {
                  isEditing: '3',
                  tid: this.tableData[i].tid,
                  jxsezcxmDm: this.tableData[i].jxsezcxmDm,
                  jxsezcxmMc: this.tableData[i].jxsezcxmMc,
                  ...data.records[j],
                };
              } else if (data.records[j].ly === '1') {
                this.tableData[i] = {
                  isEditing: '1',
                  tid: this.tableData[i].tid,
                  jxsezcxmDm: this.tableData[i].jxsezcxmDm,
                  jxsezcxmMc: this.tableData[i].jxsezcxmMc,
                  ...data.records[j],
                };
              } else if (data.records[j].ly === '0') {
                this.tableData[i] = {
                  isEditing: '0',
                  tid: this.tableData[i].tid,
                  jxsezcxmDm: this.tableData[i].jxsezcxmDm,
                  jxsezcxmMc: this.tableData[i].jxsezcxmMc,
                  ...data.records[j],
                };
              }
            }
          }
        }
        console.log(this.tableData);
        this.tableData = JSON.parse(JSON.stringify(this.tableData));
        this.validateRules = [];
        // 由于申报状态存在变更，所以每次查询都要重新获取
        this.$store.commit('sbzt/setSbztCacheInfo', {});
        this.tableData.forEach(async (d, index) => {
          this.$set(d, 'index', index);
          // 新增转出税额校验逻辑
          // if (d.zcse < 0 && !['07', '08', '09', '10'].includes(d.jxsezcxmDm)) {
          //   l = {
          //     content: `转出税额为负数（${numberToPrice(d.zcse)}），可能存在数据异常`,
          //     index,
          //     type: 'error',
          //     djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          //     nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          //     nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
          //     sszq: d.sszq,
          //     jxsezcxmDm: d.jxsezcxmDm,
          //   };
          //   if (d.hlzt) {
          //     l = {
          //       ...l,
          //       finalContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
          //         'YYYY年MM月DD日',
          //       )}已忽略该差异。`,
          //     };
          //   } else {
          //     l = {
          //       ...l,
          //       extraContent: '，若该差异可忽略，请点击此“',
          //       extraHandleMsg: '忽略',
          //       finalContent: '”。',
          //     };
          //   }
          //   this.validateRules.push(l);
          // }
          if (d.cymsg) {
            let l = {
              content: d.cymsg,
              handleMsg: d.zxmsg,
              index,
              type: d.cybz,
              djxh: this.$store.state.zzstz.userInfo?.djxh || '',
              nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
              nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
              sszq: d.sszq,
              jxsezcxmDm: d.jxsezcxmDm,
            };
            if (d.hlzt) {
              if (await this.getSbztBySsq(d.sszq)) {
                l = {
                  ...l,
                  finalContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。`,
                };
              } else {
                l = {
                  ...l,
                  hlsy: d.hlzt.hlsy,
                  extraContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。若有修改，请点击此“`,
                  extraHandleMsg: '编辑',
                  finalContent: '”。',
                };
              }
            } else {
              l = {
                ...l,
                extraContent: '，若该差异可忽略，请点击此“',
                extraHandleMsg: '忽略',
                finalContent: '”。',
              };
            }
            this.validateRules.push(l);
          }
        });
      } catch (e) {
        console.error(e);
        this.tableData = [];
        this.validateRules = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    getSbztBySsq,
    check() {
      this.$router.push('/lsgl');
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    getData() {
      this.tableData = JSON.parse(JSON.stringify(this.historyTableData));
      this.query();
    },
    zcseClick(row) {
      console.log('openPage-row', row);
      this.$emit('openPage', {
        from: 'jxsezczz',
        data: {
          // zzuuid: row.uuid, //数据加工时无法加工zzuuid，暂时拿掉
          sszq: String(row.sszq),
          jxsezcxmDm: row.jxsezcxmDm,
          zzscbz: 'Y',
        },
        type: 'jxsezcmx',
      });
    },
    editRow(item, idx) {
      this.tableData[idx].isEditing = '2';
      console.log(this.tableData, '====', idx);
    },
    saveRow(item) {
      const obj = {
        uuid: item.uuid || '',
        sszq: this.sszq,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        jxsezcxmDm: item.jxsezcxmDm,
        jxsezcxmmc: item.jxsezcxmMc,
        zcse: item.zcse,
        jzjtse: item.jzjtse,
        tzlxDm: item.tzlxDm || '1',
        ly: item.ly || '1',
      };
      saveJxsezczz(obj).then((res) => {
        console.log('save', res);
        this.$message.success('保存成功');
        this.$set(item, 'isEditing', '1');
        this.tableData = JSON.parse(JSON.stringify(this.historyTableData));
        this.query();
      });
    },
    cancelRow(item) {
      if (!item.uuid) {
        this.$set(item, 'zcse', '0.00');
        this.$set(item, 'jzjtse', '0.00');
      }
      this.$set(item, 'isEditing', '1');
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.myTable.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    rowClassName({ rowIndex }) {
      if (rowIndex === this.activeRule.index) return 'active-row';
      return '';
    },
  },
};
</script>

<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
// .znsbSbBodyDiv {
//   padding-bottom: 0 !important;
// }
/deep/ .t-input {
  border-color: #4285f4;
}
/deep/.filter-btns {
  float: right;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
/deep/ .maximum .gov-validate-dialog-content {
  padding-bottom: 0;
}
img {
  width: 16px;
  height: 16px;
}
.adaption-body {
  overflow: hidden;
  flex: 1;
}
</style>
