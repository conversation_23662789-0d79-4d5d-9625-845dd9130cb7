<!--
 * @Descripttion: 台账-增值税一般纳税人销项发票明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-04-02 09:55:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue">
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        style="margin-top: 16px"
        ref="queryControl1"
        :config="querySearchConfigXxfpmx"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
      >
      </search-control-panel>
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        ref="queryControl2"
        :config="querySearchConfigHwlwmx"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
      >
      </search-control-panel>
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <!-- <t-button v-if="tabValue === '1'" theme="primary" @click="patchSave(true)">批量设置为即征即退</t-button>
          <t-button v-if="tabValue === '1'" theme="primary" @click="patchSave(false)">批量设置为非即征即退</t-button> -->
          <t-button
            variant="outline"
            theme="primary"
            @click="ckdg()"
            v-if="this.$store.state.isProduct.envValue && !this.xgmnsrFlag"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
        >
          <RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="0" label="销项发票明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="xxfpmxList"
            :columns="xxfpmxColumn"
            @select-change="rehandleSelectChange"
            :pagination="pagination"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 0)"
            :foot-data="xxfpMxfootData"
          >
            <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
            <template #fplxDm="{ row }">
              {{ fplxList.find((d) => d.value === row.fplxDm) && fplxList.find((d) => d.value === row.fplxDm).label }}
            </template>
            <template #cezsbz="{ row }">
              {{ { Y: '是', N: '否' }[row.cezsbz] || '' }}
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="货物服务明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="hwfpmxList"
            :columns="hwfpmxColumn"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="hwpagination"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 1)"
            :foot-data="xxfpHwMxfootData"
          >
            <template #xh="{ rowIndex }">{{
              (hwpagination.current - 1) * hwpagination.pageSize + rowIndex + 1
            }}</template>

            <!-- <template #fphm="{ row }">
            <t-button class="tableButton" variant="text" theme="primary" @click="showPdf">{{ row.fphm }}</t-button>
          </template> -->
            <template #fplxDm="{ row }">
              {{
                (fplxList.find((d) => d.value === row.fplxDm) && fplxList.find((d) => d.value === row.fplxDm).label) ||
                ''
              }}
            </template>
            <template #jsfsDm="{ row }">
              {{
                (zsfsList.find((d) => d.code === row.jsfsDm) && zsfsList.find((d) => d.code === row.jsfsDm).caption) ||
                ''
              }}
            </template>
            <template #zsxmDm="{ row }">
              {{
                (zsxmList.find((d) => d.value === row.zsxmDm) && zsxmList.find((d) => d.value === row.zsxmDm).label) ||
                ''
              }}
            </template>
            <template #sl="{ row }">
              {{ row.sl * 100 + '%' }}
            </template>
            <template #jzjtbz="{ row }">
              <div v-if="opFlag && row.uuid === currentRowUuid">
                <t-select v-model="currentSfwjzjt">
                  <t-option key="Y" label="是" value="Y" />
                  <t-option key="N" label="否" value="N" />
                </t-select>
              </div>
              <div v-else>
                <span>{{ { Y: '是', N: '否' }[row.jzjtbz] || '-' }}</span>
              </div>
            </template>
            <template #operation="{ row }">
              <div v-if="opFlag && row.uuid === currentRowUuid">
                <t-button
                  style="margin-right: 5px"
                  class="tableButton"
                  variant="text"
                  theme="primary"
                  @click="opSave(row)"
                  >保存</t-button
                >
                <t-button class="tableButton" variant="text" theme="primary" @click="opFlag = false">取消</t-button>
              </div>
              <div v-else>
                <t-button
                  class="tableButton"
                  variant="text"
                  theme="primary"
                  @click="
                    () => {
                      opFlag = true;
                      currentRowUuid = row.uuid;
                    }
                  "
                  >编辑</t-button
                >
              </div>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
    <Modal :visible="isModalVisible" @close="isModalVisible = false">
      <PdfViewer :pdfUrl="pdfUrl" />
    </Modal>
  </div>
</template>
<script>
import {
  initXxfpHwfwMxQuery,
  initXxfpMxQuery,
  xxfpHwfuMxSaveOrUpdate,
  initXxfpMxQueryHj,
  initXxfpHwfwMxQueryHj,
} from '@/pages/index/api/tzzx/zzstz/xxfp.js';
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import { ChartIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import { multiSelectHandle, computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import { xxfpmxColumns, hwfpmxColumns, querySearchConfig1, querySearchConfig2 } from './config.js';
import { slList, zsxmList, zsfsList, fplxList } from '../../config';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    Modal,
    PdfViewer,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.slList = slList;
    this.zsxmList = zsxmList;
    this.zsfsList = zsfsList;
    this.fplxList = fplxList;
    return {
      xxfpmxColumns,
      hwfpmxColumns,
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      querySearchConfig1,
      querySearchConfig2,
      querySearchConfigOneRules: {},
      picSrc: `${document.location.origin}/znsb/view/tzzx/fppy.png`,
      visible: false,
      tabValue: '0',
      formData1: {},
      formData2: {},
      xxfpmxList: [],
      hwfpmxList: [],
      checkBox: [],
      selectedRowKeys: [],
      tableLoading: false,
      opFlag: false,
      currentSfwjzjt: '',
      currentRowUuid: '',
      pdfUrl: '',
      isModalVisible: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      hwpagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      fromName: false,
      xxfpMxfootData: [],
      xxfpHwMxfootData: [],
      loadedTabs: { 0: false, 1: false }, // 新增tab加载状态标识
    };
  },
  created() {},
  computed: {
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    querySearchConfigXxfpmx() {
      if (this.xgmnsrFlag) {
        return this.querySearchConfig1.filter((item) => item.key !== 'sszq' && item.key !== 'cezsbz');
      }
      return this.querySearchConfig1.filter((item) => item.key !== 'sszqq' && item.key !== 'sszqz');
    },
    querySearchConfigHwlwmx() {
      if (this.xgmnsrFlag) {
        return this.querySearchConfig2.filter(
          (item) => item.key !== 'sszq' && item.key !== 'jsfsDm' && item.key !== 'jzjtbz',
        );
      }
      return this.querySearchConfig2.filter((item) => item.key !== 'sszqq' && item.key !== 'sszqz');
    },
    xxfpmxColumn() {
      const config = this.xgmnsrFlag
        ? this.xxfpmxColumns.filter((item) => item.colKey !== 'jsfsDm' && item.colKey !== 'cezsbz')
        : this.xxfpmxColumns;
      return this.shanxiyidongFlag
        ? config
        : config.filter((item) => item.colKey !== 'xsfnsrsbh' && item.colKey !== 'xsfmc');
    },
    hwfpmxColumn() {
      if (this.xgmnsrFlag) {
        return this.hwfpmxColumns.filter((item) => item.colKey !== 'jsfsDm' && item.colKey !== 'jzjtbz');
      }
      return this.hwfpmxColumns;
    },
    fpmxTzlx() {
      return this.xgmnsrFlag ? 'xgmnsrXxfpMxTz' : 'xxfpMxTz';
    },
    hwlwmxTzlx() {
      return this.xgmnsrFlag ? 'xgmnsrXxfpHwfwMxTz' : 'xxfpHwfwMxTz';
    },
    sszqToExtract() {
      return dayjs(this.tabValue * 1 ? this.formData2.sszq : this.formData1.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return this.tabValue * 1 ? this.hwlwmxTzlx : this.fpmxTzlx;
    },
    exportFileName() {
      return this.tabValue * 1 ? '销项发票货物服务明细台账' : '销项发票明细台账';
    },
    tzQueryParams() {
      const cxParam = this.xgmnsrFlag
        ? {
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
            ...(this.tabValue * 1
              ? { ...this.formData2, sl: this.formData2.sl ? this.formData2.sl : null }
              : this.formData1),
            sszqq: this.sszqqC(),
            sszqz: this.sszqzC(),
            fplxDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.fplxDm : this.formData1.fplxDm),
            jsfsDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.jsfsDm : this.formData1.jsfsDm),
            zsxmDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.zsxmDm : this.formData1.zsxmDm),
            pageNum: this.tabValue * 1 ? this.hwpagination.current : this.pagination.current,
            pageSize: this.tabValue * 1 ? this.hwpagination.pageSize : this.pagination.pageSize,
          }
        : {
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
            ...(this.tabValue * 1
              ? { ...this.formData2, sl: this.formData2.sl ? this.formData2.sl : null }
              : this.formData1),
            sszq: this.sszqC(),
            fplxDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.fplxDm : this.formData1.fplxDm),
            zsxmDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.zsxmDm : this.formData1.zsxmDm),
            pageNum: this.tabValue * 1 ? this.hwpagination.current : this.pagination.current,
            pageSize: this.tabValue * 1 ? this.hwpagination.pageSize : this.pagination.pageSize,
          };
      if (this.xgmnsrFlag) {
        this.$delete(cxParam, 'sszq');
      } else {
        this.$delete(cxParam, 'sszqq');
        this.$delete(cxParam, 'sszqz');
      }
      return cxParam;
    },
    exportButtonFlag() {
      return this.tabValue * 1 ? !this.hwfpmxList.length : !this.xxfpmxList.length;
    },
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
  },
  watch: {
    tabValue(newVal) {
      if (!this.loadedTabs[newVal]) {
        this.query();
        this.loadedTabs[newVal] = true;
      }
    },
  },
  mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
  },
  methods: {
    initTabValue() {
      this.tabValue = '0';
      console.log('initTabValue', this.tabValue);
    },
    sszqC() {
      return this.tabValue * 1
        ? dayjs(this.formData2.sszq).format('YYYYMM')
        : dayjs(this.formData1.sszq).format('YYYYMM');
    },
    sszqqC() {
      return this.tabValue * 1
        ? dayjs(this.formData2.sszqq).format('YYYYMM')
        : dayjs(this.formData1.sszqq).format('YYYYMM');
    },
    sszqzC() {
      return this.tabValue * 1
        ? dayjs(this.formData2.sszqz).format('YYYYMM')
        : dayjs(this.formData1.sszqz).format('YYYYMM');
    },
    getQueryParamsList() {
      // todo 调接口获取字典
      // this.querySearchConfig1[3].selectList = this.fplxList;
      // this.querySearchConfig2[3].selectList = this.xgmnsrFlag ? this.zsxmList.slice(0, 3) : this.zsxmList.slice(0, 5);
      // this.querySearchConfig2[4].selectList = this.zsfsList.map((d) => ({ label: d.caption, value: d.code }));
      this.querySearchConfig1.find((c) => c.key === 'fplxDm').selectList = this.fplxList;
      this.querySearchConfig2.find((c) => c.key === 'fplxDm').selectList = this.fplxList;
      this.querySearchConfig2.find((c) => c.key === 'zsxmDm').selectList = this.xgmnsrFlag
        ? this.zsxmList.slice(0, 3)
        : this.zsxmList.slice(0, 5);
      this.querySearchConfig2.find((c) => c.key === 'jsfsDm').selectList = this.zsfsList.map((d) => ({
        label: d.caption,
        value: d.code,
      }));
    },
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        console.log('url-params', params);
        if (params.get('skssqq')) {
          if (this.xgmnsrFlag) {
            this.$refs.queryControl1.setParams({
              sszqq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
              sszqz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
            });
            this.formData1.sszqq = dayjs(this.$route.query.skssqq).format('YYYYMM');
            this.formData1.sszqz = dayjs(this.$route.query.skssqz).format('YYYYMM');
            this.$refs.queryControl2.setParams({
              sszqq: dayjs(this.$route.query.skssqq).format('YYYY-MM'),
              sszqz: dayjs(this.$route.query.skssqz).format('YYYY-MM'),
            });
            this.formData2.sszqq = dayjs(this.$route.query.skssqq).format('YYYYMM');
            this.formData2.sszqz = dayjs(this.$route.query.skssqz).format('YYYYMM');
            return;
          }
          this.$refs.queryControl1.setParams({
            sszq: dayjs(params.get('skssqq')).format('YYYY-MM'),
          });
          this.formData1.sszq = dayjs(params.get('skssqq')).format('YYYYMM');
          this.$refs.queryControl2.setParams({
            sszq: dayjs(params.get('skssqq')).format('YYYY-MM'),
          });
          this.formData2.sszq = dayjs(params.get('skssqq')).format('YYYYMM');
          return;
        }
      }
      if (this.xgmnsrFlag) {
        this.$refs.queryControl1.setParams({
          sszqq: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
          sszqz: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
        });
        this.formData1.sszqq = dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM');
        this.formData1.sszqz = dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM');
        this.$refs.queryControl2.setParams({
          sszqq: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
          sszqz: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
        });
        this.formData2.sszqq = dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM');
        this.formData2.sszqz = dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM');
      } else {
        this.$refs.queryControl1.setParams({
          sszq: computeSszq(),
        });
        this.formData1.sszq = computeSszq();
        this.$refs.queryControl2.setParams({
          sszq: computeSszq(),
        });
        this.formData2.sszq = computeSszq();
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;

      // 当强制刷新或首次加载时更新状态
      if (flag) {
        this.loadedTabs[this.tabValue] = false;
        this.tabValue * 1 ? (this.hwpagination.current = 1) : (this.pagination.current = 1);
      }

      // 如果已经加载过且非强制刷新，直接返回
      if (this.loadedTabs[this.tabValue] && !flag) return;

      this.tableLoading = true;

      // p为父页面传参
      if (flag) this.tabValue * 1 ? (this.hwpagination.current = 1) : (this.pagination.current = 1);
      this.tableLoading = true;
      let params = {
        pageNum: this.tabValue * 1 ? this.hwpagination.current : this.pagination.current,
        pageSize: this.tabValue * 1 ? this.hwpagination.pageSize : this.pagination.pageSize,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      };
      if (p) {
        this.tabValue = p?.tabValue || '1';
        params = { ...p, ...params };
        if (p.sszq) {
          p.sszq = dayjs(p.sszq).format('YYYY-MM');
          p.sszqq = dayjs(p?.sszqq).format('YYYY-MM') || '';
          p.sszqz = dayjs(p?.sszqz).format('YYYY-MM') || '';
          this.$refs.queryControl2.setParams(p);
        }
      } else {
        this.formData2.sl = ''; // 去掉父传参中 税率
        params = {
          ...(this.tabValue * 1 ? this.formData2 : this.formData1),
          ...params,
          sszq: this.sszqC(),
          sszqq: this.sszqqC(),
          sszqz: this.sszqzC(),
          fplxDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.fplxDm : this.formData1.fplxDm),
          jsfsDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.jsfsDm : this.formData1.jsfsDm),
          zsxmDm: multiSelectHandle(this.tabValue * 1 ? this.formData2.zsxmDm : this.formData1.zsxmDm),
        };
        if (this.xgmnsrFlag) {
          this.$delete(params, 'sszq');
        } else {
          this.$delete(params, 'sszqq');
          this.$delete(params, 'sszqz');
        }
      }
      try {
        if (this.tabValue === '0') {
          const { data } = await initXxfpMxQuery(params);
          this.xxfpmxList = data.records || [];
          this.pagination.total = data.pageTotal;

          if (this.pagination.total > 0) {
            const { data } = await initXxfpMxQueryHj(params);
            this.xxfpMxfootData =
              [
                {
                  je: numberToPrice(data?.je),
                  se: numberToPrice(data?.se),
                  jshj: numberToPrice(data?.jshj),
                },
              ] || [];
          } else {
            this.xxfpMxfootData = [];
          }
          this.loadedTabs['0'] = true; // 设置已加载状态
        } else if (this.tabValue === '1') {
          const { data } = await initXxfpHwfwMxQuery(params);
          this.hwfpmxList = data.records || [];
          this.hwpagination.total = data.pageTotal;

          if (this.hwpagination.total > 0) {
            const { data } = await initXxfpHwfwMxQueryHj(params);
            this.xxfpHwMxfootData =
              [
                {
                  je: numberToPrice(data?.je),
                  se: numberToPrice(data?.se),
                  jshj: numberToPrice(data?.jshj),
                  kce: numberToPrice(data?.kce),
                },
              ] || [];
          } else {
            this.xxfpHwMxfootData = [];
          }
          this.loadedTabs['1'] = true; // 设置已加载状态
        }
      } catch (e) {
        [this.xxfpmxList, this.hwfpmxList][this.tabValue * 1] = [];
      } finally {
        this.tableLoading = false;
      }
    },
    showPdf() {
      console.log('route', this.picSrc);
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.checkBox = selectedRowData;
      this.selectedRowKeys = value;
    },
    async opSave(row) {
      console.log(row);
      if (!this.currentSfwjzjt) {
        this.$message.warning('请选择');
        return;
      }
      const params = [
        {
          ...row,
          jzjtbz: this.currentSfwjzjt,
          sszq: this.sszqC(),
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        },
      ];
      await xxfpHwfuMxSaveOrUpdate(params);
      // eslint-disable-next-line array-callback-return
      const index = this.hwfpmxList.findIndex((item) => item.uuid === row.uuid);
      console.log(index);
      if (index > -1) this.hwfpmxList[index].jzjtbz = this.currentSfwjzjt;
      this.selectedRowKeys = this.selectedRowKeys.filter((item) => item !== row.uuid);
      this.opFlag = false;
      this.currentSfwjzjt = '';
    },
    async patchSave(type) {
      const params = this.checkBox.map((d) => {
        return {
          ...d,
          jzjtbz: type ? 'Y' : 'N',
          sszq: this.sszqC(),
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        };
      });
      await xxfpHwfuMxSaveOrUpdate(params);
      this.$message.success('设置成功');
      this.selectedRowKeys = [];
      this.query();
    },
    ckdg() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }, type) {
      [this.pagination, this.hwpagination][type].current =
        pageSize !== [this.pagination, this.hwpagination][type].pageSize ? 1 : current;
      [this.pagination, this.hwpagination][type].pageSize = pageSize;
      this.loadedTabs[type === 0 ? '0' : '1'] = false; // 分页变化时重置加载状态
      this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
</style>
