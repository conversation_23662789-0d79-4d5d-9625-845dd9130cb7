import dayjs from 'dayjs';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberToCurrency';

export const xxfpmxColumns = [
  // {
  //   align: 'center',
  //   className: 'demo-multiple-select-cell',
  //   width: 60,
  //   colKey: 'row-select',
  //   type: 'multiple',
  // },
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    // width: '5%',
    width: 50,
    foot: '合计',
    // render(h, context) {
    //   const { row } = context;
    //   if (context.type === 'title') {
    //     return <div style="text-align:center">序号</div>;
    //   }
    // },
  },
  {
    width: 180,
    colKey: 'fplxDm',
    title: '发票类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'fpdm',
    title: '发票代码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 190,
    colKey: 'fphm',
    title: '发票号码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 140,
    colKey: 'je',
    title: '金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.je)}</div>,
  },
  {
    align: 'right',
    width: 120,
    colKey: 'se',
    title: '税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.se)}</div>,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'jshj',
    title: '价税合计',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.jshj)}</div>,
  },
  {
    width: 100,
    colKey: 'kprq',
    title: '开票日期',
  },
  {
    width: 160,
    colKey: 'gmfnsrsbh',
    title: '购方税号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 170,
    colKey: 'gmfnsrmc',
    title: '购方名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 180,
    colKey: 'xsfnsrsbh',
    title: '销售方纳税人识别号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 220,
    colKey: 'xsfmc',
    title: '销售方名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    align: 'center',
    colKey: 'cezsbz',
    title: '是否差额征税',
  },
];
export const hwfpmxColumns = [
  // {
  //   colKey: 'row-select',
  //   type: 'multiple',
  //   width: 60,
  //   resize: {
  //     minWidth: 60,
  //     maxWidth: 60,
  //   },
  //   fixed: 'left',
  // },
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
    foot: '合计',
  },
  {
    width: 180,
    colKey: 'fplxDm',
    title: '发票类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'fpdm',
    title: '发票代码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 190,
    colKey: 'fphm',
    title: '发票号码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'kprq',
    title: '开票日期',
  },
  {
    width: 180,
    colKey: 'gmfnsrsbh',
    title: '购方税号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 220,
    colKey: 'gmfnsrmc',
    title: '购方名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 190,
    colKey: 'hwhyslwfwmc',
    title: '货物或应税劳务、服务名称',
    ellipsisTitle: true,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 190,
    colKey: 'sphfwssflhbbm',
    title: '税收分类编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 80,
    colKey: 'ggxh',
    title: '规格型号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 100,
    colKey: 'fpspsl',
    title: '数量',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'fpspdj',
    title: '单价',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.fpspdj)}</div>,
  },
  {
    align: 'right',
    width: 80,
    colKey: 'dw',
    title: '单位',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 140,
    colKey: 'je',
    title: '金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.je)}</div>,
  },
  {
    align: 'right',
    width: 60,
    colKey: 'sl',
    title: '税率',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'se',
    title: '税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.se)}</div>,
  },
  {
    align: 'right',
    width: 140,
    colKey: 'jshj',
    title: '价税合计',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.jshj)}</div>,
  },
  {
    align: 'right',
    width: 120,
    colKey: 'kce',
    title: '扣除额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.kce)}</div>,
  },
  {
    width: 160,
    colKey: 'jsfsDm',
    title: '计税方式',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'zsxmDm',
    title: '征税项目',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    align: 'center',
    colKey: 'jzjtbz',
    title: '是否为即征即退',
    ellipsisTitle: true,
  },
  // {
  //   align: 'center',
  //   colKey: 'operation',
  //   title: '操作',
  //   width: 120,
  // },
];
export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '发票类型',
    key: 'fplxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
  {
    label: '是否差额征税',
    key: 'cezsbz',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '征税项目',
    key: 'zsxmDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票类型',
    key: 'fplxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '货物名称',
    key: 'hwhyslwfwmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '税收分类编码',
    key: 'sphfwssflhbbm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'kprqq',
    timeRange: 'end',
    clearable: true,
  },
  {
    label: '是否即征即退',
    key: 'jzjtbz',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
