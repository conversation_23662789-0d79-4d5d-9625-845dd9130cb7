<!--
 * @Descripttion: 台账-增值税一般纳税人海关缴款书明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-06 19:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
    </search-control-panel>
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <ExtractDataButton
          :sszq="sszqToExtract"
          :readyStatus="readyStatus"
          :isJxfpmx="isQuanshanOrTongyongguojiEnv"
          @query="query"
        />
        <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <t-button variant="outline" theme="primary" @click="patchSave(true)"
          ><ChevronDownRectangleIcon slot="icon" />批量设置为即征即退</t-button
        >
        <t-button variant="outline" theme="primary" @click="patchSave(false)"
          ><RectangleIcon slot="icon" />批量设置为非即征即退</t-button
        >
        <QsbButton />
      </gt-space>
      <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="mainColumns"
        height="100%"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
        :foot-data="footData"
      >
        <!-- :foot-data="footData" -->
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #bdkyyDm="{ row }">
          {{
            (bdkyyList.find((d) => d.value === row.bdkyyDm) && bdkyyList.find((d) => d.value === row.bdkyyDm).label) ||
            ''
          }}
        </template>
        <template #hxytDm="{ row }">
          {{
            (hxytList.find((d) => d.value === row.hxytDm) && hxytList.find((d) => d.value === row.hxytDm).label) || ''
          }}
        </template>
        <template #jzjtbz="{ row }">
          {{
            (sfjzjtList.find((d) => d.value === row.jzjtbz) && sfjzjtList.find((d) => d.value === row.jzjtbz).label) ||
            ''
          }}
        </template>
        <template #wsjg="{ row }">
          <span>{{ numberToPrice(row.wsjg) }}</span>
        </template>
        <template #skje="{ row }">
          <span>{{ numberToPrice(row.skje) }}</span>
        </template>
        <template #yxdkje="{ row }">
          <span>{{ numberToPrice(row.yxdkje) }}</span>
        </template>
        <template #yxdkse="{ row }">
          <span>{{ numberToPrice(row.yxdkse) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import ExportButton from '@/pages/index/components/ExportButton';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { hgwsmxQuery, jzjtSet, bdkyyGet, hgwsmxQueryHj } from '@/pages/index/api/tzzx/zzstz/hgjksmx.js';
import { ChartIcon, ChevronDownRectangleIcon, RectangleIcon, RollbackIcon } from 'tdesign-icons-vue';
import { computeSszq, multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberUtils.js';
import { querySearchConfig, mainColumns, querySearchConfigOneRules } from './config.js';
import { hxytList, bdkyyList, sfjzjtList } from '../../config';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    ChartIcon,
    ChevronDownRectangleIcon,
    RectangleIcon,
    RollbackIcon,
  },
  data() {
    this.mainColumns = mainColumns;
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.hxytList = hxytList;
    this.bdkyyList = bdkyyList;
    this.sfjzjtList = sfjzjtList;
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      tableData: [],
      footData: [],
      selectedRowKeys: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'hgwsMx';
    },
    exportFileName() {
      return '海关文书缴款';
    },
    tzQueryParams() {
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      return {
        ...this.formData,
        hxytDm: multiSelectHandle(this.formData.hxytDm),
        sszq: this.sszqC,
        ...djParam,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
    isQuanshanOrTongyongguojiEnv() {
      // 泉膳环境(jtbm === '000004')或通用国际环境(jtbm === '000005')
      const jtbm = this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm;
      return jtbm === '000004' || jtbm === '000005';
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
    this.querySearchConfig[1].selectList = this.hxytList;
    this.querySearchConfig[2].selectList = this.bdkyyList;
    this.querySearchConfig[8].selectList = this.sfjzjtList;
  },
  async mounted() {
    this.init();
  },
  methods: {
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    async init() {
      // 等需求明确后 调接口使用字典
      const { data } = await bdkyyGet();
      this.bdkyyList = data;
      this.querySearchConfig[2].selectList = this.bdkyyList;
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm; // 默认无需回到第一页，无父传参
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      // 不变的基础入参
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '100',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params }; // 基础入参+父传参
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6))); // 修改父传参YYYYMM为YYYY-MM给上面的时间框
        }
        this.$refs.queryControl.setParams(p); // 覆盖上方的查询条件为父传参
      } else {
        params = {
          ...this.formData,
          sszq: this.sszqC,
          hxytDm: multiSelectHandle(this.formData.hxytDm),
          ...params,
        }; // 没有父传参的话,传查询条件+基础入参.查询框sszq绑定为YYYY-MM，需要使用sszqC覆盖
      }
      try {
        const { data } = await hgwsmxQuery(params); // 不建议api.  建议使用接口真实尾缀，方便定位代码。
        this.tableData = data.records || [];
        // if (this.tableData.length > 1) this.footData = [data.records?.hj] || []; // 表格长度大于1时展示表尾总计行。
        this.pagination.total = data.pageTotal;

        if (this.pagination.total > 0) {
          const { data } = await hgwsmxQueryHj(params);
          this.footData =
            [
              {
                wsjg: numberToPrice(data?.wsjg),
                skje: numberToPrice(data?.skje),
                yxdkje: numberToPrice(data?.yxdkje),
                yxdkse: numberToPrice(data?.yxdkse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    async patchSave(type) {
      if (this.selectedRowKeys.length) {
        await jzjtSet({ jzjtbz: type ? 'Y' : 'N', uuidList: this.selectedRowKeys });
        this.$message.success('设置成功');
        this.selectedRowKeys = [];
        this.query();
      } else {
        this.$message.warning('请选中要执行批量设置的数据。');
      }
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      console.log(value, selectedRowData);
    },
    check() {
      this.$router.push('/lsgl');
    },
    // save() {},
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
</style>
