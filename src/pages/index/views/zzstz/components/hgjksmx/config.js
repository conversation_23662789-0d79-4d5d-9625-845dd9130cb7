import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigOneRules = {};
export const mainColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'xh',
    align: 'center',
    title: '序号',
    width: 50,
    foot: '合计',
  },
  {
    width: 200,
    colKey: 'hgjkshm',
    title: '海关专用缴款书号码',
  },
  {
    width: 180,
    colKey: 'jkdwrnsrsbh',
    title: '缴款单位税号',
  },
  {
    width: 200,
    colKey: 'jkdwrnsrmc',
    title: '缴款单位名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'tfrq',
    title: '填发日期',
  },
  {
    colKey: 'gxrzsj',
    title: '勾选日期',
    width: 100,
  },
  {
    colKey: 'hxytDm',
    title: '核选用途',
    width: 120,
  },
  {
    colKey: 'bdkyyDm',
    title: '不抵扣原因',
    width: 190,
  },
  {
    align: 'right',
    colKey: 'wsjg',
    title: '金额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'skje',
    title: '税额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'yxdkje',
    title: '可抵扣金额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'yxdkse',
    title: '可抵扣税额',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'jzjtbz',
    title: '是否为即征即退',
    width: 120,
    fixed: 'right',
  },
];
export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '核选用途',
    key: 'hxytDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '不抵扣原因',
    key: 'bdkyyDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '海关专用缴缴款书编号',
    key: 'hgjksbh',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '缴款单位税号',
    key: 'jkdwsh',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '缴款单位名称',
    key: 'jkdwmc',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '填发日期起',
    key: 'tfrqq',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'jkrqz',
    timeRange: 'start',
    clearable: true,
  },
  {
    label: '填发日期止',
    key: 'tfrqz',
    type: 'datepicker',
    value: '',
    popup: true,
    relation: 'jkrqq',
    timeRange: 'end',
    clearable: true,
  },
  {
    label: '是否为即征即退',
    key: 'sfwjzjt',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
