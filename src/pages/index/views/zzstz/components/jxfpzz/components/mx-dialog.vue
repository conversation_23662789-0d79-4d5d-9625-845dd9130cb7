<template>
  <css-dialog
    class="dialogCss"
    header="会计凭证明细"
    :visible.sync="isVisible"
    @close="onClose"
    :cancelBtn="'关闭'"
    :confirmBtn="null"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
            ><DownloadIcon slot="icon" />导出</t-button
          >
        </gt-space>
      </div>
      <div>
        <t-table
          row-key="uuid"
          height="440px"
          hover
          :data="tableData"
          :foot-data="footData"
          :columns="mainColumns"
          :loading="tableLoading"
        >
          <template #sl1="{ row }">
            {{ (row.sl1 && row.sl1 * 100 + '%') || '' }}
          </template>
          <template #kjfpDm="{ row }">
            <span>{{
              (kjfpList.find((d) => d.value === row.kjfpDm) && kjfpList.find((d) => d.value === row.kjfpDm).label) || ''
            }}</span>
          </template>
        </t-table>
      </div>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getKjpzMx } from '@/pages/index/api/tzzx/zzstz/jxfp.js';
import { getKjfp } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon } from 'tdesign-icons-vue';

// const xmxlkjfpdz = [
//   { xmxl: '0103', kjfpDm: 'F02' },
//   { xmxl: '0105', kjfpDm: 'F01' },
//   { xmxl: '0201', kmbm: '1221170000' },
// ];
export default {
  components: { CssDialog, DownloadIcon },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.mainColumns = [
      {
        colKey: 'serial-number',
        title: '序号',
        align: 'center',
        width: 50,
        foot: '合计',
      },
      {
        align: 'left',
        width: 80,
        colKey: 'gsh',
        title: '公司号',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        width: 120,
        colKey: 'lrzx',
        title: '利润中心',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        width: 210,
        colKey: 'cdefine4',
        title: '参考凭证号',
        ellipsis: true,
      },
      {
        align: 'left',
        colKey: 'kjpzbh',
        title: '凭证编号',
        width: 210,
      },
      {
        align: 'left',
        colKey: 'sl1',
        title: '税率',
        width: 80,
      },
      {
        align: 'left',
        colKey: 'kmbm',
        title: '科目编码',
        width: 160,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        colKey: 'kmmc',
        title: '科目名称',
        width: 220,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'left',
        colKey: 'kjfpmc',
        title: '分配',
        width: 180,
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        align: 'right',
        colKey: 'bbje',
        title: '本币金额',
        width: 120,
      },
    ];
    return {
      isVisible: true,
      dcLoading: false,
      tableLoading: false,
      tableData: [],
      footData: [],
      kjfpList: [],
    };
  },
  created() {},
  async mounted() {
    this.init();
  },
  methods: {
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'jxfpKjpzMxTz',
        fileName: '会计凭证明细',
        cxParam: {
          djxh: this.visible?.djxh,
          sszq: this.visible?.sszq,
          nsrsbh: this.visible?.nsrsbh,
          xmxl: this.visible?.xmxl,
          // kmbm: xmxlkjfpdz.find((i) => i.xmxl === this.visible?.xmxl)?.kmbm || '2221010101',
          // kjfpDm: xmxlkjfpdz.find((i) => i.xmxl === this.visible?.xmxl)?.kjfpDm || '',
        },
      };
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async init() {
      const res = await getKjfp({ ywDm: '500' });
      this.kjfpList = res?.data || [];
      console.log(this.visible);
      const { data } = await getKjpzMx({
        djxh: this.visible?.djxh,
        sszq: this.visible?.sszq,
        nsrsbh: this.visible?.nsrsbh,
        xmxl: this.visible?.xmxl,
        // kmbm: xmxlkjfpdz.find((i) => i.xmxl === this.visible?.xmxl)?.kmbm || '2221010101',
        // kjfpDm: xmxlkjfpdz.find((i) => i.xmxl === this.visible?.xmxl)?.kjfpDm || '',
      });
      this.tableData = data.records.dataList || [];
      this.footData =
        this.tableData.length > 0
          ? [
              {
                bbje: data.records?.hj.bbje,
              },
            ]
          : [];
    },
    onClose() {
      this.isVisible = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/ .t-table__content {
  // 表格高度修正
  height: 408px !important;
}
</style>
