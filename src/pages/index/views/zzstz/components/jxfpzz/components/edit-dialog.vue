<template>
  <css-dialog
    class="dialogCss"
    :header="['新增进项发票总账', '编辑进项发票总账'][visible.pageType]"
    :visible.sync="isVisible"
    @close="onClose"
    :closeOnOverlayClick="false"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="项目大类" name="xmdl">
              <t-select v-model="formData.xmdl" placeholder="请选择" clearable>
                <t-option v-for="item in xmdlList" :value="item.value" :label="item.label" :key="item.value"
                  ><div>{{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="项目小类" name="xmxl">
              <t-select v-model="formData.xmxl" placeholder="请选择" clearable>
                <t-option v-for="item in xmxlList" :value="item.value" :label="item.label" :key="item.value"
                  ><div>{{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="份数" name="fs">
              <gt-input-money v-model="formData.fs" theme="normal" :digit="0" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="金额" name="je">
              <gt-input-money v-model="formData.je" theme="normal" :digit="2" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税额" name="se">
              <gt-input-money v-model="formData.se" theme="normal" :digit="2" align="left" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { jxfpzzInsert, jxfpzzUpdate } from '@/pages/index/api/tzzx/zzstz/jxfp.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      sszq: [{ required: true, message: '必选', type: 'error' }],
      xmdl: [{ required: true, message: '必选', type: 'error' }],
      xmxl: [{ required: true, message: '必选', type: 'error' }],
      fs: [{ required: true, message: '必选', type: 'error' }],
      je: [{ required: true, message: '必选', type: 'error' }],
      se: [{ required: true, message: '必选', type: 'error' }],
    };
    return {
      isVisible: true,
      confirmLoading: false,
      rules: {},
      xmdlList: [],
      xmxlList: [],
      formData: {},
    };
  },
  created() {
    this.init();
  },
  mounted() {
    this.initData();
  },
  computed: {},
  methods: {
    init() {
      this.rules = this.baseRules;
      console.log(this.visible);
      this.xmdlList = [{ label: '申报抵扣的进项税额', value: '01' }];
      this.xmxlList = [
        { value: '0101', label: '本期认证相符且本期申报抵扣' },
        { value: '0103', label: '海关进口增值税专用缴款书' },
        { value: '0104', label: '农产品收购发票或者销售发票' },
        { value: '0105', label: '代扣代缴税收缴款凭证' },
        { value: '0108', label: '本期用于抵扣的旅客运输服务扣税凭证' },
      ];
    },
    initData() {
      this.$set(this.formData, 'sszq', computeSszq());
      this.$set(this.formData, 'xmdl', this.xmdlList[0].value);
      this.$set(this.formData, 'xmxl', this.xmxlList[0].value);
      if (this.visible.row?.uuid) {
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        ['uuid', 'sszq', 'xmdl', 'xmxl', 'fs', 'je', 'se'].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = [
          {
            ...p,
            sszq: Number(dayjs(p.sszq).format('YYYYMM')),
            nsrsbh: this.visible.otherObj.nsrsbh,
            nsrmc: this.visible.otherObj.nsrmc,
          },
        ];
        if (this.visible.row?.uuid) params[0].uuid = this.visible.row?.uuid;
        try {
          if (this.visible.pageType) {
            await jxfpzzUpdate(params);
          } else {
            await jxfpzzInsert(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateJmsmx');
          } else {
            this.$emit('updateJmsmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
</style>
