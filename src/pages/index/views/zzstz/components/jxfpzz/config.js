import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfigOneRules = {
  ssyfq: [{ required: true, message: '必填项', type: 'error' }],
  ssyfz: [{ required: true, message: '必填项', type: 'error' }],
  hzfzjg: [{ required: true, message: '必填项', type: 'error' }],
};
export const xmdlList = [
  { value: '01', label: '申报抵扣的进项税额' },
  { value: '02', label: '待抵扣进项税额' },
];
export const xmxlList1 = [
  { value: '0100', label: '增值税专用发票' },
  { value: '0101', label: '本期认证相符且本期申报抵扣' },
  { value: '0102', label: '前期认证相符且本期申报抵扣' },
  { value: '0103', label: '海关进口增值税专用缴款书' },
  { value: '0104', label: '农产品收购发票或者销售发票' },
  { value: '0105', label: '代扣代缴税收缴款凭证' },
  { value: '0106', label: '加计扣除农产品进项税额' },
  { value: '0107', label: '本期用于购建不动产的扣税凭证' },
  { value: '0108', label: '本期用于抵扣的旅客运输服务扣税凭证' },
  { value: '0109', label: '外贸企业进项税额抵扣证明' },
  { value: '0110', label: '其他扣税凭证' },
  { value: '0111', label: '铁路电子客票及航空运输电子客票行程单(电子发票)' },
  { value: '0112', label: '旅客运输扣税凭证（计算抵扣）' },
];
export const xmxlList2 = [
  { value: '0201', label: '本期认证相符且本期未申报抵扣' },
  { value: '0202', label: '按照税法规定不允许抵扣' },
  { value: '0203', label: '海关进口增值税专用缴款书' },
  { value: '0204', label: '农产品收购发票或者销售发票' },
  { value: '0205', label: '代扣代缴税收通用缴款书' },
  { value: '0206', label: '其他扣税凭证' },
];
export const mainColumns = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
    foot: '合计',
  },
  {
    align: 'center',
    width: 100,
    colKey: 'sszq',
    title: '所属月份',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'xmdl',
    title: '项目大类',
  },
  {
    align: 'left',
    width: 200,
    colKey: 'xmxl',
    title: '项目小类',
    ellipsis: true,
  },
  {
    align: 'right',
    colKey: 'fs',
    title: '份数',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'je',
    title: '金额',
    width: 150,
  },
  {
    align: 'right',
    colKey: 'se',
    title: '税额',
    width: 150,
  },
];
export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '汇总分支机构',
    key: 'hzfzjg',
    type: 'select',
    multiple: false,
    selectList: [
      { label: '是', value: '1' },
      { label: '否', value: '2' },
    ],
    value: 'Y',
    clearable: false,
  },
  {
    label: '项目大类',
    key: 'xmdl',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '项目小类',
    key: 'xmxl',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
