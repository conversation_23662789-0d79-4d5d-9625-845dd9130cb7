<!--* @Descripttion: 台账-增值税一般纳税人进项发票总账-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        class="znsbHeadqueryDiv"
        ref="queryControl"
        :config="querySearchConfigFinal"
        :formRules="querySearchConfigRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        @changeSelect="changeSelect"
      />
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            @query="query"
            :jxfpzzTqsjBranchFlag="true"
          />
          <t-button variant="outline" theme="primary" @click="ckdgpdf()" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <t-dropdown
            :options="[
              { content: '生成计提表', value: 1, onClick: () => scjtb() },
              { content: '提交审核', value: 2, onClick: () => tjsh() },
              { content: '查看计提凭证', value: 3, onClick: () => ckjtpz() },
            ]"
            trigger="click"
            v-if="this.$store.state.isProduct.envValue"
          >
            <t-button variant="outline" theme="primary">
              <ChevronRightCircleIcon slot="icon" /><span>计提</span>
            </t-button>
          </t-dropdown>
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <BatchExportButton
            v-if="quanshanFlag"
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="myTable"
          row-key="uuid"
          hover
          :height="dynamicHeight"
          :data="tableData"
          :columns="mainColumns"
          :pagination="pagination"
          :loading="tableLoading"
          @page-change="pageChange"
          :rowClassName="rowClassName"
          @row-click="(e) => (activeRule = e)"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #xmdl="{ row }">
            <span>{{
              (xmdlList.find((d) => d.value === row.xmdl) && xmdlList.find((d) => d.value === row.xmdl).label) || ''
            }}</span>
          </template>
          <template #xmxl="{ row }">
            <span>{{ [xmxlList1, xmxlList2][row.xmdl * 1 - 1].find((d) => d.value === row.xmxl).label }}</span>
          </template>
          <template #fs="{ row }">
            <span class="specText" @click="openPage(row)">{{ row.fs }}</span>
          </template>
          <template #je="{ row }">
            <span class="specText" @click="openPage(row)">{{ numberToPrice(row.je) }}</span>
          </template>
          <template #se="{ row }">
            <div
              v-if="row.cylxbz === 'error'"
              style="display: flex; justify-content: space-between; align-items: center; float: right"
            >
              <t-tooltip :showArrow="false" :destroyOnClose="false">
                <span class="specText" @click="openPage(row)">{{ numberToPrice(row.se) }}</span>
                <template #content>
                  <span>{{ row.cysm }}</span>
                  <span @click="mxVisible = row" style="margin-left: 10px; color: lightblue; cursor: pointer">
                    {{ row.xzmsg }}
                  </span>
                </template>
                <ErrorCircleFilledIcon :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }" />
              </t-tooltip>
            </div>
            <span class="specText" @click="openPage(row)" v-else>{{ numberToPrice(row.se) }}</span>
          </template>
        </t-table>
      </div>
      <ValidateDialog
        :validate-rules="validateRules"
        :handleMsg="true"
        :extraHandleMsg="true"
        @ruleClick="ruleClick"
        @toggle="toggle"
        @handleMsg="(item) => (mxVisible = item)"
        @extraHandleMsg="
          (item) => {
            this.hlVisible = {
              ...item,
              showHlxmFlag: false, // 不显示忽略项目
            };

            // 使用深拷贝确保响应式更新
            this.hlformData = {
              hlsy: item.hlsy || '',
            };
          }
        "
      />
      <!-- <div v-show="hlVisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="是否确定忽略该疑点差异信息"
          confirmBtn="是"
          cancelBtn="否"
          :onConfirm="qrhlcyxx"
          :onClose="closeBox"
        >
        </t-dialog>
      </div> -->
      <IgnoreDialog :visible="hlVisible" :formData="hlformData" @confirm="qrhlcyxx($event)" @close="closeBox" />
      <mxDialog :visible.sync="mxVisible" v-if="mxVisible" />
      <Modal :visible="isModalVisible1" @close="isModalVisible1 = false">
        <PdfViewer :pdfUrl="pdfUrl1" />
      </Modal>
      <Modal :visible="isModalVisible2" @close="isModalVisible2 = false">
        <PdfViewer :pdfUrl="pdfUrl2" />
      </Modal>
      <Modal :visible="isModalVisible3" @close="isModalVisible3 = false">
        <PdfViewer :pdfUrl="pdfUrl3" />
      </Modal>
    </div>
  </div>
</template>
<script>
// import { getTzDifferences } from '@/pages/index/api/tzzx/zzstz/gyapi.js';
import { initJxfpZzQuery } from '@/pages/index/api/tzzx/zzstz/jxfp.js';
import { cybdhl } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import dayjs from 'dayjs';
import ExportButton from '@/pages/index/components/ExportButton/index.vue';
import BatchExportButton from '@/pages/index/components/ExportButton/index-quanshan.vue';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import IgnoreDialog from '@/pages/index/components/IgnoreDialog/index.vue';
import { ErrorCircleFilledIcon, ChartIcon, ChevronRightCircleIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import { computeSszq, multiSelectHandle, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { querySearchConfig, mainColumns, xmdlList, xmxlList1, xmxlList2, querySearchConfigOneRules } from './config.js';
import mxDialog from './components/mx-dialog.vue';

export default {
  components: {
    QsbButton,
    ExportButton,
    BatchExportButton,
    ExtractDataButton,
    SkeletonFrame,
    SearchControlPanel,
    mxDialog,
    IgnoreDialog,
    ErrorCircleFilledIcon,
    ValidateDialog,
    ChartIcon,
    ChevronRightCircleIcon,
    PdfViewer,
    Modal,
  },
  data() {
    this.xmdlList = xmdlList;
    this.xmxlList1 = xmxlList1;
    this.xmxlList2 = xmxlList2;
    this.mainColumns = mainColumns;
    return {
      loading: true,
      isProduct: this.$store.state.isProduct.envValue,
      validateDialogToggle: true,
      pdfUrl: `${document.location.origin}/znsb/view/tzzx/zzsjfjsfsbdgybnsrsy.pdf`, // pdf文件路径
      pdfUrl1: `${document.location.origin}/znsb/view/tzzx/ckdg1.pdf`, // pdf文件路径
      pdfUrl2: `${document.location.origin}/znsb/view/tzzx/jtb.pdf`, // pdf文件路径
      pdfUrl3: `${document.location.origin}/znsb/view/tzzx/jtpz.pdf`, // pdf文件路径
      isModalVisible1: false,
      isModalVisible2: false,
      isModalVisible3: false,
      userInfo: {},
      formData: {},
      querySearchConfig,
      querySearchConfigOneRules,
      tableLoading: false,
      hlVisible: false,
      mxVisible: false,
      tableData: [],
      validateRules: [],
      activeRule: '',
      hlformData: {
        hlsy: '',
      },
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
  },
  computed: {
    zszfjgFlag() {
      return (
        this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000003' &&
        this.$store.state.zdmczh.companyDifferentiationConfig.fzjgbz === 'zjg'
      );
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    querySearchConfigFinal() {
      let querySearchConfigTemp = this.querySearchConfig;
      if (!this.zszfjgFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'hzfzjg');
      }
      if (this.xgmnsrFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'jsfsDm');
      }
      return querySearchConfigTemp;
    },
    querySearchConfigRules() {
      const querySearchConfigOneRulesTemp = JSON.parse(JSON.stringify(this.querySearchConfigOneRules));
      if (!this.zszfjgFlag) {
        delete querySearchConfigOneRulesTemp.hzfzjg;
        delete this.formData.hzfzjg;
      }
      return querySearchConfigOneRulesTemp;
    },
    ssyfqC() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    ssyfzC() {
      return dayjs(this.formData.ssyfz).format('YYYYMM');
    },
    sszqC() {
      return (sszq) => {
        if (sszq) return sszq.substring(0, 4).concat('-', sszq.substring(4, 6));
        return '';
      };
    },
    dynamicHeight() {
      let height = '100%';
      if (!this.validateDialogToggle) {
        return height;
      }
      switch (this.validateRules.length) {
        case 0:
          height = '100%';
          break;
        case 1:
          height = '85%';
          break;
        case 2:
          height = '80%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '70%';
          break;
      }
      return height;
    },
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'jxfpZzTz';
    },
    exportFileName() {
      return '进项发票总账台账';
    },
    tzQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        ...this.formData,
        ssyfq: this.ssyfqC,
        ssyfz: this.ssyfzC,
        xmxl: multiSelectHandle(this.formData.xmxl),
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.querySearchConfig[1].value = computeSszq();
    this.formData.ssyfq = computeSszq();
    this.formData.ssyfz = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.querySearchConfig[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
        this.formData.ssyfq = dayjs(this.$route.query.skssqq).format('YYYYMM');
        this.formData.ssyfz = dayjs(this.$route.query.skssqz).format('YYYYMM');
      }
    }
  },
  mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
  },
  methods: {
    async qrhlcyxx(submitData) {
      console.log('忽略成功');
      try {
        const params = {
          uuid: this.tableData.find((t) => t.index === this.hlVisible.index)?.uuid,
          djxh: this.hlVisible.djxh,
          nsrsbh: this.hlVisible.nsrsbh,
          nsrmc: this.hlVisible.nsrmc,
          sszq: this.hlVisible.sszq,
          ywlxDm: '500',
          hlsy: submitData.hlsy, // 从参数获取处理后的数据
        };
        const { msg } = await cybdhl(params);
        console.log('cybdhl-msg', msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query({ flag: true });
        this.hlVisible = false;
      }
    },
    getSbztBySsq,
    closeBox() {
      this.hlVisible = false;
    },
    toggle(val) {
      this.validateDialogToggle = val;
    },
    getQueryParamsList() {
      const xmdlIndex = this.querySearchConfig.findIndex((item) => item.key === 'xmdl');
      if (xmdlIndex !== -1) {
        this.querySearchConfig[xmdlIndex].selectList = this.xmdlList;
      }
    },
    initQueryConditions() {
      // 确保不重置hzfzjg参数
      if (this.zszfjgFlag) {
        // 保留原有hzfzjg值
        const hzfzjgIndex = this.querySearchConfig.findIndex((item) => item.key === 'hzfzjg');
        if (hzfzjgIndex !== -1) {
          this.$set(this.querySearchConfig[hzfzjgIndex], 'value', '1');
          this.$set(this.formData, 'hzfzjg', '1');
        }
      } else {
        delete this.formData.hzfzjg;
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (dayjs(this.formData.ssyfz).diff(dayjs(this.formData.ssyfq), 'month') > 2) {
        this.$message.warning('时间范围不能超过三个月');
        return;
      }
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        // 确保p参数中的hzfzjg也被正确处理
        if (!this.zszfjgFlag && params.hzfzjg) {
          delete params.hzfzjg;
        }
        if (params.ssyfq) {
          params.ssyfq = dayjs(params.ssyfq).format('YYYYMM');
        }
        if (params.ssyfz) {
          params.ssyfz = dayjs(params.ssyfz).format('YYYYMM');
        }
        this.$refs.queryControl.setParams(p);
      } else {
        // // 确保切换机构时重置hzfzjg值
        // if (!this.zszfjgFlag) {
        //   delete this.formData.hzfzjg;
        // }
        // params = {
        //   ...this.formData,
        //   ssyfq: this.ssyfqC,
        //   ssyfz: this.ssyfzC,
        //   xmxl: multiSelectHandle(this.formData.xmxl),
        //   ...params,
        // };
        const commonParams = {
          ssyfq: this.ssyfqC,
          ssyfz: this.ssyfzC,
          xmdl: this.formData.xmdl,
          xmxl: multiSelectHandle(this.formData.xmxl),
          ...params,
        };

        params =
          this.zszfjgFlag && this.formData.hzfzjg ? { hzfzjg: this.formData.hzfzjg, ...commonParams } : commonParams;
      }
      try {
        const { data } = await initJxfpZzQuery(params);
        this.tableData = data.records?.dataList || [];
        this.pagination.total = data.pageTotal;
        this.validateRules = [];
        // 由于申报状态存在变更，所以每次查询都要重新获取
        this.$store.commit('sbzt/setSbztCacheInfo', {});
        this.tableData.forEach(async (d, index) => {
          this.$set(d, 'index', index);
          if (d.cysm) {
            let l = {
              content: d.cysm,
              handleMsg: d.xzmsg,
              index,
              type: d.cylxbz,
              djxh: d.djxh,
              nsrsbh: d.nsrsbh,
              nsrmc: d.nsrmc,
              sszq: d.sszq,
              xmxl: d.xmxl,
            };
            if (d.hlzt) {
              if (await this.getSbztBySsq(d.sszq)) {
                l = {
                  ...l,
                  finalContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。`,
                };
              } else {
                l = {
                  ...l,
                  hlsy: d.hlzt.hlsy,
                  extraContent: `，操作人员${d.hlzt.hlczry}于${dayjs(d.hlzt.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${d.hlzt.hlsy}。若有修改，请点击此“`,
                  extraHandleMsg: '编辑',
                  finalContent: '”。',
                };
              }
            } else {
              l = {
                ...l,
                extraContent: '，若该差异可忽略，请点击此“',
                extraHandleMsg: '忽略',
                finalContent: '”。',
              };
            }
            this.validateRules.push(l);
          }
        });
        console.log('this.tableData', this.tableData);
      } catch (e) {
        this.tableData = [];
        this.validateRules = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    openPage(row) {
      // 根据项目小类确定下钻的页面 暂时传参只有所属月份 后续字典变化，需手动维护以下列表type
      const list = [
        { value: '0100', label: '增值税专用发票', type: 'jxfpmx', hxyt: '1' },
        { value: '0101', label: '本期认证相符且本期申报抵扣', type: 'jxfpmx', hxyt: '1' },
        { value: '0102', label: '前期认证相符且本期申报抵扣', type: 'jxfpmx', hxyt: '1' },
        { value: '0103', label: '海关进口增值税专用缴款书', type: 'hgjksmx' },
        { value: '0104', label: '农产品收购发票或者销售发票', type: 'ncpfpmx' },
        { value: '0105', label: '代扣代缴税收缴款凭证', type: 'dkdjjspzmx' },
        { value: '0106', label: '加计扣除农产品进项税额', type: 'ncpfpmx' },
        { value: '0107', label: '本期用于购建不动产的扣税凭证', type: 'gjbdcdkspzmx' },
        { value: '0108', label: '本期用于抵扣的旅客运输服务扣税凭证', type: 'lkysfwkspzmx' },
        { value: '0109', label: '外贸企业进项税额抵扣证明', type: 'ckhwznxzmmx' },
        { value: '0110', label: '其他扣税凭证', type: 'qtkspzzz' },
        { value: '0111', label: '铁路电子客票及航空运输电子客票行程单(电子发票)', type: 'lkysfwkspzmx', tabValue: '1' },
        { value: '0112', label: '旅客运输扣税凭证（计算抵扣）', type: 'lkysfwkspzmx', tabValue: '0' },
        { value: '0201', label: '本期认证相符且本期未申报抵扣', type: 'jxfpmx', hxyt: '2,3' },
        { value: '0202', label: '按照税法规定不允许抵扣', type: 'jxfpmx', hxyt: '2' },
        { value: '0203', label: '海关进口增值税专用缴款书', type: 'hgjksmx' },
        { value: '0204', label: '农产品收购发票或者销售发票', type: 'ncpfpmx' },
        { value: '0205', label: '代扣代缴税收通用缴款书', type: 'dkdjjspzmx' },
        { value: '0206', label: '其他扣税凭证', type: 'qtkspzzz' },
      ];
      const type = list.find((d) => d.value === row.xmxl)?.type;
      const hxyt = list.find((d) => d.value === row.xmxl).hxyt
        ? { hxytDm: list.find((d) => d.value === row.xmxl).hxyt }
        : {};
      const tabValue = list.find((d) => d.value === row.xmxl).tabValue
        ? { tabValue: list.find((d) => d.value === row.xmxl).tabValue }
        : {};
      (type &&
        this.$emit('openPage', { from: 'dkjxzz', data: { sszq: String(row.sszq), ...hxyt, ...tabValue }, type })) ||
        this.$message.error('跳转失败，无该台账');
    },
    changeSelect(value, item) {
      if (item.key === 'xmdl') {
        const xmxlIndex = this.querySearchConfig.findIndex((item) => item.key === 'xmxl');
        if (value === '01' && xmxlIndex !== -1) {
          this.formData.xmxl = '';
          this.querySearchConfig[xmxlIndex].selectList = this.xmxlList1;
        } else if (value === '02') {
          this.formData.xmxl = '';
          this.querySearchConfig[xmxlIndex].selectList = this.xmxlList2;
        } else {
          this.formData.xmxl = '';
        }
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    ckdg() {
      this.$router.push('/lsgl');
    },
    ckdgpdf() {
      // 打开本地pdf文件
      this.isModalVisible1 = true;
      this.pdfUrl1 = `${document.location.origin}/znsb/view/tzzx/ckdg1.pdf`;
      console.log(this.pdfUrl1, 'pdfUrl1');
    },
    scjtb() {
      // 打开生成计提表pdf文件
      this.isModalVisible2 = true;
      this.pdfUrl2 = `${document.location.origin}/znsb/view/tzzx/jtb.pdf`;
      console.log(this.pdfUrl2, 'pdfUrl2');
    },
    tjsh() {
      // 提交审核
      this.$message.success('提交审核成功');
    },
    ckjtpz() {
      // 打开查看计提凭证pdf文件
      this.isModalVisible3 = true;
      this.pdfUrl3 = `${document.location.origin}/znsb/view/tzzx/jtpz.pdf`;
      console.log(this.pdfUrl3, 'pdfUrl3');
    },
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.myTable.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    rowClassName({ rowIndex }) {
      if (rowIndex === this.activeRule.index) return 'active-row';
      return '';
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
