<!--
 * @Descripttion: 台账-其他扣税凭证总账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-11 14:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    >
      <template #t1><span></span></template>
      <template #t2><span></span></template>
    </search-control-panel>
    <!-- :formRules="querySearchConfigOneRules" -->
    <div class="queryBtns">
      <gt-space size="10px">
        <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
          ><ChartIcon slot="icon" />查看底稿</t-button
        >
        <ExportButton
          :pagingFlag="false"
          :tzlx="tzlx"
          :fileName="exportFileName"
          :tzQueryParams="tzQueryParams"
          :exportButtonFlag="exportButtonFlag"
        />
        <QsbButton />
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="key"
        hover
        :data="tableData"
        :columns="fpzzColumns"
        :editable-row-keys="editableRowKeys"
        height="100%"
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        @row-edit="onRowEdit"
        @row-validate="onRowValidate"
        @validate="onValidate"
        :loading="tableLoading"
      >
        <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
        <template #dkfs2="{ row }">
          <div style="float: right">
            <span>{{ row.dkfs2 }}</span>
          </div>
        </template>
        <template #dkje2="{ row }">
          <div style="float: right">
            <span
              v-if="row.qtkspzxmlx === '1'"
              class="specText"
              @click="openLkysfwkspzmx(row)"
              style="color: #0052d9; text-decoration: underline"
              >{{ numberToPrice(row.dkje2) }}</span
            >
            <span v-else>{{ numberToPrice(row.dkje2) }}</span>
          </div>
        </template>
        <template #dkse="{ row }">
          <div style="float: right">
            <span
              v-if="row.qtkspzxmlx === '1'"
              class="specText"
              @click="openLkysfwkspzmx(row)"
              style="color: #0052d9; text-decoration: underline"
              >{{ numberToPrice(row.dkse) }}</span
            >
            <span v-else>{{ numberToPrice(row.dkse) }}</span>
          </div>
        </template>
        <template #ddkje="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.ddkje) }}</span>
          </div>
        </template>
        <template #ddkfs="{ row }">
          <div style="float: right">
            <span>{{ row.ddkfs }}</span>
          </div>
        </template>
        <template #ddkse="{ row }">
          <div style="float: right">
            <span>{{ numberToPrice(row.ddkse) }}</span>
          </div>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { computeSszq, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { ChartIcon } from 'tdesign-icons-vue';
import { qtkspzzzQuery, qtkspzzzUpdate } from '@/pages/index/api/tzzx/zzstz/qtkspzzz.js';
import { MessagePlugin, Select, InputNumber } from 'tdesign-vue';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { querySearchConfig } from './config.js';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    ChartIcon,
  },
  data() {
    return {
      loading: true,
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      editableRowKeys: ['1'],
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      selectedRowKeys: [],
      qtkspzxmlxList: [
        { value: '1', label: '国内旅客运输服务' },
        { value: '2', label: '尚未抵扣完毕的不动产或者不动产在建工程' },
        { value: '3', label: '固定资产、无形资产、不动产转变用途可以抵扣的进项税额' },
        { value: '4', label: '桥、闸通行费' },
      ],
      tableData: [],
      formData: {},
      querySearchConfig,
      id: dayjs().unix(),
      tableLoading: false,
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
    };
  },
  created() {
    this.querySearchConfig[0].value = computeSszq();
    this.formData.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
  },
  mounted() {
    this.checkAllowEdit();
  },
  computed: {
    fpzzColumns() {
      return [
        {
          width: 50,
          align: 'center',
          colKey: 'xh',
          title: '序号',
        },
        {
          colKey: 'qtkspzxmlx',
          title: '其他扣税凭证项目类型',
          cell: (h, { row }) => this.qtkspzxmlxList.find((t) => t.value === row.qtkspzxmlx)?.label,
          width: 300,
          edit: {
            component: Select,
            // props, 透传全部属性到 Select 组件
            props: {
              readonly: true,
              options: this.qtkspzxmlxList,
            },
            // 校验规则，此处同 Form 表单
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'dkfs2',
          title: '抵扣份数',
          ellipsis: true,
          width: 75,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'dkje2',
          title: '抵扣金额',
          ellipsis: true,
          width: 80,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'dkse',
          title: '抵扣税额',
          ellipsis: true,
          width: 80,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'ddkfs',
          title: '待抵扣份数',
          ellipsis: true,
          width: 75,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'ddkje',
          title: '待抵扣金额',
          ellipsis: true,
          width: 80,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'ddkse',
          title: '待抵扣税额',
          ellipsis: true,
          width: 80,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 75,
          foot: '-',
          cell: (h, { row }) => {
            if (row.qtkspzxmlx === '1' || row.qtkspzxmlx === '2') {
              return null;
            }
            if (row.ly !== '1') {
              return null;
            }
            // if (row.zzscbz === 'Y') {
            //   return null;
            // }
            const editable = this.editableRowKeys.includes(row.key);

            return (
              <div>
                {!editable && (
                  <t-link
                    theme="primary"
                    hover="color"
                    disabled={!this.allowEdit}
                    data-id={row.key}
                    onClick={this.onEdit}
                  >
                    编辑
                  </t-link>
                )}
                {editable && (
                  <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
    sszqToExtract() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return 'qtkspzZz';
    },
    exportFileName() {
      return '其他扣税凭证总账表';
    },
    tzQueryParams() {
      return {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        sszq: this.formData.sszq.substring(0, 4) + this.formData.sszq.substring(5, 7),
      };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  watch: {
    formData: {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const { formData } = this;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    openLkysfwkspzmx(row) {
      this.$emit('openPage', {
        from: 'qtkspzzz',
        data: {
          sszq: String(row.sszq),
          zzscbz: 'Y',
        },
        type: 'lkysfwkspzmx',
      });
    },
    onEdit(e) {
      console.log(this.tableData, 'this.tableData');
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
        console.log('id1');
      } else {
        id = e.currentTarget.dataset.id;
      }

      // if (!this.editableRowKeys.includes(id)) {
      this.editableRowKeys.push(id);
      // }
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            this.tableData.splice(current.rowIndex, 1, current.editedRow);
            // MessagePlugin.success('保存成功');
            this.update(current.editedRow);
          }
          // 关闭编辑/保存折叠按钮
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        console.log('Promise Table Data Validate:', params);
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          MessagePlugin.warning(firstError[0].message);
        }
      });
    },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async query(pm = { p: false }) {
      const { p } = pm;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      this.tableLoading = true;
      if (p) {
        params = { ...p, ...params };
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
        }
        this.$refs.queryControl.setParams(p);
      } else {
        params = { sszq: this.formData.sszq.substring(0, 4) + this.formData.sszq.substring(5, 7), ...params };
      }
      try {
        const { data } = await qtkspzzzQuery(params);
        console.log(data.records, 'data.records');
        this.tableData = data.records || [];
        this.defaultLength = this.tableData.length || 0;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        this.tableData.sort((a, b) => {
          return Number(a.qtkspzxmlx) - Number(b.qtkspzxmlx);
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    async update(params) {
      try {
        console.log('updateData', params);
        const { data } = await qtkspzzzUpdate(params);
        console.log('qtkspzzzUpdate-data', data);
        this.$message.success(data);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
.znsbSbBodyDiv {
  padding-bottom: 50px !important;
}
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
</style>
