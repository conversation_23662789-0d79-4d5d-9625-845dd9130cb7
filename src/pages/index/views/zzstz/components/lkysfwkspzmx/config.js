import dayjs from 'dayjs';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';

export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    label: '',
    key: 't1',
  },
  {
    label: '',
    key: 't2',
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszq',
  },
  {
    label: '发票类型',
    key: 'fplxDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    filterable: true,
  },
  {
    label: '发票号码',
    key: 'fphm',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '开票日期起',
    key: 'kprqq',
    type: 'datepicker',
    // value: dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'kprqz',
    timeRange: 'start',
  },
  {
    label: '开票日期止',
    key: 'kprqz',
    type: 'datepicker',
    // value: dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'kprqq',
  },
  {
    label: '核选用途',
    key: 'hxytDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const dataColumns1 = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    colKey: 'gsh2',
    title: '公司号',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'lrzx',
    title: '利润中心',
    width: 120,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmdm',
    title: '科目编码',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kmmc',
    title: '科目名称',
    width: 300,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'kjpzbh',
    title: '凭证编号 ',
    width: 210,
  },
  {
    colKey: 'kjfp',
    title: '分配 ',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'pztt',
    title: '凭证抬头',
    width: 160,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'bxpzlxmc',
    title: '旅客运输凭证类型',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 80,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'kdje',
    title: '可抵金额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'kdse',
    title: '可抵税额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'fs',
    title: '份数',
    width: 100,
  },
  {
    colKey: 'tzyy',
    title: '调整原因',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tzsj',
    title: '调整时间',
    width: 200,
  },
  {
    colKey: 'czr',
    title: '操作人',
    width: 100,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 100,
    foot: '-',
    fixed: 'right',
  },
];
export const dataColumns2 = [
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 200,
    colKey: 'fplxDm',
    title: '发票类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 80,
    colKey: 'fpdm',
    title: '发票代码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 190,
    colKey: 'fphm',
    title: '发票号码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 110,
    colKey: 'je',
    title: '金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 110,
    colKey: 'se',
    title: '税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'jshj',
    title: '价税合计',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    width: 100,
    colKey: 'kprq',
    title: '开票日期',
    cell: (h, { row }) => <div>{row.kprq ? dayjs(row.kprq).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    align: 'center',
    colKey: 'skssq',
    title: '税款所属期',
    width: 110,
  },
  {
    colKey: 'xfsh',
    title: '销方税号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'xfmc',
    title: '销方名称',
    width: 260,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'gxrq',
    title: '勾选日期 ',
    width: 100,
    cell: (h, { row }) => <div>{row.gxrzsj ? dayjs(row.gxrzsj).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    colKey: 'hxytDm',
    title: '核选用途 ',
    width: 110,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 120,
    colKey: 'bdkyyDm',
    title: '不抵扣原因',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 110,
    colKey: 'dkje',
    title: '可抵扣金额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 110,
    colKey: 'dkse',
    title: '可抵扣税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
