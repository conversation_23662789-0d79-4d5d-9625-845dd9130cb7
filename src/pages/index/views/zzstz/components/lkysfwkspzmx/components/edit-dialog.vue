<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增旅客运输服务扣税明细账', '编辑旅客运输服务扣税明细账'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker v-model="formData.sszq" mode="month" clearable style="width: 100%" :disabled="!sbgzFlag" />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item :label="formLabels.lrzx" name="lrzx">
              <t-select v-model="formData.lrzx" :placeholder="`请选择${formLabels.lrzx}`" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="!this.dalianzhonggongFlag">
            <t-form-item :label="formLabels.kjfp" name="kjfp">
              <t-select
                v-model="formData.kjfp"
                :placeholder="`请选择${formLabels.kjfp}信息`"
                clearable
                @change="getbxpzlxDm(formData.kjfp)"
              >
                <t-option v-for="item in kjfpList" :value="item.value" :label="item.value" :key="item.value"></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="旅客运输凭证类型" name="bxpzlxDm">
              <t-select v-model="formData.bxpzlxDm" placeholder="请选择旅客运输凭证类型" disabled>
                <t-option
                  v-for="item in bxpzlxList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="visible.pageType">
            <t-form-item label="凭证编号" name="kjpzbh">
              <t-input :maxlength="30" v-model="formData.kjpzbh" placeholder="请填写凭证编号" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmdm">
              <t-select
                v-model="formData.kmdm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmdm)"
              >
                <t-option v-for="item in kmdmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="kmdm_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="可抵金额" name="kdje">
              <gt-input-money v-model="formData.kdje" theme="normal" align="left" clearable />
              <!-- <t-input-number
                v-model="formData.kdje"
                theme="column"
                :decimal-places="2"
                placeholder="请填写可抵金额"
                clearable
            /> -->
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-select v-model="formData.sl1" placeholder="请选择税率" clearable>
                <t-option
                  v-for="item in slList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"
                ></t-option> </t-select
            ></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="可抵税额" name="kdse">
              <!-- <t-input-number
                v-model="formData.kdse"
                theme="column"
                :decimal-places="2"
                placeholder="请填写可抵税额"
                clearable
            /> -->
              <gt-input-money v-model="formData.kdse" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="份数" name="fs">
              <gt-input-money v-model="formData.fs" theme="normal" :digit="fsdigit" align="left" clearable />
              <!-- <t-input-number
                v-model="formData.fs"
                theme="column"
                :decimal-places="0"
                placeholder="请填写份数"
                clearable
            /> -->
            </t-form-item>
          </t-col>
        </t-row>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="调整原因" name="tzyy">
              <t-textarea :maxlength="150" v-model="formData.tzyy" placeholder="请填写调整原因" clearable
            /></t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx, getKmbmByywbm, getJsfssldzb, getLkysfwpzfpdzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { lkysfwkspzSave, lkysfwkspzUpdate } from '@/pages/index/api/tzzx/zzstz/lkysfwkspzmx.js';
import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    this.baseRules = {
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      kjfp: [{ required: true, message: '必填', type: 'error' }],
      // kjpzbh: [{ required: true, message: '必填', type: 'error' }],
      bxpzlxDm: [{ required: true, message: '必填', type: 'error' }],
      kmdm: [{ required: true, message: '必填', type: 'error' }],
      kmmc: [{ required: true, message: '必填', type: 'error' }],
      kdje: [{ required: true, message: '必填', type: 'error' }],
      sl1: [{ required: true, message: '必填', type: 'error' }],
      kdse: [{ required: true, message: '必填', type: 'error' }],
      fs: [
        { required: true, message: '必填', type: 'error' },
        { pattern: /^[1-9]\d*$/, message: '请输入非0正整数', type: 'error' },
      ],
    };
    return {
      fsdigit: 0,
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [], // 利润中心
      slList: [],
      kjfpList: [],
      kmdmList: [],
      bxpzlxList: [],
      jsfsSldzList: [],
      formData: {
        sszq: '',
        uuid: '',
        gsh2: '',
        lrzx: '',
        kjfp: '',
        bxpzlxDm: '',
        kjpzbh: '',
        kmdm: '',
        kmmc: '',
        sl1: '',
        fs: '1',
        tzyy: '',
      },
    };
  },
  created() {},
  async mounted() {
    this.getJsfssldzb();
    await this.getLrzx();
    this.getLkyskmdm();
    // 2025.7.1 暂时写死了，用不上此配置
    // await this.getLkysfwpzfpdzb();
    this.init();
  },
  computed: {
    // 新增计算属性获取所有表单项标签
    formLabels() {
      const config = this.$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig;
      // 将数组转换为对象格式
      const labels = Array.isArray(config)
        ? config.reduce((acc, { item, displayName }) => {
            acc[item] = displayName;
            return acc;
          }, {})
        : {};

      // 返回转换后的对象，确保包含默认值
      return {
        ...labels,
        lrzx: labels.lrzx || '利润中心', // 如果配置中没有lrzx，使用默认值
        kjfp: labels.kjfp || '分配', // 如果配置中没有kjfp，使用默认值
      };
    },
    sbgzFlag() {
      return this.$store.state.sbzt.sbgzEnterFlag;
    },
    dalianzhonggongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000002';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
  },
  methods: {
    getbxpzlxDm(val) {
      const { label } = this.kjfpList.find((item) => item.value === val);
      this.formData.bxpzlxDm = this.bxpzlxList.find((item) => item.value === label)?.value;
    },
    async init() {
      // 当dalianzhonggongFlag为true时，去掉kjfp的必录校验
      if (this.dalianzhonggongFlag) {
        // 创建一个不包含kjfp必录规则的新规则对象
        const { kjfp, ...otherRules } = this.baseRules;
        this.rules = otherRules;
      } else {
        this.rules = this.baseRules;
      }

      this.slList = [
        { value: 0.17, label: '17%' },
        { value: 0.16, label: '16%' },
        { value: 0.13, label: '13%' },
        { value: 0.1, label: '10%' },
        { value: 0.09, label: '9%' },
        { value: 0.06, label: '6%' },
        { value: 0.05, label: '5%' },
        { value: 0.03, label: '3%' },
        { value: 0.01, label: '1%' },
      ];
      this.bxpzlxList = [
        { value: '1', label: '航空运输电子客票行程单' },
        { value: '2', label: '铁路车票' },
        { value: '3', label: '公路、水路等其他客票' },
      ];
      this.kjfpList = this.tongyongguojiFlag
        ? [
            { value: '机票', label: '1' },
            { value: '火车', label: '2' },
            { value: '其他客票', label: '3' },
          ]
        : [
            { value: '机票', label: '1' },
            { value: '火车', label: '2' },
            { value: '交通工具-巴士', label: '3' },
            { value: '交通工具-出租车（含网约车）', label: '3' },
            { value: '交通工具-轮船', label: '3' },
            { value: '市内交通费', label: '3' },
            { value: '出差中其他费用', label: '3' },
          ];
      console.log(this.visible);
      // this.getLkysKjpzbh();
      if (this.sbgzFlag) {
        this.$set(this.formData, 'sszq', dayjs(this.$store.state.sbzt.sbgzSsyfInfo.ssyfq).format('YYYY-MM'));
      } else {
        this.$set(this.formData, 'sszq', computeSszq());
      }
      if (this.visible.row?.lrzx) {
        this.formData = { ...this.visible.row, sszq: dayjs(String(this.visible.row.sszq)).format('YYYY-MM') };
      }
    },
    async getJsfssldzb() {
      const { data } = await getJsfssldzb();
      this.jsfsSldzList = data;
      this.slList = this.uniqueObjects(data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      const index0 = this.slList.findIndex((t) => t.value === 0);
      index0 > 1 ? this.slList.splice(index0, 1) : null;
      console.log('jsfsSldzList', this.jsfsSldzList);
      console.log('slList', this.slList);
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async getLkyskmdm() {
      if (this.tongyongguojiFlag) {
        this.kmdmList = [{ label: '应交税费_应交增值税_进项税额', value: '22210101' }];
      } else {
        const { data } = await getKmbmByywbm({ ywlxDm: '410' });
        this.kmdmList = data;
      }
      this.formData.kmdm = this.kmdmList[0]?.value;
      this.getXzKmmc(this.formData.kmdm);
    },
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
    },
    // getLkysKjpzbh() {
    //   this.formData.kjpzbh = '************';
    // },
    async getLkysfwpzfpdzb() {
      const { data } = await getLkysfwpzfpdzb();
      if (data.length) {
        this.kjfpList = data;
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'sszq',
          'uuid',
          'lrzx',
          'kjpzbh',
          'kjfp',
          'bxpzlxDm',
          'kmdm',
          'kmmc',
          'sl1',
          'kdje',
          'kdse',
          'fs',
          'tzyy',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = {
          ...p,
          oldkjpzbh: this.visible.oldkjpzbh,
          ...this.visible.otherObj,
          sszq: Number(dayjs(p.sszq).format('YYYYMM')),
        };
        // 通用国际企业分支：额外添加lrzx和gsh字段
        if (this.tongyongguojiFlag) {
          const lrzxItem = this.lrzxList.find((item) => item.value === this.formData.lrzx);
          params.lrzx = this.formData.lrzx; // 确保使用当前选中的value
          params.gsh2 = lrzxItem ? lrzxItem.label : ''; // 使用对应的label作为gsh
        }
        try {
          console.log('params', params);
          if (this.visible.pageType) {
            await lkysfwkspzUpdate(params);
          } else {
            await lkysfwkspzSave([params]);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateLkysfwkspzmx', { flag: true });
          } else {
            this.$emit('updateLkysfwkspzmx', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    getXzKmmc(kmdm) {
      this.formData.kmmc = this.kmdmList.find((item) => item.value === kmdm)?.label;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
#kmdm_options {
  width: 370px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
