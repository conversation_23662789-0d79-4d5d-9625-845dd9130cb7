<!--
 * @Descripttion: 台账-旅客运输服务扣税凭证明细账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-07 14:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue">
      <search-control-panel
        ref="queryControl1"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        :config="querySearchConfig1"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
        <template #t1><span></span></template>
        <template #t2><span></span></template>
      </search-control-panel>
      <search-control-panel
        ref="queryControl2"
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        :config="querySearchConfig2"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      >
      </search-control-panel>
      <!-- :formRules="querySearchConfigOneRules" -->
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button v-if="tabValue === '0'" theme="primary" @click="newOrEditRow({})" :disabled="!allowEdit"
            ><add-icon slot="icon" />新增</t-button
          >
          <t-button v-if="tabValue === '0'" theme="primary" @click="delRow" :disabled="!allowEdit"
            ><DeleteIcon slot="icon" />删除</t-button
          >
          <ExtractDataButton
            v-if="tabValue === '0'"
            variant="outline"
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            @query="query"
          />
          <ExtractDataButton v-else :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-button variant="outline" theme="primary" @click="check" v-if="this.$store.state.isProduct.envValue"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <ExportButton
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
        </gt-space>
        <t-button variant="outline" theme="primary" v-if="fromName" @click="goBack"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="0" label="凭证明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            hover
            :data="tableData1"
            :columns="tableColumns"
            height="100%"
            lazyLoad
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination1"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 0)"
            :foot-data="pzFootData"
          >
            <template #xh="{ rowIndex }">{{
              (pagination1.current - 1) * pagination1.pageSize + rowIndex + 1
            }}</template>
            <template #sl1="{ row }">
              {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
            </template>
            <template #fs="{ row }">
              <div style="float: right">
                <span>{{ row.fs }}</span>
              </div>
            </template>
            <template #kdje="{ row }">
              <div style="float: right">
                <span>{{ numberToPrice(row.kdje) }}</span>
              </div>
            </template>
            <template #kdse="{ row }">
              <div style="float: right">
                <span>{{ numberToPrice(row.kdse) }}</span>
              </div>
            </template>
            <template #operation="{ row }">
              <t-link
                theme="primary"
                hover="color"
                @click="newOrEditRow(row)"
                v-show="row.ly === '1'"
                :disabled="!allowEdit"
              >
                编辑
              </t-link>
            </template>
          </t-table>
          <EditDialogQS
            :visible.sync="editDialogVisible"
            v-if="editDialogVisible && quanshanFlag"
            @updateLkysfwkspzmx="(item) => query(item)"
          />
          <EditDialog
            :visible.sync="editDialogVisible"
            v-if="editDialogVisible && !quanshanFlag"
            @updateLkysfwkspzmx="(item) => query(item)"
          />
          <div v-show="boxvisible">
            <t-dialog
              theme="warning"
              style="display: block; border-radius: 10px"
              :width="400"
              header="警示"
              body="请确认是否删除所选明细"
              :onConfirm="confirmDelRow"
              :onClose="closeBox"
            >
              <!-- class="confirmDialogCss"
          <div style="text-align: center">
            <div><img src="../../../../assets/error.png" alt="" /></div>
            <div class="tsnr">请确认是否删除所选明细</div>
            <div class="tsnrf">
              <div>xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</div>
            </div>
          </div> -->
            </t-dialog>
          </div>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="发票明细" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            hover
            :data="tableData2"
            :columns="tableColumns"
            height="100%"
            lazyLoad
            :pagination="pagination2"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 1)"
            :foot-data="fpFootData"
          >
            <template #xh="{ rowIndex }">{{
              (pagination2.current - 1) * pagination2.pageSize + rowIndex + 1
            }}</template>
            <template #fplxDm="{ row }">
              {{
                (fplxList.find((d) => d.value === row.fplxDm) && fplxList.find((d) => d.value === row.fplxDm).label) ||
                ''
              }}
            </template>
            <template #hxytDm="{ row }">
              {{
                (hxytList.find((d) => d.value === row.hxytDm) && hxytList.find((d) => d.value === row.hxytDm).label) ||
                ''
              }}
            </template>
            <template #bdkyyDm="{ row }"
              >{{
                (bdkyyList.find((d) => d.value === row.bdkyyDm) &&
                  bdkyyList.find((d) => d.value === row.bdkyyDm).label) ||
                ''
              }}
            </template>
            <template #je="{ row }">
              <span>{{ numberToPrice(row.je) }}</span>
            </template>
            <template #se="{ row }">
              <span>{{ numberToPrice(row.se) }}</span>
            </template>
            <template #jshj="{ row }">
              <span>{{ numberToPrice(row.jshj) }}</span>
            </template>
            <template #dkje="{ row }">
              <span>{{ numberToPrice(row.dkje) }}</span>
            </template>
            <template #dkse="{ row }">
              <span>{{ numberToPrice(row.dkse) }}</span>
            </template>
            <template #cezsbz="{ row }">
              {{ { Y: '是', N: '否' }[row.cezsbz] || '' }}
            </template>
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import {
  lkysfwkspzQuery,
  lkysfwkspzDelete,
  lkysfwfpmxQuery,
  lkysfwkspzQueryHj,
  lkysfwfpmxQueryHj,
} from '@/pages/index/api/tzzx/zzstz/lkysfwkspzmx.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, ChartIcon, DeleteIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberUtils.js';
import { computeSszq, getSbztBySsq } from '@/pages/index/views/util/tzzxTools.js';
import { querySearchConfig1, querySearchConfig2, dataColumns1, dataColumns2 } from './config.js';
import EditDialog from './components/edit-dialog.vue';
import EditDialogQS from './components/edit-dialog-quanshan.vue';
import { fplxList, bdkyyList, hxytList } from '../../config';

export default {
  components: {
    QsbButton,
    ExportButton,
    ExtractDataButton,
    SearchControlPanel,
    EditDialog,
    EditDialogQS,
    AddIcon,
    DeleteIcon,
    ChartIcon,
    RollbackIcon,
  },
  data() {
    this.fplxList = fplxList;
    this.hxytList = hxytList;
    this.bdkyyList = bdkyyList;
    return {
      userInfo: {},
      isProduct: this.$store.state.isProduct.envValue,
      editDialogVisible: false,
      boxvisible: false,
      querySearchConfig1,
      querySearchConfig2,
      dataColumns1,
      dataColumns2,
      tableLoading: false,
      fromName: false,
      tabValue: '0',
      formData1: {},
      formData2: {},
      checkBox: [],
      selectedRowKeys: [],
      slList: [],
      lrzxList: [],
      kmdmList: [],
      tableData1: [],
      tableData2: [],
      delformData: [],
      pzFootData: [],
      fpFootData: [],
      pagination1: { current: 1, pageSize: 10, total: 0 },
      pagination2: { current: 1, pageSize: 10, total: 0 },
      loadedTabs: { 0: false, 1: false }, // 新增tab加载状态
      columnsLoading: false, // 列配置加载状态
      remoteColumns: [], // 动态列配置
      allowEdit: false, // 编辑权限状态
      editCheckLoading: false, // 检查状态加载中
    };
  },
  created() {
    this.querySearchConfig1[0].value = computeSszq();
    this.querySearchConfig2[0].value = computeSszq();
    this.formData1.sszq = computeSszq();
    this.formData2.sszq = computeSszq();
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.querySearchConfig2[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
        this.formData1.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
        this.formData2.sszq = dayjs(this.$route.query.skssqq).format('YYYYMM');
      }
    }
    this.formData2.kprqq = dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.formData2.kprqz = dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    this.querySearchConfig2[1].selectList = this.fplxList.filter((i) => i.value === '51' || i.value === '61');
    this.querySearchConfig2[5].selectList = this.hxytList;
  },
  mounted() {
    this.fetchTableColumns(); // 初始化表头配置
    this.checkAllowEdit();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData1.sszq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return this.tabValue * 1 ? 'lkysfwksfpmx' : 'lkysfwkspzMxz';
    },
    exportFileName() {
      return this.tabValue * 1 ? '旅客运输扣税发票明细账' : '旅客运输扣税凭证明细账';
    },
    tzQueryParams() {
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      if (this.tabValue === '1') {
        djParam.pageNum = this.pagination2.current;
        djParam.pageSize = this.pagination2.pageSize;
      }
      if (this.tabValue === '0') {
        djParam.pageNum = this.pagination1.current;
        djParam.pageSize = this.pagination1.pageSize;
      }
      return this.tabValue * 1
        ? {
            sszq: this.formData2.sszq.substring(0, 4) + this.formData2.sszq.substring(5, 7),
            ...djParam,
          }
        : {
            sszq: this.formData1.sszq.substring(0, 4) + this.formData1.sszq.substring(5, 7),
            ...djParam,
          };
    },
    exportButtonFlag() {
      return this.tabValue * 1 ? !this.tableData2.length : !this.tableData1.length;
    },

    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    tableColumns() {
      if (!this.remoteColumns.length) {
        // 兼容初始化状态
        return this.tabValue === '0' ? this.dataColumnsPzmx : this.dataColumnsFpmx;
      }
      return this.remoteColumns;
    },
    dataColumnsPzmx() {
      let columns = this.dataColumns1;

      // 泉膳企业修改分配列显示
      if (this.quanshanFlag) {
        columns = columns.map((col) => {
          if (col.colKey === 'kjfp') {
            return { ...col, title: '文本' };
          }
          return col;
        });
      }

      return this.shanxiyidongFlag ? columns.filter((d) => d.colKey !== 'sl1') : columns;
    },
    dataColumnsFpmx() {
      return this.dataColumns2;
    },
  },
  // 添加tab切换监听
  watch: {
    tabValue(newVal) {
      if (!this.loadedTabs[newVal]) {
        this.query({ initQuery: true });
        this.loadedTabs[newVal] = true;
      }
      // 切换tab时重新获取表头配置
      this.fetchTableColumns();
      this.checkAllowEdit();
    },
    formData1: {
      deep: true,
      handler() {
        if (this.tabValue === '0') this.checkAllowEdit();
      },
    },
    formData2: {
      deep: true,
      handler() {
        if (this.tabValue === '1') this.checkAllowEdit();
      },
    },
    '$store.state.sbzt': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
    '$store.state.zzstz.userInfo': {
      deep: true,
      handler() {
        this.checkAllowEdit();
      },
    },
  },
  methods: {
    async checkAllowEdit() {
      if (this.editCheckLoading) return;
      this.editCheckLoading = true;

      const { sbgzEnterFlag, sbgzSsyfInfo } = this.$store.state.sbzt;
      const isTab1 = this.tabValue === '0';
      const formData = isTab1 ? this.formData1 : this.formData2;

      // 添加表单数据有效性检查
      if (!formData || !formData.sszq) {
        this.allowEdit = false;
        this.editCheckLoading = false;
        return;
      }

      try {
        if (sbgzEnterFlag) {
          // 申报更正进入的逻辑
          this.allowEdit = dayjs(formData.sszq).format('YYYY-MM') === sbgzSsyfInfo.ssyfq;
        } else {
          // 非申报更正进入的逻辑
          const isDeclared = await getSbztBySsq(formData.sszq);
          this.allowEdit = !isDeclared;
        }
      } catch (error) {
        console.error('编辑权限检查失败', error);
        this.allowEdit = false;
      } finally {
        this.editCheckLoading = false;
      }
    },
    // 动态配置表头信息
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultColumns = this.tabValue === '0' ? this.dataColumnsPzmx : this.dataColumnsFpmx;

        // 处理列配置
        this.remoteColumns = defaultColumns
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);

            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.title) {
              return null;
            }

            // 如果配置项存在，则更新列标题
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列

        console.log('表头配置更新成功', this.remoteColumns);
      } catch (e) {
        console.error('获取表头配置失败', e);
        // 失败时回退到默认配置
        this.remoteColumns = this.tabValue === '0' ? this.dataColumnsPzmx : this.dataColumnsFpmx;
      } finally {
        this.columnsLoading = false;
      }
    },
    initTabValue() {
      this.tabValue = '0';
      console.log('initTabValue', this.tabValue);
    },
    goBack() {
      this.$emit('openPage', { type: this.fromName, notQuery: true });
    },
    closeBox() {
      this.boxvisible = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
        sszq: this.formData1.sszq.substring(0, 4) + this.formData1.sszq.substring(5, 7),
        ly: '1',
      };
      let selectedRowData = row;
      if (pageType === 0 && this.selectedRowKeys.length > 0) {
        console.log('selectedRowKeys', this.selectedRowKeys);
        const temp = JSON.stringify(this.tableData1.find((t) => t.uuid === this.selectedRowKeys[0]));
        selectedRowData = JSON.parse(temp);
        this.$delete(selectedRowData, 'uuid');
      }
      console.log('selectedRowData', selectedRowData);
      const rowdata = JSON.stringify(selectedRowData);
      this.editDialogVisible = { row: JSON.parse(rowdata), oldkjpzbh: row.kjpzbh, otherObj, pageType };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData1.findIndex((i) => i.uuid === item.uuid);
        this.tableData1.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async query(pm = { flag: false, p: false, initQuery: false }) {
      console.log('this.$store.state.zzstz.userInfo', this.$store.state.zzstz.userInfo);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) {
        console.log('默认第一页', flag);
        this.tabValue * 1 ? (this.pagination2.current = 1) : (this.pagination1.current = 1);
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: 0,
        pageSize: 0,
      };
      if (this.tabValue === '1') {
        params.pageNum = this.pagination2.current;
        params.pageSize = this.pagination2.pageSize;
      }
      if (this.tabValue === '0') {
        params.pageNum = this.pagination1.current;
        params.pageSize = this.pagination1.pageSize;
      }
      if (p) {
        // 优先使用传入的tabValue，否则默认'0'
        this.tabValue = p.tabValue || '0';
        params = { ...p, ...params };
        if (p.sszq) {
          p.sszq = String(p.sszq.substring(0, 4).concat('-', p.sszq.substring(4, 6)));
        }
        if (this.tabValue === '0') {
          this.$refs.queryControl1.setParams(p);
        } else {
          this.$refs.queryControl2.setParams(p);
        }
      } else if (this.tabValue === '0') {
        params = { sszq: this.formData1.sszq.substring(0, 4) + this.formData1.sszq.substring(5, 7), ...params };
      } else if (this.tabValue === '1') {
        params = {
          ...this.formData2,
          sszq: this.formData2.sszq.substring(0, 4) + this.formData2.sszq.substring(5, 7),
          ...params,
        };
      }
      // 增加缓存判断逻辑
      if (this.loadedTabs[this.tabValue] && !pm.initQuery && !pm.flag) {
        this.tableLoading = false;
        return;
      }
      try {
        // 拆分两个tab的独立查询逻辑
        if (this.tabValue === '0') {
          const { data } = await lkysfwkspzQuery(params);
          this.tableData1 = data.records || [];
          console.log('this.tableData1', this.tableData1);
          this.tableData1.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
          this.pagination1.total = data.pageTotal;
          this.loadedTabs['0'] = true;

          if (this.pagination1.total > 0) {
            const { data } = await lkysfwkspzQueryHj(params);
            this.pzFootData =
              [
                {
                  kdje: numberToPrice(data?.kdje),
                  kdse: numberToPrice(data?.kdse),
                },
              ] || [];
          } else {
            this.pzFootData = [];
          }
        } else if (this.tabValue === '1') {
          const { data } = await lkysfwfpmxQuery(params);
          this.tableData2 = data.records || [];
          console.log('this.tableData2', this.tableData2);
          this.tableData2.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
          this.pagination2.total = data.pageTotal;
          this.loadedTabs['1'] = true;

          if (this.pagination2.total > 0) {
            const { data } = await lkysfwfpmxQueryHj(params);
            this.fpFootData =
              [
                {
                  je: numberToPrice(data?.je),
                  se: numberToPrice(data?.se),
                  jshj: numberToPrice(data?.jshj),
                  dkje: numberToPrice(data?.dkje),
                  dkse: numberToPrice(data?.dkse),
                },
              ] || [];
          } else {
            this.fpFootData = [];
          }
        }
      } catch (e) {
        this.tableData1 = [];
        this.tableData2 = [];
        console.errror(e);
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params) {
      try {
        const { msg } = await lkysfwkspzDelete(params);
        console.log('lkysfwkspzDelete-msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.errror(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    check() {
      this.$router.push('/lsgl');
    },
    pageChange({ current, pageSize }, type) {
      // [this.pagination1, this.pagination2][type].current =
      //   pageSize !== [this.pagination1, this.pagination2][type].pageSize ? 1 : current;
      // [this.pagination1, this.pagination2][type].pageSize = pageSize;
      // this.query();
      // 获取对应的分页对象
      const pagination = type === 0 ? this.pagination1 : this.pagination2;

      // 更新当前页码和每页条数
      const isPageSizeChanged = pageSize !== pagination.pageSize;
      pagination.current = isPageSizeChanged ? 1 : current;
      pagination.pageSize = pageSize;

      // 重置对应tab的加载状态
      const tabKey = type === 0 ? '0' : '1';
      this.loadedTabs[tabKey] = false;

      // 触发查询
      this.query({ initQuery: true });
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  // padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.filter-btns {
  float: right;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
