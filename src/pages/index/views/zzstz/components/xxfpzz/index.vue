<!--
 * @Descripttion: 台账-增值税一般纳税人销项发票总账
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        class="znsbHeadqueryDiv"
        ref="queryControl"
        :config="querySearchConfig"
        :formRules="querySearchConfigRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
      />
      <div class="queryBtns">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-button
            variant="outline"
            theme="primary"
            @click="ckdgpdf()"
            v-if="this.$store.state.isProduct.envValue && !this.xgmnsrFlag"
            ><ChartIcon slot="icon" />查看底稿</t-button
          >
          <t-dropdown
            :options="[
              { content: '生成计提表', value: 1, onClick: () => scjtb() },
              { content: '提交审核', value: 2, onClick: () => tjsh() },
              { content: '查看计提凭证', value: 3, onClick: () => ckjtpz() },
            ]"
            trigger="click"
            v-if="this.$store.state.isProduct.envValue && !this.xgmnsrFlag"
          >
            <t-button variant="outline" theme="primary">
              <ChevronRightCircleIcon slot="icon" /><span>计提</span>
            </t-button>
          </t-dropdown>
          <ExportButton
            :pagingFlag="false"
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <BatchExportButton
            v-if="quanshanFlag"
            :pagingFlag="false"
            :tzlx="tzlx"
            :fileName="exportFileName"
            :tzQueryParams="tzQueryParams"
            :exportButtonFlag="exportButtonFlag"
          />
          <QsbButton />
          <!-- 添加小规模纳税人提示文字 -->
          <span v-if="xgmnsrFlag" class="xgmnsr-tip"> 【按1%税率展示】 </span>
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="myTable"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="tableColumns"
          :height="dynamicHeight"
          :loading="tableLoading"
          :foot-data="footData"
          lazyLoad
          :rowClassName="rowClassName"
          @row-click="(e) => (activeRule = e)"
        >
          <!-- :horizontalScrollAffixedBottom="true" -->
          <!-- tableLayout="auto" -->
          <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
          <template v-for="column in ['qtfpje', 'qtfpse', 'zyfpse']" #[column]="{ row, col }">
            <div :key="column" :slot="column">
              <span
                v-if="!(['a12', 'a345', 'a12-3', 'a345-3'].includes(row.zsxmDm) || !row[col.colKey])"
                class="specText"
                @click="openPage({ ...row, colKey: col.colKey })"
                >{{ numberToPrice(row[col.colKey]) }}</span
              >
              <span v-else>{{ numberToPrice(row[col.colKey]) }}</span>
            </div>
          </template>
          <template #jsfsDm="{ row }">
            {{ zsfsList.find((t) => t.code === row.jsfsDm) && zsfsList.find((t) => t.code === row.jsfsDm).caption }}
          </template>
          <template #zsxmDm="{ row }">
            {{ zsxmDmC(row) }}
          </template>
          <template #sl="{ row }">
            {{ row.sl === null ? '-' : row.sl * 100 + '%' }}
          </template>
          <!-- 2024.12.27 小规模台账时添加开具增值税专用发票疑点。 -->
          <template #zyfpje="{ row }">
            <div v-if="xgmnsrFlag && row.cylxbz">
              <div style="display: flex; justify-content: space-between; align-items: center; float: right">
                <t-tooltip :showArrow="false" :destroyOnClose="false">
                  <span
                    v-if="!(['a12', 'a345', 'a12-3', 'a345-3'].includes(row.zsxmDm) || !row.zyfpje)"
                    class="specText"
                    @click="openPage({ ...row, colKey: 'zyfpje' })"
                  >
                    {{ numberToPrice(row.zyfpje) }}
                  </span>
                  <span v-else>{{ numberToPrice(row.zyfpje) }}</span>
                  <template #content>
                    <span>{{ row.cysm }}</span>
                    <span style="margin-left: 10px; color: lightblue; cursor: pointer" @click="openPage({ ...row })"
                      >请点此查看销项税疑点差异明细</span
                    >
                    <span>。</span>
                    <span>如果该疑点可忽略，请点击此</span>
                    <span
                      style="margin-left: 10px; color: lightblue; cursor: pointer"
                      @click="(item) => (hlVisible = item)"
                      >“忽略”</span
                    >
                  </template>
                  <error-circle-filled-icon
                    :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }"
                  />
                </t-tooltip>
              </div>
            </div>
            <div v-else>
              <span
                v-if="!(['a12', 'a345', 'a12-3', 'a345-3'].includes(row.zsxmDm) || !row.zyfpje)"
                class="specText"
                @click="openPage({ ...row, colKey: 'zyfpje' })"
              >
                {{ numberToPrice(row.zyfpje) }}
              </span>
              <span v-else>{{ numberToPrice(row.zyfpje) }}</span>
            </div>
          </template>
          <!-- 2024.10.18 需求变更 当未开票收入为负数时，增加一个可忽略疑点，不阻断业务。 -->
          <template #wkpje="{ row }">
            <div
              v-if="row.wkpje < 0 && row.uuid"
              style="display: flex; justify-content: space-between; align-items: center; float: right"
            >
              <t-tooltip :showArrow="false" :destroyOnClose="false">
                <span>{{ numberToPrice(row.wkpje) }}</span>
                <template #content>
                  <span>{{ row.ydmsg }}</span>
                </template>
                <error-circle-filled-icon :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }" />
              </t-tooltip>
            </div>
            <span v-else>{{ numberToPrice(row.wkpje) }}</span>
          </template>
        </t-table>
      </div>
      <ValidateDialog
        :validate-rules="validateRules"
        :handleMsg="true"
        :extraHandleMsg="true"
        @ruleClick="ruleClick"
        @toggle="toggle"
        @handleMsg="(item) => openPage(item)"
        @extraHandleMsg="
          (item) => {
            this.hlVisible = {
              ...item,
              showHlxmFlag: true, // 强制显示忽略项目
            };

            // 使用深拷贝确保响应式更新
            this.hlformData = item.hlztvo
              ? JSON.parse(
                  JSON.stringify({
                    ...item.hlztvo,
                    hlDm: item.hlztvo.hldm || '00', // 设置默认值
                  }),
                )
              : {
                  hlDm: '00',
                  hlyy: '',
                  hlsy: '',
                };
          }
        "
      />
      <!-- <div v-show="hlVisible">
        <t-dialog
          theme="warning"
          style="display: block; border-radius: 10px"
          :width="400"
          header="警示"
          body="是否确定忽略该疑点差异信息"
          confirmBtn="是"
          cancelBtn="否"
          :onConfirm="qrhlcyxx"
          :onClose="closeBox"
        >
        </t-dialog>
      </div> -->
      <IgnoreDialog :visible="hlVisible" :formData="hlformData" @confirm="qrhlcyxx($event)" @close="closeBox" />
      <Modal :visible="isModalVisible1" @close="isModalVisible1 = false">
        <PdfViewer :pdfUrl="pdfUrl1" />
      </Modal>
      <Modal :visible="isModalVisible2" @close="isModalVisible2 = false">
        <PdfViewer :pdfUrl="pdfUrl2" />
      </Modal>
      <Modal :visible="isModalVisible3" @close="isModalVisible3 = false">
        <PdfViewer :pdfUrl="pdfUrl3" />
      </Modal>
    </div>
  </div>
</template>
<script>
import { ChartIcon, ChevronRightCircleIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue';
import { initXxfpZzQuery, xxfpUpdateHlbz, initXxfpZzQueryInXgmnsr } from '@/pages/index/api/tzzx/zzstz/xxfp.js';
import dayjs from 'dayjs';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton/index.vue';
import BatchExportButton from '@/pages/index/components/ExportButton/index-quanshan.vue';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import IgnoreDialog from '@/pages/index/components/IgnoreDialog/index.vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import {
  multiSelectHandle,
  computeSszq,
  jyssReadyStatusFetch,
  getSbztBySsq,
  getSbztBySsqqz,
} from '@/pages/index/views/util/tzzxTools.js';
import { querySearchConfigOneRules, querySearchConfigYbnsr, mainColumnsYbnsr } from './config.js';
import { slList, zsfsList, zsxmList } from '../../config';

export default {
  components: {
    IgnoreDialog,
    SkeletonFrame,
    QsbButton,
    ExtractDataButton,
    ExportButton,
    BatchExportButton,
    SearchControlPanel,
    ChartIcon,
    ChevronRightCircleIcon,
    ErrorCircleFilledIcon,
    PdfViewer,
    Modal,
    ValidateDialog,
  },
  data() {
    this.slList = slList;
    this.zsfsList = zsfsList;
    return {
      loading: true,
      isProduct: this.$store.state.isProduct.envValue,
      hlVisible: false,
      validateDialogToggle: true,
      pdfUrl1: `${document.location.origin}/znsb/view/tzzx/ckdg1.pdf`, // pdf文件路径
      pdfUrl2: `${document.location.origin}/znsb/view/tzzx/jtb.pdf`, // pdf文件路径
      pdfUrl3: `${document.location.origin}/znsb/view/tzzx/jtpz.pdf`, // pdf文件路径
      isModalVisible1: false,
      isModalVisible2: false,
      isModalVisible3: false,
      visible: false,
      dialogTitle: '',
      formData: {},
      hlformData: {
        hlDm: '',
        hlyy: '',
        hlsy: '',
      },
      userInfo: {},
      querySearchConfigYbnsr,
      querySearchConfigOneRules,
      // querySearchConfig: {},
      mainColumns: {},
      tableLoading: false,
      delformData: [],
      selectedRowKeys: [],
      validateRules: [],
      activeRule: '',
      tableData: [],
      footData: [],
      remoteColumns: [], // 新增接收后端列配置
      columnsLoading: false, // 新增列配置加载状态
    };
  },
  created() {},
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
    '$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig': {
      handler(newVal, oldVal) {
        console.log('companyDifferentiationConfig变化', newVal, oldVal);
        this.fetchTableColumns(); // 新增
      },
      immediate: true,
    },
  },
  computed: {
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    zszfjgFlag() {
      return (
        this.$store.state.zdmczh.companyDifferentiationConfig.jtbm === '000003' &&
        this.$store.state.zdmczh.companyDifferentiationConfig.fzjgbz === 'zjg'
      );
    },
    querySearchConfig() {
      let querySearchConfigTemp = this.querySearchConfigYbnsr;
      if (!this.zszfjgFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'hzfzjg');
      }
      if (this.xgmnsrFlag) {
        querySearchConfigTemp = querySearchConfigTemp.filter((item) => item.key !== 'jsfsDm');
      }
      return querySearchConfigTemp;
    },
    querySearchConfigRules() {
      const querySearchConfigOneRulesTemp = JSON.parse(JSON.stringify(this.querySearchConfigOneRules));
      if (!this.zszfjgFlag) {
        delete querySearchConfigOneRulesTemp.hzfzjg;
        delete this.formData.hzfzjg;
      }
      return querySearchConfigOneRulesTemp;
    },
    tableColumns() {
      if (!this.remoteColumns.length) return mainColumnsYbnsr; // 兼容初始化状态

      const columns = [...this.remoteColumns];

      if (this.xgmnsrFlag) {
        columns[1].width = 160;
        columns[3].width = 200;
        return columns.filter((item) => item.colKey !== 'jsfsDm' && item.colKey !== 'nsjctz');
      }

      columns[1].width = 80;
      columns[3].width = 170;
      return columns;
    },
    ssyfqC() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    ssyfzC() {
      return dayjs(this.formData.ssyfz).format('YYYYMM');
    },
    dynamicHeight() {
      console.log('计算高度属性是否生效');
      let height = '100%';
      if (!this.validateDialogToggle) {
        console.log('this.validateDialogToggle', this.validateDialogToggle);
        return height;
      }
      switch (this.validateRules.length) {
        case 0:
          height = '100%';
          break;
        case 1:
          height = '85%';
          break;
        case 2:
          height = '80%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '70%';
          break;
      }
      console.log('计算高度属性已生效，返回高度为', height);
      return height;
    },
    zsxmList() {
      return this.xgmnsrFlag
        ? [
            ...zsxmList,
            { value: 'a12-3', label: '货物及劳务（3%征税率）' },
            { value: 'a345-3', label: '服务、不动产和无形资产（3%征税率）' },
          ]
        : [
            ...zsxmList,
            { value: 'a12', label: '货物及加工修理修配劳务' },
            { value: 'a345', label: '服务、不动产和无形资产' },
          ];
    },
    zsxmDmC() {
      return (row) => {
        if (['a12', 'a345', 'a12-3', 'a345-3'].includes(row.zsxmDm)) {
          return this.zsxmList.find((t) => t.value === row.zsxmDm)?.label;
        }
        if (row.zsxmDm) {
          return `其中：${this.zsxmList.find((t) => t.value === row.zsxmDm)?.label}`;
        }
        return '-';
      };
    },
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyStatus', this.$store.state.jyss.readyStatus);
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      return this.xgmnsrFlag ? 'xgmnsrXxfpZzTz' : 'xxfpZzTz';
    },
    exportFileName() {
      return '销项发票总账';
    },
    tzQueryParams() {
      return this.xgmnsrFlag
        ? {
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
            ...this.formData,
            ssyfq: this.ssyfqC,
            ssyfz: this.ssyfzC,
            zsxmDm: multiSelectHandle(this.formData.zsxmDm),
            pageNum: 1,
            pageSize: 1000000,
          }
        : {
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
            ...this.formData,
            ssyfq: this.ssyfqC,
            ssyfz: this.ssyfzC,
            jsfsDm: multiSelectHandle(this.formData.jsfsDm),
            zsxmDm: multiSelectHandle(this.formData.zsxmDm),
            pageNum: 1,
            pageSize: 1000000,
          };
    },
    exportButtonFlag() {
      return !this.tableData.length;
    },
  },
  mounted() {
    this.initQueryConditions();
    this.getQueryParamsList();
    this.fetchTableColumns(); // 新增调用
    this.query();
  },
  methods: {
    // 动态配置表头信息
    // 目前为替换形式：在以明确字段基础上替换字段展示名称，若后续有字段变更需求则可改成从后台读取配置
    fetchTableColumns() {
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;
        console.log('companyDifferentiationConfig', companyDifferentiationConfig);
        this.remoteColumns = mainColumnsYbnsr
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);
            // 如果找到匹配项且displayFlag为false，则返回null过滤掉该列
            if (matchedItem && matchedItem.displayName === column.title && matchedItem.displayFlag === false) {
              return null;
            }
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('获取表头配置失败', e);
        this.remoteColumns = mainColumnsYbnsr; // 失败时使用本地配置
      } finally {
        this.columnsLoading = false;
      }
    },
    getQueryParamsList() {
      const zsfsIndex = this.querySearchConfigYbnsr.findIndex((item) => item.key === 'jsfsDm');
      if (zsfsIndex !== -1) {
        this.querySearchConfigYbnsr[zsfsIndex].selectList = this.zsfsList.map((d) => ({
          value: d.code,
          label: d.caption,
        }));
      }
      const zsxmIndex = this.querySearchConfigYbnsr.findIndex((item) => item.key === 'zsxmDm');
      if (zsxmIndex !== -1) {
        this.querySearchConfigYbnsr[zsxmIndex].selectList = this.xgmnsrFlag
          ? this.zsxmList.slice(0, 3)
          : this.zsxmList.slice(0, 5);
      }
    },
    initQueryConditions() {
      if (window.location.search) {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);
        console.log('url-params', params);
        if (params.get('skssqq') && params.get('skssqz')) {
          this.querySearchConfigYbnsr[0].value = dayjs(params.get('skssqq')).format('YYYY-MM');
          this.querySearchConfigYbnsr[1].value = dayjs(params.get('skssqz')).format('YYYY-MM');
          this.formData.ssyfq = dayjs(params.get('skssqq')).format('YYYYMM');
          this.formData.ssyfz = dayjs(params.get('skssqz')).format('YYYYMM');
          return;
        }
      }
      this.querySearchConfigYbnsr[0].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfigYbnsr[1].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData.ssyfq = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData.ssyfz = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      // 确保不重置hzfzjg参数
      if (this.zszfjgFlag) {
        // 保留原有hzfzjg值
        const hzfzjgIndex = this.querySearchConfigYbnsr.findIndex((item) => item.key === 'hzfzjg');
        if (hzfzjgIndex !== -1) {
          // 用set赋值时可以让querySearchConfigYbnsr中的默认值成功带出，querySearchConfigYbnsr[hzfzjgIndex].value='Y'则不行。
          this.$set(this.querySearchConfigYbnsr[hzfzjgIndex], 'value', 'Y');
          this.$set(this.formData, 'hzfzjg', 'Y');
        }
      } else {
        delete this.formData.hzfzjg;
      }
    },
    qsb() {
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/nssb/sbrwmx`;
        window.parent.goSelfChange(menuParams);
      }
    },
    async qrhlcyxx(submitData) {
      console.log('忽略成功');
      try {
        const params = {
          hlsy: submitData.hlsy, // 从参数获取处理后的数据
          hlDm: submitData.hlDm || '', // 新增 hlDm 字段
          hlyy: submitData.hlyy || '', // 新增 hlyy 字段
          uuid: this.tableData.find((t) => t.index === this.hlVisible.index)?.uuid,
          hlbz: 'Y',
        };
        const { msg } = await xxfpUpdateHlbz(params);
        console.log('cybdhl-msg', msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query({ flag: true });
        this.hlVisible = false;
      }
    },
    closeBox() {
      this.hlVisible = false;
    },
    toggle(val) {
      console.log('打开校验弹窗', val);
      this.validateDialogToggle = val;
    },
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.myTable.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    rowClassName({ rowIndex }) {
      if (rowIndex === this.activeRule.index) return 'active-row';
      return '';
    },
    async query(pm = { flag: false, p: false }) {
      const { p } = pm;
      if (dayjs(this.formData.ssyfz).diff(dayjs(this.formData.ssyfq), 'month') > 2) {
        this.$message.warning('时间范围不能超过三个月');
        return;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
      };
      if (p) {
        params = { ...p, ...params };
        // 确保p参数中的hzfzjg也被正确处理
        if (!this.zszfjgFlag && params.hzfzjg) {
          delete params.hzfzjg;
        }
        if (params.ssyfq) {
          params.ssyfq = dayjs(params.ssyfq).format('YYYYMM');
        }
        if (params.ssyfz) {
          params.ssyfz = dayjs(params.ssyfz).format('YYYYMM');
        }
        this.$refs.queryControl.setParams(p);
      } else {
        const commonParams = {
          ssyfq: this.ssyfqC,
          ssyfz: this.ssyfzC,
          jsfsDm: multiSelectHandle(this.formData.jsfsDm),
          zsxmDm: multiSelectHandle(this.formData.zsxmDm),
          ...params,
        };

        params =
          this.zszfjgFlag && this.formData.hzfzjg ? { hzfzjg: this.formData.hzfzjg, ...commonParams } : commonParams;
      }
      try {
        let res = {};
        if (this.xgmnsrFlag) {
          console.log('小规模销项发票总账初始化');
          res = await initXxfpZzQueryInXgmnsr(params);
        } else {
          console.log('一般人销项发票总账初始化');
          res = await initXxfpZzQuery(params);
        }
        const { data } = res;
        this.tableData = data.records?.dataList || [];
        // 在查询方法中清空缓存（根据业务需求）
        this.validateRules = [];
        // 由于申报状态存在变更，所以每次查询都要重新获取
        this.$store.commit('sbzt/setSbztCacheInfo', {});
        this.tableData.forEach(async (item, index) => {
          this.$set(item, 'index', index);
          if (item.cylxbz) {
            let l = {
              content: item.cysm,
              handleMsg: '请点此查看销项税疑点明细',
              index,
              type: item.cylxbz,
              djxh: item.djxh,
              sszq: item.sszq,
              sl: item.sl,
              nsrsbh: item.nsrsbh,
              nsrmc: item.nsrmc,
            };
            if (item.hlztvo) {
              if (await this.getSbztBySsqXxfpzz(String(item.sszq))) {
                l = {
                  ...l,
                  finalContent: `操作人员${item.hlztvo.hlczry}于${dayjs(item.hlztvo.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${item.hlztvo.hlsy}`,
                };
              } else {
                l = {
                  ...l,
                  hlztvo: item.hlztvo,
                  extraContent: `操作人员${item.hlztvo.hlczry}于${dayjs(item.hlztvo.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${item.hlztvo.hlsy}。若有修改，请点击此“`,
                  extraHandleMsg: '编辑',
                  finalContent: '”。',
                };
              }
            } else {
              l = {
                ...l,
                extraContent: '若该差异可忽略，请点击此“',
                extraHandleMsg: '忽略',
                finalContent: '”。',
              };
            }
            this.validateRules.push(l);
          }
          if (item.wkpje < 0 && item.uuid) {
            this.$set(
              item,
              'ydmsg',
              `所属月份：${item.sszq}，税率：${item.sl * 100}%， 征税项目：${
                this.zsxmList.find((t) => t.value === item.zsxmDm)?.label
              }，未开具发票的销售额小于0。`,
            );
            let l = {
              content: item.ydmsg,
              index,
              type: 'error',
              djxh: item.djxh,
              nsrsbh: item.nsrsbh,
              nsrmc: item.nsrmc,
              sszq: item.sszq,
              showHlxmFlag: true,
            };
            if (item.hlztvo) {
              if (await this.getSbztBySsqXxfpzz(String(item.sszq))) {
                l = {
                  ...l,
                  finalContent: `操作人员${item.hlztvo.hlczry}于${dayjs(item.hlztvo.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${item.hlztvo.hlsy}`,
                };
              } else {
                l = {
                  ...l,
                  hlztvo: item.hlztvo,
                  extraContent: `操作人员${item.hlztvo.hlczry}于${dayjs(item.hlztvo.hlczsj).format(
                    'YYYY年MM月DD日',
                  )}已忽略该差异。忽略原因：${item.hlztvo.hlsy}。若有修改，请点击此“`,
                  extraHandleMsg: '编辑',
                  finalContent: '”。',
                };
              }
            } else {
              l = {
                ...l,
                extraContent: '若该差异可忽略，请点击此“',
                extraHandleMsg: '忽略',
                finalContent: '”。',
              };
            }
            this.validateRules.push(l);
          }
          // 由前端计算合计列，添加分支避免当企业资质为小规模纳税人时页面合计值受后端额外返回结果影响。
          const xsehj = this.xgmnsrFlag
            ? Number(item?.zyfpje || 0) + Number(item?.qtfpje || 0) + Number(item?.wkpje || 0)
            : Number(item?.zyfpje || 0) +
              Number(item?.qtfpje || 0) +
              Number(item?.wkpje || 0) +
              Number(item?.nsjctzje || 0);
          const xxsehj = this.xgmnsrFlag
            ? Number(item?.zyfpse || 0) + Number(item?.qtfpse || 0) + Number(item?.wkpse || 0)
            : Number(item?.zyfpse || 0) +
              Number(item?.qtfpse || 0) +
              Number(item?.wkpse || 0) +
              Number(item?.nsjctzse || 0);
          this.$set(item, 'xsehj', xsehj);
          this.$set(item, 'xxsehj', xxsehj);
        });
        let hj = this.tableData.length > 0 ? data.records?.hj || {} : {};
        console.log('xxfphj', hj);
        if (Object.keys(hj).length !== 0) {
          console.log('追加合计处理');
          hj = {
            ...hj,
            xsehj: this.xgmnsrFlag
              ? Number(hj?.zyfpje || 0) + Number(hj?.qtfpje || 0) + Number(hj?.wkpje || 0)
              : Number(hj?.zyfpje || 0) + Number(hj?.qtfpje || 0) + Number(hj?.wkpje || 0) + Number(hj?.nsjctzje || 0),
            xxsehj: this.xgmnsrFlag
              ? Number(hj?.zyfpse || 0) + Number(hj?.qtfpse || 0) + Number(hj?.wkpse || 0)
              : Number(hj?.zyfpse || 0) + Number(hj?.qtfpse || 0) + Number(hj?.wkpse || 0) + Number(hj?.nsjctzse || 0),
          };
        }
        // 数据格式转换
        Object.keys(hj).forEach((key) => {
          hj[key] = this.numberToPrice(hj[key]);
        });
        if (Object.keys(hj).length) {
          this.footData = [hj];
        } else {
          this.footData = [];
        }
        jyssReadyStatusFetch(
          this.$store.state.zzstz.userInfo?.djxh || '',
          this.$store.state.zzstz.userInfo?.nsrsbh || '',
          this.formData.ssyfq,
        );
      } catch (e) {
        this.tableData = [];
        this.footData = [];
        this.validateRules = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    async getSbztBySsqXxfpzz(param) {
      // 因为在小规模情况下 sszq为“2025-04至2025-06”这样的字段表达，导致使用原参数会导致后端报错，所以要强制转换数据格式
      if (this.xgmnsrFlag) {
        const sszqq = param.substring(0, 7);
        const sszqz = param.substring(8, 15);
        return getSbztBySsqqz(sszqq, sszqz, 'BDA0610611');
      }
      return getSbztBySsq(param);
    },
    openPage(row) {
      const data = this.xgmnsrFlag
        ? {
            sl: row.sl,
            sszqq: dayjs(row?.sszq.substring(0, 7)).format('YYYYMM'),
            sszqz: dayjs(row?.sszq.substring(8, 15)).format('YYYYMM'),
            zsxmDm: row.zsxmDm,
            tabValue: '0',
          }
        : { jsfsDm: row.jsfsDm, sl: row.sl, sszq: String(row.sszq), zsxmDm: row.zsxmDm };

      this.$emit('openPage', {
        from: 'xxfpzz',
        data,
        type: 'xxfpmx',
      });
    },
    close() {
      this.visible = false;
    },
    ckdg() {
      this.$router.push('/lsgl');
    },
    ckdgpdf() {
      // 打开本地pdf文件
      this.isModalVisible1 = true;
      this.pdfUrl1 = `${document.location.origin}/znsb/view/tzzx/ckdg1.pdf`;
      console.log(this.pdfUrl1, 'pdfUrl1');
    },
    scjtb() {
      // 打开生成计提表pdf文件
      this.isModalVisible2 = true;
      this.pdfUrl2 = `${document.location.origin}/znsb/view/tzzx/jtb.pdf`;
      console.log(this.pdfUrl2, 'pdfUrl2');
    },
    tjsh() {
      // 提交审核
      this.$message.success('提交审核成功');
    },
    ckjtpz() {
      // 打开查看计提凭证pdf文件
      this.isModalVisible3 = true;
      this.pdfUrl3 = `${document.location.origin}/znsb/view/tzzx/jtpz.pdf`;
      console.log(this.pdfUrl3, 'pdfUrl3');
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
// 添加提示文字样式
.xgmnsr-tip {
  margin-left: 10px;
  font-size: 14px;
  line-height: 32px; // 与按钮高度对齐
  color: #faad14; // 使用warning主题的橙黄色
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.t-table__body .active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/.filter-btns {
  float: right;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/ .t-table {
  // 表格高度修正
  height: calc(100% - 1px) !important;
}
</style>
