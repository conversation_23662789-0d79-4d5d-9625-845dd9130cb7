import { computeSszq } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberToCurrency';

export const querySearchConfigOneRules = {
  ssyfq: [{ required: true, message: '必填项', type: 'error' }],
  ssyfz: [{ required: true, message: '必填项', type: 'error' }],
  hzfzjg: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfigYbnsr = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: computeSszq(),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  // 2025.7.1 销项台账汇总分支机构查询条件默认为否并隐藏
  // {
  //   label: '汇总分支机构',
  //   key: 'hzfzjg',
  //   type: 'select',
  //   multiple: false,
  //   selectList: [
  //     { label: '是', value: 'Y' },
  //     { label: '否', value: 'N' },
  //   ],
  //   value: 'Y',
  //   clearable: false,
  // },
  {
    label: '计税方式',
    key: 'jsfsDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  // {
  //   key: 'hzfzjg',
  //   label: '汇总分支机构',
  //   type: 'radio',
  //   radioOptions: [
  //     { label: '是', value: 'Y' },
  //     { label: '否', value: 'N' },
  //   ],
  //   value: 'Y',
  //   // 可选参数
  //   placement: 'horizontal', // 布局方式 horizontal/vertical
  //   clearable: true,
  //   disabled: false, // 是否禁用
  // },
];
export const mainColumnsYbnsr = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
    foot: '合计',
  },
  {
    width: 80,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
  },
  {
    width: 130,
    colKey: 'jsfsDm',
    title: '计税方式',
    ellipsis: true,
  },
  {
    width: 170,
    colKey: 'zsxmDm',
    title: '征税项目',
    ellipsis: true,
  },
  {
    width: 50,
    align: 'right',
    colKey: 'sl',
    title: '税率',
    ellipsis: true,
  },
  {
    align: 'center',
    colKey: 'kjskzzszyfp',
    title: '开具增值税专用发票',
    children: [
      {
        width: 130,
        align: 'right',
        colKey: 'zyfpje',
        title: '销售额',
        ellipsis: true,
        // cell: (h, { row }) => <div>{numberToPrice(row.zyfpje)}</div>,
      },
      {
        width: 130,
        align: 'right',
        colKey: 'zyfpse',
        title: '销项(应纳)税额',
        ellipsis: true,
        // cell: (h, { row }) => <div>{numberToPrice(row.zyfpse)}</div>,
      },
    ],
  },
  {
    align: 'center',
    colKey: 'kjqtfp',
    title: '开具其他发票',
    children: [
      {
        width: 130,
        align: 'right',
        colKey: 'qtfpje',
        title: '销售额',
        ellipsis: true,
        // cell: (h, { row }) => <div>{numberToPrice(row.qtfpje)}</div>,
      },
      {
        width: 130,
        align: 'right',
        colKey: 'qtfpse',
        title: '销项(应纳)税额',
        ellipsis: true,
        // cell: (h, { row }) => <div>{numberToPrice(row.qtfpse)}</div>,
      },
    ],
  },
  {
    align: 'center',
    colKey: 'wkjfp',
    title: '未开具发票',
    children: [
      {
        width: 130,
        align: 'right',
        colKey: 'wkpje',
        title: '销售额',
        ellipsis: true,
        // cell: (h, { row }) => <div>{numberToPrice(row.wkpje)}</div>,
      },
      {
        width: 130,
        align: 'right',
        colKey: 'wkpse',
        title: '销项(应纳)税额',
        ellipsis: true,
        cell: (h, { row }) => <div>{numberToPrice(row.wkpse)}</div>,
      },
    ],
  },
  {
    align: 'center',
    colKey: 'nsjctz',
    title: '纳税检查调整',
    children: [
      {
        width: 130,
        align: 'right',
        colKey: 'nsjctzje',
        title: '销售额',
        ellipsis: true,
        cell: (h, { row }) => <div>{numberToPrice(row.nsjctzje)}</div>,
      },
      {
        width: 130,
        align: 'right',
        colKey: 'nsjctzse',
        title: '销项(应纳)税额',
        ellipsis: true,
        cell: (h, { row }) => <div>{numberToPrice(row.nsjctzse)}</div>,
      },
    ],
  },
  {
    align: 'center',
    colKey: 'hj',
    title: '合计',
    fixed: 'right',
    children: [
      {
        width: 130,
        align: 'right',
        colKey: 'xsehj',
        title: '销售额',
        ellipsis: true,
        fixed: 'right',
        cell: (h, { row }) => <div>{numberToPrice(row.xsehj)}</div>,
      },
      {
        width: 130,
        align: 'right',
        colKey: 'xxsehj',
        title: '销项(应纳)税额',
        ellipsis: true,
        fixed: 'right',
        cell: (h, { row }) => <div>{numberToPrice(row.xxsehj)}</div>,
      },
    ],
  },
  // {
  //   align: 'center',
  //   colKey: 'operation',
  //   title: '操作',
  //   width: 70,
  //   fixed: 'right',
  //   foot: '-',
  // },
];
