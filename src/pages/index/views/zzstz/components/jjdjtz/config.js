import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  skssqq: [{ required: true, message: '必填项', type: 'error' }],
  skssqz: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfig = [
  {
    label: '税款所属期起',
    key: 'skssqq',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    relation: 'skssqz',
    timeRange: 'start',
  },
  {
    label: '税款所属期止',
    key: 'skssqz',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'skssqq',
  },
  {
    label: '企业税号',
    key: 'nsrsbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '企业名称',
    key: 'nsrmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const dataColumns = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
  },
  {
    align: 'center',
    colKey: 'skssqq',
    title: '税款所属期起',
    width: 110,
  },
  {
    align: 'center',
    colKey: 'skssqz',
    title: '税款所属期止',
    width: 110,
  },
  {
    colKey: 'nsrsbh',
    title: '企业税号',
    width: 180,
  },
  {
    colKey: 'nsrmc',
    title: '企业名称',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    colKey: 'syjjdjl',
    title: '使用加计抵减率',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'jxsezcbys',
    title: '进项税额本月数',
    width: 140,
  },
  {
    align: 'right',
    width: 150,
    colKey: 'bdjtjjdjdyjxse',
    title: '不得计提加计抵减对应进项税额',
    ellipsisTitle: true,
  },
  {
    align: 'right',
    width: 150,
    colKey: 'kjtesxbys',
    title: '可计提额上限本月数',
    ellipsisTitle: true,
  },
  {
    align: 'right',
    width: 150,
    colKey: 'kjtesxlj',
    title: '可计提额上限累计数',
    ellipsisTitle: true,
  },
  {
    align: 'right',
    colKey: 'yjte',
    title: '已计提额本月数',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'yjtelj',
    title: '已计提额累计数',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'qmkjte',
    title: '期末可计提额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'jxse',
    title: '进项税额转出本月数',
    ellipsisTitle: true,
    width: 150,
  },
  {
    align: 'right',
    colKey: 'bqtje',
    title: '本期调减额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'ljtje',
    title: '累计调减额',
    width: 120,
  },
  {
    colKey: 'jjdjzcmc',
    title: '适用加计抵减政策',
    width: 240,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
