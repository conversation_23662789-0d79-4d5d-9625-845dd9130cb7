<!--
 * @Descripttion: 台账-增值税加计抵减台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-07-09 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    />

    <div class="queryBtns">
      <gt-space size="10px">
        <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <QsbButton />
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="dataColumns"
        height="100%"
        lazyLoad
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #syjjdjl="{ row }">
          {{ (row.syjjdjl && row.syjjdjl * 100 + '%') || '-' }}
        </template>
        <template #jxsezcbys="{ row }">
          <span>{{ numberToPrice(row.jxsezcbys) }}</span>
        </template>
        <template #bdjtjjdjdyjxse="{ row }">
          <span>{{ numberToPrice(row.bdjtjjdjdyjxse) }}</span>
        </template>
        <template #kjtesxbys="{ row }">
          <span>{{ numberToPrice(row.kjtesxbys) }}</span>
        </template>
        <template #kjtesxlj="{ row }">
          <span>{{ numberToPrice(row.kjtesxlj) }}</span>
        </template>
        <template #yjte="{ row }">
          <span>{{ numberToPrice(row.yjte) }}</span>
        </template>
        <template #yjtelj="{ row }">
          <span>{{ numberToPrice(row.yjtelj) }}</span>
        </template>
        <template #qmkjte="{ row }">
          <span>{{ numberToPrice(row.qmkjte) }}</span>
        </template>
        <template #jxse="{ row }">
          <span>{{ numberToPrice(row.jxse) }}</span>
        </template>
        <template #bqtje="{ row }">
          <span>{{ numberToPrice(row.bqtje) }}</span>
        </template>
        <template #ljtje="{ row }">
          <span>{{ numberToPrice(row.ljtje) }}</span>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { jjdjtzbQuery } from '@/pages/index/api/tzzx/zzstz/jjdjtz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon } from 'tdesign-icons-vue';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { querySearchConfigOneRules, querySearchConfig, dataColumns } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
  },
  data() {
    return {
      userInfo: {},
      querySearchConfigOneRules,
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      formData: {},
      selectedRowKeys: [],
      syjjdjzcList: [],
      tableData: [],
      dataColumns,
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    this.querySearchConfig[0].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    this.querySearchConfig[1].value = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    this.formData.skssqq = dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.formData.skssqz = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.querySearchConfig[1].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData.skssqq = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData.skssqz = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
      }
    }
  },
  mounted() {},
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.skssqq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
  },
  methods: {
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        this.$refs.queryControl.setParams(p);
      } else {
        params = { ...this.formData, ...params };
      }
      try {
        const { data } = await jjdjtzbQuery(params);
        console.log(data.records, 'data.records');
        this.tableData = data.records || [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.pagination.total = data.pageTotal;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'jjdjtzb',
        fileName: '增值税加计抵减台账',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
</style>
