<!--
 * @Descripttion: 台账-增值税一般纳税人收入总账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-06-06 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <t-tabs v-model="tabValue">
        <search-control-panel
          ref="queryControl2"
          class="znsbHeadqueryDiv"
          v-show="tabValue === '1'"
          style="margin-top: 16px"
          :config="querySearchConfigSrlx"
          :formRules="querySearchConfigOneRules"
          @search="query({ flag: true, initQuery: true })"
          :colNum="4"
          @formChange="(v) => (formData2 = v)"
        />
        <search-control-panel
          ref="queryControl1"
          class="znsbHeadqueryDiv"
          v-show="tabValue === '0'"
          style="margin-top: 16px"
          :config="querySearchConfigXxkm"
          :formRules="querySearchConfigOneRules"
          @search="query({ flag: true, initQuery: true })"
          :colNum="4"
          @formChange="(v) => (formData1 = v)"
        />
        <search-control-panel
          ref="queryControl3"
          class="znsbHeadqueryDiv"
          v-show="tabValue === '2'"
          style="margin-top: 16px"
          :config="querySearchConfig3"
          :formRules="querySearchConfigOneRules"
          @search="query({ flag: true, initQuery: true })"
          :colNum="4"
          @formChange="(v) => (formData3 = v)"
        />
        <div class="queryBtns" style="display: flex; justify-content: space-between">
          <gt-space size="10px">
            <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
            <t-button
              variant="outline"
              theme="primary"
              @click="check"
              v-if="this.$store.state.isProduct.envValue && !this.xgmnsrFlag"
              ><ChartIcon slot="icon" />查看底稿</t-button
            >
            <ExportButton
              :tzlx="tzlx"
              :fileName="exportFileName"
              :tzQueryParams="tzQueryParams"
              :exportButtonFlag="exportButtonFlag"
            />
            <BatchExportButton
              v-if="quanshanFlag"
              :tzlx="tzlx"
              :fileName="exportFileName"
              :tzQueryParams="tzQueryParams"
              :exportButtonFlag="exportButtonFlag"
            />
            <t-button
              v-if="tabValue === '1'"
              variant="outline"
              theme="primary"
              :loading="dcydmxLoading"
              @click="dcydmx"
              :disabled="!this.shanxiyidongButtonShowFlag"
              ><FileIcon slot="icon" />导出疑点明细</t-button
            >
            <t-button
              v-if="tabValue === '1'"
              variant="outline"
              theme="primary"
              @click="scwcpz"
              :loading="scwcpzLoading"
              :disabled="!this.shanxiyidongButtonShowFlag"
              ><FilterIcon slot="icon" />生成尾差凭证</t-button
            >
            <QsbButton />
            <!-- 添加小规模纳税人提示文字 -->
            <span v-if="xgmnsrFlag" class="xgmnsr-tip"> 【按3%税率展示】 </span>
          </gt-space>
        </div>
        <t-tab-panel value="1" :label="tabTitleName" :destroyOnHide="false">
          <div class="znsbSbBodyDiv">
            <t-table
              ref="myTable"
              row-key="key"
              hover
              :data="tableData2"
              :columns="tabValue === '1' ? tableColumnsSrlx : []"
              lazyLoad
              :height="dynamicHeight"
              :foot-data="footData2"
              :selected-row-keys="selectedRowKeys"
              @select-change="rehandleSelectChange"
              :pagination="srpagination"
              :loading="tableLoading"
              @page-change="(e) => pageChange(e, 1)"
              :rowClassName="rowClassName"
              @row-click="onRowMousedown"
            >
              <!-- :horizontalScrollAffixedBottom="true" -->
              <!-- tableLayout="auto" -->
              <template #xh="{ rowIndex }">{{
                (srpagination.current - 1) * srpagination.pageSize + rowIndex + 1
              }}</template>
              <template #sl1="{ row }">
                {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
              </template>
              <template #xssr="{ row }">
                {{ numberToPrice(row.xssr) }}
              </template>
              <template #xxse="{ row }">
                {{ numberToPrice(row.xxse) }}
              </template>
              <template #xxskmse="{ row }">
                {{ numberToPrice(row.xxskmse) }}
              </template>
              <template #cyse="{ row }">
                <div
                  v-if="row.ccyz === 'Y'"
                  style="display: flex; justify-content: space-between; align-items: center; float: right"
                >
                  <t-tooltip :showArrow="false" :destroyOnClose="false">
                    <span
                      class="specText"
                      v-if="row.srlxdm === '180'"
                      @click="openXxsydcymx(row, 1)"
                      style="color: #0052d9; text-decoration: underline"
                      >{{ numberToPrice(row.cyse) }}</span
                    >
                    <span v-else>{{ numberToPrice(row.cyse) }}</span>
                    <!-- <span>{{ numberToPrice(row.cyse) }}</span> -->
                    <template #content>
                      <span>{{ row.ccmsg }}</span>
                      <span style="margin-left: 10px; color: lightblue; cursor: pointer" @click="openXxsydcymx(row, 0)"
                        >请点此查看销项税疑点差异明细</span
                      >
                    </template>
                    <error-circle-filled-icon
                      :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }"
                    />
                  </t-tooltip>
                </div>
                <span
                  class="specText"
                  v-else-if="row.srlxdm === '180'"
                  @click="openXxsydcymx(row, 1)"
                  style="color: #0052d9; text-decoration: underline"
                  >{{ numberToPrice(row.cyse) }}</span
                >
                <span v-else>{{ numberToPrice(row.cyse) }}</span>
              </template>
            </t-table>
          </div>
          <ValidateDialog
            :validate-rules="validateRules"
            :handleMsg="true"
            :extraHandleMsg="true"
            @ruleClick="ruleClick"
            @toggle="toggle"
            @handleMsg="(item) => openXxsydcymxFromValidate(item)"
            @extraHandleMsg="(item) => (hlVisible = item)"
          />
          <div v-show="hlVisible">
            <t-dialog
              theme="warning"
              style="display: block; border-radius: 10px"
              :width="400"
              header="警示"
              body="是否确定忽略该疑点差异信息"
              confirmBtn="是"
              cancelBtn="否"
              :onConfirm="qrhlcyxx"
              :onClose="closeBox"
            >
            </t-dialog>
          </div>
        </t-tab-panel>
        <t-tab-panel value="0" label="按销项科目" :destroyOnHide="false">
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="uuid"
              hover
              :data="tableData1"
              :columns="tabValue === '0' ? tableColumnsXxkm : []"
              lazyLoad
              height="100%"
              :foot-data="footData1"
              :pagination="pagination"
              :loading="tableLoading"
              @page-change="(e) => pageChange(e, 0)"
            >
              <!-- :horizontalScrollAffixedBottom="true" -->
              <template #xh="{ rowIndex }">{{
                (pagination.current - 1) * pagination.pageSize + rowIndex + 1
              }}</template>
              <template #sl1="{ row }">
                {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
              </template>
              <template #xssr="{ row }">
                <div style="float: right">
                  <span
                    v-if="row.srlxDm === '110' || row.srlxDm === '120'"
                    class="specText"
                    @click="openSrmx(row, row.xssrkmdm)"
                    style="color: #0052d9; text-decoration: underline"
                    >{{ numberToPrice(row.xssr) }}</span
                  >
                  <span v-else>{{ numberToPrice(row.xssr) }}</span>
                </div>
              </template>
              <template #xsse="{ row }">
                <div style="float: right">
                  <span
                    v-if="row.srlxDm === '130'"
                    class="specText"
                    @click="openSrmx(row, row.xssekmdm)"
                    style="color: #0052d9; text-decoration: underline"
                    >{{ numberToPrice(row.xsse) }}</span
                  >
                  <span v-else>{{ numberToPrice(row.xsse) }}</span>
                </div>
              </template>
            </t-table>
          </div>
        </t-tab-panel>
        <t-tab-panel value="2" label="汇总分配（分摊预征）" :destroyOnHide="false" v-if="this.hzfpTabVisible">
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="uuid"
              hover
              :data="tableData3"
              :columns="tabValue === '2' ? tableColumnsHzfp : []"
              lazyLoad
              height="100%"
              :foot-data="footData3"
              :pagination="hzpagination"
              :loading="tableLoading"
              @page-change="(e) => pageChange(e, 2)"
            >
              <!-- :horizontalScrollAffixedBottom="true" -->
              <!-- tableLayout="auto" -->
              <template #xh="{ rowIndex }">{{
                (hzpagination.current - 1) * hzpagination.pageSize + rowIndex + 1
              }}</template>
              <template #fzjgybhwjlwjzjtxssr="{ row }">
                {{ numberToPrice(row.fzjgybhwjlwjzjtxssr) }}
              </template>
              <template #fzjgysfwjzjtxssr="{ row }">
                {{ numberToPrice(row.fzjgysfwjzjtxssr) }}
              </template>
              <template #fzjgybhwjlwxssr="{ row }">
                <span
                  class="specText"
                  @click="openSrmxFromHzfp(row, '01,02')"
                  style="color: #0052d9; text-decoration: underline"
                  >{{ numberToPrice(row.fzjgybhwjlwxssr) }}</span
                >
              </template>
              <template #fzjgysfwxssr="{ row }">
                <span
                  class="specText"
                  @click="openSrmxFromHzfp(row, '03')"
                  style="color: #0052d9; text-decoration: underline"
                  >{{ numberToPrice(row.fzjgysfwxssr) }}</span
                >
              </template>
            </t-table>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>
<script>
import { getAll, getLrzx, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import {
  initSrzzQuery,
  initSrzzQueryHj,
  initSrzzQueryInXgmnsr,
  querySrzzAndSrcybdhzb,
  querySrzzAndSrcybdhzbHj,
  querySrzzAndSrcybdhzbInXgmnsr,
  getTzDifferences,
  cymxblunt,
  queryHzfp,
  sfzjg,
  cybdhl,
} from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { downloadBlobFile } from '@/core/download';
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import ExportButton from '@/pages/index/components/ExportButton/index.vue';
import BatchExportButton from '@/pages/index/components/ExportButton/index-quanshan.vue';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import { ErrorCircleFilledIcon, ChartIcon, FileIcon, FilterIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { computeSszq, multiSelectHandle, jyssReadyStatusFetch } from '@/pages/index/views/util/tzzxTools.js';
import { numberToPrice } from '@/utils/numberToCurrency';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import {
  querySearchConfig1,
  querySearchConfig2,
  querySearchConfig3,
  xskmColumns,
  srlxColumns,
  hzfpColumns,
} from './config.js';

export default {
  components: {
    SkeletonFrame,
    QsbButton,
    ExportButton,
    BatchExportButton,
    ExtractDataButton,
    FileIcon,
    SearchControlPanel,
    ErrorCircleFilledIcon,
    ValidateDialog,
    ChartIcon,
    FilterIcon,
  },
  data() {
    return {
      loading: true,
      validateDialogToggle: true,
      isProduct: this.$store.state.isProduct.envValue,
      userInfo: {},
      formData1: {},
      formData2: {},
      formData3: {},
      querySearchConfigOneRules: {
        ssyfq: [{ required: true, message: '必填项', type: 'error' }],
        ssyfz: [{ required: true, message: '必填项', type: 'error' }],
      },
      djxh: '',
      nsrsbh: '',
      tabValue: '1',
      querySearchConfig1,
      querySearchConfig2,
      querySearchConfig3,
      hzfpTabVisible: false,
      dcydmxLoading: false,
      scwcpzLoading: false,
      tableLoading: false,
      hlVisible: false,
      firstInit: true,
      firstInit1: true,
      firstInit2: true,
      backFromCymx: false,
      selectedRowKeys: [],
      slList: [],
      jsfsList: [],
      zsxm1List: [],
      srlxList: [],
      srlxList2: [],
      lrzxList: [],
      kmdmList: [],
      tableData1: [],
      footData1: [],
      tableData2: [],
      footData2: [],
      tableData3: [],
      footData3: [],
      validateRules: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      srpagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      hzpagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      activeRule: '',
      loadedTabs: { 0: false, 1: false, 2: false }, // 新增三tab加载状态
      remoteColumnsSrlx: [], // 新增接收后端列配置-按收入类型
      remoteColumnsXxkm: [], // 新增接收后端列配置-按销项科目
      remoteColumnsHzfp: [], // 新增接收后端列配置-汇总分配
      remotequerySearchConfigSrlx: [], // 动态查询条件配置-按收入类型
      remotequerySearchConfigXxkm: [], // 动态查询条件配置-按销项科目
      remotequerySearchConfigHzfp: [], // 动态查询条件配置-汇总分配
    };
  },
  created() {},
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
    tabValue(newVal) {
      if (!this.loadedTabs[newVal]) {
        this.query({ initQuery: true });
        this.loadedTabs[newVal] = true;
      }
      // 新增：切换tab时重新获取表头配置
      // this.fetchTableColumns();
    },
    // '$store.state.zdmczh.companyDifferentiationConfig.companyDifferentiationConfig': {
    //   handler(newVal, oldVal) {
    //     console.log('companyDifferentiationConfig变化', newVal, oldVal);
    //     this.fetchTableColumns();
    //   },
    //   immediate: true,
    // },
  },
  computed: {
    tabTitleName() {
      return this.shanxiyidongFlag ? '汇总收入' : '按收入类型';
    },
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    querySearchConfigSrlx() {
      if (!this.remotequerySearchConfigSrlx.length) {
        // 兼容初始化状态
        return this.defaultQueryParamsColumnsSrlx;
      }

      // 使用动态配置的列
      return this.remotequerySearchConfigSrlx;
    },
    querySearchConfigXxkm() {
      if (!this.remotequerySearchConfigXxkm.length) {
        // 兼容初始化状态
        return this.defaultQueryParamsColumnsXxkm;
      }
      // 使用动态配置的列
      return this.remotequerySearchConfigXxkm;
    },
    defaultQueryParamsColumnsSrlx() {
      let config = this.querySearchConfig2;
      // 泉膳环境下移除利润中心
      if (this.quanshanFlag) {
        config = config.filter((item) => item.key !== 'lrzx');
      }
      if (this.xgmnsrFlag) {
        config = config.filter((item) => item.key !== 'jsfsDm1');
      }
      if (this.shanxiyidongFlag) {
        config = config.filter((item) => item.key !== 'srlxDm');
      }
      return config;
    },
    defaultQueryParamsColumnsXxkm() {
      let config = this.querySearchConfig1;
      if (this.xgmnsrFlag) {
        config = this.querySearchConfig1.filter((item) => item.key !== 'jsfsDm1');
      }
      return config;
    },
    // ssyfqC() {
    //   return this.tabValue * 1
    //     ? dayjs(this.formData2.ssyfq).format('YYYYMM')
    //     : dayjs(this.formData1.ssyfq).format('YYYYMM');
    // },
    // ssyfzC() {
    //   return this.tabValue * 1
    //     ? dayjs(this.formData2.ssyfz).format('YYYYMM')
    //     : dayjs(this.formData1.ssyfz).format('YYYYMM');
    // },
    dynamicHeight() {
      let height = '100%';
      if (!this.validateDialogToggle) {
        return height;
      }
      switch (this.validateRules.length) {
        case 0:
          height = '100%';
          break;
        case 1:
          height = '85%';
          break;
        case 2:
          height = '80%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '70%';
          break;
      }
      return height;
    },
    sszqToExtract() {
      return dayjs(this.tabValue * 1 ? this.formData2.ssyfq : this.formData1.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
    tzlx() {
      if (this.tabValue === '2') {
        return 'srHjzp';
      }
      if (this.tabValue === '1') {
        return this.xgmnsrFlag ? 'XgmnsrSrzzAndSrcybdhzb' : 'SrzzAndSrcybdhzb';
      }
      if (this.tabValue === '0') {
        return this.xgmnsrFlag ? 'xgmnsrSrZzTz' : 'srZzTz';
      }
      return '';
    },
    exportFileName() {
      if (this.tabValue === '2') {
        return '汇总分配表导出';
      }
      if (this.tabValue === '1') {
        return '收入总账1表导出';
      }
      if (this.tabValue === '0') {
        return '收入总账表导出';
      }
      return '';
    },
    tzQueryParams() {
      let cxParam = {};
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
      };
      if (this.tabValue === '2') {
        djParam.pageNum = this.hzpagination.current;
        djParam.pageSize = this.hzpagination.pageSize;
        cxParam = {
          ...this.formData3,
          sszqq: dayjs(this.formData3.sszqq).format('YYYYMM'),
          sszqz: dayjs(this.formData3.sszqz).format('YYYYMM'),
          ...djParam,
        };
      }
      if (this.tabValue === '1') {
        djParam.pageNum = this.srpagination.current;
        djParam.pageSize = this.srpagination.pageSize;
        cxParam = this.xgmnsrFlag
          ? {
              ...this.formData2,
              sszqq:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfq).format('YYYYMM')
                  : dayjs(this.formData1.ssyfq).format('YYYYMM'),
              sszqz:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfz).format('YYYYMM')
                  : dayjs(this.formData1.ssyfz).format('YYYYMM'),
              zsxmDm1: multiSelectHandle(this.formData2.zsxmDm1),
              sl1: multiSelectHandle(this.formData2.sl1),
              srlxDm: multiSelectHandle(this.formData2.srlxDm),
              xssrkmdm: multiSelectHandle(this.formData2.xssrkmdm),
              lrzx: multiSelectHandle(this.formData2.lrzx),
              ...djParam,
            }
          : {
              ...this.formData2,
              sszqq:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfq).format('YYYYMM')
                  : dayjs(this.formData1.ssyfq).format('YYYYMM'),
              sszqz:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfz).format('YYYYMM')
                  : dayjs(this.formData1.ssyfz).format('YYYYMM'),
              jsfsDm1: multiSelectHandle(this.formData2.jsfsDm1),
              zsxmDm1: multiSelectHandle(this.formData2.zsxmDm1),
              sl1: multiSelectHandle(this.formData2.sl1),
              srlxDm: multiSelectHandle(this.formData2.srlxDm),
              xssrkmdm: multiSelectHandle(this.formData2.xssrkmdm),
              lrzx: multiSelectHandle(this.formData2.lrzx),
              ...djParam,
            };
      }
      if (this.tabValue === '0') {
        djParam.pageNum = this.pagination.current;
        djParam.pageSize = this.pagination.pageSize;
        cxParam = this.xgmnsrFlag
          ? {
              ...this.formData1,
              sszqq:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfq).format('YYYYMM')
                  : dayjs(this.formData1.ssyfq).format('YYYYMM'),
              sszqz:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfz).format('YYYYMM')
                  : dayjs(this.formData1.ssyfz).format('YYYYMM'),
              zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
              sl1: multiSelectHandle(this.formData1.sl1),
              srlxDm: multiSelectHandle(this.formData1.srlxDm),
              lrzx: multiSelectHandle(this.formData1.lrzx),
              ...djParam,
            }
          : {
              ...this.formData1,
              sszqq:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfq).format('YYYYMM')
                  : dayjs(this.formData1.ssyfq).format('YYYYMM'),
              sszqz:
                this.tabValue * 1
                  ? dayjs(this.formData2.ssyfz).format('YYYYMM')
                  : dayjs(this.formData1.ssyfz).format('YYYYMM'),
              jsfsDm1: multiSelectHandle(this.formData1.jsfsDm1),
              zsxmDm1: multiSelectHandle(this.formData1.zsxmDm1),
              sl1: multiSelectHandle(this.formData1.sl1),
              srlxDm: multiSelectHandle(this.formData1.srlxDm),
              lrzx: multiSelectHandle(this.formData1.lrzx),
              ...djParam,
            };
      }
      return cxParam;
    },
    exportButtonFlag() {
      if (this.tabValue === '2') {
        return !this.tableData3.length;
      }
      if (this.tabValue === '1') {
        return !this.tableData2.length;
      }
      if (this.tabValue === '0') {
        return !this.tableData1.length;
      }
      return false;
    },
    tableColumnsSrlx() {
      let columns = this.remoteColumnsSrlx.length ? [...this.remoteColumnsSrlx] : this.getCurrentDefaultColumns();

      if (this.xgmnsrFlag) {
        // 调整所属期字段的宽度
        columns.find((c) => c.colKey === 'sszq').width = 160;
        columns = columns.filter((item) => item.colKey !== 'jsfsmc');
      } else {
        columns.find((c) => c.colKey === 'sszq').width = 80;
      }
      if (this.shanxiyidongFlag) {
        columns = columns.filter((item) => item.colKey !== 'srlxmc');
      }
      if (this.quanshanFlag) {
        columns = columns.filter((item) => item.colKey !== 'gsh2' && item.colKey !== 'lrzx');
      }
      return columns;
    },

    tableColumnsXxkm() {
      const columns = this.remoteColumnsXxkm.length ? [...this.remoteColumnsXxkm] : this.getCurrentDefaultColumns();

      if (this.xgmnsrFlag) {
        columns.find((c) => c.colKey === 'sszq').width = 160;
        columns.find((c) => c.title === '销项科目代码').colKey = 'xssekmdm';
        columns.find((c) => c.title === '销项科目名称').colKey = 'xssekmmc';
        return columns.filter((item) => item.colKey !== 'jsfsmc');
      }
      columns.find((c) => c.colKey === 'sszq').width = 80;
      columns.find((c) => c.title === '销项科目代码').colKey = 'xssrkmdm';
      columns.find((c) => c.title === '销项科目名称').colKey = 'xssrkmmc';
      return columns;
    },

    tableColumnsHzfp() {
      if (!this.remoteColumnsHzfp.length) return this.getCurrentDefaultColumns(); // 兼容初始化状态
      return [...this.remoteColumnsHzfp];
    },
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    shanxiyidongZfjgFlag() {
      // true 为总机构， false 为分机构
      return this.shanxiyidongFlag && this.$store.state.zdmczh.companyDifferentiationConfig?.fzjgbz === 'zjg';
    },
    shanxiyidongButtonShowFlag() {
      if (!this.shanxiyidongFlag) {
        return true;
      }
      return this.shanxiyidongZfjgFlag;
    },
  },
  mounted() {
    this.sfzjg();
    this.initQueryConditions();
    this.fetchTableColumns(); // 新增调用
    this.getLrzx();
    this.getQueryParamsList();
  },
  methods: {
    // 动态配置表头信息
    fetchTableColumns() {
      this.fetchQueryParamsConfig();
      try {
        this.columnsLoading = true;
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 确保获取当前tab对应的列配置
        const defaultColumnsSrlx = JSON.parse(JSON.stringify(srlxColumns));
        const defaultColumnsXxkm = JSON.parse(JSON.stringify(xskmColumns));
        const defaultColumnsHzfp = JSON.parse(JSON.stringify(hzfpColumns));

        this.remoteColumnsSrlx = defaultColumnsSrlx
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);
            if (matchedItem && matchedItem.displayName === column.title && matchedItem.displayFlag === false) {
              return null;
            }
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);
        this.remoteColumnsXxkm = defaultColumnsXxkm
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);
            if (matchedItem && matchedItem.displayName === column.title && matchedItem.displayFlag === false) {
              return null;
            }
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);
        this.remoteColumnsHzfp = defaultColumnsHzfp
          .map((column) => {
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.colKey);
            if (matchedItem && matchedItem.displayName === column.title && matchedItem.displayFlag === false) {
              return null;
            }
            return matchedItem
              ? {
                  ...column,
                  title: matchedItem.displayName,
                  ...(matchedItem.width !== undefined && { width: matchedItem.width }),
                }
              : column;
          })
          .filter(Boolean);
        console.log('变更表头配置成功', this.remoteColumnsSrlx);
      } catch (e) {
        console.error('获取表头配置失败', e);
        this.remoteColumnsSrlx = JSON.parse(JSON.stringify(srlxColumns));
        this.remoteColumnsXxkm = JSON.parse(JSON.stringify(xskmColumns));
        this.remoteColumnsHzfp = JSON.parse(JSON.stringify(hzfpColumns));
      } finally {
        this.columnsLoading = false;
      }
    },
    // 动态配置查询条件信息
    fetchQueryParamsConfig() {
      try {
        const { companyDifferentiationConfig } = this.$store.state.zdmczh.companyDifferentiationConfig;

        // 获取当前tab对应的默认列配置
        const defaultQueryParamsColumnsSrlx = JSON.parse(JSON.stringify(this.defaultQueryParamsColumnsSrlx));
        const defaultQueryParamsColumnsXxkm = JSON.parse(JSON.stringify(this.defaultQueryParamsColumnsXxkm));

        // 处理查询条件配置
        this.remotequerySearchConfigSrlx = defaultQueryParamsColumnsSrlx
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);
            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }
            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列
        this.remotequerySearchConfigXxkm = defaultQueryParamsColumnsXxkm
          .map((column) => {
            // 查找匹配的配置项
            const matchedItem = companyDifferentiationConfig?.find((item) => item.item === column.key);
            // 如果配置项存在且displayFlag为false，则过滤掉该列
            if (matchedItem?.displayFlag === false && matchedItem?.displayName === column.label) {
              return null;
            }
            // 如果配置项存在，则更新列标题
            return matchedItem ? { ...column, label: matchedItem.displayName } : column;
          })
          .filter(Boolean); // 过滤掉null的列
      } catch (e) {
        console.error('[SRZZ] 获取查询条件配置失败:', e);
        // 失败时回退到默认配置
        this.remotequerySearchConfigSrlx = JSON.parse(JSON.stringify(this.defaultQueryParamsColumnsSrlx));
        this.remotequerySearchConfigXxkm = JSON.parse(JSON.stringify(this.defaultQueryParamsColumnsXxkm));
      }
    },
    // 获取当前tab对应的默认列配置
    getCurrentDefaultColumns() {
      switch (this.tabValue) {
        case '0':
          console.log('xskmColumns', xskmColumns);
          return xskmColumns;
        case '1':
          console.log('srlxColumns', srlxColumns);
          return srlxColumns;
        case '2':
          console.log('hzfpColumns', hzfpColumns);
          return hzfpColumns;
        default:
          return [];
      }
    },

    initTabValue() {
      this.tabValue = '1';
      console.log('initTabValue', this.tabValue);
    },
    async sfzjg() {
      if (this.xgmnsrFlag) {
        this.hzfpTabVisible = false;
        this.tabValue = '1';
      } else if (this.shanxiyidongFlag) {
        // 山西移动不包含Hzfp分支
        this.hzfpTabVisible = false;
        this.tabValue = '1';
      } else {
        const params = {
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
          sszqq:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfq).format('YYYYMM')
              : dayjs(this.formData1.ssyfq).format('YYYYMM'),
          sszqz:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfz).format('YYYYMM')
              : dayjs(this.formData1.ssyfz).format('YYYYMM'),
          djxh: this.$store.state.zzstz.userInfo?.djxh,
        };
        const { data } = await sfzjg(params);
        this.hzfpTabVisible = data.sfzjg;
        if (!data.sfzjg) {
          this.tabValue = '1';
        }
      }
    },
    initQueryConditions() {
      if (Object.keys(this.$route.query).length) {
        console.log('Object.keys(this.$route.query)', Object.keys(this.$route.query));
        console.log('url-params', this.$route.query);
        if (this.$route.query.skssqq && this.$route.query.skssqz) {
          this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
          this.querySearchConfig1[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
          this.formData1.ssyfq = dayjs(this.$route.query.skssqq).format('YYYYMM');
          this.formData1.ssyfz = dayjs(this.$route.query.skssqz).format('YYYYMM');
          this.querySearchConfig2[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
          this.querySearchConfig2[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
          this.formData2.ssyfq = dayjs(this.$route.query.skssqq).format('YYYYMM');
          this.formData2.ssyfz = dayjs(this.$route.query.skssqz).format('YYYYMM');
          this.querySearchConfig3[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM');
          this.querySearchConfig3[1].value = dayjs(this.$route.query.skssqz).format('YYYY-MM');
          this.formData3.sszqq = dayjs(this.$route.query.skssqq).format('YYYYMM');
          this.formData3.sszqz = dayjs(this.$route.query.skssqz).format('YYYYMM');
          return;
        }
      }
      this.querySearchConfig1[0].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfig1[1].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData1.ssyfq = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData1.ssyfz = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfig2[0].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfig2[1].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData2.ssyfq = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData2.ssyfz = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfig3[0].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.querySearchConfig3[1].value = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData3.sszqq = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').startOf('quarter').format('YYYY-MM')
        : computeSszq();
      this.formData3.sszqz = this.xgmnsrFlag
        ? dayjs().add(-1, 'quarter').endOf('quarter').format('YYYY-MM')
        : computeSszq();
    },
    async qrhlcyxx() {
      console.log('忽略成功');
      try {
        const params = {
          uuid: this.tableData2.find((t) => t.index === this.hlVisible.index)?.uuid,
          djxh: this.hlVisible.djxh,
          nsrsbh: this.hlVisible.nsrsbh,
          nsrmc: this.hlVisible.nsrmc,
          sszq: this.hlVisible.sszq,
          ywlxDm: '100',
        };
        const { msg } = await cybdhl(params);
        console.log('确认忽略差异信息操作返回msg', msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query({ flag: true });
        this.hlVisible = false;
      }
    },
    closeBox() {
      this.hlVisible = false;
    },
    // 生成尾差凭证
    async scwcpz() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要生成尾差凭证的数据行');
      } else {
        try {
          this.scwcpzLoading = true;
          const params = [];
          this.selectedRowKeys.forEach((i) => {
            const t = this.tableData2.find((t) => t.key === i);
            params.push({
              cyse: t?.cyse,
              xssr: t?.xssr,
              xxskmse: t?.xxskmse,
              sl1: t?.sl1,
              sszq: this.xgmnsrFlag ? dayjs(t?.sszq.substring(0, 7)).format('YYYYMM') : t?.sszq,
              djxh: this.$store.state.zzstz.userInfo?.djxh,
              nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
              nsrmc: this.$store.state.zzstz.userInfo?.jgmc,
              gsh2: t?.gsh2,
              lrzx: t?.lrzx,
              jsfsDm1: t?.jsfsDm1,
              zsxmDm: t?.zsxmDm,
              srlxDm: t?.srlxdm,
            });
          });
          console.log('wcpzParams', params);
          const { msg } = await cymxblunt(params);
          console.log('生成尾差凭证情况msg', msg);
          this.$message.success(msg);
          this.query({ flag: true });
        } catch (e) {
          console.log('尾差凭证生成失败：', e);
        } finally {
          this.selectedRowKeys = [];
          this.scwcpzLoading = false;
        }
      }
    },
    // 从校验窗口的链接跳转销项疑点差异明细页面
    openXxsydcymxFromValidate(item) {
      console.log('item', item);
      this.openXxsydcymx(
        this.tableData2.find((t) => t.index === item.index),
        0,
      );
    },
    // 打开销项疑点差异明细页面，type区分 0-警示/1-下钻
    openXxsydcymx(row, type) {
      console.log('row, type', row, type);
      let queryData = {};
      if (type) {
        queryData = this.xgmnsrFlag
          ? {
              sszqq: Number(row.sszq.substring(0, 4).concat(row.sszq.substring(5, 7))),
              sszqz: Number(row.sszq.substring(8, 12).concat(row.sszq.substring(13, 15))),
              gsh2: row.gsh2,
              zsxmDm1: row.zsxmDm,
              sl1: row.sl1,
              lrzx: row.lrzx,
            }
          : {
              sszq: row.sszq,
              gsh2: row.gsh2,
              jsfsDm1: row.jsfsDm1,
              zsxmDm1: row.zsxmDm,
              sl1: row.sl1,
              lrzx: row.lrzx,
            };
      } else {
        queryData = this.xgmnsrFlag
          ? {
              sszqq: Number(row.sszq.substring(0, 4).concat(row.sszq.substring(5, 7))),
              sszqz: Number(row.sszq.substring(8, 12).concat(row.sszq.substring(13, 15))),
              gsh2: row.gsh2,
              jsfsDm1: row.jsfsDm1,
              zsxmDm1: row.zsxmDm,
              sl1: row.sl1,
              lrzx: row.lrzx,
              ccyz: row.ccyz,
            }
          : {
              sszq: row.sszq,
              gsh2: row.gsh2,
              jsfsDm1: row.jsfsDm1,
              zsxmDm1: row.zsxmDm,
              sl1: row.sl1,
              lrzx: row.lrzx,
              ccyz: row.ccyz,
            };
      }
      this.$emit('openPage', {
        from: 'srzz',
        data: {
          ...queryData,
        },
        type: 'xxscymx',
      });
    },
    pageChange({ current, pageSize }, type) {
      // [this.pagination, this.srpagination, this.hzpagination][type].current =
      //   pageSize !== [this.pagination, this.srpagination, this.hzpagination][type].pageSize ? 1 : current;
      // [this.pagination, this.srpagination, this.hzpagination][type].pageSize = pageSize;
      // this.query({ fy: true });
      // 根据type重置对应tab的加载状态
      // 根据type确定要更新的分页对象
      const pagination = [this.pagination, this.srpagination, this.hzpagination][type];

      // 更新当前页码和每页条数
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;

      // 触发查询
      this.query({ initQuery: true });
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      console.log(value, selectedRowData);
    },
    async dcydmx() {
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.srpagination.current,
        pageSize: 1000000,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: this.xgmnsrFlag ? 'xgmnsrSrcybdmx' : 'Srcybdmx',
        fileName: '疑点明细',
        cxParam: {
          // ...this.formData2,
          sszqq:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfq).format('YYYYMM')
              : dayjs(this.formData1.ssyfq).format('YYYYMM'),
          sszqz:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfz).format('YYYYMM')
              : dayjs(this.formData1.ssyfz).format('YYYYMM'),
          ...djParam,
          ccyz: 'Y',
        },
      };
      console.log(params);
      this.dcydmxLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcydmxLoading = false;
    },
    check() {
      this.$router.push('/lsgl');
    },
    openSrmx(row, kmdm) {
      let data = {};
      if (this.xgmnsrFlag) {
        data = {
          // srzzuuid: row.uuid, //数据加工时无法加工srzzuuid，暂时拿掉
          sszqq: dayjs(String(row.sszq).substring(0, 7)).format('YYYYMM'),
          sszqz: dayjs(String(row.sszq).substring(8, 15)).format('YYYYMM'),
          lrzx: row.lrzx,
          srlxDm: row.srlxDm,
          zsxmDm1: row.zsxmDm1,
          sl1: row.sl1,
          kmdm,
          zzscbz: 'Y',
          // 2024.10.23 新需求：通过销项台账跳转进入收入明细页面时，查询结果不显示凭证抬头文本为“乐企调账”的凭证。通过菜单进入收入明细页面时，查询结果显示全部凭证信息。
          tzbz: 'Y',
        };
      } else {
        data = {
          // srzzuuid: row.uuid, //数据加工时无法加工srzzuuid，暂时拿掉
          sszq: String(row.sszq),
          lrzx: row.lrzx,
          srlxDm: row.srlxDm,
          jsfsDm1: row.jsfsDm1,
          zsxmDm1: row.zsxmDm1,
          sl1: row.sl1,
          kmdm,
          zzscbz: 'Y',
          // 2024.10.23 新需求：通过销项台账跳转进入收入明细页面时，查询结果不显示凭证抬头文本为“乐企调账”的凭证。通过菜单进入收入明细页面时，查询结果显示全部凭证信息。
          tzbz: 'Y',
        };
      }
      this.$emit('openPage', {
        from: 'srzz',
        data,
        type: 'srmx',
      });
    },
    openSrmxFromHzfp(row, zsxmDm) {
      this.$emit('openPage', {
        from: 'srzz',
        data: {
          sszq: dayjs(row.ssyf).format('YYYYMM'),
          bhznsqynsrsbh: row.fzjgnsrsbh,
          zsxmDm1: zsxmDm,
        },
        type: 'srmx',
      });
    },
    async getQueryParamsList() {
      this.srlxList2 = this.quanshanFlag
        ? [
            { value: '180', label: '主营及其他业务收入' },
            { value: '130', label: '视同销售收入' },
            { value: '140', label: '暂估收入' },
          ]
        : [
            { value: '180', label: '主营及其他业务收入' },
            { value: '130', label: '视同销售收入' },
          ];
      const res1 = await getAll();
      this.jsfsList = res1.data.jsfsList;
      this.zsxm1List = res1.data.zsxmList;
      this.srlxList = res1.data.srlxList;
      this.kmdmList = res1.data.kmList;
      if (this.tongyongguojiFlag) {
        this.slList = res1.data.slList.map((value) => ({
          value,
          label: `${(value * 100).toFixed(0)}%`,
        }));
      } else {
        const res2 = await getJsfssldzb();
        this.slList = this.uniqueObjects(res2.data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      }
      if (this.querySearchConfigXxkm.find((c) => c.key === 'jsfsDm1')) {
        this.querySearchConfigXxkm.find((c) => c.key === 'jsfsDm1').selectList = this.jsfsList;
      }
      if (this.querySearchConfigSrlx.find((c) => c.key === 'jsfsDm1')) {
        this.querySearchConfigSrlx.find((c) => c.key === 'jsfsDm1').selectList = this.jsfsList;
      }
      // this.querySearchConfigXxkm.find((c) => c.key === 'jsfsDm1').selectList = this.jsfsList;
      // this.querySearchConfigSrlx.find((c) => c.key === 'jsfsDm1').selectList = this.jsfsList;
      this.querySearchConfigXxkm.find((c) => c.key === 'zsxmDm1').selectList = this.xgmnsrFlag
        ? this.zsxm1List.slice(0, 3)
        : this.zsxm1List.slice(0, 5);
      this.querySearchConfigSrlx.find((c) => c.key === 'zsxmDm1').selectList = this.xgmnsrFlag
        ? this.zsxm1List.slice(0, 3)
        : this.zsxm1List.slice(0, 5);
      this.querySearchConfigXxkm.find((c) => c.key === 'srlxDm').selectList = this.srlxList;
      if (this.querySearchConfigSrlx.find((c) => c.key === 'srlxDm')) {
        this.querySearchConfigSrlx.find((c) => c.key === 'srlxDm').selectList = this.srlxList2;
      }
      // this.querySearchConfigSrlx.find((c) => c.key === 'srlxDm').selectList = this.xgmnsrFlag
      //   ? this.srlxList2.filter((item) => item.value !== '130')
      //   : this.srlxList2;
      this.querySearchConfigXxkm.find((c) => c.key === 'xssrkmdm').selectList = this.kmdmList.map((d) => ({
        label: `${d.value} | ${d.label}`,
        value: d.value,
      }));
      this.querySearchConfigXxkm.find((c) => c.key === 'sl1').selectList = this.xgmnsrFlag
        ? this.slList
            .filter((i) => [0, 0.03, 0.05].includes(i.value))
            .map((d) => ({ label: d.label, value: String(d.value) }))
        : this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
      this.querySearchConfigSrlx.find((c) => c.key === 'sl1').selectList = this.xgmnsrFlag
        ? this.slList
            .filter((i) => [0, 0.03, 0.05].includes(i.value))
            .map((d) => ({ label: d.label, value: String(d.value) }))
        : this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
      // this.querySearchConfig1[2].selectList = this.jsfsList;
      // this.querySearchConfig2[2].selectList = this.jsfsList;
      // this.querySearchConfig1[3].selectList = this.xgmnsrFlag ? this.zsxm1List.slice(0, 3) : this.zsxm1List.slice(0, 5);
      // this.querySearchConfig2[3].selectList = this.xgmnsrFlag ? this.zsxm1List.slice(0, 3) : this.zsxm1List.slice(0, 5);
      // this.querySearchConfig1[5].selectList = this.xgmnsrFlag
      //   ? this.srlxList.filter((item) => item.value !== '130')
      //   : this.srlxList;
      // this.querySearchConfig2[5].selectList = this.xgmnsrFlag
      //   ? this.srlxList2.filter((item) => item.value !== '130')
      //   : this.srlxList2;
      // this.querySearchConfig1[6].selectList = this.kmdmList.map((d) => ({
      //   label: `${d.value} | ${d.label}`,
      //   value: d.value,
      // }));
      // this.querySearchConfig1[4].selectList = this.xgmnsrFlag
      //   ? this.slList
      //       .filter((i) => [0, 0.03, 0.05].includes(i.value))
      //       .map((d) => ({ label: d.label, value: String(d.value) }))
      //   : this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
      // this.querySearchConfig2[4].selectList = this.xgmnsrFlag
      //   ? this.slList
      //       .filter((i) => [0, 0.03, 0.05].includes(i.value))
      //       .map((d) => ({ label: d.label, value: String(d.value) }))
      //   : this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async getLrzx() {
      try {
        const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = data;
        this.querySearchConfigSrlx.find((c) => c.key === 'lrzx').selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        this.querySearchConfigXxkm.find((c) => c.key === 'lrzx').selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        // this.querySearchConfig1[7].selectList = this.lrzxList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        // this.querySearchConfig2[6].selectList = this.lrzxList.map((d) => ({
        //   label: `${d.value} | ${d.label}`,
        //   value: d.value,
        // }));
        if (this.formData1.lrzx) {
          this.formData1.lrzx =
            this.querySearchConfigXxkm
              .find((c) => c.key === 'lrzx')
              .selectList.find((t) => t.value === this.formData1.lrzx)?.label || '';
        }
        if (this.formData2.lrzx) {
          this.formData2.lrzx =
            this.querySearchConfigSrlx
              .find((c) => c.key === 'lrzx')
              .selectList.find((t) => t.value === this.formData2.lrzx)?.label || '';
        }
      } catch (e) {
        console.log('[SRZZ] 获取利润中心列表操作失败:', e);
      }
    },
    async query(pm = { flag: false, p: false, fy: false, initQuery: false }) {
      const { flag, p, fy, initQuery } = pm;
      console.log('pm', pm);
      if (
        this.tabValue * 1
          ? dayjs(this.formData2.ssyfz).diff(dayjs(this.formData2.ssyfq), 'month') > 2
          : dayjs(this.formData1.ssyfz).diff(dayjs(this.formData1.ssyfq), 'month') > 2
      ) {
        this.$message.warning('时间范围不能超过三个月');
        return;
      }
      if (flag) {
        console.log('默认第一页', flag);
        this.tabValue * 1 ? (this.srpagination.current = 1) : (this.pagination.current = 1);
        if (this.hzfpTabVisible) this.hzpagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: 0,
        pageSize: 0,
      };
      if (this.tabValue === '2') {
        params.pageNum = this.hzpagination.current;
        params.pageSize = this.hzpagination.pageSize;
      }
      if (this.tabValue === '1') {
        params.pageNum = this.srpagination.current;
        params.pageSize = this.srpagination.pageSize;
      }
      if (this.tabValue === '0') {
        params.pageNum = this.pagination.current;
        params.pageSize = this.pagination.pageSize;
      }
      if (p) {
        this.tabValue = p.tabValue;
        this.backFromCymx = true;
        if (p.ssyfq) {
          p.sszqq = dayjs(p.ssyfq).format('YYYYMM');
        } else if (this.tabValue * 1) {
          p.sszqq = dayjs(this.formData2.ssyfq).format('YYYYMM');
        } else {
          p.sszqq = dayjs(this.formData1.ssyfq).format('YYYYMM');
        }
        if (p.ssyfz) {
          p.sszqz = dayjs(p.ssyfz).format('YYYYMM');
        } else if (this.tabValue * 1) {
          p.sszqz = dayjs(this.formData2.ssyfz).format('YYYYMM');
        } else {
          p.sszqz = dayjs(this.formData1.ssyfz).format('YYYYMM');
        }
        params = { ...(this.tabValue * 1 ? this.formData2 : this.formData1), ...p, ...params }; // 起始时间待解决
        // this.$refs.queryControl2.setParams(p);
      } else if (this.tabValue === '2') {
        params = {
          ...this.formData3,
          sszqq: dayjs(this.formData3.sszqq).format('YYYYMM'),
          sszqz: dayjs(this.formData3.sszqz).format('YYYYMM'),
          ...params,
        };
      } else {
        const currentFormData = this.tabValue * 1 ? this.formData2 : this.formData1;
        params = {
          ...(this.tabValue * 1 ? this.formData2 : this.formData1),
          sszqq:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfq).format('YYYYMM')
              : dayjs(this.formData1.ssyfq).format('YYYYMM'),
          sszqz:
            this.tabValue * 1
              ? dayjs(this.formData2.ssyfz).format('YYYYMM')
              : dayjs(this.formData1.ssyfz).format('YYYYMM'),
          jsfsDm1: multiSelectHandle(currentFormData.jsfsDm1),
          zsxmDm1: multiSelectHandle(currentFormData.zsxmDm1),
          sl1: multiSelectHandle(currentFormData.sl1),
          srlxDm: multiSelectHandle(currentFormData.srlxDm),
          xssrkmdm: multiSelectHandle(currentFormData.xssrkmdm),
          lrzx: multiSelectHandle(currentFormData.lrzx),
          ...params,
        };
      }
      console.log('tabValue', this.tabValue);
      console.log('params', params);

      // 增加缓存判断逻辑
      if (this.loadedTabs[this.tabValue] && !pm.initQuery && !pm.flag) {
        this.tableLoading = false;
        return;
      }
      try {
        // 拆分三个tab的独立查询逻辑
        if (this.tabValue === '0') {
          if (initQuery || this.firstInit1 || this.backFromCymx) {
            // 销项科目查询逻辑...
            this.backFromCymx = false;
            let res = {};
            if (this.xgmnsrFlag) {
              console.log('小规模收入总账初始化');
              res = await initSrzzQueryInXgmnsr(params);
            } else {
              console.log('一般人收入总账初始化');
              res = await initSrzzQuery(params);
            }
            const { data } = res;
            console.log(data.records, 'data.records');
            if (data.records) {
              this.tableData1 = data.records.dataList || [];
              this.firstInit1 = false;
              // this.footData = this.tableData.length > 1 ? [this.data.records?.hj] || [] : [];
              this.tableData1.forEach((item, index) => {
                this.$set(item, 'index', index);
              });
              if (!fy) {
                if (this.tableData1.length > 0) {
                  const { data } = await initSrzzQueryHj(params);
                  this.footData1 =
                    [
                      {
                        xssr: this.numberToPrice(data?.xssr),
                        xsse: this.numberToPrice(data?.xsse),
                      },
                    ] || [];
                } else {
                  this.footData1 = [];
                }
              }
              // this.footData1 =
              //   this.tableData1.length > 1
              //     ? [
              //         {
              //           xssr: this.numberToPrice(data.records?.hj.xssr),
              //           xsse: this.numberToPrice(data.records?.hj.xsse),
              //         },
              //       ]
              //     : [];
              this.pagination.total = data.pageTotal;
            } else {
              this.tableData1 = [];
              this.footData1 = [];
              this.pagination.total = 0;
            }
            this.loadedTabs['0'] = true;
          }
        } else if (this.tabValue === '1') {
          if (initQuery || this.firstInit) {
            // 收入类型查询逻辑...
            let res = {};
            if (this.xgmnsrFlag) {
              console.log('小规模收入总账查询');
              res = await querySrzzAndSrcybdhzbInXgmnsr(params);
            } else {
              console.log('一般人收入总账查询');
              res = await querySrzzAndSrcybdhzb(params);
            }
            const { data } = res;
            console.log(data.records, 'data.records');
            if (data.records) {
              this.tableData2 = data.records.dataList || [];
              this.firstInit = false;
              this.validateRules = [];
              this.tableData2.forEach((item, index) => {
                this.$set(item, 'index', index);
                this.$set(item, 'key', this.getKey());
                if (item.ccmsg) {
                  const l = {
                    content: item.ccmsg,
                    handleMsg: '请点此查看销项税疑点明细',
                    index,
                    type: 'error',
                    djxh: item.djxh,
                    sszq: item.sszq,
                    nsrsbh: item.nsrsbh,
                    nsrmc: item.nsrmc,
                  };
                  // 2024.10.31 禅道665 差异比对去掉忽略
                  // if (item.hlzt) {
                  //   l = {
                  //     ...l,
                  //     finalContent: `，操作人员${item.hlzt.hlczry}于${dayjs(item.hlzt.hlczsj).format(
                  //       'YYYY年MM月DD日',
                  //     )}已忽略该差异。`,
                  //   };
                  // } else {
                  //   l = {
                  //     ...l,
                  //     extraContent: '，若该差异可忽略，请点击此“',
                  //     extraHandleMsg: '忽略',
                  //     finalContent: '”。',
                  //   };
                  // }
                  this.validateRules.push(l);
                }
              });
              if (!fy) {
                if (this.tableData2.length > 0) {
                  const { data } = await querySrzzAndSrcybdhzbHj(params);
                  this.footData2 =
                    [
                      {
                        xssr: this.numberToPrice(data?.xssr),
                        xxse: this.numberToPrice(data?.xxse),
                        xxskmse: this.numberToPrice(data?.xxskmse),
                        cyse: this.numberToPrice(data?.cyse),
                      },
                    ] || [];
                } else {
                  this.footData2 = [];
                }
              }
              this.srpagination.total = data.pageTotal;
              const validate = await getTzDifferences({
                tzlx: 'srzz',
                cxParam: {
                  ...params,
                },
              });
              console.log(validate);
              // this.validateRules = validate.data;
              console.log('this.validateRules', this.validateRules);
            } else {
              this.tableData2 = [];
              this.validateRules = [];
              this.footData2 = [];
              this.srpagination.total = 0;
            }
            this.loadedTabs['1'] = true;
          }
        } else if (this.tabValue === '2') {
          if (initQuery || this.firstInit2) {
            // 汇总发票查询逻辑...
            const { data } = await queryHzfp(params);
            console.log(data.records, 'data.records');
            if (data.records) {
              this.tableData3 = data.records || [];
              this.firstInit2 = false;
              this.tableData3.forEach((item, index) => {
                this.$set(item, 'index', index);
                this.$set(item, 'key', this.getKey());
              });
              const index0 = this.tableData3.findIndex((t) => t.uuid === null);
              if (data.pageTotal) {
                this.footData3 = [
                  {
                    fzjgybhwjlwxssr: this.numberToPrice(this.tableData3[index0]?.fzjgybhwjlwxssr),
                    fzjgybhwjlwjzjtxssr: this.numberToPrice(this.tableData3[index0]?.fzjgybhwjlwjzjtxssr),
                    fzjgysfwxssr: this.numberToPrice(this.tableData3[index0]?.fzjgysfwxssr),
                    fzjgysfwjzjtxssr: this.numberToPrice(this.tableData3[index0]?.fzjgysfwjzjtxssr),
                  },
                ];
              }
              this.tableData3.splice(index0, 1);
              this.hzpagination.total = data.pageTotal;
            } else {
              this.tableData3 = [];
              this.footData3 = [];
              this.hzpagination.total = 0;
            }
            this.loadedTabs['2'] = true;
          }
        }

        // if (initQuery || this.firstInit || this.tabValue === '1') {
        //   // 收入类型查询逻辑...
        // }
        // if (initQuery || this.firstInit1 || this.backFromCymx || this.tabValue === '0') {
        //   // 销项科目查询逻辑...
        // }
        // if (initQuery || this.firstInit2 || this.tabValue === '2') {
        //   // 汇总发票查询逻辑...
        // }
        jyssReadyStatusFetch(
          this.$store.state.zzstz.userInfo?.djxh || '',
          this.$store.state.zzstz.userInfo?.nsrsbh || '',
          this.tabValue * 1 ? this.formData2.ssyfq : this.formData1.ssyfq,
        );
      } catch (error) {
        console.error('[SRZZ] 查询发生异常', {
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
        });
        [this.tableData1, this.tableData2][this.tabValue * 1] = [];
        this.validateRules = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    numberToPrice,
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.myTable.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    toggle(val) {
      this.validateDialogToggle = val;
    },
    onRowMousedown(e) {
      console.log(e);
      this.activeRule = e;
    },
    rowClassName({ rowIndex, type }) {
      if (rowIndex === this.activeRule.index && type === 'body') return 'active-row';
      return '';
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
// 添加提示文字样式
.xgmnsr-tip {
  margin-left: 10px;
  font-size: 14px;
  line-height: 32px; // 与按钮高度对齐
  color: #faad14; // 使用warning主题的橙黄色
}
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
