export const querySearchConfig1 = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '销售科目',
    key: 'xssrkmdm',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '收入类型',
    key: 'srlxDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    multiple: true,
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const querySearchConfig3 = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '分支机构税号',
    key: 'fzjgnsrsbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '分支机构名称',
    key: 'fzjgnsrmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const xskmColumns = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'sszq',
    title: '所属月份',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 110,
    colKey: 'gsh2',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'xssrkmdm',
    title: '销项科目代码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 180,
    colKey: 'xssrkmmc',
    title: '销项科目名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'srlxmc',
    title: '收入类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 130,
    colKey: 'jsfsmc',
    title: '计税方式',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 80,
    colKey: 'zsxmmc',
    title: '征税项目',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    align: 'right',
    colKey: 'xssr',
    title: '销售收入',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    align: 'right',
    colKey: 'xsse',
    title: '销项税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
export const srlxColumns = [
  {
    width: 50,
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    foot: '合计',
    fixed: 'left',
  },
  {
    width: 50,
    colKey: 'xh',
    align: 'center',
    title: '序号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 110,
    colKey: 'gsh2',
    title: '公司号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 100,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 145,
    colKey: 'srlxmc',
    title: '收入类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 130,
    colKey: 'jsfsmc',
    title: '计税方式',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 80,
    colKey: 'zsxmmc',
    title: '征税项目',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    align: 'right',
    colKey: 'xssr',
    title: '销售收入',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    align: 'right',
    colKey: 'xxse',
    title: '销项税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    align: 'right',
    colKey: 'xxskmse',
    title: '销项税科目税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    align: 'right',
    colKey: 'cyse',
    title: '差异税额',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
];
export const hzfpColumns = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'ssyf',
    align: 'center',
    title: '所属月份',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 170,
    colKey: 'fzjgnsrsbh',
    title: '分支机构税号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 220,
    colKey: 'fzjgnsrmc',
    title: '分支机构名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'xssr',
    title: '销售收入',
    children: [
      {
        width: 170,
        align: 'right',
        colKey: 'fzjgybhwjlwxssr',
        title: '分支结构一般货物及劳务',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        width: 225,
        align: 'right',
        colKey: 'fzjgybhwjlwjzjtxssr',
        title: '分支结构一般货物及劳务即征即退',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        width: 140,
        align: 'right',
        colKey: 'fzjgysfwxssr',
        title: '分支机构应税服务',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
      {
        width: 190,
        align: 'right',
        colKey: 'fzjgysfwjzjtxssr',
        title: '分支机构应税服务即征即退',
        ellipsis: {
          theme: 'light',
          placement: 'bottom',
        },
      },
    ],
  },
];
