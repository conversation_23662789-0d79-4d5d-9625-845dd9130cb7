<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    header="查看参考凭证明细"
    :visible.sync="isVisible"
    @close="onClose"
    :cancelBtn="'关闭'"
    :confirmBtn="null"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <div class="queryBtns">
        <gt-space size="10px">
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
            ><DownloadIcon slot="icon" />导出</t-button
          >
        </gt-space>
      </div>
      <div>
        <t-table row-key="uuid" height="376px" hover :data="tableData" :columns="dataColumns" :loading="tableLoading">
          <!-- :pagination="pagination"
        @page-change="pageChange" -->
          <!-- <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template> -->
          <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
          <template #sl1="{ row }">
            {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
          </template>
          <template #bbje="{ row }">
            <div>
              <span>{{ numberToPrice(row.bbje) }}</span>
            </div>
          </template>
        </t-table>
      </div>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { queryFlbByckpzbh } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { downloadBlobFile } from '@/core/download';
import { DownloadIcon } from 'tdesign-icons-vue';

export default {
  components: { CssDialog, DownloadIcon },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
  },
  data() {
    return {
      userInfo: {},
      isVisible: true,
      confirmLoading: false,
      tableLoading: false,
      dcLoading: false,
      tableData: [],
      // pagination: {
      //   current: 1,
      //   pageSize: 5,
      //   total: 0,
      // },
    };
  },
  created() {
    this.query();
  },
  computed: {
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    dataColumns() {
      if (this.quanshanFlag) {
        return [
          {
            width: 50,
            align: 'center',
            colKey: 'xh',
            title: '序号',
          },
          {
            width: 210,
            align: 'left',
            colKey: 'ckpzh',
            title: '参考凭证编号',
          },
          {
            width: 210,
            align: 'left',
            colKey: 'kjpzbh',
            title: '凭证编号',
          },
          {
            width: 80,
            align: 'left',
            colKey: 'sl1',
            title: '税率',
          },
          {
            colKey: 'kmbm',
            align: 'left',
            title: '科目编码',
            width: 160,
            ellipsis: {
              theme: 'light',
              placement: 'bottom',
            },
          },
          {
            colKey: 'kmmc',
            align: 'left',
            title: '科目名称',
            width: 220,
            ellipsis: {
              theme: 'light',
              placement: 'bottom',
            },
          },
          {
            colKey: 'kjfpmc',
            align: 'left',
            title: '分配',
            width: 180,
            ellipsis: {
              theme: 'light',
              placement: 'bottom',
            },
          },
          {
            align: 'right',
            colKey: 'bbje',
            title: '本币金额',
            width: 120,
          },
        ];
      }
      return this.xgmnsrFlag
        ? [
            {
              width: 50,
              align: 'center',
              colKey: 'xh',
              title: '序号',
            },
            {
              width: 80,
              align: 'left',
              colKey: 'gsh',
              title: '公司号',
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              width: 120,
              align: 'left',
              colKey: 'lrzx',
              title: '利润中心',
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              width: 210,
              align: 'left',
              colKey: 'ckpzh',
              title: '参考凭证编号',
            },
            {
              width: 210,
              align: 'left',
              colKey: 'kjpzbh',
              title: '凭证编号',
            },
            {
              width: 80,
              align: 'left',
              colKey: 'sl1',
              title: '税率',
            },
            {
              colKey: 'kmbm',
              align: 'left',
              title: '科目编码',
              width: 160,
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              colKey: 'kmmc',
              align: 'left',
              title: '科目名称',
              width: 220,
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              align: 'right',
              colKey: 'bbje',
              title: '本币金额',
              width: 120,
            },
          ]
        : [
            {
              width: 50,
              align: 'center',
              colKey: 'xh',
              title: '序号',
            },
            {
              width: 80,
              align: 'left',
              colKey: 'gsh',
              title: '公司号',
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              width: 120,
              align: 'left',
              colKey: 'lrzx',
              title: '利润中心',
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              width: 210,
              align: 'left',
              colKey: 'ckpzh',
              title: '参考凭证编号',
            },
            {
              width: 210,
              align: 'left',
              colKey: 'kjpzbh',
              title: '凭证编号',
            },
            {
              width: 80,
              align: 'left',
              colKey: 'sl1',
              title: '税率',
            },
            {
              colKey: 'kmbm',
              align: 'left',
              title: '科目编码',
              width: 160,
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              colKey: 'kmmc',
              align: 'left',
              title: '科目名称',
              width: 220,
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              colKey: 'kjfpmc',
              align: 'left',
              title: '分配',
              width: 180,
              ellipsis: {
                theme: 'light',
                placement: 'bottom',
              },
            },
            {
              align: 'right',
              colKey: 'bbje',
              title: '本币金额',
              width: 120,
            },
          ];
    },
  },
  methods: {
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      let cxParam = {
        ckpzh: this.visible?.ckpzh || '',
        nsrsbh: this.visible?.nsrsbh || '',
      };
      if (this.quanshanFlag) {
        cxParam = {
          ...cxParam,
          sszq: this.visible?.sszq || '',
        };
      } else if (this.xgmnsrFlag) {
        cxParam = {
          ...cxParam,
          lrzx: this.visible?.lrzx || '',
          gsh2: this.visible?.gsh2 || '',
          sszqq: this.visible?.sszqq || '',
          sszqz: this.visible?.sszqz || '',
        };
      } else {
        cxParam = {
          ...cxParam,
          lrzx: this.visible?.lrzx || '',
          gsh2: this.visible?.gsh2 || '',
        };
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'flbByckpzbh',
        fileName: '参考凭证明细导出',
        cxParam,
      };
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    onClose() {
      this.isVisible = false;
    },
    async query() {
      this.tableLoading = true;
      let params = {
        ckpzh: this.visible?.ckpzh || '',
        nsrsbh: this.visible?.nsrsbh || '',
        djxh: this.visible?.djxh || '',
      };
      if (this.quanshanFlag) {
        params = {
          ...params,
          sszq: this.visible?.sszq || '',
        };
      } else if (this.xgmnsrFlag) {
        params = {
          ...params,
          lrzx: this.visible?.lrzx || '',
          gsh2: this.visible?.gsh2 || '',
          sszqq: this.visible?.sszqq || '',
          sszqz: this.visible?.sszqz || '',
        };
      } else {
        params = {
          ...params,
          lrzx: this.visible?.lrzx || '',
          gsh2: this.visible?.gsh2 || '',
        };
      }
      try {
        const { data } = await queryFlbByckpzbh(params);
        console.log('data.records', data.records);
        this.tableData = data.records || [];
        // this.pagination.total = data.pageTotal;
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    // pageChange({ current, pageSize }) {
    //   this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
    //   this.pagination.pageSize = pageSize;
    //   this.query();
    // },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/.t-input__inner {
  width: 52px !important;
}
</style>
