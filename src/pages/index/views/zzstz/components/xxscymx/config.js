import dayjs from 'dayjs';

export const querySearchConfig1 = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    visible: (params) => !params.quanshanFlag, // 新增可见性控制
  },
  {
    label: '参考凭证编号',
    key: 'ckpzh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '计税方式',
    key: 'jsfsDm1',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '税率',
    key: 'sl1',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '征税项目',
    key: 'zsxmDm1',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '参考凭证编号',
    key: 'ckpzh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    visible: (params) => !params.quanshanFlag, // 新增可见性控制
  },
];
export const tableColumns = [
  {
    width: 50,
    colKey: 'xh',
    title: '序号',
    align: 'center',
    foot: '合计',
  },
  {
    width: 80,
    colKey: 'gsh2',
    title: '公司号',
    visible: (params) => params.showGshLrzx, // 新增可见性控制
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
    visible: (params) => params.showGshLrzx, // 新增可见性控制
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 210,
    colKey: 'ckpzh',
    title: '参考凭证编号',
  },
  {
    width: 180,
    colKey: 'jsfsmc',
    title: '计税方式',
  },
  {
    width: 160,
    colKey: 'zsxmmc',
    title: '征税项目',
  },
  {
    width: 60,
    colKey: 'sl1',
    title: '税率',
  },
  {
    align: 'right',
    colKey: 'xssr',
    title: '销售收入',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'xxse',
    title: '销项税额',
    width: 120,
  },
  {
    align: 'right',
    colKey: 'xxskmse',
    title: '销项税科目税额',
    width: 140,
  },
  {
    align: 'right',
    colKey: 'cyse',
    title: '差异税额',
    width: 120,
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 120,
    foot: '-',
    fixed: 'right',
  },
];
