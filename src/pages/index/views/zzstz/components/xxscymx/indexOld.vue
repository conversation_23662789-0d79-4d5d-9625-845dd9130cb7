<!--
 * @Descripttion: 台账-增值税一般纳税人收入总账销项税差异明细
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-07-08 19:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="ggMenu">
      <div :style="{ height: '100%' }">
        <SkeletonFrame v-if="loading" />
        <div class="znsbBackGroupDiv adaption-wrap" v-else>
          <search-control-panel
            ref="queryControl"
            class="znsbHeadqueryDiv"
            :config="querySearchConfig"
            :formRules="querySearchConfigOneRules"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
            :LABEL_THRESHOLD="9"
            :labelWidth="'calc(8em + 32px)'"
          />
          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
            </gt-space>
            <t-button @click="goBackToZzstz"><RollbackIcon slot="icon" />返回台账</t-button>
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="myTable"
              row-key="uuid"
              :data="tableData"
              :columns="tableColumns"
              height="100%"
              lazyLoad
              tableLayout="auto"
              :foot-data="footData"
              :pagination="pagination"
              :loading="tableLoading"
              @page-change="pageChange"
              :horizontalScrollAffixedBottom="true"
            >
              <template #xh="{ rowIndex }">{{
                (pagination.current - 1) * pagination.pageSize + rowIndex + 1
              }}</template>
              <template #sl1="{ row }">
                {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
              </template>
              <template #xssr="{ row }">
                <div>
                  <span>{{ numberToPrice(row.xssr) }}</span>
                </div>
              </template>
              <template #xxse="{ row }">
                <div>
                  <span>{{ numberToPrice(row.xxse) }}</span>
                </div>
              </template>
              <template #xxskmse="{ row }">
                <div>
                  <span>{{ numberToPrice(row.xxskmse) }}</span>
                </div>
              </template>
              <template #cyse="{ row }">
                <div>
                  <span>{{ numberToPrice(row.cyse) }}</span>
                </div>
              </template>
              <template #operation="{ row }">
                <t-link v-show="row.ckpzh !== null" theme="primary" hover="color" @click="openCkpzmx(row)">
                  查看明细
                </t-link>
              </template>
            </t-table>
          </div>
          <MxDialog :visible.sync="mxDialogVisible" v-if="mxDialogVisible" @updateSrmx="query()" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import { getLrzx, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { querySrcybdmx, querySrcybdmxHj } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { downloadBlobFile } from '@/core/download';
import dayjs from 'dayjs';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { querySearchConfig, tableColumns } from './config.js';
import MxDialog from './components/mx-dialog.vue';

export default {
  components: {
    SkeletonFrame,
    Mybreadcrumb,
    SearchControlPanel,
    MxDialog,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    return {
      loading: true,
      userInfo: {},
      formData: {},
      querySearchConfigOneRules: {
        ssyfq: [{ required: true, message: '必填项', type: 'error' }],
      },
      djxh: '',
      nsrsbh: '',
      tableColumns,
      querySearchConfig,
      mxDialogVisible: false,
      tableLoading: false,
      dcLoading: false,
      slList: [],
      lrzxList: [],
      tableData: [],
      footData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '增值税台账', '销项税差异明细'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/tzzx/zzstz'],
      goBackPath: '/znsb/view/tzzx/zzstz', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    this.$refs.myBre.initMyBre(parmObjDhcd);
    this.init();
    this.changeLoading();
  },
  computed: {
    ssyfqC() {
      console.log('this.formData.ssyfq', this.formData.ssyfq);
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
  },
  methods: {
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 10000);
    },
    goBackToZzstz() {
      this.$router.push({
        name: 'zzstz',
        query: {
          active: 'srzz',
          tabValue: '1',
        },
      });
    },
    getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true };
      this.query(params);
    },
    openCkpzmx(row) {
      this.mxDialogVisible = {
        ckpzh: row.ckpzh,
        lrzx: row.lrzx,
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };
    },
    pageChange({ current, pageSize }) {
      console.log('pageChange', { current, pageSize });
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query({ fy: true });
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'Srcybdmx',
        fileName: '疑点明细',
        cxParam: {
          ...this.formData,
          sszq: this.ssyfqC,
          gsh2: this.$route.query.gsh,
          jsfsDm1: this.$route.query.jsfsDm,
          zsxmDm1: this.$route.query.zsxmDm,
          ...djParam,
          ccyz: this.$route.query.ccyz,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async init() {
      this.getLrzx();
      this.getSlList();
      this.query({
        flag: true,
        p: {
          sszq: String(this.$route.query.ssyfq),
          ssyfq: dayjs(String(this.$route.query.ssyfq)).format('YYYY-MM'),
          gsh2: this.$route.query.gsh,
          jsfsDm1: this.$route.query.jsfsDm,
          zsxmDm1: this.$route.query.zsxmDm,
          sl1: String(this.$route.query.sl),
          lrzx: this.$route.query.lrzx,
        },
      });
    },
    async getLrzx() {
      try {
        const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = data;
        this.querySearchConfig[3].selectList = this.lrzxList.map((d) => ({ label: d.value, value: d.value }));
      } catch (e) {
        console.log(e);
      }
    },
    async getSlList() {
      const { data } = await getJsfssldzb();
      this.slList = this.uniqueObjects(data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      const index0 = this.slList.findIndex((t) => t.value === 0);
      index0 > 1 ? this.slList.splice(index0, 1) : null;
      console.log('slList', this.slList);
      this.querySearchConfig[1].selectList = this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async query(pm = { flag: false, p: false, fy: false }) {
      const { flag, p, fy } = pm;
      if (flag) {
        console.log('默认第一页', flag);
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        console.log('p', p);
        console.log('queryControl', this.$refs.queryControl);
        if (this.$route.query.ccyz) {
          params = { ...p, ...params, ccyz: this.$route.query.ccyz };
        } else {
          params = { ...p, ...params };
        }
        this.$refs.queryControl.setParams(p);
      } else if (this.$route.query.ccyz) {
        params = {
          ...this.formData,
          sszq: this.ssyfqC,
          ...params,
          ccyz: this.$route.query.ccyz,
        };
      } else {
        params = {
          ...this.formData,
          sszq: this.ssyfqC,
          ...params,
        };
      }
      try {
        const { data } = await querySrcybdmx(params);
        console.log(data.records, 'data.records');
        if (data.records) {
          this.tableData = data.records.dataList || [];
          // this.footData = this.tableData.length > 1 ? [this.data.records?.hj] || [] : [];
          this.tableData.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
          if (!fy) {
            if (this.tableData.length > 1) {
              const { data } = await querySrcybdmxHj(params);
              this.footData =
                [
                  {
                    xssr: this.numberToPrice(data?.xssr),
                    xxse: this.numberToPrice(data?.xxse),
                    xxskmse: this.numberToPrice(data?.xxskmse),
                    cyse: this.numberToPrice(data?.cyse),
                  },
                ] || [];
            } else {
              this.footData = [];
            }
          }
          this.pagination.total = data.pageTotal;
        } else {
          this.tableData = [];
          this.footData = [];
          this.pagination.total = 0;
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.tableLoading = false;
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/.filter-btns {
  float: right;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
