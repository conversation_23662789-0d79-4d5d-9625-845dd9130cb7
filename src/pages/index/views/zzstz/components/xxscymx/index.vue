<!--
 * @Descripttion: 台账-增值税一般纳税人收入总账销项税差异明细
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-07-08 19:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
    />
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
      </gt-space>
      <!-- <t-button @click="goBackToZzstz"><RollbackIcon slot="icon" />返回台账</t-button> -->
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="myTable"
        row-key="uuid"
        hover
        :data="tableData"
        :columns="filteredColumns"
        height="100%"
        lazyLoad
        :foot-data="footData"
        :pagination="pagination"
        :loading="tableLoading"
        @page-change="pageChange"
      >
        <!-- :horizontalScrollAffixedBottom="true" -->
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #sl1="{ row }">
          {{ row.sl1 === null ? '-' : row.sl1 * 100 + '%' }}
        </template>
        <template #xssr="{ row }">
          <div>
            <span>{{ numberToPrice(row.xssr) }}</span>
          </div>
        </template>
        <template #xxse="{ row }">
          <div>
            <span>{{ numberToPrice(row.xxse) }}</span>
          </div>
        </template>
        <template #xxskmse="{ row }">
          <div>
            <span>{{ numberToPrice(row.xxskmse) }}</span>
          </div>
        </template>
        <template #cyse="{ row }">
          <div>
            <span>{{ numberToPrice(row.cyse) }}</span>
          </div>
        </template>
        <template #operation="{ row }">
          <t-link v-show="row.ckpzh !== null" theme="primary" hover="color" @click="openCkpzmx(row)"> 查看明细 </t-link>
        </template>
      </t-table>
    </div>
    <MxDialog :visible.sync="mxDialogVisible" v-if="mxDialogVisible" @updateSrmx="query()" />
  </div>
</template>
<script>
import { getAll, getLrzx, getJsfssldzb } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { querySrcybdmx, querySrcybdmxHj } from '@/pages/index/api/tzzx/zzstz/zzsybnsrsrzz.js';
import { downloadBlobFile } from '@/core/download';
import dayjs from 'dayjs';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { querySearchConfig1, querySearchConfig2, tableColumns } from './config.js';
import MxDialog from './components/mx-dialog.vue';

export default {
  components: {
    SearchControlPanel,
    MxDialog,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      formData: {},
      querySearchConfigOneRules: {
        ssyfq: [{ required: true, message: '必填项', type: 'error' }],
      },
      djxh: '',
      nsrsbh: '',
      querySearchConfig1,
      querySearchConfig2,
      fromName: false,
      ccyz: false,
      mxDialogVisible: false,
      tableLoading: false,
      dcLoading: false,
      slList: [],
      lrzxList: [],
      jsfsList: [],
      zsxm1List: [],
      tableData: [],
      footData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  created() {},
  async mounted() {
    this.getLrzx();
    this.getSlList();
    this.getJsfsZsxmList();
  },
  computed: {
    xgmnsrFlag() {
      return this.$store.state.zzstz.userInfo.nsrlx === '2';
    },
    querySearchConfig() {
      if (this.xgmnsrFlag) {
        return this.querySearchConfig2.filter(
          (item) => typeof item.visible === 'undefined' || item.visible({ quanshanFlag: this.quanshanFlag }),
        );
      }
      return this.querySearchConfig1.filter(
        (item) => typeof item.visible === 'undefined' || item.visible({ quanshanFlag: this.quanshanFlag }),
      );
    },
    dataColumns() {
      if (this.xgmnsrFlag) {
        return tableColumns.filter((item) => item.colKey !== 'jsfsmc');
      }
      return tableColumns;
    },
    ssyfC() {
      console.log('this.formData.sszq', this.formData.sszq);
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    ssyfqC() {
      console.log('this.formData.sszqq', this.formData.sszqq);
      return dayjs(this.formData.sszqq).format('YYYYMM');
    },
    ssyfzC() {
      console.log('this.formData.sszqz', this.formData.sszqz);
      return dayjs(this.formData.sszqz).format('YYYYMM');
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    tongyongguojiFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000005';
    },
    filteredColumns() {
      return this.dataColumns.filter(
        (col) => typeof col.visible === 'undefined' || col.visible({ showGshLrzx: !this.quanshanFlag }),
      );
    },
  },
  methods: {
    openCkpzmx(row) {
      this.mxDialogVisible = this.xgmnsrFlag
        ? {
            ckpzh: row.ckpzh,
            lrzx: row.lrzx,
            gsh2: row.gsh2,
            sszqq: dayjs(this.formData.sszqq).format('YYYYMM'),
            sszqz: dayjs(this.formData.sszqz).format('YYYYMM'),
            nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          }
        : {
            ckpzh: row.ckpzh,
            lrzx: row.lrzx,
            gsh2: row.gsh2,
            sszq: dayjs(this.formData.sszq).format('YYYYMM'),
            nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
            djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          };
    },
    pageChange({ current, pageSize }) {
      console.log('pageChange', { current, pageSize });
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query({ fy: true });
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const cxParam = this.xgmnsrFlag
        ? {
            ...this.formData,
            sszqq: this.ssyfqC,
            sszqz: this.ssyfzC,
            gsh2: this.$route.query.gsh,
            ...djParam,
            ccyz: this.$route.query.ccyz,
          }
        : {
            ...this.formData,
            sszq: this.ssyfC,
            gsh2: this.$route.query.gsh,
            ...djParam,
            ccyz: this.$route.query.ccyz,
          };
      if (this.xgmnsrFlag) {
        this.$delete(cxParam, 'sszq');
      } else {
        this.$delete(cxParam, 'sszqq');
        this.$delete(cxParam, 'sszqz');
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: this.xgmnsrFlag ? 'xgmnsrSrcybdmx' : 'Srcybdmx',
        fileName: '疑点明细',
        cxParam,
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    async getJsfsZsxmList() {
      try {
        const { data } = await getAll();
        this.jsfsList = data.jsfsList;
        this.zsxm1List = data.zsxmList;
        this.slList = data.slList.map((value) => ({
          value,
          label: `${(value * 100).toFixed(0)}%`,
        }));
        this.querySearchConfig1[4].selectList = this.jsfsList;
        this.querySearchConfig1[5].selectList = this.zsxm1List;
        this.querySearchConfig2[3].selectList = this.zsxm1List.slice(0, 3);
      } catch (e) {
        console.error(e);
      }
    },
    async getLrzx() {
      try {
        if (this.quanshanFlag) return; // 当quanshanFlag为true时跳过请求
        const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
        this.lrzxList = data;
        this.querySearchConfig1[1].selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        this.querySearchConfig2[5].selectList = this.lrzxList.map((d) => ({
          label: `${d.value} | ${d.label}`,
          value: d.value,
        }));
        if (this.formData.lrzx) {
          this.formData.lrzx = this.xgmnsrFlag
            ? this.querySearchConfig2[5].selectList.find((t) => t.value === this.formData.lrzx)?.label || ''
            : this.querySearchConfig1[1].selectList.find((t) => t.value === this.formData.lrzx)?.label || '';
        }
      } catch (e) {
        console.log(e);
      }
    },
    async getSlList() {
      if (!this.tongyongguojiFlag) {
        const { data } = await getJsfssldzb();
        this.slList = this.uniqueObjects(data.map((d) => ({ label: `${d.sl1 * 100}%`, value: d.sl1 })));
      }
      const index0 = this.slList.findIndex((t) => t.value === 0);
      index0 > 1 ? this.slList.splice(index0, 1) : null;
      console.log('slList', this.slList);
      this.querySearchConfig1[3].selectList = this.slList.map((d) => ({ label: d.label, value: String(d.value) }));
      this.querySearchConfig2[2].selectList = [
        { label: '5%', value: '0.05' },
        { label: '3%', value: '0.03' },
        { label: '0%', value: '0' },
      ];
    },
    uniqueObjects(arr) {
      const unique = [];
      const seen = new Set();
      arr.forEach((item) => {
        const itemStr = JSON.stringify(item);
        if (!seen.has(itemStr)) {
          seen.add(itemStr);
          unique.push(item);
        }
      });
      return unique;
    },
    async query(pm = { flag: false, p: false, fy: false }) {
      const { flag, p, from, fy } = pm;
      // 暂定只赋予一次，之后查询均返回初始进入的页面
      if (!this.fromName) {
        this.fromName = from ?? this.fromName;
      } else if (from && this.fromName !== from) {
        this.fromName = from;
      }
      if (flag) {
        console.log('默认第一页', flag);
        this.pagination.current = 1;
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        console.log('p', p);
        params = { ...p, ...params };
        if (this.xgmnsrFlag) {
          this.$refs.queryControl.setParams({
            sszqq: dayjs(String(p.sszqq)).format('YYYY-MM'),
            sszqz: dayjs(String(p.sszqz)).format('YYYY-MM'),
            lrzx: p.lrzx,
            gsh2: p.gsh2,
            ckpzh: p.ckpzh,
            sl1: p.sl1 ? String(p.sl1) : '',
            zsxmDm1: p.zsxmDm1,
          });
        } else {
          this.$refs.queryControl.setParams({
            sszq: dayjs(String(p.sszq)).format('YYYY-MM'),
            lrzx: p.lrzx,
            gsh2: p.gsh2,
            ckpzh: p.ckpzh,
            sl1: p.sl1 ? String(p.sl1) : '',
            jsfsDm1: p.jsfsDm1,
            zsxmDm1: p.zsxmDm1,
          });
        }
        this.ccyz = p?.ccyz;
      } else if (this.ccyz) {
        params = this.xgmnsrFlag
          ? {
              ...this.formData,
              ...params,
              sszqq: this.ssyfqC,
              sszqz: this.ssyfzC,
              gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
              ccyz: 'Y',
            }
          : {
              ...this.formData,
              ...params,
              sszq: this.ssyfC,
              gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
              ccyz: 'Y',
            };
      } else {
        params = this.xgmnsrFlag
          ? {
              ...this.formData,
              ...params,
              sszqq: this.ssyfqC,
              sszqz: this.ssyfzC,
              gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
            }
          : {
              ...this.formData,
              ...params,
              sszq: this.ssyfC,
              gsh2: this.$store.state.zzstz.userInfo?.qydmz || '',
            };
      }
      if (this.xgmnsrFlag) {
        this.$delete(params, 'sszq');
      } else {
        this.$delete(params, 'sszqq');
        this.$delete(params, 'sszqz');
      }
      try {
        const { data } = await querySrcybdmx(params);
        console.log(data.records, 'data.records');
        if (data.records) {
          this.tableData = data.records.dataList || [];
          // this.footData = this.tableData.length > 1 ? [this.data.records?.hj] || [] : [];
          this.tableData.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
          if (!fy) {
            if (this.tableData.length > 0) {
              const { data } = await querySrcybdmxHj(params);
              this.footData =
                [
                  {
                    xssr: this.numberToPrice(data?.xssr),
                    xxse: this.numberToPrice(data?.xxse),
                    xxskmse: this.numberToPrice(data?.xxskmse),
                    cyse: this.numberToPrice(data?.cyse),
                  },
                ] || [];
            } else {
              this.footData = [];
            }
          }
          this.pagination.total = data.pageTotal;
        } else {
          this.tableData = [];
          this.footData = [];
          this.pagination.total = 0;
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.tableLoading = false;
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/.filter-btns {
  float: right;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
