import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  skssqq: [{ required: true, message: '必填项', type: 'error' }],
  skssqz: [{ required: true, message: '必填项', type: 'error' }],
};
export const mainColumns1 = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqq',
    title: '税款所属期起',
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqz',
    title: '税款所属期止',
  },
  {
    align: 'left',
    width: 250,
    colKey: 'nsrsbh',
    title: '被汇总纳税人税号',
  },
  {
    align: 'left',
    width: 310,
    colKey: 'nsrmc',
    title: '被汇总纳税人名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 120,
    colKey: 'yhlb',
    title: '用户类别',
  },
  {
    align: 'left',
    width: 160,
    colKey: 'djxm',
    title: '抵减项目',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'bqfse',
    title: '本期发生额',
  },
];
export const mainColumns2 = [
  {
    colKey: 'xh',
    title: '序号',
    align: 'center',
    width: 50,
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqq',
    title: '税款所属期起',
  },
  {
    align: 'center',
    width: 110,
    colKey: 'skssqz',
    title: '税款所属期止',
  },
  {
    align: 'left',
    width: 180,
    colKey: 'jyxmdz',
    title: '经营地项目/房源地址',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 180,
    colKey: 'kqysssxbyglbh',
    title: '跨区域涉税事项报验管理编号/不动产权证号',
    ellipsisTitle: true,
  },
  {
    align: 'left',
    width: 200,
    colKey: 'zgswjgdm',
    title: '主管税务机关',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'left',
    width: 120,
    colKey: 'yhlbDm',
    title: '用户类别',
  },
  {
    align: 'left',
    width: 160,
    colKey: 'djxmDm',
    title: '抵减项目',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'bqfse',
    title: '本期发生额',
  },
];
export const querySearchConfig1 = [
  {
    label: '税款所属期起',
    key: 'skssqq',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    relation: 'skssqz',
    timeRange: 'start',
  },
  {
    label: '税款所属期止',
    key: 'skssqz',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'skssqq',
  },
  {
    label: '被汇总纳税人税号',
    key: 'nsrsbh',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '被汇总纳税人名称',
    key: 'nsrmc',
    type: 'input',
    value: '',
    placeholder: '请输入',
    clearable: true,
  },
];
export const querySearchConfig2 = [
  {
    label: '税款所属期起',
    key: 'skssqq',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    relation: 'skssqz',
    timeRange: 'start',
  },
  {
    label: '税款所属期止',
    key: 'skssqz',
    type: 'datepicker',
    value: dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'skssqq',
  },
  {
    label: '抵减项目',
    key: 'djxmDm',
    type: 'select',
    value: '',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
