<!--
 * @Descripttion: 台账-增值税一般纳税人进项发票明细账
-->
<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue">
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '0'"
        style="margin-top: 16px"
        ref="queryControl1"
        :formRules="querySearchConfigOneRules"
        :config="querySearchConfig1"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData1 = v)"
      />
      <search-control-panel
        class="znsbHeadqueryDiv"
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        ref="queryControl2"
        :formRules="querySearchConfigOneRules"
        :config="querySearchConfig2"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData2 = v)"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <ExtractDataButton :sszq="sszqToExtract" :readyStatus="readyStatus" @query="query" />
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => dc() },
              { content: '导出所有页', value: 2, onClick: () => dc('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="0" label="分支机构" :destroyOnHide="false" v-if="'总机构才显示'">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            hover
            :data="dataList1"
            :columns="mainColumns1"
            :pagination="pagination1"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 0)"
            :foot-data="footData"
          >
            <template #xh="{ rowIndex }">{{
              (pagination1.current - 1) * pagination1.pageSize + rowIndex + 1
            }}</template>
            <template #bqfse="{ row }">
              <span>{{ numberToPrice(row.bqfse) }}</span>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <t-tab-panel value="1" label="跨区域经营" :destroyOnHide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            row-key="uuid"
            height="100%"
            hover
            :data="dataList2"
            :columns="mainColumns2"
            :pagination="pagination2"
            :loading="tableLoading"
            @page-change="(e) => pageChange(e, 1)"
          >
            <template #xh="{ rowIndex }">{{
              (pagination2.current - 1) * pagination2.pageSize + rowIndex + 1
            }}</template>
            <template #bqfse="{ row }">
              <span>{{ numberToPrice(row.bqfse) }}</span>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getDjxm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { queryZzsyjFzjgtz, getZzsyjFzjgtzHj, queryZzsyjKqyjytz } from '@/pages/index/api/tzzx/zzstz/yjtz.js';
import { downloadBlobFile } from '@/core/download';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import {
  mainColumns1,
  mainColumns2,
  querySearchConfig1,
  querySearchConfig2,
  querySearchConfigOneRules,
} from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    this.mainColumns1 = mainColumns1;
    this.mainColumns2 = mainColumns2;
    return {
      userInfo: {},
      querySearchConfig1,
      querySearchConfig2,
      querySearchConfigOneRules,
      visible: false,
      firstInit: true,
      tabValue: '0',
      djxmList: [],
      formData1: {},
      formData2: {},
      dataList1: [],
      dataList2: [],
      footData: [],
      tableLoading: false,
      dcLoading: false,
      pagination1: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination2: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      fromName: false,
    };
  },
  created() {
    this.querySearchConfig1[0].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    this.querySearchConfig1[1].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .endOf('month')
      .format('YYYY-MM-DD');
    this.querySearchConfig2[0].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    this.querySearchConfig2[1].value = dayjs()
      .startOf('month')
      .subtract(1, 'month')
      .endOf('month')
      .format('YYYY-MM-DD');
    this.formData1.skssqq = dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.formData1.skssqz = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    this.formData2.skssqq = dayjs().startOf('month').subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
    this.formData2.skssqz = dayjs().startOf('month').subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
    if (Object.keys(this.$route.query).length) {
      console.log('url-params', this.$route.query);
      if (this.$route.query.skssqq) {
        this.querySearchConfig1[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.querySearchConfig1[1].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.querySearchConfig2[0].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.querySearchConfig2[1].value = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData1.skssqq = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData1.skssqz = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData2.skssqq = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
        this.formData2.skssqz = dayjs(this.$route.query.skssqq).format('YYYY-MM-DD');
      }
    }
  },
  async mounted() {
    this.getDjxmList();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.tabValue * 1 ? this.formData2.skssqq : this.formData1.skssqq).format('YYYYMM');
    },
    readyStatus() {
      return this.$store.state.jyss.readyStatus;
    },
  },
  methods: {
    async getDjxmList() {
      const { data } = await getDjxm();
      this.djxmList = data;
      this.querySearchConfig2[2].selectList = this.djxmList;
    },
    async query(pm = { flag: false, p: false, fy: false, initQuery: false }) {
      const { flag, p, from, fy, initQuery } = pm;
      this.fromName = from ?? this.fromName;
      // p为父页面传参
      if (flag) this.tabValue * 1 ? (this.pagination2.current = 1) : (this.pagination1.current = 1);
      this.tableLoading = true;
      let params = {
        pageNum: this.tabValue * 1 ? this.pagination2.current : this.pagination1.current,
        pageSize: this.tabValue * 1 ? this.pagination2.pageSize : this.pagination1.pageSize,
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
      };
      if (p) {
        this.tabValue = '0'; // todo p可以增加入参tabValue控制lab
        params = { ...p, ...params };
        this.$refs.queryControl1.setParams(p);
      } else {
        params = {
          ...[this.formData1, this.formData2][this.tabValue * 1],
          ...params,
        };
      }
      let res;
      try {
        if (initQuery || this.firstInit || this.tabValue === '1') {
          res = await queryZzsyjKqyjytz(params);
          const { data } = res;
          this.dataList2 = data.records || [];
          this.pagination2.total = data.pageTotal;
          this.firstInit = false;
        }
        if (initQuery || this.tabValue === '0') {
          res = await queryZzsyjFzjgtz(params);
          const { data } = res;
          this.dataList1 = data.records.dataList || [];
          // this.footData = this.tableData.length > 1 ? [this.data.records?.hj] || [] : [];
          if (!fy) {
            if (this.dataList1.length > 0) {
              const { data } = await getZzsyjFzjgtzHj(params);
              this.footData = data || [];
            } else {
              this.footData = [];
            }
          }
          this.pagination1.total = data.pageTotal;
        }
      } catch (e) {
        [this.dataList1, this.dataList2][this.tabValue * 1] = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    pageChange({ current, pageSize }, type) {
      [this.pagination1, this.pagination2][type].current =
        pageSize !== [this.pagination1, this.pagination2][type].pageSize ? 1 : current;
      [this.pagination1, this.pagination2][type].pageSize = pageSize;
      this.query({ fy: true });
    },
    async dc(isAll) {
      if (this.tabValue === '1' && !this.dataList2.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      if (this.tabValue === '0' && !this.dataList1.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: this.tabValue * 1 ? 'zzsyjKqyjyTz' : 'zzsyjFzjgTz',
        fileName: this.tabValue * 1 ? '增值税预缴跨区域经验台账' : '增值税预缴分支机构台账',
        cxParam: {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          ...(this.tabValue * 1
            ? { ...this.formData2, sl: this.formData2.sl ? this.formData2.sl : null }
            : this.formData1),
          sszqq: this.sszqqC,
          sszqz: this.sszqzC,
          pageNum: this.tabValue * 1 ? this.pagination2.current : this.pagination1.current,
          pageSize: this.tabValue * 1 ? this.pagination2.pageSize : this.pagination1.pageSize,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.filter-btns {
  float: right;
}
</style>
