import dayjs from 'dayjs';

// const quarterOfYear = require('dayjs/plugin/quarterOfYear');
// // day.js季度插件
// dayjs.extend(quarterOfYear);

export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'sszq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '企业',
    key: 'qy',
    type: 'input',
    placeholder: '请输入企业税号/企业名称',
    clearable: true,
  },
  {
    label: '',
    key: 't1',
  },
];
