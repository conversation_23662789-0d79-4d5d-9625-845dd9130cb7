<!--
 * @Descripttion: 查询统计-增值税税金计提表
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2025-07-23 16:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <div class="mainbody">
      <Mybreadcrumb ref="myBre" style="width: 100%; background-color: #fff" />
      <div class="ggMenu">
        <div class="znsbBackGroupDiv adaption-wrap">
          <search-control-panel
            ref="queryControl"
            class="znsbHeadqueryDiv"
            :config="querySearchConfig"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
            :LABEL_THRESHOLD="9"
            :labelWidth="'calc(8em + 32px)'"
          >
            <template #t1><span></span></template>
            <template #t2><span></span></template>
          </search-control-panel>

          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
            </gt-space>
          </div>
          <div class="znsbSbBodyDiv">
            <t-enhanced-table
              ref="tableRef"
              row-key="uuid"
              hover
              :data="currentPageData"
              :columns="dataColumns"
              height="100%"
              :lazyLoad="false"
              :pagination="{
                defaultCurrent: 1,
                defaultPageSize: 10,
                total: tableData.length,
                showPageSize: true,
                pageSizeOptions: [5, 10, 20, 50],
              }"
              @page-change="handlePageChange"
              :loading="tableLoading"
              :foot-data="footData"
              :tree="{
                childrenKey: 'mxDataList',
                treeNodeColumnIndex: 1,
                checkStrictly: true,
                defaultExpandAll: true,
              }"
              :treeExpandAndFoldIcon="treeExpandIcon"
            >
              <template #xh="{ row }">
                <span>{{ row.xh }}</span>
              </template>
              <template #dzfpje="{ row }">
                <span>{{
                  row.dzfpje !== null && row.dzfpje !== undefined && row.dzfpje !== '' ? numberToPrice(row.dzfpje) : ''
                }}</span>
              </template>

              <template #dzfpse="{ row }">
                <span>{{
                  row.dzfpse !== null && row.dzfpse !== undefined && row.dzfpse !== '' ? numberToPrice(row.dzfpse) : ''
                }}</span>
              </template>

              <template #wkpje="{ row }">
                <span>{{
                  row.wkpje !== null && row.wkpje !== undefined && row.wkpje !== '' ? numberToPrice(row.wkpje) : ''
                }}</span>
              </template>

              <template #wkpse="{ row }">
                <span>{{
                  row.wkpse !== null && row.wkpse !== undefined && row.wkpse !== '' ? numberToPrice(row.wkpse) : ''
                }}</span>
              </template>

              <template #hssr="{ row }">
                <span>{{
                  row.hssr !== null && row.hssr !== undefined && row.hssr !== '' ? numberToPrice(row.hssr) : ''
                }}</span>
              </template>

              <template #bhssrhj="{ row }">
                <span>{{
                  row.bhssrhj !== null && row.bhssrhj !== undefined && row.bhssrhj !== ''
                    ? numberToPrice(row.bhssrhj)
                    : ''
                }}</span>
              </template>

              <template #xxshj="{ row }">
                <span>{{
                  row.xxshj !== null && row.xxshj !== undefined && row.xxshj !== '' ? numberToPrice(row.xxshj) : ''
                }}</span>
              </template>

              <template #qcld="{ row }">
                <span>{{
                  row.qcld !== null && row.qcld !== undefined && row.qcld !== '' ? numberToPrice(row.qcld) : ''
                }}</span>
              </template>

              <template #bqjxrz="{ row }">
                <span>{{
                  row.bqjxrz !== null && row.bqjxrz !== undefined && row.bqjxrz !== '' ? numberToPrice(row.bqjxrz) : ''
                }}</span>
              </template>

              <template #bddkdjxzc="{ row }">
                <span>{{
                  row.bddkdjxzc !== null && row.bddkdjxzc !== undefined && row.bddkdjxzc !== ''
                    ? numberToPrice(row.bddkdjxzc)
                    : ''
                }}</span>
              </template>

              <template #kdkdjxzc="{ row }">
                <span>{{
                  row.kdkdjxzc !== null && row.kdkdjxzc !== undefined && row.kdkdjxzc !== ''
                    ? numberToPrice(row.kdkdjxzc)
                    : ''
                }}</span>
              </template>

              <template #qmld="{ row }">
                <span>{{
                  row.qmld !== null && row.qmld !== undefined && row.qmld !== '' ? numberToPrice(row.qmld) : ''
                }}</span>
              </template>

              <template #yjzzs="{ row }">
                <span>{{
                  row.yjzzs !== null && row.yjzzs !== undefined && row.yjzzs !== '' ? numberToPrice(row.yjzzs) : ''
                }}</span>
              </template>

              <template #cjs="{ row }">
                <span>{{
                  row.cjs !== null && row.cjs !== undefined && row.cjs !== '' ? numberToPrice(row.cjs) : ''
                }}</span>
              </template>

              <template #jyffj="{ row }">
                <span>{{
                  row.jyffj !== null && row.jyffj !== undefined && row.jyffj !== '' ? numberToPrice(row.jyffj) : ''
                }}</span>
              </template>

              <template #dfjyffj="{ row }">
                <span>{{
                  row.dfjyffj !== null && row.dfjyffj !== undefined && row.dfjyffj !== ''
                    ? numberToPrice(row.dfjyffj)
                    : ''
                }}</span>
              </template>

              <template #fjshj="{ row }">
                <span>{{
                  row.fjshj !== null && row.fjshj !== undefined && row.fjshj !== '' ? numberToPrice(row.fjshj) : ''
                }}</span>
              </template>

              <template #qcldfjskc="{ row }">
                <span>{{
                  row.qcldfjskc !== null && row.qcldfjskc !== undefined && row.qcldfjskc !== ''
                    ? numberToPrice(row.qcldfjskc)
                    : ''
                }}</span>
              </template>

              <template #qmldfjskc="{ row }">
                <span>{{
                  row.qmldfjskc !== null && row.qmldfjskc !== undefined && row.qmldfjskc !== ''
                    ? numberToPrice(row.qmldfjskc)
                    : ''
                }}</span>
              </template>
            </t-enhanced-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import { DownloadIcon, ChevronRightIcon, ChevronDownIcon } from 'tdesign-icons-vue';
import { downloadBlobFile } from '@/core/download';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import { initZzssjjtb } from '@/pages/index/api/cxtj/zzssjjtb.js';
import { querySearchConfig } from './config.js';

export default {
  components: {
    Mybreadcrumb,
    SearchControlPanel,
    DownloadIcon,
  },
  data() {
    return {
      currentRow: {},
      userInfo: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      reserveSelectedRowOnPaginate: true,
      formData: {},
      tableData: [],
      footData: [],
      currentPageData: [],
      currentList: [],
      pagination: {},
    };
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '增值税税金计提表'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      xlxShow: false,
      isxlxShow: false,
    };
    this.$refs.myBre.initMyBre(parmObjDhcd);
    this.getInitQueryData();
    this.query();
  },
  computed: {
    sszqC() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
    dataColumns() {
      return [
        {
          title: '序号',
          colKey: 'xh',
          align: 'center',
          width: 60,
          fixed: 'left',
          foot: '合计',
        },
        {
          title: '企业税号',
          colKey: 'nsrsbh',
          width: 200,
          fixed: 'left',
          ellipsis: {
            theme: 'light',
            placement: 'bottom',
          },
        },
        {
          title: '企业名称',
          colKey: 'nsrmc',
          width: 320,
          fixed: 'left',
          ellipsis: {
            theme: 'light',
            placement: 'bottom',
          },
        },
        {
          title: '增值税税率',
          colKey: 'sl1',
          align: 'right',
          width: 100,
        },
        {
          title: '电子发票金额（月报表）',
          colKey: 'dzfpje',
          align: 'right',
          width: 180,
        },
        {
          title: '电子发票税额（月报表）',
          colKey: 'dzfpse',
          align: 'right',
          width: 180,
        },
        {
          title: '未开金额',
          colKey: 'wkpje',
          align: 'right',
          width: 180,
        },
        {
          title: '未开票税额',
          colKey: 'wkpse',
          align: 'right',
          width: 180,
        },
        {
          title: '含税收入',
          colKey: 'hssr',
          align: 'right',
          width: 180,
        },
        {
          title: '不含税收入合计',
          colKey: 'bhssrhj',
          align: 'right',
          width: 180,
        },
        {
          title: '销项税合计',
          colKey: 'xxshj',
          align: 'right',
          width: 180,
        },
        {
          title: '期初留抵',
          colKey: 'qcld',
          align: 'right',
          width: 180,
        },
        {
          title: '本期进项认证',
          colKey: 'bqjxrz',
          align: 'right',
          width: 180,
        },
        {
          title: '不得抵扣的进项转出',
          colKey: 'bddkdjxzc',
          align: 'right',
          width: 180,
        },
        {
          title: '可抵扣的进项转出',
          colKey: 'kdkdjxzc',
          align: 'right',
          width: 180,
        },
        {
          title: '期末留抵',
          colKey: 'qmld',
          align: 'right',
          width: 180,
        },
        {
          title: '应交增值税',
          colKey: 'yjzzs',
          align: 'right',
          width: 180,
        },
        {
          title: '城建税',
          colKey: 'cjs',
          align: 'right',
          width: 180,
        },
        {
          title: '教育费附加',
          colKey: 'jyffj',
          align: 'right',
          width: 180,
        },
        {
          title: '地方教育费附加',
          colKey: 'dfjyffj',
          align: 'right',
          width: 180,
        },
        {
          title: '附加税合计',
          colKey: 'fjshj',
          align: 'right',
          width: 180,
        },
        {
          title: '期初留底附加税扣除',
          colKey: 'qcldfjskc',
          align: 'right',
          width: 180,
        },
        {
          title: '期末留底附加税扣除',
          colKey: 'qmldfjskc',
          align: 'right',
          width: 180,
        },
      ];
    },
    // 可以使用同名插槽代替渲染函数：<template #treeExpandAndFoldIcon><icon /></template>
    treeExpandIcon() {
      return this.treeExpandAndFoldIconRender;
    },
  },
  methods: {
    handlePageChange(pageInfo, newData) {
      console.log('page-change:', pageInfo, newData);
      const start = (pageInfo.current - 1) * pageInfo.pageSize;
      const end = start + pageInfo.pageSize;
      this.$set(this.pagination, 'pageSize', pageInfo.pageSize);
      this.currentPageData = this.tableData.slice(start, end);
    },
    getInitQueryData() {
      this.formData.sszq = dayjs().subtract(1, 'month').format('YYYY-MM');
    },
    resetQueryParams() {
      this.$refs.queryControl.onReset();
    },
    treeExpandAndFoldIconRender(h, { type }) {
      return type === 'expand' ? <ChevronRightIcon /> : <ChevronDownIcon />;
    },
    async query() {
      this.tableLoading = true;
      const params = {
        ...this.formData,
        sszq: this.sszqC,
      };
      try {
        const { data } = await initZzssjjtb(params);
        const tempData = data || [];

        // 创建合计行对象
        const footerRow = {};
        // 需要合计的字段列表（从第5列开始的所有数值列）
        const sumColumns = [
          'dzfpje',
          'dzfpse',
          'wkpje',
          'wkpse',
          'hssr',
          'bhssrhj',
          'xxshj',
          'qcld',
          'bqjxrz',
          'bddkdjxzc',
          'kdkdjxzc',
          'qmld',
          'yjzzs',
          'cjs',
          'jyffj',
          'dfjyffj',
          'fjshj',
          'qcldfjskc',
          'qmldfjskc',
        ];
        // 初始化合计值
        sumColumns.forEach((col) => {
          footerRow[col] = 0;
        });
        tempData.forEach((item, index) => {
          // 只给父节点添加序号
          this.$set(item, 'xh', index + 1);
          // 如果有子节点，子节点不设置序号或设置为空
          if (item.mxDataList && item.mxDataList.length > 0) {
            item.mxDataList.forEach((child) => {
              this.$set(child, 'xh', ''); // 子节点序号设为空
            });
          }
          sumColumns.forEach((col) => {
            const value = parseFloat(item[col]) || 0;
            console.log('value:', value);
            console.log('footerRow[col]:', footerRow[col]);
            footerRow[col] += value;
          });
        });
        console.log('footerRow:', footerRow);
        // 格式化合计数据
        sumColumns.forEach((col) => {
          footerRow[col] = this.numberToPrice(footerRow[col]);
        });
        if (tempData.length > 0) {
          this.footData = [footerRow];
          console.log('footData:', this.footData);
        } else {
          this.footData = [];
        }
        this.tableData = tempData;
        // 初始化时加载第一页数据
        this.handlePageChange({
          current: 1,
          pageSize: 10,
        });
      } catch (e) {
        console.error(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        ...this.formData,
        sszq: this.sszqC,
      };
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'zzssjjtb',
        fileName: '增值税税金计提表',
        cxParam: {
          ...this.formData,
          sbsjq: this.ssyfqC,
          sbsjz: this.ssyfzC,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    numberToPrice,
  },
};
</script>
<style scoped lang="less">
@import '../../../styles/dialog.less';
@import '../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
/deep/.filter-btns {
  float: right;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
