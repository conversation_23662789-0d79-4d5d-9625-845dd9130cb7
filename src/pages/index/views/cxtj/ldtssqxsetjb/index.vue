<!--
 * @Descripttion: 查询统计-留抵退税申请销售额统计表
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2025-07-30 09:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <div class="mainbody">
      <Mybreadcrumb ref="myBre" style="width: 100%; background-color: #fff" />
      <div class="ggMenu">
        <div class="znsbBackGroupDiv adaption-wrap">
          <search-control-panel
            ref="queryControl"
            class="znsbHeadqueryDiv"
            :config="querySearchConfig"
            :formRules="querySearchConfigOneRules"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
            :LABEL_THRESHOLD="9"
            :labelWidth="'calc(8em + 32px)'"
          >
          </search-control-panel>
          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
            </gt-space>
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="myTable"
              row-key="uuid"
              hover
              :data="tableData"
              :columns="dataColumns"
              height="100%"
              lazyLoad
              :pagination="pagination"
              :loading="tableLoading"
              :foot-data="footData"
              @page-change="pageChange"
            >
              <template #xh="{ rowIndex }">{{
                (pagination.current - 1) * pagination.pageSize + rowIndex + 1
              }}</template>
            </t-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { downloadBlobFile } from '@/core/download';
import { numberToPrice } from '@/utils/numberToCurrency';
import { queryLdtssqxsetjb } from '@/pages/index/api/cxtj/ldtssqxsetjb.js';
import { DownloadIcon } from 'tdesign-icons-vue';
import { querySearchConfig, dataColumns, querySearchConfigOneRules } from './config.js';

export default {
  components: {
    Mybreadcrumb,
    SearchControlPanel,
    DownloadIcon,
  },
  data() {
    return {
      userInfo: {},
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      fromName: false,
      dataColumns,
      querySearchConfigOneRules,
      tableData: [],
      footData: [],
      // 企业列表
      companyListoption: [],
      companyNsrsbhList: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    sssqqC() {
      return dayjs(this.formData.sssqq).format('YYYYMM');
    },
    sssqzC() {
      return dayjs(this.formData.sssqz).format('YYYYMM');
    },
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    const companyList = JSON.parse(window.sessionStorage.getItem('companyList'));

    if (companyList) {
      companyList.forEach((item) => {
        this.companyListoption.push({ value: item.nsrsbh, label: item.jgmc });
        this.companyNsrsbhList.push(item.nsrsbh);
      });
    }
    this.companyListoption.unshift({ value: 'all', label: '全部管辖企业' });
    this.querySearchConfig[0].selectList = this.companyListoption;
    this.querySearchConfig[0].value = ['all'];
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '留抵退税申请销售额统计表'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      xlxShow: false,
      isxlxShow: false,
    };
    this.$refs.myBre.initMyBre(parmObjDhcd);
    this.getInitQueryData();
    this.query();
  },
  methods: {
    getInitQueryData() {
      this.formData.sssqq = dayjs().subtract(1, 'month').format('YYYY-MM');
      this.formData.sssqz = dayjs().subtract(1, 'month').format('YYYY-MM');
    },
    resetQueryParams() {
      this.$refs.queryControl.onReset();
    },
    async query(pm = { flag: false, p: false }) {
      const { flag } = pm; // 默认无需回到第一页，无父传参
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      // 不变的基础入参
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '100',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      // 添加优化处理：当qyList包含'all'时，传空数组表示查询所有
      const queryData = { ...this.formData };
      if (queryData.qyList && queryData.qyList.includes('all')) {
        queryData.qyList = [];
      }
      params = { ...queryData, sssqq: this.sssqqC, sssqz: this.sssqzC, ...params }; // 没有父传参的话,传查询条件+基础入参.查询框sszq绑定为YYYY-MM，需要使用sszqC覆盖
      try {
        const { data } = await queryLdtssqxsetjb(params); // 不建议api.  建议使用接口真实尾缀，方便定位代码。
        this.tableData = data?.records.tableData || [];

        // 表格长度大于1时展示表尾总计行。
        if (this.tableData.length > 0) {
          const hj = data?.records.footData || {};
          console.log('hj', hj);
          Object.keys(hj).forEach((key) => {
            hj[key] = numberToPrice(hj[key]);
          });
          this.footData = [hj];
        } else {
          this.footData = [];
        }
        this.pagination.total = data?.pageTotal || 0;
      } catch (e) {
        console.log(e);
        this.tableData = [];
        this.footData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      // 添加优化处理：当qyList包含'all'时，传空数组表示查询所有
      const queryData = { ...this.formData };
      if (queryData.qyList && queryData.qyList.includes('all')) {
        queryData.qyList = [];
      }
      const params = {
        yzpzzlDm: 'BDA0610606',
        tzlx: 'ldtssqxsetjb',
        fileName: '留抵退税申请销售额统计表',
        cxParam: {
          ...queryData,
          sssqq: this.sssqqC,
          sssqz: this.sssqzC,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../styles/sbPageGy.less';
/deep/.filter-btns {
  float: right;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
</style>
