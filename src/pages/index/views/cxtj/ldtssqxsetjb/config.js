import dayjs from 'dayjs';
import { numberToPrice } from '@/utils/numberToCurrency';

export const querySearchConfig = [
  {
    label: '企业名称',
    key: 'qyList',
    type: 'select',
    value: [],
    selectList: [],
    placeholder: '请选择',
    clearable: true,
    multiple: true,
    filterable: true,
    minCollapsedNum: 1,
    max: 5,
  },
  {
    label: '所属时期起',
    key: 'sssqq',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    relation: 'sssqz',
    timeRange: 'start',
  },
  {
    label: '所属时期止',
    key: 'sssqz',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sssqq',
  },
];

export const querySearchConfigOneRules = {};

export const dataColumns = [
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
    fixed: 'left',
  },
  {
    colKey: 'nsrsbh',
    title: '企业税号',
    width: 180,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'nsrmc',
    title: '企业名称',
    width: 320,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    colKey: 'tdhyxse',
    title: '特定行业销售额',
    align: 'right',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.tdhyxse)}</div>,
  },
  {
    colKey: 'tqqbxse',
    title: '同期全部销售额',
    align: 'right',
    width: 200,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    cell: (h, { row }) => <div>{numberToPrice(row.tqqbxse)}</div>,
  },
  {
    colKey: 'zb',
    title: '占比',
    align: 'right',
    width: 140,
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
    // 前端计算处理小数存在精度问题，改成后端计算返回前端直接展示
    // cell: (h, { row }) => <div>{row.zb === null ? '-' : `${row.zb * 100}%`}</div>,
  },
];
