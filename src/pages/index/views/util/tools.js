// eslint-disable-next-line no-unused-vars
import { MessagePlugin } from 'tdesign-vue';
import { validValueRes } from '@gt4/common-front';

export default {
  stringToArray(_mKeys) {
    let mKeys = _mKeys;
    if (mKeys) {
      if (mKeys.indexOf(',') !== -1) {
        mKeys = mKeys.split(',');
      } else {
        mKeys = [mKeys];
      }
    } else {
      mKeys = [];
    }
    return mKeys;
  },
  mark(mes = '未实现') {
    // MessagePlugin.warning(`未实现:${mes}`);
    console.log(`未实现:${mes}`);
  },
  randomId(count = 16) {
    return Math.random().toString(count).substr(2);
  },
  optionAllowEmpty(arr, o = { label: '请选择', value: '' }) {
    return [o, ...arr];
  },
  clone(data) {
    return JSON.parse(JSON.stringify(data));
  },
  getSame(arr, mKeys = '') {
    const o = {};
    const sameArr = [];

    arr.forEach((item) => {
      const targetmKeys = mKeys ? item[mKeys] : item;
      if (!o[targetmKeys]) {
        o[targetmKeys] = true;
      } else {
        sameArr.push(item);
      }
    });

    return sameArr;
  },
  checkJwRule(arr, rules, formData, checkNull = false) {
    let isPass = true;
    let mes = '';
    const needCheckArr = [];

    arr.forEach((mKeys) => {
      if (!isPass) return;
      if (formData[mKeys]) {
        const jwd = formData[mKeys];
        if (jwd && Number.isNaN(Number(jwd))) {
          isPass = false;
          mes = '格式错误';
        } else if (rules[mKeys]) {
          if (rules[mKeys][0] - Number(jwd) > 0 || rules[mKeys][1] - Number(jwd) <= 0) {
            isPass = false;
            mes = `范围应大于${rules[mKeys][0]}且小于${rules[mKeys][1]}`;
          }
        }
      } else {
        needCheckArr.push(true);
      }
    });

    if (isPass && needCheckArr.length !== 0 && needCheckArr.length !== arr.length) {
      mes = '请填写完整';
      isPass = false;
    }
    if (isPass && checkNull) {
      if (checkNull && needCheckArr.length === arr.length) {
        mes = '必录';
        isPass = false;
      }
    }
    return { isPass, message: mes };
  },
  getTotal(arr, mKeys) {
    let total = 0;
    arr.forEach((item) => {
      total += Number(item[mKeys]);
    });
    if (total === 0) return 0;
    return this.nonnegative(total);
  },
  nonnegative(value, digit = 2) {
    if (!value) return '';
    const s = validValueRes(value, { type: 'nonnegative', digit });
    return s.value;
  },
  changeToObj(arr, name = 'value') {
    const o = {};
    if (!arr || !arr.forEach) return {};
    arr.forEach(function (item) {
      o[`${item[name]}`] = item;
    });
    return o;
  },
};
