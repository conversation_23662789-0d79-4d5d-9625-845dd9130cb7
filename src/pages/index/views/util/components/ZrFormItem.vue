<script lang="jsx">
// eslint-disable-next-line no-unused-vars
import { InputMoney } from '@gt4/common-front';
import { FormItem } from 'tdesign-vue';

export default {
  extends: FormItem,
  computed: {
    extraNode: function extraNode() {
      const h = this.$createElement;
      const list = this.errorList;
      // eslint-disable-next-line no-unused-vars
      const self = this;

      // 根据不同组件去算tip的 left以及right
      // target里面的$children

      if (this.needErrorMessage && list && list[0] && list[0].message) {
        return (
          <t-popup content={list[0].message} showArrow class={`${this.classPrefix}-input__extra`}>
            <t-icon class="zr-form-item-icon icon-right" name="error-circle-filled"></t-icon>
          </t-popup>
        );
      }

      if (this.successList.length) {
        return h(
          'div',
          {
            class: ''.concat(this.classPrefix, '-input__extra'),
          },
          [this.successList[0].message],
        );
      }

      return null;
    },
  },
};
</script>
<style lang="less" scoped>
.zr-form-item-icon {
  position: absolute;
  top: 5px;
  right: 8px;
  z-index: 30;
  width: 15px;
  height: 15px;
  line-height: 44px;
  cursor: pointer;
}
</style>
