import {
  sfysb,
  isYsApi,
  isProductApi,
  computeSszqApi,
  jyssReadyStatusApi,
  yhsJyssReadyStatusApi,
  fcsJyssReadyStatusApi,
  qysdsyjJyssReadyStatusApi,
  companyDifferentiationConfigApi,
} from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import store from '@/pages/index/store';
import dayjs from 'dayjs';

let intervalId = null; // 存储 setInterval 的 ID
let yhsIntervalId = null; // 存储 setInterval 的 ID
let currentDjxh = null;
let currentNsrsbh = null;
let currentSszq = null;
// 添加全局变量存储回调函数
let jyssCallback = null;
let yhsCallback = null;
let fcsCallback = null;
let qysdsyjCallback = null;

export function multiSelectHandle(multiSelectData) {
  if (multiSelectData) {
    if (multiSelectData instanceof Array) {
      return multiSelectData.join(',');
    }
    return multiSelectData;
  }
  return null;
}

export function isProductEnv() {
  isProductApi().then((res) => {
    store.commit('isProduct/setEnvValue', res.data !== 'SC');
  });
}

export function isYsEnv() {
  isYsApi().then((res) => {
    store.commit('isYs/setValue', res.data === 'Y');
  });
}

// 触发调用企业差异化配置信息
export function getCompanyDifferentiationConfig(params) {
  return new Promise((resolve, reject) => {
    try {
      companyDifferentiationConfigApi(params)
        .then((res) => {
          console.log('fieldNameTransformApi - res', res);
          store.commit('zdmczh/setCompanyDifferentiationConfig', res.data);
          // 临时方法，当对象企业为山西移动时，将isProduct/setEnvValue设为false
          if (res.data.jtbm === '000003') {
            store.commit('isProduct/setEnvValue', false);
          } else {
            isProductApi().then((res) => {
              store.commit('isProduct/setEnvValue', res.data !== 'SC');
            });
          }
          resolve(res); // 确保resolve被调用
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
          reject(error);
        });
    } catch (error) {
      console.error('Error in getCompanyDifferentiationConfig:', error);
      reject(error);
    }
  });
}

export function initComputeSszq() {
  computeSszqApi().then((res) => {
    sessionStorage.setItem('sszqDays', res.data);
  });
}

export function computeSszq() {
  // 获取当前日期
  const currentDate = new Date();
  // 获取当前日期的天部分
  const currentDay = currentDate.getDate();

  const sszqDays = sessionStorage.getItem('sszqDays');

  let days = 100;
  if (sszqDays) {
    if (sszqDays !== 'default') {
      days = Number(sszqDays);
    }
  }
  // 判断当前日期是否为配置日期之后的日期
  const isAfter25th = currentDay >= days;
  if (isAfter25th) {
    return dayjs().format('YYYY-MM');
  }
  return dayjs().subtract(1, 'month').format('YYYY-MM');
}

// 开始轮询
export function startPolling() {
  if (intervalId !== null) {
    console.warn('Polling is already started.');
    return;
  }

  // eslint-disable-next-line no-use-before-define
  intervalId = setInterval(jyssReadyStatusExecute, 5000); // 每 5 秒轮询一次
}
// 停止轮询
export function stopPolling(callback, context) {
  if (intervalId === null) return;
  clearInterval(intervalId);
  intervalId = null;
  if (callback) {
    callback.call(context); // 确保执行回调
  }
}

// 从后端接口获取数据
export function jyssReadyStatusFetch(djxh, nsrsbh, sszq, callback) {
  console.log('jyssReadyStatusFetch', djxh, nsrsbh, sszq, typeof callback);
  // 保存回调函数
  if (callback && typeof callback === 'function') {
    jyssCallback = callback;
  }

  return new Promise((resolve, reject) => {
    currentDjxh = djxh;
    currentNsrsbh = nsrsbh;
    currentSszq = dayjs(sszq).format('YYYYMM');
    try {
      const params = { djxh, nsrsbh, sszq: currentSszq };
      jyssReadyStatusApi(params)
        .then((res) => {
          const jyssReadyStatus = res.data !== 'unReady';
          store.commit('jyss/setReadyStatus', jyssReadyStatus);
          resolve(jyssReadyStatus); // 返回准备状态
          if (!jyssReadyStatus) {
            startPolling();
          } else if (jyssCallback && typeof jyssCallback === 'function') {
            console.log('轮询完成，准备状态已改变', typeof jyssCallback);
            jyssCallback();
            jyssCallback = null; // 清除回调函数
          }
        })
        .catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}

export function jyssReadyStatusExecute() {
  return new Promise((resolve, reject) => {
    try {
      const params = {
        djxh: currentDjxh,
        nsrsbh: currentNsrsbh,
        sszq: currentSszq,
      };
      jyssReadyStatusApi(params)
        .then((res) => {
          const jyssReadyStatus = res.data !== 'unReady';
          store.commit('jyss/setReadyStatus', jyssReadyStatus);
          resolve(); // 现在在 Promise 上下文中
          if (!jyssReadyStatus) {
            startPolling();
          } else {
            // 轮询完成时执行回调
            if (jyssCallback && typeof jyssCallback === 'function') {
              console.log('轮询完成，执行回调');
              jyssCallback();
              jyssCallback = null; // 清除回调函数
            }
            stopPolling(null, null);
          }
        })
        .catch(reject);
    } catch (error) {
      console.error('Error fetching data:', error);
      reject(error);
    }
  });
}

// 印花税轮询
// 开始轮询
export function startPollingYhs(params) {
  if (yhsIntervalId !== null) {
    console.warn('Polling is already started.');
    return;
  }

  // eslint-disable-next-line no-use-before-define
  yhsIntervalId = setInterval(() => yhsJyssReadyStatusExecute(params), 5000); // 每 5 秒轮询一次
}
// 停止轮询
export function stopPollingYhs(callback) {
  if (yhsIntervalId === null) {
    console.warn('Polling is not started.');
    return;
  }
  currentDjxh = null;
  currentNsrsbh = null;
  currentSszq = null;
  clearInterval(yhsIntervalId);
  yhsIntervalId = null;
  if (callback && typeof callback === 'function') {
    callback();
  }
}
// 从后端接口获取数据
export function yhsJyssReadyStatusFetch(djxh, gsh, nsrsbh, sszq, ywlx, callback) {
  // 保存回调函数
  if (callback && typeof callback === 'function') {
    yhsCallback = callback;
  }

  currentDjxh = djxh;
  currentNsrsbh = nsrsbh;
  currentSszq = dayjs(sszq).format('YYYYMM');
  try {
    const params = {
      djxh: currentDjxh,
      gsh,
      nsrsbh: currentNsrsbh,
      sszq: currentSszq,
      ywlx,
    };
    yhsJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setYhsReadyStatus', jyssReadyStatus);
      if (!jyssReadyStatus) {
        // 需要先停止再启动，防止重复轮询
        stopPollingYhs();
        startPollingYhs(params);
      } else {
        stopPollingYhs(() => {
          if (yhsCallback && typeof yhsCallback === 'function') {
            yhsCallback();
            yhsCallback = null; // 清除回调函数
          }
        });
      }
    });
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

export function yhsJyssReadyStatusExecute(params) {
  try {
    yhsJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setYhsReadyStatus', jyssReadyStatus);
      if (!jyssReadyStatus) {
        startPollingYhs(params);
      } else {
        stopPollingYhs(() => {
          if (yhsCallback && typeof yhsCallback === 'function') {
            yhsCallback();
            yhsCallback = null; // 清除回调函数
          }
        });
      }
    });
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

// 停止轮询
export function stopPollingFcs(callback) {
  if (intervalId === null) {
    console.warn('Polling is not started.');
    return;
  }
  clearInterval(intervalId);
  intervalId = null;
  if (callback && typeof callback === 'function') {
    callback();
  }
}

// 开始轮询
export function startPollingFcs(params) {
  if (intervalId !== null) {
    stopPollingFcs(); // 先停止已有轮询
  }

  intervalId = setInterval(() => {
    fcsJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setFcsReadyStatus', jyssReadyStatus);
      if (jyssReadyStatus) {
        stopPollingFcs();
      }
    });
  }, 5000); // 直接在此处理轮询逻辑
}

export function fcsJyssReadyStatusFetch(params, callback) {
  // 保存回调函数
  if (callback && typeof callback === 'function') {
    fcsCallback = callback;
  }

  fcsJyssReadyStatusApi(params).then((res) => {
    const jyssReadyStatus = res.data !== 'unReady';
    store.commit('jyss/setFcsReadyStatus', jyssReadyStatus);
    if (!jyssReadyStatus) {
      startPollingFcs(params);
    } else {
      stopPollingFcs(() => {
        if (fcsCallback && typeof fcsCallback === 'function') {
          fcsCallback();
          fcsCallback = null; // 清除回调函数
        }
      });
    }
  });
}

export function fcsJyssReadyStatusExecute(params) {
  try {
    fcsJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setFcsReadyStatus', jyssReadyStatus);
      if (!jyssReadyStatus) {
        startPollingFcs(params);
      } else {
        stopPollingFcs(() => {
          if (fcsCallback && typeof fcsCallback === 'function') {
            fcsCallback();
            fcsCallback = null; // 清除回调函数
          }
        });
      }
    });
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

// 停止轮询
export function stopPollingQysdsyj(callback) {
  if (intervalId === null) {
    console.warn('Polling is not started.');
    return;
  }
  clearInterval(intervalId);
  intervalId = null;
  if (callback && typeof callback === 'function') {
    callback();
  }
}

// 开始轮询
export function startPollingQysdsyj(params) {
  if (intervalId !== null) {
    stopPollingQysdsyj(); // 先停止已有轮询
  }

  intervalId = setInterval(() => {
    qysdsyjJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setQysdsyjReadyStatus', jyssReadyStatus);
      if (jyssReadyStatus) {
        stopPollingQysdsyj();
      }
    });
  }, 5000); // 直接在此处理轮询逻辑
}

export function qysdsyjJyssReadyStatusFetch(params, callback) {
  // 保存回调函数
  if (callback && typeof callback === 'function') {
    qysdsyjCallback = callback;
  }

  qysdsyjJyssReadyStatusApi(params).then((res) => {
    const jyssReadyStatus = res.data !== 'unReady';
    store.commit('jyss/setQysdsyjReadyStatus', jyssReadyStatus);
    if (!jyssReadyStatus) {
      startPollingQysdsyj(params);
    } else {
      stopPollingQysdsyj(() => {
        if (qysdsyjCallback && typeof qysdsyjCallback === 'function') {
          qysdsyjCallback();
          qysdsyjCallback = null; // 清除回调函数
        }
      });
    }
  });
}

export function qysdsyjJyssReadyStatusExecute(params) {
  try {
    qysdsyjJyssReadyStatusApi(params).then((res) => {
      const jyssReadyStatus = res.data !== 'unReady';
      store.commit('jyss/setQysdsyjReadyStatus', jyssReadyStatus);
      if (!jyssReadyStatus) {
        startPollingQysdsyj(params);
      } else {
        stopPollingQysdsyj(() => {
          if (qysdsyjCallback && typeof qysdsyjCallback === 'function') {
            qysdsyjCallback();
            qysdsyjCallback = null; // 清除回调函数
          }
        });
      }
    });
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

export async function getSbztBySsq(sszq, yzpzzlDm = 'BDA0610606') {
  const cacheKey = String(dayjs(sszq).format('YYYYMM')); // 显式转为字符串
  // console.log('store.state.sbzt.sbztCacheInfo', store.state.sbzt.sbztCacheInfo);

  // 检查缓存是否存在
  // if (store.state.sbzt.sbztCacheInfo?.[cacheKey] !== undefined) {
  //   return store.state.sbzt.sbztCacheInfo[cacheKey];
  // }

  // 无缓存时发起请求并更新缓存
  try {
    // 发起异步请求
    const { data } = await sfysb({
      djxh: store.state.zzstz.userInfo?.djxh || '',
      yzpzzlDm,
      sszq: cacheKey,
    });

    const result = data === 'YSB';
    // 更新缓存
    // store.commit('sbzt/setSbztCacheInfo', {
    //   [cacheKey]: result,
    // });

    return result;
  } catch (error) {
    console.error('申报状态查询失败:', error);
    return false;
  }
}

export async function getSbztBySsqqz(sszqq, sszqz, yzpzzlDm = 'BDA0610606') {
  const cacheKeySszqq = String(dayjs(sszqq).format('YYYY-MM'));
  const cacheKeySszqz = String(dayjs(sszqz).format('YYYY-MM'));
  // console.log('getSbztBySsqqz', cacheKeySszqq, cacheKeySszqz);
  // const cacheKey = cacheKeySszqq.concat('-').concat(cacheKeySszqz);
  // console.log('store.state.sbzt.sbztCacheInfo', store.state.sbzt.sbztCacheInfo);

  // // 检查缓存是否存在
  // if (store.state.sbzt.sbztCacheInfo?.[cacheKey] !== undefined) {
  //   return store.state.sbzt.sbztCacheInfo[cacheKey];
  // }

  // 无缓存时发起请求并更新缓存
  try {
    const { data } = await sfysb({
      djxh: store.state.zzstz.userInfo?.djxh || '',
      yzpzzlDm,
      sszqq: cacheKeySszqq,
      sszqz: cacheKeySszqz,
    });

    const result = data === 'YSB';
    // 更新缓存
    // store.commit('sbzt/setSbztCacheInfo', {
    //   [cacheKey]: result,
    // });

    return result;
  } catch (error) {
    console.error('申报状态查询失败:', error);
    return false;
  }
}
