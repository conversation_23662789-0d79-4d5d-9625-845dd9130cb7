export default {
  getter(key) {
    let data = localStorage.getItem(key);
    if (data[0] === '{' || data[0] === '[') {
      data = JSON.parse(data);
    }
    return data;
  },
  setter(key, data) {
    let saveData;
    if (typeof data === 'object') {
      saveData = JSON.stringify(data);
    } else {
      saveData = data;
    }
    localStorage.setItem(key, saveData);
  },
  getTarget(key, taget) {
    let data = localStorage.getItem(taget);
    data = JSON.parse(data);
    if (!data) data = {};
    data = data[key];
    if (!data) return data;
    if (data[0] === '{' || data[0] === '[') {
      data = JSON.parse(data);
    }
    return data;
  },
  setTaget(key, target, data) {
    let saveData;
    if (typeof data === 'object') {
      saveData = JSON.stringify(data);
    } else {
      saveData = data;
    }
    let targetData = localStorage.getItem(target);
    if (!targetData) {
      targetData = {};
    } else {
      targetData = JSON.parse(targetData);
    }
    targetData[key] = saveData;
    localStorage.setItem(target, JSON.stringify(targetData));
  },
};
