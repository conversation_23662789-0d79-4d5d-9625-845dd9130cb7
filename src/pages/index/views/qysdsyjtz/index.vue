<!--
 * @Descripttion: 台账-城镇土地使用税总账
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-07-26 14:45:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="ggMenu">
      <div :style="{ height: '100%' }">
        <SkeletonFrame v-if="loading" />
        <div class="znsbBackGroupDiv adaption-wrap" v-else>
          <search-control-panel
            ref="queryControl"
            class="znsbHeadqueryDiv"
            :config="querySearchConfig"
            :formRules="querySearchConfigOneRules"
            @search="query({ flag: true })"
            :colNum="4"
            @formChange="(v) => (formData = v)"
            :LABEL_THRESHOLD="9"
            :labelWidth="'calc(8em + 32px)'"
          >
            <template #t2><span></span></template>
          </search-control-panel>

          <div class="queryBtns" style="display: flex; justify-content: space-between">
            <gt-space size="10px">
              <t-button theme="primary" @click="query({ flag: true })"
                ><CloudDownloadIcon slot="icon" />提取数据</t-button
              >
              <t-button variant="outline" theme="primary" @click="check"><ChartIcon slot="icon" />查看底稿</t-button>
              <t-dropdown
                :options="[
                  { content: '导出当前页', value: 1, onClick: () => exportExcl() },
                  { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
                ]"
              >
                <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
              </t-dropdown>
            </gt-space>
          </div>
          <div class="znsbSbBodyDiv">
            <t-table
              ref="tableRef"
              row-key="key"
              hover
              :data="tableData"
              :columns="tableColumns"
              :editable-row-keys="editableRowKeys"
              height="100%"
              lazyLoad
              :selected-row-keys="selectedRowKeys"
              @select-change="rehandleSelectChange"
              @row-edit="onRowEdit"
              @row-validate="onRowValidate"
              @validate="onValidate"
              :pagination="pagination"
              @page-change="pageChange"
              :loading="tableLoading"
            >
              <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
              <template #cyrs="{ row }">
                <span>{{ row.cyrs }}</span>
              </template>
              <template #jmzcze="{ row }">
                <span>{{ numberToPrice(row.jmzcze) }}</span>
              </template>
              <template #yysr="{ row }">
                <span>{{ numberToPrice(row.yysr) }}</span>
              </template>
              <template #yycb="{ row }">
                <span>{{ numberToPrice(row.yycb) }}</span>
              </template>
              <template #lrze="{ row }">
                <span>{{ numberToPrice(row.lrze) }}</span>
              </template>
            </t-table>
          </div>
          <Modal :visible="isModalVisible1" @close="isModalVisible1 = false">
            <PdfViewer :pdfUrl="pdfUrl1" />
          </Modal>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import dayjs from 'dayjs';
import PdfViewer from '@/pages/index/components/pdf/PdfViewer.vue';
import Modal from '@/pages/index/components/pdf/Modal.vue';
import { ChartIcon, CloudDownloadIcon, DownloadIcon } from 'tdesign-icons-vue';
import { downloadBlobFile } from '@/core/download';
import { MessagePlugin, Input, InputNumber } from 'tdesign-vue';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { querySearchConfig } from './config.js';

export default {
  components: {
    SkeletonFrame,
    Mybreadcrumb,
    SearchControlPanel,
    DownloadIcon,
    ChartIcon,
    CloudDownloadIcon,
    PdfViewer,
    Modal,
  },
  data() {
    return {
      loading: true,
      isModalVisible1: false,
      pdfUrl1: `${document.location.origin}/znsb/view/tzzx/qysdsyjtzckdg.pdf`,
      userInfo: {},
      tableLoading: false,
      dcLoading: false,
      formData: {},
      querySearchConfig,
      querySearchConfigOneRules: {},
      editableRowKeys: ['1'],
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      selectedRowKeys: [],
      tableData: [],
      id: dayjs().unix(),
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  computed: {
    tableColumns() {
      return [
        {
          width: 50,
          align: 'center',
          colKey: 'xh',
          title: '序号',
        },
        {
          colKey: 'ssyf',
          align: 'center',
          title: '所属月份',
          ellipsis: true,
          width: 180,
          edit: {
            component: Input,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'cyrs',
          title: '从业人数',
          ellipsis: true,
          width: 120,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'jmzcze',
          title: '资产总额-季末（万元）',
          ellipsis: true,
          width: 160,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'yysr',
          title: '营业收入（元）',
          ellipsis: true,
          width: 160,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'yycb',
          title: '营业成本（元）',
          ellipsis: true,
          width: 160,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'right',
          colKey: 'lrze',
          title: '利润总额（元）',
          ellipsis: true,
          width: 160,
          edit: {
            component: InputNumber,
            props: {
              autofocus: true,
              autoWidth: true,
              theme: 'normal',
            },
            rules: [{ required: true, message: '不能为空' }],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 100,
          foot: '-',
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.key);

            return (
              <div>
                {!editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onEdit}>
                    编辑
                  </t-link>
                )}
                {editable && (
                  <t-link class="t-link-btn" theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
  },
  created() {
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '企业所得税预缴台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/qysdsyjtz'],
      goBackPath: '/znsb/view/qysdsyjtz', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    this.$refs.myBre.initMyBre(parmObjDhcd);
    this.query();
    this.changeLoading();
  },
  methods: {
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
    onEdit(e) {
      console.log(this.tableData, 'this.tableData');
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
        console.log('id1');
      } else {
        id = e.currentTarget.dataset.id;
      }

      // if (!this.editableRowKeys.includes(id)) {
      this.editableRowKeys.push(id);
      // }
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            this.tableData.splice(current.rowIndex, 1, current.editedRow);
            MessagePlugin.success('保存成功');
            // this.update(current.editedRow);
          }
          // 关闭编辑/保存折叠按钮
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        console.log('Promise Table Data Validate:', params);
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          MessagePlugin.warning(firstError[0].message);
        }
      });
    },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async query() {
      this.tableLoading = true;
      try {
        this.tableData = [
          {
            index: '0',
            ssyf: '2024-07 至 2024-09',
            cyrs: 1569,
            jmzcze: 162885.25,
            yysr: 1923291753.17,
            yycb: 1232385455.54,
            lrze: 176576953.37,
          },
        ];
        this.pagination.total = this.tableData.length;
      } catch (e) {
        console.log(e);
      } finally {
        setTimeout(() => {
          this.tableLoading = false;
          if (this.loading) {
            this.loading = false;
          }
        }, 200);
      }
    },
    // async update(params) {
    //   try {
    //     console.log('updateData', params);
    //     const { code, msg, data } = await qtkspzzzUpdate(params);
    //     console.log('data', data);
    //     console.log('code', code);
    //     if (code === 1) {
    //       console.log(msg);
    //       this.$message.success(data);
    //     } else if (msg) {
    //       console.log(msg);
    //       this.$message.warning(msg);
    //     } else {
    //       this.$message.warning(data);
    //     }
    //   } catch (e) {
    //     console.log(e);
    //   } finally {
    //     this.query();
    //   }
    // },
    // 查看底稿
    check() {
      this.isModalVisible1 = true;
    },
    // 导出
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610790',
        tzlx: 'qysdsyjtz',
        fileName: '企业所得税预缴台账',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
    getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      const params = { flag: true };
      this.query(params);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../styles/dialog.less';
@import '../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbSbBodyDiv {
  padding-bottom: 10px !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
}
</style>
