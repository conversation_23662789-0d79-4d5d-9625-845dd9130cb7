<!--
 * @Descripttion: 台账-营业账簿台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-04-17 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="container t-table-demo__editable-row">
    <div style="display: flex; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div style="margin-right: 12px">所属账期</div>
        <t-date-picker v-model="sszq" mode="month" able-time-picker allow-input clearable />
      </div>
      <div class="btns">
        <!-- <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="ckdg">查看底稿</t-button>
        <t-image-viewer v-model="pdf_visible" :images="[picSrc]"> </t-image-viewer> -->
        <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="query">提取数据</t-button>
        <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="save">保存台账</t-button>
        <!-- <t-button
          style="margin-left: 12px"
          class="btn"
          variant="outline"
          theme="primary"
          type="submit"
          @click="download(props.row.creativeScript)"
          >模板下载</t-button
        > -->
        <!-- <t-button style="margin-left: 12px" class="btn" variant="outline" theme="primary" type="submit" @click="query"
          >模板导入</t-button
        > -->
      </div>
    </div>

    <div class="tableBtn">
      <t-button class="btn" theme="primary" type="submit" @click="addRow">增行</t-button>
      <t-button class="btn" variant="outline" theme="primary" type="submit" @click="delRow">删行</t-button>
    </div>

    <!-- :table-content-width="tableLayout === 'fixed' ? undefined : '1600px'" -->
    <div class="myTable">
      <t-table
        class="qqtable"
        ref="tableRef"
        row-key="key"
        width="80%"
        :data="yyzbtzList"
        :columns="fpzzColumns"
        :foot-data="footData"
        :editable-row-keys="editableRowKeys"
        table-layout="auto"
        bordered
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        @row-edit="onRowEdit"
        @row-validate="onRowValidate"
        @validate="onValidate"
      >
        <!-- :rowspanAndColspanInFooter="rowspanAndColspanInFooter" -->
        <!-- <template #t-foot-required> <div style="font-weight: bold; text-align: center">合计</div> </template> -->
      </t-table>
    </div>
  </div>
</template>
<script lang="jsx">
import dayjs from 'dayjs';
import api from '@/pages/index/api/tzzx/yhstz/yyzbtz.js';
import { MessagePlugin, Input, DatePicker } from 'tdesign-vue';

export default {
  data() {
    return {
      // picSrc: `${document.location.origin}/znsb/view/tzzx/fppy.png`,
      // pdf_visible: false,
      editableRowKeys: ['1'],
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      minHeight: document.documentElement.clientHeight - 160,
      formData: {
        sszq: dayjs().subtract(1, 'month').format('YYYY-MM'),
      },
      sszq: dayjs().subtract(1, 'month').format('YYYY-MM'),
      selectedRowKeys: [],
      yyzbtzList: [],
      delformData: [],
      id: dayjs().unix(),
      tableLayout: true,
      showHeader: true,
      collapsed: true,
      cshList: [],
      maxHeight: '450',
      mxPageVisible: false,
      mxDialogHeight: '70%',
      mxDialogWidth: '80%',
      // 表尾有一行数据
      // footData: [
      //   {
      //     index: '123',
      //     type: '全部类型',
      //     default: '',
      //     description: '-',
      //   },
      // ],
    };
  },
  created() {
    this.init();
  },
  computed: {
    fpzzColumns() {
      return [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: 60,
          // foot: () => <b style="font-weight: bold;text-align: center">合计</b>,
        },
        {
          align: 'center',
          colKey: 'zbrq1',
          title: '账簿日期',
          width: 100,
          render(h, context) {
            const { type, row } = context;
            if (type === 'title')
              return (
                <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
                  <span style="color:red">*</span>账簿日期
                </div>
              );
            return <div style="text-align:center">{row.zbrq1}</div>;
          },
          cell: (h, { row }) => <div>{row.zbrq1 ? dayjs(row.zbrq1).format('YYYY-MM') : ''}</div>,
          edit: {
            component: DatePicker,
            props: {
              clearable: true,
              autoWidth: true,
            },
            // 校验规则，此处同 Form 表单
            rules: [
              { required: true, message: '不能为空' },
              // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'kmDm',
          title: '科目代码',
          width: 100,
          edit: {
            component: Input,
            props: {
              clearable: true,
              autoWidth: true,
            },
            // 校验规则，此处同 Form 表单
            rules: [
              { required: true, message: '不能为空' },
              // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'kmmc',
          title: '科目名称',
          width: 160,
          edit: {
            component: Input,
            props: {
              clearable: true,
              autoWidth: true,
            },
            // 校验规则，此处同 Form 表单
            rules: [
              { required: true, message: '不能为空' },
              // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
            ],
            showEditIcon: false,
          },
        },
        {
          align: 'center',
          colKey: 'sqjy',
          title: '上期结余',
          children: [
            {
              align: 'center',
              colKey: 'sqjyjf',
              title: '借方',
              width: 120,
              render(h, context) {
                const { type, row } = context;
                if (type === 'title')
                  return (
                    <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
                      <span style="color:red">*</span>借方
                    </div>
                  );
                return <div style="text-align:center">{row.sqjyjf}</div>;
              },
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
            {
              align: 'center',
              colKey: 'sqjydf',
              title: '贷方',
              width: 120,
              render(h, context) {
                const { type, row } = context;
                if (type === 'title')
                  return (
                    <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
                      <span style="color:red">*</span>贷方
                    </div>
                  );
                return <div style="text-align:center">{row.sqjydf}</div>;
              },
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
          ],
        },
        {
          align: 'center',
          colKey: 'bqfs',
          title: '本期发生',
          children: [
            {
              align: 'center',
              colKey: 'bqfsjf',
              title: '借方',
              width: 120,
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
            {
              align: 'center',
              colKey: 'bqfsdf',
              title: '贷方',
              width: 120,
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
          ],
        },
        {
          align: 'center',
          colKey: 'bqjy',
          title: '本期结余',
          children: [
            {
              align: 'center',
              colKey: 'bqjyjf',
              title: '借方',
              width: 120,
              render(h, context) {
                const { type, row } = context;
                if (type === 'title')
                  return (
                    <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
                      <span style="color:red">*</span>借方
                    </div>
                  );
                return <div style="text-align:center">{row.bqjyjf}</div>;
              },
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
            {
              align: 'center',
              colKey: 'bqjydf',
              title: '贷方',
              width: 120,
              render(h, context) {
                const { type, row } = context;
                if (type === 'title')
                  return (
                    <div style="text-align:center,font-size:14px;color:#333;font-weight:700">
                      <span style="color:red">*</span>贷方
                    </div>
                  );
                return <div style="text-align:center">{row.bqjydf}</div>;
              },
              edit: {
                component: Input,
                props: {
                  clearable: true,
                  autoWidth: true,
                },
                // 校验规则，此处同 Form 表单
                rules: [
                  { required: true, message: '不能为空' },
                  // { max: 10, message: '字符数量不能超过 10', type: 'warning' },
                ],
                showEditIcon: false,
              },
            },
          ],
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 120,
          foot: '-',
          cell: (h, { row }) => {
            const editable = this.editableRowKeys.includes(row.key);

            return (
              <div class="table-operations">
                {!editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onEdit}>
                    编辑
                  </t-link>
                )}
                {!editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onEdit}>
                    附件
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onSave}>
                    保存
                  </t-link>
                )}
                {editable && (
                  <t-link theme="primary" hover="color" data-id={row.key} onClick={this.onCancel}>
                    取消
                  </t-link>
                )}
              </div>
            );
          },
        },
      ];
    },
    pagination() {
      return {
        defaultCurrent: 1,
        defaultPageSize: 10,
        total: 0,
        showJumper: true,
        onChange: (pageInfo) => {
          if (this.defaultLength < this.yyzbtzList.length) {
            this.pagination.defaultCurrent = 1;
            this.visible = true;
          } else {
            this.pagination.defaultCurrent = pageInfo.current;
            this.pagination.defaultPageSize = pageInfo.pageSize;
            this.query();
            console.log(pageInfo, 'query');
          }
        },
      };
    },
    iconName() {
      return this.collapsed ? 'chevron-right' : 'chevron-left';
    },
  },
  methods: {
    // ckdg() {
    //   console.log('route', this.picSrc);
    //   this.pdf_visible = true;
    // },
    addRedStar(h, { column }) {
      // 给表头加必选标识
      return [h('span', { style: 'color: red' }, '*'), h('span', ` ${column.label}`)];
    },
    rowspanAndColspanInFooter({ rowIndex, colIndex }) {
      console.log('rowIndex', rowIndex);
      console.log('colIndex', colIndex);
      console.log('fpzzColumns', this.fpzzColumns.length - 2);
      if (rowIndex === 0 && colIndex === 0) return { colspan: this.fpzzColumns.length - 4 };
      return {};
    },
    onEdit(e) {
      console.log(this.yyzbtzList, 'this.yyzbtzList');
      let id = 0;
      if (e === undefined) {
        id = this.yyzbtzList[this.yyzbtzList.length - 1].key;
        console.log('id1');
      } else {
        id = e.currentTarget.dataset.id;
      }

      // if (!this.editableRowKeys.includes(id)) {
      this.editableRowKeys.push(id);
      // }
      console.log(this.editableRowKeys, 'this.editableRowKeys');
      this.isEditable = true;
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    onCancel(e) {
      const { id } = e.currentTarget.dataset;
      this.updateEditState(id);
      this.$refs.tableRef.clearValidateData();
    },
    async onSave(e) {
      console.log(e, 'e');
      const { id } = e.currentTarget.dataset;
      this.currentSaveId = id;
      console.log(this.currentSaveId, 'this.currentSaveId');
      this.$refs.tableRef.validateRowData(id).then((params) => {
        console.log('Event Table Promise Validate:', params);
        if (params.result.length) {
          const r = params.result[0];
          console.log('r', r);
          MessagePlugin.error(`${r.col.title} ${r.errorList[0].message}`);
          return;
        }
        // 如果是 table 的父组件主动触发校验
        if (params.trigger === 'parent' && !params.result.length) {
          const current = this.editMap[this.currentSaveId];
          console.log('current', current);
          if (current) {
            this.yyzbtzList.splice(current.rowIndex, 1, current.editedRow);
            MessagePlugin.success('保存成功');
          }
          this.updateEditState(this.currentSaveId);
        }
      });
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    // onValidateTableData() {
    //   // 执行结束后触发事件 validate
    //   this.$refs.tableRef.validateTableData().then((params) => {
    //     console.log('Promise Table Data Validate:', params);
    //     const cellKeys = Object.keys(params.result);
    //     const firstError = params.result[cellKeys[0]];
    //     if (firstError) {
    //       MessagePlugin.warning(firstError[0].message);
    //     }
    //   });
    // },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(params) {
      console.log('Event Table Data Validate:', params);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      const { row, col, value } = params;
      const oldRowData = this.editMap[row.key]?.editedRow || row;
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    // openJxfpmx(row) {
    //   this.$emit('openJxfpmx', row);
    // },
    async delRow() {
      this.checkBox.forEach((item) => {
        const index = this.yyzbtzList.findIndex((i) => i.key === item.key);
        this.yyzbtzList.splice(index, 1);
        this.delformData.push({
          uuid: item.uuid,
          djxh: item.djxh,
          sszq: item.sszq,
          scbz: '1',
        });
        this.pagination.total = this.yyzbtzList.length;
      });

      this.id = dayjs().unix();
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
    },
    addRow() {
      this.yyzbtzList.push({
        key: this.getKey(),
        index: this.yyzbtzList.length, // 数据索引 就应该 从 0 开始
        djxh: '91511622762319341', // 暂时写死
        sszq: this.sszq.substring(0, 4) + this.sszq.substring(5, 7),
        xmDm: '',
        je: '',
        se: '',
      });
      console.log(this.yyzbtzList, 'yyzbtzList');
      this.checkBox = [];
      this.pagination.total = this.yyzbtzList.length;
      this.onEdit();
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async init() {
      const sszq = this.sszq.substring(0, 4) + this.sszq.substring(5, 7);
      console.log(sszq, 'sszq');
      this.formData.djxh = '91511622762319341';
      this.formData.sszq = sszq;
      this.formData.pageSize = this.pagination.defaultPageSize;
      this.formData.pageNum = this.pagination.defaultCurrent;
      const params = this.formData;
      const { code, msg, data } = await api.init(params);
      const { returnCode } = data.returnCode;
      const { returnMsg } = data.returnMsg;
      console.log('data', data);
      console.log('code', code);
      console.log('returnCode', returnCode);
      if (code === 0) {
        console.log(data.data.records, 'data.data.records');
        this.yyzbtzList = data.data.records || [];
        // this.cshList = data.data.records || [];
        this.defaultLength = this.yyzbtzList.length || 0;
        // this.slList = data.data.slList;
        // this.zsfsList = data.data.zsfsList;
        // this.zsxmList = data.data.zsxmList;
        this.yyzbtzList.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        this.pagination.total = data.data.pageTotal;
      } else if (msg) {
        console.log(msg);
      } else {
        console.log(returnMsg);
        this.$message.warning(returnMsg);
      }
    },
    async query() {
      const sszq = this.sszq.substring(0, 4) + this.sszq.substring(5, 7);
      console.log(sszq, 'sszq');
      this.formData.djxh = '91511622762319341';
      this.formData.sszq = sszq;
      this.formData.pageSize = this.pagination.defaultPageSize;
      this.formData.pageNum = this.pagination.defaultCurrent;
      const params = this.formData;
      const { data } = await api.query(params);
      if (data.data === null) {
        this.yyzbtzList = [];
        this.pagination.total = 0;
      } else {
        console.log(data.data.records, 'data.data.records');
        this.yyzbtzList = data.data.records || [];
        this.defaultLength = this.yyzbtzList.length || 0;
        this.yyzbtzList.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        this.pagination.total = data.data.pageTotal;
      }
    },
    async save() {
      this.delformData.forEach((item) => {
        this.yyzbtzList.push({
          uuid: item.uuid,
          djxh: item.djxh,
          sszq: item.sszq,
          scbz: '1',
        });
      });
      const params = this.yyzbtzList;
      const { msg, data } = await api.add(params);
      const { returnCode } = data;
      const { returnMsg } = data;
      console.log('data', data);
      console.log('returnCode', returnCode);
      // console.log('returnCode', returnCode);
      if (returnCode === 0) {
        console.log(returnMsg);
        this.$message.success(returnMsg);
      } else if (returnMsg) {
        console.log(returnMsg);
        this.$message.warning(returnMsg);
      } else {
        console.log(msg);
        this.$message.warning(msg);
      }
      this.init();
      this.delformData = [];
    },
    download() {
      window.open('http://localhost:9002/znsb/api/tzzx/yhszz/v1/yhsYyzbtzMbDownload?t=1713778006206');
    },
  },
};
</script>
<style lang="less">
.t-table-demo__editable-row .table-operations > .t-link {
  margin-right: 8px;
}
.t-table-demo__editable-row .t-demo-col__datepicker .t-date-picker {
  width: 120px;
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog {
      padding: 0;
      border-radius: 2px;
    }
  }
}
.container {
  width: 100%;
  padding: 16px 24px;
  .header {
    div {
      display: inline-block;
      vertical-align: middle;
    }
    .sszq {
      padding-right: 8px;
    }
    .btns {
      float: right;
      .btn {
        margin-left: 12px;
      }
    }
  }
  .tableBtn {
    margin: 20px 0;
    .btn {
      margin-right: 12px;
    }
  }
  .pag {
    margin: 20px 0;
  }
  .edit {
    color: #4285f4;
    cursor: pointer;
  }
}
.title {
  position: relative;
  width: 100%;
  padding: 15px;
  font-size: 16px;
  font-weight: normal;
  color: #000;
  border-bottom: 1px solid rgb(233, 231, 231);
  .closeBtn {
    position: absolute;
    top: 24px;
    right: 24px;
    display: inline-block;
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.4);
    border-radius: 2px;
  }
  .closeBtn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.btnsleft {
  display: flex;
  height: 44px;
  padding-top: 12px;
  padding-right: 24px;
  margin-top: 80px;
  border-top: 1px solid rgb(233, 231, 231);
  justify-content: flex-end;
  .btn {
    width: 60px;
    height: 32px;
    padding: 0;
    line-height: 32px;
  }
  .btn1 {
    margin-right: 12px;
  }
}
.con {
  padding-right: 20px;
  padding-left: 20px;
}
// .row {
//   margin-bottom: 30px;
// }
.search-line {
  display: block;
}
.specText {
  cursor: pointer;
}
.tdgv-wrapper .t-table td[key='row-select'] {
  padding: 13px 0 11px;
  text-align: center;
}
.tdgv-wrapper .t-table--bordered tfoot > tr:first-child > td {
  text-align: center;
}
.tdgv-wrapper .t-input--auto-width {
  width: fit-content;
  min-width: 100% !important;
}
</style>
