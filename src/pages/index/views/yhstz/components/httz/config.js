import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  ssyfq: [{ required: true, message: '必填项', type: 'error' }],
  ssyfz: [{ required: true, message: '必填项', type: 'error' }],
};
export function Ssyf() {
  const year = dayjs().format('YYYY-MM').substring(0, 4);
  const month = dayjs().format('YYYY-MM').substring(5, 7);
  let ssyfq;
  let ssyfz;
  switch (month) {
    case '01':
      ssyfq = `${(parseInt(year, 10) - 1).toString()}-10`;
      ssyfz = `${(parseInt(year, 10) - 1).toString()}-12`;
      break;
    case '02':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '03':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '04':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '05':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '06':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '07':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '08':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '09':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '10':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '11':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    case '12':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    default:
      break;
  }
  return { ssyfq, ssyfz };
}
export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().startQuarter.substring(0, 7),
    value: Ssyf().ssyfq,
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().endQuarter.substring(0, 7),
    value: Ssyf().ssyfz,
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '合同名称',
    key: 'htmc',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同类型',
    key: 'zspmDm',
    type: 'select',
    filterable: true,
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '合同子类',
    key: 'zszmDm',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '签订日期起',
    key: 'qdrqq',
    type: 'datepicker',
    placeholder: '请选择',
    clearable: true,
    relation: 'qdrqz',
    timeRange: 'start',
  },
  {
    label: '签订日期止',
    key: 'qdrqz',
    type: 'datepicker',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'qdrqq',
  },
  {
    label: '合同编号',
    key: 'htbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '供应商编码',
    key: 'gysbm',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '客户编码',
    key: 'khbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];
export const htColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ywqdDm !== '01' }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
  },
  {
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 180,
    colKey: 'htbh',
    title: '合同编号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'htmc',
    title: '合同名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'zspmmc',
    title: '合同类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'zszmmc',
    title: '合同子类',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'htydsxrq',
    title: '合同约定生效日期',
    cell: (h, { row }) => <div>{row.htydsxrq ? dayjs(row.htydsxrq).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    width: 140,
    colKey: 'htydzzrq',
    title: '合同约定终止日期',
    cell: (h, { row }) => <div>{row.htydzzrq ? dayjs(row.htydzzrq).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    width: 120,
    colKey: 'ynspzsllsrq',
    title: '合同签订日期',
    cell: (h, { row }) => <div>{row.ynspzsllsrq ? dayjs(row.ynspzsllsrq).format('YYYY-MM-DD') : ''}</div>,
  },
  {
    width: 160,
    colKey: 'gysbm',
    title: '供应商编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 160,
    colKey: 'khbh',
    title: '客户编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 120,
    colKey: 'htzjk1',
    title: '合同总价款',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 160,
    colKey: 'htbt',
    title: '合同主体',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'htsm1',
    title: '合同说明',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    width: 140,
    colKey: 'fbhtbj',
    title: '分包合同标记',
  },
  {
    align: 'center',
    width: 140,
    colKey: 'kjhtbj',
    title: '框架合同标记',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'bhsje',
    title: '不含税金额',
    fixed: 'right',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    fixed: 'right',
  },
];

export const sjjsColumn = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    align: 'center',
    width: '10%',
    colKey: 'index',
    title: '序号',
    cell: 'index-cell',
  },
  {
    align: 'center',
    width: '40%',
    colKey: 'sjjsje',
    title: '实际结算金额',
  },
  {
    align: 'center',
    width: '40%',
    colKey: 'sjjsrq',
    title: '实际结算日期',
  },
];

export const dfslColumn = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ywqdDm === 'TZ_USER') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    align: 'center',
    width: '9%',
    colKey: 'index',
    title: '序号',
    cell: 'index-cell',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrmc',
    title: '对方书立人名称',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrnsrsbh',
    title: '对方书立人纳税人识别号（统一社会信用代码）',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrsjje',
    title: '对方书立人涉及金额',
  },
];
