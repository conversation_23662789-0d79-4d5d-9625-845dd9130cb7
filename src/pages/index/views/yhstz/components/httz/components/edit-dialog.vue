<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增合同明细', '编辑合同明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item
              :label="fieldConfig.lrzx.condition(this) ? fieldConfig.lrzx.renamedLabel : fieldConfig.lrzx.label"
              name="lrzx"
            >
              <t-select v-model="formData.lrzx" placeholder="请选择" clearable>
                <t-option v-for="item in lrzxList" :value="item.value" :label="item.value" :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同编号" name="htbh">
              <t-input :maxlength="30" v-model="formData.htbh" placeholder="请填写合同编号" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同名称" name="htmc">
              <t-input :maxlength="30" v-model="formData.htmc" placeholder="请填写合同名称" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同签订日期" name="ynspzsllsrq">
              <t-date-picker
                v-model="formData.ynspzsllsrq"
                placeholder="请填写合同签订日期"
                style="width: 276px; height: 32px"
                :disabled="htqdrqDisabled"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同约定生效日期" name="htydsxrq">
              <t-date-picker
                v-model="formData.htydsxrq"
                placeholder="请填写合同约定生效日期"
                style="width: 276px; height: 32px"
                clearable
                :disableDate="(date) => getDisableDate(date, 'htydzzrq', 'start')"
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同约定终止日期" name="htydzzrq">
              <t-date-picker
                v-model="formData.htydzzrq"
                placeholder="请填写合同约定终止日期"
                style="width: 276px; height: 32px"
                clearable
                :disableDate="(date) => getDisableDate(date, 'htydsxrq', 'end')"
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同类型" name="zspmDm">
              <t-select v-model="formData.zspmDm" placeholder="请选择合同类型" clearable @change="zspmChange">
                <t-option
                  v-for="item in zspmDmList"
                  :value="item.zspmDm"
                  :label="item.zspmDm + ` | ` + item.zspmmc"
                  :key="item.zspmDm"
                ></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同子类" name="zszmDm">
              <t-select
                v-model="formData.zszmDm"
                placeholder="请选择合同子类"
                clearable
                :disabled="!zszmDmCheckList.length"
              >
                <t-option
                  v-for="item in zszmDmCheckList"
                  :value="item.zszmDm"
                  :label="item.zszmmc"
                  :key="item.zszmDm"
                ></t-option>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同总价款" name="htzjk1">
              <!-- <t-input-number
                v-model="formData.htzjk1"
                theme="column"
                :decimal-places="2"
                placeholder="请填写合同总价款"
                clearable
              /> -->
              <gt-input-money v-model="formData.htzjk1" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="不含税金额" name="bhsje">
              <!-- <t-input-number
                v-model="formData.bhsje"
                theme="column"
                :decimal-places="2"
                placeholder="请填写不含税金额"
                clearable
              /> -->
              <gt-input-money v-model="formData.bhsje" theme="normal" align="left" clearable />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同主体" name="htbt">
              <t-input :maxlength="30" v-model="formData.htbt" placeholder="请填写合同主体" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同说明" name="htsm1">
              <t-input :maxlength="30" v-model="formData.htsm1" placeholder="请填写合同说明" clearable></t-input>
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
      <t-card header-bordered :style="{ width: '100%', paddingBottom: '20px' }">
        <template>
          <t-tabs v-model="value">
            <t-tab-panel :value="1" label="实际结算信息">
              <template>
                <t-button @click="addSjjs" :style="{ margin: '16px 8px' }"><add-icon slot="icon" />新增</t-button>
                <t-button @click="delSjjs" :style="{ margin: '16px 8px' }"><DeleteIcon slot="icon" />删除</t-button>
              </template>
              <t-table
                ref="tableRef"
                row-key="key"
                :stripe="true"
                :data="sjjsList"
                :columns="sjjsColumn"
                :hover="true"
                :bordered="true"
                :editable-cell-state="editableCellState"
                :selected-row-keys="selectedRowKeys"
                @select-change="rehandleSelectChange"
              >
                <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
                <template #sjjsje="{ row }">
                  <span>{{ numberToPrice(row.sjjsje) }}</span>
                </template>
              </t-table>
            </t-tab-panel>
            <t-tab-panel :value="2" label="对方书立人信息">
              <template>
                <t-button @click="addDfslr" :style="{ margin: '16px 8px' }" :disabled="isCkSy"
                  ><add-icon slot="icon" />新增</t-button
                >
                <t-button @click="delDfslr" :style="{ margin: '16px 8px' }" :disabled="isCkSy"
                  ><DeleteIcon slot="icon" />删除</t-button
                >
              </template>
              <t-table
                ref="tableRef"
                row-key="key"
                :stripe="true"
                :data="dfslrList"
                :columns="dfslColumn"
                :hover="true"
                :bordered="true"
                :editable-cell-state="editableCellState"
                :selected-row-keys="selectedRowKeys"
                @select-change="rehandleSelectChange"
              >
                <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
                <template #dfslrsjje="{ row }">
                  <span>{{ numberToPrice(row.dfslrsjje) }}</span>
                </template>
              </t-table>
            </t-tab-panel>
          </t-tabs>
        </template>
      </t-card>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { Input, InputNumber, DatePicker } from 'tdesign-vue';
import { queryYhslfkbl, insertHttz, updateHttz, checkYsb } from '@/pages/index/api/tzzx/yhstz/httz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { AddIcon, DeleteIcon } from 'tdesign-icons-vue';

const fieldConfig = {
  lrzx: {
    label: '利润中心',
    renamedLabel: '成本中心段',
    condition: (vm) => vm.shanxiyidongFlag,
  },
};
export default {
  components: { CssDialog, GtInputMoney, AddIcon, DeleteIcon },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      htbh: [{ required: true, message: '必填', type: 'error' }],
      htmc: [{ required: true, message: '必填', type: 'error' }],
      ynspzsllsrq: [{ required: true, message: '必填', type: 'error' }],
      htydsxrq: [{ required: true, message: '必填', type: 'error' }],
      htydzzrq: [{ required: true, message: '必填', type: 'error' }],
      zspmDm: [{ required: true, message: '必填', type: 'error' }],
      // zszmDm: [{ required: true, message: '必填', type: 'error' }],
      htzjk1: [{ required: true, message: '必填', type: 'error' }],
      bhsje: [{ required: true, message: '必填', type: 'error' }],
    };
    return {
      isCkSy: false,
      isVisible: true,
      confirmLoading: false,
      htqdrqDisabled: false,
      rules: {},
      lrzxList: [],
      zspmDmList: [],
      zszmDmList: [],
      zszmDmCheckList: [],
      sjjsList: [],
      dfslrList: [],
      selectedRowKeys: [],
      checkBox: [],
      value: 1,
      fieldConfig,
      formData: {
        uuid: '',
        lrzx: '',
        htbh: '',
        htmc: '',
        ynspzsllsrq: '',
        htydsxrq: '',
        htydzzrq: '',
        zspmDm: '',
        zszmDm: '',
        htzjk1: '',
        bhsje: '',
        htbt: '',
        htsm1: '',
        fbhtbj: 'N',
        kjhtbj: 'N',
      },
    };
  },
  created() {
    this.zspmDmList = this.$parent.zspmDmList;
    this.zszmDmList = this.$parent.zszmDmList;
    this.init();
  },
  computed: {
    // 新增山西移动标志计算属性
    shanxiyidongFlag() {
      // return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
      return true;
    },
    sjjsColumn() {
      return [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: '10%',
          align: 'center',
          fixed: 'left',
        },
        {
          align: 'center',
          width: '10%',
          colKey: 'xh',
          title: '序号',
          cell: 'index-cell',
        },
        {
          title: '实际结算金额',
          colKey: 'sjjsje',
          align: 'center',
          width: '40%',
          // 编辑状态相关配置，全部集中在 edit
          edit: {
            // 1. 支持任意组件。需保证组件包含 `value` 和 `onChange` 两个属性，且 onChange 的第一个参数值为 new value。
            // 2. 如果希望支持校验，组件还需包含 `status` 和 `tips` 属性。具体 API 含义参考 Input 组件
            component: InputNumber,
            // props, 透传全部属性到 Input 组件（可以是一个函数，不同行有不同的 props 属性 时，使用 Function）
            props: {
              clearable: true,
              decimalPlaces: '2',
              theme: 'normal',
            },
            // 除了点击非自身元素退出编辑态之外，还有哪些事件退出编辑态
            abortEditOnEvent: ['onEnter', 'onMouseleave', 'onBlur'],
            // 编辑完成，退出编辑态后触发
            onEdited: (context) => {
              this.sjjsList.splice(context.rowIndex, 1, context.newRowData);
              console.log('Edit sjjsje:', context);
              this.calcHtzjk();
              console.log('Sum sjjsje', this.formData.htzjk1);
              // MessagePlugin.success('Success');
            },
            // 校验规则，此处同 Form 表单
            rules: [{ required: true, message: '不能为空' }],
            // 默认是否为编辑状态
            defaultEditable: false,
            // 校验时机：exit | change
            validateTrigger: 'change',
            // 透传给 component: Input 的事件
            on: (editContext) => ({
              onBlur: () => {
                console.log('失去焦点', editContext);
              },
              // both onEnter and enter can work
              onEnter: (ctx) => {
                console.log('回车', ctx);
              },
            }),
            // showEditIcon: false,
          },
        },
        {
          title: '实际结算日期',
          colKey: 'sjjsrq',
          align: 'center',
          width: '40%',
          // props, 透传全部属性到 DatePicker 组件
          edit: {
            component: DatePicker,
            props: {
              mode: 'date',
            },
            // 除了点击非自身元素退出编辑态之外，还有哪些事件退出编辑态
            abortEditOnEvent: ['onChange', 'onMouseleave'],
            onEdited: (context) => {
              this.sjjsList.splice(context.rowIndex, 1, context.newRowData);
              console.log('Edit sjjsrq:', context);
              // MessagePlugin.success('Success');
            },
            // 校验规则，此处同 Form 表单
            rules: () => [
              {
                // validator: (val) => dayjs(val).isAfter(dayjs()),
                // message: '只能选择今天以后日期',
              },
            ],
            // showEditIcon: false,
          },
        },
      ];
    },
    dfslColumn() {
      return [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: '10%',
          align: 'center',
          fixed: 'left',
        },
        {
          align: 'center',
          width: '9%',
          colKey: 'xh',
          title: '序号',
          cell: 'index-cell',
        },
        {
          title: '对方书立人名称',
          colKey: 'dfslrmc',
          align: 'center',
          width: '27%',
          edit: {
            component: Input,
            props: {
              clearable: true,
            },
            abortEditOnEvent: ['onEnter', 'onMouseleave', 'onBlur'],
            onEdited: (context) => {
              this.dfslrList.splice(context.rowIndex, 1, context.newRowData);
              console.log('Edit dfslrmc:', context);
              // MessagePlugin.success('Success');
            },
            rules: [{ required: true, message: '不能为空' }],
            defaultEditable: false,
            validateTrigger: 'change',
            on: (editContext) => ({
              onBlur: () => {
                console.log('失去焦点', editContext);
              },
              onEnter: (ctx) => {
                console.log('回车', ctx);
              },
            }),
            // showEditIcon: false,
          },
        },
        {
          title: '对方书立人纳税人识别号（统一社会信用代码）',
          colKey: 'dfslrnsrsbh',
          align: 'center',
          width: '27%',
          edit: {
            component: Input,
            props: {
              clearable: true,
            },
            abortEditOnEvent: ['onEnter', 'onMouseleave', 'onBlur'],
            onEdited: (context) => {
              this.dfslrList.splice(context.rowIndex, 1, context.newRowData);
              console.log('Edit dfslrnsrsbh:', context);
              // MessagePlugin.success('Success');
            },
            rules: [{ required: true, message: '不能为空' }],
            defaultEditable: false,
            validateTrigger: 'change',
            on: (editContext) => ({
              onBlur: () => {
                console.log('失去焦点', editContext);
              },
              onEnter: (ctx) => {
                console.log('回车', ctx);
              },
            }),
            // showEditIcon: false,
          },
        },
        {
          title: '对方书立人涉及金额',
          colKey: 'dfslrsjje',
          align: 'center',
          width: '27%',
          edit: {
            component: InputNumber,
            props: {
              clearable: true,
              decimalPlaces: '2',
              theme: 'normal',
            },
            abortEditOnEvent: ['onEnter', 'onMouseleave', 'onBlur'],
            onEdited: (context) => {
              this.dfslrList.splice(context.rowIndex, 1, context.newRowData);
              console.log('Edit dfslrsjje:', context);
              // MessagePlugin.success('Success');
            },
            rules: [{ required: true, message: '不能为空' }],
            defaultEditable: false,
            validateTrigger: 'change',
            on: (editContext) => ({
              onBlur: () => {
                console.log('失去焦点', editContext);
              },
              onEnter: (ctx) => {
                console.log('回车', ctx);
              },
            }),
            // showEditIcon: false,
          },
        },
      ];
    },
  },
  methods: {
    calcHtzjk() {
      this.formData.htzjk1 = this.sjjsList.reduce((sum, item) => sum + item.sjjsje, 0);
    },
    getDisableDate(date, relation, timeRange) {
      const selectData = this.formData[relation];
      if (!selectData) return false;
      const formatDate = dayjs(selectData).hour(0).minute(0).second(0);
      if (timeRange === 'start') {
        // 大于选中结束时间的都不可选
        return date > new Date(formatDate);
      }
      if (timeRange === 'end') {
        // 小于选中开始时间的都不可选
        return date < new Date(formatDate);
      }
      return false;
    },
    // async getLrzx() {
    //   const { code, data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
    //   if (code === 1) {
    //     this.lrzxList = data;
    //     if (this.lrzxList.length === 1) {
    //       this.formData.lrzx = this.lrzxList[0].value;
    //     } else {
    //       this.formData.lrzx = '';
    //     }
    //   }
    // },
    // 用于控制哪些行或哪些单元格不允许出现编辑态
    editableCellState(cellParams) {
      console.log('cellParams', cellParams);
      // const { row } = cellParams;
      // return row.ywqdDm !== 'TZ_USER';
      return true;
    },
    onRowValidate(params) {
      console.log('validate:', params);
    },
    // 用于提交前校验数据（示例代码有效，勿删）
    validateTableData() {
      // 仅校验处于编辑态的单元格
      this.$refs.tableRef.validateTableData().then((result) => {
        console.log('validate result: ', result);
      });
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    addSjjs() {
      this.sjjsList.push({
        key: this.getKey(),
        index: this.sjjsList.length + 1, // 序号从 1 开始
        sjjsje: '',
        sjjsrq: '',
      });
      console.log(this.sjjsList, 'sjjsList');
      this.checkBox = [];
    },
    addDfslr() {
      this.dfslrList.push({
        key: this.getKey(),
        index: this.dfslrList.length + 1, // 序号从 1 开始
        dfslrmc: '',
        dfslrnsrsbh: '',
        dfslrsjje: '',
      });
      console.log(this.dfslrList, 'dfslrList');
      this.checkBox = [];
    },
    async delSjjs() {
      this.checkBox.forEach((item) => {
        const index = this.sjjsList.findIndex((i) => i.key === item.key);
        this.sjjsList.splice(index, 1);
      });
      this.calcHtzjk();
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
    },
    async delDfslr() {
      this.checkBox.forEach((item) => {
        const index = this.dfslrList.findIndex((i) => i.key === item.key);
        this.dfslrList.splice(index, 1);
      });
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
    },
    async init() {
      // 避免异步加载因网络等延迟导致已加载数据被覆盖掉问题
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
      this.rules = this.baseRules;
      console.log(this.visible);
      if (this.visible.row?.uuid) {
        this.formData = this.visible.row;
        this.zszmDmCheckList = this.zszmDmList.filter((i) => i.zspmDm === this.formData?.zspmDm);
        const { data } = await queryYhslfkbl([this.visible.row.uuid]);
        this.sjjsList = data?.sjjsxxList || [];
        this.sjjsList.forEach((item, index) => {
          this.$set(item, 'index', index + 1);
        });
        this.dfslrList = data?.dfslxxList || [];
        this.dfslrList.forEach((item, index) => {
          this.$set(item, 'index', index + 1);
        });
        this.checkYsb();
      }
    },
    // 2025.1.10 建议优化合同明细信息，点击【编辑】按钮后，应该判断属期已申报，合同签订日期置灰不允许修改。否则可修改合同签订日期。
    async checkYsb() {
      const { data } = await checkYsb([this.visible.row.zzuuid]);
      if (data === 'Y') {
        this.htqdrqDisabled = true;
        this.$delete(this.baseRules, 'ynspzsllsrq');
      }
    },
    zspmChange(val) {
      this.zszmDmCheckList = this.zszmDmList.filter((i) => i.zspmDm === val);
      this.formData.zszmDm = '';
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        [
          'uuid',
          'lrzx',
          'htbh',
          'htmc',
          'ynspzsllsrq',
          'htydsxrq',
          'htydzzrq',
          'zspmDm',
          'zszmDm',
          'htzjk1',
          'bhsje',
          'htbt',
          'htsm1',
          'fbhtbj',
          'kjhtbj',
        ].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = { httzxx: p, ...this.visible.otherObj, sjjsxxList: this.sjjsList, dfslxxList: this.dfslrList };
        try {
          if (this.visible.pageType) {
            await updateHttz(params);
          } else {
            await insertHttz(params);
          }
          this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
          if (this.visible.pageType) {
            this.$emit('updateHttz');
          } else {
            this.$emit('updateHttz', { flag: true }); // 新增回到第一页
          }
          this.isVisible = false;
        } catch (e) {
          console.error(e);
        } finally {
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
