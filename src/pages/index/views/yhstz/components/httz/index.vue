<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue" @change="handleTabChange">
      <!-- 合同明细页签 -->
      <search-control-panel
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        class="znsbHeadqueryDiv"
        ref="queryControl"
        :config="filteredQueryConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
      />
      <search-control-panel
        v-show="tabValue === '2'"
        style="margin-top: 16px"
        class="znsbHeadqueryDiv"
        ref="queryControlFilter"
        :config="filteredQueryConfig"
        :form-rules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :col-num="4"
        @formChange="(v) => (formDataFilter = v)"
      />
      <div v-show="tabValue === '1'" class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
          <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
          <ExtractDataButton
            variant="outline"
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            :ywtsMsg="'财务凭证数据以及合同数据'"
            :ywlx="'yhs'"
            @query="query"
          />
          <!-- <t-dropdown
            :options="[
              { content: '下载模版', value: 1, onClick: () => downloadTemplate() },
              { content: '导入数据', value: 2, onClick: () => importExcel() },
            ]"
          >
            <t-button variant="outline" theme="primary"> <UploadIcon slot="icon" /><span>导入</span> </t-button>
          </t-dropdown> -->
          <t-button variant="outline" theme="primary" @click="downloadTemplate"
            ><FileIcon slot="icon" />下载模版</t-button
          >
          <t-upload
            action="/nssb/httz/v1/uploadExcel"
            :tips="tips"
            v-model="files"
            theme="custom"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            @fail="handleFail"
            @success="handleSuccess"
          >
            <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入数据</t-button>
          </t-upload>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <a ref="downloadTemplate" style="display: none" href="./yhshtxxtzmb.xlsx"></a>
          <!--        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="drmb()">导入模板</t-button>
        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="dr()">导入</t-button>-->
          <QsbButton />
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="1" label="合同明细" :destroy-on-hide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="tableData"
            :columns="filteredTableColumns"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination"
            @page-change="pageChange($event)"
            :loading="tableLoading"
            :foot-data="footData"
          >
            <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
            <template #htzjk1="{ row }">
              <span>{{ numberToPrice(row.htzjk1) }}</span>
            </template>
            <template #bhsje="{ row }">
              <span>{{ numberToPrice(row.bhsje) }}</span>
            </template>
            <template #fbhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.fbhtbj] || '' }}
            </template>
            <template #kjhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.kjhtbj] || '' }}
            </template>
            <template #operation="{ row }">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)" v-show="row.ywqdDm === '01'">
                编辑
              </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <!-- 被过滤合同页签 -->
      <t-tab-panel v-if="!quanshanFlag" value="2" label="被过滤合同" :destroy-on-hide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="tableDataFilter"
            :columns="htColumnsFilter"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="paginationFilter"
            @page-change="pageChange($event)"
            :loading="tableLoading"
            :foot-data="footDataFilter"
          >
            <template #xh="{ rowIndex }">{{
              (paginationFilter.current - 1) * paginationFilter.pageSize + rowIndex + 1
            }}</template>
            <template #htzjk1="{ row }">
              <span>{{ numberToPrice(row.htzjk1) }}</span>
            </template>
            <template #bhsje="{ row }">
              <span>{{ numberToPrice(row.bhsje) }}</span>
            </template>
            <template #fbhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.fbhtbj] || '' }}
            </template>
            <template #kjhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.kjhtbj] || '' }}
            </template>
            <!-- <template #operation="{ row }">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)" v-show="row.ywqdDm === '01'">
                编辑
              </t-link>
            </template> -->
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateHttz="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getYhsHtZspm, getYhsHtZszm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  queryHttz,
  deleteSelected,
  queryHttzHj,
  queryHttzFiltered,
  queryHttzFilteredHj,
} from '@/pages/index/api/tzzx/yhstz/httz.js';
import { downloadBlobFile } from '@/core/download';
import { AddIcon, DeleteIcon, FileIcon, UploadIcon, DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import { multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import EditDialog from './components/edit-dialog.vue';
import { querySearchConfig, querySearchConfigOneRules, htColumns } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    EditDialog,
    SearchControlPanel,
    AddIcon,
    FileIcon,
    UploadIcon,
    DeleteIcon,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.htColumns = htColumns;
    return {
      tabValue: '1',
      formData: {}, // 页签1的表单数据
      formDataFilter: {}, // 页签2的表单数据
      dcLoading: false,
      querySearchConfig,
      boxvisible: false,
      tableLoading: false,
      editDialogVisible: false,
      fromName: false,
      zspmDmList: [],
      zszmDmList: [],
      delformData: [],
      checkBox: [],
      selectedRowKeys: [],
      htlxList: [{ value: '01', label: '借款合同' }],
      htzlList: [{ value: '01', label: '金融机构借款合同' }],
      pagination: { current: 1, pageSize: 10, total: 0 },
      paginationFilter: { current: 1, pageSize: 10, total: 0 },
      tableData: [],
      tableDataFilter: [],
      footData: [],
      footDataFilter: [],
      tips: '',
      files: [],
      lastQueryParamsTab1: null, // 记录页签1的上次查询参数
      lastQueryParamsTab2: null, // 记录页签2的上次查询参数
    };
  },
  created() {
    this.formData.ssyf = dayjs().subtract(1, 'month').format('YYYY-MM');
  },
  mounted() {
    this.init();
    this.adjustColumnTitles(); // 添加列标题调整逻辑
  },
  watch: {
    'formData.zspmDm': {
      handler(newVal) {
        // 根据 zspmDm 的变化更新 zszmDm
        this.updateZszmDm(newVal);
      },
      deep: true, // 如果 zspmDm 是对象或数组，可能需要深度监听
    },
    formData: {
      handler(newVal) {
        console.log('formData changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
    formDataFilter: {
      handler(newVal) {
        console.log('formDataFilter changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
  },
  computed: {
    shanxiyidongFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003';
    },
    quanshanFlag() {
      return this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000004';
    },
    filteredQueryConfig() {
      return this.querySearchConfig.filter(
        (item) => (item.key !== 'gysbm' && item.key !== 'khbh') || !this.shanxiyidongFlag,
      );
    },
    filteredTableColumns() {
      this.adjustColumnTitles();
      return this.htColumns.filter(
        (col) => (col.colKey !== 'gysbm' && col.colKey !== 'khbh') || !this.shanxiyidongFlag,
      );
    },
    htColumnsFilter() {
      return this.filteredTableColumns.filter((column) => column.colKey !== 'operation');
    },
    currentFormData() {
      return this.tabValue === '2' ? this.formDataFilter : this.formData;
    },
    currentPagination() {
      return this.tabValue === '2' ? this.paginationFilter : this.pagination;
    },
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyYhsStatus', this.$store.state.jyss.readyYhsStatus);
      return this.$store.state.jyss.readyYhsStatus;
    },
    ssyfDefault() {
      return dayjs(this.formData.ssyf).format('YYYYMM');
    },
  },
  methods: {
    // 列标题调整方法
    adjustColumnTitles() {
      const columns = [...this.htColumns]; // 创建列配置副本

      // 如果是山西移动企业，修改利润中心列标题
      if (this.shanxiyidongFlag) {
        this.htColumns = columns.map((col) => {
          if (col.colKey === 'lrzx') {
            return { ...col, title: '成本中心段' };
          }
          return col;
        });
      }
    },
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$message.error(`导入失败`);
      }
      this.clearFiles();
      this.query({ flag: true });
    },

    handleTabChange(activeTab) {
      // 获取目标页签的上次查询条件
      const targetTabLastParams = activeTab === '1' ? this.lastQueryParamsTab1 : this.lastQueryParamsTab2;

      // 获取当前表单数据作为新的查询条件
      const currentFormData = activeTab === '1' ? this.formData : this.formDataFilter;

      const newParams = {
        ...currentFormData,
        tabValue: activeTab,
      };

      // 如果没有上次查询记录或查询条件变化时才执行查询
      if (!targetTabLastParams || JSON.stringify(newParams) !== JSON.stringify(targetTabLastParams)) {
        this.query({ flag: true });
      }

      // 更新目标页签的上次查询记录
      if (activeTab === '1') {
        this.lastQueryParamsTab1 = newParams;
      } else {
        this.lastQueryParamsTab2 = newParams;
      }
    },
    updateZszmDm(zspmDm) {
      // 根据 zspmDm 更新 zszmDm
      const filteredZszmDmList = this.zszmDmList.filter((item) => zspmDm.includes(item.zspmDm));
      this.querySearchConfig[4].selectList = filteredZszmDmList.map((d) => ({ value: d.zszmDm, label: d.zszmmc }));
      // 如果this.formData.zszmDm中的值在this.querySearchConfig[4].selectList内，则不清空this.formData.zszmDm
      const validValues = this.querySearchConfig[4].selectList.map((item) => item.value);
      const hasValidValues = (this.formData.zszmDm || []).some((value) => validValues.includes(value));
      if (hasValidValues) {
        return; // 有有效值时不执行清空操作
      }
      this.formData.zszmDm = []; // 清空当前的 zszmDm 选择
    },
    closeBox() {
      this.boxvisible = false;
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: 'htzzmx',
        fileName: '合同台账导出',
        cxParam: {
          ...this.formData,
          ssyfq: Number(dayjs(this.formData.ssyfq).format('YYYYMM')),
          ssyfz: Number(dayjs(this.formData.ssyfz).format('YYYYMM')),
          zspmDm: multiSelectHandle(this.formData.zspmDm),
          zszmDm: multiSelectHandle(this.formData.zszmDm),
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        sszq: Number(dayjs(this.formData.ssyf).format('YYYYMM')),
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async init() {
      this.initYhsHtZspm();
      this.initYhsHtZszm();
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p, from } = pm;
      if (dayjs(this.currentFormData.ssyfz).diff(dayjs(this.currentFormData.ssyfq), 'month') > 2) {
        this.$message.warning('时间范围不能超过三个月');
        return;
      }
      // 下钻进入时，需要返回按钮能够返回至上次操作页面
      this.fromName = from ?? this.fromName;
      // flag标志为ture时，需要重置查询，分页重置为首页
      if (flag) {
        this.tabValue === '2' ? (this.paginationFilter.current = 1) : (this.pagination.current = 1);
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.currentPagination.current,
        pageSize: this.currentPagination.pageSize,
      };
      if (p) {
        // 下钻进入时，默认进入明细页
        this.tabValue = '1';
        params = {
          ...p,
          ssyfq: Number(p.ssyfq),
          ssyfz: Number(p.ssyfz),
          ...params,
        };
        this.$refs.queryControl.setParams({
          ssyfq: dayjs(p.ssyfq).format('YYYY-MM'),
          ssyfz: dayjs(p.ssyfz).format('YYYY-MM'),
          zszmDm: p.zszmDm.split(','),
          zspmDm: p.zspmDm.split(','),
        });
      } else {
        params = {
          ...params,
          ...this.currentFormData,
          ssyfq: Number(dayjs(this.currentFormData.ssyfq).format('YYYYMM')),
          ssyfz: Number(dayjs(this.currentFormData.ssyfz).format('YYYYMM')),
          zspmDm: multiSelectHandle(this.currentFormData.zspmDm),
          zszmDm: multiSelectHandle(this.currentFormData.zszmDm),
        };
      }
      try {
        const isFilterTab = this.tabValue === '2';
        const apiQueryMethod = isFilterTab ? queryHttzFiltered : queryHttz; // 待接新接口
        const apiQueryHjMethod = isFilterTab ? queryHttzFilteredHj : queryHttzHj; // 待接新接口
        const { data } = await apiQueryMethod(params);
        if (isFilterTab) {
          this.tableDataFilter = data?.list || [];
          this.paginationFilter.total = data?.total || 0;
        } else {
          this.tableData = data?.list || [];
          this.pagination.total = data?.total || 0;
        }
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        if (this.currentPagination.total > 0) {
          const { data } = await apiQueryHjMethod(params);
          if (isFilterTab) {
            this.footDataFilter =
              [
                {
                  bhsje: numberToPrice(data?.bhsje),
                },
              ] || [];
          } else {
            this.footData =
              [
                {
                  bhsje: numberToPrice(data?.bhsje),
                },
              ] || [];
          }
        } else {
          this.footData = [];
          this.footDataFilter = [];
        }
        // 查询成功后更新对应页签的上次查询记录
        const currentParams = {
          ...params,
          tabValue: this.tabValue,
        };

        if (this.tabValue === '1') {
          this.lastQueryParamsTab1 = currentParams;
        } else {
          this.lastQueryParamsTab2 = currentParams;
        }
      } catch (e) {
        this.tableData = [];
        this.tableDataFilter = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params) {
      try {
        const { msg } = await deleteSelected(params);
        console.log('deleteSelected-msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    async initYhsHtZspm() {
      console.log('this.formData.ssyf', this.formData.ssyf);
      const { data } = await getYhsHtZspm(this.formData.ssyf);
      this.zspmDmList = data;
      this.querySearchConfig[3].selectList = this.zspmDmList.map((d) => ({ value: d.zspmDm, label: d.zspmmc }));
    },
    async initYhsHtZszm() {
      console.log('this.formData.ssyf', this.formData.ssyf);
      const { data } = await getYhsHtZszm(this.formData.ssyf);
      this.zszmDmList = data;
    },

    // 分页处理
    pageChange({ current, pageSize }) {
      const pagination = this.tabValue === '2' ? this.paginationFilter : this.pagination;
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
    // 下载模板
    async downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
