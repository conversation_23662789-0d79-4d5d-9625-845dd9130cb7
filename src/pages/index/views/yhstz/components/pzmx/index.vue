<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      class="znsbHeadqueryDiv"
      ref="queryControl"
      :config="querySearchConfig"
      :formRules="querySearchConfigOneRules"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
    />
    <div class="queryBtns" style="display: flex; justify-content: space-between">
      <gt-space size="10px">
        <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
        <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
        <ExtractDataButton
          variant="outline"
          :sszq="sszqToExtract"
          :readyStatus="readyStatus"
          :ywtsMsg="'财务凭证数据以及合同数据'"
          :ywlx="'yhs'"
          @query="query"
        />
        <t-dropdown
          :options="[
            { content: '导出当前页', value: 1, onClick: () => exportExcl() },
            { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
          ]"
        >
          <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
        </t-dropdown>
        <!--        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="drmb()">导入模板</t-button>
          <t-button variant="outline" theme="primary" :loading="dcLoading" @click="dr()">导入</t-button>-->
        <QsbButton />
      </gt-space>
    </div>
    <div class="znsbSbBodyDiv">
      <t-table
        ref="tableRef"
        row-key="uuid"
        height="100%"
        hover
        :data="tableData"
        :columns="dataColumns"
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
        :foot-data="footData"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
        <template #yhszspzlxDm="{ row }">
          {{
            (pzlxList.find((d) => d.value === row.yhszspzlxDm) &&
              pzlxList.find((d) => d.value === row.yhszspzlxDm).label) ||
            ''
          }}
        </template>
        <template #htlxmc="{ row }">
          {{
            (htlxList.find((d) => d.value === row.htlxmc) && htlxList.find((d) => d.value === row.htlxmc).label) || ''
          }}
        </template>
        <template #htbt="{ row }">
          {{ (htxlList.find((d) => d.value === row.htbt) && htxlList.find((d) => d.value === row.htbt).label) || '' }}
        </template>
        <template #pzkplx="{ row }">
          {{
            (pzkplxList.find((d) => d.value === row.pzkplx) && pzkplxList.find((d) => d.value === row.pzkplx).label) ||
            ''
          }}
        </template>
        <template #khz="{ row }">
          {{ (khzList.find((d) => d.value === row.khz) && khzList.find((d) => d.value === row.khz).label) || '' }}
        </template>
        <template #hylb="{ row }">
          {{ (hylbList.find((d) => d.value === row.hylb) && hylbList.find((d) => d.value === row.hylb).label) || '' }}
        </template>
        <!-- <template #kmmc="{ row }">
          {{ (kmbmList.find((d) => d.value === row.kmbm) && kmbmList.find((d) => d.value === row.kmbm).label) || '' }}
        </template> -->
        <template #operation="{ row }">
          <t-link theme="primary" hover="color" @click="newOrEditRow(row)" v-show="row.ywqdDm === '01'"> 编辑 </t-link>
        </template>
      </t-table>
    </div>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updatePzmx="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  queryPzmx,
  deleteSelected,
  getHtlx,
  getKplx,
  getPzlx,
  getKhz,
  getAllHtxl,
  getPzmxAllKmbm,
  queryPzmxHj,
} from '@/pages/index/api/tzzx/yhstz/pzmx.js';
import { downloadBlobFile } from '@/core/download';
import { AddIcon, DeleteIcon, DownloadIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import EditDialog from './components/edit-dialog.vue';
import { querySearchConfig, querySearchConfigOneRules, dataColumns, hylbList } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    EditDialog,
    SearchControlPanel,
    AddIcon,
    DeleteIcon,
    DownloadIcon,
  },
  data() {
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.dataColumns = dataColumns;
    this.hylbList = hylbList;
    return {
      dcLoading: false,
      footData: [],
      querySearchConfig,
      boxvisible: false,
      tableLoading: false,
      editDialogVisible: false,
      htlxList: [],
      htxlList: [],
      pzlxList: [],
      pzkplxList: [],
      khzList: [],
      kmbmList: [],
      tableData: [],
      lrzxList: [],
      formData: {},
      delformData: [],
      checkBox: [],
      selectedRowKeys: [],
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    this.formData.ssyf = dayjs().subtract(1, 'month').format('YYYY-MM');
  },
  mounted() {
    this.init();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyYhsStatus', this.$store.state.jyss.readyYhsStatus);
      return this.$store.state.jyss.readyYhsStatus;
    },
    ssyfDefault() {
      return dayjs(this.formData.ssyf).format('YYYYMM');
    },
  },
  methods: {
    async getLrzx() {
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      this.querySearchConfig[5].selectList = this.lrzxList.map((d) => ({
        value: d.value,
        label: `${d.value} | ${d.label}`,
      }));
      if (this.formData.lrzx) {
        this.formData.lrzx =
          this.querySearchConfig[5].selectList.find((t) => t.value === this.formData.lrzx)?.label || '';
      }
    },
    closeBox() {
      this.boxvisible = false;
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: 'pzmx',
        fileName: '凭证明细导出',
        cxParam: {
          ...this.formData,
          ssyf: Number(this.ssyfDefault),
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    newOrEditRow(row) {
      console.log('row', row);
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        gsh: this.$store.state.zzstz.userInfo?.qydmz || '',
        sszq: Number(dayjs(this.formData.ssyf).format('YYYYMM')),
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      this.delete(params);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async init() {
      this.getLrzx();
      // 缺一个科目编码
      const a = await getHtlx();
      this.htlxList = a.data;
      const b = await getPzlx();
      this.pzlxList = b.data;
      const c = await getKplx();
      this.pzkplxList = c.data;
      const d = await getKhz();
      this.khzList = d.data;
      const e = await getPzmxAllKmbm();
      this.kmbmList = e.data;
      const f = await getAllHtxl();
      this.htxlList = f.data.filter((s) => s.value.startsWith('7') && s.value !== '700');
      this.querySearchConfig[1].selectList = this.htxlList;
      this.querySearchConfig[6].selectList = this.pzlxList;
      this.querySearchConfig[7].selectList = this.pzkplxList;
      this.querySearchConfig[8].selectList = this.kmbmList;
    },
    async query(pm = { flag: false, p: false }) {
      if (!this.formData.htbt) {
        return;
      }
      if (!this.formData.gysbm && !this.formData.khbh) {
        await this.$message.error('请填写客户编码或供应商编码');
        return;
      }
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      if (p) {
        params = { ...p, ...params };
        if (params.ssyf) {
          params.ssyf = Number(dayjs(params.ssyf).format('YYYYMM'));
        }
        this.$refs.queryControl.setParams(p);
      } else {
        params = {
          ...this.formData,
          ssyf: Number(this.ssyfDefault),
          ...params,
        };
      }
      try {
        const { data } = await queryPzmx(params);
        this.tableData = data?.list || [];
        console.log(this.tableData, 'this.tableData');
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'kmmc', this.kmbmList.find((d) => d.value === item.kmbm)?.label);
        });
        this.pagination.total = data?.total || 0;

        if (this.pagination.total > 0) {
          const { data } = await queryPzmxHj(params);
          this.footData =
            [
              {
                bbje: numberToPrice(data?.bbje),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        this.tableData = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params) {
      try {
        const { msg } = await deleteSelected(params);
        console.log('deleteSelected-msg', msg);
        this.$message.success(msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
</style>
