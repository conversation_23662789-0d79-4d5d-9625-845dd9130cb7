<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['新增凭证明细', '编辑凭证明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="{
      content: '保存',
      loading: confirmLoading,
    }"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form ref="forms" :data="formData" labelAlign="top" labelWidth="165px" :rules="rules">
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="所属月份" name="sszq">
              <t-date-picker
                v-model="formData.sszq"
                mode="month"
                placeholder="请填写所属月份"
                style="width: 276px; height: 32px"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="利润中心" name="lrzx">
              <t-select v-model="formData.lrzx" placeholder="请选择利润中心" clearable>
                <t-option
                  v-for="item in lrzxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同类型" name="htlxmc">
              <t-select v-model="formData.htlxmc" placeholder="请选择合同类型" clearable @change="getHtxlByHtlx">
                <t-option
                  v-for="item in htlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div>{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="合同细类" name="htbt">
              <t-select v-model="formData.htbt" placeholder="请选择合同细类" clearable @change="getKmbmByHtxl">
                <t-option
                  v-for="item in htxlList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div id="lrzx_options">{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="凭证类型" name="yhszspzlxDm">
              <t-select v-model="formData.yhszspzlxDm" placeholder="请选择凭证类型" clearable>
                <t-option
                  v-for="item in pzlxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div>{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="凭证开票类型" name="pzkplx">
              <t-select v-model="formData.pzkplx" placeholder="请选择凭证开票类型" clearable>
                <t-option
                  v-for="item in pzkplxList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div>{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="供应商编码" name="zggys">
              <t-input :maxlength="30" v-model="formData.zggys" placeholder="请填写供应商编码" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目编码" name="kmbm">
              <t-select
                v-model="formData.kmbm"
                placeholder="请选择科目编码"
                clearable
                @change="getXzKmmc(formData.kmbm)"
              >
                <t-option v-for="item in kmbmList" :value="item.value" :label="item.value" :key="item.value">
                  <div id="lrzx_options">{{ item.value }} | {{ item.label }}</div>
                </t-option>
              </t-select></t-form-item
            >
          </t-col>
          <t-col :span="4">
            <t-form-item label="科目名称" name="kmmc">
              <t-input :maxlength="100" v-model="formData.kmmc" placeholder="请填写科目名称" readonly
            /></t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="客户组" name="khz">
              <t-select v-model="formData.khz" placeholder="请选择客户组" clearable>
                <t-option
                  v-for="item in khzList"
                  :value="item.value"
                  :label="`${item.value} | ${item.label}`"
                  :key="item.value"
                  ><div>{{ item.value }} | {{ item.label }}</div></t-option
                >
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="this.hylbFlag">
            <t-form-item label="客户编码" name="khbh">
              <t-input
                :maxlength="30"
                v-model="formData.khbh"
                placeholder="请填写客户编码"
                @change="getHylbByKhbh"
                clearable
              ></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-else>
            <t-form-item label="客户编码" name="khbh">
              <t-input :maxlength="30" v-model="formData.khbh" placeholder="请填写客户编码" clearable></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4" v-show="this.hylbFlag">
            <t-form-item label="行业类别" name="hylb">
              <t-select v-model="formData.hylb" placeholder="请选择行业类别" disabled>
                <t-option v-for="item in hylbList" :value="item.value" :label="item.label" :key="item.value" />
              </t-select>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="本币金额" name="bbje">
              <!-- <t-input-number
                v-model="formData.bbje"
                theme="column"
                :decimal-places="2"
                placeholder="请填写本币金额"
                clearable
            /> -->
              <gt-input-money v-model="formData.bbje" theme="normal" align="left" clearable
            /></t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import { getLrzx } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import {
  insertPzmx,
  updatePzmx,
  getHtlx,
  getHtxlByHtlx,
  getKmbmByHtxl,
  getKplx,
  getPzlx,
  getKhz,
} from '@/pages/index/api/tzzx/yhstz/pzmx.js';
import { getHylbByKhbh } from '@/pages/index/api/tzzx/szlgn/khbmyhylbdz.js';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { khbmyhylbdzsjgsh, hylbList } from '../config.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {
      sszq: [{ required: true, message: '必填', type: 'error' }],
      lrzx: [{ required: true, message: '必填', type: 'error' }],
      htlxmc: [{ required: true, message: '必填', type: 'error' }],
      kmbm: [{ required: true, message: '必填', type: 'error' }],
      bbje: [{ required: true, message: '必填', type: 'error' }],
    };
    this.khbmyhylbdzsjgsh = khbmyhylbdzsjgsh;
    this.hylbList = hylbList;
    return {
      hylbFlag: false,
      isVisible: true,
      confirmLoading: false,
      rules: {},
      lrzxList: [],
      value: 1,
      htlxList: [],
      htxlList: [],
      kmbmList: [],
      pzlxList: [],
      pzkplxList: [],
      khzList: [],
      formData: {
        uuid: '',
        gsh: '',
        sszq: '',
        lrzx: '',
        htlxmc: '',
        htbt: '',
        yhszspzlxDm: '',
        pzkplx: '',
        zggys: '',
        kmbm: '',
        kmmc: '',
        khz: '',
        khbh: '',
        hylb: '',
        bbje: '',
      },
    };
  },
  mounted() {
    this.formData.gsh = this.visible.row?.gsh || this.visible.otherObj?.gsh || '';
    console.log('gsh', this.formData.gsh);
    console.log('khbmyhylbdzsjgsh', this.khbmyhylbdzsjgsh);
    if (this.khbmyhylbdzsjgsh.find((d) => d.value === this.formData.gsh)?.label) {
      console.log('gsh', this.formData.gsh);
      this.hylbFlag = true;
    }
    // this.hylbFlag = false;
    this.formData.sszq = dayjs().subtract(1, 'month').format('YYYY-MM');
    this.init();
  },
  watch: {
    'formData.htbt': {
      handler() {
        this.rules = {
          ...this.baseRules,
          ...this.getDynamicRules(),
        };
      },
    },
    'formData.kmbm': {
      handler() {
        this.rules = {
          ...this.baseRules,
          ...this.getDynamicRules(),
        };
      },
    },
  },
  methods: {
    getDynamicRules() {
      const rules = {};
      const { htbt, kmbm } = this.formData;

      // 供应商编码必录规则
      const zggysRequired = [
        ['701', '*'],
        ['702', '*'],
        ['710', '*'],
        ['711', '*'],
        ['712', '!6601221100'],
        ['713', '6051040000'],
        ['714', '6051040000'],
      ].some(([h, k]) => h === htbt && (k === '*' || (k.startsWith('!') ? kmbm !== k.slice(1) : kmbm === k)));

      // 客户编码必录规则
      const khbhRequired = [
        ['703', '*'],
        ['704', '*'],
        ['705', '*'],
        ['706', '*'],
        ['707', '*'],
        ['708', '*'],
        ['712', '6601221100'],
        ['713', '!6051040000'],
        ['714', '!6051040000'],
      ].some(([h, k]) => h === htbt && (k === '*' || (k.startsWith('!') ? kmbm !== k.slice(1) : kmbm === k)));

      if (zggysRequired) {
        rules.zggys = [{ required: true, message: '必填', type: 'error' }];
      }
      if (khbhRequired) {
        rules.khbh = [{ required: true, message: '必填', type: 'error' }];
      }

      return rules;
    },
    async getHylbByKhbh() {
      try {
        const { data } = await getHylbByKhbh([this.formData.khbh]);
        this.formData.hylb = data[0]?.hylb;
        console.log('data[0]', data[0]);
      } catch (e) {
        console.log('e', e);
      }
    },
    getXzKmmc(kmbm) {
      this.formData.kmmc = this.kmbmList.find((item) => item.value === kmbm)?.label;
    },
    // async getLrzx() {
    //   const { code, data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
    //   if (code === 1) {
    //     this.lrzxList = data;
    //     if (this.lrzxList.length === 1) {
    //       this.formData.lrzx = this.lrzxList[0].value;
    //     } else {
    //       this.formData.lrzx = '';
    //     }
    //   }
    // },
    async init() {
      // 避免异步加载因网络等延迟导致已加载数据被覆盖掉问题
      const { data } = await getLrzx({ nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '' });
      this.lrzxList = data;
      if (this.lrzxList.length === 1) {
        this.formData.lrzx = this.lrzxList[0].value;
      } else {
        this.formData.lrzx = '';
      }
      const a = await getHtlx();
      this.htlxList = a.data;
      const b = await getPzlx();
      this.pzlxList = b.data;
      const c = await getKplx();
      this.pzkplxList = c.data;
      const d = await getKhz();
      this.khzList = d.data;

      this.rules = this.baseRules;
      console.log('this.visible', this.visible);
      let htbtTemp = '';
      let kmbmTemp = '';
      let kmmcTemp = '';
      if (this.visible.row?.uuid) {
        this.formData = this.visible.row;
        htbtTemp = this.visible.row.htbt;
        kmbmTemp = this.visible.row.kmbm;
        kmmcTemp = this.visible.row.kmmc;
      }
      await this.getHtxlByHtlx();
      this.formData.htbt = htbtTemp;
      await this.getKmbmByHtxl();
      this.formData.kmbm = kmbmTemp;
      this.formData.kmmc = kmmcTemp;
    },
    async getHtxlByHtlx() {
      // this.formData.htbt = '';
      const { data } = await getHtxlByHtlx({ htlxmc: this.formData?.htlxmc || '' });
      this.htxlList = data;
      // 新增校验逻辑
      const currentHtbt = this.formData.htbt;
      if (currentHtbt) {
        const exists = this.htxlList.some((item) => item.value === currentHtbt);
        if (!exists) {
          this.formData.htbt = '';
          this.getKmbmByHtxl(); // 触发科目编码清空
        }
      }
      if (this.htxlList.length === 1) {
        this.formData.htbt = this.htxlList[0].value;
        this.getKmbmByHtxl();
      }
    },
    async getKmbmByHtxl() {
      // this.formData.kmbm = '';
      // this.formData.kmmc = '';
      const { data } = await getKmbmByHtxl({ htbt: this.formData?.htbt || '' });
      this.kmbmList = data;
      // 新增校验逻辑
      const currentKmbm = this.formData.kmbm;
      if (currentKmbm) {
        const exists = this.kmbmList.some((item) => item.value === currentKmbm);
        if (!exists) {
          this.formData.kmbm = '';
          this.formData.kmmc = '';
        }
      }

      if (this.kmbmList.length === 1) {
        this.formData.kmbm = this.kmbmList[0].value;
        this.formData.kmmc = this.kmbmList[0].label;
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      // 合并动态规则
      this.rules = {
        ...this.baseRules,
        ...this.getDynamicRules(),
      };
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        if (this.formData.zggys || this.formData.khbh) {
          const p = {};
          [
            'uuid',
            'gsh',
            'sszq',
            'lrzx',
            'htlxmc',
            'htbt',
            'yhszspzlxDm',
            'pzkplx',
            'zggys',
            'kmbm',
            'kmmc',
            'khz',
            'khbh',
            'bbje',
            'hylb',
          ].forEach((d) => {
            p[d] = this.formData?.[d] ?? null;
          });
          const params = { ...p, ...this.visible.otherObj };
          try {
            if (this.visible.pageType) {
              await updatePzmx(params);
            } else {
              await insertPzmx(params);
            }
            this.$message.success(['新增成功', '修改成功'][this.visible.pageType]);
            if (this.visible.pageType) {
              this.$emit('updatePzmx');
            } else {
              this.$emit('updatePzmx', { flag: true }); // 新增回到第一页
            }
            this.isVisible = false;
          } catch (e) {
            console.error(e);
          } finally {
            this.confirmLoading = false;
          }
        } else {
          this.$message.error('客户编号和供应商编码不可同时为空，请注意填写规范。');
          this.confirmLoading = false;
        }
      } else {
        this.confirmLoading = false;
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
