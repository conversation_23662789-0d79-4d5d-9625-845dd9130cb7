import dayjs from 'dayjs';

const khbmyhylbdzsjgsh = [
  { value: '6800', label: 1 },
  { value: '7000', label: 1 },
];
const hylbList = [
  { value: '01', label: '批发行业' },
  { value: '02', label: '零售行业' },
];
const kmbmList = [
  { value: '2202010100', label: '应付账款-一般应付-供应商' },
  { value: '2202020000', label: '应付账款-应付暂估-一般暂估' },
  { value: '6001010100', label: '主营业务收入-成品-集成' },
  { value: '6001010200', label: '主营业务收入-成品-手工' },
  { value: '6001030000', label: '主营业务收入-仓储服务费' },
  { value: '6001030100', label: '主营业务收入-仓储服务费-仓储' },
  { value: '6001030200', label: '主营业务收入-仓储服务费-操作' },
  { value: '6001030300', label: '主营业务收入-仓储服务费-运输' },
  { value: '6001030400', label: '主营业务收入-仓储服务费-包辅材' },
  { value: '6041000000', label: '销售折扣与折让-集成' },
  { value: '6051010100', label: '其他业务收入-辅料-集成' },
  { value: '6051010200', label: '其他业务收入-辅料-手工' },
  { value: '6051030000', label: '其他业务收入-促销品-集成' },
  { value: '6051050100', label: '其他业务收入-服务费-仓储服务' },
  { value: '6601150200', label: '期间费用-运杂费-销售运杂费' },
  { value: '6601150300', label: '期间费用-运杂费-内部调拨运杂费' },
  { value: '6601160000', label: '期间费用-财产保险费' },
  { value: '6601221100', label: '期间费用-服务费-仓储服务费' },
  { value: '6601221600', label: '期间费用-服务费-联营扣点' },
  { value: '6601221700', label: '期间费用-服务费-类直营扣点' },
];
export const querySearchConfigOneRules = {
  ssyf: [{ required: true, message: '必填项', type: 'error' }],
  htbt: [{ required: true, message: '必填项', type: 'error' }],
};
export const querySearchConfig = [
  {
    label: '所属月份',
    key: 'ssyf',
    type: 'datepicker',
    mode: 'month',
    value: dayjs().subtract(1, 'month').format('YYYY-MM'),
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '合同细类',
    key: 'htbt',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '客户编码',
    key: 'khbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '凭证编号',
    key: 'kjpzbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '供应商编码',
    key: 'gysbm',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'lrzx',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '凭证类型',
    key: 'yhszspzlxDm',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '凭证开票类型',
    key: 'pzkplx',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '科目编码',
    key: 'kmbm',
    type: 'select',
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: row.ywqdDm !== '01' }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 110,
    colKey: 'gsh',
    title: '公司号',
  },
  {
    width: 120,
    colKey: 'lrzx',
    title: '利润中心',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'htlxmc',
    title: '合同类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'htbt',
    title: '合同细类',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'kjpzbh',
    title: '凭证编号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'yhszspzlxDm',
    title: '凭证类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'pzkplx',
    title: '凭证开票类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'khz',
    title: '客户组',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
      ellipsis: {
        theme: 'light',
        placement: 'bottom',
      },
    },
  },
  {
    width: 140,
    colKey: 'khbh',
    title: '客户编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 140,
    colKey: 'hylb',
    title: '行业类别',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 150,
    colKey: 'gysbm',
    title: '供应商编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 160,
    colKey: 'kmbm',
    title: '科目编码',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 150,
    colKey: 'kmmc',
    title: '科目名称',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'right',
    width: 140,
    colKey: 'bbje',
    title: '本币金额',
    fixed: 'right',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];

export { kmbmList, khbmyhylbdzsjgsh, hylbList };
