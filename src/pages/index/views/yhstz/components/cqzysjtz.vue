<!--
 * @Descripttion: 台账-印花税-产权转移书据台账
 * @Version: 1.0
 * @Author: ljf
 * @Date: 2024-04-17 10:00:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div class="container">
    <div style="display: flex; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div style="margin-right: 12px">所属账期</div>
        <t-date-picker v-model="sszq" mode="month" able-time-picker allow-input clearable />
      </div>
      <div class="btns">
        <!-- <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="ckdg">查看底稿</t-button>
        <t-image-viewer v-model="pdf_visible" :images="[picSrc]"> </t-image-viewer> -->
        <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="query"
          >从企业财务系统生成台账</t-button
        >
        <t-button style="margin-left: 12px" class="btn" theme="primary" type="submit" @click="save">保存</t-button>
        <!-- <t-button
          style="margin-left: 12px"
          class="btn"
          variant="outline"
          theme="primary"
          type="submit"
          @click="download"
          >模板下载</t-button
        > -->
        <!-- <t-button style="margin-left: 12px" class="btn" variant="outline" theme="primary" type="submit" @click="query"
          >模板导入</t-button
        > -->
      </div>
    </div>

    <div class="tableBtn">
      <t-button class="btn" theme="primary" type="submit" @click="openZzlrym">增行</t-button>
      <t-button class="btn" variant="outline" theme="primary" type="submit" @click="delRow">删行</t-button>
    </div>

    <div>
      <t-table
        :key="id"
        ref="myTable"
        row-key="key"
        width="80%"
        :data="cqzysjList"
        :columns="yhszzColumns"
        bordered
        lazyLoad
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
        :pagination="pagination"
      >
        <!-- <template #zpje="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.zpje }}</span>
                </div>
              </template>
              <template #zpse="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.zpse }}</span>
                </div>
              </template>
              <template #qtfpje="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.qtfpje }}</span>
                </div>
              </template>
              <template #qtfpse="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.qtfpse }}</span>
                </div>
              </template>
              <template #wkpje="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.wkpje }}</span>
                </div>
              </template>
              <template #wkpse="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.wkpse }}</span>
                </div>
              </template>
              <template #nsjctzje="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.nsjctzje }}</span>
                </div>
              </template>
              <template #nsjctzse="{ row }">
                <div class="specText" style="float: right">
                  <span @click="openXxfpmx(row)" style="color: #0052d9; text-decoration: underline">{{ row.nsjctzse }}</span>
                </div>
              </template> -->
        <template #operation="{ row }">
          <t-link theme="primary" hover="color" @click="edit(row)"> 编辑 </t-link>
        </template>
      </t-table>
      <div>
        <t-dialog :visible="visible" :confirm-btn="null" :cancel-btn="null" :close-btn="false" width="1200">
          <div slot="header" class="tableH">
            <div class="title">
              录入印花税产权转移书据台账
              <t-icon class="closeBtn" name="close" @click="cancel" />
            </div>
          </div>
          <div class="con">
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据编号</span><br />
                  <t-input
                    v-model="formData.bh"
                    theme="normal"
                    placeholder="请填写书据编号"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据名称</span><br />
                  <t-input
                    v-model="formData.mc"
                    theme="normal"
                    placeholder="请填写书据名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据类型</span><br />
                  <t-select v-model="formData.shujvlxDm" placeholder="请选择书据类型" clearable>
                    <t-option
                      v-for="item in sjlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据生成日期</span><br />
                  <t-date-picker
                    v-model="formData.sjscrq1"
                    placeholder="请填写书据生成日期"
                    style="width: 276px; height: 32px"
                    clearable
                  ></t-date-picker>
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人姓名/名称</span><br />
                  <t-input
                    v-model="formData.cqrmc"
                    theme="normal"
                    placeholder="请填写产权人姓名/名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人证件号码/统一社会信用代码</span><br />
                  <t-input
                    v-model="formData.cqrzjhm"
                    theme="normal"
                    placeholder="请填写产权人证件号码/统一社会信用代码"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人地址</span>
                  <t-input
                    v-model="formData.cqrdz"
                    theme="normal"
                    placeholder="请填写产权人地址"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人联系方式</span><br />
                  <t-input
                    v-model="formData.cqrlxfs"
                    theme="normal"
                    placeholder="请填写产权人联系方式"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象名称</span><br />
                  <t-input
                    v-model="formData.zydxmc"
                    theme="normal"
                    placeholder="请填写转移对象名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象类型</span><br />
                  <t-select v-model="formData.zydxlxDm" placeholder="请选择转移对象类型" clearable>
                    <t-option
                      v-for="item in zydxlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象详情描述</span><br />
                  <t-input
                    v-model="formData.zydxxqms"
                    theme="normal"
                    placeholder="请填写转移对象详情描述"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移方式</span><br />
                  <t-select v-model="formData.zyfsDm" placeholder="请选择转移方式" clearable>
                    <t-option
                      v-for="item in zyfsList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移原因</span><br />
                  <t-input
                    v-model="formData.zyyy"
                    theme="normal"
                    placeholder="请填写转移原因"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移日期</span><br />
                  <t-date-picker
                    v-model="formData.zyrq"
                    placeholder="请填写转移日期"
                    style="width: 276px; height: 32px"
                    clearable
                  ></t-date-picker>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移地点</span><br />
                  <t-input
                    v-model="formData.zydd"
                    theme="normal"
                    placeholder="请填写转移地点"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">转移价值</span><br />
                  <t-input-number
                    v-model="formData.zyjz"
                    type="number"
                    theme="normal"
                    placeholder="请填写转移价值"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">相关费用</span><br />
                  <t-input-number
                    v-model="formData.xgfyje"
                    type="number"
                    theme="normal"
                    placeholder="请填写相关费用"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权证明文件</span><br />
                  <t-input
                    v-model="formData.cqzmwj"
                    theme="normal"
                    placeholder="请填写产权证明文件"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移合同/协议</span><br />
                  <t-input
                    v-model="formData.zyht"
                    theme="normal"
                    placeholder="请填写转移合同/协议"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">其他相关证明文件</span><br />
                  <t-input
                    v-model="formData.qtxgzmwj"
                    theme="normal"
                    placeholder="请填写其他相关证明文件"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">备注</span><br />
                  <t-input
                    v-model="formData.bz"
                    theme="normal"
                    placeholder="请填写备注"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">附加信息</span><br />
                  <t-input
                    v-model="formData.fjxx2"
                    theme="normal"
                    placeholder="请填写附加信息"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div />
              </t-col>
              <t-col :span="3">
                <div />
              </t-col>
            </t-row>
          </div>
          <div class="btnsleft">
            <t-button class="btn btn1" theme="default" variant="base" @click="cancel">取消</t-button>
            <t-button class="btn" @click="addRow">确定</t-button>
          </div>
        </t-dialog>
      </div>
      <div>
        <t-dialog :visible="bjvisible" :confirm-btn="null" :cancel-btn="null" :close-btn="false" width="1200">
          <div slot="header" class="tableH">
            <div class="title">编辑印花税产权转移书据台账<t-icon class="closeBtn" name="close" @click="cancel" /></div>
          </div>
          <div class="con">
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据编号</span><br />
                  <t-input
                    v-model="bjformData.bh"
                    theme="normal"
                    placeholder="请填写书据编号"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据名称</span><br />
                  <t-input
                    v-model="bjformData.mc"
                    theme="normal"
                    placeholder="请填写书据名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据类型</span><br />
                  <t-select v-model="bjformData.shujvlxDm" placeholder="请选择书据类型" clearable>
                    <t-option
                      v-for="item in sjlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item frist_cow_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">书据生成日期</span><br />
                  <t-date-picker
                    v-model="bjformData.sjscrq1"
                    placeholder="请填写书据生成日期"
                    style="width: 276px; height: 32px"
                    clearable
                  ></t-date-picker>
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人姓名/名称</span><br />
                  <t-input
                    v-model="bjformData.cqrmc"
                    theme="normal"
                    placeholder="请填写产权人姓名/名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人证件号码/统一社会信用代码</span><br />
                  <t-input
                    v-model="bjformData.cqrzjhm"
                    theme="normal"
                    placeholder="请填写产权人证件号码/统一社会信用代码"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人地址</span>
                  <t-input
                    v-model="bjformData.cqrdz"
                    theme="normal"
                    placeholder="请填写产权人地址"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权人联系方式</span><br />
                  <t-input
                    v-model="bjformData.cqrlxfs"
                    theme="normal"
                    placeholder="请填写产权人联系方式"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象名称</span><br />
                  <t-input
                    v-model="bjformData.zydxmc"
                    theme="normal"
                    placeholder="请填写转移对象名称"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象类型</span><br />
                  <t-select v-model="bjformData.zydxlxDm" placeholder="请选择转移对象类型" clearable>
                    <t-option
                      v-for="item in zydxlxList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移对象详情描述</span><br />
                  <t-input
                    v-model="bjformData.zydxxqms"
                    theme="normal"
                    placeholder="请填写转移对象详情描述"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移方式</span><br />
                  <t-select v-model="bjformData.zyfsDm" placeholder="请选择转移方式" clearable>
                    <t-option
                      v-for="item in zyfsList"
                      :value="item.value"
                      :label="item.label"
                      :key="item.value"
                    ></t-option>
                  </t-select>
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移原因</span><br />
                  <t-input
                    v-model="bjformData.zyyy"
                    theme="normal"
                    placeholder="请填写转移原因"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移日期</span><br />
                  <t-date-picker
                    v-model="bjformData.zyrq"
                    placeholder="请填写转移日期"
                    style="width: 276px; height: 32px"
                    clearable
                  ></t-date-picker>
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移地点</span><br />
                  <t-input
                    v-model="bjformData.zydd"
                    theme="normal"
                    placeholder="请填写转移地点"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span style="color: red">*</span>
                  <span class="formtitle">转移价值</span><br />
                  <t-input-number
                    v-model="bjformData.zyjz"
                    type="number"
                    theme="normal"
                    placeholder="请填写转移价值"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">相关费用</span><br />
                  <t-input-number
                    v-model="bjformData.xgfyje"
                    type="number"
                    theme="normal"
                    placeholder="请填写相关费用"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">产权证明文件</span><br />
                  <t-input
                    v-model="bjformData.cqzmwj"
                    theme="normal"
                    placeholder="请填写产权证明文件"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">转移合同/协议</span><br />
                  <t-input
                    v-model="bjformData.zyht"
                    theme="normal"
                    placeholder="请填写转移合同/协议"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">其他相关证明文件</span><br />
                  <t-input
                    v-model="bjformData.qtxgzmwj"
                    theme="normal"
                    placeholder="请填写其他相关证明文件"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
            </t-row>
            <t-row :gutter="16" class="row">
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">备注</span><br />
                  <t-input
                    v-model="bjformData.bz"
                    theme="normal"
                    placeholder="请填写备注"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div class="form_item">
                  <span class="formtitle">附加信息</span><br />
                  <t-input
                    v-model="bjformData.fjxx2"
                    theme="normal"
                    placeholder="请填写附加信息"
                    style="width: 276px; height: 32px"
                    clearable
                  />
                </div>
              </t-col>
              <t-col :span="3">
                <div />
              </t-col>
              <t-col :span="3">
                <div />
              </t-col>
            </t-row>
          </div>
          <div class="btnsleft">
            <t-button class="btn btn1" theme="default" variant="base" @click="cancel">取消</t-button>
            <t-button class="btn" @click="updateRow">确定</t-button>
          </div>
        </t-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import api from '@/pages/index/api/tzzx/yhstz/cqzysjtz.js';
import dayjs from 'dayjs';
// import { Select } from 'tdesign-vue';

export default {
  data() {
    return {
      // picSrc: `${document.location.origin}/znsb/view/tzzx/fppy.png`,
      // pdf_visible: false,
      minHeight: document.documentElement.clientHeight - 160,
      visible: false,
      bjvisible: false,
      cxformData: {
        sszq: dayjs().subtract(1, 'month').format('YYYY-MM'),
      },
      formData: {
        uuid: '',
        bh: '',
        mc: '',
        shujvlxDm: '',
        sjscrq1: '',
        cqrmc: '',
        cqrzjhm: '',
        cqrdz: '',
        cqrlxfs: '',
        zydxmc: '',
        zydxlxDm: '',
        zydxxqms: '',
        zyfsDm: '',
        zyyy: '',
        zyrq: '',
        zydd: '',
        zyjz: '',
        xgfyje: '',
        cqzmwj: '',
        zyht: '',
        qtxgzmwj: '',
        bz: '',
        fjxx2: '',
      },
      bjformData: {
        uuid: '',
        bh: '',
        mc: '',
        shujvlxDm: '',
        sjscrq1: '',
        cqrmc: '',
        cqrzjhm: '',
        cqrdz: '',
        cqrlxfs: '',
        zydxmc: '',
        zydxlxDm: '',
        zydxxqms: '',
        zyfsDm: '',
        zyyy: '',
        zyrq: '',
        zydd: '',
        zyjz: '',
        xgfyje: '',
        cqzmwj: '',
        zyht: '',
        qtxgzmwj: '',
        bz: '',
        fjxx2: '',
      },
      delformData: [],
      sszq: dayjs().subtract(1, 'month').format('YYYY-MM'),
      selectedRowKeys: [],
      sjlxList: [{ value: '01', label: '土地使用权转让书据' }],
      zydxlxList: [{ value: '01', label: '土地' }],
      zyfsList: [{ value: '01', label: '买卖' }],
      cqzysjList: [],

      yhszzColumns: [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: 60,
        },
        {
          align: 'center',
          colKey: 'sjjbxx',
          title: '一、书据基本信息',
          children: [
            {
              align: 'center',
              colKey: '1',
              title: '书据编号',
              children: [
                {
                  align: 'center',
                  colKey: 'bh',
                  title: '1',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '2',
              title: '书据名称',
              children: [
                {
                  align: 'center',
                  colKey: 'mc',
                  title: '2',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '3',
              title: '书据类型',
              children: [
                {
                  align: 'center',
                  colKey: 'shujvlxDm',
                  title: '3',
                  width: 120,
                  cell: (h, { row }) => this.sjlxList.find((t) => t.value === row.shujvlxDm)?.label,
                },
              ],
            },
            {
              align: 'center',
              colKey: '4',
              title: '书据生成日期',
              children: [
                {
                  align: 'center',
                  colKey: 'sjscrq1',
                  title: '4',
                  width: 120,
                  cell: (h, { row }) => <div>{row.sjscrq1 ? dayjs(row.sjscrq1).format('YYYY-MM-DD') : ''}</div>,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'cqrxx',
          title: '二、产权人信息',
          children: [
            {
              align: 'center',
              colKey: '5',
              title: '产权人姓名/名称',
              children: [
                {
                  align: 'center',
                  colKey: 'cqrmc',
                  title: '5',
                  width: 140,
                },
              ],
            },
            {
              align: 'center',
              colKey: '6',
              title: '产权人证件号码/统一社会信用代码',
              children: [
                {
                  align: 'center',
                  colKey: 'cqrzjhm',
                  title: '6',
                  width: 200,
                },
              ],
            },
            {
              align: 'center',
              colKey: '7',
              title: '产权人地址',
              children: [
                {
                  align: 'center',
                  colKey: 'cqrdz',
                  title: '7',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '8',
              title: '产权人联系方式',
              children: [
                {
                  align: 'center',
                  colKey: 'cqrlxfs',
                  title: '8',
                  width: 120,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'zydxms',
          title: '三、转移对象描述',
          children: [
            {
              align: 'center',
              colKey: '9',
              title: '转移对象名称',
              children: [
                {
                  align: 'center',
                  colKey: 'zydxmc',
                  title: '9',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '10',
              title: '转移对象类型',
              children: [
                {
                  align: 'center',
                  colKey: 'zydxlxDm',
                  title: '10',
                  width: 120,
                  cell: (h, { row }) => this.zydxlxList.find((t) => t.value === row.zydxlxDm)?.label,
                },
              ],
            },
            {
              align: 'center',
              colKey: '11',
              title: '转移对象详情描述',
              children: [
                {
                  align: 'center',
                  colKey: 'zydxxqms',
                  title: '11',
                  width: 140,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'zyfsjyy',
          title: '四、转移方式及原因',
          children: [
            {
              align: 'center',
              colKey: '12',
              title: '转移方式',
              children: [
                {
                  align: 'center',
                  colKey: 'zyfsDm',
                  title: '12',
                  width: 120,
                  cell: (h, { row }) => this.zyfsList.find((t) => t.value === row.zyfsDm)?.label,
                },
              ],
            },
            {
              align: 'center',
              colKey: '13',
              title: '转移原因',
              children: [
                {
                  align: 'center',
                  colKey: 'zyyy',
                  title: '13',
                  width: 120,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'zyrqjdd',
          title: '五、转移日期及地点',
          children: [
            {
              align: 'center',
              colKey: '14',
              title: '转移日期',
              children: [
                {
                  align: 'center',
                  colKey: 'zyrq',
                  title: '14',
                  width: 120,
                  cell: (h, { row }) => <div>{row.zyrq ? dayjs(row.zyrq).format('YYYY-MM-DD') : ''}</div>,
                },
              ],
            },
            {
              align: 'center',
              colKey: '15',
              title: '转移地点',
              children: [
                {
                  align: 'center',
                  colKey: 'zydd',
                  title: '15',
                  width: 120,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'zyjzjfy',
          title: '六、转移价值及费用',
          children: [
            {
              align: 'center',
              colKey: '16',
              title: '转移价值',
              children: [
                {
                  align: 'center',
                  colKey: 'zyjz',
                  title: '16',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '17',
              title: '相关费用',
              children: [
                {
                  align: 'center',
                  colKey: 'xgfyje',
                  title: '17',
                  width: 120,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'xgzmwj',
          title: '七、相关证明文件',
          children: [
            {
              align: 'center',
              colKey: '18',
              title: '产权证明文件',
              children: [
                {
                  align: 'center',
                  colKey: 'cqzmwj',
                  title: '18',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '19',
              title: '转移合同/协议',
              children: [
                {
                  align: 'center',
                  colKey: 'zyht',
                  title: '19',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '20',
              title: '其他相关证明文件',
              children: [
                {
                  align: 'center',
                  colKey: 'qtxgzmwj',
                  title: '20',
                  width: 140,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'bzjfjxx',
          title: '八、备注及附加信息',
          children: [
            {
              align: 'center',
              colKey: '21',
              title: '备注',
              children: [
                {
                  align: 'center',
                  colKey: 'bz',
                  title: '21',
                  width: 120,
                },
              ],
            },
            {
              align: 'center',
              colKey: '22',
              title: '附加信息',
              children: [
                {
                  align: 'center',
                  colKey: 'fjxx2',
                  title: '22',
                  width: 120,
                },
              ],
            },
          ],
        },
        {
          align: 'center',
          colKey: 'operation',
          title: '操作',
          width: 70,
          foot: '-',
        },
      ],
      id: dayjs().unix(),
      tableLayout: true,
      showHeader: true,
      collapsed: true,
      maxHeight: '450',
      mxPageVisible: false,
      mxDialogHeight: '70%',
      mxDialogWidth: '80%',
    };
  },
  created() {
    this.init();
  },
  computed: {
    pagination() {
      return {
        defaultCurrent: 1,
        defaultPageSize: 10,
        total: 0,
        showJumper: true,
        onChange: (pageInfo) => {
          this.pagination.defaultCurrent = pageInfo.current;
          this.pagination.defaultPageSize = pageInfo.pageSize;
          this.query();
          console.log(pageInfo, 'query');
        },
      };
    },
    iconName() {
      return this.collapsed ? 'chevron-right' : 'chevron-left';
    },
  },
  methods: {
    // ckdg() {
    //   console.log('route', this.picSrc);
    //   this.pdf_visible = true;
    // },
    edit(row) {
      this.bjvisible = true;
      this.bjformData = row;
    },
    openXxfpmx(row) {
      this.$emit('openXxfpmx', row);
    },
    openZzlrym() {
      this.formData.bh = '';
      this.formData.mc = '';
      this.formData.shujvlxDm = '';
      this.formData.sjscrq1 = '';
      this.formData.cqrmc = '';
      this.formData.cqrzjhm = '';
      this.formData.cqrdz = '';
      this.formData.cqrlxfs = '';
      this.formData.zydxmc = '';
      this.formData.zydxlxDm = '';
      this.formData.zydxxqms = '';
      this.formData.zyfsDm = '';
      this.formData.zyyy = '';
      this.formData.zyrq = '';
      this.formData.zydd = '';
      this.formData.zyjz = '';
      this.formData.xgfyje = '';
      this.formData.cqzmwj = '';
      this.formData.zyht = '';
      this.formData.qtxgzmwj = '';
      this.formData.bz = '';
      this.formData.fjxx2 = '';
      this.visible = true;
    },
    async delRow() {
      this.checkBox.forEach((item) => {
        const index = this.cqzysjList.findIndex((i) => i.key === item.key);
        this.cqzysjList.splice(index, 1);
        this.delformData.push({
          uuid: item.uuid,
          djxh: item.djxh,
          sszq: item.sszq,
          scbz: '1',
        });
        this.pagination.total = this.cqzysjList.length;
      });

      this.id = dayjs().unix();
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
    },
    updateRow() {
      this.bjvisible = false;
    },
    addRow() {
      this.cqzysjList.push({
        key: this.getKey(),
        index: this.cqzysjList.length, // 数据索引 就应该 从 0 开始
        djxh: '91511622762319341', // 暂时写死
        sszq: this.sszq.substring(0, 4) + this.sszq.substring(5, 7),
        bh: this.formData.bh,
        mc: this.formData.mc,
        shujvlxDm: this.formData.shujvlxDm,
        sjscrq1: this.formData.sjscrq1,
        cqrmc: this.formData.cqrmc,
        cqrzjhm: this.formData.cqrzjhm,
        cqrdz: this.formData.cqrdz,
        cqrlxfs: this.formData.cqrlxfs,
        zydxmc: this.formData.zydxmc,
        zydxlxDm: this.formData.zydxlxDm,
        zydxxqms: this.formData.zydxxqms,
        zyfsDm: this.formData.zyfsDm,
        zyyy: this.formData.zyyy,
        zyrq: this.formData.zyrq,
        zydd: this.formData.zydd,
        zyjz: this.formData.zyjz,
        xgfyje: this.formData.xgfyje,
        cqzmwj: this.formData.cqzmwj,
        zyht: this.formData.zyht,
        qtxgzmwj: this.formData.qtxgzmwj,
        bz: this.formData.bz,
        fjxx2: this.formData.fjxx2,
      });
      console.log(this.formData, 'xjformdata');
      this.checkBox = [];
      this.pagination.total = this.cqzysjList.length;
      this.visible = false;
    },
    onClickCloseBtn(context) {
      console.log('点击了关闭按钮', context);
    },
    close(context) {
      console.log('关闭弹窗，点击关闭按钮、按下ESC、点击蒙层等触发', context);
      this.visible = false;
      this.bjvisible = false;
    },
    cancel() {
      this.visible = false;
      this.bjvisible = false;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    getKey() {
      return Math.random().toString().split('.')[1];
    },
    async init() {
      const sszq = this.sszq.substring(0, 4) + this.sszq.substring(5, 7);
      console.log(sszq, 'sszq');
      this.cxformData.djxh = '91511622762319341';
      this.cxformData.sszq = sszq;
      this.cxformData.pageSize = this.pagination.defaultPageSize;
      this.cxformData.pageNum = this.pagination.defaultCurrent;
      const params = this.cxformData;
      const { code, msg, data } = await api.init(params);
      const { returnCode } = data.returnCode;
      const { returnMsg } = data.returnMsg;
      console.log('data', data);
      console.log('code', code);
      console.log('returnCode', returnCode);
      if (code === 0) {
        console.log(data.data.records, 'data.data.records');
        this.cqzysjList = data.data.records || [];
        this.defaultLength = this.cqzysjList.length || 0;
        // this.slList = data.data.slList;
        // this.zsfsList = data.data.zsfsList;
        // this.zsxmList = data.data.zsxmList;
        this.cqzysjList.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        this.pagination.total = data.data.pageTotal;
      } else if (msg) {
        console.log(msg);
      } else {
        console.log(returnMsg);
        this.$message.warning(returnMsg);
      }
    },
    async query() {
      const sszq = this.sszq.substring(0, 4) + this.sszq.substring(5, 7);
      console.log(sszq, 'sszq');
      this.formData.djxh = '91511622762319341';
      this.formData.sszq = sszq;
      this.formData.pageSize = this.pagination.defaultPageSize;
      this.formData.pageNum = this.pagination.defaultCurrent;
      const params = this.formData;
      const { data } = await api.query(params);
      if (data.data === null) {
        this.cqzysjList = [];
        this.pagination.total = 0;
      } else {
        console.log(data.data.records, 'data.data.records');
        this.cqzysjList = data.data.records || [];
        this.defaultLength = this.cqzysjList.length || 0;
        this.cqzysjList.forEach((item, index) => {
          this.$set(item, 'index', index);
          this.$set(item, 'key', this.getKey());
        });
        this.pagination.total = data.data.pageTotal;
      }
    },
    async save() {
      this.delformData.forEach((item) => {
        this.cqzysjList.push({
          uuid: item.uuid,
          djxh: item.djxh,
          sszq: item.sszq,
          scbz: '1',
        });
      });
      const params = this.cqzysjList;
      const { msg, data } = await api.add(params);
      const { returnCode } = data;
      const { returnMsg } = data;
      console.log('data', data);
      console.log('returnCode', returnCode);
      // console.log('returnCode', returnCode);
      if (returnCode === 0) {
        console.log(returnMsg);
        this.$message.success(returnMsg);
      } else if (returnMsg) {
        console.log(returnMsg);
        this.$message.warning(returnMsg);
      } else {
        console.log(msg);
        this.$message.warning(msg);
      }
      this.init();
      this.delformData = [];
    },
    download() {
      window.open('http://localhost:9002/znsb/api/tzzx/yhszz/v1/yhsCqzysjMbDownload?t=1713778006206');
    },
  },
};
</script>
<style scoped lang="less">
.dialog .t-dialog__ctx .tdgv-wrapper .t-dialog .t-dialog__body {
  padding: 0;
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog {
      padding: 0;
      border-radius: 2px;
    }
  }
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog__footer {
      padding: 0;
    }
  }
}
.tdgv-wrapper {
  .t-collapse-panel {
    /deep/.t-collapse-panel__header {
      .t-collapse-panel__header-content {
        font-size: 20px;
        font-weight: 400;
        color: #333;
      }
    }
  }
}
.container {
  width: 100%;
  padding: 16px 24px;
  .header {
    div {
      display: inline-block;
      vertical-align: middle;
    }
    .sszq {
      padding-right: 8px;
    }
    .btns {
      float: right;
      .btn {
        margin-left: 12px;
      }
    }
  }
  .tableBtn {
    margin: 20px 0;
    .btn {
      margin-right: 12px;
    }
  }
  .pag {
    margin: 20px 0;
  }
  .edit {
    color: #4285f4;
    cursor: pointer;
  }
}
.tableH {
  width: 100%;
}
.title {
  position: relative;
  width: 100%;
  padding: 15px;
  font-family: PingFangSC-SNaNpxibold;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0;
  color: #000;
  border-bottom: 1px solid rgb(233, 231, 231);
  .closeBtn {
    position: absolute;
    top: 24px;
    right: 24px;
    display: inline-block;
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
  }
  .closeBtn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.btnsleft {
  display: flex;
  height: 44px;
  padding-top: 12px;
  padding-right: 24px;
  margin-top: 24px;
  border-top: 1px solid rgb(233, 231, 231);
  justify-content: flex-end;
  .btn {
    width: 60px;
    height: 32px;
    padding: 0;
    line-height: 32px;
  }
  .btn1 {
    margin-right: 12px;
  }
}
.con {
  padding-right: 20px;
  padding-left: 20px;
}
// .row {
//   margin-bottom: 30px;
// }
.search-line {
  display: block;
}
.specText {
  cursor: pointer;
}
.formtitle {
  font-size: 14px;
}
.form_item {
  width: 276px;
  height: 58px;
  margin-top: 24px;
}
.frist_cow_item {
  margin-top: 0;
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog__position.t-dialog--top {
      position: fixed;
      left: 50%;
      padding-top: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
