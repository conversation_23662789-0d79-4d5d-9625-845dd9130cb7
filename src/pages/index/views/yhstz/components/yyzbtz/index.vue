<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <search-control-panel
      ref="queryControl"
      class="znsbHeadqueryDiv"
      :config="querySearchConfig"
      @search="query({ flag: true })"
      :colNum="4"
      @formChange="(v) => (formData = v)"
      :LABEL_THRESHOLD="9"
      :labelWidth="'calc(8em + 32px)'"
      :formRules="querySearchConfigOneRules"
    >
      <template #t1><span></span></template>
      <template #t2><span></span></template>
    </search-control-panel>
    <div class="queryBtns">
      <gt-space size="10px">
        <ExtractDataButton
          :sszq="sszqToExtract"
          :readyStatus="readyStatus"
          :ywtsMsg="'营业账簿数据'"
          :ywlx="'yyzb'"
          @query="query"
        />
        <t-button
          variant="outline"
          theme="primary"
          :disabled="buttonvisible"
          @click="saveTz"
          :loading="savebuttonLoading"
          ><SaveIcon slot="icon" />保存台账</t-button
        >
        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="exportExcl"
          ><DownloadIcon slot="icon" />导出</t-button
        >
        <QsbButton />
      </gt-space>
      <t-button
        variant="outline"
        theme="primary"
        v-if="fromName"
        @click="$emit('openPage', { type: fromName, notQuery: true })"
        ><RollbackIcon slot="icon" />返回</t-button
      >
    </div>
    <div class="znsbSbBodyDiv" @click="onTableclick">
      <t-table
        ref="tableRef"
        row-key="key"
        hover
        :data="tableData"
        :columns="dataColumns"
        :editable-row-keys="editableRowKeys"
        height="100%"
        lazyLoad
        :pagination="pagination"
        @page-change="pageChange"
        :loading="tableLoading"
        @row-edit="onRowEdit"
        @row-validate="onRowValidate"
        @validate="onValidate"
      >
        <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
      </t-table>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { downloadBlobFile } from '@/core/download';
import { MessagePlugin, Input } from 'tdesign-vue';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { yhsYyzbSaveTz, yhsYyzbQuery, getYyzbCsz, updateWqzdz } from '@/pages/index/api/tzzx/yhstz/yyzbtz.js';
import { numberToPrice } from '@/utils/numberToCurrency';
import { DownloadIcon, SaveIcon, RollbackIcon } from 'tdesign-icons-vue';
import { querySearchConfig } from './config.js';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    SearchControlPanel,
    DownloadIcon,
    SaveIcon,
    RollbackIcon,
  },
  data() {
    return {
      userInfo: {},
      editableRowKeys: ['1'],
      buttonvisible: false,
      YHS0000001: 'N',
      YHS0000002: '0',
      currentSaveId: '',
      // 保存变化过的行信息
      editMap: {},
      selectedRowKeys: [],
      tableData: [],
      formData: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      savebuttonLoading: false,
      fromName: false,
      pagination: { current: 1, pageSize: 10, total: 0 },
      uuid: '',
      zuuid: '',
      syuuid: '',
      kmmc: '',
      dqjehj: '',
      wqzgdjehj: '',
      jsyj: '',
      querySearchConfigOneRules: {
        ssnd: [{ required: true, message: '必填项', type: 'error' }],
      },
    };
  },
  created() {
    this.formData.ssnd = this.querySearchConfig[0].value;
    this.init();
  },
  mounted() {},
  computed: {
    dataColumns() {
      return [
        {
          width: 60,
          align: 'center',
          colKey: 'xh',
          title: '序号',
        },
        {
          colKey: 'kmmc',
          title: '科目名称',
          width: 280,
        },
        {
          title: '当期财报',
          align: 'center',
          ellipsis: true,
          children: [
            {
              title: '实收资本 + 资本公积',
              align: 'center',
              children: [
                {
                  colKey: 'dqjehj',
                  title: '(1)',
                  align: 'center',
                  width: 210,
                  cell: (h, { row }) => <div>{this.numberToPrice(row.dqjehj)}</div>,
                  // edit: {
                  //   component: InputNumber,
                  //   props: {
                  //     theme: 'normal',
                  //     autofocus: true,
                  //     autoWidth: true,
                  //   },
                  //   // 除了点击非自身元素退出编辑态之外，还有哪些事件退出编辑态
                  //   abortEditOnEvent: ['onEnter'],
                  //   // 编辑完成，退出编辑态后触发
                  //   onEdited: (context) => {
                  //     console.log('编辑完成', context);
                  //   },
                  //   rules: [{ required: true, message: '不能为空' }],
                  //   defaultEditable: false,
                  //   validateTrigger: 'change',
                  //   // 透传给 component: Input 的事件
                  //   on: (editContext) => ({
                  //     onBlur: () => {
                  //       console.log('失去焦点', editContext);
                  //       const current = this.editMap[this.currentSaveId];
                  //       this.tableData.splice(current.rowIndex, 1, current.editedRow);
                  //     },
                  //   }),
                  //   showEditIcon: false,
                  // },
                  // cell: (h, { row }) => {
                  //   if (this.YHS0000001 === 'Y') {
                  //     return (
                  //       <div>
                  //         {
                  //           <t-link theme="primary" hover="color" data-id={row.dqjehj.key} onClick={this.onEdit}>
                  //             {row.dqjehj}
                  //           </t-link>
                  //         }
                  //       </div>
                  //     );
                  //   }
                  //   return <div>{row.dqjehj}</div>;
                  // },
                },
              ],
            },
          ],
        },
        {
          title: '往期财报最大值',
          align: 'center',
          ellipsis: true,
          children: [
            {
              title: '实收资本 + 资本公积',
              align: 'center',
              children: [
                {
                  colKey: 'wqzgdjehj',
                  title: '(2)',
                  align: 'center',
                  width: 210,
                  edit: {
                    component: Input,
                    props: {
                      theme: 'number',
                      align: 'center',
                      autofocus: true,
                    },
                    // 除了点击非自身元素退出编辑态之外，还有哪些事件退出编辑态
                    abortEditOnEvent: ['onEnter'],
                    // 编辑完成，退出编辑态后触发
                    onEdited: (context) => {
                      console.log('编辑完成', context);
                    },
                    rules: [{ required: true, message: '不能为空' }],
                    // 默认是否为编辑状态
                    defaultEditable: false,
                    validateTrigger: 'change',
                    // 透传给 component: Input 的事件
                    on: (editContext) => ({
                      onBlur: () => {
                        console.log('失去焦点', editContext);
                        if (editContext.row.wqzgdjehj - editContext.editedRow.wqzgdjehj !== 0) {
                          this.buttonvisible = false;
                        }
                        const current = this.editMap[this.currentSaveId];
                        this.tableData.splice(current.rowIndex, 1, current.editedRow);
                        console.log('this.tableData', this.tableData);
                      },
                      onEnter: (ctx) => {
                        console.log('回车', ctx);
                        if (editContext.row.wqzgdjehj - editContext.editedRow.wqzgdjehj !== 0) {
                          this.buttonvisible = false;
                        }
                        const current = this.editMap[this.currentSaveId];
                        this.tableData.splice(current.rowIndex, 1, current.editedRow);
                        console.log('this.tableData', this.tableData);
                      },
                    }),
                    showEditIcon: false,
                  },
                  cell: (h, { row }) => {
                    return (
                      <div>
                        <t-link theme="primary" hover="color" data-id={row.wqzgdjehj.key} onClick={this.onEdit}>
                          {this.numberToPrice(row.wqzgdjehj)}
                        </t-link>
                      </div>
                    );
                  },
                },
              ],
            },
          ],
        },
        {
          title: '计税依据',
          align: 'center',
          ellipsis: true,
          children: [
            {
              title: '',
              children: [
                {
                  colKey: 'jsyj',
                  align: 'center',
                  title: '(3) = (1) - (2)',
                  width: 210,
                  cell: (h, { row }) => <div>{this.numberToPrice(row.jsyj)}</div>,
                },
              ],
            },
          ],
        },
      ];
    },
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyYhsStatus', this.$store.state.jyss.readyYhsStatus);
      return this.$store.state.jyss.readyYhsStatus;
    },
  },
  methods: {
    convertToFloat(str) {
      const floatValue = parseFloat(str);
      return floatValue ? floatValue.toFixed(2) : '0.00';
    },
    async init() {
      // 根据接口获取参数，控制按钮显示、以及表格可编辑
      try {
        // const { data } = await getYyzbCsz();
        // this.YHS0000001 = data.YHS0000001;
        // this.YHS0000002 = data.YHS0000002;
        this.YHS0000001 = 'Y';
        this.YHS0000002 = '1';
      } catch (e) {
        console.error(e);
      }
      // this.YHS0000001 = 'Y'; // 测试用入口
      if (this.YHS0000001 === 'Y') {
        // 显示保存台账按钮，表可编辑
        this.buttonvisible = true;
      } else {
        // 隐藏保存台账按钮，表不可编辑
        this.buttonvisible = false;
      }
      // 初始化查询
      this.query();
    },
    onEdit(e) {
      let id = 0;
      if (e === undefined) {
        id = this.tableData[this.tableData.length - 1].key;
      } else {
        id = e.currentTarget.dataset.id;
        this.currentSaveId = id;
      }
      this.editableRowKeys.push(id);
    },
    updateEditState(id) {
      const index = this.editableRowKeys.findIndex((t) => t === id);
      this.editableRowKeys.splice(index, 1);
    },
    // 行校验反馈事件，this.$refs.tableRef.validateRowData 执行结束后触发
    onRowValidate(params) {
      console.log('Event Table Row Validate:', params);
    },
    onValidateTableData() {
      // 执行结束后触发事件 validate
      this.$refs.tableRef.validateTableData().then((params) => {
        const cellKeys = Object.keys(params.result);
        const firstError = params.result[cellKeys[0]];
        if (firstError) {
          MessagePlugin.warning(firstError[0].message);
        }
      });
    },
    // 表格全量数据校验反馈事件，this.$refs.tableRef.validateTableData() 执行结束后触发
    onValidate(maxValue) {
      console.log('Number(this.dqjehj) <= Number(maxValue)', Number(this.dqjehj) <= Number(maxValue));
      if (Number(this.dqjehj) < Number(maxValue)) {
        return false;
      }
      return true;
    },
    onTableclick() {
      // 点击页面其他地方时，关闭编辑框
      this.updateEditState(this.currentSaveId);
    },
    // edit(index) {
    //   console.log(index);
    // },
    onRowEdit(params) {
      console.log('onRowEdit', params);
      const { row, col, value } = params;
      this.wqzgdjehj = value; // 当期财报
      const oldRowData = this.editMap[row.wqzgdjehj.key]?.editedRow || row;
      this.dqjehj = oldRowData.dqjehj; // 往期财报
      this.jsyj = oldRowData.jsyj; // 计税依据
      const editedRow = { ...oldRowData, [col.colKey]: value };
      this.editMap[row.wqzgdjehj.key] = {
        ...params,
        editedRow,
      };

      // ⚠️ 重要：以下内容应用于全量数据校验（单独的行校验不需要）
      // const newData = [...this.data];
      // newData[rowIndex] = editedRow;
      // this.data = newData;
      // 或者
      // this.$set(this.data, rowIndex, editedRow);
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    // 查询
    async query(pm = { flag: false, p: false }) {
      // 关闭编辑框
      this.updateEditState(this.currentSaveId);
      const { flag, p, from } = pm;
      this.fromName = from ?? this.fromName;
      if (flag) this.pagination.current = 1;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      this.tableLoading = true;
      if (p) {
        params = { ...p, ...params }; // 起始时间待解决
        this.$refs.queryControl.setParams(p);
      } else {
        params = {
          ...this.formData,
          ...params,
        };
      }
      try {
        const { data } = await yhsYyzbQuery(params);
        this.tableData = data.list || [];
        this.pagination.total = data.total;
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        this.uuid = data.list[0]?.uuid || '';
        this.zuuid = data.list[0]?.zuuid || '';
        this.syuuid = data.list[0]?.syuuid || '';
        this.kmmc = data.list[0]?.kmmc || '';
        this.dqjehj = data.list[0]?.dqjehj || '';
        this.wqzgdjehj = data.list[0]?.wqzgdjehj || '';
        this.jsyj = data.list[0]?.jsyj || '';
        console.log('this.tableData', this.tableData);
        // 清空编辑信息
        this.editMap = {};
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    // 保存台账
    async saveTz() {
      this.savebuttonLoading = true;
      // 校验
      const ValidateFlag = this.onValidate(this.wqzgdjehj);
      if (!ValidateFlag) {
        this.$message.warning('当期财报金额小于往期财报金额最大值');
        this.savebuttonLoading = false;
      } else {
        const params = {
          uuid: this.tableData[0].uuid,
          wqzgdjehj: this.tableData[0].wqzgdjehj,
        };
        console.log('this.tableData[0].uuid', this.tableData[0].uuid);
        console.log('params', params);

        try {
          // await yhsYyzbSaveTz(params);
          const { data } = await updateWqzdz(params);
          this.$message.success(data);
          this.buttonvisible = true;
        } catch (e) {
          this.savebuttonLoading = false;
          this.buttonvisible = false;
          console.error(e);
        } finally {
          // 重新查询一下
          this.savebuttonLoading = false;
          this.query();
        }
      }
    },
    // 导出
    async exportExcl() {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: 'yhsyyzb',
        fileName: '印花税营业账簿台账信息',
        cxParam: {
          ...this.formData,
          ...djParam,
        },
      };
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
.znsbSbBodyDiv {
  padding-bottom: 50px !important;
}
/deep/.t-link-btn {
  margin-right: 8px;
}
/deep/.filter-btns {
  float: right;
}
</style>
