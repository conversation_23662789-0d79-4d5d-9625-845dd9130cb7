<!--
 * @Descripttion: 差异比对
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-06-11 10:06:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="dataColumns"
          :height="dynamicHeight()"
          lazyLoad
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #papmData="{ row }">
            <span>{{ numberToPrice(row.papmData) }}</span>
          </template>
          <template #declarationData="{ row }">
            <span>{{ numberToPrice(row.declarationData) }}</span>
          </template>
          <template #taxDifference="{ row }">
            <div
              v-if="row.taxDifference !== 0">
              <t-tooltip :showArrow="false" :destroyOnClose="false">
                <span>{{ numberToPrice(row.taxDifference) }}</span>
                <template #content>
                  <span>{{ row.ydmsg }}</span>
                </template>
                <close-circle-filled-icon :style="{ color: 'red', cursor: 'pointer', transform: 'translateY(-3px)' }" />
              </t-tooltip>
            </div>
            <span v-else>{{ numberToPrice(row.taxDifference) }}</span>
          </template>
        </t-table>
      </div>
      <ValidateDialog
        :validate-rules="validateRules"
        :handleMsg="true"
        :extraHandleMsg="true"
        @ruleClick="ruleClick"
        @toggle="toggle"
      />
    </div>
  </div>
</template>
<script>
import { CloseCircleFilledIcon } from 'tdesign-icons-vue';
import dayjs from 'dayjs';
import { downloadBlobFile } from '@/core/download';
import { numberToPrice } from '@/utils/numberToCurrency';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { FileAddIcon, DownloadIcon, TipsIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { querySearchConfig, dataColumns, Ssyf } from './config.js';
import { getCheckResult } from '@/pages/index/api/tzzx/yhstz/cybd.js';
import ValidateDialog from '@/pages/index/components/validateDialog/index.vue';
import { multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';

export default {
  components: {
    SkeletonFrame,
    QsbButton,
    ExtractDataButton,
    CloseCircleFilledIcon,
    CssDialog,
    SearchControlPanel,
    DownloadIcon,
    FileAddIcon,
    TipsIcon,
    ValidateDialog
  },
  data() {
    return {
      loading: true,
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      formData: {},
      querySearchConfigOneRules: {
        monthStart: [{ required: true, message: '必填项', type: 'error' }],
        monthEnd: [{ required: true, message: '必填项', type: 'error' }],
      },
      tableData: [],
      dataList: [],
      stampTaxTypeList: [],
      validateRules: [],
      validateDialogToggle: true,
      dataColumns,
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    this.formData.monthStart = Ssyf().monthStart;
    this.formData.monthEnd = Ssyf().monthEnd;
  },
  watch: {
    'validateRules.length': function () {
      if (this.validateRules.length) {
        console.log(this.vavalidateRules.length)
        this.validateDialogToggle = true;
      } else {
        this.validateDialogToggle = false;
      }
    },
  },
  mounted() {},
  computed: {},
  methods: {
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 2000);
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (!p) {
        console.log(JSON.stringify(this.formData));
        const monthStart = this.formData.monthStart;
        const monthEnd = this.formData.monthEnd;
        if (!this.isNaturalQuarterRange(monthStart, monthEnd)) {
          this.$message.warning('仅支持按自然季度进行筛选');
          return;
        }
      }
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        gsh: this.$store.state.zzstz.userInfo?.qydmz || '',
        pageNo: this.pagination.current,
        pageSize: 1000000,
      };
      if (p) {
        params = { ...p, ...params }; // 起始时间待解决
        this.$refs.queryControl.setParams(p);
      } else {
        params = {
          ...this.formData,
          ...params,
          stampTaxType: this.formData.stampTaxType ? multiSelectHandle(this.formData.stampTaxType) : '',
        };
      }
      try {
        const { data } = await getCheckResult(params);
        this.tableData = data.list || [];
        this.pagination.total = data.total;
        this.dataList = data.list;
        this.validateRules = [];
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
          if (item.taxDifference !== 0) {
            this.$set(
              item,
              'ydmsg',
              `${item.months}期间，PAPM数据：${item.papmData}， 申报数据${item.declarationData}
              ，存在差异，差异税额：${item.taxDifference}`,
            );
            const l = {
              content: item.ydmsg,
              index,
              type: 'error',
              showHlxmFlag: true
            };
            this.validateRules.push(l);
          }
        });
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        gsh: this.$store.state.zzstz.userInfo?.qydmz || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      this.handleTableData();
      console.log(this.tableData);
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: 'cybd',
        fileName: '差异比对表导出',
        cxParam: {
          ...this.formData,
          ...djParam,
          tableData: isAll === 'all' ? [] : this.tableData,
          isAll: isAll === 'all' ? 'Y' : 'N',
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
        params.cxParam.stampTaxType = this.formData.stampTaxType ? multiSelectHandle(this.formData.stampTaxType) : '';
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.handleTableData();
    },
    handleTableData() {
      const { current, pageSize } = this.pagination;
      const start = (current - 1) * pageSize;
      const end = start + pageSize;
      this.tableData = this.dataList.slice(start, end); // 当前页的数据
      // 添加 xh 字段
      this.tableData = this.tableData.map((item, index) => {
        item.xh = start + index + 1; // 计算序号并挂载到 item 上
        return item;
      });

      this.validateRules = [];
      this.tableData.forEach((item, index) => {
        this.$set(item, 'index', index);
        if (item.taxDifference !== 0) {
          this.$set(
            item,
            'ydmsg',
            `${item.months}期间，PAPM数据：${item.papmData}，申报数据${item.declarationData}，存在差异，差异税额：${item.taxDifference}`
          );
          const l = {
            content: item.ydmsg,
            index,
            type: 'error',
            showHlxmFlag: true
          };
          this.validateRules.push(l);
        }
      });
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
    ruleClick(e) {
      console.log(e);
      this.activeRule = e;
      this.$refs.tableRef.scrollToElement({ index: e.index, top: 47, time: 60 });
    },
    dynamicHeight() {
      console.log('计算高度属性是否生效');
      let height = '100%';
      if (!this.validateDialogToggle) {
        console.log('this.validateDialogToggle', this.validateDialogToggle);
        return height;
      }
      console.log(this.validateRules.length)
      switch (this.validateRules.length) {
        case 0:
          height = '100%';
          break;
        case 1:
          height = '85%';
          break;
        case 2:
          height = '80%';
          break;
        case 3:
          height = '75%';
          break;
        default:
          height = '70%';
          break;
      }
      console.log('计算高度属性已生效，返回高度为', height);
      return height;
    },
    toggle(val) {
      console.log('打开校验弹窗', val);
      this.validateDialogToggle = val;
    },
    isNaturalQuarterRange(startMonth, endMonth) {
      const start = dayjs(startMonth);
      const end = dayjs(endMonth);

      if (start.year() !== end.year()) return false;

      const startQuarter = Math.ceil(start.month() / 3); // month从0开始，所以用Math.ceil
      const endQuarter = Math.ceil(end.month() / 3);

      // 判断是否是完整季度范围
      const quarterStartMonths = [1, 4, 7, 10]; // 每个季度的起始月（dayjs中month=0~11）
      const quarterEndMonths = [3, 6, 9, 12];

      const isStartOfQuarter = quarterStartMonths.includes(start.month() + 1); // dayjs的month是0-based
      const isEndOfQuarter = quarterEndMonths.includes(end.month() + 1);

      if (!isStartOfQuarter || !isEndOfQuarter) return false;

      return endQuarter === startQuarter || endQuarter === startQuarter + 1;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}

/deep/.active-row > td {
  background: #d8e6fa !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
