import dayjs from 'dayjs';
// // 计算当前季度的开始和结束时间
// export function calcNowQuarter() {
//   const index = dayjs().quarter();
//   const year = dayjs().year();
//   const startQuarter = dayjs(`${year}-${3 * (index - 1) + 1}-01`).format('YYYY-MM-DD');
//   const endQuarter = dayjs(`${year}-${3 * index}-${new Date('2024', 3 * index, 0).getDate()}`).format('YYYY-MM-DD');
//   return { startQuarter, endQuarter };
// }
// 计算默认所属月份起和止
export function Ssyf() {
  const year = dayjs().format('YYYY-MM').substring(0, 4);
  const month = dayjs().format('YYYY-MM').substring(5, 7);
  let monthStart;
  let monthEnd;
  switch (month) {
    case '01':
      monthStart = `${(parseInt(year, 10) - 1).toString()}-10`;
      monthEnd = `${(parseInt(year, 10) - 1).toString()}-12`;
      break;
    case '02':
      monthStart = `${year}-01`;
      monthEnd = `${year}-03`;
      break;
    case '03':
      monthStart = `${year}-01`;
      monthEnd = `${year}-03`;
      break;
    case '04':
      monthStart = `${year}-01`;
      monthEnd = `${year}-03`;
      break;
    case '05':
      monthStart = `${year}-04`;
      monthEnd = `${year}-06`;
      break;
    case '06':
      monthStart = `${year}-04`;
      monthEnd = `${year}-06`;
      break;
    case '07':
      monthStart = `${year}-04`;
      monthEnd = `${year}-06`;
      break;
    case '08':
      monthStart = `${year}-07`;
      monthEnd = `${year}-09`;
      break;
    case '09':
      monthStart = `${year}-07`;
      monthEnd = `${year}-09`;
      break;
    case '10':
      monthStart = `${year}-07`;
      monthEnd = `${year}-09`;
      break;
    case '11':
      monthStart = `${year}-10`;
      monthEnd = `${year}-12`;
      break;
    case '12':
      monthStart = `${year}-10`;
      monthEnd = `${year}-12`;
      break;
    default:
      break;
  }
  return { monthStart, monthEnd };
}

export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'monthStart',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().startQuarter.substring(0, 7),
    value: Ssyf().monthStart,
    placeholder: '请选择',
    clearable: true,
    relation: 'monthEnd',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'monthEnd',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().endQuarter.substring(0, 7),
    value: Ssyf().monthEnd,
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'monthStart',
  },
  {
    label: '数据来源',
    key: 'dataSource',
    type: 'select',
    selectList: [
      { value: '0', label: '非BPM系统' },
      { value: '1', label: 'BPM系统' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '印花税类型',
    key: 'stampTaxType',
    type: 'select',
    multiple: true,
    selectList: [
        { value: '采购合同', label: '采购合同' },
        { value: '销售合同', label: '销售合同' },
        { value: '仓储合同', label: '仓储合同' },
        { value: '运输合同', label: '运输合同' },
        { value: '财产保险合同', label: '财产保险合同' },
        { value: '租赁合同', label: '租赁合同' },
        { value: '营业账簿', label: '营业账簿' },
        { value: '买卖合同', label: '买卖合同' },
        { value: '承揽合同', label: '承揽合同' },
        { value: '技术合同', label: '技术合同' },
        { value: '运输合同', label: '运输合同' },
        { value: '租赁合同', label: '租赁合同' },
        { value: '财产保险合同', label: '财产保险合同' },
        { value: '建设工程合同', label: '建设工程合同' },
        { value: '产权转移书据', label: '产权转移书据' },
        { value: '保管合同', label: '保管合同' },
        { value: '仓储合同', label: '仓储合同' },
        { value: '借款合同', label: '借款合同' },
        { value: '证券交易合同', label: '证券交易合同' },
            ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '利润中心',
    key: 'profitCenter',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同编号',
    key: 'contractCode',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '客商编码',
    key: 'partnerCode',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];
export const dataColumns = [
  {
    width: 60,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 100,
    colKey: 'months',
    title: '期间',
  },
  {
    width: 90,
    colKey: 'dataSource',
    title: '数据来源',
  },
  {
    width: 90,
    colKey: 'profitCenter',
    title: '利润中心',
  },
  {
    width: 100,
    colKey: 'stampTaxType',
    title: '印花税类型',
  },
  {
    width: 180,
    colKey: 'stampTaxDetail',
    title: '印花税细类',
  },
  {
    width: 100,
    colKey: 'partnerCode',
    title: '客商编码',
  },
  {
    width: 100,
    colKey: 'contractCode',
    title: '合同编号',
  },
  {
    width: 120,
    colKey: 'papmData',
    title: 'PAPM数据',
  },
  {
    width: 120,
    colKey: 'declarationData',
    title: '申报数据',
  },
  {
    width: 120,
    colKey: 'taxDifference',
    title: '差异税额',
  },
];