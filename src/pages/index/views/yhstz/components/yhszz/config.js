import dayjs from 'dayjs';
// // 计算当前季度的开始和结束时间
// export function calcNowQuarter() {
//   const index = dayjs().quarter();
//   const year = dayjs().year();
//   const startQuarter = dayjs(`${year}-${3 * (index - 1) + 1}-01`).format('YYYY-MM-DD');
//   const endQuarter = dayjs(`${year}-${3 * index}-${new Date('2024', 3 * index, 0).getDate()}`).format('YYYY-MM-DD');
//   return { startQuarter, endQuarter };
// }
// 计算默认所属月份起和止
export function Ssyf() {
  const year = dayjs().format('YYYY-MM').substring(0, 4);
  const month = dayjs().format('YYYY-MM').substring(5, 7);
  let ssyfq;
  let ssyfz;
  switch (month) {
    case '01':
      ssyfq = `${(parseInt(year, 10) - 1).toString()}-10`;
      ssyfz = `${(parseInt(year, 10) - 1).toString()}-12`;
      break;
    case '02':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '03':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '04':
      ssyfq = `${year}-01`;
      ssyfz = `${year}-03`;
      break;
    case '05':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '06':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '07':
      ssyfq = `${year}-04`;
      ssyfz = `${year}-06`;
      break;
    case '08':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '09':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '10':
      ssyfq = `${year}-07`;
      ssyfz = `${year}-09`;
      break;
    case '11':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    case '12':
      ssyfq = `${year}-10`;
      ssyfz = `${year}-12`;
      break;
    default:
      break;
  }
  return { ssyfq, ssyfz };
}
export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'ssyfq',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().startQuarter.substring(0, 7),
    value: Ssyf().ssyfq,
    placeholder: '请选择',
    clearable: true,
    relation: 'ssyfz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'ssyfz',
    type: 'datepicker',
    mode: 'month',
    // value: calcNowQuarter().endQuarter.substring(0, 7),
    value: Ssyf().ssyfz,
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ssyfq',
  },
  {
    label: '申报期限类型',
    key: 'sbqxlx',
    type: 'select',
    selectList: [
      { value: '00', label: '按期' },
      { value: '01', label: '按次' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '税目',
    key: 'zspmDm',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '子目',
    key: 'zszmDm',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '凭证书立日期起',
    key: 'ynspzslrqq',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    relation: 'ynspzslrqz',
    timeRange: 'start',
  },
  {
    label: '凭证书立日期止',
    key: 'ynspzslrqz',
    type: 'datepicker',
    value: '',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'ynspzslrqq',
  },
  {
    label: '应税凭证编号',
    key: 'ynspzbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
];
export const dataColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({
      disabled:
        (!(row.syzt === '待处理' || row.syzt === '处理失败') && row.bgzt !== '已变更') ||
        row.yqbz === 'Y' ||
        row.cqbz === 'Y',
    }),
    width: 60,
  },
  {
    width: 60,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 120,
    colKey: 'zspmmc',
    title: '税目',
  },
  {
    width: 180,
    colKey: 'zszmmc',
    title: '子目',
  },
  {
    width: 100,
    colKey: 'sbqxlx',
    title: '申报期限',
  },
  {
    width: 120,
    colKey: 'skssqq',
    title: '税款所属期起',
  },
  {
    width: 120,
    colKey: 'skssqz',
    title: '税款所属期止',
  },
  {
    width: 120,
    colKey: 'yspzbh',
    title: '应税凭证编号',
  },
  {
    width: 120,
    colKey: 'ynspzslrq',
    title: '应纳税凭证书立（领受）日期',
  },
  {
    align: 'right',
    width: 130,
    colKey: 'sjjsje',
    title: '实际计税金额',
  },
  {
    align: 'right',
    width: 80,
    colKey: 'sl1',
    title: '税率',
  },
  {
    width: 120,
    colKey: 'yspzmc',
    title: '应税凭证名称',
  },
  {
    width: 100,
    colKey: 'yspzsl',
    title: '应税凭证数量',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'ynse',
    title: '应纳税额',
    fixed: 'right',
  },
  {
    align: 'right',
    width: 100,
    colKey: 'bgzt',
    title: '变更标志',
    fixed: 'right',
  },
  {
    align: 'center',
    width: 100,
    colKey: 'syzt',
    title: '税源状态',
    fixed: 'right',
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    foot: '-',
    fixed: 'right',
  },
];
