<template>
  <css-dialog
    class="dialogCss"
    placement="center"
    :header="['查看税源明细', '编辑税源明细'][visible.pageType]"
    :visible.sync="isVisible"
    :confirmBtn="[null, '保存'][visible.pageType]"
    :cancelBtn="[null, '取消'][visible.pageType]"
    :onConfirm="confirm"
    @close="onClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div class="nr">
      <t-form
        ref="forms"
        :data="formData"
        labelAlign="top"
        labelWidth="165px"
        :rules="rules"
        :disabled="[true, false][visible.pageType]"
      >
        <t-row :gutter="16">
          <t-col :span="4">
            <t-form-item label="税目" name="zspmmc">
              <t-input v-model="formData.zspmmc" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="子目" name="zszmmc">
              <t-input v-model="formData.zszmmc" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="申报期限" name="sbqxlx">
              <t-input v-model="formData.sbqxlx" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税款所属期起" name="skssqq">
              <t-date-picker
                v-model="formData.skssqq"
                placeholder="税款所属期起"
                style="width: 276px; height: 32px"
                disabled
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税款所属期止" name="skssqz">
              <t-date-picker
                v-model="formData.skssqz"
                placeholder="税款所属期止"
                style="width: 276px; height: 32px"
                disabled
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="应纳税凭证书立（领受）日期" name="ynspzslrq">
              <t-date-picker
                v-model="formData.ynspzslrq"
                placeholder="请填写应纳税凭证书立（领受）日期"
                style="width: 276px; height: 32px"
                @change="ynspzsllsrqChange"
                clearable
              ></t-date-picker>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="应征凭证数量" name="yspzsl">
              <t-input v-model="formData.yspzsl" align="left" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="实际计税金额" name="sjjsje">
              <gt-input-money v-model="formData.sjjsje" theme="normal" align="left" disabled />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="税率" name="sl1">
              <t-input :value="formattedSl1" align="left" disabled></t-input>
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="应纳税额" name="ynse">
              <gt-input-money v-model="formData.ynse" theme="normal" align="left" disabled />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="应征凭证名称" name="yspzmc">
              <t-input v-model="formData.yspzmc" disabled></t-input>
            </t-form-item>
          </t-col>
          <!-- 2025.2.25 印花税台账中税源明细中查询结果列表中显示中无“应税凭证编号”，但是编辑页面有显示“应税凭证编号”，建议编辑页面也去掉此字段，此字段暂无意义。 -->
          <!-- <t-col :span="4">
            <t-form-item label="应税凭证编号" name="yspzbh">
              <t-input v-model="formData.yspzbh" disabled></t-input>
            </t-form-item>
          </t-col> -->
        </t-row>
      </t-form>
      <t-card header-bordered :style="{ width: '100%', paddingBottom: '20px' }">
        <template>
          <t-tabs v-model="value">
            <t-tab-panel :value="1" label="实际结算信息">
              <t-table
                ref="tableRef"
                row-key="key"
                :stripe="true"
                :data="sjjsList"
                :columns="sjjsColumn"
                :hover="true"
                :bordered="true"
              >
                <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
                <template #sjjsje="{ row }">
                  <span>{{ numberToPrice(row.sjjsje) }}</span>
                </template>
              </t-table>
            </t-tab-panel>
            <t-tab-panel :value="2" label="对方书立人信息">
              <t-table
                ref="tableRef"
                row-key="key"
                :stripe="true"
                :data="dfslrList"
                :columns="dfslColumn"
                :hover="true"
                :bordered="true"
              >
                <template #xh="{ rowIndex }">{{ rowIndex + 1 }}</template>
                <template #dfslrsjje="{ row }">
                  <span>{{ numberToPrice(row.dfslrsjje) }}</span>
                </template>
              </t-table>
            </t-tab-panel>
          </t-tabs>
        </template>
      </t-card>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>
<script>
import dayjs from 'dayjs';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import GtInputMoney from '@/pages/index/components/custom-input-money/index.vue';
import { queryYhslfkbl, updateYhscjLsrq } from '@/pages/index/api/tzzx/yhstz/yhszz.js';

export default {
  components: { CssDialog, GtInputMoney },
  props: {
    visible: {
      type: [Object, Boolean],
      required: true,
      default: () => {},
    },
  },
  data() {
    this.baseRules = {};
    return {
      zspmDmList: [],
      zszmDmList: [],
      isCkSy: false,
      isVisible: true,
      confirmLoading: false,
      rules: {},
      sjjsList: [],
      dfslrList: [],
      value: 1,
      formData: {
        uuid: '',
        zspmmc: '',
        zszmmc: '',
        sbqxlx: '',
        skssqq: '',
        skssqz: '',
        ynspzslrq: '',
        sjjsje: '',
        sl1: '',
        yspzmc: '',
        // yspzbh: '',
        yspzsl: '',
        ynse: '',
      },
    };
  },
  created() {
    this.init();
  },
  computed: {
    sjjsColumn() {
      return [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: '10%',
          align: 'center',
          fixed: 'left',
        },
        {
          align: 'center',
          width: '10%',
          colKey: 'xh',
          title: '序号',
          cell: 'index-cell',
        },
        {
          title: '实际结算金额',
          colKey: 'sjjsje',
          align: 'center',
          width: '40%',
        },
        {
          title: '实际结算日期',
          colKey: 'sjjsrq',
          align: 'center',
          width: '40%',
        },
      ];
    },
    dfslColumn() {
      return [
        {
          colKey: 'row-select',
          type: 'multiple',
          className: 'demo-multiple-select-cell',
          width: '10%',
          align: 'center',
          fixed: 'left',
        },
        {
          align: 'center',
          width: '9%',
          colKey: 'xh',
          title: '序号',
          cell: 'index-cell',
        },
        {
          title: '对方书立人名称',
          colKey: 'dfslrmc',
          align: 'center',
          width: '27%',
        },
        {
          title: '对方书立人纳税人识别号（统一社会信用代码）',
          colKey: 'dfslrnsrsbh',
          align: 'center',
          width: '27%',
        },
        {
          title: '对方书立人涉及金额',
          colKey: 'dfslrsjje',
          align: 'center',
          width: '27%',
        },
      ];
    },
    formattedSl1() {
      return this.formData.sl1 === null ? '-' : `${this.formData.sl1 * 100}%`;
    },
  },
  methods: {
    dayjs,
    async init() {
      if (this.visible.row?.uuid) {
        this.formData = this.visible.row;
        const { data } = await queryYhslfkbl([this.visible.row.uuid]);
        this.sjjsList = data?.sjjsxxList || [];
        this.sjjsList.forEach((item, index) => {
          this.$set(item, 'index', index + 1);
        });
        this.dfslrList = data?.dfslxxList || [];
        this.dfslrList.forEach((item, index) => {
          this.$set(item, 'index', index + 1);
        });
        // 2025.3.3 禅道1123 印花税台账中税源明细中按次申报的税源未生成税源时，
        // 点击编辑按钮弹出编辑框，只允许修改应纳税凭证书立（领受）日期为当前修改日期，
        // 建议默认带出当前日期，其他日期不置灰选择其他日期提示“只允许修改当前日期”。
        this.formData.ynspzslrq = dayjs().format('YYYY-MM-DD');
        this.formData.skssqq = this.formData.ynspzslrq;
        this.formData.skssqz = this.formData.ynspzslrq;
      }
    },
    ynspzsllsrqChange() {
      if (this.formData.ynspzslrq !== dayjs().format('YYYY-MM-DD')) {
        const confirmDia = this.$dialog({
          theme: 'info',
          header: '提示',
          body: '应纳税凭证书立（领受）日期只允许修改为当前日期。',
          confirmBtn: '确定',
          cancelBtn: null,
          onConfirm: () => {
            this.formData.ynspzslrq = dayjs().format('YYYY-MM-DD');
            // 请求成功后，销毁弹框
            confirmDia.destroy();
          },
          onClose: () => {
            this.formData.ynspzslrq = dayjs().format('YYYY-MM-DD');
            // 请求成功后，销毁弹框
            confirmDia.hide();
          },
        });
      }
    },
    onClose() {
      this.isVisible = false;
    },
    async confirm() {
      const val = await this.$refs.forms.validate();
      this.confirmLoading = true;
      if (val === true) {
        const p = {};
        ['ynspzslrq', 'uuid'].forEach((d) => {
          p[d] = this.formData?.[d] ?? null;
        });
        const params = { ...p };
        const { data } = await updateYhscjLsrq(params);
        if (data) {
          await this.$message.success('修改成功');
          this.onClose();
          this.$emit('updateSymx');
        }
      } else {
        this.confirmLoading = false;
      }
    },
    numberToPrice(number) {
      const value = Number(number).toFixed(2);
      if (!value) return 0;
      // 获取整数部分
      const intPart = Math.trunc(value);
      // 整数部分处理，增加,
      const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      // 预定义小数部分
      let floatPart = '';
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.');
      if (valueArray.length === 2) {
        // 有小数部分
        floatPart = valueArray[1].toString(); // 取得小数部分
        return `${intPartFormat}.${floatPart}`;
      }
      return intPartFormat + floatPart;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../../styles/dialog.less';
/deep/ .t-input-number.t-is-controls-right {
  width: 100% !important;
}
#lrzx_options {
  width: 290px;
  overflow: hidden; /* 隐藏超出div宽度的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  white-space: nowrap; /* 确保文本在一行内显示 */
}
</style>
