<!-- eslint-disable no-param-reassign -->
<!--
 * @Descripttion: 台账-印花税台账明细
 * @Version: 1.0
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-07-16 14:45:00
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        @changeSelect="htlxChange"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <ExtractDataButton
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            :ywtsMsg="'财务凭证数据以及合同数据'"
            :ywlx="ywlx"
            @query="query"
          />
          <t-button theme="primary" @click="scsy" :loading="scsybtnloading"
            ><FileAddIcon slot="icon" />生成税源</t-button
          >
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
        <t-checkbox
          :checked="isBg"
          :defaultChecked="false"
          :indeterminate="false"
          :onChange="handleBgzt"
          style="display: block; width: 160px; margin-top: 6px"
          >显示已变更税源</t-checkbox
        >
        <t-link theme="primary" @click="wxTis" target="_self" style="width: 100px; margin-top: 4px">
          <tips-icon slot="prefix-icon" style="color: rgb(0 10 198)"></tips-icon>
          温馨提示
        </t-link>
        <!-- <t-button variant="outline" theme="primary" @click="wxTis"><TipsIcon slot="icon" />温馨提示</t-button> -->
      </div>
      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          :foot-data="footData"
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
          :rowClassName="rowClassName"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #sl1="{ row }">
            {{ row.sl1 === null ? '-' : (row.sl1 * 100).toFixed(2) + '%' }}
          </template>
          <template #sjjsje="{ row }">
            <span
              class="specText"
              v-if="Number(row.sjjsje) > 0 && row.zspmDm === '101110502'"
              @click="openYyzbtz(row, 1)"
              style="color: #0052d9; text-decoration: underline"
              >{{ numberToPrice(row.sjjsje) }}</span
            >
            <span
              class="specText"
              v-else-if="Number(row.sjjsje) > 0 && row.zspmDm !== '101110502'"
              @click="openHtmx(row, 1)"
              style="color: #0052d9; text-decoration: underline"
              >{{ numberToPrice(row.sjjsje) }}</span
            >
            <span v-else>{{ numberToPrice(row.sjjsje) }}</span>
          </template>
          <template #ynse="{ row }">
            <span>{{ numberToPrice(row.ynse) }}</span>
          </template>
          <template #bgzt="{ row }">
            <t-tag v-if="row.bgzt === '已变更'" theme="primary" variant="light-outline">已变更</t-tag>
            <t-tag v-else variant="light-outline">未变更</t-tag>
          </template>
          <template #syzt="{ row }">
            <t-tag v-if="syztDsc(row)" theme="default" variant="light-outline">未生成</t-tag>
            <t-tag v-if="syztClz(row)" theme="primary" variant="light-outline">处理中</t-tag>
            <div v-if="syztClsb(row)">
              <t-tooltip :destroyOnClose="false">
                <template #content>
                  <span>点击查看失败原因</span>
                </template>
                <t-tag theme="danger" variant="light-outline" @click="sbyy(row)">处理失败</t-tag>
              </t-tooltip>
            </div>
            <t-tag v-if="syztYsc(row)" theme="success" variant="light-outline">已生成</t-tag>
            <t-tag v-if="syztYsb(row)" theme="success" variant="light-outline">已申报</t-tag>
          </template>
          <template #operation="{ row }">
            <t-link theme="primary" hover="color" @click="openDetail(row)" v-if="operationOpenDetail(row)">
              查看
            </t-link>
            <t-link theme="primary" class="t-link-btn" hover="color" @click="editRow(row)" v-if="operationEditRow(row)">
              编辑
            </t-link>
            <t-link
              theme="primary"
              hover="color"
              @click="deleteRow(row)"
              v-if="operationEditRow(row)"
              :disabled="deleteRowStatus(row)"
            >
              &nbsp;删除
            </t-link>
          </template>
        </t-table>
        <div v-show="boxvisible">
          <css-dialog
            class="dialogMiddleCss"
            placement="center"
            confirmBtn="确定"
            :cancelBtn="null"
            :onConfirm="closeBox"
            :onClose="closeBox"
            :closeOnOverlayClick="false"
          >
            <t-divider style="margin: 0"></t-divider>
            <div slot="header">
              <TipsIcon slot="icon" />
              <span>温馨提示</span>
            </div>
            <div class="nr">
              <p>
                <br />1.
                本功能内展示的印花税税源均通过合同明细、凭证明细、营业账簿等台账自动生成，如需调整税源数据需调整对应台账数据或源端系统数据，调整后系统将重新生成税源信息。
              </p>
              <p><br />2. 对于税源状态为“待采集”或数据存在变更的税源，需要点击“生成税源”按钮，将税源数据推送至税局。</p>
              <p><br />3. 生成税源后方可进行印花税的申报或更正。</p>
              <p><br /></p>
            </div>
            <t-divider style="margin: 0"></t-divider>
          </css-dialog>
        </div>
        <div v-show="tishiboxvisible && !zhidaoFlag">
          <css-dialog
            class="dialogMiddleCss"
            placement="center"
            confirmBtn="确定"
            :cancelBtn="null"
            :onConfirm="onConfirm"
            :onClose="closeBox"
            :closeOnOverlayClick="false"
            ><t-divider style="margin: 0"></t-divider>
            <div slot="header">
              <TipsIcon slot="icon" />
              <span>温馨提示</span>
            </div>
            <div class="nr">
              <p v-show="yqcqFlag"><br />只可采集当前征期内的税源，逾期与未到征期的按期税源暂无法采集。</p>
              <p v-show="cjbgFlag"><br />存在已采集税源发生数据变更，请重新进行税源采集操作。</p>
              <p v-show="sbbgFlag"><br />存在已申报税源发生数据变更，请重新进行税源采集后进行申报更正。<br /></p>
              <p><br /></p>
            </div>
            <t-divider style="margin: 0"></t-divider>
          </css-dialog>
        </div>
        <div v-show="Scsyboxvisible">
          <t-dialog
            theme="warning"
            style="display: block; border-radius: 10px"
            :width="400"
            header="警示"
            body="请确认是否要生成税源！"
            :onConfirm="confirmScsybox"
            :onClose="closeBox"
            :confirmBtn="{
              content: '确认',
              loading: scsyLoading,
            }"
          >
          </t-dialog>
        </div>
        <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateSymx="query()" />
        <div v-show="deleteRowvisible">
          <t-dialog
            theme="warning"
            style="display: block; border-radius: 10px"
            :width="400"
            header="警示"
            body="请确认是否该明细"
            :onConfirm="confirmDelRow"
            :onClose="closeBox"
          >
          </t-dialog>
        </div>
        <!--查看失败原因弹出框-->
        <t-dialog
          :visible.sync="sbyysmBz"
          :onClose="sbonClose"
          @confirm="sbonClose"
          :cancelBtn="null"
          theme="danger"
          header="失败原因"
        >
          <div>{{ this.sbyyBody }}</div>
        </t-dialog>
        <!-- 营业账簿报送期限提醒弹窗 -->
        <div class="yyzbtx">
          <t-dialog :visible.sync="yyzbbstxBz" header="提示" :footer="false" :onClose="handleClose">
            <div class="dialog-body">
              <p>请关注营业账簿台账中往期财报最大值是否正确，初次使用本系统请手动填写此项数据，避免申报数据有误。</p>
            </div>
            <div class="dialog-footer">
              <t-checkbox v-model="doNotRemind">不再提示</t-checkbox>
              <t-button theme="primary" @click="handleConfirm">确认</t-button>
            </div>
          </t-dialog>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { getYhsHtZspm, getYhsHtZszm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import {
  initYhsZzQuery,
  syYhsSy,
  deleteYhscj,
  queryYhstzxxHj,
  yyzbWqzdzCheck,
  yyzbTsCheck,
} from '@/pages/index/api/tzzx/yhstz/yhszz.js';
import { downloadBlobFile } from '@/core/download';
import { numberToPrice } from '@/utils/numberToCurrency';
import { multiSelectHandle, yhsJyssReadyStatusFetch } from '@/pages/index/views/util/tzzxTools.js';
import CssDialog from '@/pages/index/components/css-dialog/index.vue';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { FileAddIcon, DownloadIcon, TipsIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import { querySearchConfig, dataColumns, Ssyf } from './config.js';
import EditDialog from './components/edit-dialog.vue';

export default {
  components: {
    EditDialog,
    SkeletonFrame,
    QsbButton,
    ExtractDataButton,
    CssDialog,
    SearchControlPanel,
    DownloadIcon,
    FileAddIcon,
    TipsIcon,
  },
  data() {
    return {
      ywlx: 'yhs',
      scsyLoading: false,
      scsybtnloading: false,
      loading: true,
      sbyysmBz: false,
      yyzbbstxBz: false,
      doNotRemind: false,
      sbyyBody: '',
      deleteRowvisible: false,
      editDialogVisible: false,
      isProduct: this.$store.state.isProduct.envValue,
      userInfo: {},
      querySearchConfig,
      tableLoading: false,
      dcLoading: false,
      isBg: false,
      yqcqFlag: false,
      cjbgFlag: false,
      sbbgFlag: false,
      tishiboxvisible: false,
      zhidaoFlag: false,
      Scsyboxvisible: false,
      formData: {},
      querySearchConfigOneRules: {
        ssyfq: [{ required: true, message: '必填项', type: 'error' }],
        ssyfz: [{ required: true, message: '必填项', type: 'error' }],
      },
      selectedRowKeys: [],
      boxvisible: false,
      bgzt: '',
      zspmList: [], // 征收品目
      zszmList: [], // 征收子目
      tableData: [],
      dataList: [],
      footData: [],
      deleteRowId: '',
      dataColumns,
      pagination: { current: 1, pageSize: 10, total: 0 },
    };
  },
  created() {
    // const { startQuarter, endQuarter } = calcNowQuarter();
    // this.formData.ssyfq = startQuarter.substring(0, 7);
    // this.formData.ssyfz = endQuarter.substring(0, 7);
    this.formData.ssyfq = Ssyf().ssyfq;
    this.formData.ssyfz = Ssyf().ssyfz;
  },
  mounted() {
    this.init();
  },
  computed: {
    sszqToExtract() {
      return dayjs(this.formData.ssyfq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyYhsStatus', this.$store.state.jyss.readyYhsStatus);
      return this.$store.state.jyss.readyYhsStatus;
    },
  },
  methods: {
    handleConfirm() {
      // 处理确认逻辑
      if (this.doNotRemind) {
        // 存储不再提醒的状态，例如使用localStorage
        sessionStorage.setItem('doNotRemind', 'true');
      }
      this.yyzbbstxBz = false;
    },
    handleClose() {
      this.yyzbbstxBz = false;
    },
    openYyzbtz(row) {
      this.$emit('openPage', {
        from: 'yhszz',
        data: {
          ssnd: dayjs(row.skssqq).format('YYYY'),
        },
        type: 'yyzbtz',
      });
    },
    openHtmx(row) {
      this.$emit('openPage', {
        from: 'yhszz',
        data: {
          ssyfq: row.ssrqq,
          ssyfz: row.ssrqz,
          zspmDm: row.zspmDm,
          zszmDm: row.zszmDm,
        },
        type: 'httz',
      });
    },
    sbyy(row) {
      this.sbyyBody = row.cwxx;
      this.sbyysmBz = true;
    },
    sbonClose() {
      this.sbyysmBz = false;
      this.sbyyBody = '';
    },
    syztDsc(row) {
      return row.syzt === '待处理';
    },
    syztClz(row) {
      return row.syzt === '处理中';
    },
    syztClsb(row) {
      return row.syzt === '处理失败';
    },
    syztYsc(row) {
      return row.syzt === '处理成功' || row.syzt === '申报中' || row.syzt === '申报失败';
    },
    syztYsb(row) {
      return row.syzt === '申报成功';
    },
    operationOpenDetail(row) {
      return (
        ((row.syzt === '处理成功' || row.syzt === '申报中' || row.syzt === '申报失败' || row.syzt === '申报成功') &&
          row.yqbz !== 'Y') ||
        row.syzt === '处理中'
      );
    },
    operationEditRow(row) {
      return (row.syzt === '待处理' || row.yqbz === 'Y') && row.sbqxlx === '按次申报' && row.syzt !== '处理中';
    },
    deleteRowStatus(row) {
      return !(row.sbqxlx === '按次申报' && row.sjjsje === 0);
    },
    openDetail(row) {
      const pageType = 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        sszq: Number(dayjs(this.formData.ssyf).format('YYYYMM')),
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    editRow(row) {
      const pageType = 1;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        sszq: Number(dayjs(this.formData.ssyf).format('YYYYMM')),
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    deleteRow(row) {
      this.deleteRowId = row.uuid;
      this.deleteRowvisible = true;
    },
    confirmDelRow() {
      deleteYhscj([this.deleteRowId]).then((res) => {
        if (res.data) {
          this.deleteRowId = '';
          this.closeBox();
          this.query();
        }
      });
    },
    changeLoading() {
      setTimeout(() => {
        this.loading = false;
      }, 2000);
    },
    handleBgzt(val) {
      this.isBg = val;
      if (val) {
        // 显示已变更税源
        this.bgzt = '已变更';
      } else {
        this.bgzt = '';
      }
      this.query();
    },
    rowClassName({ row }) {
      if (row.bgzt === '已变更') {
        return 'active-row';
      }
      return '';
    },
    rehandleSelectChange(value, { selectedRowData }) {
      const newArrLength = value.length;
      const currentArrLength = this.selectedRowKeys.length;
      if (newArrLength === this.tableData.length && newArrLength > currentArrLength) {
        this.selectedRowKeys = value;
      } else if (newArrLength === 0) {
        this.selectedRowKeys = [];
      } else if (newArrLength > currentArrLength) {
        // 新增勾选
        const differenceId = value.filter((item) => !this.selectedRowKeys.includes(item))[0];
        const differrnceSbqxlx = selectedRowData.filter((item) => item.uuid === differenceId)[0].sbqxlx;
        if (differrnceSbqxlx !== '按次申报') {
          // 全部按期申报均勾选
          const uuids = this.tableData
            .filter(
              (item) =>
                item.sbqxlx !== '按次申报' &&
                !(
                  (!(item.syzt === '待处理' || item.syzt === '处理失败') && item.bgzt !== '已变更') ||
                  item.yqbz === 'Y' ||
                  item.cqbz === 'Y'
                ),
            )
            .map((item) => item.uuid);
          this.selectedRowKeys = [...this.selectedRowKeys, ...uuids];
        } else {
          // 2025.2.8 按次税源在同一天的不同税目生成税源时，勾选一条税源需同时将同一天的其他税源一起勾选
          // 2025.4.11  明确此处时间判断应使用应纳税凭证书立（领受）日期字段
          const selectedItem = selectedRowData.find((item) => item.uuid === differenceId);
          const date = selectedItem.ynspzslrq;
          const uuids = this.tableData
            .filter(
              (item) =>
                item.sbqxlx === '按次申报' &&
                item.ynspzslrq === date &&
                !(
                  (!(item.syzt === '待处理' || item.syzt === '处理失败') && item.bgzt !== '已变更') ||
                  item.yqbz === 'Y' ||
                  item.cqbz === 'Y'
                ),
            )
            .map((item) => item.uuid);
          this.selectedRowKeys = [...this.selectedRowKeys, ...uuids];
        }
        // else {
        //   this.selectedRowKeys = value;
        // }
      } else if (newArrLength < currentArrLength) {
        // 取消勾选
        const differenceId = this.selectedRowKeys.filter((item) => !value.includes(item))[0];
        const differrnceSbqxlx = this.tableData.filter((item) => item.uuid === differenceId)[0].sbqxlx;
        if (differrnceSbqxlx !== '按次申报') {
          // 全部按期申报均取消勾选
          const uuids = this.tableData.filter((item) => item.sbqxlx !== '按次申报').map((item) => item.uuid);
          this.selectedRowKeys = this.selectedRowKeys.filter((item) => !uuids.includes(item));
        } else {
          // 2025.2.8 按次税源在同一天的不同税目生成税源时，勾选一条税源需同时将同一天的其他税源一起勾选
          // 2025.4.11  明确此处时间判断应使用应纳税凭证书立（领受）日期字段
          const selectedItem = this.tableData.find((item) => item.uuid === differenceId);
          const date = selectedItem.ynspzslrq;
          const uuids = this.tableData
            .filter((item) => item.sbqxlx === '按次申报' && item.ynspzslrq === date)
            .map((item) => item.uuid);
          this.selectedRowKeys = this.selectedRowKeys.filter((item) => !uuids.includes(item));
        }
        // else {
        //   this.selectedRowKeys = value;
        // }
      }
    },
    async init() {
      this.initYhsHtZspm();
      this.initYhsHtZszm();
      this.checkYyzb();
      this.query();
    },
    async checkYyzb() {
      console.log('sessionStorage.getItem', sessionStorage.getItem('doNotRemind'));

      // 2025.1.20 生成税源前校验:
      // 进入印花税台账时系统需判断是否为营业账簿的报送期限内，
      // 若是则弹出提示“请关注营业账簿台账中往期财报最大值是否正确，初次使用本系统请手动填写此项数据，避免申报数据有误。”。
      if (sessionStorage.getItem('doNotRemind') !== 'true') {
        const { data } = await yyzbTsCheck({
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        });
        if (data === 'Y') {
          // const confirmDia = this.$dialog({
          //   header: '提示',
          //   body: '请关注营业账簿台账中往期财报最大值是否正确，初次使用本系统请手动填写此项数据，避免申报数据有误。',
          //   onConfirm: () => {
          //     confirmDia.hide();
          //   },
          // });
          this.yyzbbstxBz = true;
        }
      }
    },
    htlxChange(val, item) {
      if (item.key === 'zspmDm') {
        this.querySearchConfig[4].selectList = this.zszmList
          .filter((i) => this.formData.zspmDm.includes(i.zspmDm))
          .map((d) => ({ value: d.zszmDm, label: d.zszmmc }));
      }
    },
    async query(pm = { flag: false, p: false }) {
      const { flag, p } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
        bgzt: this.bgzt,
      };
      if (p) {
        params = { ...p, ...params }; // 起始时间待解决
        this.$refs.queryControl.setParams(p);
      } else {
        params = {
          ...this.formData,
          zspmDm: multiSelectHandle(this.formData.zspmDm),
          zszmDm: multiSelectHandle(this.formData.zszmDm),
          ...params,
        };
      }
      try {
        const { data } = await initYhsZzQuery(params);
        this.tableData = data.list || [];
        this.pagination.total = data.total;
        this.dataList = data.list;
        // 弹出提示信息
        if (this.dataList.length !== '0') {
          console.log('this.dataList', this.dataList);
          this.dataList.forEach((t) => {
            if (t.cqbz === 'Y' || t.yqbz === 'Y') {
              this.yqcqFlag = true;
              console.log('this.yqcqFlag', this.yqcqFlag);
            }
            if (t.bgzt === '已变更' && t.ysbbz === 'Y') {
              this.sbbgFlag = true;
              console.log('this.sbbgFlag', this.sbbgFlag);
            }
            if (t.bgzt === '已变更' && t.ysbbz === 'N') {
              this.cjbgFlag = true;
              console.log('this.cjbgFlag', this.cjbgFlag);
            }
          });
          if (this.yqcqFlag || this.sbbgFlag || this.cjbgFlag) {
            this.tishiboxvisible = true;
          }
        }
        this.tableData.forEach((item, index) => {
          this.$set(item, 'index', index);
        });
        if (this.selectedRowKeys.length !== 0) {
          const selectedData =
            this.tableData.filter(
              (value) => value.sbqxlx !== '按次申报' && this.selectedRowKeys.includes(value.uuid),
            ) || [];
          if (selectedData.length !== 0) {
            // 全部按期申报均勾选
            const uuids = this.tableData.filter((item) => item.sbqxlx !== '按次申报').map((item) => item.uuid);
            this.selectedRowKeys = [...this.selectedRowKeys, ...uuids];
          }
        }
        if (this.pagination.total > 0) {
          const { data } = await queryYhstzxxHj(params);
          this.footData =
            [
              {
                sjjsje: numberToPrice(data?.sjjsje),
                ynse: numberToPrice(data?.ynse),
              },
            ] || [];
        } else {
          this.footData = [];
        }
      } catch (e) {
        console.error(e);
        this.tableData = [];
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    wxTis() {
      this.boxvisible = true;
    },
    closeBox() {
      this.boxvisible = false;
      this.tishiboxvisible = false;
      this.Scsyboxvisible = false;
      this.deleteRowvisible = false;
    },
    onConfirm() {
      this.zhidaoFlag = true;
      // this.cjbgboxvisible = false;
      // this.sbbgboxvisible = false;
      // this.yqcqboxvisible = false;
      // this.yqcqsbbgboxvisible = false;
      // this.yqcqcjbgboxvisible = false;
    },
    scsy() {
      // 是否存在按次多选同一属期
      // const uniqueKeys = new Set();
      // const selectedData = this.tableData.filter(value => this.selectedRowKeys.includes(value.uuid) && value.sbqxlx === '按次申报');
      // for (const item of selectedData) {
      //   const key = `${item.zspmmc}-${item.zszmmc}`;
      //   if (uniqueKeys.has(key)) {
      //     return true;  // 发现了重复
      //   }
      //   uniqueKeys.add(key);
      // }
      if (this.scsybtnloading) return; // 新增：如果正在加载中，直接返回
      this.Scsyboxvisible = true;
    },
    async confirmScsybox() {
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选择要生成税源的数据');
        return;
      }
      if (this.scsybtnloading) return; // 新增：防止重复提交
      this.scsyLoading = true;
      this.scsybtnloading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
      };

      const selectedData = this.tableData.filter(
        (value) => this.selectedRowKeys.includes(value.uuid) && value.sbqxlx !== '按次申报',
      );
      if (selectedData.length > 0) {
        const acUuidList = this.tableData
          .filter((value) => this.selectedRowKeys.includes(value.uuid) && value.sbqxlx === '按次申报')
          .map((value) => value.uuid);
        // 存在按期生成税源
        params = {
          ...this.formData,
          symxUuidList: acUuidList,
          ...params,
        };
      } else {
        params = {
          symxUuidList: this.selectedRowKeys,
          ...params,
        };
      }
      try {
        // 2025.1.20 生成税源前校验:
        // 印花税生成税源时增加提示，若选中税源有营业账簿，系统检查往期最大值是否为0，
        // 若为0则提示“本期营业账簿的往期财报最大值为0，是否确认生成税源？”选择“是”则生成税源，选择“否”则中断生成税源操作。
        const { data } = await yyzbWqzdzCheck(params);
        if (data === 'N') {
          const confirmDia = this.$dialog({
            header: '提示',
            body: '本期营业账簿的往期财报最大值为0，是否确认生成税源？',
            onConfirm: async () => {
              try {
                // 立即显示成功提示并关闭弹窗
                this.$message.success('提交成功，后续请关注税源状态的变化。');
                confirmDia.destroy();
                this.selectedRowKeys = [];
                // 异步调用接口
                await syYhsSy(params);
              } catch (e) {
                console.error('生成税源失败:', e);
              } finally {
                // 接口返回后恢复按钮状态并刷新页面
                this.scsybtnloading = false;
                this.query();
              }
            },
            onClose: () => {
              this.scsyLoading = false;
              this.scsybtnloading = false;
              this.Scsyboxvisible = false;
              confirmDia.hide();
            },
          });
        } else {
          try {
            // 立即显示成功提示
            this.$message.success('提交成功，后续请关注税源状态的变化。');
            this.Scsyboxvisible = false;
            this.selectedRowKeys = [];
            // 异步调用接口
            await syYhsSy(params);
          } catch (e) {
            console.error('生成税源失败:', e);
          } finally {
            // 接口返回后恢复按钮状态并刷新页面
            this.scsybtnloading = false;
            this.query();
          }
        }
      } finally {
        this.scsyLoading = false;
        this.Scsyboxvisible = false;
        yhsJyssReadyStatusFetch(
          this.$store.state.zzstz.userInfo?.djxh || '',
          this.formData.gsh,
          this.formData.nsrsbh,
          this.sszqToExtract,
          this.ywlx,
        );
      }
    },
    async initYhsHtZspm() {
      console.log(this.formData.ssyfq, 'this.formData.ssyfq');
      const { data } = await getYhsHtZspm(this.formData.ssyfq);
      console.log(data, 'data');
      this.zspmList = data;
      this.querySearchConfig[3].selectList = this.zspmList.map((d) => ({ value: d.zspmDm, label: d.zspmmc }));
    },
    async initYhsHtZszm() {
      const { data } = await getYhsHtZszm(this.formData.ssyfq);
      this.zszmList = data;
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }
      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        // djxh: '10111525000001030001',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: 'yhstzxx',
        fileName: '印花税台账信息表导出',
        cxParam: {
          ...this.formData,
          zspmDm: multiSelectHandle(this.formData.zspmDm),
          zszmDm: multiSelectHandle(this.formData.zszmDm),
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}

/deep/.active-row > td {
  background: #d8e6fa !important;
}
.dialogMiddleCss {
  /deep/ .t-dialog--default {
    width: 750px !important;
    height: 30% !important;
  }
  /deep/ .t-dialog__body .nr {
    height: 80% !important;
  }
}
.yyzbtx {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
  }
  .dialog-footer .t-checkbox {
    margin-right: 10px;
  }
}
</style>
