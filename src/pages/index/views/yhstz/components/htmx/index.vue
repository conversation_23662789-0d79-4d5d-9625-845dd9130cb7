<template>
  <div class="znsbBackGroupDiv adaption-wrap">
    <t-tabs v-model="tabValue" @change="handleTabChange">
      <!-- 合同明细页签 -->
      <search-control-panel
        v-show="tabValue === '1'"
        style="margin-top: 16px"
        class="znsbHeadqueryDiv"
        ref="queryControl"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
      />
      <search-control-panel
        v-show="tabValue === '2'"
        style="margin-top: 16px"
        class="znsbHeadqueryDiv"
        ref="queryControlFilter"
        :config="querySearchConfig"
        :form-rules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :col-num="4"
        @formChange="(v) => (formDataFilter = v)"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="newOrEditRow"><add-icon slot="icon" />新增</t-button>
          <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
          <t-button theme="primary" :disabled="deleteAllEnable" @click="delAll"
            ><DeleteIcon slot="icon" />删除全部</t-button
          >
          <!-- <ExtractDataButton
            variant="outline"
            :sszq="sszqToExtract"
            :readyStatus="readyStatus"
            :ywtsMsg="'财务凭证数据以及合同数据'"
            :ywlx="'yhs'"
            @query="query"
          /> -->
          <!-- <t-dropdown
            :options="[
              { content: '下载模版', value: 1, onClick: () => downloadTemplate() },
              { content: '导入数据', value: 2, onClick: () => importExcel() },
            ]"
          >
            <t-button variant="outline" theme="primary"> <UploadIcon slot="icon" /><span>导入</span> </t-button>
          </t-dropdown> -->
          <t-button variant="outline" theme="primary" @click="downloadTemplate"
            ><FileIcon slot="icon" />下载模版</t-button
          >
          <t-upload
            ref="uploadRef"
            action="/nssb/yhsHtmxDlController/uploadExcel"
            :tips="tips"
            v-model="files"
            :auto-upload="false"
            theme="custom"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            @fail="handleFail"
            @success="handleSuccess"
            :beforeUpload="handleBeforeUpload"
          >
            <t-button variant="outline" theme="primary"><UploadIcon slot="icon" />导入数据</t-button>
          </t-upload>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <a ref="downloadTemplate" style="display: none" href="./合同导入模板.xlsx"></a>
          <!--        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="drmb()">导入模板</t-button>
        <t-button variant="outline" theme="primary" :loading="dcLoading" @click="dr()">导入</t-button>-->
          <QsbButton />
        </gt-space>
        <t-button
          variant="outline"
          theme="primary"
          v-if="fromName"
          @click="$emit('openPage', { type: fromName, notQuery: true })"
          ><RollbackIcon slot="icon" />返回</t-button
        >
      </div>
      <t-tab-panel value="1" label="合同明细" :destroy-on-hide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="tableData"
            :columns="htColumns"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="pagination"
            @page-change="pageChange($event)"
            :loading="tableLoading"
            :foot-data="footData"
          >
            <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
            <template #ydbz="{ row }">
              {{ { Y: '是', N: '否' }[row.ydbz] || '' }}
            </template>
            <template #bhshtebz="{ row }">
              {{ { Y: '是', N: '否' }[row.bhshtebz] || '' }}
            </template>
            <template #jryhsbz="{ row }">
              {{ { Y: '是', N: '否' }[row.jryhsbz] || '' }}
            </template>
            <template #bzshtbz="{ row }">
              {{ { Y: '是', N: '否' }[row.bzshtbz] || '' }}
            </template>
            <template #fbhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.fbhtbj] || '' }}
            </template>
            <template #kjhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.kjhtbj] || '' }}
            </template>
            <template #bhsje="{ row }">
              <span>{{ numberToPrice(row.bhsje) }}</span>
            </template>
            <template #operation="{ row }">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)"> 编辑 </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
      <!-- 不合规合同页签 -->
      <t-tab-panel value="2" label="不合规合同" :destroy-on-hide="false">
        <div class="znsbSbBodyDiv">
          <t-table
            ref="tableRef"
            row-key="uuid"
            height="100%"
            hover
            :data="tableDataFilter"
            :columns="htColumnsFilter"
            :selected-row-keys="selectedRowKeys"
            @select-change="rehandleSelectChange"
            :pagination="paginationFilter"
            @page-change="pageChange($event)"
            :loading="tableLoading"
            :foot-data="footDataFilter"
          >
            <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
            <template #ydbz="{ row }">
              {{ { Y: '是', N: '否' }[row.ydbz] || '' }}
            </template>
            <template #bhshtebz="{ row }">
              {{ { Y: '是', N: '否' }[row.bhshtebz] || '' }}
            </template>
            <template #jryhsbz="{ row }">
              {{ { Y: '是', N: '否' }[row.jryhsbz] || '' }}
            </template>
            <template #bzshtbz="{ row }">
              {{ { Y: '是', N: '否' }[row.bzshtbz] || '' }}
            </template>
            <template #fbhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.fbhtbj] || '' }}
            </template>
            <template #kjhtbj="{ row }">
              {{ { Y: '是', N: '否' }[row.kjhtbj] || '' }}
            </template>
            <template #sldd="{ row }">
              {{ { Y: '是', N: '否' }[row.sldd] || '' }}
            </template>
            <template #bhsje="{ row }">
              <span>{{ numberToPrice(row.bhsje) }}</span>
            </template>
            <template #operation="{ row }">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)"> 编辑 </t-link>
              <t-link theme="primary" class="t-link-btn" hover="color" @click="deleteClickRow(row)">
                &nbsp;删除
              </t-link>
            </template>
          </t-table>
        </div>
      </t-tab-panel>
    </t-tabs>
    <EditDialog :visible.sync="editDialogVisible" v-if="editDialogVisible" @updateHtmx="query()" />
    <div v-show="boxvisible">
      <t-dialog
        theme="warning"
        style="display: block; max-height: 90vh; overflow-y: auto; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDelRow"
        :onClose="closeBox"
      >
      </t-dialog>
    </div>
    <div v-show="deleteRowvisible">
      <t-dialog
        theme="warning"
        style="display: block; max-height: 90vh; overflow-y: auto; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除所选明细"
        :onConfirm="confirmDeleleRow"
        :onClose="closeDelRowBox"
      >
      </t-dialog>
    </div>
    <div v-show="deleteAllvisible">
      <t-dialog
        theme="warning"
        style="display: block; max-height: 90vh; overflow-y: auto; border-radius: 10px"
        :width="400"
        header="警示"
        body="请确认是否删除查询出的所有明细"
        :onConfirm="confirmDeleleAll"
        :onClose="closeDelAllBox"
      >
      </t-dialog>
    </div>
    <div v-show="uploadConfirmVisible">
      <t-dialog
        theme="warning"
        style="display: block; max-height: 90vh; overflow-y: auto; border-radius: 10px"
        :width="400"
        header="警示"
        body="导入后系统将导入模版中所有合同数据，此操作需要消耗一定时间，请勿频繁操作。导入数据后系统将自动进行台账数据加工及算税，请注意核对数据。是否确认导入操作？"
        :onConfirm="confirmUpload"
        :onClose="closeUploadBox"
      >
      </t-dialog>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import QsbButton from '@/pages/index/components/QsbButton';
import ExtractDataButton from '@/pages/index/components/ExtractDataButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import {
  queryHtmx,
  queryHj,
  deleteFailSelected,
  deleteSelected,
  deleteAll,
  uploadExcel,
} from '@/pages/index/api/tzzx/yhstz/htmx.js';
import { downloadBlobFile } from '@/core/download';
import { getYhsHtZspm, getYhsHtZszm } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import { AddIcon, DeleteIcon, FileIcon, UploadIcon, DownloadIcon, RollbackIcon } from 'tdesign-icons-vue';
import { numberToPrice } from '@/utils/numberToCurrency';
import { multiSelectHandle } from '@/pages/index/views/util/tzzxTools.js';
import EditDialog from './components/edit-dialog.vue';
import { querySearchConfig, querySearchConfigOneRules, htColumns } from './config.js';
import { ref, computed, watch } from 'vue';

export default {
  components: {
    QsbButton,
    ExtractDataButton,
    EditDialog,
    SearchControlPanel,
    AddIcon,
    FileIcon,
    UploadIcon,
    DeleteIcon,
    DownloadIcon,
    RollbackIcon,
  },
  data() {
    this.querySearchConfigOneRules = querySearchConfigOneRules;
    this.htColumns = htColumns;
    return {
      tabValue: '1',
      formData: {}, // 页签1的表单数据
      formDataFilter: {}, // 页签2的表单数据
      dcLoading: false,
      querySearchConfig,
      boxvisible: false,
      deleteRowvisible: false,
      tableLoading: false,
      editDialogVisible: false,
      deleteAllvisible: false,
      fromName: false,
      queryParams: {},
      zspmDmList: [],
      zszmDmList: [],
      delformData: [],
      checkBox: [],
      selectedRowKeys: [],
      htlxList: [{ value: '01', label: '借款合同' }],
      htzlList: [{ value: '01', label: '金融机构借款合同' }],
      pagination: { current: 1, pageSize: 50, total: 0 },
      paginationFilter: { current: 1, pageSize: 10, total: 0 },
      tableData: [],
      tableDataFilter: [],
      footData: [],
      footDataFilter: [],
      tips: '',
      files: [],
      deleteRow: null,
      lastQueryParamsTab1: null, // 记录页签1的上次查询参数
      lastQueryParamsTab2: null, // 记录页签2的上次查询参数
      deleteAllEnable: true,
      pendingFile: null,
      uploadConfirmVisible: false,
      uploadkey: 'canUpload',
    };
  },
  created() {
    this.formData.sszq = dayjs().subtract(1, 'month').format('YYYY-MM');
  },
  mounted() {
    this.init();
  },
  watch: {
    'formData.zspmDm': {
      handler(newVal) {
        // 根据 zspmDm 的变化更新 zszmDm
        this.updateZszmDm(newVal);
      },
      deep: true, // 如果 zspmDm 是对象或数组，可能需要深度监听
    },
    formData: {
      handler(newVal) {
        console.log('formData changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
    formDataFilter: {
      handler(newVal) {
        console.log('formDataFilter changed:', JSON.parse(JSON.stringify(newVal)));
      },
      deep: true,
    },
  },
  computed: {
    htColumnsFilter() {
      const columns = [...this.htColumns];
      // 查找“操作”列的索引
      const operationIndex = columns.findIndex((col) => col.colKey === 'operation');

      // 如果找到“操作”列，在它前面插入“失败原因”列
      if (operationIndex > -1) {
        columns.splice(operationIndex, 0, {
          width: 300,
          colKey: 'sbyy',
          align: 'center',
          title: '失败原因',
          fixed: 'right',
        });
      }
      return columns;
    },
    currentFormData() {
      return this.tabValue === '2' ? this.formDataFilter : this.formData;
    },
    currentPagination() {
      return this.tabValue === '2' ? this.paginationFilter : this.pagination;
    },
    sszqToExtract() {
      return dayjs(this.formData.sszqq).format('YYYYMM');
    },
    readyStatus() {
      console.log('readyYhsStatus', this.$store.state.jyss.readyYhsStatus);
      return this.$store.state.jyss.readyYhsStatus;
    },
    sszqDefault() {
      return dayjs(this.formData.sszq).format('YYYYMM');
    },
  },
  methods: {
    clearFiles() {
      // 清空文件
      this.files = [];
    },
    handleFail(res) {
      console.log('handleFail', res);
      this.$loading(false);
      this.$message.error(res);
    },
    handleSuccess(res) {
      console.log('handleSuccess', res);
      if (res.response?.data) {
        this.$loading(false);
        if (res.response.data?.code === '00') {
          this.$message.success(res.response.data.msg);
        } else {
          this.$message.warning(res.response.data.msg);
        }
      } else {
        this.$loading(false);
        this.$message.error(`导入失败`);
      }
      this.clearFiles();
      this.query({ flag: true });
    },
    handleTabChange(activeTab) {
      // 获取目标页签的上次查询条件
      const targetTabLastParams = activeTab === '1' ? this.lastQueryParamsTab1 : this.lastQueryParamsTab2;

      // 获取当前表单数据作为新的查询条件
      const currentFormData = activeTab === '1' ? this.formData : this.formDataFilter;

      const newParams = {
        ...currentFormData,
        tabValue: activeTab,
      };

      // 如果没有上次查询记录或查询条件变化时才执行查询
      if (!targetTabLastParams || JSON.stringify(newParams) !== JSON.stringify(targetTabLastParams)) {
        this.query({ flag: true });
      }

      // 更新目标页签的上次查询记录
      if (activeTab === '1') {
        this.lastQueryParamsTab1 = newParams;
      } else {
        this.lastQueryParamsTab2 = newParams;
      }
    },
    updateZszmDm(zspmDm) {
      // 根据 zspmDm 更新 zszmDm
      const filteredZszmDmList = this.zszmDmList.filter((item) => zspmDm.includes(item.zspmDm));
      this.querySearchConfig[4].selectList = filteredZszmDmList.map((d) => ({ value: d.zszmDm, label: d.zszmmc }));
      // 如果this.formData.zszmDm中的值在this.querySearchConfig[4].selectList内，则不清空this.formData.zszmDm
      const validValues = this.querySearchConfig[4].selectList.map((item) => item.value);
      const hasValidValues = (this.formData.zszmDm || []).some((value) => validValues.includes(value));
      if (hasValidValues) {
        return; // 有有效值时不执行清空操作
      }
      this.formData.zszmDm = []; // 清空当前的 zszmDm 选择
    },
    closeBox() {
      this.boxvisible = false;
    },
    closeDelRowBox() {
      this.deleteRowvisible = false;
    },
    handleBeforeUpload() {
      this.$nextTick(() => {
          this.uploadConfirmVisible = true;
      });
      console.log('this.uploadConfirmVisibl111e', this.uploadConfirmVisible);
      return true;
    },
    confirmUpload() {
      this.$loading(true);
      this.$refs.uploadRef.uploadFiles();
      this.closeUploadBox();
    },
    closeUploadBox() {
      this.$nextTick(() => {
        this.clearFiles();
        this.uploadConfirmVisible = false;
      });
    },
    async exportExcl(isAll) {
      const isFilterTab = this.tabValue === '2';
      if (isFilterTab) {
        if (!this.tableDataFilter.length) {
          this.$message.warning('表格数据为空，请查询后重试');
          return;
        }
      } else {
        if (!this.tableData.length) {
          this.$message.warning('表格数据为空，请查询后重试');
          return;
        }
      }

      const djParam = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      const params = {
        yzpzzlDm: 'BDA0610794',
        tzlx: isFilterTab ? 'htmxbhgdl' : 'htmxdl',
        fileName: isFilterTab ? '不合规合同明细导出' : '合同明细导出',
        cxParam: {
          ...this.formData,
          sszqq: Number(dayjs(this.formData.sszqq).format('YYYYMM')),
          sszqz: Number(dayjs(this.formData.sszqz).format('YYYYMM')),
          htlx: this.formData.zspmDm ? multiSelectHandle(this.formData.zspmDm) : '',
          htzl: this.formData.zszmDm ? multiSelectHandle(this.formData.zszmDm) : '',
          cgbz: isFilterTab ? 'N' : 'Y',
          ...djParam,
        },
      };
      if (isAll === 'all') {
        params.cxParam.pageNo = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      this.dcLoading = true;
      const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
      if (res.status === 200) {
        this.$message.success('导出成功');
      } else {
        this.$message.error('导出失败');
      }
      this.dcLoading = false;
    },
    newOrEditRow(row) {
      const pageType = row?.uuid ? 1 : 0;
      const otherObj = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        sszq: Number(dayjs(this.formData.sszq).format('YYYYMM')),
      };
      const rowdata = JSON.stringify(row);
      this.editDialogVisible = { row: JSON.parse(rowdata), otherObj, pageType };
    },
    async delRow() {
      console.log('this.selectedRowKeys', this.selectedRowKeys);
      if (!this.selectedRowKeys.length) {
        this.$message.warning('请先选中要删除的行');
      } else {
        this.boxvisible = true;
      }
    },
    async delAll() {
      this.deleteAllvisible = true;
    },
    async confirmDeleleAll() {
      this.closeDelAllBox();
      try {
        const { data } = await deleteAll(this.queryParams);
        console.log('deleteAll-msg', data);
        this.$message.success(data);
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    closeDelAllBox() {
      this.deleteAllvisible = false;
    },
    deleteClickRow(row) {
      this.deleteRow = row;
      this.deleteRowvisible = true;
    },
    confirmDelRow() {
      // 关闭弹窗
      this.closeBox();
      // 执行删行
      this.checkBox.forEach((item) => {
        const index = this.tableData.findIndex((i) => i.uuid === item.uuid);
        this.tableData.splice(index, 1);
        this.delformData.push(item.uuid);
      });
      const params = this.delformData;
      const isFilterTab = this.tabValue === '2';
      const cgbz = isFilterTab ? false : true;
      this.delete(params, cgbz);
      this.checkBox = [];
      this.selectedRowKeys = []; // 重置选择框的数据，不然会存在历史数据导致多选时，数据增加
      // this.$message.success('删除成功');
    },
    confirmDeleleRow() {
      // 关闭弹窗
      this.closeDelRowBox();
      // 执行删行
      if (!this.deleteRow) {
        this.$message.warning('没有可删除的数据');
        return;
      }
      this.delformData.push(this.deleteRow.uuid);
      const params = this.delformData;
      const isFilterTab = this.tabValue === '2';
      const cgbz = isFilterTab ? false : true;
      this.delete(params, cgbz);
      // 重置对应行
      this.deleteRow = null;
    },
    rehandleSelectChange(value, { selectedRowData }) {
      console.log('@@@', value, selectedRowData);
      this.selectedRowKeys = value;
      this.checkBox = selectedRowData.filter((i) => i);
    },
    async init() {
      this.initYhsHtZspm();
      this.initYhsHtZszm();
    },
    async query(pm = { flag: false, p: false }) {
      this.deleteAllEnable = true;
      const { flag, p, from } = pm;
      if (dayjs(this.currentFormData.sszqz).diff(dayjs(this.currentFormData.sszqq), 'month') > 2) {
        this.$message.warning('时间范围不能超过三个月');
        return;
      }
      // 下钻进入时，需要返回按钮能够返回至上次操作页面
      this.fromName = from ?? this.fromName;
      // flag标志为ture时，需要重置查询，分页重置为首页
      if (flag) {
        this.tabValue === '2' ? (this.paginationFilter.current = 1) : (this.pagination.current = 1);
      }
      this.tableLoading = true;
      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
        nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
        pageNo: this.currentPagination.current,
        pageSize: this.currentPagination.pageSize,
      };
      if (p) {
        // 下钻进入时，默认进入明细页
        this.tabValue = '1';
        params = {
          ...p,
          sszqq: Number(p.sszqq),
          sszqz: Number(p.sszqz),
          ...params,
        };
        this.$refs.queryControl.setParams({
          sszqq: dayjs(p.sszqq).format('YYYY-MM'),
          sszqz: dayjs(p.sszqz).format('YYYY-MM'),
          htlx: p.zspmDm.split(','),
          htzl: p.zszmDm.split(','),
        });
      } else {
        params = {
          ...params,
          ...this.currentFormData,
          sszqq: Number(dayjs(this.currentFormData.sszqq).format('YYYYMM')),
          sszqz: Number(dayjs(this.currentFormData.sszqz).format('YYYYMM')),
          htlx: this.currentFormData.zspmDm ? multiSelectHandle(this.currentFormData.zspmDm) : '',
          htzl: this.currentFormData.zszmDm ? multiSelectHandle(this.currentFormData.zszmDm) : '',
        };
      }
      try {
        const isFilterTab = this.tabValue === '2';
        params.cgbz = isFilterTab ? 'N' : 'Y';
        this.queryParams = params;
        const { data } = await queryHtmx(params);
        if (!isFilterTab) {
          this.tableData = (data?.list || []).map((item) => {
            const matchedZspm = this.zspmDmList.find((d) => d.zspmDm === item.htlx);
            const matchedZszm = this.zszmDmList.find((d) => d.zspmDm === item.htzl);
            return {
              ...item,
              zspmDm: item.htlx,
              zszmDm: item.htzl,
              zspmmc: matchedZspm ? matchedZspm.zspmmc : '',
              zszmmc: matchedZszm ? matchedZszm.zszmmc : '',
            };
          });
          this.pagination.total = data?.total || 0;
          this.tableData.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
        } else {
          this.tableDataFilter = (data?.list || []).map((item) => {
            const matchedZspm = this.zspmDmList.find((d) => d.zspmDm === item.htlx);
            const matchedZszm = this.zszmDmList.find((d) => d.zspmDm === item.htzl);
            return {
              ...item,
              zspmDm: item.htlx,
              zszmDm: item.htzl,
              zspmmc: matchedZspm ? matchedZspm.zspmmc : '',
              zszmmc: matchedZszm ? matchedZszm.zszmmc : '',
            };
          });
          this.paginationFilter.total = data?.total || 0;
          this.tableDataFilter.forEach((item, index) => {
            this.$set(item, 'index', index);
          });
        }
        if (this.currentPagination.total > 0) {
          const { data } = await queryHj(params);
          if (isFilterTab) {
            this.footDataFilter =
              [
                {
                  bhsje: numberToPrice(data ? data : 0),
                },
              ] || [];
          } else {
            this.footData =
              [
                {
                  bhsje: numberToPrice(data ? data : 0),
                },
              ] || [];
          }
        } else {
          this.footData = [];
          this.footDataFilter = [];
        }
        // 查询成功后更新对应页签的上次查询记录
        const currentParams = {
          ...params,
          tabValue: this.tabValue,
        };
        if (this.isSameQuarter(params.sszqq)) {
          this.deleteAllEnable = false;
        }
        if (this.tabValue === '1') {
          this.lastQueryParamsTab1 = currentParams;
        } else {
          this.lastQueryParamsTab2 = currentParams;
        }
      } catch (e) {
        this.tableData = [];
        this.tableDataFilter = [];
      } finally {
        this.tableLoading = false;
      }
    },
    async delete(params, cgbz) {
      try {
        if (cgbz) {
          const { msg } = await deleteSelected(params);
          console.log('deleteSelected-msg', msg);
          this.$message.success(msg);
        } else {
          const { msg } = await deleteFailSelected(params);
          console.log('deleteFailSelected-msg', msg);
          this.$message.success(msg);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.query();
        this.delformData = [];
      }
    },
    async initYhsHtZspm() {
      console.log('this.formData', this.formData);
      console.log('this.formData.sszq', this.formData.sszq);
      const { data } = await getYhsHtZspm(this.formData.sszq);
      this.zspmDmList = data;
      this.querySearchConfig[3].selectList = this.zspmDmList.map((d) => ({ value: d.zspmDm, label: d.zspmmc }));
      console.log('this.zspmDmList', JSON.stringify(this.zspmDmList));
    },
    async initYhsHtZszm() {
      console.log('this.formData.sszq', this.formData.sszq);
      const { data } = await getYhsHtZszm(this.formData.sszq);
      this.zszmDmList = data;
    },

    // 分页处理
    pageChange({ current, pageSize }) {
      const pagination = this.tabValue === '2' ? this.paginationFilter : this.pagination;
      pagination.current = pageSize !== pagination.pageSize ? 1 : current;
      pagination.pageSize = pageSize;
      this.query();
    },
    numberToPrice(number) {
      return numberToPrice(number);
    },
    // 下载模板
    async downloadTemplate() {
      this.$refs.downloadTemplate.click();
    },
    isSameQuarter(input) {
      // 确保 input 是字符串，如 202504 → '202504'
      const inputStr = String(input);

      // 输入校验：确保是6位数字字符串
      if (!/^\d{6}$/.test(inputStr)) {
        console.warn('无效的日期输入:', input);
        return false;
      }

      const year = parseInt(inputStr.slice(0, 4), 10); // 提取年份
      const month = parseInt(inputStr.slice(4, 6), 10); // 提取月份（1-12）

      // 获取当前时间的上个月
      const now = dayjs();
      const lastMonthDate = now.subtract(1, 'month');

      // 构造一个 dayjs 对象表示输入的日期
      const inputDate = dayjs()
        .year(year)
        .month(month - 1); // 注意：dayjs 的 month 是从 0 开始

      // 计算季度
      const inputQuarter = Math.floor((inputDate.month() + 3) / 3);
      const lastMonthQuarter = Math.floor((lastMonthDate.month() + 3) / 3);

      // 判断年份相同且季度相同
      return inputDate.year() === lastMonthDate.year() && inputQuarter === lastMonthQuarter;
    },
  },
};
</script>
<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';
/deep/ .t-table__footer > tr:first-child {
  td {
    text-align: right;
  }
  td:first-child {
    text-align: left;
  }
}
/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.znsbHeadqueryDiv {
  min-height: 48px !important;
  padding-top: 0 !important;
  // border-bottom: 1px solid #eee;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}
.znsbSbBodyDiv {
  height: 100% !important;
}
/deep/ .t-tabs__content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
}
/deep/ .t-tab-panel {
  height: 0;
  flex: 1;
}
/deep/.active-row > td {
  background: rgb(210, 231, 251) !important;
}
/deep/ .minimum {
  position: fixed !important;
}
/deep/ .maximum {
  position: fixed !important;
  left: 0 !important;
  z-index: 999 !important;
}
</style>
