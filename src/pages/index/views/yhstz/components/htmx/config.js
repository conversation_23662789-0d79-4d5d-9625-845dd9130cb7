import dayjs from 'dayjs';

export const querySearchConfigOneRules = {
  sszqq: [{ required: true, message: '必填项', type: 'error' }],
  sszqz: [{ required: true, message: '必填项', type: 'error' }],
};
export function Ssyf() {
  const year = dayjs().format('YYYY-MM').substring(0, 4);
  const month = dayjs().format('YYYY-MM').substring(5, 7);
  let sszqq;
  let sszqz;
  switch (month) {
    case '01':
      sszqq = `${(parseInt(year, 10) - 1).toString()}-10`;
      sszqz = `${(parseInt(year, 10) - 1).toString()}-12`;
      break;
    case '02':
      sszqq = `${year}-01`;
      sszqz = `${year}-03`;
      break;
    case '03':
      sszqq = `${year}-01`;
      sszqz = `${year}-03`;
      break;
    case '04':
      sszqq = `${year}-01`;
      sszqz = `${year}-03`;
      break;
    case '05':
      sszqq = `${year}-04`;
      sszqz = `${year}-06`;
      break;
    case '06':
      sszqq = `${year}-04`;
      sszqz = `${year}-06`;
      break;
    case '07':
      sszqq = `${year}-04`;
      sszqz = `${year}-06`;
      break;
    case '08':
      sszqq = `${year}-07`;
      sszqz = `${year}-09`;
      break;
    case '09':
      sszqq = `${year}-07`;
      sszqz = `${year}-09`;
      break;
    case '10':
      sszqq = `${year}-07`;
      sszqz = `${year}-09`;
      break;
    case '11':
      sszqq = `${year}-10`;
      sszqz = `${year}-12`;
      break;
    case '12':
      sszqq = `${year}-10`;
      sszqz = `${year}-12`;
      break;
    default:
      break;
  }
  return { sszqq, sszqz };
}
export const querySearchConfig = [
  {
    label: '所属月份起',
    key: 'sszqq',
    type: 'datepicker',
    mode: 'month',
    value: Ssyf().sszqq,
    placeholder: '请选择',
    clearable: true,
    relation: 'sszqz',
    timeRange: 'start',
  },
  {
    label: '所属月份止',
    key: 'sszqz',
    type: 'datepicker',
    mode: 'month',
    value: Ssyf().sszqz,
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'sszqq',
  },
  {
    label: '应税凭证编号',
    key: 'yzpzbh',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '合同类型',
    key: 'zspmDm',
    type: 'select',
    filterable: true,
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '合同子类',
    key: 'zszmDm',
    type: 'select',
    multiple: true,
    selectList: [],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '签订日期起',
    key: 'htqdrqq',
    type: 'datepicker',
    placeholder: '请选择',
    clearable: true,
    relation: 'qdrqz',
    timeRange: 'start',
  },
  {
    label: '签订日期止',
    key: 'htqdrqz',
    type: 'datepicker',
    placeholder: '请选择',
    clearable: true,
    timeRange: 'end',
    relation: 'htqdrqq',
  },
  {
    label: '不含税金额',
    key: 'bhsje',
    type: 'input',
    placeholder: '请输入',
    clearable: true,
  },
  {
    label: '是否计入印花税',
    key: 'jryhsbz',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '是否不征税合同',
    key: 'bzshtbz',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
  {
    label: '是否是分包合同',
    key: 'fbhtbj',
    type: 'select',
    value: '',
    selectList: [
      { value: 'Y', label: '是' },
      { value: 'N', label: '否' },
    ],
    placeholder: '请选择',
    clearable: true,
  },
];
export const htColumns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    width: 50,
    align: 'center',
    colKey: 'xh',
    title: '序号',
    foot: '合计',
  },
  {
    width: 80,
    colKey: 'sszq',
    align: 'center',
    title: '所属月份',
  },
  {
    width: 120,
    colKey: 'ywfw',
    title: '业务范围',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 180,
    colKey: 'yzpzbh',
    title: '应税凭证编号',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'zspmmc',
    title: '合同类型',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'zszmmc',
    title: '合同子类',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 120,
    colKey: 'htqdrq',
    title: '合同签订日期',
    cell: (h, { row }) => <div>{row.htqdrq ? dayjs(row.htqdrq).format('YYYY/MM/DD') : ''}</div>,
  },
  {
    align: 'center',
    width: 120,
    colKey: 'ydbz',
    title: '是否异地',
  },
  {
    align: 'center',
    width: 140,
    colKey: 'bhshtebz',
    title: '是否不含税合同额',
  },
  {
    align: 'center',
    width: 120,
    colKey: 'jryhsbz',
    title: '是否计入印花税',
  },
  {
    align: 'center',
    width: 140,
    colKey: 'bzshtbz',
    title: '是否是不征税合同',
  },
  {
    width: 200,
    colKey: 'bzsyy',
    title: '不征税原因',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    width: 200,
    colKey: 'sldd',
    title: '书立地点',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    width: 120,
    colKey: 'fbhtbj',
    title: '分包合同标记',
  },
  {
    align: 'center',
    width: 120,
    colKey: 'kjhtbj',
    title: '框架合同标记',
  },
  {
    width: 120,
    colKey: 'jsrqq',
    title: '预计结算期起',
    cell: (h, { row }) => <div>{row.jsrqq ? dayjs(row.jsrqq).format('YYYY/MM/DD') : ''}</div>,
  },
  {
    width: 120,
    colKey: 'jsrqz',
    title: '预计结算期止',
    cell: (h, { row }) => <div>{row.jsrqz ? dayjs(row.jsrqz).format('YYYY/MM/DD') : ''}</div>,
  },
  {
    align: 'center',
    width: 140,
    colKey: 'bz',
    title: '备注',
  },
  {
    align: 'right',
    width: 120,
    colKey: 'bhsje',
    title: '不含税金额',
    fixed: 'right',
    ellipsis: {
      theme: 'light',
      placement: 'bottom',
    },
  },
  {
    align: 'center',
    colKey: 'operation',
    title: '操作',
    width: 80,
    fixed: 'right',
  },
];

export const sjjsColumn = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ly === '1') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    align: 'center',
    width: '10%',
    colKey: 'index',
    title: '序号',
    cell: 'index-cell',
  },
  {
    align: 'center',
    width: '40%',
    colKey: 'sjjsje',
    title: '实际结算金额',
  },
  {
    align: 'center',
    width: '40%',
    colKey: 'sjjsrq',
    title: '实际结算日期',
  },
];

export const dfslColumn = [
  {
    colKey: 'row-select',
    type: 'multiple',
    className: 'demo-multiple-select-cell',
    checkProps: ({ row }) => ({ disabled: !(row.ywqdDm === 'TZ_USER') }),
    width: 50,
    align: 'center',
    fixed: 'left',
  },
  {
    align: 'center',
    width: '9%',
    colKey: 'index',
    title: '序号',
    cell: 'index-cell',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrmc',
    title: '对方书立人名称',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrnsrsbh',
    title: '对方书立人纳税人识别号（统一社会信用代码）',
  },
  {
    align: 'center',
    width: '27%',
    colKey: 'dfslrsjje',
    title: '对方书立人涉及金额',
  },
];
