<template>
  <div class="mainbody">
    <Mybreadcrumb ref="myBre" @jguuidOnChange="getJguuid"> </Mybreadcrumb>
    <div class="minbox" style="display: flex">
      <TzMenu />
      <div class="tzzt">
        <gt-collapse-menu
          class="ggMenu"
          theme="primary"
          title="台账列表"
          :list="dynamicList"
          expandMutex
          :defaultExpanded="expanded"
          :default-value="defaultValue"
          :toolbar="false"
          :value="active"
          @change="collapseHandle"
        >
          <template #panel>
            <yhszz ref="yhszz" v-show="active === 'yhszz'" @openPage="openPage" @xxfpzzBtn="xxfpzzBtn" />
            <!-- @openHttzmx="openHttzmx"
          @openCqzysjtzmx="openCqzysjtzmx"
          @openYyzbtzmx="openYyzbtzmx" -->
            <httzQs v-if="QuanshanFlag" ref="httz" v-show="active === 'httz'" @openPage="openPage" />
            <httz v-else ref="httz" v-show="active === 'httz'" @openPage="openPage" />
            <htmx ref="htmx" v-show="active === 'htmx'" @openPage="openPage" />
            <pzmx ref="pzmx" v-show="active === 'pzmx'" @openPage="openPage" />
            <!-- <cqzysjtz v-show="active === 'cqzysjtz'" /> -->
            <yyzbtz ref="yyzbtz" v-show="active === 'yyzbtz'" @openPage="openPage" />
            <cybd ref="cybd" v-show="active === 'cybd'" @openPage="openPage" />
          </template>
          <!-- <template #menu-item>menu-item</template> -->
        </gt-collapse-menu>
      </div>
    </div>
  </div>
</template>

<script>
import Mybreadcrumb from '@/pages/index/components/common-breadcrumb/mybreadcrumb';
import yhszz from '@/pages/index/views/yhstz/components/yhszz/index.vue';
import httz from '@/pages/index/views/yhstz/components/httz/index.vue';
import httzQs from '@/pages/index/views/yhstz/components/httz/index-quanshan.vue';
import htmx from '@/pages/index/views/yhstz/components/htmx/index.vue';
import pzmx from '@/pages/index/views/yhstz/components/pzmx/index.vue';
// import cqzysjtz from '@/pages/index/views/yhstz/components/cqzysjtz.vue';
import yyzbtz from '@/pages/index/views/yhstz/components/yyzbtz/index.vue';
import cybd from '@/pages/index/views/yhstz/components/cybd/index.vue';
import TzMenu from '@/pages/index/components/NewTzMenu';
import { CollapseMenu } from '@gt4/common-front';
import { initComputeSszq, isProductEnv, isYsEnv, stopPollingYhs } from '@/pages/index/views/util/tzzxTools.js';
import { companyDifferentiationConfigApi } from '@/pages/index/api/tzzx/gyApi/gyapi.js';

export default {
  components: { GtCollapseMenu: CollapseMenu, TzMenu, yhszz, httz, httzQs, htmx, pzmx, yyzbtz, cybd, Mybreadcrumb },
  data() {
    return {
      expanded: ['1'],
      defaultValue: 'yhszz',
      active: 'yhszz',
      sbrwmxbz: this.$route.query.sbrwmxBz || false,
      sbrwmxAbbbz: this.$route.query.sbrwmxAbbBz || false,
      list: [
        {
          id: '1',
          title: '印花税台账',
          children: [
            {
              id: 'yhszz',
              title: '税源明细',
              // description: '印花税总账',
              required: false,
            },
            {
              id: 'httz',
              title: '合同明细',
              // description: '合同台账',
              required: false,
            },
            {
              id: 'htmx',
              title: '合同明细',
              // description: '合同台账',
              required: false,
            },
            {
              id: 'pzmx',
              title: '凭证明细',
              // description: '凭证明细',
              required: false,
            },
            // {
            //   id: 'cqzysjtz',
            //   title: '产权转移书据明细',
            //   // description: '产权转移书据台账',
            //   required: false,
            // },
            {
              id: 'yyzbtz',
              title: '营业账簿',
              // description: '营业账簿台账',
              required: false,
            },
            // {
            //   id: 'jmxtz',
            //   title: '减免项台账',
            //   // description: '营业账簿台账',
            //   required: false,
            // },
            {
              id: 'cybd',
              title: '差异比对',
              // description: '差异比对',
              required: false,
            },
          ],
        },
      ],
      jtbm: '', // 新增集团编码状态
      ShanxiyidongFlag: false, // 新增山西移动标识
      QuanshanFlag: false, // 新增泉膳标识
      semirFlag: false, // 新增森马标识
      dalianzhonggongFlag: false, // 新增大连重工标识
    };
  },
  async created() {
    initComputeSszq();
    isProductEnv();
    isYsEnv();
    // if (window.location.search) {
    //   // 暂时默认从申报明细跳转
    //   this.sbrwmxbz = true;
    // }
    // getCompanyDifferentiationConfig({
    //   djxh: this.$store.state.zzstz.userInfo.djxh,
    //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
    // });
    const userInfo = window.sessionStorage.getItem('jgxxList');
    this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
    // 新增企业类型判断
    try {
      const res = await companyDifferentiationConfigApi({
        nsrsbh: JSON.parse(userInfo)?.nsrsbh,
        djxh: JSON.parse(userInfo)?.djxh,
      });
      this.jtbm = res.data?.jtbm || '';
      this.ShanxiyidongFlag = this.jtbm === '000003';
      this.QuanshanFlag = this.jtbm === '000004';
      this.semirFlag = this.jtbm === '000001';
      this.dalianzhonggongFlag = this.jtbm === '000002';
    } catch (e) {
      console.error('企业类型查询失败:', e);
    }
  },
  // 修改list为计算属性
  computed: {
    isYsEnv() {
      return this.$store.state.isYs.envValue;
    },
    dynamicList() {
      const baseItems = [
        {
          id: 'yhszz',
          title: '税源明细',
          required: false,
        },
        // 根据大连重工标识过滤合同明细
        ...(this.dalianzhonggongFlag
          ? [
              {
                id: 'htmx',
                title: '合同明细',
                required: false,
              },
            ]
          : [
              {
                id: 'httz',
                title: '合同明细',
                required: false,
              },
            ]),
        // 根据泉膳标识过滤凭证明细
        ...(this.QuanshanFlag || this.ShanxiyidongFlag || this.dalianzhonggongFlag
          ? []
          : [
              {
                id: 'pzmx',
                title: '凭证明细',
                required: false,
              },
            ]),
        {
          id: 'yyzbtz',
          title: '营业账簿',
          required: false,
        },
        // 根据森马标识过滤凭证明细
        ...(this.semirFlag && !this.isYsEnv
          ? [
              {
                id: 'cybd',
                title: '差异比对',
                required: false,
              },
            ]
          : []),
      ];

      return [
        {
          id: '1',
          title: '印花税台账',
          children: [...baseItems],
        },
      ];
    },
  },
  methods: {
    xxfpzzBtn(val, val1) {
      this.active = val;
      this.expanded = [];
      this.expanded.push(val1);
    },
    collapseHandle(val) {
      console.log('当前页面：', val);
      this.active = val;
      const params = { flag: true };
      this.$refs[val].query(params);
    },
    async getJguuid(data) {
      console.log('下拉选data', data);
      this.$store.commit('zzstz/setUserInfoData', data[0]);
      sessionStorage.setItem('jgxxList', JSON.stringify(data[0]));
      stopPollingYhs();
      // // 使用 await 等待 getCompanyDifferentiationConfig 完成
      // await getCompanyDifferentiationConfig({
      //   djxh: this.$store.state.zzstz.userInfo.djxh,
      //   nsrsbh: this.$store.state.zzstz.userInfo.nsrsbh,
      // });
      // // 确保状态更新完成
      // await this.$nextTick();
      const params = { flag: true };
      this.$refs[this.active].query(params);
      if (this.active === 'pzmx') {
        this.$refs[this.active].getLrzx();
      }
    },
    // data不传的话默认用子页面的查询参数。notQuery传true时子页面不触发查询。from为父页面activ，用于子页面返回对应父页面，按需将from储存在子页面。
    openPage({ data = false, type, notQuery = false, from = false, flag = true }) {
      console.log('传递过来的参数', type, data, from);
      if (!notQuery) this.$refs[type].query({ flag, p: data, from });
      this.active = type;
    },
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
    // 通知面包屑组件
    // 从导航栏菜单
    const parmObjDhcd = {
      mybreadList: ['首页', '印花税台账'], // 【必填】面包屑层级
      iframeGoBack: true, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务进
    const parmObjSbrw = {
      mybreadList: ['首页', '申报概览', '税费申报', '印花税台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmx'],
      goBackPath: '/znsb/view/nssb/sbrwmx', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    // 从申报任务(报表)进
    const parmObjSbrwAbb = {
      mybreadList: ['首页', '申报概览', '税费申报', '印花税台账'], // 【必填】面包屑层级
      iframeGoBack: false, // 是否可以返回首页
      isCanGoBack: true, // 【选填】是否可以回退，不传参数则默认不可
      mbxUrlList: ['/znsb/view/nssb/sbrwgl', '/znsb/view/nssb/sbrwmxAbb'],
      goBackPath: '/znsb/view/nssb/sbrwmxAbb', // 【选填】点击回退时，跳转的路径。一般是项目内，
      // companyList: this.companyList, // 方便本地测试，提交时需要注释掉
      selectedCompany: JSON.parse(window.sessionStorage.getItem('jgxxList'))?.jguuid || '',
      xlxShow: true,
    };
    if (this.sbrwmxbz) {
      this.$refs.myBre.initMyBre(parmObjSbrw);
    } else if (this.sbrwmxAbbbz) {
      this.$refs.myBre.initMyBre(parmObjSbrwAbb);
    } else {
      this.$refs.myBre.initMyBre(parmObjDhcd);
    }
    // this.$refs.myBre.initMyBre(parmObjDhcd);
  },
};
</script>
<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.t-form-item__ {
  margin-bottom: 0 !important;
}
.mainbody {
  // display: flex;
  height: 100vh;
  // flex-direction: column;
  &-content {
    // flex: 1;
    margin-bottom: 10px;
    /deep/.gt-collapse-menu-sidebar-unfold {
      height: calc(100vh - 60px);
    }
  }
  /deep/.gt-collapse-menu-content-unfold {
    margin-left: 0 !important;
  }
  /deep/.gt-collapse-menu-sidebar-unfold {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content .t-default-menu {
    width: 180px !important;
  }
  /deep/.gt-collapse-menu-sidebar {
    display: block;
  }
}
.tzzt {
  width: calc(100% - 50px);
}
.ggMenu {
  border-left: 1px solid #eee;
  /deep/.gt-collapse-menu-sidebar-header__title {
    font-size: 14px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header {
    line-height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-header-unfold {
    height: 55px !important;
  }
  /deep/.t-menu__item {
    height: 55px !important;
  }
  /deep/.gt-collapse-menu-sidebar-content {
    height: calc(100% - 55px) !important;
  }
}
</style>
