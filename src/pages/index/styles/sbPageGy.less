.znsbBackGroupDiv {
  display: flex;

  /*整个页面背景色：灰色*/
  width: 100%;
  // height: 100vh;
  height: 100%;
  min-width: 1000px;
  padding: 10px;
  overflow-y: auto;
  background-color: rgb(238, 238, 238);
  flex-direction: column;

  .znsbSbBodyDiv {
    height: 0;
    overflow-y: auto;
    flex: 1;
  }

  .znsbHeadqueryDiv {
    height: auto;
    min-height: 64px;
    padding: 0 24px;
    padding-top: 16px;
    // margin-bottom: 1px;
    background-color: #fff;

    /deep/ .t-form {
      margin-left: -50px;
    }

    /deep/ .t-form__label--right {
      padding-right: 6px;
    }

    /deep/.t-form:not(.t-form-inline) .t-form__item:last-of-type {
      margin-bottom: 16px;
    }
  }

  .znsbHeadBtnDiv {
    height: 64px;
    padding: 16px 24px;
    margin-bottom: 1px;
    background-color: #fff;

    /*页面头部的按钮（靠右侧）*/

    .znsbRightBtn {
      display: flex;
      justify-content: flex-end;
      flex-wrap: nowrap;
    }
  }

  .queryBtns {
    display: flex;
    height: 48px;
    padding: 16px 24px 0;
    background-color: #fff; 
    justify-content: space-between;
    .btn {
      margin-right: 16px;
    }
    /deep/ .gov-space {
      position: relative;
      width: 100%;
    }
  }

  .znsbSbTitleDiv {
    /*页面头部的申报标题 和 副标题*/
    padding: 16px 24px;
    background: white;

    .title-info {
      /*标题*/
      height: 52px;
      font-size: 24px;
      font-weight: 600;
      line-height: 68px;
      letter-spacing: 0;
      color: #333;
      text-align: center;
      background: #f9fbfc;
    }

    .subtitle-info {
      display: flex;

      /*副标题*/
      width: 100%;
      height: 54px;
      padding: 16px 24px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0;
      color: #666;
      background: #f9fbfc;

      .left {
        width: 33%;
        height: 22px;
        text-align: left;
      }

      .center {
        width: 33%;
        height: 22px;
        text-align: center;
      }

      .right {
        width: 33%;
        height: 22px;
        text-align: right;
      }
    }
  }

  .znsbSbBodyDiv {
    /* 申报页面的 中间结构 */
    // height: 100%;
    height: calc(100% - 114px);
    padding: 16px 24px;
    padding-bottom: 10px;
    background-color: #fff;
    /deep/ .t-table th {
      border-bottom: 0;
    }

    .descriptions-table {
      width: 100%;
      overflow: hidden;
      border-radius: 2px;

      > table {
        width: 100%;
        font-size: 14px;
        line-height: 20px;
        color: #333;
        background: #fff;
        border-collapse: collapse;
        table-layout: auto;
      }

      .row {
        height: 40px;
        border: 1px solid #dcdcdc;
        box-sizing: border-box;
      }

      .label {
        padding: 10px 16px;
        text-align: left;
        background: #fafafa;
      }

      .content {
        padding: 10px 12px;
      }

      .label,
      .content {
        width: 10%;
        border-right: 1px solid #dcdcdc;
        box-sizing: border-box;

        &:last-child {
          border-right: 0;
        }
      }
    }

    /* 申报页面的 编辑form */

    .t-form {
      width: 100%;
      padding: 0;
      margin: 0 15px 0 0;
      overflow: hidden;
      background-color: white;
      border: 1px solid #dcdcdc;
      border-radius: 2px;

      .row1 {
        width: 100%;
        height: 41px;
        border-bottom: 1px solid #dcdcdc;

        &:last-child {
          border-bottom: 0;
        }
      }

      .label,
      .content {
        display: inline-block;
        height: 40px;
        font-weight: normal;
        border-right: 1px solid #dcdcdc;
      }

      .label {
        width: 80%;
        padding: 10px 16px;
        overflow: hidden;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
        background: #fafafa;
        border-right: 1px solid #dcdcdc;
        border-left: 1px solid #dcdcdc;
      }

      .required::before {
        margin-left: -8px;
        color: red;
        content: ' * ';
      }

      .content {
        width: 120%;
        margin-left: -20%;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        letter-spacing: 0;
        color: #333;
        text-align: right;
        border: 0;

        ///deep/ .inputBoderIpt {
        //  .t-input__inner {
        //    text-align: right;
        //  }
        //}
        .t-is-disabled {
          color: #333;
          background-color: #fff;

          .t-input__inner {
            color: #333;
          }
        }

        .t-date-picker {
          width: 100%;
        }

        /* 输入框的间距 */
        /deep/.t-input__wrap {
          width: calc(100% - 8px);
          margin: 4px;

          .t-input__inner {
            text-align: left;
          }
        }

        ///deep/ .t-select-input {
        //  width:calc(100% - 8px);
        //  margin: 4px;
        //}

        /deep/ .mright {
          .t-input__inner {
            text-align: right;
          }
        }
      }

      .t-form {
        display: flex;
        width: 100%;

        border-left: 1px solid #eeeeef;
        flex-wrap: wrap;

        .formItem {
          .t-form__label {
            color: rgba(255, 255, 255, 0);
          }
        }

        .t-form__item {
          height: 40px;
          margin-bottom: 0;

          .t-form__label {
            width: 40% !important;
            height: 40px;
            padding-left: 15px;
            font-size: 14px;
            line-height: 40px;
            text-align: left;
            background-color: #f7f8fa;
            border: 1px solid #eeeeef;
            border-top: none;
            border-left: none;
          }
        }

        .t-form__controls-content {
          height: 40px;
          line-height: 40px;
          border-right: 1px solid #eeeeef;
          border-bottom: 1px solid #eeeeef;
          box-sizing: border-box;
          //.inputBoder {
          //  margin: 2px;
          //  box-sizing: border-box;
          //}

          .t-is-disabled {
            background-color: #fff;
          }

          .t-select__right-icon {
            color: rgb(187, 186, 186);
          }
        }

        .t-input__extra {
          bottom: 0;
          left: 5px;
          z-index: 5;
          display: none;
        }

        .t-input-number {
          border: 0;
        }
      }
    }

    /* tab 页签样式 */
    .t-card {
      border: 0;

      .t-card__actions:hover {
        cursor: pointer;
      }
    }

    .t-dialog__header {
      display: none;

      .demo-card {
        display: none;
      }
    }

    /deep/.t-card__body {
      padding: 0;
    }

    .table-head {
      height: 68px;
      font-size: 24px;
      font-weight: 700;
      line-height: 36px;
      color: #333;
      text-align: center;
      background-color: #f9fafd;
    }
  }
}

.adaption-wrap {
  display: flex;
  flex-direction: column;
}

.czBtn {
  padding-right: 16px;
  color: #4285f4;
  cursor: pointer;
}

.czBtn:hover {
  color: #1d5ac9;
}

/deep/.t-button--variant-outline.t-is-disabled {
  background-color: #fff;

  .t-button__text {
    color: #b8ccff;
  }
}

.ggMenu {
  // height: 100%;
  height: calc(100vh - 60px);

  li {
    padding: 0;
  }

  /deep/.t-menu__content {
    margin-top: -8px;

    div {
      display: flex;
    }
  }

  /deep/ .gt-collapse-menu[data-v-74cbddd5] .t-default-menu .t-menu__item {
    padding-top: 0;
  }

  /deep/ .t-table {
    height: calc(100% - 54px);
  }
}

.wxts {
  height: 86x;
  padding: 16px 24px;
  margin-bottom: 1px;
  background-color: #fff;
}

.inDialog {
  padding: 0 !important;
}

.tsCol {
  display: flex;
  align-items: center;
}

.tsIcon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
}

.inMenu {
  padding: 0 !important;
}
.specText {
  color: #0052d9;
  cursor: pointer;
}

/deep/ .gt-collapse-menu-sidebar-content > div:first-child {
  display: none;
}

/deep/ .gt-collapse-menu-sidebar-content {
  background-color: #eee !important;
  // background-color: #fff !important;
}

.mainbody /deep/ .gt-collapse-menu-sidebar-content .t-default-menu {
  height: calc(100% - 10px) !important;
}