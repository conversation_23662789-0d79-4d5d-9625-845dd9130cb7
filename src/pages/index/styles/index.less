.button-right-icon {
  margin-top: -2px;
  vertical-align: middle;
}
.cxssbbg {
  background: #f7f8fa;
}
.header-pixed {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2499;
  width: 100%;
  height: 116px;
  background-color: white;
}
.cxssb-header {
  height: 56px;
  background: #4285f4;
}
.cxssb-bread {
  height: 60px;
  background: #fff;
}
.bread-crumbs-div-second {
  width: 1184px;
  margin: 0 auto;
}
.bread-crumbs-width-screen {
  margin-left: 16px;
}
.css-table-main {
  width: 100%;
  font-size: 14px;
  color: #323232;
  border-collapse: collapse;

  &:not(.validate-table) {
    .el-form-item {
      margin-bottom: 0;

      &.el-form-item--small,
      &.el-form-item--mini {
        margin-bottom: 0;
      }
    }
  }

  &.table--td--height__25 {
    > tbody,
    & > tr > td {
      height: 25px;
    }
  }

  .css-table-item-100 {
    width: 100%;
  }
  .css-table-main-label {
    /* width: 170px;
    max-width: 170px;
    min-width: 170px; */
    padding-right: 20px;
    color: #606266;
    text-align: center;
    background-color: #f9fafd;
    box-sizing: border-box;

    .badge {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      cursor: pointer;
      border: 8px solid;
      border-color: tomato tomato transparent transparent;
    }

    label {
      background-color: #f0f2ff;
    }

    span {
      // color: #333;
    }

    a {
      // color: #333;
    }
  }

  .el-tag {
    margin-right: 10px;
    margin-bottom: 5px;
    border-radius: 4px;
  }

  .css-back {
    height: 80px;
    padding-top: 20px;
  }

  .explain {
    margin-left: 10px;
    font-size: 14px;
    line-height: 30px;
    color: #696969;
  }

  .warning {
    position: relative;
    margin-left: 20px;
    font-size: 14px;
    color: #fb5353;
  }

  & > tbody > tr,
  & > tr {
    > td {
      width: 1000px; /*fix 表格里有table展示异常*/
      padding: 8px 10px;
      word-break: break-all;
      border: 1px solid #dadbdf;

      a {
        color: #5e77ff;
      }

      .link {
        display: block;

        & + .link {
          margin-top: 10px;
        }
      }

      .relative {
        i {
          position: absolute;
          right: 40px;
          line-height: 50px;
          color: #909090;
        }
      }

      .css-table-text {
        display: inline-block;
        width: 75%;
        padding: 6px 10px;
        background-color: #eee;
        border: solid 1px #ccc;
        border-radius: 4px;
        box-sizing: border-box;
      }
    }
  }

  &.bumf {
    .css-table-main-label {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      text-align: center;
      background-color: #fff;

      label {
        background-color: #fff;
      }
    }

    tbody,
    tr {
      td {
        padding: 20px;
        border: 2px solid #333;
      }
    }
  }
}

.tdgv-wrapper .t-table thead td,
.tdgv-wrapper .t-table th {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  // text-align: center !important;
}
//body.tdgv-wrapper .gt-collapse-menu .t-default-menu .t-menu__item {
//  height: 80px;
//  padding-top: 0;
//}
//.tdgv-wrapper .t-default-menu .t-menu__item {
//  line-height: 39px;
//}
//body.tdgv-wrapper .title-ellipsis {
//  white-space: normal;
//}
//.gt-collapse-menu .t-default-menu .t-menu__item.t-is-active:not(.t-is-opened) .title-ellipsis {
//  white-space: normal;
//}
.ant-modal-mask,
.ant-modal-wrap {
  z-index: 2500;
}
