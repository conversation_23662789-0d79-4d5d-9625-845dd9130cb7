// 申报页面样式
.fx-page-content {
  width: 1184px;
  margin: 24px auto;
  background: #fff;
  .fx-page-body {
    padding: 24px;
    margin-top: 16px;
  }
}

// 下部分主体内容
.fx-body-content {
  padding: 32px 24px 24px;
  margin-top: 16px;
  background: #FFF;
  // 税款信息文字固定部分   
  .content-label {
    margin-bottom: 16px;
    span {
      font-size: 20px;
      font-weight: 700;
      color: #333;
    }
  }
}

.fx-page {
  width: 1184px;
  padding: 24px;
  background: #fff;
}

.fx-page-footer-btn {
  margin: 24px;
  text-align: center;
}

.fx-page-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: #333;
}

//  全局自定义table标签样式
.gt-table {
  &--normal{
    td.edit {
      padding: 5px;
    }
    td.read {
      padding-right: 16px;
      padding-left: 16px;
    }
    // td操作：[ 增加 删除 ]
    td.operation {
      width: 120px;
      padding: 10px 16px;
    }
    //  多个table拼接时最后一行无下边框
    tr.gt-tr-noBorderBottom {
      td, th {
        border-bottom: 0;
      }
    }
  }
}

//  设置填表式form和表头样式
.gt-form-wrap {
  padding: 16px 24px;
  font-size: 14px;
  line-height: 22px;
  //  设置标题等样式
  .gt-form-header {
    padding: 16px 0;
    margin-bottom: 16px;
    background-color: #F9FAFD;
    border-radius: 2px;
    .gt-form-header-title {
      padding: 0 16px;
      font-size: 24px;
      font-weight: 600;
      line-height: 32px;
      color: #27282E;
      text-align: center;
    }
    .gt-form-header-subtitle {
      padding-bottom: 0;
      font-weight: 400;
      color: #666;
      text-align: center;
      .gt-table--normal {
        td {
          padding: 16px 16px 0;
          border: 0;
        }
      }
    }
  }
}

// 文件原子样式

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}
.text-indent1 {
  text-indent: 1em;
}
.text-indent2 {
  text-indent: 2em;
}
.text-indent3 {
  text-indent: 3em;
}
.text-indent4 {
  text-indent: 4em;
}
.text-indent5 {
  text-indent: 5em;
}
// 外边距
.mb-8 {
  margin-bottom: 8px;
}
.mb-24 {
  margin-bottom: 24px;
}
.ml-12 {
  margin-left: 12px;
}
.mr-12 {
  margin-right: 12px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-12 {
  margin-top: 12px;
}
.mt-16 {
  margin-top: 16px;
}
.mt-20 {
  margin-top: 20px;
}
.mt-24 {
  margin-top: 24px;
}
.mt-32 {
  margin-top: 32px;
}
.mt--8 {
  margin-top: -8px;
}
.mt--9 {
  margin-top: -9px;
}

//  内边距
.pr-8 {
  padding-right: 8px;
}

.plr-16 {
  padding-right: 16px;
  padding-left: 16px;
}
.plr-24 {
  padding-right: 24px;
  padding-left: 24px;
}
.pt-8 {
  padding-top: 8px;
}
.pt-16 {
  padding-top: 16px;
}
.pb-16 {
  padding-bottom: 16px;
}
.pb-24 {
  padding-bottom: 24px;
}
.pd-10 {
  padding: 10px;
}
//悬浮
.float-right {
  float: right;
  clear: both;
}

// 处罚表格

.fx-cf-title1 {
  margin: 16px 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 40px;
  color: #27282e;
  text-align: center;
}

.fx-cf-title2 {
  margin: 16px 0;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: #333;
  text-align: center;
}

.gt-page-content {
  padding: 16px 24px;
  .gt-page-body {
    padding-top: 16px;
    .tb-title {
      height: 56px;
      font-size: 16px;
      font-weight: bold;
      line-height: 56px;
      color: #333;
    }
  }
  .gt-page-footer {
    text-align: center;
  }
}

.gt-title {
  position: relative;
  padding: 18px 16px 16px;
  background-color: #f9fafd;
  border-radius: 2px;
  .gt-title-text {
    margin-bottom: 18px;
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
    color: #27282e;
    text-align: center;
  }
  .gt-title-date {
    position: absolute;
    bottom: 16px;
    width: 100%;
    font-size: 14px;
    line-height: 22px;
    color: #666;
    text-align: center;
  }
  .gt-title-desc {
    font-size: 14px;
    line-height: 22px;
    color: #666;
    text-align: right;
  }
}
.gt-title2 {
  height: 56px;
  font-size: 16px;
  font-weight: bold;
  line-height: 56px;
  color: #333;
}

//  一行文字超出隐藏...
.one-line-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
