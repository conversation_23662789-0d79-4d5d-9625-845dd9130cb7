import zzstz from '@/pages/index/views/zzstz/index';

function yfjjkctzgl() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/yfjjkctzgl/index.vue');
}
function qsgzsz() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/yfjjkctzgl/components/qsgzsz.vue');
}
function tzfp() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/yfjjkctzgl/components/pdf.vue');
}
function fztzmx() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/yfjjkctzgl/components/fztzmx.vue');
}

export const yfjjkctzRouter = [
  {
    name: 'yfjjkctzgl',
    path: '/yfjjkctzgl',
    component: yfjjkctzgl,
    meta: {
      layout: zzstz,
      title: '研发加计扣除台账管理',
      breadCrumbs: [
        { title: '研发加计扣除台账管理', to: '' },
        { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },
  {
    name: 'qsgzsz',
    path: '/qsgzsz',
    component: qsgzsz,
    meta: {
      layout: zzstz,
      title: '取数规则设置',
      breadCrumbs: [
        // { title: '研发加计扣除台账管理', to: '' },
        // { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },
  {
    name: 'tz_pdf',
    path: '/tz_pdf',
    component: tzfp,
    meta: {
      layout: zzstz,
      title: '台账发票',
      breadCrumbs: [
        // { title: '研发加计扣除台账管理', to: '' },
        // { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },

  {
    name: 'fztzmx',
    path: '/fztzmx',
    component: fztzmx,
    meta: {
      layout: zzstz,
      title: '研发加计扣除台账管理',
      breadCrumbs: [
        { title: 'fztzmx', to: '' },
        { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },
];
