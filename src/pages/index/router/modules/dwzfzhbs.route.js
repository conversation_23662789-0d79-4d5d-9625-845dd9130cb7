/**
 * 对外支付综合办税（国际汇税通）路由配置
 * 迁移自原sdsxgxxbg项目，已适配nssb-web项目结构
 */
const meta = {
  storeName: 'dwfhtz/dwzfzhbs/form',
  verifyBegin: false, // 禁用前置校验
  hasHome: true, // 是否展示首页, true 展示 false 隐藏
  breadCrumbs: ['对外支付综合办税（国际汇税通）'],
};

export default [
  {
    name: 'dwzfzhbs',
    path: '/dwzfzhbs',
    component: () => import(/* webpackChunkName: "app-wrap" */ '../../views/sb/common/app-wrap/index.vue'),
    redirect: '/dwzfzhbs/jc',
    meta,
    children: [
      // 新增迁移页面路由
      {
        name: 'dwzfzhbs/jc',
        path: 'jc',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/jc/index.vue'),
        meta: {
          title: '综合办税-基础功能',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/jg',
        path: 'jg',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/jg/index.vue'),
        meta: {
          title: '综合办税-结果页面',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znjsq',
        path: 'znjsq',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znjsq/index.vue'),
        meta: {
          title: '综合办税-智能计算器',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znjsq/cyfa',
        path: 'znjsq/cyfa',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znjsq/cyfa/index.vue'),
        meta: {
          title: '综合办税-常用方案',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znjsq/nsywpd',
        path: 'znjsq/nsywpd',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znjsq/nsywpd/index.vue'),
        meta: {
          title: '综合办税-纳税义务判断',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znjsq/sfznjs',
        path: 'znjsq/sfznjs',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znjsq/sfznjs/index.vue'),
        meta: {
          title: '综合办税-税费智能计算',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znjsq/skjsq',
        path: 'znjsq/skjsq',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znjsq/skjsq/index.vue'),
        meta: {
          title: '综合办税-税款计算器',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znsb',
        path: 'znsb',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znsb/index.vue'),
        meta: {
          title: '综合办税-智能申报',
          ...meta,
        },
      },
      {
        name: 'dwzfzhbs/znsb/jsjg',
        path: 'znsb/jsjg',
        component: () => import(/* webpackChunkName: "dwzfzhbs" */ '../../views/dwfhtz/dwzfzhbs/znsb/jsjg/index.vue'),
        meta: {
          title: '综合办税-计算结果',
          ...meta,
        },
      },
    ],
  },
];
