import Vue from 'vue';
import VueRouter from 'vue-router';
import routes from './routes';
import { yfjjkctzRouter } from './yfjjkctzgl';

Vue.use(VueRouter);

const baseRoutes = [
  {
    path: '*',
    redirect: '/dwfhtz',
    // redirect: '/yhstz',
  },
];

routes.push(...baseRoutes);
routes.push(...yfjjkctzRouter);

const router = new VueRouter({
  mode: 'history',
  base: `${window.STATIC_ENV_CONFIG.ROUTER_PREFIX}/`,
  routes: [...routes],
});

export default router;
