import lsgl from '@/pages/index/views/lsgl/lsgl.vue';
import zzstz from '@/pages/index/views/zzstz/index.vue';
import yhstz from '@/pages/index/views/yhstz/index.vue';
import dwfhtz from '@/pages/index/views/dwfhtz/index.vue';
import lrzxdz from '@/pages/index/views/config/lrzxdz/index.vue';
import ydyjtz from '@/pages/index/views/dlzg/ydyjtz/index.vue';
import jjdjtz from '@/pages/index/views/dlzg/jjdjtz/index.vue';
import fcstz from '@/pages/index/views/fcstz/index.vue';
import cztdsystz from '@/pages/index/views/cztdsystz/index.vue';
import xxscymx from '@/pages/index/views/zzstz/components/xxscymx/indexOld.vue';
// import qysdsyjtz from '@/pages/index/views/qysdsyjtz/index.vue';
// import zzsxgmnsrtz from '@/pages/index/views/zzsxgmnsrtz/index.vue';
import qysdsyjtz from '@/pages/index/views/qysdsyjtzNew/index.vue';
import zzsyjtz from '@/pages/index/views/zzsyjtz/index.vue';
import khbmyhylbdzsz from '@/pages/index/views/szlgn/khbmyhylbdzsz/index.vue';
import zzssjjtb from '@/pages/index/views/cxtj/zzssjjtb/index.vue';
import ldtssqxsetjb from '@/pages/index/views/cxtj/ldtssqxsetjb/index.vue';
import dwzfzhbsRoutes from './modules/dwzfzhbs.route.js';

function userinit() {
  return import(/* webpackChunkName: "sbfdemo" */ '@/pages/index/views/zzstz/index.vue');
}
function yfjjkctzgl() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/yfjjkctzgl/index.vue');
}

function commonTzxxDemo() {
  return import(/* webpackChunkName: "openpdf" */ '@/pages/index/views/commonTzxxDemo/index.vue');
}
function ghjftz() {
  return import('../views/tysbtz/ghjftz/index.vue');
}

export default [
  {
    name: 'commonTzxxDemo',
    path: '/commonTzxxDemo',
    component: commonTzxxDemo,
  },
  {
    name: 'zzstz',
    path: '/zzstz',
    component: userinit,
    meta: {
      layout: zzstz,
      title: '增值税台账',
      breadCrumbs: [
        { title: '增值税台账', to: '' },
        { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },
  {
    name: 'lsgl',
    path: '/lsgl',
    component: lsgl,
    meta: {
      layout: lsgl,
      title: '理税概览',
      breadCrumbs: [
        { title: '理税概览', to: '' },
        { title: '理税概览', to: '/lsgl' },
      ],
    },
  },
  {
    name: 'xxscymx',
    path: '/xxscymx',
    component: xxscymx,
    meta: {
      layout: xxscymx,
      title: '销项税差异明细',
      breadCrumbs: [{ title: '首页', to: '' }],
    },
  },
  {
    path: '/xxscymx',
    name: 'openXxsydcymx',
    component: () => import('@/pages/index/views/zzstz/components/xxscymx/indexOld.vue'),
  },
  {
    name: 'yhstz',
    path: '/yhstz',
    component: yhstz,
    meta: {
      layout: yhstz,
      title: '印花税台账',
      breadCrumbs: [
        { title: '印花税台账', to: '' },
        { title: '印花税台账', to: '/yhstz' },
      ],
    },
  },
  {
    name: 'dwfhtz',
    path: '/dwfhtz',
    component: dwfhtz,
    meta: {
      layout: dwfhtz,
      title: '对外付汇台账',
      breadCrumbs: [
        { title: '对外付汇台账', to: '' },
        { title: '对外付汇台账', to: '/dwfhtz' },
      ],
    },
  },
  {
    name: 'lrzxdz',
    path: '/lrzxdz',
    component: lrzxdz,
    meta: {
      layout: lrzxdz,
      title: '利润中心对照',
      breadCrumbs: [{ title: '利润中心对照', to: '/lrzxdz' }],
    },
  },
  {
    name: 'ydyjtz',
    path: '/ydyjtz',
    component: ydyjtz,
    meta: {
      layout: ydyjtz,
      title: '异地预缴台账',
      breadCrumbs: [{ title: '异地预缴台账', to: '/ydyjtz' }],
    },
  },
  {
    name: 'jjdjtz',
    path: '/jjdjtz',
    component: jjdjtz,
    meta: {
      layout: jjdjtz,
      title: '加计抵减台账',
      breadCrumbs: [{ title: '加计抵减台账', to: '/jjdjtz' }],
    },
  },
  {
    name: 'cztdsystz',
    path: '/cztdsystz',
    component: cztdsystz,
    meta: {
      layout: cztdsystz,
      title: '城镇土地使用税台账',
      breadCrumbs: [
        { title: '城镇土地使用税台账', to: '' },
        { title: '城镇土地使用税台账', to: '/cztdsystz' },
      ],
    },
  },
  {
    name: 'fcstz',
    path: '/fcstz',
    component: fcstz,
    meta: {
      layout: fcstz,
      title: '房产税台账',
      breadCrumbs: [
        { title: '房产税台账', to: '' },
        { title: '房产税台账', to: '/fcstz' },
      ],
    },
  },
  {
    name: 'zzsyjtz',
    path: '/zzsyjtz',
    component: zzsyjtz,
    meta: {
      layout: zzsyjtz,
      title: '增值税预缴台账',
      breadCrumbs: [
        { title: '增值税预缴台账', to: '' },
        { title: '增值税预缴台账', to: '/zzsyjtz' },
      ],
    },
  },
  // {
  //   name: 'zzsxgmnsrtz',
  //   path: '/zzsxgmnsrtz',
  //   component: zzsxgmnsrtz,
  //   meta: {
  //     layout: zzsxgmnsrtz,
  //     title: '增值税小规模纳税人台账',
  //     breadCrumbs: [
  //       { title: '增值税小规模纳税人台账', to: '' },
  //       { title: '增值税小规模纳税人台账', to: '/zzsxgmnsrtz' },
  //     ],
  //   },
  // },
  {
    name: 'yfjjkctzgl',
    path: '/yfjjkctzgl',
    component: yfjjkctzgl,
    meta: {
      layout: zzstz,
      title: '研发加计扣除台账管理',
      breadCrumbs: [
        { title: '研发加计扣除台账管理', to: '' },
        { title: '增值税台账', to: '/zzstz' },
      ],
    },
  },
  {
    name: 'qysdsyjtz',
    path: '/qysdsyjtz',
    component: qysdsyjtz,
    meta: {
      layout: qysdsyjtz,
      title: '企业所得税预缴台账',
      breadCrumbs: [
        { title: '企业所得税预缴台账', to: '' },
        { title: '企业所得税预缴台账', to: '/qysdsyjtz' },
      ],
    },
  },
  {
    name: 'khbmyhylbdzsz',
    path: '/khbmyhylbdzsz',
    component: khbmyhylbdzsz,
    meta: {
      layout: khbmyhylbdzsz,
      title: '客户编码与行业类别对照',
      breadCrumbs: [
        { title: '客户编码与行业类别对照', to: '' },
        { title: '客户编码与行业类别对照', to: '/khbmyhylbdzsz' },
      ],
    },
  },
  {
    name: 'zzssjjtb',
    path: '/zzssjjtb',
    component: zzssjjtb,
    meta: {
      layout: zzssjjtb,
      title: '增值税税金计提表',
      breadCrumbs: [
        { title: '增值税税金计提表', to: '' },
        { title: '增值税税金计提表', to: '/zzssjjtb' },
      ],
    },
  },
  {
    name: 'ldtssqxsetjb',
    path: '/ldtssqxsetjb',
    component: ldtssqxsetjb,
    meta: {
      layout: ldtssqxsetjb,
      title: '留抵退税申请销售额统计表',
      breadCrumbs: [
        { title: '留抵退税申请销售额统计表', to: '' },
        { title: '留抵退税申请销售额统计表', to: '/ldtssqxsetjb' },
      ],
    },
  },
  {
    name: 'ghjftz',
    path: '/ghjftz',
    component: ghjftz,
    meta: {
      breadCrumbs: [{ title: '工会经费台账', to: '/ghjftz' }],
    },
  },
  ...dwzfzhbsRoutes,
];
