export const urlMap = {
  BDA0611159: {
    ywbm: 'qysds_a_yjd',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/qysds_a_yjd',
  },
  BDA0611038: {
    ywbm: 'qysds_b_yjd',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/qysds_b_yjd',
  },
  BDA0610994: {
    ywbm: 'qysds_a_nd',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/qysds_a_nd',
  },
  BDA0610058: {
    ywbm: 'qsqysds',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/qsqysds',
  },
  BDA0611099: {
    ywbm: 'kjqysdsbg',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/kjqysdsbg',
  },
  BDA0611100: {
    ywbm: 'fjmqysdszxsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/fjmqysdszxsb',
  },
  BDA0611092: {
    ywbm: 'fjmqysdsyjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/fjmqysdsyjsb',
  },
  BDA0611093: {
    ywbm: 'fjmqysdsndsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/fjmqysdsndsb',
  },
  BDA0610922: {
    ywbm: 'glywwlndbgsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/glywwlndbgsb',
  },
  BDA0611054: {
    ywbm: 'fssrtysb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/fssrtysb',
  },
  BDA0610857: {
    ywbm: 'cbj',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/cbj',
  },
  BDA0610334: {
    ywbm: 'whsyjsf',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/whsyjsf',
  },
  BDA0611055: {
    ywbm: 'sytbsyjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/sytbsyjsb',
  },
  BDA0610809: {
    ywbm: 'fqdqdzcpcljjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/fqdqdzcpcljjsb',
  },
  BDA0610069: {
    ywbm: 'kqsyfyjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/kqsyfyjsb',
  },
  BDA0610070: {
    ywbm: 'kqsyfndsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/kqsyfndsb',
  },
  BDA0610100: {
    ywbm: 'tysb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/tysb',
  },
  BDA0611056: {
    ywbm: 'yjtkfxzbjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/yjtkfxzbjsb',
  },
  BDA0610333: {
    ywbm: 'whsyjsfdkdjsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/whsyjsfdkdjsb',
  },
  BDA0611160: {
    ywbm: 'qysdskdqjyndsb',
    url: '/sbzx/view/sdsfsgjssb/#/yyzx/qysdskdqjyndsb',
  },
};

export const idxYwbm2YzpzzlDm = (() => {
  const mapping = {};
  const entries = Object.entries(urlMap);
  entries.forEach(([yzpzzlDm, { ywbm, url }]) => {
    mapping[ywbm] = {
      yzpzzlDm,
      url,
    };
  });
  return mapping;
})();
