/* eslint-disable no-param-reassign,no-restricted-properties,prefer-const */
import { DialogPlugin } from 'tdesign-vue';
import api from '@/pages/index/api/qyzx/request';

export default {
  /**
   *  后面对象属性值赋值给前面对象(全赋值）
   * str 原内容
   */
  fillFormData(filledObj, dataobj) {
    if (dataobj) {
      Object.keys(dataobj).forEach((key) => {
        filledObj[key] = dataobj[key];
      });
    }
    return filledObj;
  },
  /**
   *  后面对象属性值赋值给前面对象(全赋值）后面属性为null时，前面属性为空
   * str 原内容
   */
  fillFormDataNull(filledObj, dataobj) {
    if (dataobj) {
      Object.keys(dataobj).forEach((key) => {
        filledObj[key] = dataobj[key];
      });
    } else {
      Object.keys(filledObj).forEach((key) => {
        filledObj[key] = '';
      });
    }
    return filledObj;
  },
  /**
   *  后面对象属性值赋值给前面对象(部分赋值）
   * str 原内容
   */
  fillFormDataExist(filledObj, dataobj) {
    Object.keys(dataobj).forEach((key) => {
      if (this.$chk(filledObj[key])) filledObj[key] = dataobj[key];
    });
    return filledObj;
  },
  /**
   *  后面对象属性值不为空时赋值给前面对象
   * str 原内容
   */
  fillFormDataskipNull(filledObj, dataobj) {
    Object.keys(dataobj).forEach((key) => {
      if (this.$chk(filledObj[key]) && this.$chk(dataobj[key])) filledObj[key] = dataobj[key];
    });
    return filledObj;
  },

  /**
   * 判断是否非空
   * a 判断内容
   */
  $chk(a) {
    return !!(a || a === 0);
  },
  close() {
    window.opener = null;
    const win = window.open('about:blank', '_top');
    win.close();
  },
  goHome() {
    window.open('/mhzx/api/mh/mhsy/index', '_self');
  },
  gyAlert(body, header = '提示', theme = 'info') {
    const alertDia = DialogPlugin.alert({
      header,
      theme: theme === null ? undefined : theme,
      body,
      confirmBtn: '确定',
      placement: 'center',
      closeOnOverlayClick: false,
      onConfirm: () => {
        alertDia.hide();
      },
    });
  },
  gyAlertWithPromise(body, header = '温馨提示', theme = 'primary') {
    return new Promise((resolve) => {
      const alertDia = DialogPlugin.alert({
        header,
        theme: theme === null ? undefined : theme,
        body,
        placement: 'center',
        closeOnOverlayClick: false,
        onConfirm: () => {
          alertDia.hide();
          resolve('confirm');
        },
      });
    });
  },
  /**
   * 生成唯一不重复主键
   * @returns {string}
   */
  createRandomId() {
    return (
      (Math.random() * 10000000).toString(16).substr(0, 4) + new Date().getTime() + Math.random().toString().substr(2.5)
    );
  },
  /**
   * 根据自定义字段转换为数结构数据
   * @param treeData
   * @param valueKey  代码名
   * @param labelKey  代码值
   * @param parentKey 上下级次名
   * @param lastchildKey 最底层名
   * @param lastchildValue 最底层值
   * @param parentValue 最顶层值(默认!$chk()判断）
   * 示例  DM_GY_XZQH  valueKey=XZQHSZ_DM,labelKey=XZQHMC,parentKey=SJXZQHSZ_DM,
   * lastchildKey=XZQHJC，lastchildValue='3',parentValue='',
   */
  getTreeData(
    treeData,
    valueKey,
    labelKey,
    parentKey,
    lastchildKey,
    lastchildValue,
    parentValue = '',
    treeType,
    sjdmCode,
  ) {
    const topData = this.getDataByparentKey(treeData, parentKey, parentValue, treeType, sjdmCode);
    return topData.map((tree) => ({
      ...tree,
      value: tree[valueKey],
      label: tree[labelKey],
      children: this.getChildrenData(
        tree,
        treeData,
        valueKey,
        labelKey,
        lastchildKey,
        lastchildValue,
        parentKey,
        tree[valueKey],
        treeType,
      ),
    }));
  },
  getChildrenData(
    tree,
    treeData,
    valueKey,
    labelKey,
    lastchildKey,
    lastchildValue,
    parentKey,
    parentValue,
    treeType,
    sjdmCode,
  ) {
    if (tree[lastchildKey] !== lastchildValue) {
      // const childtreeData = this.getDataByparentKey(treeData, parentKey, parentValue);
      // eslint-disable-next-line no-restricted-syntax
      // for (const entry of Object.entries(childtreeData)) {
      //   entry.value = entry[valueKey];
      //   entry.label = entry[labelKey];
      //   entry.label = this.getChildrenData(
      //     entry,
      //     treeData,
      //     valueKey,
      //     labelKey,
      //     lastchildKey,
      //     lastchildValue,
      //     parentKey,
      //     entry[valueKey],
      //   );
      // }
      return this.getDataByparentKey(treeData, parentKey, parentValue, treeType, sjdmCode).map((item) => ({
        ...item,
        value: item[valueKey],
        label: item[labelKey],
        children: this.getChildrenData(
          item,
          treeData,
          valueKey,
          labelKey,
          lastchildKey,
          lastchildValue,
          parentKey,
          item[valueKey],
        ),
      }));
    }
    return null;
  },

  /**
   * 根据上级过滤符合条件的数据
   * @param parentkey
   */
  getDataByparentKey(treeData, parentKey, parentValue, treeType, sjdmCode) {
    // if (treeType === 'pzsljg') {
    //   treeData.map((item) => {
    //     if (item.sJGSXZGLJG_DM === '000000000') {
    //       Object.assign(item, {
    //         sJGSXZGLJG_DM: null,
    //       });
    //     }
    //     return null;
    //   });
    // }
    // 此正则只匹配一位或多位0（处理最上级节点的 sjDm不为null导致树形结构组成有问题）
    treeData.map((item) => {
      let reg = /^0+$/;
      if (reg.test(item[sjdmCode])) {
        item[sjdmCode] = null;
      }
      return null;
    });
    return treeData?.filter((tree) => tree[parentKey] === parentValue);
  },
  /**
   * 多级下拉树过滤方法
   * 返回过滤字段节点及其子节点
   * @param searchText
   * @param node
   * @returns {boolean}
   */
  diyfilter(searchText, node) {
    return (
      node.label.indexOf(searchText) >= 0 ||
      (node.getParents().length > 0
        ? node.getParents().filter((child) => child.label.indexOf(searchText) >= 0)?.length > 0
        : false)
    );
  },
  /**
   * 格式化银行账号
   * 返回yhzh和光标位置
   * @param e
   * @returns {{yhzh, cursorIndex: number}|null}
   */
  formatCard(e) {
    // 获取input的dom对象
    const input = e.target;
    // 获取当前光标的位置
    const cursorIndex = input.selectionStart;
    // 字符串中光标之前空格的个数
    const lineNumOfCursorLeft = (e.target.value?.slice(0, cursorIndex).match(/\s/g) || []).length;
    // 去掉所有空格的字符串
    const noLine = e.target.value.replace(/\s/g, '');
    // 去除格式不对的字符并重新插入空格的字符串
    const newCardNum = noLine
      .replace(/\D+/g, '')
      .replace(/(\d{4})/g, '$1 ')
      .replace(/\s$/, '');
    // 改后字符串中原光标之前空格的个数
    const newLineNumOfCursorLeft = (newCardNum.slice(0, cursorIndex).match(/\s/g) || []).length;
    // 光标在改后字符串中应在的位置
    const newCursorIndex = cursorIndex + newLineNumOfCursorLeft - lineNumOfCursorLeft;
    return { yhzh: newCardNum, cursorIndex: newCursorIndex };
  },
  /**
   * 格式化手机号码
   * 返回sjhm和光标位置
   * @param e
   * @returns {null|{sjhm, cursorIndex: number}}
   */
  formatPhone(e) {
    // 获取input的dom对象
    const input = e.target;
    // 获取当前光标的位置
    const cursorIndex = input.selectionStart;
    // 字符串中光标之前空格的个数
    const lineNumOfCursorLeft = (e.target.value?.slice(0, cursorIndex).match(/\s/g) || []).length;
    // 去除格式不对的字符并重新插入空格的字符串
    const noLine = e.target.value.replace(/\s/g, '').replace(/\D+/g, '');
    let newPhoneNum = noLine;
    if (noLine.length > 3 && noLine.length < 8) newPhoneNum = noLine.replace(/^(\d{3})/g, '$1-');
    else newPhoneNum = noLine.replace(/^(\d{3})(\d{4})/g, '$1-$2-');
    // 改后字符串中原光标之前空格的个数
    const newLineNumOfCursorLeft = (newPhoneNum.slice(0, cursorIndex).match(/-/g) || []).length;
    // 光标在改后字符串中应在的位置
    const newCursorIndex = cursorIndex + newLineNumOfCursorLeft - lineNumOfCursorLeft;
    return { sjhm: newPhoneNum, cursorIndex: newCursorIndex };
  },

  floatAdd(arg1, arg2) {
    let r1;
    let r2;
    let m;
    try {
      r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (arg1 * m + arg2 * m) / m;
  },

  // 减
  floatSub(arg1, arg2) {
    let r1;
    let r2;
    let m;
    let n;
    try {
      r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    // 动态控制精度长度
    n = r1 >= r2 ? r1 : r2;
    return ((arg1 * m - arg2 * m) / m).toFixed(n);
  },

  // 乘
  floatMul(arg1, arg2) {
    let m = 0;
    const s1 = arg1.toString();
    const s2 = arg2.toString();
    try {
      m += s1.split('.')[1].length;
    } catch (e) {
      console.log(e);
    }
    try {
      m += s2.split('.')[1].length;
    } catch (e) {
      console.log(e);
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
  },

  // 除
  floatDiv(arg1, arg2) {
    let t1 = 0;
    let t2 = 0;
    let r1;
    let r2;
    try {
      t1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      console.log(e);
    }
    try {
      t2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      console.log(e);
    }

    r1 = Number(arg1.toString().replace('.', ''));

    r2 = Number(arg2.toString().replace('.', ''));
    return (r1 / r2) * Math.pow(10, t2 - t1);
  },
  /**
   * 按千分位格式化数字
   * @param val
   * @returns {*|string}
   */
  milliFormat(val) {
    return val && val.toString().replace(/(^|\s)\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
  },
  /**
   * 数组对象按指定key值去重
   * @param arrObj
   * @param reduceKey
   * @returns {*}
   */
  reduce(arrObj, reduceKey) {
    let obj = {};
    return arrObj.reduce((cur, next) => {
      obj[next[reduceKey]] ? '' : (obj[next[reduceKey]] = true && cur.push(next));
      return cur;
    }, []);
  },
  /**
   * 文件流下载
   * @param arrObj
   * @param reduceKey
   * @returns {*}
   */
  stremtoFile(data, type = 'pdf', fileName = 'downloadfile') {
    const file = new Blob([data], {
      type: `application/${type}`,
    });
    const url = window.URL.createObjectURL(file);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('target', '_blank');
    a.setAttribute('download', `${fileName}.${type}`);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  },

  /**
   * 将数字金额转换为大写中文金额
   * @param {number|string} n
   * @returns {string}
   */
  transformNumberToChinese(n) {
    let unit = '京亿万仟佰拾兆万仟佰拾亿仟佰拾万仟佰拾元角分';
    let str = '';
    let curreny = n;
    curreny += '00';
    const p = curreny.indexOf('.');
    if (p > -1) {
      curreny = curreny.substring(0, p) + curreny.substr(p + 1, 2);
    }
    unit = unit.substr(unit.length - curreny.length);
    for (let l = curreny.length, i = 0; i < l; i++) {
      str += '零壹贰叁肆伍陆柒捌玖'.charAt(curreny.charAt(i)) + unit.charAt(i);
    }
    return (
      str
        .replace(/零(?:仟|佰|拾|角)/g, '零')
        .replace(/零+/g, '零')
        .replace(/零(兆|万|亿|元)/g, '$1')
        // replace(/(兆|亿)万/g, '$1').
        // replace(/(京|兆)亿/g, '$1').
        // replace(/(京)兆/g, '$1').
        // replace(/(京|兆|亿|仟|佰|拾)(万?)(\S)仟/g, '$1$2零$3仟').
        .replace(/^元零?|零分/g, '')
        .replace(/(元|角)$/g, '$1整')
        .replace(/^分$/g, '零元整')
    );
  },
  /**
   * 数字转中文
   * @param {number|string} n
   * @returns {string}
   */
  toChinese(num) {
    num = Math.floor(num);
    let chinese = '';
    let digits = Math.floor(Math.log10(num)) + 1;
    let digit = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    let times = ['', '十', '百', '千', '万'];
    if (digits >= 9) {
      let quotient = Math.floor(num / Math.pow(10, 8));
      let remainder = num % Math.pow(10, 8);
      let remainderDigits = Math.floor(Math.log10(remainder)) + 1;
      return `${this.toChinese(quotient)}亿${remainderDigits < 8 ? '零' : ''}${
        remainder > 0 ? this.toChinese(remainder) : ''
      }`;
    }
    // 10000 0000
    if (digits === 1) return digit[num];
    if (digits === 2) {
      let quotient = Math.floor(num / 10);
      let remainder = num % 10;
      if (quotient > 1) {
        chinese = digit[quotient];
      }
      chinese += times[1];
      if (remainder > 0) {
        chinese += digit[remainder];
      }
      return chinese;
    }
    if (digits > 5) {
      let quotient = num / Math.pow(10, 4);
      let remainder = num % Math.pow(10, 4);
      let remainderDigits = Math.floor(Math.log10(remainder)) + 1;
      return `${this.toChinese(quotient)}万${remainderDigits < 4 ? '零' : ''}${
        remainder > 0 ? this.toChinese(remainder) : ''
      }`;
    }
    for (let index = digits; index >= 1; index--) {
      let number = Math.floor((num / Math.pow(10, index - 1)) % 10);
      // console.log(index+" "+number);
      if (number > 0) {
        chinese = chinese + digit[number] + times[index - 1];
      } else if (index > 1) {
        let nextNumber = Math.floor((num / Math.pow(10, index - 2)) % 10);
        if (nextNumber > 0 && index > 1) {
          chinese += digit[number];
        }
      }
    }
    return chinese;
  },
  getMcbyDmb(dmblist, dm, colomndm, cloumnmc) {
    return dmblist.find((item) => item[colomndm] === dm)?.[cloumnmc];
  },
  // 随机获取对应位数的数字和字母字符串
  getrand(min, max) {
    let str = '';
    const range = max ? Math.round(Math.random() * (max - min)) + min : min;
    const arr = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z',
    ];
    for (let i = 0; i < range; i++) {
      const index = Math.round(Math.random() * (arr.length - 1));
      str += arr[index];
    }
    return str;
  },
  getNEWuserInfo(router) {
    let childrenCard = router?.GT4_MEMBER_SUBSESSION_ID;
    if (childrenCard) {
      api.getUserInfo({ Token: childrenCard }).then((res) => {
        console.log(res);
        sessionStorage.setItem('userInfodb', '测试数据');
      });
    }
  },
  // zgswjgDm为入参 返回值为地区编码
  dqbmFun(zgswjgDm) {
    let str = zgswjgDm?.substring(1, 5);
    // 大连 宁波 厦门  青岛
    const dqbmList = ['2102', '3302', '3502', '3702', '4403'];
    if (dqbmList.includes(str)) {
      return str;
    }
    str = `${zgswjgDm.substring(1, 3)}00`;
    return str;
  },
  gyAlertyqCheck(body, header = '温馨提示', theme = 'info') {
    const alertDia = DialogPlugin.alert({
      header,
      theme: theme === null ? undefined : theme,
      body,
      confirmBtn: '确定',
      placement: 'top',
      closeOnOverlayClick: false,
      onConfirm: () => {
        alertDia.hide();
        window.open('/mhzx/api/mh/mhsy/index', '_self');
      },
    });
  },
  getTempCookieMethod() {
    // 第一个tpass
    let mTempCookieStr = '';
    // 全部tpass
    const mCookieTempList = [];
    // 总数
    let countA = 0;
    const cookiestr = document.cookie;
    const splitstr = cookiestr?.split(';');
    if (splitstr && splitstr.length > 0) {
      for (let i = 0; i < splitstr.length; i++) {
        if (splitstr[i].trim() && splitstr[i].trim().startsWith('tpass_')) {
          if (!mTempCookieStr) {
            mTempCookieStr = splitstr[i].trim();
          }
          mCookieTempList.push(splitstr[i].trim());
          countA += 1;
        }
      }
    }
    return { mTempCookieStr, countA, mCookieTempList };
  },
  getTokenValueMethod(tokenLong) {
    return tokenLong?.substring(5 + 1 + 32 + 1, 39 + 179);
  },
  setIntervalFun() {
    let mTime = 30;
    const loop = setInterval(() => {
      mTime -= 1;
      if (mTime === 0) {
        clearInterval(loop);
      } else {
        // 获取跳转前保存的tpass信息。
        const myCookieStrOldData = JSON.parse(window.sessionStorage.getItem('myCookieStrOldData'));
        // 获取现在的tpass信息
        const { mCookieTempList } = this.getTempCookieMethod();
        // 获取不存在于跳转前的tpasslist中的值
        const mCookieEndList = mCookieTempList.filter((f) => myCookieStrOldData.indexOf(f) === -1);
        if (mCookieEndList && mCookieEndList.length > 0) {
          // 如果获取当，存在sessionStorage中
          window.sessionStorage.setItem('myCookieStrData', mCookieEndList[0]);
          clearInterval(loop);
        }
      }
    }, 1000);
  },
  // 扁平化树形数组
  // treeArray树形数组
  // childKey 代表子级字段名
  flattenTree(treeArray, childKey) {
    let result = [];

    treeArray.forEach((node) => {
      // 添加当前节点
      result.push(node);

      // 如果存在 children，递归处理
      if (node[childKey] && node[childKey].length > 0) {
        result = result.concat(this.flattenTree(node[childKey], childKey));
      }
    });

    return result;
  },

  // 查找祖先节点
  findAncestors(arr, childUuid) {
    // 创建节点映射以便快速查找
    const nodeMap = {};
    arr.forEach((node) => {
      nodeMap[node.uuid] = node;
    });
    const ancestors = [];
    let currentPid = nodeMap[childUuid].pid;
    ancestors.push(nodeMap[childUuid]);
    while (currentPid) {
      const currentNode = nodeMap[currentPid];

      ancestors.push(currentNode);
      currentPid = currentNode.pid;
    }
    // 反转数组，使根节点在第一个位置
    return ancestors.reverse();
  },
  // 给每一个子项添加父节点
  addAllNameToTree(nodes, parentNames = []) {
    return nodes.map((node) => {
      // 创建当前节点的全路径名称
      const allNameArr = [...parentNames, node.xmm];
      if (allNameArr.length > 1) {
        allNameArr.pop();
      }
      const currentAllName = allNameArr.join(' / ');

      // 创建新节点对象，保留原有属性并添加 allName
      const newNode = {
        ...node,
        curXmm: currentAllName,
      };

      // 如果有子节点，递归处理子节点
      if (node.czfreqvoChildList && node.czfreqvoChildList.length > 0) {
        newNode.czfreqvoChildList = this.addAllNameToTree(node.czfreqvoChildList, [...parentNames, node.xmm]);
      }

      return newNode;
    });
  },
  // 防抖函数
  debounce(func, delay = 300, immediate = false) {
    let timer = null;

    return function (...args) {
      const context = this;
      const callNow = immediate && !timer;

      clearTimeout(timer);

      timer = setTimeout(() => {
        if (!immediate) {
          func.apply(context, args);
        }
        timer = null;
      }, delay);

      if (callNow) {
        func.apply(context, args);
      }
    };
  },
};
