import { xwcjOptions } from '@gt4/common-front';
// import { xwcjOptions } from '@gt/components';
import dayjs from 'dayjs';
/**
 * 开发调试步骤
 * 1. 企A {"SssqQ":"2023-12-01","SssqZ":"2023-12-31","Djxh":"10214406000000030307"}
 * 2. 在localStorage中加 xwcjVisible: true; xwcjEnv: develop
 * 3. 在hosts加 127.0.0.1 xx.sat.tax.cn   ************ dzswj.xwcjsjjsctrlnsrd.jcsj.tax.cn
 */

const ywMcMap = {
  qysds_a_yjd: '居民企业（查账征收）企业所得税月（季）度申报',
  'qysds_a_nd/sz/iframe': '居民企业（查账征收）企业所得税年度申报',
  qysds_a_nd: '居民企业（查账征收）企业所得税年度申报',
  qysds_b_yjd: '居民企业（核定征收）企业所得税月（季）度申报',
  qysds_b_nd: '居民企业（核定征收）企业所得税年度申报',
  qysdskdqjyndsb: '跨地区经营汇总纳税企业的分支机构年度纳税申报',
};

// 前置校验行为采集模板
const getQzjyCjxxTemp = () => ({
  sjcjkey: 'qysdstqsbjkjlh',
  version: '1.0.0',
  wblj: {
    fxfl: {
      fxdly: '《风险目录》',
      fxdmc: '非注销企业应缴纳税款在税款所属期对应的申报期前提前入库',
      nknshgzmc: '企业所得税提前申报监控及留痕',
    },
    ywss: {
      sstx: '所得税',
      sjsfz: '企业所得税',
      sjywhj: '', // 事项名称
    },
    xxxtgl: {
      sjxxxtmc: '新电子税务局纳税人端',
      cflhxtjd: '', // 事项名称
    },
    cflhxs: '阻断并提示',
  },
  jcxx: {
    cfsj: '',
    tsnr: '',
  },
  gjlhxx: {
    gjlhxx_1: {
      txtcxz: '返回首页',
    },
    gjlhxx_2: {
      sbssqq: '',
    },
    gjlhxx_3: {
      sbssqz: '',
    },
    gjlhxx_4: {
      sbrq: '',
    },
    gjlhxx_5: {
      nsrzt: '', // 纳税人状态
    },
  },
  gzmsxx: {
    gzmsxx_1: {
      gzlj: '若干条件满足其中一条',
    },
    gzmsxx_2: {
      // tqsb 校验不通过即是满足。nsrzt！=06，qsztsbsqjk是不做校验的，verifyBegin是没有这条校验
      cftj: '通过电子税务局渠道办理的，核定征收居民企业未进入清算状态，不得在核定的税款所属期内提前申报本期税款。即：核定征收居民企业在规定纳税申报期之前进行纳税申报，监控其税务登记状态是否为“清算”，不为“清算”的，不得在规定纳税申报期之前进行纳税申报。并提示“为保护纳税人权益，电子税务局无法办理提前申报预缴企业所得税的业务，请进入申报期后再进行申报。”',
      tjpdqk: '不满足',
    },
    gzmsxx_3: {
      // "jkxmlx": "qsztsbsqjk"。跨地区的没有这条
      cftj: '纳税人状态为“清算”的，进行提示：“您当前为清算状态，只需申报清算备案日[yyyy-mm--dd]所属的XX年度第XX季度及XXX年第XX季度之前季度的未申报记录，对于后续季度无需申报。”',
      tjpdqk: '不满足',
    },
  },
  bcxx: {},
});

// 获取业务名称
const getYwMc = (route) => {
  const { SfNb, SssqQ = '', SssqZ = '' } = route.query;
  let ywDm = route.name;
  // 核定征收的年度特殊处理
  if (ywDm === 'qysds_b_yjd' && (SfNb === 'Y' || (SssqQ.indexOf('-01-01') > -1 && SssqZ.indexOf('-12-31') > -1))) {
    ywDm = 'qysds_b_nd';
  }
  return ywMcMap[ywDm] || '';
};

export const xwcjToQzjyHandle = async (route, body = []) => {
  const ywMc = getYwMc(route);
  // 非以上五个业务不进行留痕
  if (!ywMc) return;
  // 提前申报 or 清算状态纳税人申报属期提醒监控 被阻断 才进行上报
  const targetList = body.filter(
    (item) => (item.jkxmlx === 'tqsb' || item.jkxmlx === 'qsztsbsqjk') && item.tslxBm === 'zd',
  );
  if (targetList.length === 0) return;
  const { tsxx = '', jkxmlx } = targetList[0];
  const { nsrztmc = '' } = body.filter((item) => item.jkxmlx === 'nsrzt')[0] || {};
  const qzjyCjxxTemp = getQzjyCjxxTemp();
  const { SssqQ = '', SssqZ = '' } = route.query;
  const { wblj, jcxx, gjlhxx, gzmsxx } = qzjyCjxxTemp;
  wblj.ywss.sjywhj = ywMc;
  wblj.xxxtgl.cflhxtjd = ywMc;
  jcxx.cfsj = dayjs().format('YYYY-MM-DD HH:mm:ss');
  jcxx.tsnr = tsxx;
  gjlhxx.gjlhxx_2.sbssqq = SssqQ;
  gjlhxx.gjlhxx_3.sbssqz = SssqZ;
  gjlhxx.gjlhxx_4.sbrq = dayjs().format('YYYY-MM-DD');
  gjlhxx.gjlhxx_5.nsrzt = nsrztmc;
  qzjyCjxxTemp.sjcjkey = jkxmlx === 'qsztsbsqjk' ? 'QSZTSBSQJK' : 'FQSZTTQSBJK';
  const changeIndex = (jkxmlx === 'qsztsbsqjk') + 2;
  gzmsxx[`gzmsxx_${changeIndex}`].tjpdqk = '满足';
  gzmsxx[`gzmsxx_${changeIndex}`].cftj = tsxx;
  try {
    console.log(qzjyCjxxTemp);
    const oldUpdateTime = localStorage.getItem('xwcj_update_time');
    // 防止一秒上传两次
    if (oldUpdateTime === jcxx.cfsj) return;
    const response = await xwcjOptions.trackXwcjUploadNoGnCheck(qzjyCjxxTemp);
    // 更新行为采集时间
    response === '00' && localStorage.setItem('xwcj_update_time', jcxx.cfsj);
    console.log(response, '行为采集');
  } catch (error) {
    console.log('行为采集出错');
  }
};
