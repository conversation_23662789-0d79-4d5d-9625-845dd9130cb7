/* eslint-disable one-var */
import api from '@/pages/index/api/common/commonApi';
import { getDate } from '@gt4/common-front';

function maxRequest(fn, maxNum) {
  return new Promise((revolse, reject) => {
    fn().then(
      (value) => {
        revolse(value);
      },
      (err) => {
        if (maxNum !== undefined) {
          return maxRequest(fn, maxNum - 1);
        }
        return revolse(err);
      },
    );
  });
}

//
async function cacheDmbRequest({ prefixDmbUrl, Id, Params }) {
  const _api = api();
  let codeKey = Id; // 用于码表存sessionStore的key名
  if (Params) {
    const vals = Object.values(Params);
    vals.length > 0 ? (codeKey = `${codeKey}_${vals.join('_')}`) : '';
  }
  const selectDataSource = window.sessionStorage.getItem(codeKey);
  if (!selectDataSource) {
    const data = await _api.GetDmb(
      prefixDmbUrl,
      Id,
      Params,
      // params: _params,
    );
    if (data) {
      window.sessionStorage.setItem(codeKey, JSON.stringify(data));
    }
    return data || [];
  }
  return JSON.parse(selectDataSource);
}

// 递归获取vue子组件的实例ref
function getRef(name, children, key) {
  let ref = null;
  for (let i = 0; i < children.length; i++) {
    const val = children[i];
    if (val[key] === name) {
      ref = val;
      break;
    }
    if (val.$children && val.$children.length) {
      const subref = getRef(name, val.$children, key);
      if (subref) return subref;
    }
  }

  return ref;
}

// 解析获取URL中的参数
function getURLParams() {
  const sHref = window.location.href;
  const args = sHref.split('?');
  if (args[0] === sHref) {
    return '';
  }
  const herbart = args[1].split('#')[0].split('&');
  const obj = {};
  for (let i = 0; i < herbart.length; i++) {
    herbart[i] = herbart[i].split('=');
    // eslint-disable-next-line prefer-destructuring
    obj[herbart[i][0]] = herbart[i][1];
  }
  return obj;
}

// 格式化日期: yyyy-MM-dd
function formatDate(date) {
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  if (month < 10) {
    month = `0${month}`;
  }
  if (day < 10) {
    day = `0${day}`;
  }
  return `${year}-${month}-${day}`;
}
// 获取时间区间日期
function getTimeRange(val) {
  let st, et;
  const now = new Date(),
    nowDayOfWeek = now.getDay(),
    nowDay = now.getDate(),
    nowMonth = now.getMonth(),
    nowYear = now.getFullYear(),
    jd = Math.ceil((nowMonth + 1) / 3);
  switch (val) {
    case '周':
      st = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek);
      et = new Date(nowYear, nowMonth, nowDay + (6 - nowDayOfWeek));
      break;
    case '月':
      st = new Date(nowYear, nowMonth, 1);
      et = new Date(nowYear, nowMonth, nowDay);
      break;
    case '季':
      // 获取当前月份的前面三个月
      st = new Date(nowYear, (jd - 2) * 3, 1);
      et = new Date(nowYear, (jd - 1) * 3, 0);
      // 获取当前月份所在季度的月份
      // st = new Date(nowYear, (jd - 1) * 3, 1);
      // et = new Date(nowYear, jd * 3, 0);
      break;
    case '年':
      st = new Date(nowYear, 0, 1);
      et = new Date(nowYear, 11, 31);
      break;

    default:
      break;
  }
  return [st, et];
}

// 获取当前申报纳税人端上传url（244环境）
const getsbNsrdUploadBaseUrl = () => {
  const whiteList = ['localhost', '*************'];
  const { hostname } = window.location;
  return whiteList.includes(hostname) ? 'http://**************:8080' : '';
};

// 对话框打开封装, 需传对话框name，形如{ name:'aaa',  options:{}  } 默认宽度1182，位置center
function openDialog(params) {
  // 默认options
  const baseOptions = {
    placement: 'center',
    width: '1182',
    onConfirm: ({ e, trigger }) => {
      this[params.name].hide();
    },
    onClose: ({ e }) => {
      this[params.name].hide();
    },
  };
  if (this[params.name]) {
    this[params.name].show();
    console.log('该对话框已存在将复用');
    return;
  }
  this[params.name] = this.$dialog({
    ...baseOptions,
    ...params.options,
  });
}

// 计算年度、月度、季度的时间范围
const strToDateRange = function (val, mode) {
  if (!val) {
    return ['', ''];
  }
  if (mode === 'quarter') {
    const quarterMap = {
      Q1: '01',
      Q2: '04',
      Q3: '07',
      Q4: '10',
    };
    const current = val.toString().replace(/Q\d$/, (value) => {
      return `${quarterMap[value]}-01`;
    });
    return getDate.getQuarter(current);
  }
  if (mode === 'month') {
    return getDate.getThisMonthRange(val);
  }

  return getDate.getThisYearRange(val);
};

// 生成伪UUID
function genUUID() {
  let uuid = '';
  for (let i = 0; i < 8; i++) {
    // eslint-disable-next-line no-bitwise
    uuid += (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `fs${uuid}${new Date().getTime()}`;
}

const sbNsrdUploadBaseUrl = getsbNsrdUploadBaseUrl();
export {
  maxRequest,
  cacheDmbRequest,
  getRef,
  openDialog,
  getURLParams,
  getTimeRange,
  formatDate,
  strToDateRange,
  genUUID,
  sbNsrdUploadBaseUrl,
};
