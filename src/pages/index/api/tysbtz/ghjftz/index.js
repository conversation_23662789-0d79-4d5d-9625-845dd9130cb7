/*
 * @Author: liumu
 * @Date: 2025-03-26 15:01:10
 * @LastEditTime: 2025-04-07 10:45:21
 * @LastEditors: liumu
 * @Description: 描述
 */
import { fetch } from '@/core/request';

export default {
  initData(data) {
    return fetch({
      url: '/fssrTzzx/tysb/v1/initData',
      method: 'post',
      data: JSON.stringify(data),
    });
  },
  saveOrUpdate(data) {
    return fetch({
      url: '/fssrTzzx/tysb/v1/saveOrUpdate',
      method: 'post',
      data: JSON.stringify(data),
    });
  },
  delete(data) {
    return fetch({
      url: '/fssrTzzx/tysb/v1/delete',
      method: 'post',
      data: JSON.stringify(data),
    });
  },

  hqzspmzm(data) {
    return fetch({
      url: '/fssrTzzx/tysb/v1/hqxlxx',
      method: 'post',
      data: JSON.stringify(data),
    });
  },
};
