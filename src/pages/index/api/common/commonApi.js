import { fetch } from '@/core/request';

const commonBaseURL = '/common/v1';

export default function (prefix) {
  return {
    // 获取前端全局配置参数
    getFrontConfig() {
      return fetch({
        url: '/common/v1/getFrontConfig',
        method: 'post',
        loading: true,
        noMessage: true,
      });
    },

    // 获取码表数据
    getDmb(data, loading = true) {
      return fetch({
        url: `${commonBaseURL}/dmb`,
        method: 'post',
        data,
        loading,
      });
    },

    // 获取子节点码表
    getChildrentByDm(data, loading = true) {
      return fetch({
        url: `${commonBaseURL}/getChildrentByDm`,
        method: 'post',
        data,
        loading,
      });
    },

    // 文书业务前置校验
    verifyBegin(data) {
      return fetch({
        url: `${prefix}/v1/verifyBegin`,
        method: 'post',
        data,
        loading: true,
      });
    },

    // 获取历史数据
    beginData(data = {}) {
      return fetch({
        url: `${prefix}/v1/beginData`,
        method: 'post',
        data,
        loading: true,
      });
    },

    // 获取列表
    GetSheets(options, params) {
      const { loading = true, ...data } = options;
      return fetch({
        url: `${prefix}/v1/getSheets`,
        method: 'post',
        data,
        params,
        loading,
      });
    },

    // 获取表单公式
    GetFormulas(options, params) {
      const { loading = true, bilingual = false, ...data } = options;
      return fetch({
        url: `${prefix}/v1/getFormulas`,
        method: 'post',
        data,
        params,
        loading,
        bilingual,
      });
    },

    // 初始化数据
    InitData(options, params) {
      const { loading = true, bilingual = false, ...data } = options;
      return fetch({
        url: `${prefix}/v1/initData`,
        method: 'post',
        data,
        params,
        loading,
        bilingual,
      });
    },

    // 进度事项查看
    ckFormInitData(data, loading = true) {
      return fetch({
        url: `${prefix}/v1/ckFormInitData`,
        method: 'post',
        data,
        loading,
      });
    },

    // 异步提交表单
    SubmitForm(options, params) {
      const { loading = true, bilingual = false, ...data } = options;
      return fetch({
        url: `${prefix}/v1/submitForm`,
        method: 'post',
        data,
        params,
        loading,
        bilingual,
      });
    },

    // 获取文书申请结果
    GetSqResult(options, params) {
      const { loading = false, bilingual = false, ...data } = options;
      return fetch({
        url: `${prefix}/v1/getSqResult`,
        method: 'post',
        data,
        params,
        bilingual,
        noMessage: true,
      });
    },

    // 提供给 gt-select 组件获取代码表
    GetDmb(prefixDmbUrl, Id, _params) {
      return fetch({
        url: `${commonBaseURL}/dmb`,
        method: 'post',
        data: { Id, Params: _params },
        params: null,
      }).then((res) => {
        return res.data;
      });
    },

    /** ************************ 附送资料 ********************* */
    // Sxuuid	string	事项UUID
    // Swsxdm	string	税务事项代码，无则不需要传入此参数
    // Ywbm	string	附列资料代码，如果
    // Djxh  string	Djxh（纳税人端调用时从会话取，不需要传；税务人端调用则必传）
    // 根据事项UUID获取已上传的附列资料列表.
    getBusinessFlzlList(data) {
      return fetch({
        url: '/flzl/v1/getBusinessFlzlList',
        method: 'post',
        data,
        loading: true,
        baseURL: '/wszlzx/api/dzzl',
      });
    },

    // Oldsxuuid	string	Old事项uuid
    // Newsxuuid	string	New事项uuid
    // Swsxdm	string	税务事项代码
    // Djxh	string	Djxh（纳税人端调用时从会话取，不需要传；税务人端调用则必传）
    // 补正复用附列资料，把Oldsxuuid下的附列资料关联到Newsxuuid下
    bzFyFlzl(data) {
      return fetch({
        url: '/flzl/v1/bzFyFlzl',
        method: 'post',
        data,
        loading: true,
        baseURL: '/wszlzx/api/dzzl',
      });
    },

    // 复用附列资料
    fyFlzl(data) {
      return fetch({
        url: '/flzl/v1/fyFlzl',
        method: 'post',
        data,
        loading: true,
        baseURL: '/wszlzx/api/dzzl',
        noMessage: true,
      });
    },
  };
}
