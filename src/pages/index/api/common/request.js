import { fetch, fileFetch } from '@/core/request';

export default {
  listXzqh({ params }) {
    return fetch({
      url: '/common/v1/listXzqh',
      method: 'post',
      params,
    });
  },
  listYhyywdByQzqhAndYhhb({ data }) {
    return fetch({
      url: '/common/v1/listYhyywdByQzqhAndYhhb',
      method: 'post',
      data,
    });
  },
  getXzqhList({ params }) {
    return fetch({
      url: '/common/v1/getXzqhList',
      method: 'post',
      params,
    });
  },
  /**
   * 获取缓存表信息，多个表，号分隔
   * @param params
   * @returns {*}
   */
  gethcbData({ params }) {
    return fetch({
      url: '/tdzzsxmxxbg/v1/getXtdmxx',
      method: 'get',
      params,
    });
  },
  // 获取街道乡镇
  getJdxz({ params }) {
    return fetch({
      url: '/tdzzsxmxxbg/v1/getJdxz',
      method: 'get',
      params,
    });
  },
  // 获取税务机关
  getSwjg({ params }) {
    return fetch({
      url: '/tdzzsxmxxbg/v1/getSwjg',
      method: 'get',
      params,
    });
  },
  // 获取税务科所
  getSwks({ params }) {
    return fetch({
      url: '/tdzzsxmxxbg/v1/getSwks',
      method: 'get',
      params,
    });
  },
  /**
   * 上传附列资料，组件内部接口
   */
  getFlzlListBySwsxDm({ data }) {
    return fileFetch({
      url: '/wszlzx/api/dzzl/flzl/v1/getFlzlListBySwsxDm',
      method: 'post',
      data,
    });
  },
  /**
   * 上传附列资料，组件内部接口
   */
  getBusinessFlzlList({ data }) {
    return fileFetch({
      url: '/wszlzx/api/dzzl/flzl/v1/getBusinessFlzlList',
      method: 'post',
      data,
    });
  },
  checkNsrzt() {
    return fetch({
      url: '/common/v1/checkNsrzt',
      method: 'get',
    });
  },

  downloadFile({ params }) {
    return fetch({
      url: '/common/v1/downloadFile',
      method: 'post',
      params,
    });
  },

  getSflx() {
    return fetch({
      url: '/common/v1/getSflx',
      method: 'get',
    });
  },
};
