import { fetch } from '@/core/request';
import commonApi from '@/pages/index/api/common/commonApi';

const prefix = '/qysdssb/kjqysdsbg/v1';
const dyPrefix = '/sdsgjsjg/tbnstzxhxssxJd/v1';

export default {
  ...commonApi('/qysdssb/kjqysdsbg'),

  // 查询法定/指定扣缴的合同信息列表，支持分页，支持直接查系统合同编号
  cxHtlbxx(data) {
    return fetch({
      url: `${prefix}/cxHtlbxx`,
      method: 'post',
      data,
    });
  },

  //  查询递延税款补缴规则接口
  getFormulasDyskbj(data) {
    return fetch({
      url: `${dyPrefix}/getFormulas`,
      method: 'post',
      data,
      loading: true,
    });
  },
  //  查询递延税款补缴初始化数据
  getInitDataDyskbj(data, params) {
    return fetch({
      url: `${dyPrefix}/initData`,
      method: 'post',
      data,
      params,
    });
  },
  //  递延补缴查询报告列表
  cxDybjbgxx(data, params) {
    return fetch({
      url: `${prefix}/cxDybjbgxx`,
      method: 'post',
      data,
      params,
    });
  },
  //  查询扣缴义务人登记
  getFjmhtbaxx(data, params) {
    return fetch({
      url: `${prefix}/getFjmhtbaxx`,
      method: 'post',
      data,
      params,
    });
  },
  // 根据识别号查非居民信息(扣缴企业所得税)
  cxFjmsfxx(data) {
    return fetch({
      url: `${prefix}/cxFjmsfxx`,
      method: 'post',
      data,
    });
  },
  // 劳务合同情况，直接查核心登记信息
  cxNsrxxByFjmdjxh(data) {
    return fetch({
      url: `${prefix}/cxNsrxxByFjmdjxh`,
      method: 'post',
      data,
    });
  },
  // （合同选不了，但不阻断业务继续使用）查询合同信息
  cxHtxx(data) {
    return fetch({
      url: `${prefix}/cxHtxx`,
      method: 'post',
      data,
      notClose: true,
    });
  },

  // 查询合同执行起止日期
  getHtzxq(data, params) {
    return fetch({
      url: `${prefix}/getHtzxq`,
      method: 'post',
      data,
      params,
      loading: true,
    });
  },
  // 源泉扣缴合同查询所得类型代码
  getSdlxDm(data, params) {
    return fetch({
      url: `${prefix}/getSdlxDm`,
      method: 'post',
      data,
      params,
      noMessage: true,
    });
  },
  // 更新申报所得类型及代码
  updateSdlxdm(data, params) {
    return fetch({
      url: `${prefix}/updateSdlxdm`,
      method: 'post',
      data,
      params,
      noMessage: true,
    });
  },
  // 生成指定扣缴文书待办
  scZdkjwsDb(data, params) {
    return fetch({
      url: `${prefix}/scZdkjwsDb`,
      method: 'post',
      data,
      params,
      loading: true,
    });
  },
  // 生成递延纳税预填待办信息
  scDynsytdbxx(data, params) {
    return fetch({
      url: `${prefix}/scDynsytdbxx`,
      method: 'post',
      data,
      params,
      noMessage: true,
    });
  },
  // 查询未关联递延报告单申报信息
  cxWglDybgbSbxx(data) {
    return fetch({
      url: `${prefix}/cxWglDybgbSbxx`,
      method: 'post',
      data,
      loading: true,
    });
  },
  // 查询未关联递延报告单申报信息
  getYhlb(data = {}) {
    return fetch({
      url: `${prefix}/getYhlb`,
      method: 'post',
      data,
      loading: true,
      noMessage: true,
    });
  },
  // 导出 excel
  downloadExportFile(data) {
    return fetch({
      url: `${prefix}/xsxddyxxbgbDownloadFile`,
      method: 'post',
      data,
      loading: true,
      notClose: true,
    });
  },
};
