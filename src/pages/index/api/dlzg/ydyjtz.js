import { fetch } from '@/core/request';

const servicePrefix = '/ydyjtzController';

export const queryByCondition = (params) => {
  return fetch({
    url: `${servicePrefix}/queryByCondition`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryQyList = () => {
  return fetch({
    url: `${servicePrefix}/queryQyList`,
    method: 'post',
    loading: true,
  });
};