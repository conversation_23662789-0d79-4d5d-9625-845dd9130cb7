/*
 * @Descripttion: 门户首页接口请求
 * @Version: 1.0
 * @Author: wjx
 * @Date: 2024-04-02 13:50:13
 * @LastEditors: wjx
 * @LastEditTime: 2024-04-02 13:50:24
 */
import { fetch, fetchNoPrefix } from '@/core/request';

const servicePrefix = '';
export default {
  getInfo() {
    return fetch({
      url: `${servicePrefix}/personal/getInfo`,
      method: 'post',
    });
  },
  resetPassword(params) {
    return fetch({
      url: `${servicePrefix}/personal/resetPassword`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  qhjg(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/qhjg`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getAllJgOfZz(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/getAllJgOfZz`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  init() {
    return fetch({
      url: `${servicePrefix}/user/init`,
      method: 'post',
    });
  },
  bindedJgList(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/bindedJgList`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  switchJgList(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/switchJgList`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  dbrwslQuery(params) {
    return fetchNoPrefix({
      url: `${servicePrefix}/xxzx/wddb/dbrwslQuery`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  upPicUrl(data) {
    return fetch({
      url: `${servicePrefix}/user/upPicUrl`,
      data,
      method: 'post',
      'Content-Type': 'multipart/form-data',
    });
  },
  getYtbsxx(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getYtbsxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getYysrxx(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getYysrxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  //新增当期开票金额模块
  getdqkpje(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getdqkpje`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getZzsxgxx(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getZzsxgxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getQysdsgxxx(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getQysdsgxxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getFxsxxgxx(params) {
    return fetch({
      url: `${servicePrefix}/tbxxsj/v1/getFxsxxgxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  saveTxdzUrl(params) {
    return fetch({
      url: `${servicePrefix}/user/saveTxdzUrl`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getPicUrl(params) {
    return fetch({
      url: `${servicePrefix}/user/getPicUrl`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
};
