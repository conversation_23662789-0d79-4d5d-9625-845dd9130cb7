import { fetch } from '@/core/request';
import { MessagePlugin } from 'tdesign-vue';

const servicePrefix = '';

export default {
  init() {
    return fetch({
      url: `${servicePrefix}/user/init`,
      method: 'post',
    });
  },
  qhzz(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/qhzz`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getAllZzxx(params) {
    return fetch({
      url: `${servicePrefix}/zzxx/getAllZzxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  insert(params) {
    return fetch({
      url: `${servicePrefix}/zzxx/insert`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  update(params) {
    return fetch({
      url: `${servicePrefix}/zzxx/update`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  delete(params) {
    return fetch({
      url: `${servicePrefix}/zzxx/delete`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getAllJgxx(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/getAllJgxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  zzjgInsert(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/insert`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  deleteQyByZz(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/delete`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getUserAll(params) {
    return fetch({
      url: `${servicePrefix}/user/getAll`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  yhxxDelete(params) {
    return fetch({
      url: `${servicePrefix}/user/unBindZz`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getYhuuidsOfZz(params) {
    return fetch({
      url: `${servicePrefix}/user/getYhuuidsOfZz`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  yhxxInsert(params) {
    return fetch({
      url: `${servicePrefix}/user/bindZz`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  insertYhxx(params) {
    return fetch({
      url: `${servicePrefix}/user/insertYhxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  deleteYhxx(params) {
    return fetch({
      url: `${servicePrefix}/user/deleteYhxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  updateYhxx(params) {
    return fetch({
      url: `${servicePrefix}/user/updateYhxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getAllJsxx(params) {
    return fetch({
      url: `${servicePrefix}/user/getAllJsxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getAllRole(params) {
    return fetch({
      url: `${servicePrefix}/role/getAllRole`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  bindJsxxBatch(params) {
    return fetch({
      url: `${servicePrefix}/user/bindJsxxBatch`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  bindJgxxBatch(params) {
    return fetch({
      url: `${servicePrefix}/user/bindJgxxBatch`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getDqyhAllJgxx(params) {
    return fetch({
      url: `${servicePrefix}/user/getAllJgxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  resetPassword(params) {
    return fetch({
      url: `${servicePrefix}/user/resetPassword`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  unlockUser(params) {
    return fetch({
      url: `${servicePrefix}/user/unlockUser`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  lockUser(params) {
    return fetch({
      url: `${servicePrefix}/user/lockUser`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  zzjgDelete(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/delete`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },

  sfzjlxList(params) {
    return fetch({
      url: `${servicePrefix}/user/sfzjlxList`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  switchZzList(params) {
    return fetch({
      url: `${servicePrefix}/zzxx/switchZzList`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  getGLZzid() {
    return fetch({
      url: `${servicePrefix}/zzxx/getGLZzid`,
      data: JSON.stringify(),
      method: 'post',
    });
  },
  getBjCompanyInfo(params) {
    return fetch({
      url: `${servicePrefix}/zzjg/getBjCompanyInfo`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  exportExcel() {
    return fetch({
      url: `${servicePrefix}/user/exportExcel`,
      data: JSON.stringify(),
      method: 'post',
      loading: true,
      responseType: 'blob',
    }).then((res) => {
      if (res.data.type === 'application/json') {
        const reader = new FileReader();
        reader.readAsText(res.data);
        reader.onload = (event) => {
          const jsonData = JSON.parse(event.target.result);
          const { Data } = jsonData.Response;
          MessagePlugin.warning(Data.returnMsg);
        };
      } else {
        console.log('excel', res.data);
        // 解析成对象失败，说明是正常的文件流，将文件流转成blob形式
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=UTF-8' });
        // 创建一个超链接，将文件流赋进去，然后实现这个超链接的单击事件
        const eLink = document.createElement('a');
        eLink.download = '用户管理模板下载'; // 配置下载文件名
        eLink.style.display = 'none';
        eLink.href = URL.createObjectURL(blob);
        document.body.appendChild(eLink);
        eLink.click();
        URL.revokeObjectURL(eLink.href); // 释放URL 对象
        document.body.removeChild(eLink);
      }
    });
  },
  importExcel() {
    return fetch({
      url: `${servicePrefix}/user/importExcel`,
      data: JSON.stringify(),
      method: 'post',
    });
  },
};
