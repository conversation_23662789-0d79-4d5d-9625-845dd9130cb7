/*
 * @Descripttion:
 * @Version: 1.0
 * @Author: wjx
 * @Date: 2024-02-04 14:09:21
 * @LastEditors: wjx
 * @LastEditTime: 2024-02-04 14:24:32
 */
import { fetch } from '@/core/request';

const servicePrefix = '/demo/v1';
export default {
  /**
   * 初始化
   * @param params
   * @returns {*}
   */
  queryNsrxx(params) {
    return fetch({
      url: `/yhxx/getYhxx?djxh=1`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  querySbxx(params) {
    return fetch({
      url: `${servicePrefix}/querySbxx`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
  saveSbData(params) {
    return fetch({
      url: `${servicePrefix}/saveSbData`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
};
