import { fetchSso, fetch } from '@/core/request';

// const servicePrefix = '/tzzx/tzpz';
const servicePrefix1 = '/tzpz';

export default {
  initTzxxJson(params) {
    return fetch({
      url: `${servicePrefix1}/initTz`,
      // url: `${servicePrefix1}/initTz?tzbm=${params.tzbm}`,
      data: JSON.stringify(params),
      method: 'post',
      // method: 'get',
      // loading: true,
    });
  },
  commonTzInitData(url, params) {
    return fetch({
      url: `${url}`,
      data: JSON.stringify(params),
      method: 'post',
    });
  },
};
