import { fetch } from '@/core/request';

const servicePrefix = '/yjtz/v1';

// 查询企业所得税税费种认定信息
export const queryNsrzInfo = (params) => {
  return fetch({
    url: `${servicePrefix}/queryNsrzInfo`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
    // // 模拟API返回，nsqx_dm为'06'表示月报模式
    // return new Promise((resolve) => {
    //   setTimeout(() => {
    //     resolve({
    //       success: true,
    //       data: {
    //         nsqx_dm: '06', // 06表示月报，08表示季报
    //         // 其他模拟数据
    //         nsrsbh: '123456789012345678',
    //         nsrmc: '模拟企业名称',
    //         djxh: '123456789',
    //       },
    //     });
    //   }, 500);
  });
};

export const queryYjtz = (params) => {
  return fetch({
    url: `${servicePrefix}/queryYjtz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const updateYjtz = (params) => {
  return fetch({
    url: `${servicePrefix}/updateYjtz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryFzjgfztz = (params) => {
  return fetch({
    url: `${servicePrefix}/queryFzjgfztz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryFzjgfztzHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryFzjgfztzHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const updateFzjgfztz = (params) => {
  return fetch({
    url: `${servicePrefix}/updateFzjgfztz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const yjtzTqsj = (params) => {
  return fetch({
    url: `${servicePrefix}/yjtzTqsj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
