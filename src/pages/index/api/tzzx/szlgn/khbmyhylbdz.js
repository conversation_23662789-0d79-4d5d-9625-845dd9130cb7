import { fetch } from '@/core/request';

const servicePrefix = '/khbhhylbdz/v1';

// 查询对照表信息
export const queryDzb = (params) => {
  return fetch({
    url: `${servicePrefix}/queryDzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 更新对照表行业类别
export const gxDzbHylb = (params) => {
  return fetch({
    url: `${servicePrefix}/gxDzbHylb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 查询客户编号列表
export const getKhbmList = (params) => {
  return fetch({
    url: `${servicePrefix}/getKhbmList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 导入
export const input = (params) => {
  return fetch({
    url: `${servicePrefix}/input`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 模版下载（暂未使用）
export const downloadTemplate = (params) => {
  return fetch({
    url: `${servicePrefix}/downloadTemplate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 通过客户编码获得行业类别
export const getHylbByKhbh = (params) => {
  return fetch({
    url: `${servicePrefix}/getHylbByKhbh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
