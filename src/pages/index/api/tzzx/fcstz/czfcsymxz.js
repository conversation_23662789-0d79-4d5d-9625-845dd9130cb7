import { fetch } from '@/core/request';

const servicePrefix = '/czf/v1';

export const getCzfsymx = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/queryCzftzxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getNdyzj = (params) => {
  return fetch({
    url: `${servicePrefix}/getNdyzj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
