import { fetch } from '@/core/request';

const servicePrefix = '/fcjcxxtzb/v1';

// 初始化查询-房产税
export const initFcjcxxtzbQuery = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/queryFcstzxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 删除-房产税
export const deleteFcs = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/deleteFcjcxxtzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 生成税源-房产税
export const scsyFcs = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/scfcssy`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 保存-房产税
export const saveFcs = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/saveFcjcxxtzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 修改-房产税
export const updateFcs = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/saveFcjcxxtzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 初始化查询-资产明细
export const initZcmxbQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initZcmxbQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 初始化查询详情
export const initbQueryXq = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/queryFcstzxxByUUid`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
