import { fetch } from '@/core/request';

const servicePrefix = '/yhsCqzysjtz/v1';

export default {
  init(params) {
    return fetch({
      url: `${servicePrefix}/initYhsCqzysjtzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${servicePrefix}/initYhsCqzysjtzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${servicePrefix}/yhsCqzysjSave`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${servicePrefix}/yhsCqzysjMbDownload`,
      method: 'get',
      loading: true,
    });
  },
};
