import { fetch } from '@/core/request';

const servicePrefix = '/yhsYyzbtz/v1';

export default {
  init(params) {
    return fetch({
      url: `${servicePrefix}/initYhsYyzbtzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${servicePrefix}/initYhsYyzbtzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${servicePrefix}/yhsYyzbtzSave`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${servicePrefix}/yhsYyzbtzMbDownload`,
      method: 'get',
      loading: true,
    });
  },
};
// 保存台账
export const yhsYyzbSaveTz = (params) => {
  return fetch({
    url: `/yhsYyzbtz/v1/updateYyzbtzxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 查询
export const yhsYyzbQuery = (params) => {
  return fetch({
    url: `/yhsYyzbtz/v1/queryYyzbtzxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 获取参数值
export const getYyzbCsz = () => {
  return fetch({
    url: `/yhsYyzbtz/v1/initYyzbtzxx`,
    data: JSON.stringify(),
    method: 'post',
    loading: true,
  });
};

// 修改往期最大值
export const updateWqzdz = (params) => {
  return fetch({
    url: `/yhsYyzbtz/v1/updateWqzdz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
