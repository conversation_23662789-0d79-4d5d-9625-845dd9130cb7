import { fetch } from '@/core/request';

const servicePrefix = '/yhsHtmxDlController';

export const queryHtmx = (params) => {
  return fetch({
    url: `${servicePrefix}/queryListByCondition`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryDetail = (uuid) => {
  return fetch({
    url: `${servicePrefix}/queryDetail?uuid=${uuid}`,
    method: 'post',
    loading: true,
  });
};

export const insertHtmx = (params) => {
  return fetch({
    url: `${servicePrefix}/insert`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteSelected = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteSelected`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteFailSelected = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteFailSelected`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteAll = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteAll`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const updateHtmx = (params) => {
  return fetch({
    url: `${servicePrefix}/update`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const uploadExcel = (params) => {
  return fetch({
    url: `${servicePrefix}/uploadExcel`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
