import { fetch } from '@/core/request';

const servicePrefix = '/httz/v1';
const oldServicePrefix = '/yhsHttz/v1';

export const queryHttz = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHttz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryYhslfkbl = (params) => {
  return fetch({
    url: `${servicePrefix}/queryYhslfkbl`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const deleteSelected = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteSelected`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const insertHttz = (params) => {
  return fetch({
    url: `${servicePrefix}/insertHttz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const updateHttz = (params) => {
  return fetch({
    url: `${servicePrefix}/updateHttz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryHttzHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHttzHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryHttzFiltered = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHttzFiltered`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryHttzFilteredHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHttzFilteredHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const checkYsb = (params) => {
  return fetch({
    url: `${servicePrefix}/checkYsb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export default {
  init(params) {
    return fetch({
      url: `${oldServicePrefix}/initYhsHttzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${oldServicePrefix}/initYhsHttzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${oldServicePrefix}/yhsHttzSave`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${oldServicePrefix}/yhsHttzMbDownload`,
      method: 'get',
      loading: true,
    });
  },
  queryData(params) {
    return fetch({
      url: `${oldServicePrefix}/initYhsHttzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
};
