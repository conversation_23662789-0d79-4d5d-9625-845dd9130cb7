import { fetch } from '@/core/request';

const servicePrefix = '/yhstz/v1';

export const initYhsZzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/queryYhsTzxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryYhstzxxHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryYhstzxxHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteYhscj = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteYhscj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryYhslfkbl = (params) => {
  return fetch({
    url: `${servicePrefix}/queryYhslfkbl`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const updateYhscjLsrq = (params) => {
  return fetch({
    url: `${servicePrefix}/updateYhscjLsrq`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const syYhsSy = (params) => {
  return fetch({
    url: `${servicePrefix}/syYhsSy`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
    timeout: 60000, // 设置超时时间为60秒
  });
};
export const yyzbWqzdzCheck = (params) => {
  return fetch({
    url: `${servicePrefix}/yyzbWqzdzCheck`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
    timeout: 60000, // 设置超时时间为60秒
  });
};
export const yyzbTsCheck = (params) => {
  return fetch({
    url: `${servicePrefix}/yyzbTsCheck`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export default {
  init(params) {
    return fetch({
      url: `${servicePrefix}/initYhsZzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${servicePrefix}/initYhsZzQuery`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${servicePrefix}/yhsZzSave`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${servicePrefix}/yhsZzMbDownload`,
      method: 'get',
      loading: true,
    });
  },
};
