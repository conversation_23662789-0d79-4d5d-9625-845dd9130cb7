import { fetch } from '@/core/request';

const servicePrefix = '/pzmx/v1';

export const queryPzmx = (params) => {
  return fetch({
    url: `${servicePrefix}/queryPzmx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const queryPzmxHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryPzmxHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteSelected = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteSelected`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const insertPzmx = (params) => {
  return fetch({
    url: `${servicePrefix}/insertPzmx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const updatePzmx = (params) => {
  return fetch({
    url: `${servicePrefix}/updatePzmx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getHtlx = () => {
  return fetch({
    url: `${servicePrefix}/getHtlx`,
    method: 'post',
    loading: true,
  });
};
export const getHtxlByHtlx = (params) => {
  return fetch({
    url: `${servicePrefix}/getHtxlByHtlx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getKmbmByHtxl = (params) => {
  return fetch({
    url: `${servicePrefix}/getKmbmByHtxl`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getKplx = () => {
  return fetch({
    url: `${servicePrefix}/getKplx`,
    method: 'post',
    loading: true,
  });
};
export const getPzlx = () => {
  return fetch({
    url: `${servicePrefix}/getPzlx`,
    method: 'post',
    loading: true,
  });
};
export const getKhz = () => {
  return fetch({
    url: `${servicePrefix}/getKhz`,
    method: 'post',
    loading: true,
  });
};
export const getPzmxAllKmbm = () => {
  return fetch({
    url: `${servicePrefix}/getPzmxAllKmbm`,
    method: 'post',
    loading: true,
  });
};
export const getAllHtxl = () => {
  return fetch({
    url: `${servicePrefix}/getAllHtxl`,
    method: 'post',
    loading: true,
  });
};
