import { fetch } from '@/core/request';

const servicePrefix = '/srzz/v1';

export const initSrzzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initSrzzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const initSrzzQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/initSrzzQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const querySrzzAndSrcybdhzb = (params) => {
  return fetch({
    url: `${servicePrefix}/querySrzzAndSrcybdhzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const querySrzzAndSrcybdhzbHj = (params) => {
  return fetch({
    url: `${servicePrefix}/querySrzzAndSrcybdhzbHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryHzfp = (params) => {
  return fetch({
    url: `${servicePrefix}/queryHzfp`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const sfzjg = (params) => {
  return fetch({
    url: `${servicePrefix}/sfzjg`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 销项税差异明细
export const querySrcybdmx = (params) => {
  return fetch({
    url: `${servicePrefix}/querySrcybdmx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 销项税差异明细合计
export const querySrcybdmxHj = (params) => {
  return fetch({
    url: `${servicePrefix}/querySrcybdmxHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 查询参考凭证明细
export const queryFlbByckpzbh = (params) => {
  return fetch({
    url: `${servicePrefix}/queryFlbByckpzbh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 生成尾差凭证
export const cymxblunt = (params) => {
  return fetch({
    url: `${servicePrefix}/cymxblunt`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 差异比对忽略按钮
export const cybdhl = (params) => {
  return fetch({
    url: `${servicePrefix}/cybdhl`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getTzDifferences = (params) => {
  return fetch({
    url: `differentialComparison/getTzDifferences`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 小规模纳税人收入总账初始化
export const initSrzzQueryInXgmnsr = (params) => {
  return fetch({
    url: `/srzz/xgmnsr/v1/initSrzzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 小规模纳税人收入总账和收入差异对比查询
export const querySrzzAndSrcybdhzbInXgmnsr = (params) => {
  return fetch({
    url: `/srzz/xgmnsr/v1/querySrzzAndSrcybdhzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
