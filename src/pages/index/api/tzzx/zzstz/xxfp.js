import { fetch } from '@/core/request';

const servicePrefix = '/xxfp/v1';

// 发票明细
export const initXxfpMxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initXxfpMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 忽略疑点
export const xxfpUpdateHlbz = (params) => {
  return fetch({
    url: `${servicePrefix}/xxfpUpdateHlbz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 货物明细
export const initXxfpHwfwMxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initXxfpHwfwMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 货物 设置即征即退
export const xxfpHwfuMxSaveOrUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/xxfpHwfuMxSaveOrUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 销项总账查询
export const initXxfpZzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initXxfpZzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 销项发票明细合计查询
export const initXxfpMxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/initXxfpMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 销项发票货物明细合计查询
export const initXxfpHwfwMxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/initXxfpHwfwMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 小规模纳税人销项发票总账初始化
export const initXxfpZzQueryInXgmnsr = (params) => {
  return fetch({
    url: `/xxfp/xgmnsr/v1/initXxfpZzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
