import { fetch } from '@/core/request';

const jmszzPrefix = '/jmsZz/v1';
const jmsmxPrefix = '/jmsMxz/v1';

// 减免税总账查询
export const initJmszzQuery = (params) => {
  return fetch({
    url: `${jmszzPrefix}/initJmszzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 明细查询
export const initJmsmxQuery = (params) => {
  return fetch({
    url: `${jmsmxPrefix}/initJmsmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 明细 新增、修改
export const jmsmxzSaveOrUpdate = (params) => {
  return fetch({
    url: `${jmsmxPrefix}/jmsmxzSaveOrUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 分配相关数据
export const getKjfp = (params) => {
  return fetch({
    url: `gyDmget/v1/getKjfp`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 获取税收减免性质对照表
export const getSsjmxzData = (kjfpDm) => {
  return fetch({
    url: `/gyDmget/v1/getSsjmxzxx?kjfpDm=${kjfpDm}`,
    method: 'post',
    loading: true,
  });
};

// 删除： 入参 uuid集合
export const deleteJmsmxs = (params) => {
  return fetch({
    url: `${jmsmxPrefix}/jmsmxzDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//减免税明细合计查询
export const initJmsmxQueryHj = (params) => {
  return fetch({
    url: `${jmsmxPrefix}/initJmsmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};