// 进项总账、明细
import { fetch } from '@/core/request';

const servicePrefix = '/jxfp/v1';

// 初始化
export const init = (params) => {
  return fetch({
    url: `${servicePrefix}/initDkjxzzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 总账
export const initJxfpZzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initJxfpZzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 明细
export const initJxfpMxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initJxfpMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 明细货物
export const initJxfpHwfwMxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/initJxfpHwfwMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 获取会计凭证明细接口
export const getKjpzMx = (params) => {
  return fetch({
    url: `${servicePrefix}/getKjpzMx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 进项发票明细合计查询
export const initJxfpMxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/initJxfpMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 进项发票货物明细合计查询
export const initJxfpHwfwMxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/initJxfpHwfwMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 进项发票总账新增
export const jxfpzzInsert = (params) => {
  return fetch({
    url: `${servicePrefix}/jxfpzzInsert`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 进项发票总账修改
export const jxfpzzUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/jxfpzzUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 进项发票总账删除
export const jxfpzzDelete = (params) => {
  return fetch({
    url: `${servicePrefix}/jxfpzzDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
