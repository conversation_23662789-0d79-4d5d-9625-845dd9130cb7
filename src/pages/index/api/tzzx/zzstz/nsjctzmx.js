import { fetch } from '@/core/request';

const servicePrefix = '/ncjctz/v1';

export const nsjctzmxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/nsjctzmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const nsjctzmxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/nsjctzmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const nsjctzmxSave = (params) => {
  return fetch({
    url: `${servicePrefix}/nsjctzmxSave`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const nsjctzmxUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/nsjctzmxUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const nsjctzmxDelete = (params) => {
  return fetch({
    url: `${servicePrefix}/nsjctzmxDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
