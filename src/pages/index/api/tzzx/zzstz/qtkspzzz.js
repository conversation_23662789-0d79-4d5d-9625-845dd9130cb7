import { fetch } from '@/core/request';

const servicePrefix = '/qtkspzzz/v1';

export const qtkspzzzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/qtkspzzzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const qtkspzzzUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/qtkspzzzUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
