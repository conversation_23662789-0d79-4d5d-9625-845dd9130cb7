import { fetch } from '@/core/request';

const servicePrefix = '/wkjfpmx/v1';

export default {
  init(params) {
    return fetch({
      url: `${servicePrefix}/initData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${servicePrefix}/initData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${servicePrefix}/saveData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${servicePrefix}/downloadData`,
      method: 'get',
      loading: true,
    });
  },
};
