import { fetch } from '@/core/request';

const servicePrefix = '/jxsezc/v1';

// 进项税额转出总账列表
export const queryJxsezczzList = (params) => {
  return fetch({
    url: `${servicePrefix}/queryJxsezczzList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 进项税额转出明细列表
export const queryJxsezcmxzList = (params) => {
  return fetch({
    url: `${servicePrefix}/queryJxsezcmxzList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 进项税额总账编辑
export const saveJxsezczz = (params) => {
  return fetch({
    url: `${servicePrefix}/saveJxsezczz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 编辑进项转出税额
export const editJxsezczzswzd = (params) => {
  return fetch({
    url: `${servicePrefix}/editJxsezczzswzd`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 明细保存
export const addJxsezcmxz = (params) => {
  return fetch({
    url: `${servicePrefix}/addJxsezcmxz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 校验明细
export const queryCybdmxb = (params) => {
  return fetch({
    url: `${servicePrefix}/queryCybdmxb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const editJxsezcmxz = (params) => {
  return fetch({
    url: `${servicePrefix}/editJxsezcmxz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const deleteJxsezcmxz = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteJxsezcmxz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 进项税额转出明细新增和编辑时查询会计凭证是否重复接口
export const kjpzbhRepeatQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/kjpzbhRepeatQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 总账页面项目列表初始化
export const getJxsezczzInit = (params) => {
  return fetch({
    url: `${servicePrefix}/getJxsezczzInit`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 进项税额转出明细合计
export const queryJxsezcmxzListHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryJxsezcmxzListHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
