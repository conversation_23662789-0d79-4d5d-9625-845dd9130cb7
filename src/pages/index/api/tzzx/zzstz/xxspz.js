import { fetch } from '@/core/request';

const servicePrefix = '/xxspz/v1';

export const queryXxspz = (params) => {
  return fetch({
    url: `${servicePrefix}/queryXxspz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryXxspzHj = (params) => {
  return fetch({
    url: `${servicePrefix}/queryXxspzHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const addXxspz = (params) => {
  return fetch({
    url: `${servicePrefix}/addXxspz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const updateXxspz = (params) => {
  return fetch({
    url: `${servicePrefix}/updateXxspz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const deleteXxspz = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteXxspz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
