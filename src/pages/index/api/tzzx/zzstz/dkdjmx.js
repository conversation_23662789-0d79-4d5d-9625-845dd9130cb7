import { fetch } from '@/core/request';

const jmszzPrefix = '/dkdjpz/v1';

export const initDkdjpzMxQuery = (params) => {
  return fetch({
    url: `${jmszzPrefix}/initDkdjpzMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//代扣代缴明细合计查询
export const initDkdjpzMxQueryHj = (params) => {
  return fetch({
    url: `${jmszzPrefix}/initDkdjpzMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//代扣代缴项目保存
export const updateDkdjpzMx = (params) => {
  return fetch({
    url: `${jmszzPrefix}/updateDkdjpzMx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};