import { fetch } from '@/core/request';

const servicePrefix = '/jzjttz/v1';

export const jzjttzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjttzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const jzjttzQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjttzQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const jzjttzSave = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjttzSave`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const jzjttzUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjttzUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const jzjttzDelete = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjttzDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
