import { fetch } from '@/core/request';

const servicePrefix = '/srzz/v1';

export const getInit = () => {
  return fetch({
    url: `${servicePrefix}/getInit`,
    method: 'post',
    loading: true,
  });
};
export const srzzmxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/srzzmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const srmxSave = (params) => {
  return fetch({
    url: `${servicePrefix}/srmxSave`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const srmxUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/srmxUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const srmxDelete = (params) => {
  return fetch({
    url: `${servicePrefix}/srmxDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const scfxpz = (params) => {
  return fetch({
    url: `${servicePrefix}/scfxpz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 收入明细合计查询
export const srzzmxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/srzzmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
