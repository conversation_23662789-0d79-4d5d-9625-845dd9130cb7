import { fetch } from '@/core/request';

const jmszzPrefix = '/ckhwznxzm/v1';

export const initCkhwznxzmMxQuery = (params) => {
  return fetch({
    url: `${jmszzPrefix}/initCkhwznxzmMxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//出口货物转内销证明明细合计查询
export const initCkhwznxzmMxQueryHj = (params) => {
  return fetch({
    url: `${jmszzPrefix}/initCkhwznxzmMxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

