import { fetch } from '@/core/request';

// const servicePrefix = '/wkptz/v1';
const servicePrefix = '/srzz/v1';

export const wkpmxQuery = (params) => {
  return fetch({
    // url: `${servicePrefix}/wkpmxQuery`,
    url: `${servicePrefix}/srzzmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const wkpmxQueryHj = (params) => {
  return fetch({
    // url: `${servicePrefix}/wkpmxQueryHj`,
    url: `${servicePrefix}/srzzmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const wkpmxSave = (params) => {
  return fetch({
    // url: `${servicePrefix}/wkpmxSave`,
    url: `${servicePrefix}/srmxSave`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const wkpmxUpdate = (params) => {
  return fetch({
    // url: `${servicePrefix}/wkpmxUpdate`,
    url: `${servicePrefix}/srmxUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const wkpmxDelete = (params) => {
  return fetch({
    // url: `${servicePrefix}/wkpmxDelete`,
    url: `${servicePrefix}/srmxDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
