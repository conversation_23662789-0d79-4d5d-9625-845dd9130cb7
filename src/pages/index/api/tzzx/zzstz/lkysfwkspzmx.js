import { fetch } from '@/core/request';

const servicePrefix = '/lkysfwkspzmx/v1';

export const getInit = () => {
  return fetch({
    url: `/srzz/v1//getInit`,
    method: 'post',
    loading: true,
  });
};
export const lkysfwkspzQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwkspzQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const lkysfwkspzSave = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwkspzSave`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const lkysfwkspzUpdate = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwkspzUpdate`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const lkysfwkspzDelete = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwkspzDelete`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 旅客运输扣发票明细查
export const lkysfwfpmxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwfpmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//旅客运输服务凭证明细合计查询
export const lkysfwkspzQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwkspzQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

//旅客运输服务发票明细合计查询
export const lkysfwfpmxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/lkysfwfpmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
