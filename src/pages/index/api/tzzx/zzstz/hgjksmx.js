import { fetch } from '@/core/request';

const servicePrefix = '/hgwsmx/v1';

export const hgwsmxQuery = (params) => {
  return fetch({
    url: `${servicePrefix}/hgwsmxQuery`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const jzjtSet = (params) => {
  return fetch({
    url: `${servicePrefix}/jzjtSet`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const bdkyyGet = () => {
  return fetch({
    url: `${servicePrefix}/bdkyyGet`,
    method: 'post',
    loading: true,
  });
};

//海关缴款书明细合计查询
export const hgwsmxQueryHj = (params) => {
  return fetch({
    url: `${servicePrefix}/hgwsmxQueryHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
