import { fetch } from '@/core/request';

const servicePrefix1 = '/zzsyjKqyjy/v1';
const servicePrefix2 = '/zzsyjFzjg/v1';

export const queryZzsyjKqyjytz = (params) => {
  return fetch({
    url: `${servicePrefix1}/queryZzsyjKqyjytz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const queryZzsyjFzjgtz = (params) => {
  return fetch({
    url: `${servicePrefix2}/queryZzsyjFzjgtz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getZzsyjFzjgtzHj = (params) => {
  return fetch({
    url: `${servicePrefix2}/getZzsyjFzjgtzHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
