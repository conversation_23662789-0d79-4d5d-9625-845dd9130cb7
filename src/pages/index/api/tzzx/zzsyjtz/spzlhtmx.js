import { fetch } from '@/core/request';

// 查询-租赁合同
export const queryZlht = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryFcsHtxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 保存-租赁合同
export const saveZlht = (params) => {
  return fetch({
    url: `/fctzmx/v1/saveOrUpdateFcsHtxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 修改-租赁合同
export const updateZlht = (params) => {
  return fetch({
    url: `/fctzmx/v1/saveOrUpdateFcsHtxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 删除-租赁合同
export const deleteZlht = (params) => {
  return fetch({
    url: `/fctzmx/v1/deleteFcsHtxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 租赁合同-新增页面查询详情
export const initbQueryXq = (params) => {
  return fetch({
    url: `/fcjcxxtzb/v1/queryFcstzxxByFwcqzsh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 租赁合同-查询房产税合同明细
export const queryHtxxByHtbhFybh = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryHtxxByHtbhFybh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 租赁合同-查询房产税合同明细
export const queryFcsHtxxMx = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryFcsHtxxMx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 租赁合同-查询房产税合同明细
export const queryFcsHtxxMxByZbuuid = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryFcsHtxxMxByZbuuid`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 保存-租赁合同明细
export const saveHtmxxx = (params) => {
  return fetch({
    url: `/fctzmx/v1/saveHtmxxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 编辑-租赁合同明细
export const updateHtmxxx = (params) => {
  return fetch({
    url: `/fctzmx/v1/updateHtmxxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 删除-租赁合同明细
export const deleteHtmxxx = (params) => {
  return fetch({
    url: `/fctzmx/v1/deleteFcsHtxxMX`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 租赁合同-查询房产税合同明细合计
export const queryFcsHtxxMxByZbuuidHj = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryFcsHtxxMxByZbuuidHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 租赁合同-查询房产税合同明细合计
export const queryFcsHtxxMxHj = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryFcsHtxxMxHj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 租赁合同- 根据合同编号查询不动产权证书号
export const queryBdcqzshByHtbh = (params) => {
  return fetch({
    url: `/fctzmx/v1/queryBdcqzshByHtbh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 初始化增值税预缴计划数据
export const initZzsyjjh = (params) => {
  return fetch({
    url: `/zzsyjjh/v1/initZzsyjjh`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 保存增值税预缴计划数据
export const saveZzsyjjhList = (params) => {
  return fetch({
    url: `/zzsyjjh/v1/saveZzsyjjhList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
