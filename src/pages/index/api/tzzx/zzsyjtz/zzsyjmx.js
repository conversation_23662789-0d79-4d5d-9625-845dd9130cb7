import { fetch } from '@/core/request';

// 增值税预缴查询-出租不动产预缴明细
export const queryCzbdcyjmxList = (params) => {
  return fetch({
    url: `/zzsyjmx/v1/queryCzbdcyjmxList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 增值税预缴查询-增值税预缴明细
export const queryZzsyjmxList = (params) => {
  return fetch({
    url: `/zzsyjmx/v1/queryZzsyjmxList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 增值税预缴查询-增值税预缴明细合计
export const queryZzsyjmxSum = (params) => {
  return fetch({
    url: `/zzsyjmx/v1/queryZzsyjmxSum`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 增值税预缴查询-增值税预缴情况
export const queryZzsyjqkList = (params) => {
  return fetch({
    url: `/zzsyjmx/v1/queryZzsyjqkList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 增值税预缴查询-行政区划下拉树获取
export const getXzqhTreeData = (params) => {
  return fetch({
    url: `/fctzmx/v1/getXzqhTreeData`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
