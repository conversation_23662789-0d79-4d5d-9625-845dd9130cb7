import { fetch } from '@/core/request';

const gyDmgetPrefix = '/gyDmget/v1';

export const getAll = () => {
  return fetch({
    url: `${gyDmgetPrefix}/getAll`,
    method: 'post',
    loading: true,
  });
};
export const getKjfp = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getKjfp`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getKmbmByywbm = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getKmbmByywbm`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getLrzx = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getLrzx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getKjfpByJxsezcxm = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getKjfpByJxsezcxm`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getJxsezcxmList = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getJxsezcxmList`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getJsfssldzb = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getJsfssldzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 查询全部征税项目与税率对照表
export const queryAllZsxm1sldzb = () => {
  return fetch({
    url: `${gyDmgetPrefix}/queryAllZsxm1sldzb`,
    method: 'post',
    loading: true,
  });
};
// 校验
export const getTzDifferences = (params) => {
  return fetch({
    url: `differentialComparison/getTzDifferences`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
export const getDjxm = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getDjxm`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const getYhsHtZspm = (sszq) => {
  return fetch({
    url: `${gyDmgetPrefix}/getYhsHtZspm?sszq=${sszq}`,
    method: 'post',
    loading: true,
  });
};

export const getYhsHtZszm = (sszq) => {
  return fetch({
    url: `${gyDmgetPrefix}/getYhsHtZszm?sszq=${sszq}`,
    method: 'post',
    loading: true,
  });
};

// 土地用途
export const getCztdsysTdyt = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getDmData`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 权力类型
export const getCztdsysQllx = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getDmData`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 权力性质
export const getCztdsysQlxz = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getDmData`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 获取土地等级
export const getTddjListByXzqh = (params) => {
  return fetch({
    url: `/cztdsys/v1/getTddjListByXzqh?xzqhszDm=${params.xzqhszDm}&jdxzDm=${params.jdxzDm}`,
    data: JSON.stringify(params),
    method: 'get',
    loading: true,
  });
};

// 获取行政区划
export const getXzqhJdxzSwjg = (params) => {
  return fetch({
    url: `/fcsCztdsys/v1/getXzqhJdxzSwjg`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 获取从价中的计税比例
export const queryFcjsblList = (params) => {
  return fetch({
    url: `/fcs/v1/queryFcjsblList?swjgDm=${params.swjgDm}&czlx=${params.czlx}`,
    method: 'get',
  });
};

// 增值税台账提取数据时调用
export const extract = (params) => {
  return fetch({
    url: `/dataSync/extract`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 增值税台账进项发票提取发票时调用
export const extractInvoice = (params) => {
  return fetch({
    url: `/dataSync/extractInvoice`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 旅客运输服务凭证分配对照表
export const getLkysfwpzfpdzb = () => {
  return fetch({
    url: `${gyDmgetPrefix}/getLkysfwpzfpdzb`,
    method: 'post',
    loading: true,
  });
};

// 分支机构享受区域性优惠情况代码
export const getFzjgxsqyxyhqkDm = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/getFzjgxsqyxyhqkDm`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 当前是否为生产环境
export const isProductApi = () => {
  return fetch({
    url: `${gyDmgetPrefix}/isProduct`,
    method: 'post',
    loading: true,
  });
};

// 算税状态完毕状态
export const jyssReadyStatusApi = (params) => {
  return fetch({
    url: `/dataSync/jyssReadyStatus`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 是否已申报
export const sfysb = (params) => {
  return fetch({
    url: `/tzzxCommon/sfysb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 计算所属账期
export const computeSszqApi = () => {
  return fetch({
    url: `${gyDmgetPrefix}/computeSszq`,
    method: 'post',
    loading: true,
  });
};

// 当前是否为预生产环境
export const isYsApi = () => {
  return fetch({
    url: `${gyDmgetPrefix}/isYs`,
    method: 'post',
    loading: true,
  });
};

// 印花税台账提取数据时调用
export const yhsExtract = (params) => {
  return fetch({
    url: `/yhsDataSync/extract`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 房产税台账提取数据时调用
export const fcsExtract = (params) => {
  return fetch({
    url: `/fcsDataSync/extract`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 印花税台账提取数据时调用（获取状态）
export const yhsJyssReadyStatusApi = (params) => {
  return fetch({
    url: `/yhsDataSync/jyssReadyStatus`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 房产税台账提取数据时调用（获取状态）
export const fcsJyssReadyStatusApi = (params) => {
  return fetch({
    url: `/fcsDataSync/jyssReadyStatus`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 印花税台账提取数据时调用
export const yhsExtractYyzb = (params) => {
  return fetch({
    url: `/yhsDataSync/extractYyzb`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 企业所得税预缴台账提取数据时调用
export const qysdsyjExtract = (params) => {
  return fetch({
    url: `/yjtz/v1/yjtzTqsj`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 企业所得税预缴台账提取数据时调用（获取状态）
export const qysdsyjJyssReadyStatusApi = (params) => {
  return fetch({
    url: `/yjtz/v1/jyssReadyStatus`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 字段名称转换接口
export const companyDifferentiationConfigApi = (params) => {
  return fetch({
    url: `${gyDmgetPrefix}/companyDifferentiationConfig`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
