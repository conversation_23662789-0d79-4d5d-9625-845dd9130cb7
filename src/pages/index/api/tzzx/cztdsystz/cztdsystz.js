import { fetch } from '@/core/request';

const servicePrefix = '/cztdsystz/v1';

export default {
  init(params) {
    return fetch({
      url: `${servicePrefix}/initData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  // 暂未实现
  query(params) {
    return fetch({
      url: `${servicePrefix}/initData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  add(params) {
    return fetch({
      url: `${servicePrefix}/saveData`,
      data: JSON.stringify(params),
      method: 'post',
      loading: true,
    });
  },
  download() {
    return fetch({
      url: `${servicePrefix}/downData`,
      method: 'get',
      loading: true,
    });
  },
};
// 生成税源
export const syCztdsysSy = (params) => {
  return fetch({
    url: `/cztdsytz/v1/sccztdsy`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};
// 查询
export const initCztdsystzQuery = (params) => {
  return fetch({
    url: `/cztdsytz/v1/queryCztdjcxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 保存
export const saveCztdsys = (params) => {
  return fetch({
    url: `/cztdsytz/v1/saveTdjcxx`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 删除
export const deleteSelected = (params) => {
  return fetch({
    url: `/cztdsytz/v1/deleteCztdjcxxByUUid`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// 获取税额标准
export const getSebzByTddj = (params) => {
  return fetch({
    url: `/cztdsys/v1/getZspmAndDwseByTddj?tddjDm=${params.tddjDm}&xzqhszDm=${params.xzqhszDm}&jdxzDm=${params.jdxzDm}&yxqq=${params.yxqq}&yxqz=${params.yxqz}&zgswskfjDm=${params.zgswskfjDm}`,
    data: JSON.stringify(params),
    method: 'get',
    loading: true,
  });
};
