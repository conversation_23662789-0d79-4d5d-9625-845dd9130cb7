import { fetch } from '@/core/request';

const prefix = '/znjsq';
const kjznsbPrefix = '/znjsq';
const kjqysdsbgPrefix = '/znjsq';
const whsyjsfdkdjsbPrefix = '/znjsq';

export default {
  // 获取费用类型
  getZffylxData(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/getZffylxData`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 查询方案
  getSchemes(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/getSchemes`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 更新方案
  updateScheme(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/updateScheme`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 获取问题及答案
  getQuestions(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/getQuestions`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 纳税人义务判断
  judgeDuty(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/judgeDuty`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 保存方案
  saveSchemes(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/saveSchemes`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 删除方案
  delScheme(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/delScheme`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 获取税收协定信息
  getTaxTreaty(data, loading = true) {
    return fetch({
      url: `${prefix}/v1/getTaxTreaty`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 检查是否已经做扣缴登记和文化事业费缴费登记
  verifyBegin(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/verifyBegin`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 自动办理扣缴税款登记
  zdblKjskdjxx(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/zdblKjskdjxx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 文化事业建设费缴费登记
  saveWhsyjsfjfdj(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/saveWhsyjsfjfdj`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    }).then((res) => {
      return res.data;
    });
  },
  // 保存计算结果
  saveJsjgMx(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/saveJsjgMx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 更新计算结果
  updateJsjgMx(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/updateJsjgMx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 删除（作废）计算结果
  zfJsjg(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/zfJsjg`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 查询计算结果
  getJsjgById(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/getJsjgById`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 查询计算结果列表
  listJsjg(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/listJsjg`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 代扣代缴增值税暂存
  zcDkdjzzs(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/zcDkdjzzs`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    }).then((res) => {
      return res.data;
    });
  },
  // 代扣代缴增值税校验
  bbjyDkdjzzs(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/bbjyDkdjzzs`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    }).then((res) => {
      return res.data;
    });
  },
  // 查询文化事业建设费减征比例
  cxWhsyjsfJzbl(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/cxWhsyjsfJzbl`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    }).then((res) => {
      return res.data;
    });
  },
  //  单笔申报提交
  submit(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/submit`,
      method: 'post',
      data,
      loading,
    });
  },
  //  多笔申报提交
  mulSubmit(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/mulSubmit`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 获取申报结果
  getSbResult(data, loading = true) {
    return fetch({
      url: `${kjznsbPrefix}/v1/getSbResult`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    }).then((res) => {
      return res.data;
    });
  },
  // （阻断业务）查询合同信息
  cxHtxx(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/cxHtxx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 查询指定扣缴文书
  cxZdkjwsxx(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/cxZdkjwsxx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 根据识别号查非居民信息(扣缴企业所得税)
  cxFjmsfxx(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/cxFjmsfxx`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 劳务合同情况，直接查核心登记信息
  cxNsrxxByFjmdjxh(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/cxNsrxxByFjmdjxh`,
      method: 'post',
      data,
      loading,
    }).then((res) => {
      return res.data;
    });
  },
  // 获取扣缴企业所得税初始化数据
  getKjqysdsbgInitData(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/initData`,
      method: 'post',
      data,
      loading,
    });
  },
  // 获取扣缴企业所得税要素数据
  getKjqysdsbgYsData(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/ysData`,
      method: 'post',
      data,
      loading,
    });
  },
  // 获取扣缴企业所得税公式
  getKjqysdsbgFormulas(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/getFormulas`,
      method: 'post',
      data,
      loading,
    });
  },
  //  暂存扣缴企业所得税报文
  saveKjqysdsbgForm(data, loading = true) {
    return fetch({
      url: `${kjqysdsbgPrefix}/v1/save`,
      method: 'post',
      data,
      loading,
    });
  },
  // 获取文化事业建设费代扣代缴初始化数据
  getWhsyjsfdkdjsbInitData(data, loading = true) {
    return fetch({
      url: `${whsyjsfdkdjsbPrefix}/v1/initData`,
      method: 'post',
      data,
      loading,
      noMessage: true,
    });
  },
  // 获取文化事业建设费代扣代缴要素数据
  getWhsyjsfdkdjsbYsData(data, loading = true) {
    return fetch({
      url: `${whsyjsfdkdjsbPrefix}/v1/ysData`,
      method: 'post',
      data,
      loading,
    });
  },
  // 获取文化事业建设费代扣代缴公式
  getWhsyjsfdkdjsbFormulas(data, loading = true) {
    return fetch({
      url: `${whsyjsfdkdjsbPrefix}/v1/getFormulas`,
      method: 'post',
      data,
      loading,
    });
  },
  //  暂存文化事业建设费代扣代缴报文
  saveWhsyjsfdkdjsbForm(data, loading = true) {
    return fetch({
      url: `${whsyjsfdkdjsbPrefix}/v1/save`,
      method: 'post',
      data,
      loading,
    });
  },
};
