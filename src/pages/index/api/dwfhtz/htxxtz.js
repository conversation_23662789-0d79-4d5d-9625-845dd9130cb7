/**
 * 合同信息台账相关API接口
 * <AUTHOR>
 * @date 2024-10-01
 */

import request from '@/core/request';

/**
 * 查询合同信息台账列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function queryHtxxtzList(params) {
  return request({
    url: '/api/dwfhtz/htxxtz/list',
    method: 'post',
    data: params,
  });
}

/**
 * 查询合同信息台账合计数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function queryHtxxtzSum(params) {
  return request({
    url: '/api/dwfhtz/htxxtz/sum',
    method: 'post',
    data: params,
  });
}

/**
 * 新增合同信息
 * @param {Object} data 合同数据
 * @returns {Promise}
 */
export function addHtxxtz(data) {
  return request({
    url: '/api/dwfhtz/htxxtz/add',
    method: 'post',
    data,
  });
}

/**
 * 更新合同信息
 * @param {Object} data 合同数据
 * @returns {Promise}
 */
export function updateHtxxtz(data) {
  return request({
    url: '/api/dwfhtz/htxxtz/update',
    method: 'post',
    data,
  });
}

/**
 * 删除合同信息
 * @param {Array} uuids 合同UUID数组
 * @returns {Promise}
 */
export function deleteHtxxtz(uuids) {
  return request({
    url: '/api/dwfhtz/htxxtz/delete',
    method: 'post',
    data: { uuids },
  });
}

/**
 * 导出合同信息台账
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export function exportHtxxtz(params) {
  return request({
    url: '/api/dwfhtz/htxxtz/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

/**
 * 下载导入模板
 * @returns {Promise}
 */
export function downloadHtxxtzTemplate() {
  return request({
    url: '/api/dwfhtz/htxxtz/template',
    method: 'get',
    responseType: 'blob',
  });
}

/**
 * 批量导入合同信息
 * @param {FormData} formData 包含文件的表单数据
 * @returns {Promise}
 */
export function importHtxxtz(formData) {
  return request({
    url: '/api/dwfhtz/htxxtz/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
