/**
 * 合同支付明细相关API
 */
import request from '@/core/request';

// 查询合同支付明细列表
export function queryHtzfmxList(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/list',
    method: 'post',
    data: params,
  });
}

// 查询合同支付明细合计
export function queryHtzfmxSum(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/sum',
    method: 'post',
    data: params,
  });
}

// 新增合同支付明细
export function addHtzfmx(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/add',
    method: 'post',
    data: params,
  });
}

// 更新合同支付明细
export function updateHtzfmx(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/update',
    method: 'post',
    data: params,
  });
}

// 删除合同支付明细
export function deleteHtzfmx(uuids) {
  return request({
    url: '/api/dwfhtz/htzfmx/delete',
    method: 'post',
    data: { uuids },
  });
}

// 导出合同支付明细
export function exportHtzfmx(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 下载合同支付明细模板
export function downloadHtzfmxTemplate() {
  return request({
    url: '/api/dwfhtz/htzfmx/template',
    method: 'get',
    responseType: 'blob',
  });
}

// 导入合同支付明细
export function importHtzfmx(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/api/dwfhtz/htzfmx/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 提取支付明细数据
export function extractHtzfmx(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/extract',
    method: 'post',
    data: params,
  });
}

// 查看支付信息
export function viewPaymentInfo(uuid) {
  return request({
    url: '/api/dwfhtz/htzfmx/paymentInfo',
    method: 'post',
    data: { uuid },
  });
}

// 申报支付明细
export function declarePayment(params) {
  return request({
    url: '/api/dwfhtz/htzfmx/declare',
    method: 'post',
    data: params,
  });
}
