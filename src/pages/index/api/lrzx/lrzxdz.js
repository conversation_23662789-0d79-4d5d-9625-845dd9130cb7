import { fetch } from '@/core/request';

const servicePrefix = '/lrzxdzController';

export const queryByCondition = (params) => {
  return fetch({
    url: `${servicePrefix}/queryByCondition`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

// export const queryDetail = (uuid) => {
//   return fetch({
//     url: `${servicePrefix}/queryDetail?uuid=${uuid}`,
//     method: 'post',
//     loading: true,
//   });
// };

export const insertLrzxdz = (params) => {
  return fetch({
    url: `${servicePrefix}/insertLrzxdz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const deleteLrzxdz = (params) => {
  return fetch({
    url: `${servicePrefix}/deleteLrzxdz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const updateLrzxdz = (params) => {
  return fetch({
    url: `${servicePrefix}/updateLrzxdz`,
    data: JSON.stringify(params),
    method: 'post',
    loading: true,
  });
};

export const getCompanyInfo = (nsrsbh) => {
  return fetch({
    url: `${servicePrefix}/getCompanyInfo?nsrsbh=${nsrsbh}`,
    method: 'post',
    loading: true,
  });
};

export const getQsZjgInfoByGsh = (gsh) => {
  return fetch({
    url: `${servicePrefix}/getQsZjgInfoByGsh?gsh=${gsh}`,
    method: 'post',
    loading: true,
  });
};
