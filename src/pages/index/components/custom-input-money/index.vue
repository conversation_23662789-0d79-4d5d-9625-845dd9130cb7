<template>
  <t-input
    :id="id || `${name}Id`"
    ref="inputRef"
    :class="['gov-input-money', $attrs.class, errorMsg && validateStyle ? ' error-input-money' : ''].filter(Boolean)"
    default-value=""
    :value="inputValue"
    v-bind="$attrs"
    type="text"
    :name="name"
    :placeholder="placeholder"
    :style="stylesheet"
    :disabled="disabled"
    :size="size"
    :align="align"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    v-on="omittedListeners"
  >
    <template v-if="customErrorMsg || onlyShowErrorIcon" #prefixIcon>
      <error-circle-filled-icon v-if="onlyShowErrorIcon" :style="{ color: prefixIconColor }" />
      <t-tooltip :content="customErrorMsg" v-else-if="customErrorMsg">
        <error-circle-filled-icon :style="{ color: prefixIconColor, cursor: 'pointer' }" />
      </t-tooltip>
    </template>
  </t-input>
</template>

<script>
import { omit } from 'lodash-es';
import { ErrorCircleFilledIcon } from 'tdesign-icons-vue';

import { validValueRes } from '@gt4/common-front/dist/toolkit';

/**
 * 金额输入框
 * @category Form
 * @cover InputMoney
 */
export default {
  name: 'gt-input-money',
  components: {
    ErrorCircleFilledIcon,
  },
  model: {
    event: 'change',
  },
  props: {
    customErrorMsg: {
      type: String,
      default: '',
    },
    prefixIconColor: {
      type: String,
      default: 'red',
    },
    onlyShowErrorIcon: {
      type: Boolean,
      default: false,
    },

    /**
     * 文本位置
     */
    align: {
      type: String,
      default: 'right',
    },
    /**
     * 非负数校验的定制提示语
     */
    nonnegativeErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 非负整数校验的定制提示语
     */
    nonnegativeIntErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 非正数校验的定制提示语
     */
    nonpositiveErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 非正整数校验的定制提示语
     */
    nonpositiveIntErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 数字整数位校验的定制提示语
     */
    integerNumberErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 	请输入不小于 xx 的数！、 请输入不大于 xx 的数！、当前单元格的输入范围为[xx-xx]！
     *  数字填写范围校验的定制提示语
     */
    limitErrorMsg: {
      type: String,
      default: '',
    },
    /**
     * 	输入框的类型
     */
    type: {
      type: String,
      default: 'number',
    },
    /**
     * 最大值
     */
    maxValue: {
      type: [String, Number],
      default: '',
    },
    /**
     * 最小值
     */
    minValue: {
      type: [String, Number],
      default: '',
    },
    /**
     * 值
     */
    value: {
      type: [String, Number],
      default: '',
    },
    /**
     * 允许赋空值
     */
    allowNullish: {
      type: Boolean,
      default: false,
    },
    /**
     * 名称
     */
    name: {
      type: String,
      default: '',
    },
    /**
     * id
     */
    id: {
      type: String,
      default: '',
    },
    /**
     * 提示语
     */
    placeholder: {
      type: String,
      default: '',
    },
    /**
     * 千分位
     */
    isSeparator: {
      type: Boolean,
      default: true,
    },
    /**
     * 禁用
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 自动选中
     */
    autoSelect: {
      type: Boolean,
      default: true,
    },
    /**
     * 保留小数位，默认为2
     */
    digit: {
      type: Number,
      default: 2,
    },
    /**
     * 样式
     */
    stylesheet: {
      type: Object,
      default: () => ({}),
    },
    /**
     * 按钮尺寸
     * @values small, medium, large
     */
    size: {
      type: String,
      default: 'medium',
    },
    /**
     * input 改变
     */
    onChange: {
      type: Function,
      default: () => {},
    },
    /**
     * 聚焦事件
     */
    onFocus: {
      type: Function,
      default: () => {},
    },
    /**
     * 失去焦点事件
     */
    onBlur: {
      type: Function,
      default: () => {},
    },
    /**
     * 设置验证状态
     */
    setValid: {
      type: Function,
      default: () => {},
    },
    /**
     * pre format
     */
    preFormat: {
      type: Function,
      default: (x) => x,
    },
    /**
     * 是否需要type对应的默认校验
     */
    validate: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否需要展示type对应的默认校验样式
     */
    validateStyle: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否直接输入百分数，不需要除100转换成小数
     */
    valuePercentage: {
      type: Boolean,
      default: false,
    },
    /**
     * 支持输入的整数位长度，默认14位
     */
    integerNumber: {
      type: Number,
      default: 14,
    },
  },
  data() {
    return {
      inputValue: '', // 展示给用户的值 比如： 设置了百分比，100%
      realValue: this.value, // 真实的值 比如：设置了百分比，真实值是1
      errorMsg: '',
      // eslint-disable-next-line no-nested-ternary
      append: this.type === 'percent' ? '%' : this.type === 'milli' ? '‰' : '',
    };
  },
  computed: {
    // 过滤掉部分已在组件接管事件否则触发两次
    omittedListeners() {
      return omit(this.$listeners, ['change', 'focus', 'blur']);
    },
  },
  watch: {
    value(value) {
      // 0不等于空
      if (((this.allowNullish && !value) || Number(value) !== Number(this.realValue)) && value !== this.realValue) {
        // 数据格式化和校验
        const { errorMsg, showValue, value: relValue } = validValueRes(value, { ...this.$props });

        // 校验提示
        if (errorMsg) {
          this.setValid(true);
        }

        if (this.inputValue !== showValue || this.realValue !== relValue) {
          this.inputValue = showValue;
          this.realValue = relValue;
          // 如果传进来的值和格式化后的值不一致还需要触发一次change，否则v-model绑定拿不到格式化后的值
          this.$emit('change', this.realValue, { e: null, validRes: { errorMsg } });
          this.onChange(this.realValue, { e: null, validRes: { errorMsg } });
        }
      }
    },
  },
  mounted() {
    const validResult = validValueRes(this.value, { ...this.$props });
    if (this.inputValue !== validResult.showValue || this.realValue !== validResult.value) {
      this.inputValue = validResult.showValue;

      this.realValue = validResult.value;
      // 组件挂载后如果传进来的值和格式化后的值不一致还需要触发一次change，否则v-model绑定拿不到格式化后的值
      this.$emit('change', this.realValue, { e: null, validRes: { errorMsg: validResult.errorMsg } });
      this.onChange(this.realValue, { e: null, validRes: { errorMsg: validResult.errorMsg } });
    }
  },
  methods: {
    handleChange(value, { e }) {
      this.realValue = value;
      this.inputValue = value;
      this.$emit('change', value, { e });
      this.onChange(value, { e });
    },
    handleFocus(value, { e }) {
      this.inputValue = this.realValue ?? '';
      this.$nextTick(() => {
        this.autoSelect && e.target?.select();
        this.$emit('focus', value, { e });
        this.onFocus(value, { e });
      });
    },
    handleBlur(value, { e }) {
      this.handleMoneyBlur(value, e);
    },
    handleMoneyBlur(val, e) {
      // 数据格式化和校验
      const { errorMsg, showValue, value: relValue } = validValueRes(val, { ...this.$props });

      this.errorMsg = errorMsg;

      // 校验提示
      if (errorMsg) {
        this.setValid(true);
      }

      if (this.inputValue !== showValue || this.realValue !== relValue) {
        this.inputValue = showValue;
        this.realValue = relValue;
      }
      // 失去焦点之后还需要触发一次change，否则v-model绑定拿不到格式化后的值
      this.$emit('change', this.realValue, { e, validRes: { errorMsg } });
      this.onChange(this.realValue, { e, validRes: { errorMsg } });
      this.$emit('blur', this.realValue, { e, validRes: { errorMsg } });
      this.onBlur(this.realValue, { e, validRes: { errorMsg } });
    },
  },
};
</script>

<style lang="less" scoped>
.error-input-money {
  border: 1px solid red;

  .t-input__prefix,
  input {
    color: red;
  }
}
</style>
