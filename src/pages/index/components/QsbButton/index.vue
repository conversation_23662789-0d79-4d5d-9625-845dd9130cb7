<template>
  <div>
    <t-button v-show="hiddenFlag" variant="outline" theme="primary" @click="qsb"
      ><LinkIcon slot="icon" />去申报</t-button
    >
  </div>
</template>

<script>
import { LinkIcon } from 'tdesign-icons-vue';

export default {
  props: {},
  data() {
    return {
      hiddenFlag: true,
    };
  },
  components: {
    LinkIcon,
  },
  created() {
    // const URL = window.location.pathname;
    // if (URL.indexOf('/znsb/view/tzzx/zzstz') > -1) {
    //   this.hiddenFlag = false;
    // }
    this.hiddenFlag = false;
  },
  methods: {
    qsb() {
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/nssb/sbrwmx`;
        window.parent.goSelfChange(menuParams);
      }
    },
  },
};
</script>
<style lang="less" scoped></style>
