<template>
  <div>
    <!-- 条件 -->
    <div class="tz-condition-wrap">
      <Condition
        :condition="{ type: 'default' }"
        :conditionData="conditionData"
        ref="myCondition"
        @conditionChange="conditionChange"
      />
      <Operation
        :operation="topRightBtn"
        :delformData="delformData"
        :data="data"
        :conditionData="conditionData"
        @clearDelFormData="clearDelFormData"
        @changeData="changeData"
        @init="init"
      />
    </div>
    <!-- 表格 -->
    <div class="tz-table-wrap">
      <div class="tz-btn-wrap">
        <t-space>
          <t-button theme="primary" @click="handleAddRow">增行</t-button>
          <t-popconfirm
            :visible="popconfirmVisible"
            theme="default"
            content="是否删除"
            @visible-change="onPopconfirmVisibleChange"
          >
            <t-button theme="primary" variant="outline">删除</t-button>
          </t-popconfirm>
        </t-space>
      </div>
      <t-table
        :pagination="_pagination"
        :rowKey="rowkey"
        :data="dataIdx"
        :columns="customTableHead"
        :conditionData="conditionData"
        :loading="loading"
        :bordered="true"
        :hover="true"
        :editableRowKeys="editableRowKeys"
        :selected-row-keys="selectedRowKeys"
        @select-change="rehandleSelectChange"
      >
        <template #operation="{ row }">
          <t-link theme="primary" hover="color" @click="edit(row)"> 编辑 </t-link>
        </template>
      </t-table>
      <add-row-dialog
        :dialogMode="dialogMode"
        :djxh="djxh"
        :conditionData="conditionData"
        :dialogParams="dialogParams"
        :visible="addRowVisible"
        dialogTitle="录入信息"
        :editConfig="editConfig"
        @changeVisible="changeVisible"
        @addRow="addRow"
      />
    </div>
  </div>
</template>

<script>
import api from '@/pages/index/api/demo/commonTzxxDemo.js';
import { MessagePlugin } from 'tdesign-vue';
import TableEditmixin from './mixins/tableEdit';
import Condition from './condition';
import Operation from './operation';
import AddRowDialog from './addRowDialog';

export default {
  name: 'index',
  components: {
    Condition,
    Operation,
    AddRowDialog,
  },
  mixins: [TableEditmixin],
  props: {
    djxh: {
      type: String,
      default: '1',
    },
    pagination: {
      type: Object,
      default: () => {},
    },
    config: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 初始化获取数据的方法url
      initUrl: '',
      addRowVisible: false,
      editConfig: [],
      rowkey: 'idx',
      popconfirmVisible: false,
      componentsArray: [],
      loading: false,
      colkeyList: [],
    };
  },
  computed: {
    initfn() {
      if (this.config.pageMain) {
        if (this.config.pageMain.initfn) {
          return this.config.pageMain.initfn;
        }
      }
      return {};
    },
    topRightBtn() {
      if (this.config.pageMain) {
        if (this.config.pageMain.topRightBtn) {
          return this.config.pageMain.topRightBtn;
        }
      }
      return [];
    },
    dataIdx() {
      return this.dataAddIdx(this.data);
    },
    customTableHead() {
      if (this.config.pageMain) {
        if (this.config.pageMain.mainGrid) {
          const { mainGrid } = this.config.pageMain;
          mainGrid.map((item) => {
            if (item.cell) {
              item.cell = (h, { row }) => {
                console.log(row, item.editConfig.options, item.colKey);
                return this.onCell(row, item.editConfig.options, item.colKey);
              };
            }
          });
          return this.config.pageMain.mainGrid;
        }
      }
      return [];
    },
    editableRowKeys() {
      return this.data.map((item) => item[this.rowkey]);
    },
    _pagination() {
      return this.data.length ? this.pagination : null;
    },
    sszq() {
      if (this.conditionData && this.conditionData.sszq) {
        console.log('this.conditionData.sszq', this.conditionData.sszq);
        return this.conditionData.sszq.substring(0, 4) + this.conditionData.sszq.substring(5, 7);
      }
      return '';
    },
  },
  watch: {
    customTableHead: {
      handler(val) {
        const newVal = JSON.parse(JSON.stringify(val));
        this.editConfig = [];
        // 构建新增dialog配置
        this.buildEditConfig(newVal);
      },
      deep: true,
    },
    'initfn.url': {
      handler(url) {
        this.initUrl = url;
        this.init();
      },
    },
  },
  methods: {
    async init() {
      console.log('查询数据', this.initUrl);
      const { code, data, msg } = await api.commonTzInitData(this.initUrl, {
        djxh: this.djxh,
        pageSize: 10,
        pageNum: 1,
        // ...this.conditionData,
        sszq: this.sszq,
      });
      if (code === 1) {
        this.data = data.data.records || [];
      } else {
        MessagePlugin.error(msg);
      }
    },
    edit(row) {
      this.addRowVisible = true;
      this.dialogParams = { ...row };
      this.dialogMode = 'edit';
    },

    // 删除确认
    onPopconfirmVisibleChange(val, context = {}) {
      // trigger 表示触发来源，可以根据触发来源自由控制 visible
      if (context && context.trigger === 'confirm') {
        this.handleDelSelectRows();
        this.popconfirmVisible = false;
      } else {
        this.popconfirmVisible = val;
      }
    },

    // 新增一行
    handleAddRow() {
      this.dialogMode = 'add';
      this.addRowVisible = true;
    },
    addRow(rowData) {
      if (this.dialogMode === 'add') {
        this.data.push(rowData);
      } else {
        const findIndex = this.data.findIndex((t) => t.idx === rowData.idx);
        this.data.splice(findIndex, 1, rowData);
      }
    },
    // 删除几行
    handleDelSelectRows() {
      this.selectedRowKeys.forEach((key) => {
        const findIndex = this.data.findIndex((t) => t[this.rowkey] === key);
        this.delformData.push(this.data[findIndex]);
        this.data.splice(findIndex, 1);
      });
      this.selectedRowKeys = [];
    },
  },
};
</script>

<style lang="less" scoped>
.tz-condition-wrap {
  position: relative;
  padding: 20px 20px 0;
}
.tz-table-wrap {
  padding: 0 20px 20px;
  background: #fff;
}
.tz-btn-wrap {
  margin-bottom: 20px;
}
</style>
