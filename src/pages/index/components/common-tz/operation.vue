<template>
  <div class="tz-operation-wrap">
    <!-- <t-space v-if="operation.type === 'default'">
      <t-button theme="primary" @click="handleExtract">提取数据</t-button>
      <t-button theme="primary" @click="handleSave">保存台账</t-button>
      <t-button theme="primary" @click="handleTrial">我要试算</t-button>
    </t-space> -->
    <t-space>
      <t-button
        v-for="(item, index) in operation"
        :key="index"
        theme="primary"
        @click="handleCb(item.btnType, item.url, item.params)"
      >
        {{ item.label }}
      </t-button>
    </t-space>
  </div>
</template>

<script>
import api from '@/pages/index/api/demo/commonTzxxDemo.js';

export default {
  name: 'operation',
  props: {
    operation: {
      type: Array,
      default() {
        return [];
      },
    },
    conditionData: {
      type: Object,
      default() {
        return {};
      },
    },
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    delformData: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.operation.forEach((item) => {
      if (item.btnType === 'tqsjBtn') {
        this.initUrl = item.url;
        this.initParams = item.params;
      }
    });
  },
  methods: {
    handleCb(btnType, url, params) {
      console.log(btnType, url, params);
      switch (btnType) {
        case 'tqsjBtn':
          this.handleExtract();
          break;
        case 'bctzBtn':
          this.handleSave(url, params);
          break;
        case 'wyssBtn':
          this.handleTrial(url);
          break;
        default:
          break;
      }
    },
    async handleExtract() {
      // const sszq = this.conditionData.sszq.substring(0, 4) + this.conditionData.sszq.substring(5, 7);
      // console.log(sszq, 'sszq');
      // const param = {};
      // param.djxh = '10111525000001030001';
      // param.sszq = sszq;
      // param.pageSize = this.pagination.defaultPageSize;
      // param.pageNum = this.pagination.defaultCurrent;
      // const { code, msg, data } = await fetch({
      //   url: this.initUrl,
      //   data: JSON.stringify(param),
      //   method: 'post',
      //   loading: true,
      // });
      // const { returnCode } = data.returnCode;
      // const { returnMsg } = data.returnMsg;
      // if (code === 1) {
      //   console.log(data.data.records, 'data.data.records');
      //   this.$emit('changeData', data.data.records || []);
      // } else if (msg) {
      //   console.log(msg);
      // } else {
      //   console.log(returnMsg);
      //   this.$message.warning(returnMsg);
      // }
    },
    async handleSave(url) {
      const param = [...this.data];
      this.delformData.forEach((item) => {
        param.push({
          uuid: item.uuid || '111',
          djxh: item.djxh || '111',
          sszq: item.sszq || '202404',
          scbz: '1',
        });
      });
      // console.log('param', param);

      const { code, data, msg } = await api.commonTzInitData(url, param);
      if (code === 1) {
        console.log(msg);
        this.$message.success(msg);
      } else if (msg) {
        this.$message.warning(msg);
      }
      // const { msg, data } = await fetch({
      //   url,
      //   data: JSON.stringify(param),
      //   method: 'post',
      //   loading: true,
      // });
      // const { msg, data } = await api.add(params);
      // const { code } = data;
      // const { returnMsg } = data;
      // console.log('data', data);
      // console.log(code);
      // // console.log('returnCode', returnCode);
      // if (code === 1) {
      //   console.log(msg);
      // } else if (msg) {
      //   console.log(msg);
      // } else {
      //   console.log(returnMsg);
      //   this.$message.warning(returnMsg);
      // }
      // this.init();
      this.$emit('clearDelFormData');
      this.$emit('init');
    },
    handleTrial(url) {
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = url;
        window.parent.goSelfChange(menuParams);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.tz-operation-wrap {
  position: absolute;
  top: 20px;
  right: 21px;
}
</style>
