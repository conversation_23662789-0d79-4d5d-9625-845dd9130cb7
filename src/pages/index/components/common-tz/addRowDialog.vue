<template>
  <div>
    <t-dialog :visible="visible" :confirm-btn="null" :cancel-btn="null" :close-btn="false" width="920">
      <div slot="header" class="tableH">
        <div class="title">
          {{ dialogTitle }}
          <t-icon class="closeBtn" name="close" @click="cancel" />
        </div>
      </div>
      <div class="con">
        <t-row :gutter="16" class="row">
          <t-col :span="4" v-for="item in editConfig" :key="item.colKey">
            <div class="form_item frist_cow_item">
              <span class="formtitle">{{ item.title }}</span>
              <t-select v-if="item.editConfig.component === 't-select'" v-model="dialogFormData[item.colKey]" clearable>
                <t-option
                  v-for="option in item.editConfig.options"
                  :value="option.value"
                  :label="option.label"
                  :key="option.value"
                ></t-option>
              </t-select>
              <t-input-number
                v-if="item.editConfig.component === 't-input-number'"
                v-model="dialogFormData[item.colKey]"
                type="number"
                theme="normal"
                style="width: 280px; height: 32px"
                clearable
              />
              <t-input
                v-if="item.editConfig.component === 't-input'"
                v-model="dialogFormData[item.colKey]"
                theme="normal"
                style="width: 280px; height: 32px"
                clearable
              />
              <t-date-picker
                style="width: 280px; height: 32px"
                v-model="dialogFormData[item.colKey]"
                v-if="item.editConfig.component === 't-date-picker'"
              />
            </div>
          </t-col>
        </t-row>
      </div>
      <div class="btnsleft">
        <t-button class="btn btn1" theme="default" variant="base" @click="cancel">取消</t-button>
        <t-button class="btn" @click="addRow">确定</t-button>
      </div>
    </t-dialog>
  </div>
</template>

<script>
export default {
  name: 'addRowDialog',
  props: {
    djxh: {
      type: String,
      default: '1',
    },
    dialogTitle: {
      type: String,
      dafault: '录入',
    },
    editConfig: {
      type: Array,
      dafault() {
        return [];
      },
    },
    visible: {
      type: Boolean,
      dafault: false,
    },
    dialogMode: {
      type: String,
      dafault: 'add',
    },
    dialogParams: {
      type: Object,
      dafault() {
        return {};
      },
    },
    conditionData: {
      type: Object,
      dafault() {
        return {};
      },
    },
  },
  data() {
    return {
      dialogFormData: {},
    };
  },
  computed: {
    sszq() {
      if (this.conditionData) {
        if (this.conditionData.sszq) {
          return this.conditionData.sszq.substring(0, 4) + this.conditionData.sszq.substring(5, 7);
        }
      }

      return '';
    },
  },
  watch: {
    visible(newVisible) {
      if (newVisible && this.dialogMode === 'edit') {
        this.dialogFormData = this.dialogParams;
      } else {
        this.dialogFormData = {
          djxh: this.djxh,
          // sszq: this.conditionData.sszq.substring(0, 4) + this.conditionDatasszq.substring(5, 7),
          sszq: this.sszq,
        };
      }
    },
  },
  methods: {
    // 取消
    cancel() {
      this.$emit('changeVisible', false);
    },
    // 确定
    addRow() {
      this.$emit('changeVisible', false);
      this.$emit('addRow', this.dialogFormData);
    },
  },
};
</script>

<style scoped lang="less">
.dialog .t-dialog__ctx .tdgv-wrapper .t-dialog .t-dialog__body {
  padding: 0;
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog {
      padding: 0;
      border-radius: 2px;
    }
  }
}
.tdgv-wrapper {
  .t-dialog__ctx {
    /deep/.t-dialog__footer {
      padding: 0;
    }
  }
}
.tdgv-wrapper {
  .t-collapse-panel {
    /deep/.t-collapse-panel__header {
      .t-collapse-panel__header-content {
        font-size: 20px;
        font-weight: 400;
        color: #333;
      }
    }
  }
}
.container {
  width: 100%;
  padding: 16px 24px;
  .header {
    div {
      display: inline-block;
      vertical-align: middle;
    }
    .sszq {
      padding-right: 8px;
    }
    .btns {
      float: right;
      .btn {
        margin-left: 12px;
      }
    }
  }
  .tableBtn {
    margin: 20px 0;
    .btn {
      margin-right: 12px;
    }
  }
  .pag {
    margin: 20px 0;
  }
  .edit {
    color: #4285f4;
    cursor: pointer;
  }
}
.tableH {
  width: 100%;
}
.title {
  position: relative;
  width: 100%;
  padding: 15px;
  font-family: PingFangSC-SNaNpxibold;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0;
  color: #000;
  border-bottom: 1px solid rgb(233, 231, 231);
  .closeBtn {
    position: absolute;
    top: 24px;
    right: 24px;
    display: inline-block;
    width: 16px;
    height: 16px;
    color: rgba(0, 0, 0, 0.4);
    border-radius: 8px;
  }
  .closeBtn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.btnsleft {
  display: flex;
  height: 44px;
  padding-top: 12px;
  padding-right: 24px;
  margin-top: 24px;
  border-top: 1px solid rgb(233, 231, 231);
  justify-content: flex-end;
  .btn {
    width: 60px;
    height: 32px;
    padding: 0;
    line-height: 32px;
  }
  .btn1 {
    margin-right: 12px;
  }
}
.con {
  padding-right: 20px;
  padding-left: 20px;
}
.row {
  margin-bottom: 30px;
}
.search-line {
  display: block;
}
.specText {
  cursor: pointer;
}
.formtitle {
  font-size: 14px;
}
.form_item {
  width: 280px;
  height: 58px;
  margin-top: 24px;
}
.frist_cow_item {
  margin-top: 0;
}
</style>
