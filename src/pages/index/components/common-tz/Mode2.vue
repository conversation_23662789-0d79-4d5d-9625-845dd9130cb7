<template>
  <div>
    <!-- 条件 -->
    <div class="tz-condition-wrap">
      <Condition :condition="{ type: 'default' }" @conditionChange="conditionChange" />
      <Operation
        :operation="config.pageMain.topRightBtn"
        :delformData="delformData"
        :data="data"
        :conditionData="conditionData"
        @clearDelFormData="clearDelFormData"
        @changeData="changeData"
      />
    </div>
    <!-- 表格 -->
    <div class="tz-table-wrap">
      <t-tabs v-model="value">
        <!-- 默认插槽 和 具名插槽（panel）都是用来渲染面板内容 -->
        <t-tab-panel value="first" label="销项发票明细" :destroyOnHide="false">
          <div style="width: calc(100vw - 220px); overflow: auto">
            <t-table
              ref="tableRef"
              :key="id"
              row-key="key"
              width="100%"
              :data="xxfpmxList"
              :columns="xxfpmxColumns"
              :selected-row-keys="selectedRowKeys"
              @select-change="rehandleSelectChange"
              bordered
              lazyLoad
              :hover="hover"
              :pagination="pagination"
            >
              <template #fphm="{ row }">
                <div class="specText" style="float: center">
                  <span @click="showPdf" style="color: #0052d9; text-decoration: underline">{{ row.fphm }}</span>
                  <t-image-viewer v-model="visible" :images="[picSrc]"> </t-image-viewer>
                </div>
              </template>
              <template #je="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.je }}</span>
                </div>
              </template>
              <template #se="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.se }}</span>
                </div>
              </template>
              <template #jshj="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.jshj }}</span>
                </div>
              </template>
            </t-table>
          </div>
        </t-tab-panel>
        <t-tab-panel value="second" label="货物服务明细" :destroyOnHide="false">
          <div style="width: calc(100vw - 220px); overflow: auto">
            <t-table
              ref="tableRef"
              :key="id"
              row-key="key"
              width="100%"
              :data="hwfpmxList"
              :columns="hwfpmxColumns"
              :selected-row-keys="selectedRowKeys"
              :editable-row-keys="editableRowKeys"
              @select-change="rehandleSelectChange"
              @row-edit="onRowEdit"
              bordered
              lazyLoad
              :hover="hover"
              :pagination="hwpagination"
            >
              <template #fphm="{ row }">
                <div class="specText" style="float: center">
                  <span @click="showPdf" style="color: #0052d9; text-decoration: underline">{{ row.fphm }}</span>
                  <t-image-viewer v-model="visible" :images="[picSrc]"> </t-image-viewer>
                </div>
              </template>
              <template #dj="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.dj }}</span>
                </div>
              </template>
              <template #je="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.je }}</span>
                </div>
              </template>
              <template #se="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.se }}</span>
                </div>
              </template>
              <template #jshj="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.jshj }}</span>
                </div>
              </template>
              <template #kce="{ row }">
                <div class="specText" style="float: right">
                  <span>{{ row.kce }}</span>
                </div>
              </template>
            </t-table>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import Condition from './condition';
import Operation from './operation';

const STATUS_OPTIONS = [
  { label: '审批通过', value: 0 },
  { label: '审批过期', value: 1 },
  { label: '审批失败', value: 2 },
];
export default {
  name: 'index',
  components: {
    Condition,
    Operation,
  },
  props: {
    pagination: {
      type: Object,
      default: () => {},
    },
    data: {
      type: Array,
      default: () => {
        return [
          {
            zsfs: '征收方式1',
            zsfs2: 0,
            zsfs3: '20214-05-22',
            aa: '申请状态11',
            dd: '申请状态21',
          },
          {
            zsfs: '征收方式2',
            zsfs2: 1,
            zsfs3: '20214-05-23',
            aa: '申请状态12',
            dd: '申请状态22',
          },
        ];
      },
    },
    bordered: {
      type: Boolean,
      default: true,
    },
    hover: {
      type: Boolean,
      default: true,
    },
    lazyLoad: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'medium',
    },
    maxHeight: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      popconfirmVisible: false,
      selectedRowKeys: [],
      componentsArray: [],
      loading: false,
      conditionData: {},
      colkeyList: [],
      allData: {
        condition: {
          type: 'default',
        },
        operation: {
          type: 'default',
        },
        tableHead: [
          {
            colKey: 'row-select',
            type: 'multiple',
            width: 50,
          },
          {
            title: '序号',
            colKey: 'idx',
            width: 50,
          },
          {
            title: '计税方式',
            colKey: 'zsfs',
            isRowKey: true,
            width: 100,
            edit: {
              component: 't-input',
              keepEditMode: true,
              defaultEditable: true,
            },
          },
          {
            title: '征税方式2',
            colKey: 'zsfs2',
            width: 100,
            cell: true,
            options: STATUS_OPTIONS,
            edit: {
              keepEditMode: true,
              component: 't-select',
            },
          },
          {
            title: '征税方式3',
            colKey: 'zsfs3',
            width: 100,
            edit: {
              component: 't-date-picker',
            },
          },
          {
            title: '开具税控增值税发票',
            width: 200,
            colKey: 'kjskzzsfp',
            align: 'center',
            children: [
              {
                colKey: 'aa',
                title: '申请状态1',
                width: 100,
                edit: {
                  component: 't-input',
                },
              },
              {
                colKey: 'dd',
                title: '申请状态2',
                width: 100,
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    dataIdx() {
      return this.data.map((item, index) => {
        item.idx = index + 1;
        return item;
      });
    },
    rowkey() {
      this.allData.tableHead.forEach((item) => {
        if (item.isRowKey) {
          return item.colKey;
        }
      });
      return 'zsfs';
    },
    customTableHead() {
      this.componentsArray = [];
      const customTableHead = this.allData.tableHead.map((item) => {
        if (item.children) {
          item.children = item.children.map((childItem) => {
            return this.buildEditProp(childItem);
          });
          return item;
        }
        return this.buildEditProp(item);
      });
      return customTableHead;
    },
    editableRowKeys() {
      const editableRowKeys = this.data.map((item) => item[this.rowkey]);
      return editableRowKeys;
    },
    _pagination() {
      return this.data.length ? this.pagination : null;
    },
  },
  mounted() {
    if (this.customTableHead.length > 0) {
      this.loading = false;
    }
    console.log('this.customTableHead', this.customTableHead);
    this.componentsArray.forEach((_, index) => {
      Vue.component(_);
    });
  },
  methods: {
    onPopconfirmVisibleChange(val, context = {}) {
      // trigger 表示触发来源，可以根据触发来源自由控制 visible
      if (context && context.trigger === 'confirm') {
        this.handleDelSelectRows();
        this.popconfirmVisible = false;
      } else {
        this.popconfirmVisible = val;
      }
    },
    rehandleSelectChange(value, { selectedRowData }) {
      this.selectedRowKeys = value;
    },
    buildEditProp(item) {
      let componentNameEs;
      if (item.edit) {
        if (item.edit.component === 't-select') {
          if (item.options) {
            if (item.cell) {
              item.cell = (h, { row }) => {
                return this.onCell(row, item.options, item.colKey);
              };
            }
            item.edit.props = {
              options: item.options,
              clearable: true,
              autofocus: true,
            };
          }
        }
        componentNameEs = item.edit.component.slice(2);
        import(`tdesign-vue/es/${componentNameEs}`);
        item.edit.onEdited = this.onEdited;
        item.edit.keepEditMode = true;
        item.edit.showEditIcon = false;
        this.componentsArray.push(item.edit.component);
      }
      return item;
    },
    onEdited(context) {
      this.data.splice(context.rowIndex, 1, context.newRowData);
    },
    onCell(row, options, colKey) {
      return options.find((t) => t.value === row[colKey])?.label;
    },
    handleAddRow() {
      const row = this.createRow();
      this.data.push(row);
    },
    getColkeyList(column) {
      const colkeyList = column.map((t, idx) => {
        if (t.children) {
          this.getColkeyList(t.children);
        } else {
          return t.colKey;
        }
      });

      this.colkeyList.push(...colkeyList);
    },
    createRow() {
      this.getColkeyList(this.allData.tableHead);
      const row = {};
      this.colkeyList.forEach((colKey) => {
        if (colKey) {
          row[colKey] = '';
        }
      });
      return row;
    },
    handleDelSelectRows() {
      this.selectedRowKeys.forEach((key) => {
        const findIndex = this.data.findIndex((t) => t[this.rowkey] === key);
        this.data.splice(findIndex, 1);
      });
      this.selectedRowKeys = [];
    },
    // 监听条件变化
    conditionChange(condition) {
      this.conditionData = { ...this.conditionData, ...condition };
    },
  },
};
</script>

<style lang="less" scoped>
.tz-condition-wrap {
  position: relative;
  padding: 20px 20px 0;
}
.tz-table-wrap {
  padding: 0 20px 20px;
  background: #fff;
}
.tz-btn-wrap {
  margin-bottom: 20px;
}
</style>
