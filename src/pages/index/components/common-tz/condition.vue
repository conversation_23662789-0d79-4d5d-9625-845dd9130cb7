<template>
  <div>
    <div>
      <t-form ref="form" reset-type="initial" :data="conditionData" :colon="true" label-align="left">
        <t-row class="row-space" v-if="condition.type === 'default'">
          <t-col :span="3">
            <t-form-item label="所属账期" name="sszq">
              <t-date-picker
                v-model="conditionFormData.sszq"
                able-time-picker
                allow-input
                clearable
                :style="{ width: '100%' }"
                mode="month"
              />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'condition',
  props: {
    condition: {
      type: Object,
      default() {
        return {};
      },
    },
    conditionData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      conditionFormData: {},
    };
  },
  watch: {
    conditionData: {
      handler(val) {
        this.conditionFormData = val;
      },
      deep: true,
      immediate: true,
    },
    conditionFormData: {
      handler(val) {
        this.$emit('conditionChange', val);
      },
      deep: true,
    },
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
