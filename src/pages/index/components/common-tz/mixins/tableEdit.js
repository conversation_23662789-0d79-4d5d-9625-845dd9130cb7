// import Vue from 'vue';
import dayjs from 'dayjs';

export default {
  data() {
    return {
      // 删除掉的数据
      delformData: [],
      // 打开弹窗的情况  新增还是编辑 add edit
      dialogMode: 'add',
      // 弹窗参数
      dialogParams: {},
      // 页面表格数据
      data: [],
      // 条件列表
      conditionData: {
        sszq: dayjs().format('YYYY-MM-DD'),
      },
      // 多选
      selectedRowKeys: [],
    };
  },
  methods: {
    // 编辑新增弹框是否展示
    changeVisible(visible) {
      this.addRowVisible = visible;
    },
    // 清空已删除的数据
    clearDelFormData() {
      this.delformData = [];
    },
    // 点击操作按钮后根据接口返回值改变表格数据
    changeData(data) {
      this.data = data;
    },
    // 多选
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    // 给返回的data数组每一项加序号
    dataAddIdx(data) {
      return data.map((item, index) => {
        item.idx = index + 1;
        return item;
      });
    },
    // 带children 的复杂表头取出每项的editConfig构建新增弹窗
    buildEditConfig(column) {
      column.map((item) => {
        if (item.children) {
          this.buildEditConfig(item.children);
        } else if (item.editConfig) {
          this.editConfig.push(item);
        }
      });
      return this.editConfig;
    },
    // buildEditProp(item) {
    //   let componentNameEs;
    //   if (item.edit) {
    //     if (item.edit.component === 't-select') {
    //       if (item.options) {
    //         if (item.cell) {
    //           item.cell = (h, { row }) => {
    //             return this.onCell(row, item.options, item.colKey);
    //           };
    //         }
    //         item.edit.props = {
    //           options: item.options,
    //           clearable: true,
    //           autofocus: true,
    //         };
    //       }
    //     }
    //     componentNameEs = item.edit.component.slice(2);
    //     import(`tdesign-vue/es/${componentNameEs}`);
    //     item.edit.onEdited = this.onEdited;
    //     item.edit.keepEditMode = true;
    //     item.edit.showEditIcon = false;
    //     this.componentsArray.push(item.edit.component);
    //   }
    //   return item;
    // },
    onCell(row, options, colKey) {
      return options.find((t) => t.value === row[colKey])?.label;
    },

    onEdited(context) {
      this.data.splice(context.rowIndex, 1, context.newRowData);
    },
    // 监听条件变化
    conditionChange(condition) {
      this.conditionData = { ...this.conditionData, ...condition };
    },
  },
};
