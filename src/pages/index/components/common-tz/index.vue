<template>
  <div>
    <Mode1 v-if="pageModelType === 'ONE'" :config="jsonData" :djxh="djxh" />
    <Mode2 v-else-if="pageModelType === 'TWO'" :config="jsonData" :djxh="djxh" />
    <div v-else>其他类型</div>
  </div>
</template>

<script>
import Mode1 from './Mode1.vue';
import Mode2 from './Mode2.vue';

export default {
  name: 'index',
  components: {
    Mode1,
    Mode2,
  },
  props: {
    djxh: {
      type: String,
      default: '1',
    },
    jsonData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  computed: {
    pageModelType() {
      if (this.jsonData && this.jsonData.pageBaseInfo) {
        return this.jsonData.pageBaseInfo.pageModelType;
      }
      return '';
    },
  },
  data() {
    return {
      //   jsonData: {
      //     pageBaseInfo: {
      //       pageId: 'TZBM000001MAIN',
      //       pageName: '销项发票总账',
      //       pageModelType: 'ONE',
      //     },
      //     pageMain: {
      //       topRightBtn: [
      //         {
      //           btnType: 'tqsjBtn',
      //           label: '提取数据',
      //           url: '/nssb/xxxxxx',
      //           params: ['nsrDjxh'],
      //           xh: 1,
      //         },
      //         {
      //           btnType: 'bctzBtn',
      //           label: '保存台账',
      //           url: '/nssb/xxxxxx',
      //           xh: 2,
      //         },
      //         {
      //           btnType: 'wyssBtn',
      //           label: '我要试算',
      //           url: 'http:www.baidu.com',
      //           xh: 3,
      //         },
      //       ],
      //       midLeftBtn: [
      //         {
      //           btnType: 'addRowBtn',
      //           xh: 1,
      //         },
      //         {
      //           btnType: 'delRowBtn',
      //           xh: 2,
      //         },
      //       ],
      //       mainGrid: [
      //         {
      //           colKey: 'row-select',
      //           type: 'multiple',
      //           width: 50,
      //         },
      //         {
      //           title: '序号',
      //           colKey: 'idx',
      //           width: 50,
      //         },
      //         {
      //           title: '计税方式',
      //           colKey: 'zsfs',
      //           isRowKey: true,
      //           width: 100,
      //           editConfig: {
      //             component: 't-input-number',
      //           },
      //         },
      //         {
      //           title: '征税方式2',
      //           colKey: 'zsfs2',
      //           width: 100,
      //           cell: true,
      //           editConfig: {
      //             options: [
      //               {
      //                 label: '审批通过',
      //                 value: 0,
      //               },
      //               {
      //                 label: '审批过期',
      //                 value: 1,
      //               },
      //               {
      //                 label: '审批失败',
      //                 value: 2,
      //               },
      //             ],
      //             component: 't-select',
      //           },
      //         },
      //         {
      //           title: '征税方式3',
      //           colKey: 'zsfs3',
      //           width: 100,
      //           editConfig: {
      //             component: 't-date-picker',
      //           },
      //         },
      //         {
      //           title: '开具税控增值税发票',
      //           width: 200,
      //           colKey: 'kjskzzsfp',
      //           align: 'center',
      //           children: [
      //             {
      //               colKey: 'aa',
      //               title: '申请状态1',
      //               width: 100,
      //               editConfig: {
      //                 component: 't-input',
      //               },
      //             },
      //             {
      //               colKey: 'dd',
      //               title: '申请状态2',
      //               width: 100,
      //             },
      //           ],
      //         },
      //         {
      //           align: 'center',
      //           colKey: 'operation',
      //           title: '操作',
      //           width: 70,
      //         },
      //       ],
      //     },
      //   },
    };
  },
  async created() {
    // const pageMain = this.jsonData.pageMain
    // if( pageMain && pageMain.initfn){
    //   const {params,url} = pageMain.initfn
    //   const condition = this.$refs.mode1Ref
    //   console.log('this.$refs.mode1Ref',this.$refs.mode1Ref)
    //   const { code, data, msg } = await api.commonTzInitData(url,{ tzbm: this.tzbm });
    // }
  },
  methods: {},
};
</script>

<style lang="less" scoped></style>
