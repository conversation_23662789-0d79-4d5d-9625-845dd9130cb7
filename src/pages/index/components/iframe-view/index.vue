<template>
  <div class="iframe-view">
    <!-- <t-skeleton v-if="loading" animation="gradient" /> -->
    <div v-show="loading"></div>
    <iframe
      ref="iframe"
      v-show="!loading"
      :id="id"
      :src="src"
      frameborder="0"
      width="100%"
      height="100%"
      @load="onload"
    />
  </div>
</template>

<script>
export default {
  name: 'IframeView',
  data() {
    return {
      height: '100%',
      loading: true,
    };
  },
  props: {
    src: String,
    id: { type: String, default: Date.now().toString() },
  },
  mounted() {
    // 监听内嵌表单发送的事件
    window.addEventListener('message', this.handleMessage);
  },
  destroyed() {
    window.removeEventListener('message', this.handleMessage);
  },
  methods: {
    onload() {
      this.loading = false;
    },
    handleMessage(e) {
      this.$emit('handleMessage', e);
    },
  },
};
</script>

<style lang="less" scoped>
@namespace: iframe-view;
.@{namespace} {
  height: 100%;
}
</style>
