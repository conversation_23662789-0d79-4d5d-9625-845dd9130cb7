<template>
  <div>
    <t-button theme="primary" variant="base" @click="addNewlyBuild">新增行</t-button>
    <t-table
      class="add-edit-table"
      ref="addedittable"
      row-key="key"
      :columns="columns"
      :data="addEditList"
      :editable-cell-state="editableCellState"
      @row-validate="onRowValidate"
    />
    <!-- <div class="add-newlybuild" @click="addNewlyBuild"><AddRectangleIcon style="color: #696969" />新建行</div> -->
  </div>
</template>

<script>
// import { AddRectangleIcon } from 'tdesign-icons-vue';

export default {
  name: 'add-edit-table',
  // components: { AddRectangleIcon },
  props: {
    /**
     * 可新增编辑表单配置
     */
    addEditColumns: {
      type: Array,
      default: () => [],
      require: true,
    },
    /**
     * 可新增编辑表单初始化列表数据
     */
    addEditList: {
      type: Array,
      default: () => [],
    },
    /**
     * 新增行的默认数据
     */
    rowDefaultData: {
      type: Object,
      default: () => ({}),
      require: true,
    },
    /**
     * 新增行数控制
     */
    maxRows: {
      type: Number,
      default: 100000,
    },
    /**
     * 控制某行不可编辑 返回true为可编辑
     */
    editableCellState: {
      type: Function,
      default: () => {},
    },
    // editableRowKeys: {
    //   type: Array,
    //   default: () => [],
    // },
    /**
     * 控制某行不可编辑 返回true为可编辑
     */
    addNewlyBuild: {
      type: Function,
      default: () => {},
      require: true,
    },
    /**
     * 失去焦点校验当前填写数据
     */
    onRowValidate: {
      type: Function,
      default: () => {},
    },
  },
  methods: {
    // // 新增行
    // addNewlyBuild() {
    //   if (this.maxRows && this.addEditList.length < this.maxRows) {
    //     this.addEditList.push({
    //       ...this.rowDefaultData,
    //       index: this.addEditList.length + 1,
    //       key: String(new Date().valueOf()),
    //     });
    //   } else {
    //     this.$message.warning(`最多新增${this.maxRows}条数据`);
    //   }
    // },
  },
  computed: {
    columns() {
      return [...this.addEditColumns];
    },
  },
};
</script>

<style lang="less" scoped>
.add-newlybuild {
  height: 30px;
  margin: 8px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
  border: 1px dashed #d6d6d6;
}
// 隐藏暂无数据
/deep/ .add-edit-table .t-table__empty-row {
  display: none;
}
// 兼容减免性质悬浮框太长，且嵌套再collspan折叠面板导致显示不全的问题， 只有一行数据就会错乱。。。
.add-edit-table {
  padding-top: 15px;
}
</style>
