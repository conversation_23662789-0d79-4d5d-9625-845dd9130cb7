<template>
  <div :class="{ 'g-table-empty-placeholder': true, 'g-table-empty-placeholder__border': showBorder }">
    <div class="g-table-empty-placeholder__container">
      <div class="g-table-empty-placeholder__icon-wrapper">
        <slot name="icon">
          <img class="g-table-empty-placeholder__icon" :src="icon" alt="" />
        </slot>
      </div>
      <p class="g-table-empty-placeholder__info">{{ info }}</p>
    </div>
  </div>
</template>

<script>
const DEFAULT_IMG = require('./icon-empty-placeholder.png');

export default {
  name: 'EmptyPlaceholder',
  props: {
    info: {
      type: String,
      default: '暂无数据',
    },
    icon: {
      type: String,
      default: DEFAULT_IMG,
    },
    showBorder: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
.g-table-empty-placeholder {
  position: relative;
  width: 100%;
  height: 194px;
  &__border {
    border-bottom: 1px solid rgba(39, 40, 46, 0.08);
  }
  &__container {
    position: absolute;
    top: 50%;
    width: 100%;
    text-align: center;
    transform: translate(0, -50%);
  }
  &__icon-wrapper {
    display: inline-block;
    padding-bottom: 12px;
  }
  &__icon {
    display: block;
    width: 64px;
    height: 64px;
  }
  &__info {
    font-size: 14px;
    line-height: 22px;
    color: #999;
    text-align: center;
  }
}
</style>
