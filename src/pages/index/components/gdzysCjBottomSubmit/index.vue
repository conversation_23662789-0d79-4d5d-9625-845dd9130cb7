<template>
  <div class="footerbar">
    <gt-space size="10px">
      <t-button theme="primary" variant="outline" @click="onBack">退出</t-button>
      <t-button theme="primary" @click="toDeclare">提交</t-button>
    </gt-space>
  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { MessagePlugin } from 'tdesign-vue';
// eslint-disable-next-line import/no-unresolved
import methods from '@/pages/index/views/gdzys/gdzyzyscjRequest';

export default {
  name: 'bottom-submit',
  props: {
    /**
     * 申报提交按钮是否置灰
     */
    declareDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    onBack() {
      this.$router.push('/gdzys/gdzyssybg');
    },
    toDeclare() {
      console.log('触发回调后');
      this.$emit('submitClickListener', async function (data) {
        // if (this.$store.state.gdzys.cwgzFlag) {
        //   console.log('recordSkssqq', this.recordSkssqq, this.gdzysSyxxVO.skssqq);
        //   if (this.recordSkssqq !== this.gdzysSyxxVO.skssqq) {
        //     this.$message('info', '不可更改时期起至');
        //     return;
        //   }
        // }
        let res;
        // this.statusArray = [];
        // this.tipArray = [];
        console.log('提交的数据为', ...data);
        await methods.saveCjxx(data).then((val) => {
          res = val;
        });
        console.log('res2', res);
        return res;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.footerbar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2499;
  width: 100%;
  height: 64px;
  line-height: 64px;
  text-align: center;
  background-color: white;
}
</style>
