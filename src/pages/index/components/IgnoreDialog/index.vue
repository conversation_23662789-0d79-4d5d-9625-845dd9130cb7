<template>
  <css-dialog
    class="dialogMiddleCss"
    placement="center"
    confirmBtn="确定"
    :cancelBtn="null"
    :visible="normalizedVisible"
    :onConfirm="handleConfirm"
    :onClose="handleClose"
    :closeOnOverlayClick="false"
  >
    <t-divider style="margin: 0"></t-divider>
    <div slot="header">
      <span>忽略差异</span>
    </div>
    <div class="nr">
      <t-form ref="form" :data="formData" labelAlign="top" labelWidth="165px" :rules="dynamicRules">
        <div v-show="showHlxmFlag">
          <!-- <p><br /></p>
          <p style="font-size: 1.2em; color: red">本期开票未入账时无需忽略，请通过收入明细台账新增凭证进行调账处理。</p>
          <p><br /></p> -->
          <t-row :gutter="16">
            <t-col :span="4">
              <t-form-item label="忽略项目" name="hlDm">
                <t-select
                  v-model="formData.hlDm"
                  placeholder="请选择忽略项目"
                  :clearable="false"
                  :disabled="!hlxmList.length"
                >
                  <t-option v-for="item in hlxmList" :value="item.hlDm" :label="item.hlyy" :key="item.hlDm"></t-option>
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="16" v-show="false">
            <t-col :span="4">
              <t-form-item label="忽略项目名称" name="hlyy">
                <t-input v-model="formData.hlyy"></t-input>
              </t-form-item>
            </t-col>
          </t-row>
        </div>
        <t-row :gutter="16">
          <t-col :span="12">
            <t-form-item label="忽略原因" name="hlsy">
              <t-textarea :maxlength="150" v-model="formData.hlsy" placeholder="请填写忽略原因" clearable />
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
    <t-divider style="margin: 0"></t-divider>
  </css-dialog>
</template>

<script>
import CssDialog from '@/pages/index/components/css-dialog/index.vue';

export default {
  components: { CssDialog },
  props: {
    visible: { type: [Object, Boolean], required: true, default: () => {} },
    formData: {
      type: Object,
      default: () => ({
        hlDm: '',
        hlyy: '',
        hlsy: '',
      }),
    },
  },
  created() {
    // 根据是否展示忽略项目项设置默认值
    // if (this.showHlxmFlag) {
    //   this.formData.hlDm = '00';
    // } else {
    //   this.formData.hlDm = '';
    //   this.formData.hlyy = '';
    // }
  },
  data() {
    return {
      hlxmList: [
        { hlyy: '本期开票未入账', hlDm: '00' },
        { hlyy: '前期已入账，本期开票', hlDm: '01' },
        { hlyy: '大量退货导致账面收入小于本期已开发票数据', hlDm: '02' },
        { hlyy: '其他', hlDm: '03' },
      ],
    };
  },
  computed: {
    showHlxmFlag() {
      console.log('visible.showHlxmFlag', this.visible);
      console.log('formData', this.formData);
      return this.visible?.showHlxmFlag ?? false;
    },
    normalizedVisible() {
      console.log('visible', this.visible);
      if (this.visible) {
        return true;
      }
      return false; // 同时检查 null 和 undefined
    },
    dynamicRules() {
      const baseRules = {};

      // 动态规则设置
      if (this.showHlxmFlag && this.formData.hlDm !== '00') {
        baseRules.hlDm = [{ required: true, message: '必填', type: 'error' }];
      }
      if (this.formData.hlDm !== '00') {
        baseRules.hlsy = [{ required: true, message: '必填', type: 'error' }];
      }
      return baseRules;
    },
  },
  watch: {
    'formData.hlDm': {
      handler(newVal) {
        if (this.showHlxmFlag && newVal) {
          const selected = this.hlxmList.find((item) => item.hlDm === newVal);
          this.formData.hlyy = selected ? selected.hlyy : '';

          // 新增校验状态清除逻辑
          if (newVal === '00') {
            this.$nextTick(() => {
              this.$refs.form.clearValidate(['hlsy']); // 清除忽略原因的校验状态
            });
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate().then((result) => {
        if (result === true) {
          const submitData = { ...this.formData };

          // 不展示忽略项时删除相关字段
          if (!this.showHlxmFlag) {
            delete submitData.hlDm;
            delete submitData.hlyy;
          }

          this.$refs.form.clearValidate();
          if (submitData.hlDm === '00' && this.showHlxmFlag) {
            const alertDia = this.$dialog.alert({
              theme: 'info',
              header: '提示',
              body: '本期开票未入账时无需忽略，请通过收入明细台账新增凭证进行调账处理。',
              cancelBtn: null,
              onConfirm: () => {
                // 销毁弹框
                alertDia.destroy();
              },
            });
          } else {
            this.$emit('confirm', submitData);
            this.handleClose();
          }
        }
      });
    },
    handleClose() {
      // 新增表单重置逻辑
      this.$refs.form.reset();
      this.formData.hlDm = this.showHlxmFlag ? '00' : '';
      this.formData.hlyy = '';
      this.formData.hlsy = '';
      this.$emit('close');
    },
  },
};
</script>
<style scoped lang="less">
@import '../../styles/dialog.less';
.dialogMiddleCss {
  /deep/ .t-dialog--default {
    width: 750px !important;
    height: 30% !important;
  }
  /deep/ .t-dialog__body .nr {
    height: 80% !important;
  }
}
</style>
