<template>
  <t-select
    class="g-all-select"
    ref="allSelect"
    v-bind="$attrs"
    v-on="$listeners"
    :value="value"
    :options="options"
    multiple
    :minCollapsedNum="1"
    :onChange="selectChange"
  >
    <template #panelTopContent>
      <ul v-show="showCheckAll && hasOptions" class="t-select__list">
        <li :class="selectAllClasses" style="margin-bottom: 4px">
          <t-checkbox
            @click="(e) => e.stopPropagation()"
            :checked="allChecked"
            :indeterminate="allIndeterminate"
            :onChange="selectAllChange"
            >全选</t-checkbox
          >
        </li>
      </ul>
    </template>
    <template #valueDisplay="{ value: selectedList }">
      <t-tag class="g-all-select__custom-tag" v-bind="currentTagProps" v-show="selectedList && selectedList.length > 0">
        {{ selectedList[0] && sliceText(selectedList[0].label) }}
      </t-tag>
    </template>
  </t-select>
</template>

<script>
const DEFAULT_TAG_PROPS = {
  maxWidth: 40,
};
const LABEL_THRESHOLD = 2;
const DOUBLE_BYTE_THRESHOLD = 255;

export default {
  name: 'AllSelect',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      default: () => [],
    },
    tagProps: {
      type: Object,
      default: () => DEFAULT_TAG_PROPS,
    },
    size: {
      type: String,
      default: 'medium',
    },
    showCheckAll: {
      type: Boolean,
      default: true,
    },
    maxTextLength: {
      type: Number,
      default: LABEL_THRESHOLD,
    },
  },
  data() {
    return {};
  },
  computed: {
    selectAllClasses() {
      const sizeClass = `t-size-${this.size.slice(0, 1)}`;
      return {
        't-select-option': true,
        [sizeClass]: true,
        't-is-selected': this.allChecked,
      };
    },
    hasOptions() {
      return this.options.length > 0;
    },
    // currentOptions() {
    //   return
    // },
    currentTagProps() {
      return {
        ...DEFAULT_TAG_PROPS,
        ...this.tagProps,
      };
    },
    allChecked() {
      return this.options.every((item) => this.value.includes(item.value));
    },
    allIndeterminate() {
      return !!(this.options.length > this.value.length && this.value.length);
    },
  },
  methods: {
    // 计算字符串字节长度，英文字母为单字节字符，中文汉字为双字节字符
    getStringLength(str) {
      const strLength = str.length;

      let count = 0;
      for (let i = 0; i < strLength; i++) {
        count += str.charCodeAt(i) > DOUBLE_BYTE_THRESHOLD ? 2 : 1;
      }
      // console.log('string length', str, count)
      return count;
    },
    // 根据预设值判断label是否显示省略
    shouldEllipsis(str) {
      return this.getStringLength(str) > this.maxTextLength * 2;
    },
    // label文字切割
    sliceText(text, length = 7) {
      if (this.shouldEllipsis(text)) {
        return `${text.slice(0, length)}...`;
      }
      return text;
    },
    updateValue(value = []) {
      this.$emit('change', value);
    },
    selectAllChange(checked) {
      // console.log(checked)
      const value = checked ? this.options.map((item) => item.value) : [];
      this.updateValue(value);
    },
    selectChange(selectValue) {
      // console.log(selectValue)
      this.updateValue(selectValue);
    },
  },
};
</script>

<style lang="less" scoped>
.g-all-select__custom-tag {
  /deep/.t-tag--text {
    // 为了解决ie兼容bug

    /* stylelint-disable-next-line declaration-no-important */
    // width: auto !important;
  }
}
</style>
