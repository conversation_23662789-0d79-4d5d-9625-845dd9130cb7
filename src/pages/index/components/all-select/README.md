## all-select
带全选的选择框，使用select拓展，含全选功能，不换行显示，与select大部分事件、属性相同

### props
| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| --- | --- | --- | --- | --- |
|tagProps| tag透传属性对象 | Object | - | |
|placeholder| 占位符 | String | - | |
|value| 已选标签值 | Array | - | |
|options| 选项列表 | Array | - | |
|minCollapsedNum| 最小折叠数量 | Number | - | |
|allowCheckAll| 是否能够全选 | Boolean | - | |
|maxTextLength| tag最多展示的字数,多出部分显示省略 | Number | - | 2 |

### 事件

| 方法    | 说明             | 参数 |
| ------- | ---------------- | ---- |
| change | 已选标签值变化事件 | -    |


正在迭代中，如遇到缺陷，支持群里反馈