//统一调用入口
function extMethods(mathFlag,newData,data,tag){
	//输入纳税人识别号查询纳税人信息
	if ("changeNsrsbh"==mathFlag){
		changeNsrsbh(tag,newData);
	}

}

/**
 * 输入纳税人识别号查询纳税人信息
 */
function changeNsrsbh(scope,indexNum){
	var mainUrl = window.location.protocol+"//"+window.location.host+parent.parent.urlPrefix;
	var djxh=parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.nsrxxForm.djxh;
    var nsrsbh=parent.formData.fq_.nsrjbxx.nsrsbh;
	if(indexNum==-1){
		var dzdw=parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.slxxForm.dzdw;
	}else{
		var dzdw=parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.skxxGrid.skxxGridlb[indexNum].nsrsbh;
		parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.skxxGrid.skxxGridlb[indexNum].djxh="";
	}
		
	var jsonData = {
			"djxh":djxh,
			"nsrsbh":nsrsbh,
			"sid":"dzswj.wbzy.dj.djNsrxx.cxNsrxxByNsrsbh",
			"gdslxDm":parent.parent.gdslxDm,
			"DZDW":dzdw
			};
	var test = parent.location.href.indexOf("test=true")>-1;
	if(test){
		jsonData.test=true;
	}
	
	var nsrmc="";
	if(nsrsbh==''){
		return;
	}else{		
		 $.ajax({
			 type: "POST",
				url: mainUrl+"cxNsrxxByNsrsbh",
				async: true,
				data: JSON.stringify(jsonData),
				 headers: {
					 "AuthorizationMock": parent.sessionStorage.token,
					 "contentType": "application/json"
				 },
			    dataType:"json",
			    contentType:"application/json",
				success:function(data){
				    //响应异常的情况不阻断,只打印日志
					if(data.Response.Error) {
						console.log(data.Response.Error.Message);
						return ;
					}

					if (!isNull(data)) {
						var result = jQuery.parseJSON(data.Response.Data.Body);
						if(!isNull(result)){
							var djnsrxx = result.djnsrxx[0];
							if(!isNull(djnsrxx)){
								nsrmc =djnsrxx.nsrmc;
							}
						}						
						if(isNull(nsrmc)){
							parent.layer.alert("查不到该纳税人信息，请检查输入的纳税人识别号是否正确！", {icon: 2});
						}else{
																			
							if(indexNum==-1){
								parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.slxxForm.smrxm=nsrmc;
								parent.formulaEngine.apply("ht_.hxzgsb10534Request.ywbw.sytbsyj.slxxForm.smrxm", nsrmc);
							}else{
								parent.formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.skxxGrid.skxxGridlb[indexNum].nsrmc=nsrmc;
								parent.formulaEngine.apply("ht_.hxzgsb10534Request.ywbw.sytbsyj.skxxGrid.skxxGridlb[#].nsrmc", nsrmc);
							}
							
							//刷新校验结果和控制结果
							viewEngine.formApply($('#viewCtrlId'));
							viewEngine.tipsForVerify(document.body);
						}
					}
				}
			});
		
	}
}

/**
 * 通过选择的月份取得当月平均汇率
 * 
 */
function getHl(yf) {
	var ypjhlxxs = formData.fq_.ypjhlxxs;
	for(var i=0;i<ypjhlxxs.length;i++){
		var ypjhlxx=ypjhlxxs[i];
		var dm=ypjhlxx.dm;
		var hl=parseFloat(ypjhlxx.mc);
		if(dm==yf){
			return hl;
		}
	}
	return 0;
}
/**
 * 通过当月加权平均销售价格获取速算扣除数
 * 
 */
function getSskcs(dyjqpjxsdj) {
	//兼容不配置0-65区间的省份
	if(dyjqpjxsdj<=65){
		return 0;
	}
	var zspmxxs = formData.fq_.zspmxxs;
	for(var i=0;i<zspmxxs.length;i++){
		var zspmmx=zspmxxs[i];
		if(zspmmx.ljbzq!=null&&zspmmx.ljbzq!=""){
			var ljbzq=parseFloat(zspmmx.ljbzq);
			var sskcs=parseFloat(zspmmx.sskcs);
			if(zspmmx.ljbzz!=null&&zspmmx.ljbzz!=""){
				var ljbzz=parseFloat(zspmmx.ljbzz);
				if(dyjqpjxsdj>=ljbzq && dyjqpjxsdj<=ljbzz){
					return sskcs;
				}
			}else{
				if(dyjqpjxsdj>=ljbzq){
					return sskcs;
				}
			}			
		}
	}
	return 0;
}

/**
 * 校验当月加权平均销售价格
 * 
 */
 function checkDyjqpjxsdj(dyjqpjxsdj) {
	//兼容不配置0-65区间的省份
	if(dyjqpjxsdj<=65){
		return true;
	}
	var zspmxxs = formData.fq_.zspmxxs;
	for(var i=0;i<zspmxxs.length;i++){
		var zspmmx=zspmxxs[i];
		if(zspmmx.ljbzq!=null&&zspmmx.ljbzq!==""){
			var ljbzq=parseFloat(zspmmx.ljbzq);
			if(zspmmx.ljbzz!=null&&zspmmx.ljbzz!==""){
				var ljbzz=parseFloat(zspmmx.ljbzz);
				if(dyjqpjxsdj>=ljbzq && dyjqpjxsdj<=ljbzz){
					return true;
				}
			}		
		}
	}

	return false;
}

/**
 * 通过当月加权平均销售价格获取征收标准
 * 
 */
function getZsbz(dyjqpjxsdj) {
	//兼容不配置0-65区间的省份
	if(dyjqpjxsdj<=65){
		return 0;
	}
	var zspmxxs = formData.fq_.zspmxxs;
	for(var i=0;i<zspmxxs.length;i++){
		var zspmmx=zspmxxs[i];
		if(zspmmx.ljbzq!=null&&zspmmx.ljbzq!=""){
			var ljbzq=parseFloat(zspmmx.ljbzq);
			var sysl=parseFloat(zspmmx.sysl);
			if(zspmmx.ljbzz!=null&&zspmmx.ljbzz!=""){
				var ljbzz=parseFloat(zspmmx.ljbzz);
				if(dyjqpjxsdj>=ljbzq && dyjqpjxsdj<=ljbzz){
					return sysl;
				}
			}else{
				if(dyjqpjxsdj>=ljbzq){
					return sysl;
				}
			}			
		}
	}
	return 0;
}

/**
 * 按季申报需要每月有一条记录，多家下属企业应有多个3条，如没达到征收标准，应进行0申报
 * 
 */
function checkYf(pYf, pNsrsbh) {
	// 取暑期，计算出需要申报的月份
	var skssqq = formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.nsrxxForm.skssqq;
	var skssqz = formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.nsrxxForm.skssqz;
	var ksyf = skssqq.substring(5, 7);
	var jsyf = skssqz.substring(5, 7);
	// 计算间隔的月份
	var jgyf = (jsyf * 1 - ksyf * 1) + 1;
	// 按月申报不校验
	if (jgyf === 1) {
		return true;
	}

	// 遍历skxxGridlb，得到key为nsrsbh的对象和key为nsrsbh_yf的对象
	var nsrsbhMap = {};
	var skxxGridlb = formData.ht_.hxzgsb10534Request.ywbw.sytbsyj.skxxGrid.skxxGridlb;
	for (var i=0; i<skxxGridlb.length; i++) {
		var skxx = skxxGridlb[i];
		var nsrsbh = skxx.nsrsbh;
		var yf = skxx.yf;

		if (!nsrsbhMap[nsrsbh]) {
			nsrsbhMap[nsrsbh] = {};
		}

		if (yf && !nsrsbhMap[nsrsbh][nsrsbh + '_' + yf]) {
			nsrsbhMap[nsrsbh][nsrsbh + '_' + yf] = 'Y';
		}
	}

	if (nsrsbhMap[pNsrsbh]) {
		// 判断是否相等
		return jgyf === Object.keys(nsrsbhMap[pNsrsbh]).length;
	} else {
		return false;
	}
}

/**
 * 检查数据是否为空
 * 
 * @method isNull
 * @param param
 *            {Object} 参数对象
 * @returns {Boolean} 检查结果为空或未定义返回true，不为空返回false
 */
function isNull(param) {
	if (param === null || param === "null" || param === undefined
			|| param === "undefined" || '' === param) {
		return true;
	}
	return false;
}

// 将应补退税额同步给vue外壳
function postYbtse() {
	var ybtsdseLj = formData.fq_.bqybtsfeHj;
	var message = {"type":"ybtse", "ywbm":parent.ywbm, "ybtse":ybtsdseLj};
	parent.postMessage2Vue(message);
}

export default {
	isNull,
	getZsbz,
	getSskcs,
	getHl,
	checkDyjqpjxsdj,
	checkYf,
}