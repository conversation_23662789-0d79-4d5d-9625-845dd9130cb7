// 将应补退税额同步给vue外壳
function postYbtse() {
    var ybtkqsyfje = formData.ht_.kqsyfndsbSbbdxxVO.kqsyfndsbb.kqsyfndsbForm.ybtkqsyfje;
    var message = {"type":"ybtse", "ywbm":parent.ywbm, "ybtse":ybtkqsyfje};
    parent.postMessage2Vue(message);
}

function getsyflAndSskcs(yfcpmc,bnyyjhzcl){
    var retrn={"syfl":0,"sskcs":0};
    if(yfcpmc=="" ||yfcpmc==null || yfcpmc==undefined || bnyyjhzcl==0 || bnyyjhzcl<0){
        return retrn;
    }
    //yfcpmc: 01 海上油  02 海上天然气    03 陆上油  04 陆上天然气
    var syflxx=formData.fq_.syflxx;
    if(syflxx){
        var kqsyflList = syflxx.kqsyflList;
        for(var i=0;i<kqsyflList.length;i++){
             //zspmDm:  3070101 海上油  3070102 海上天然气  3070201 陆上油   3070202 陆上天然气
            var zspmDm=kqsyflList[i].zspmDm;
            var yfcpmcDmb=kqsyflList[i].yfcpmc;
            var jfslz=kqsyflList[i].jfslz;
            var jfslq=kqsyflList[i].jfslq;
            var sskcs=kqsyflList[i].sskcs;
            var syfl=kqsyflList[i].syfl;
            //库里的费率要除以100才是真实的费率
            syfl=ROUND(syfl/100,3);
            if(yfcpmc=="01" && yfcpmcDmb.indexOf("海上原油")>-1){
    
                if( (jfslq==0 || jfslq=="") &&  bnyyjhzcl<=jfslz ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if((jfslz==0 || jfslz=="") && bnyyjhzcl>jfslq  ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if(bnyyjhzcl>jfslq && bnyyjhzcl<=jfslz){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }
    
    
            }else if(yfcpmc=="02" && yfcpmcDmb.indexOf("海上天然气")>-1 ){
                if( (jfslq==0 || jfslq=="") &&  bnyyjhzcl<=jfslz ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if((jfslz==0 || jfslz=="") && bnyyjhzcl>jfslq  ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if(bnyyjhzcl>jfslq && bnyyjhzcl<=jfslz){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }
            }else if(yfcpmc=="03" && yfcpmcDmb.indexOf("陆上原油")>-1){
                if( (jfslq==0 || jfslq=="") &&  bnyyjhzcl<=jfslz ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if((jfslz==0 || jfslz=="") && bnyyjhzcl>jfslq  ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if(bnyyjhzcl>jfslq && bnyyjhzcl<=jfslz){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }
            }else if(yfcpmc=="04" && yfcpmcDmb.indexOf("陆上天然气")>-1){
                if( (jfslq==0 || jfslq=="") &&  bnyyjhzcl<=jfslz ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if((jfslz==0 || jfslz=="") && bnyyjhzcl>jfslq  ){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }else if(bnyyjhzcl>jfslq && bnyyjhzcl<=jfslz){
                    retrn.syfl=syfl;
                    retrn.sskcs=sskcs;
                    break;
                }
            }
        }
    }
    return  retrn;
}


export default {
    getsyflAndSskcs
}