/**
 * 已实名制的，默认获取当前登录人员的姓名，自动带出不允许修改。如果取不到实名制信息时置空
 * @param smzbz 
 * @param smzxm
 * @returns
 */
function setJbr(smzbz,smzxm){
	var jbr=formData.ht_.kjqysdssbvo.slxxForm.jbr;
	if (smzbz=='Y') {
		return smzxm;
	}
	return jbr;
}

/**
 * 已实名制的，默认获取当前登录人员的身份证号码，自动带出不允许修改。如果取不到实名制信息时置空
 * @param bz
 * @param zjhm
 * @returns
 */
function setBlrysfzjhm(bz,zjhm){
	var smzbz=formData.fq_.smzxx.smzbz;
	var smzjhm=formData.fq_.smzxx.zjhm;
	var blrysfzjhm=formData.ht_.kjqysdssbvo.slxxForm.blrysfzjhm;
	var zjlx=formData.fq_.smzxx.zjlx;
	
	if(bz=='xsbz'){
		if(smzbz=='Y'){
			if(zjlx=='201'){
				return zjhm.substring(0,6)+"********"+ zjhm.substring(14);
			}else{
				return zjhm;
			}
		}else{
			return blrysfzjhm;
		}
	}else{
		if(smzbz=='Y'){
			return smzjhm;
		}else{
			return zjhm;
		}
  }
}

function setBcsbsrwbzsrmbje(je,hl){
	var wbmxxx= formData.kz_.temp.wbmxGrid.WbmxGridlb;
	var cont=0;
	
	for(var i=0;i<wbmxxx.length;i++){
		var mc=wbmxxx[i].mc;
		if(mc!==""&&mc!==null&&mc!==undefined){
			cont++;
		}
	}
	if(cont<=1){
	return ROUND(je*hl,2);
	}else{
		var bcsbsrwbzsrmbje=formData.ht_.kjqysdssbvo.fdyqkjqkForm.bcsbsrwbzsrmbje;
		return  bcsbsrwbzsrmbje;
	}
}
/**
 * 逾期申报监控：录入支付日期时， 当系统当前日期超过“支付日期”+7时，提示：该申报已逾期
 * @param xqsbjnskrq
 * @param kjywfssjZfrq
 * @returns 
 */
function flagSfyq(kjywfssjZfrq){
	if (kjywfssjZfrq===""||kjywfssjZfrq===null) {
		return true;
	}else{
		var dateTemp = kjywfssjZfrq.split("-");
		var nDate = new Date(dateTemp[1] + '-' + dateTemp[2] + '-' + dateTemp[0]); //转换为MM-DD-YYYY格式  
		  
			var millSeconds = Math.abs(nDate) + (7 * 24 * 60 * 60 * 1000);
		  var rDate = new Date(millSeconds);
		  var year = rDate.getFullYear();
		  var month = rDate.getMonth() + 1;
		  if (month < 10) month = "0" + month;
		  var date = rDate.getDate();
		  if (date < 10) date = "0" + date;
		  //获取当前日期
			var currentTime = getCurrentSystemTime();
		  var jqdate=year + "-" + month + "-" + date;
		  if (DATE_CHECK_TIME(jqdate,currentTime)) {
			return false;
		}else{
			return true;
		}
	}
}

/**
 * 将"YYYY-MM-DD"格式转换为"XX年XX月XX日"
 * @returns 
 */
function slrqFormat(){
	var slrq=formData.ht_.kjqysdssbvo.slxxForm.slrq;
	slrq=slrq.substring(0,slrq.indexOf('-'))+"年"+slrq.substring(slrq.indexOf('-')+1,slrq.lastIndexOf('-'))+"月"+slrq.substring(slrq.lastIndexOf('-')+1)+"日";
	return slrq;
}

function sfyxsb(){
	var sbrq=formData.ht_.kjqysdssbvo.kjywrjbxxForm.skssqq;
	var sfyxsb=formData.fq_.sfyxsb;

	if(DATE_CHECK_TIME(sbrq , "2019-10-01")&&sfyxsb!=="Y"){
		var msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;【非居民企业企业所得税自行申报（2019年版）】启用时间为2019年10月1日，当前属期请在【非居民企业企业所得税自行申报（2015版）】完成申报，谢谢！ ";
		var a =layer.confirm(msg,{
		 	area: ['320px','260px'],
		 	title:'提示',
		 	closeBtn : 0,
			btn : ['确定']
			},function(data){
			layer.close(a);
			closeWin();
	});
	
	}
	
}

/**
 * 关闭当前页面
 */

function closeWin() {

	if (navigator.userAgent.indexOf("MSIE") > 0) {
		if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
			window.opener = null;
			window.close();
		} else {
			window.open('', '_top');
			window.top.close();
		}
	} else if (navigator.userAgent.indexOf("Firefox") > 0) {
		window.location.href = 'about:blank ';
		window.close();
	} else if (navigator.userAgent.indexOf("Chrome") > 0) {
		top.open(location, '_self').close();
	} else {
		window.open('', '_top');
		window.top.close();
	}
}

/**
 * 获得当前系统时间 格式"yyyy-MM-dd"
 */
function getCurrentSystemTime(){
	//获取当前日期
	var currentDate = new Date();
	var currentMonth = currentDate.getMonth()+1;
	var currentDay = currentDate.getDate();
	if (currentMonth < 10){
		currentMonth = "0"+currentMonth;
	}
	if (currentDay < 10){
		currentDay = "0"+currentDay;
	}
	return currentDate.getFullYear()+"-"+currentMonth+"-"+currentDay;
}

/**
 * 对于属期在2020年及之后，当第12行享受协定待遇金额大于0时，增加非强制性提示：享受协定待遇应填报《非居民纳税人享受协定待遇信息报告表》。
 * @param jmse
 * @param ssqq
 * @returns {Boolean}
 */
function fzdjy(jmse,ssqq){
	if(jmse>0&&DATE_CHECK_TIME_SIZE('2020-01-01',ssqq)){
		return false;
	}else{
		return true;
	}
}
/**
 * 在中国的联系地址、邮政编码：联系地址与邮政编码之间用分号隔开，联系地址长度100个字符，邮政编码长度6个字符
 * 在居民国（地区）的联系地址、邮政编码：联系地址与邮政编码之间用分号（中文分号）隔开，总长度不超过107字符。
 */
function checkLxdqyzbm(lxdqyzbm,bz){
	if(isEmptyObject(lxdqyzbm)){
		return false;
	}else if((lxdqyzbm).lastIndexOf("；")==-1){
		return false;
	}else{
		var lastIndexOf=lxdqyzbm.lastIndexOf("；");
		var yzbm=lxdqyzbm.substring(lastIndexOf+1);
		var lxdz=lxdqyzbm.substring(-1,lastIndexOf);
		if(bz=='zg'){
			var regYzbm=/^\d{6}$/;
			if(lxdz.length<=100&&(isEmptyObject(yzbm) || regYzbm.test(yzbm))){
				return true;
			}else{
				return false;
			}
		}else{
			if(lxdqyzbm.length<=107){
				return true;
			}else{
				return false;
			}
		}
	}
}


/**
 * 检验电子邮箱
 * @param dzxx
 * @returns {Boolean}
 */
function checkDzxx(dzxx){
	//var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})$/;
	//取消协定附表[10.电子邮箱]中对点后域名的校验
	var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)/;
	if(reg.test(dzxx)){
		return true;
	}else{
		return false;
	}
}

/**
 * 设置在中国的联系地址、邮政编码
 * @param kjywrZcdz
 * @param kjywrYwdz
 * @param kjywryzbm
 */
function setLxdzYzbm(Zcdz,Ywdz,yzbm){
	if(!isEmptyObject(Zcdz)){
		return Zcdz+"；"+yzbm;
	}else{
		return Ywdz+"；"+yzbm;
	}
}


/**
 * 设置受理日期
 */
function setSlrq(){
	return getCurrentSystemTime();
}

/**
 * 提交时对协定待遇表报文做特殊处理
 */
function delXddybdBw(xdbChangeFlag,syssxdtkJmse){
	var xddybw = formData.ht_.kjqysdssbvo.xddyYwbd;
	if (isEmptyObject(xddybw)) {
		return;
	}
	var dzbd=formData.kz_.dzbd;
	
    if(!xdbChangeFlag&&syssxdtkJmse<=0){
    	delete formData.ht_.kjqysdssbvo.xddyYwbd;
    	var xdbd="BDA0611121---";
    	if(dzbd.indexOf(xdbd)>-1){
    		var star=dzbd.indexOf(xdbd);
    		var end=star+xdbd.length;
    		var dzbdStr=dzbd.substring(0,star)+dzbd.substring(end);
    		formData.kz_.dzbd=dzbdStr;
    	}
     }
}

/**
 * 设置非居民纳税人签章或签字日期，自动带出系统日期，不可修改。
 */
function setFjmnsrqzrq(){
	return getCurrentSystemTime();
}

/**
 * 设置扣缴义务人名称
 * @param kjywrmc
 * @param kjywrywmc
 */
function setKjywrmc(kjywrmc,kjywrywmc){
	if(!isEmptyObject(kjywrmc)){
		return kjywrmc;
	}else{
		return kjywrywmc;
	}
}

//DLGSWSBSSC-570
function ywControlBtn(dzbdbm) {
	if (dzbdbm==="BDA0611121") {
		var msg="非居民纳税人需要享受协定待遇的，纳税人应填写本表，并如实将加盖非居民签章或签字的《非居民纳税人享受协定待遇信息报告表》报送至主管税务机关。";
		layer.alert(msg, {title:"提示"});
	} 
}

//更正时根据税收优惠项目减免性质对照表来处理协定待遇的码表值
function gzSetSyssxdtk(gzsbbz,syssxdtk) {
	if (isNull(gzsbbz)){
		return;
	}
	var array="{\"1001\":\"0004081511\",\"1002\":\"0004081512\",\"1003\":\"0004081513\",\"1004\":\"0004081514\",\"1005\":\"0004081516\",\"1006\":\"0004129922\",\"1007\":\"0004083904\",\"1008\":\"0004081507\",\"1009\":\"0004121302\",\"1010\":\"0004032101\",\"1011\":\"0004032102\",\"1012\":\"0004081522\",\"1013\":\"0004102906\",\"1014\":\"0004102908\",\"1015\":\"0004081521\",\"1016\":\"0004081524\",\"2001\":\"0004135401\",\"2002\":\"0004135501\",\"2003\":\"0004135601\",\"2004\":\"0004135701\",\"2005\":\"0004139901\",\"2006\":\"0004139901\",\"2009\":\"0004139901\",\"2010\":\"0004139901\",\"3999\":\"0004139901\"}";
    var arrayJson = JSON.parse(array);
	var syssxdtk=formData.ht_.kjqysdssbvo.fdyqkjqkForm.syssxdtk;
    formData.ht_.kjqysdssbvo.fdyqkjqkForm.syssxdtk=arrayJson[syssxdtk];
}

//更正时根据税收优惠项目减免性质对照表来处理国内税收优惠的码表值
function gzSetGnsfyhxm(gzsbbz,gnsfyhxm) {
    if (isNull(gzsbbz)){
        return;
    }
    var array="{\"1001\":\"0004081511\",\"1002\":\"0004081512\",\"1003\":\"0004081513\",\"1004\":\"0004081514\",\"1005\":\"0004081516\",\"1006\":\"0004129922\",\"1007\":\"0004083904\",\"1008\":\"0004081507\",\"1009\":\"0004121302\",\"1010\":\"0004032101\",\"1011\":\"0004032102\",\"1012\":\"0004081522\",\"1013\":\"0004102906\",\"1014\":\"0004102908\",\"1015\":\"0004081521\",\"1016\":\"0004081524\",\"2001\":\"0004135401\",\"2002\":\"0004135501\",\"2003\":\"0004135601\",\"2004\":\"0004135701\",\"2005\":\"0004139901\",\"2006\":\"0004139901\",\"2009\":\"0004139901\",\"2010\":\"0004139901\",\"3999\":\"0004139901\"}";
    var arrayJson = JSON.parse(array);
    var gnsfyhxm=formData.ht_.kjqysdssbvo.fdyqkjqkForm.gnsfyhxm;
    formData.ht_.kjqysdssbvo.fdyqkjqkForm.gnsfyhxm=arrayJson[gnsfyhxm];
}

/*厦门 享受国内税收优惠 监控提示*/
function tipXm(swjgDm,gnsfyhxm){
}
/*厦门 自行申报初始化 监控提示*/
function verifyTips(swjgDm){
}


export default {
	setJbr,
	setBlrysfzjhm,
	setBcsbsrwbzsrmbje,
	flagSfyq,
	slrqFormat,
	sfyxsb,
	fzdjy,
	checkLxdqyzbm,
	checkDzxx,
	setLxdzYzbm,
	setSlrq,
	delXddybdBw,
	setFjmnsrqzrq,
	setKjywrmc,
	ywControlBtn,
	gzSetSyssxdtk,
	gzSetGnsfyhxm,
	tipXm,
	verifyTips
}