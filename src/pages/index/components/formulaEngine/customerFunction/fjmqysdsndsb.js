import nsrxxForm_gjhdqDm from '/public/static/sb/fjmqysdsndsb19nd/form/nf_dm_gy_gjhdq.json'
import nsrxxForm_gjhdqDm2 from '/public/static/sb/fjmqysdsndsb19nd/form/nf_dm_gy_gjhdq2.json'


/**
 * 检验主表减免性质是否重复
 */
function checkRepeatJmxz(jxmz){
	if (isEmptyObject(jxmz)) {
		return true;
	}
	var mxlist = formData.kz_.temp.f200b.mxList;
	var mssrGrid = formData.ht_.ywbw.F200.mssrGrid.mxGridlb;
	var sdjmGrid = formData.ht_.ywbw.F200.sdjmGrid.mxGridlb;
	var dkxmGrid = formData.ht_.ywbw.F200.dkxmGrid.mxGridlb;
	var jmsdGrid = formData.ht_.ywbw.F200.jmsdGrid.mxGridlb;
	var dmsdGrid = formData.ht_.ywbw.F200.dmsdGrid.mxGridlb;
	var index_ = 0;
	var gjz = "";
	for (var i = 0; i < mxlist.length; i++) {
		if (mxlist[i].jmxzSwsxDm == jxmz) {
			index_++;
		}
		if (!isEmptyObject(mxlist[i].ewbhgjz)) {
			gjz = mxlist[i].ewbhgjz;
		}
	}

	if (index_ >= 2) {
		return false;
	}

	if (!isEmptyObject(gjz) && gjz == "mssrGrid") {

		for (var i = 0; i < sdjmGrid.length; i++) {
			if (sdjmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dkxmGrid.length; i++) {
			if (dkxmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dmsdGrid.length; i++) {
			if (dmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "sdjmGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dkxmGrid.length; i++) {
			if (dkxmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dmsdGrid.length; i++) {
			if (dmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "dkxmGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < sdjmGrid.length; i++) {
			if (sdjmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dmsdGrid.length; i++) {
			if (dmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "jmsdGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < sdjmGrid.length; i++) {
			if (sdjmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dkxmGrid.length; i++) {
			if (dkxmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dmsdGrid.length; i++) {
			if (dmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "dmsdGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < sdjmGrid.length; i++) {
			if (sdjmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < dkxmGrid.length; i++) {
			if (dkxmGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	return true;
}

function checkRepeatF210(jxmz){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611094')==-1){
		return;
	}
	if (isEmptyObject(jxmz)) {
		return true;
	}
	var mxlist = formData.ht_.ywbw.F210.gdzcGrid.xmGridlbVO;
	var index_=0;
	for (var i = 0; i < mxlist.length; i++) {
		if (mxlist[i].jmxzSwsxDm == jxmz) {
			index_++;
		}
	}
	if (index_ >= 2) {
		return false;
	}
	return true;
}

//已缴税额的计算

function getYjse(kyyjye, sqyjje, yyyjje) {
	var csz = formData.fq_.sjyjseqzBz;
	if (csz == undefined || csz == null || csz == "" || csz == "1") {
		return kyyjye + sqyjje + yyyjje;
	}
	if (csz == "2") {
		return kyyjye + sqyjje;
	}
	if (csz == "3") {
		return sqyjje + yyyjje;
	}
	if (csz == "4") {
		return kyyjye + yyyjje;
	}
	if (csz == "5") {
		return kyyjye;
	}
	if (csz == "6") {
		return sqyjje;
	}
	if (csz == "7") {
		return yyyjje;
	}
	return kyyjye + sqyjje + yyyjje;

}


/**
 * 主表38行公式
 */
function getZyjgztscjyftsdse(csztscjgcsnsrsbhArr,ztscksxzqhDmArr,fpseArr,kszjgxzqhDm,ztscjyywftsdse){
	var fzjgxx=formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	var nsrsbh=formData.ht_.ywbw.F200.nsrxxForm.nsrsbh;
	ztscjyywftsdse=isEmptyObject(kszjgxzqhDm)?"":kszjgxzqhDm;
	var key1=nsrsbh+"_"+ztscjyywftsdse;
	for(var i=0;i<fzjgxx.length;i++){
		var jgsbh=fzjgxx[i].csztscjgcsnsrsbh;
		var jgqh=isEmptyObject(fzjgxx[i].ztscksxzqhDm)?"":fzjgxx[i].ztscksxzqhDm;
		var key2=jgsbh+"_"+jgqh;
		if(key1===key2){
			return fzjgxx[i].fpse;
		}
	}

	return 0.00;
}



function getJbrxx(smzbz,csz,bz){
	var jbr=formData.ht_.ywbw.F200.nsrqtxxForm.jbr;
	var jbrsfzjhm=formData.ht_.ywbw.F200.nsrqtxxForm.jbrsfzjhm;
	if(bz=="xm"){
		return smzbz=="Y"?csz:jbr;
	}
	if(bz=="zjhm"){
		return smzbz=="Y"?csz:jbrsfzjhm;
	}
	return "";
}



/**
 * 根据初始化接口数据初始化F220表
 *
 */
function initF220Data(){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611095')==-1 ){
		return;
	}

	var f200qcslist=formData.hq_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
	var skssqq=formData.ht_.ywbw.F200.nsrxxForm.skssqq;
	var ywbwlist=formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
	var bnd=parseInt(skssqq.split("-")[0],10);
	//本年度
	formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].nd=bnd;

	if(parent.location.href.indexOf('gzsb=zx')>-1){
		for(var j=0;j<ywbwlist.length;j++){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[j].nd=bnd-(ywbwlist.length-(j+1));
		}
	}

	for(var i=0;i<f200qcslist.length;i++){
		var nd=parseInt(f200qcslist[i].nd,10);

		//前一年度
		if(nd===(bnd-1)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj  ;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前二年度
		if(nd===(bnd-2)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前三年度
		if(nd===(bnd-3)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前四年度
		if(nd===(bnd-4)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}

		//前五年度
		if(nd===(bnd-5)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}

		//前六年度
		if(nd===(bnd-6)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前七年度
		if(nd===(bnd-7)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前八年度
		if(nd===(bnd-8)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前九年度
		if(nd===(bnd-9)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}
		//前十年度
		if(nd===(bnd-10)){
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].nd=f200qcslist[i].nd;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
			formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
			if(parent.location.href.indexOf('gzsb=zx')>-1){
				//更正接口可能返回的节点
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;

				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].kjzyhndmbdksehj=isEmptyObject(f200qcslist[i].kjzyhndmbdksehj)?0:f200qcslist[i].kjzyhndmbdksehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].kjzwmbwdksjehj=isEmptyObject(f200qcslist[i].kjzwmbwdksjehj)?0:f200qcslist[i].kjzwmbwdksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].bnjwmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjwmbyjndksjehj)?0:f200qcslist[i].bnjwmbyjndksjehj;
			}
			continue;
		}

		if(parent.location.href.indexOf('gzsb=zx')>-1){
			//本年度
			if(nd===bnd){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].kshylje=isEmptyObject(f200qcslist[i].kshylje)?0:f200qcslist[i].kshylje;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].kmbdsdehj=isEmptyObject(f200qcslist[i].kmbdsdehj)?0:f200qcslist[i].kmbdsdehj;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].flzckse=isEmptyObject(f200qcslist[i].flzckse)?0:f200qcslist[i].flzckse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].hbqyzrkse=isEmptyObject(f200qcslist[i].hbqyzrkse)?0:f200qcslist[i].hbqyzrkse;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].hbqyzrkseSn=isEmptyObject(f200qcslist[i].hbqyzrkseSn)?0:f200qcslist[i].hbqyzrkseSn;
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].qysdsmbksqylxDm=isEmptyObject(f200qcslist[i].qysdsmbksqylxDm)?'100':f200qcslist[i].qysdsmbksqylxDm;
			}
		}

	}
	var _jpath1 = "ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[#].kshylje";
	var _jpath2 = "ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[#].kmbdsdehj";
	formulaEngine.apply(_jpath1,  "");
	formulaEngine.apply(_jpath2,  "");


}

/**
 *
 * F220弥补亏损表 第9列的公式
 *
 */
function getF220Col9(qylx,kshylje,skssqq,nd,index){
	var qjnd=parseInt(skssqq.split('-')[0],10)-parseInt(nd,10);
	var f220List=formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
	var bssj=f220List[10-qjnd].bnjnmbyjndksjehj;
	//11行2列的数据
	var row11col2=f220List[10].kshylje;
	//11行6列的数据
	var row11col6=f220List[10].qysdsmbksqylxDm;

	//若第11行第2列≤0，第1行至第10行第9列=0
	if(row11col2<=0&&qjnd>=1){
		return 0;
	}

	//如第11行第6列为“一般企业”，第1行至第5行第9列=0，从第6行开始录入,1到5行默认0
	if(row11col2>0&&row11col6==="100"&&qjnd>=6){
		return 0;
	}

	//本年度
	if(qjnd===0){
		var hj=0;
		for(var i=0;i<f220List.length;i++){
			hj+=f220List[i].bnjnmbyjndksjehj;
			if(i==9){
				break;
			}
		}
		return ROUND(hj,2);
	}

	return bssj;
}



/**
 *
 * F220弥补亏损表 第9列的公式
 *
 */
function getF220Col10(qylx,skssqq,nd){
	var qjnd=parseInt(skssqq.split('-')[0],10)-parseInt(nd,10);
	var f220List=formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
	var bssj=f220List[10-qjnd].bnjwmbyjndksjehj;

	//11行6列的数据
	var row11col6=f220List[10].qysdsmbksqylxDm;

	//如第11行第6列为“一般企业”，第1行至第5行第10列=0，从第6行开始录入。
	if(row11col6=="100"&&qjnd>=6){
		return 0;
	}


	//本年度
	if(qjnd===0){
		var hj=0;
		for(var i=0;i<f220List.length;i++){
			hj+=f220List[i].bnjwmbyjndksjehj;
			if(i==9){
				break;
			}
		}
		return ROUND(hj,2);
	}

	return bssj;
}

/**
 * 判断主要机构、场所名称是否能修改
 * @param sbqylx
 */
function flagZyjgcsmc(sbqylx){
	if(sbqylx==1){
		return true;
	}else if(sbqylx==2){
		if(!isEmptyObject(formData.fq_.nsrjbxxByZyjgcsdjxh.nsrmc)){
			return true;
		}else{
			return false;
		}
	}
}

/**
 * 根据初始化接口数据初始化F300表
 *
 */
function initF300Data(){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611097')==-1){
		return;
	}
	const { hznsGrid: qcshznsxxHznsGrid = {} } = formData.fq_.ytbxx.ywbw.F300
	var  zyjgxx=formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	var  hznsxx=formData.ht_.ywbw.F300.hznsGrid.hznsGridlb;
	var qcsJgxx = formData.fq_.ytbxx.ywbw.F300.ztscGrid.ztscGridlb;
	var qcshznsxx = qcshznsxxHznsGrid.hznsGridlb;
	var kszjgxzqhDm=formData.hq_.f300.F300Form.kszjgxzqhDm;
	var zyjgcsdjxh=formData.hq_.f300.F300Form.zyjgcsdjxh;
	var zgswskfjDm=formData.hq_.f300.F300Form.zgswskfjDm;
	var zgswjDm=formData.hq_.f300.F300Form.zgswjDm;
	var ssglyDm=formData.hq_.f300.F300Form.ssglyDm;
	var zyjgcsnsrsbh=formData.hq_.f300.F300Form.zyjgcsnsrsbh;
	var zyjgcsmc=formData.hq_.f300.F300Form.zyjgcsmc;


	formData.ht_.ywbw.F300.F300Form.kszjgxzqhDm=isEmptyObject(kszjgxzqhDm)?"":kszjgxzqhDm;
	formData.ht_.ywbw.F300.F300Form.zyjgcsxzqhMc=getDmFromCodeTable([{'url':'dm_sb_shi.json','name':'sjCT','node':'','dm':'','mc':'','filter':''}],'sjCT',kszjgxzqhDm,'');
	formData.ht_.ywbw.F300.F300Form.zyjgcsdjxh=isEmptyObject(zyjgcsdjxh)?"":zyjgcsdjxh;
	formData.ht_.ywbw.F300.F300Form.zgswskfjDm=isEmptyObject(zgswskfjDm)?"":zgswskfjDm;
	formData.ht_.ywbw.F300.F300Form.zgswjDm=isEmptyObject(zgswjDm)?"":zgswjDm;
	formData.ht_.ywbw.F300.F300Form.ssglyDm=isEmptyObject(ssglyDm)?"":ssglyDm;
	formData.ht_.ywbw.F300.F300Form.zyjgcsnsrsbh=isEmptyObject(zyjgcsnsrsbh)?"":zyjgcsnsrsbh;
	formData.ht_.ywbw.F300.F300Form.zyjgcsmc=isEmptyObject(zyjgcsmc)?"":zyjgcsmc;

	if(qcsJgxx != null && qcsJgxx != undefined && qcsJgxx != "" && qcsJgxx.length > 0){

		for(var i=0;i<qcsJgxx.length;i++){
			var  voObj ={"ztscksxzqhMc":"","sbuuid": "","fpbuuid": "",
				"pzxh": "","ztscdjxh": "","csztscjgcsnsrsbh": "","csztscjgcsnsrmc": "",
				"ztscksxzqhDm": "","ztscsrze": 0,"ztscgzze": 0,"zgswskfjDm": "",
				"zgswjDm": "","ssglyDm": "","ztsczcze": 0,"fpbl": 0,"fpse": 0,"flag":false};
			if(zyjgxx[i] == null || zyjgxx[i] == undefined || zyjgxx[i] == ""){
				zyjgxx[i]=voObj;
			}
			zyjgxx[i].ztscdjxh=qcsJgxx[i].ztscdjxh;
			zyjgxx[i].csztscjgcsnsrsbh=qcsJgxx[i].csztscjgcsnsrsbh;
			zyjgxx[i].csztscjgcsnsrmc=qcsJgxx[i].csztscjgcsnsrmc;
			zyjgxx[i].ztscksxzqhDm=qcsJgxx[i].ztscksxzqhDm;
			zyjgxx[i].ztscksxzqhMc=getDmFromCodeTable([{'url':'dm_sb_shi.json','name':'sjCT','node':'','dm':'','mc':'','filter':''}],'sjCT',zyjgxx[i].ztscksxzqhDm,'');
			zyjgxx[i].zgswskfjDm=qcsJgxx[i].zgswskfjDm;
			zyjgxx[i].zgswjDm=qcsJgxx[i].zgswjDm;
			zyjgxx[i].flag=true;
			zyjgxx[i].ssglyDm=qcsJgxx[i].ssglyDm;
			zyjgxx[i].ztscsrze=qcsJgxx[i].ztscsrze;
			zyjgxx[i].ztscgzze=qcsJgxx[i].ztscgzze;
			zyjgxx[i].ztsczcze=qcsJgxx[i].ztsczcze;

		}

	}


	if(qcshznsxx != null && qcshznsxx != undefined && qcshznsxx != "" && qcshznsxx.length > 0){

		for(var i=0;i<qcshznsxx.length;i++){
			var  voObj = {"hznsksxzqhMc": "","pzxh": "","sbuuid": "","fpbuuid": "",
				"hznsjgcsdjxh": "","hznsjgcsnsrsbh": "","hznsjgcsnsrmc": "","hznsksxzqhDm": "",
				"hznscbfye": 0,"zgswskfjDm": "","zgswjDm": "","ssglyDm": "","hznszce": 0,"flag":false} ;
			if(hznsxx[i] == null || hznsxx[i] == undefined || hznsxx[i] == ""){
				hznsxx[i]=voObj;
			}
			hznsxx[i].hznsjgcsdjxh=qcshznsxx[i].hznsjgcsdjxh;
			hznsxx[i].hznsjgcsnsrsbh=qcshznsxx[i].hznsjgcsnsrsbh;
			hznsxx[i].hznsjgcsnsrmc=qcshznsxx[i].hznsjgcsnsrmc;
			hznsxx[i].hznsksxzqhDm=qcshznsxx[i].hznsksxzqhDm;
			hznsxx[i].hznsksxzqhMc=getDmFromCodeTable([{'url':'dm_sb_shi.json','name':'sjCT','node':'','dm':'','mc':'','filter':''}],'sjCT',hznsxx[i].hznsksxzqhDm,'');
			hznsxx[i].hznscbfye=qcshznsxx[i].hznscbfye;
			hznsxx[i].zgswskfjDm=qcshznsxx[i].zgswskfjDm;
			hznsxx[i].zgswjDm=qcshznsxx[i].zgswjDm;
			hznsxx[i].ssglyDm=qcshznsxx[i].ssglyDm;
			hznsxx[i].hznszce=qcshznsxx[i].hznszce;
			hznsxx[i].flag=true;

		}

	}

}



/**
 * 设置分配比例
 * @returns
 */
function setFpbl(yysrhj,zgxchj,zczehj){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611097')==-1){
		return;
	}
	var ztscGridlb=formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	//分配比例总和
	var sumFpbl=0;
	//用来标识是否存在独立部门
	var flagIndex=-1;

	if(ztscGridlb.length==1&&ztscGridlb[0].ztscsrze==0&&ztscGridlb[0].ztscgzze==0&&ztscGridlb[0].ztsczcze==0){
		ztscGridlb[0].fpbl=0;
		var _jpath2 = "ht_.ywbw.F300.ztscGrid.ztscGridlb[#].fpbl";
		formulaEngine.apply(_jpath2,  "");
		return ;
	}

	for(var i=0;i<ztscGridlb.length;i++){
		if(ztscGridlb[i].qtjglx=="dlbm"){
			flagIndex=i;
		}
		var yysrFpbl=ROUND((ztscGridlb[i].ztscsrze/yysrhj)*0.35,16);

		var zgxcFpbl=ROUND((ztscGridlb[i].ztscgzze/zgxchj)*0.35,16);

		var zczeFpbl=ROUND((ztscGridlb[i].ztsczcze/zczehj)*0.30,16);

		ztscGridlb[i].fpbl=ROUND(yysrFpbl+zgxcFpbl+zczeFpbl,10);

		sumFpbl+=ztscGridlb[i].fpbl;
	}

	if(flagIndex==-1){
		ztscGridlb[ztscGridlb.length-1].fpbl=ROUND((1-(sumFpbl-ztscGridlb[ztscGridlb.length-1].fpbl)),10);
	}else{
		ztscGridlb[flagIndex].fpbl=ROUND((1-(sumFpbl-ztscGridlb[flagIndex].fpbl)),10);
	}

	//formData.kz_.temp.f300Fpb.fpblhj=sumFpbl;

	var _jpath2 = "ht_.ywbw.F300.ztscGrid.ztscGridlb[#].fpbl";
	formulaEngine.apply(_jpath2,  "");
}

/**
 *设置分配所得税额
 */
function setFpse(ztscjyywftsdse,fpblhj){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611097')==-1){
		return;
	}
	var ztscGridlb=formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	//用来标识是否存在独立部门
	var flagIndex=-1;
	//分配所得税额总和
	var sumFpse=0;


	if(ztscGridlb.length==1&&ztscGridlb[0].ztscsrze==0&&ztscGridlb[0].ztscgzze==0&&ztscGridlb[0].ztsczcze==0){
		ztscGridlb[0].fpse=0;
		var _jpath2 = "ht_.ywbw.F300.ztscGrid.ztscGridlb[#].fpse";
		formulaEngine.apply(_jpath2,  "");
		return ;
	}


	for(var i=0;i<ztscGridlb.length;i++){
		ztscGridlb[i].fpse=ROUND(ztscGridlb[i].fpbl*ztscjyywftsdse,2);
		if(ztscGridlb[i].qtjglx=="dlbm"){
			flagIndex=i;
		}
		sumFpse+=ztscGridlb[i].fpse;
	}

	if(flagIndex==-1){
		ztscGridlb[ztscGridlb.length-1].fpse=ROUND((ztscjyywftsdse-(sumFpse-ztscGridlb[ztscGridlb.length-1].fpse)),2);
	}else{
		ztscGridlb[flagIndex].fpse=ROUND((ztscjyywftsdse-(sumFpse-ztscGridlb[flagIndex].fpse)),2);
	}

	var _jpath1 = "ht_.ywbw.F300.ztscGrid.ztscGridlb[#].fpse";
	formulaEngine.apply(_jpath1,  "");
}
/**
 * 非空校验
 * @param obj
 * @returns {Boolean}
 */
function isEmptyObject(obj){
	if(obj===""||obj===null||obj===undefined){
		return true;
	}else{
		return false;
	}
}

/**
 * 根据初始化接口数据初始化F400表
 *
 */
function initF400Data(){
	var dzbd=formData.kz_.dzbd;
	if(!isEmptyObject(dzbd)&&dzbd.indexOf('BDA0611098')==-1){
		return;
	}
	var qcsfbxx = formData.hq_.ywbw.F400;
	var fbxx = formData.ht_.ywbw.F400.Fb400Grid.Fb400Gridlb;
	var zsfsDm=formData.fq_.zsfsDm;
	if(zsfsDm!="405"){
		return ;
	}

	if (!isEmptyObject(qcsfbxx)) {
		var qcsmx = qcsfbxx.Fb400Grid.Fb400Gridlb;
		if (!isEmptyObject(qcsmx)) {
			formData.kz_.temp.f400Hdjsmxb.qcsfhbz="Y"
			for (var i = 0; i < qcsmx.length; i++) {
				var vo = '{"xmmc": "","asrzehdsre": 0,"asrzehdhdlrl": 0,"asrzehdynssde": 0}';
				var voObj = eval('(' + vo + ')');
				if (isEmptyObject(fbxx[i])) {
					fbxx[i] = voObj;
				}
				fbxx[i].xmmc = qcsmx[i].xmmc || '';
				fbxx[i].asrzehdhdlrl = qcsmx[i].asrzehdhdlrl;
				fbxx[i].asrzehdsre = qcsmx[i].asrzehdsre;
			}
		}
	}

}


function getF400lrl(index,xmmc){
	var index= index.replace(/[^0-9]/ig,"");
	var zsfsDm=formData.fq_.zsfsDm;
	var lrl=formData.fq_.lrl;
	var qcsfhbz=formData.kz_.temp.f400Hdjsmxb.qcsfhbz;
	if(zsfsDm!="405"){
		return 0 ;
	}

	if(qcsfhbz=="N"){
		return lrl;
	}

	if(qcsfhbz=="Y"){
		var fbxx = formData.hq_.ywbw.F400.Fb400Grid.Fb400Gridlb;
		if(!isEmptyObject(xmmc)){
			for(var i=0;i<fbxx.length;i++){
				if(fbxx[i].xmmc===xmmc){
					return fbxx[i].asrzehdhdlrl;
				}
			}
		}else{
			if(index<fbxx.length){
				return fbxx[index].asrzehdhdlrl;
			}
		}

	}

}


function getSjynsdsejssb(sbqylx,jsfs,cs){

	if(jsfs=='2'){
		return 0;
	}

	if(jsfs=='1'&&(sbqylx=="0"||sbqylx=="1")){
		return cs;
	}

	if(jsfs=='1'&&sbqylx=="2"){
		var sjynsdsejssb= formData.ht_.ywbw.F200.sbbxxForm.sjynsdsejssb;
		return sjynsdsejssb;
	}

	return 0;
}


/**
 * 在中国的联系地址、邮政编码：联系地址与邮政编码之间用分号隔开，联系地址长度100个字符，邮政编码长度6个字符
 * 在居民国（地区）的联系地址、邮政编码：联系地址与邮政编码之间用分号（中文分号）隔开，总长度不超过107字符。
 */
function checkLxdqyzbm(lxdqyzbm,bz){
	if(isEmptyObject(lxdqyzbm)){
		return false;
	}else if((lxdqyzbm).lastIndexOf("；")==-1){
		return false;
	}else{
		var lastIndexOf=lxdqyzbm.lastIndexOf("；");
		var yzbm=lxdqyzbm.substring(lastIndexOf+1);
		var lxdz=lxdqyzbm.substring(-1,lastIndexOf);
		if(bz=='zg'){
			var regYzbm=/^\d{6}$/;
			if(lxdz.length<=100&&regYzbm.test(yzbm)){
				return true;
			}else{
				return false;
			}
		}else{
			if(lxdqyzbm.length<=107&&yzbm.length>0){
				return true;
			}else{
				return false;
			}
		}
	}
}


/**
 * 检验电子邮箱
 * @param dzxx
 * @returns {Boolean}
 */
function checkDzxx(dzxx){
	//var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})$/;
	//取消协定附表[10.电子邮箱]中对点后域名的校验
	var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)/;
	if(reg.test(dzxx)){
		return true;
	}else{
		return false;
	}
}

/**
 * 设置享受协定待遇所得金额
 */
function setXsxddysdje(ynssdejssb,ynssdehdzs,jsfs){
	if(jsfs==1){
		return ynssdejssb;
	}else if(jsfs==2){
		return ynssdehdzs;
	}
	return 0;
}

/**
 * 设置享受协定待遇减免税额
 * @param xsxddyjmsejssb
 * @param xsxddyjmsehdzs
 * @param jsfs
 * @returns
 */
function setXsxddyjmse(xsxddyjmsejssb,xsxddyjmsehdzs,jsfs){
	if(jsfs==1){
		return xsxddyjmsejssb;
	}else if(jsfs==2){
		return xsxddyjmsehdzs;
	}
	return 0;
}



/**
 * 提交时对300表报文做特殊处理
 */
function del300bdBw(){
	var fb300bw = formData.ht_.ywbw.F300;
	if (isEmptyObject(fb300bw)) {
		return;
	}
	var fzjgxxList = formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	var hzjgxxList = formData.ht_.ywbw.F300.hznsGrid.hznsGridlb;
	var fzjgxxNew = [];
	var hzjgxxNew = [];
	for (var i=0; i < fzjgxxList.length; i++) {
		if (fzjgxxList[i].csztscjgcsnsrsbh != '' && fzjgxxList[i].csztscjgcsnsrsbh != null
			&& fzjgxxList[i].csztscjgcsnsrsbh != undefined) {
			fzjgxxNew.push(fzjgxxList[i]);
		}
	}
	for (var i=0; i < hzjgxxList.length; i++) {
		if (hzjgxxList[i].hznsjgcsnsrsbh != ''
			&& hzjgxxList[i].hznsjgcsnsrsbh != null
			&& hzjgxxList[i].hznsjgcsnsrsbh != undefined) {
			hzjgxxNew.push(hzjgxxList[i]);
		}
	}

	if(isEmptyObject(fzjgxxNew)|| fzjgxxNew.length==0){
		delete	formData.ht_.ywbw.F300.ztscGrid.ztscGridlb;
	}else{
		formData.ht_.ywbw.F300.ztscGrid.ztscGridlb=fzjgxxNew;
	}

	if(isEmptyObject(hzjgxxNew)|| hzjgxxNew.length==0){
		delete	formData.ht_.ywbw.F300.hznsGrid.hznsGridlb;
	}else{
		formData.ht_.ywbw.F300.hznsGrid.hznsGridlb=hzjgxxNew;
	}

}

/**
 * 提交时对协定待遇表报文做特殊处理
 */
function delXddybdBw(){
	var xddybw = formData.ht_.ywbw.xddy;
	if (isEmptyObject(xddybw)) {
		return;
	}
	var dzbd=formData.kz_.dzbd;
	var xsxddyjmsejssb=formData.ht_.ywbw.F200.sbbxxForm.xsxddyjmsejssb;
	var xsxddyjmsehdzs=formData.ht_.ywbw.F200.sbbxxForm.xsxddyjmsehdzs;
	var xsxddyjmse=formData.ht_.ywbw.xddy.xddyxx.xsxddyjmse;
	if(xsxddyjmsejssb<=0&&xsxddyjmsehdzs<=0&&xsxddyjmse<=0){
		delete formData.ht_.ywbw.xddy;
		var xdbd="BDA0611121---";
		if(dzbd.indexOf(xdbd)>-1){
			var star=dzbd.indexOf(xdbd);
			var end=star+xdbd.length;
			var dzbdStr=dzbd.substring(0,star)+dzbd.substring(end);
			formData.kz_.dzbd=dzbdStr;
		}
	}
}

/**
 * 提交时对F210表报文做特殊处理
 */
function delF210bdBw(){
	var f210bw = formData.ht_.ywbw.F210;
	if (isEmptyObject(f210bw)) {
		return;
	}
	var mxlist=	formData.ht_.ywbw.F210.gdzcGrid.xmGridlbVO;
	var mxNew = [];
	for (var i=0; i < mxlist.length; i++) {
		if (!isEmptyObject(mxlist[i].jmxzSwsxDm)) {
			mxNew.push(mxlist[i]);
		}
	}

	if(isEmptyObject(mxNew)|| mxNew.length==0){
		delete	formData.ht_.ywbw.F210.gdzcGrid.xmGridlbVO;
	}else{
		formData.ht_.ywbw.F210.gdzcGrid.xmGridlbVO=mxNew;
	}

}

/**
 * 根据初始化接口数据初始化F300表
 *
 */
function initF230Data(){
	var f230bw = formData.ht_.ywbw.F230;
	var f230qcs = formData.hq_.ywbw.F230;
	if (isEmptyObject(f230bw) ||isEmptyObject(f230qcs)) {
		return;
	}
	var mxlist =f230bw.kcqkxxGrid.kcqkxxGridlb;
	var qcsForm=f230qcs.F230Form;
	if (isEmptyObject(qcsForm)) {
		return;
	}

	var index=1;

	do {
		var falg=false;
		var kcqkmc="htqh"+index;
		var ktzyf="ktfyzcktzyfahtblftktzyf"+index;
		var ktjklx="ktfyzcktjklxahtblftfy"+index;
		var  ktjyglf="ktfyzcjyglfahtftfy"+index;
		var  ktsjglf="ktfyzcsjglfahtftfy"+index;
		var  kfzyf="kffyzckfzyfahtftfy"+index;
		var  kfjklx="kffyzckfjklxahtftfy"+index;
		var  kfjyglf="kffyzcjyglfahtftfy"+index;
		var  kfsjglf="kffyzcsjglfahtftfy"+index;
		var  ktfyytxe="asfgdktfyytxahtftfy"+index;
		var  kffyyzje="asfgdkffyyzjahtftfy"+index;
		var  ktfydtxe="asfgdktfydtxahtftfy"+index;
		var  kffydzje="asfgdkffydzjahtftfy"+index;
		var i=index-1;
		var vo='{"kcqkmc": "","ktzyf": 0,"ktjklx": 0,"ktjyglf": 0,"ktsjglf": 0,"ktfyzcehj": 0,"kfzyf": 0,"kfjklx": 0,"kfjyglf": 0,"kfsjglf": 0,"kffyzcehj": 0,"ktkfzczj": 0,"ktfyytxe": 0,"kffyyzje": 0,"ktfydtxe": 0,"kffydzje": 0}';
		var voObj=eval('('+vo+')')

		if(!isEmptyObject(qcsForm[kcqkmc])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kcqkmc=qcsForm[kcqkmc];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktzyf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktzyf=qcsForm[ktzyf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktjklx])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktjklx=qcsForm[ktjklx];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktjyglf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktjyglf=qcsForm[ktjyglf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktsjglf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktsjglf=qcsForm[ktsjglf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kfzyf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kfzyf=qcsForm[kfzyf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kfjklx])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kfjklx=qcsForm[kfjklx];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kfjyglf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kfjyglf=qcsForm[kfjyglf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kfsjglf])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kfsjglf=qcsForm[kfsjglf];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktfyytxe])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktfyytxe=qcsForm[ktfyytxe];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kffyyzje])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kffyyzje=qcsForm[kffyyzje];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[ktfydtxe])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].ktfydtxe=qcsForm[ktfydtxe];
			falg=true;
		}

		if(!isEmptyObject(qcsForm[kffydzje])){
			if(isEmptyObject(mxlist[i])){
				mxlist[i]=voObj;
			}
			mxlist[i].kffydzje=qcsForm[kffydzje];
			falg=true;
		}


		index++;
	}while (falg);

}

/**
 *
 * 企业类型为“单独纳税机构、场所”的纳税人在税款所属期为2020年1月1日之前不允许使用
 * 《非居民企业所得税预缴申报》（2019年版）》，对应年报请使用2015版本的申报表申报，谢谢！
 */
function sfyxsb(){
	var skssqq=formData.ht_.ywbw.F200.nsrxxForm.skssqq;
	var sbqylx=formData.ht_.ywbw.F200.nsrxxForm.sbqylx;
	var nd=(skssqq).split("-")[0];
	if(nd=="2019"&&sbqylx=="0"){
		var msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;企业类型为“单独纳税机构、场所”的纳税人在税款所属期为2020年1月1日之前不允许使用非居民企业所得税预缴申报（2019年版），对应年报请使用2015版本的申报表申报，谢谢！ ";
		var a =layer.confirm(msg,{
			area: ['340px','260px'],
			title:'提示',
			closeBtn : 0,
			btn : ['确定']
		},function(data){
			layer.close(a);
			closeWin();
		});

	}

}

/**
 * 关闭当前页面
 */

function closeWin() {

	if (navigator.userAgent.indexOf("MSIE") > 0) {
		if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
			window.opener = null;
			window.close();
		} else {
			window.open('', '_top');
			window.top.close();
		}
	} else if (navigator.userAgent.indexOf("Firefox") > 0) {
		window.location.href = 'about:blank ';
		window.close();
	} else if (navigator.userAgent.indexOf("Chrome") > 0) {
		top.open(location, '_self').close();
	} else {
		window.open('', '_top');
		window.top.close();
	}

}

function syncFetch(_url, _async, data) {
	let res;
	const xhr = new XMLHttpRequest();
	xhr.open('GET', _url, _async);
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			res = JSON.parse(xhr.response);
		}
	};

	xhr.send(JSON.stringify(data));
	return res;
}

/**
 * 从码表中获取值
 * @param codeTableAttrs 初始化码表需要的参数（参照ng-codetable把属性改成json对象，如果ng-codetable含有contact需要传入数组，统一都传入数组）
 * @param name 码表对象的名称（含有contact传入数组需要知道取最终码表对象的名称）
 * @param key 码表中对应dm的值，根据该值获取相应的value值
 * @param fieldName 码表中对应dm的value值是Object，根据该值获取Object中fieldName对应的值（可以不传）
 *
 * 实现步骤
 *  1、根据传入attributes初始化码表
 *  2、根据传入的name和key查找码表中相应key的值
 */
function getDmFromCodeTable(codeTableAttrs, name, key, fieldName, defVal) {
    if (defVal == undefined) {
        defVal = "";
    }
    
	var callBack = function() {
		var codeObject = formCT[name];
		if (undefined === codeObject) {
			return defVal;
		} else {
			var codeValue = codeObject[key];
			if (codeValue && fieldName) {
				codeValue = codeValue[fieldName];
			}

			if (!codeValue) {
				codeValue = defVal;
			}

			return codeValue;
		}
	}

	if (formCT[name]) {
		return callBack();
	} else {
		if (codeTableAttrs && codeTableAttrs.length > 0) {
			//初始化码表
			initCodeTable(undefined, codeTableAttrs[0]);
			return callBack();
		} else {
			return defVal;
		}
	}
}

/**
 * 初始化码表
 * 从码表数据来源层面来说支持三种方式的码表
 *  1、码表数据来源于url（json文件和普通的getDmb.do）
 *  2、码表数据来源于期初数model
 *  3、码表数据来源于带参数的请求params（/nssb/getDtdmb.do）
 * 码表请求支持同步异步配置async（默认为异步true）
 * 码表支持累加contact，对于两个来源的数据码表的name一样，最后的结果会做合并操作
 * @param $scope
 * @param attributes
 */
function initCodeTable($scope, attributes) {
    var _name = attributes["name"];
    var _url = attributes["url"];
    var _model = attributes["model"];
    var _params = attributes["params"];
    var _jsons = {};
   
    if (undefined === formCT[_name] || JSON.stringify(formCT[_name]) === "{}") {//判断是否已缓存
        if ((undefined !== _url && "" !== _url) || (undefined !== _params && "" !== _params)) {//URL来源
            getDmFromUrl($scope, attributes, _jsons);
        } else if (undefined !== _model && "" !== _model) {//期初数来源
            getDmFromModel($scope, attributes, _jsons);
        } else {
            //codetable指令相关参数缺失
            console.log("ERROR:codetable指令相关参数缺失，请检查...");
            return;
        }
    }
}

/**
 * 从url（json文件和普通的getDmb.do）获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromUrl($scope, attributes, _jsons) {
    // 默认为同步
    var _async = false;
    var _node = attributes["node"];
    var _name = attributes["name"];
    var _url = attributes["url"];

    var _params = attributes["params"];
    var _dynamicParam = attributes["dynamic"];
    var _data;

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    var data = {};
	var index = location.pathname.lastIndexOf('/');
	_url = location.pathname.substr(0, index) + "/static/sb/fjmqysdsndsb19nd/form/" + _url;
        
	// 允许添加参数，param为key, dynamicParam为value，可以多个逗号隔开，但个数必须一致
	if (_params && _dynamicParam) {
		var aryParam = _params.split(',');
		var aryDynamic = _dynamicParam.split(',');
		if (aryParam && aryDynamic && aryDynamic.length === aryDynamic.length) {
			for (var idx = 0; idx < aryParam.length; idx++) {
				_data = jsonPath($scope ? $scope.formData : formData, aryDynamic[idx])[0];
				if (_data) {
					_url = _url + (idx === 0 ? "?" : "&") + aryParam[idx] + "=" + _data;
				} else {
					// 发现有不符合的，则不进行拼接
					break;
				}
			}
		}
	}

	const response = syncFetch(_url, _async, data);
	_data = response;
	if (undefined === _url || "" === _url) {
		if (typeof response === "string") {
			response = JSON.parse(response);
		}
		if (response && response["dtdmbxx"]) {
			response = response["dtdmbxx"];
		}
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}
		if (typeof _data === "string") {
			_data = JSON.parse(_data);
		}
		if (_data && _data["root"]) {
			Object.keys(_data).forEach((k)=> {
				const v = _data[k];
				_jsons[v["dm"]] = v;
			});
			_data = _jsons;
		}
	} else {
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}

		var doFilter = false;
		Object.keys(_data).forEach((k)=> {
			const v = _data[k];
			doFilter = false;
			if (filterKey && filterValue && v) {
				if (v[filterKey] !== filterValue) {
					doFilter = true;
				}
			}
			if (!doFilter) {
				_jsons[k] = v;
			}
		});
	}

	formCT[_name] = _jsons;
}

/**
 * 从期初数model获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromModel($scope, attributes, _jsons) {
    var _name = attributes["name"];
    var _multi = attributes["multi"];
    var _data;

    var _model = attributes["model"];
    var _dm = attributes["dm"];

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    _data = jsonPath($scope ? $scope.formData : formData, _model)[0];
    if (undefined === _data || "" === _data) {
        console.log("ERROR:codetable指令缓存代码表获取的data为空，对应的model为:" + _model + ",name为:" + _name);
        return;
    }

    if (undefined !== _dm && "" !== _dm) {
        var doFilter = false;
        Object.keys(_data).forEach((k)=> {
			const v = _data[k];
            doFilter = false;
            if (filterKey && filterValue) {
                if (v[filterKey] !== filterValue) {
                    doFilter = true;
                }
            }
            if (!doFilter) {
                if (_multi === 'true') {
                    if (!_jsons[v[_dm]])
                        _jsons[v[_dm]] = [];
                    _jsons[v[_dm]].push(v);
                } else {
                    _jsons[v[_dm]] = v;
                }
            }
        });
        _data = _jsons;
    }

	formCT[_name] = _data;
}


/*function afterFormulaExcuted(){
	var swjgDm = formData.fq_.nsrjbxx.swjgDm;
	var subSwjgDm = swjgDm.substring(1,3);
	if(subSwjgDm=="52"){
		var br2space = "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
		var content = "<div class=\"layui-text\"  style=\"font-size:14px\">尊敬的纳税人：\n" +
			br2space + " “非居民企业所得税年度纳税申报”模块，适用于非居民纳税人在一个纳税年度内按季自行预缴申中报后，在次年5月31日前进行的年度纳税申报。您是否为非居民纳税人并已完成按季自行预缴申报。\n" + '</div>';
		layer.confirm(content,{
			area: ['400px', '300px'],
			title: '提示',
			btn: ["是", "否"],
			btn2: function (index) {
				layer.close(index);
				var msg="请您及时联系主管税务机关办理相应的涉税事宜。";
				var b = parent.layer.open({
					type: 1,
					btn: ['确定'],
					title: "提示",
					content: msg,
					area: ['400px', '300px'],
					closeBtn: 0,
					yes: function (index, layero) {
						parent.layer.close(b);
						closeWin();
					}
				});

			}
		}, function (index) {
			layer.close(index);
		});


	}

}*/

function afterFormulaExcuted(){
	var dzbd = formData.kz_.dzbd;
	var gzbz = formData.kz_.temp.gzsb;
	var skssqq=formData.ht_.ywbw.F200.nsrxxForm.skssqq;
	var jpathList = [];
	if(dzbd.indexOf("BDA0611095") > -1 && !isEmptyObject(gzbz) && gzbz == 'Y'){
		var f200qcslist=formData.hq_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
		var ywbwlist=formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb;
		var bnd=parseInt(skssqq.split("-")[0],10);

		for(var i=0;i<f200qcslist.length;i++){
			var nd=parseInt(f200qcslist[i].nd,10);
			//前一年度
			if(nd===(bnd-1)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[9].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前二年度
			if(nd===(bnd-2)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[8].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前三年度
			if(nd===(bnd-3)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[7].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前四年度
			if(nd===(bnd-4)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[6].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}

			//前五年度
			if(nd===(bnd-5)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[5].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}

			//前六年度
			if(nd===(bnd-6)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[4].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前七年度
			if(nd===(bnd-7)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[3].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前八年度
			if(nd===(bnd-8)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[2].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前九年度
			if(nd===(bnd-9)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[1].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}
			//前十年度
			if(nd===(bnd-10)){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[0].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
				continue;
			}

			//本年度
			if(nd===bnd){
				formData.ht_.ywbw.F220.qysdsmbksmxbGrid.qysdsmbksmxbGridlb[10].bnjnmbyjndksjehj=isEmptyObject(f200qcslist[i].bnjnmbyjndksjehj)?0:f200qcslist[i].bnjnmbyjndksjehj;
			}
		}
		jpathList.push("ht_.ywbw.A106000.mbksmxbGrid.mbksmxbGridlb[#].bnjnmbyjndksjehj");
	}
	if(jpathList.length>0){
		formulaEngine.apply4List(jpathList);
	}
}

// 将应补退税额同步给vue外壳
function postYbtse() {
	var ybtsdseLj = formData.ht_.ywbw.F200.sbbxxForm.ybtsdsejssb;
	var message = {"type":"ybtse", "ywbm":parent.ywbm, "ybtse":ybtsdseLj};
	parent.postMessage2Vue(message);
}

function handleJmgdqszDm(jmgdqszDm){
	var numericRegex = /^[0-9]+$/;
	var isNumberDm = numericRegex.test(jmgdqszDm);
	if(!isNumberDm) return jmgdqszDm
	var numberGjhdqDm = nsrxxForm_gjhdqDm2;
	var strGjhdqDm = nsrxxForm_gjhdqDm;
	var mc = numberGjhdqDm[jmgdqszDm]
	Object.keys(strGjhdqDm).forEach((item)=>{
		strGjhdqDm[item].includes(mc) ? jmgdqszDm = item : ''
	})
	return jmgdqszDm
}

export default {
	checkRepeatJmxz,
	checkRepeatF210,
	getYjse,
	getZyjgztscjyftsdse,
	getJbrxx,
	initF220Data,
	getF220Col9,
	getF220Col10,
	flagZyjgcsmc,
	initF300Data,
	setFpbl,
	setFpse,
	isEmptyObject,
	initF400Data,
	getF400lrl,
	getSjynsdsejssb,
	checkLxdqyzbm,
	checkDzxx,
	setXsxddysdje,
	setXsxddyjmse,
	del300bdBw,
	delXddybdBw,
	delF210bdBw,
	initF230Data,
	sfyxsb,
	closeWin,
	getDmFromCodeTable,
	afterFormulaExcuted,
	postYbtse,
	handleJmgdqszDm
}