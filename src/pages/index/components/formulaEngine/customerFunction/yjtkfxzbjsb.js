import jsonPath from '@/pages/index/utils/jsonpath.js';
/**
 * 验证是否有未填报品目信息
 */
function sfywtbpm(zspmDm) {
	let htMap = {};
	let hqSbxxGridlb = "";
	try {
        hqSbxxGridlb = formData.hq_.yjfxzbjzsbzGrid.yjfxzbjzsbzGridlb;
    }catch (e) {
        hqSbxxGridlb = formData.hq_.yjfxzbjzsbzGrid.yjfxzbjzsbzGridlb;
	}
	let htSbxxGridlb = formData.ht_.ywbw.yjtkfxzbjsb.sbxxGrid.sbxxGridlb;
	for(let i in htSbxxGridlb){
		htMap[htSbxxGridlb[i].zspmDm] = "Y";
	}
	for(let i in hqSbxxGridlb){
		if(htMap[hqSbxxGridlb[i].zspmDm] !== "Y"){
			return false;
		}
	}
	return true;
}

/**
 * 根据识别号查名称
 */
function getMcBySbh(sbh) {
	let nsrmc = "";
	if (!isNull(sbh)) {
		$.ajax({
			headers: {
				"AuthorizationMock": parent.token,
				"contentType": "application/json"
			},
			type: "POST",
			contentType: "application/json",
			url: parent.ctrl + "/fsgjssb/yjtkfxzbjsb/v1/getMcBySbh",
			dataType: "json",
			async: false,
			data: JSON.stringify({
				"Jbrsbh": sbh
			}),
			success: function (data) {
				if (data.Response.Error !== null && data.Response.Error.Message !== null) {
					parent.layer.alert("获取纳税人名称失败，失败原因：" + data.Response.Error.Message, {
						title: "提示",
						icon: 5
					});
					return;
				}
				let object = data.Response.Data;
				let body = jQuery.parseJSON(object.Body);
				if (!isNull(body.djnsrxx) && body.djnsrxx.length > 0) {
					nsrmc = data.djnsrxx[0].nsrmc;
				}
			},
			error: function (xhr, textStatus, errorThrown) {
				console.log("getMcBySbh请求" + textStatus + "异常: " + errorThrown);
			}
		});
	}
	return nsrmc;
}

/**
 * 申报成功的依申请明细信息保存数据,
 */
function setYsqxxSb(){
	let sbxxGridlb = formData.ht_.ywbw.yjtkfxzbjsb.sbxxGrid.sbxxGridlb;
	formData.kz_.ysqxx={};
	let ysqxx = {};
	let sbmxlist = [];
	let item= {};
	item.jsyj = 0;
	item.ynse = 0;
	item.ybtse = 0;
	for (let i=0;i<sbxxGridlb.length;i++){
		item.jsyj = item.jsyj + sbxxGridlb[i].xsds;
		item.ynse = item.ynse + sbxxGridlb[i].bqynsfe;
		item.ybtse = item.ybtse + sbxxGridlb[i].bqybtsfe;
	}
	item.zsxmDm = "30224";
	sbmxlist[0] = item;
	ysqxx.yzpzzlDm = "BDA0611056";
	ysqxx.yzpzmc = "油价调控风险准备金申报";
	ysqxx.sbmxlist = sbmxlist;
	formData.kz_.ysqxx = ysqxx;
}

function syncFetch(_url, _async, data) {
	let res;
	const xhr = new XMLHttpRequest();
	xhr.open('GET', _url, _async);
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			res = JSON.parse(xhr.response);
		}
	};

	xhr.send(JSON.stringify(data));
	return res;
}

/**
 * 从码表中获取值
 * @param codeTableAttrs 初始化码表需要的参数（参照ng-codetable把属性改成json对象，如果ng-codetable含有contact需要传入数组，统一都传入数组）
 * @param name 码表对象的名称（含有contact传入数组需要知道取最终码表对象的名称）
 * @param key 码表中对应dm的值，根据该值获取相应的value值
 * @param fieldName 码表中对应dm的value值是Object，根据该值获取Object中fieldName对应的值（可以不传）
 *
 * 实现步骤
 *  1、根据传入attributes初始化码表
 *  2、根据传入的name和key查找码表中相应key的值
 */
function getDmFromCodeTable(codeTableAttrs, name, key, fieldName, defVal) {
    if (defVal == undefined) {
        defVal = "";
    }
    
	var callBack = function() {
		var codeObject = formCT[name];
		if (undefined === codeObject) {
			return defVal;
		} else {
			var codeValue = codeObject[key];
			if (codeValue && fieldName) {
				codeValue = codeValue[fieldName];
			}

			if (!codeValue) {
				codeValue = defVal;
			}

			return codeValue;
		}
	}

	if (formCT[name]) {
		return callBack();
	} else {
		if (codeTableAttrs && codeTableAttrs.length > 0) {
			//初始化码表
			initCodeTable(undefined, codeTableAttrs[0]);
			return callBack();
		} else {
			return defVal;
		}
	}
}

/**
 * 初始化码表
 * 从码表数据来源层面来说支持三种方式的码表
 *  1、码表数据来源于url（json文件和普通的getDmb.do）
 *  2、码表数据来源于期初数model
 *  3、码表数据来源于带参数的请求params（/nssb/getDtdmb.do）
 * 码表请求支持同步异步配置async（默认为异步true）
 * 码表支持累加contact，对于两个来源的数据码表的name一样，最后的结果会做合并操作
 * @param $scope
 * @param attributes
 */
function initCodeTable($scope, attributes) {
    var _name = attributes["name"];
    var _url = attributes["url"];
    var _model = attributes["model"];
    var _params = attributes["params"];
    var _jsons = {};
   
    if (undefined === formCT[_name] || JSON.stringify(formCT[_name]) === "{}") {//判断是否已缓存
        if ((undefined !== _url && "" !== _url) || (undefined !== _params && "" !== _params)) {//URL来源
            getDmFromUrl($scope, attributes, _jsons);
        } else if (undefined !== _model && "" !== _model) {//期初数来源
            getDmFromModel($scope, attributes, _jsons);
        } else {
            //codetable指令相关参数缺失
            console.log("ERROR:codetable指令相关参数缺失，请检查...");
            return;
        }
    }
}

/**
 * 从url（json文件和普通的getDmb.do）获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromUrl($scope, attributes, _jsons) {
    // 默认为同步
    var _async = false;
    var _node = attributes["node"];
    var _name = attributes["name"];
    var _url = attributes["url"];

    var _params = attributes["params"];
    var _dynamicParam = attributes["dynamic"];
    var _data;

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    var data = {};
	var index = location.pathname.lastIndexOf('/');
	_url = location.pathname.substr(0, index) + "/static/sb/yjtkfxzbjsb/form/" + _url;
        
	// 允许添加参数，param为key, dynamicParam为value，可以多个逗号隔开，但个数必须一致
	if (_params && _dynamicParam) {
		var aryParam = _params.split(',');
		var aryDynamic = _dynamicParam.split(',');
		if (aryParam && aryDynamic && aryDynamic.length === aryDynamic.length) {
			for (var idx = 0; idx < aryParam.length; idx++) {
				_data = jsonPath($scope ? $scope.formData : formData, aryDynamic[idx])[0];
				if (_data) {
					_url = _url + (idx === 0 ? "?" : "&") + aryParam[idx] + "=" + _data;
				} else {
					// 发现有不符合的，则不进行拼接
					break;
				}
			}
		}
	}

	const response = syncFetch(_url, _async, data);
	_data = response;
	if (undefined === _url || "" === _url) {
		if (typeof response === "string") {
			response = JSON.parse(response);
		}
		if (response && response["dtdmbxx"]) {
			response = response["dtdmbxx"];
		}
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}
		if (typeof _data === "string") {
			_data = JSON.parse(_data);
		}
		if (_data && _data["root"]) {
			Object.keys(_data).forEach((k)=> {
				const v = _data[k];
				_jsons[v["dm"]] = v;
			});
			_data = _jsons;
		}
	} else {
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}

		var doFilter = false;
		Object.keys(_data).forEach((k)=> {
			const v = _data[k];
			doFilter = false;
			if (filterKey && filterValue && v) {
				if (v[filterKey] !== filterValue) {
					doFilter = true;
				}
			}
			if (!doFilter) {
				_jsons[k] = v;
			}
		});
	}

	formCT[_name] = _jsons;
}

/**
 * 从期初数model获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromModel($scope, attributes, _jsons) {
    var _name = attributes["name"];
    var _multi = attributes["multi"];
    var _data;

    var _model = attributes["model"];
    var _dm = attributes["dm"];

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    _data = jsonPath($scope ? $scope.formData : formData, _model)[0];
    if (undefined === _data || "" === _data) {
        console.log("ERROR:codetable指令缓存代码表获取的data为空，对应的model为:" + _model + ",name为:" + _name);
        return;
    }

    if (undefined !== _dm && "" !== _dm) {
        var doFilter = false;
        Object.keys(_data).forEach((k)=> {
			const v = _data[k];
            doFilter = false;
            if (filterKey && filterValue) {
                if (v[filterKey] !== filterValue) {
                    doFilter = true;
                }
            }
            if (!doFilter) {
                if (_multi === 'true') {
                    if (!_jsons[v[_dm]])
                        _jsons[v[_dm]] = [];
                    _jsons[v[_dm]].push(v);
                } else {
                    _jsons[v[_dm]] = v;
                }
            }
        });
        _data = _jsons;
    }

	formCT[_name] = _data;
}

function getBqyjsfe(zspmDm, index) {
	if (!isNull(zspmDm)) {
		var bqyjsfe = formData.ht_.ywbw.yjtkfxzbjsb.sbxxGrid.sbxxGridlb[index].bqyjsfe;
		if (bqyjsfe > 0) {
			return bqyjsfe;
		} else {
			var sbxxGridlb = formData.hq_.sbxxGrid.sbxxGridlb;
			for (var i in sbxxGridlb) {
				if (zspmDm === sbxxGridlb[i].zspmDm) {
					return sbxxGridlb[i].yjse;
				}
			}
		}
	}
	return 0;
}

export default {
	sfywtbpm,
	getMcBySbh,
	setYsqxxSb,
	getDmFromCodeTable,
	getBqyjsfe
}