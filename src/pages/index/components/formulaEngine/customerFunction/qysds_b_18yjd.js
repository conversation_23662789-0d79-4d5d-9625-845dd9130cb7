//获取上一年小薇企业标志
function getSnxwqy() {
  var snd = formData.fq_.sssq.sqQ.split('-')[0] - 1;

  // 上年 A年报数据
  var cyrs = formData.fq_.syndNbxx.cyrs;
  var ynssde = formData.fq_.syndNbxx.ynssde;
  var zcze = formData.fq_.syndNbxx.zcze;
  var csgjfxzhjzhy = formData.fq_.syndNbxx.csgjfxzhjzhy;
  var hyDm = '';

  // 上一年 第四季度 或12月的小薇
  var syndsjdh12ysfsxw = formData.fq_.syndsjdh12ysfsxw.sfxxwlqy;

  // 上年 B年报数据

  var ynssde_b =
    formData.fq_.synqysdsbNdxx.acbfy_ynssde != 0
      ? formData.fq_.synqysdsbNdxx.acbfy_ynssde
      : formData.fq_.synqysdsbNdxx.asrze_ynssbe;
  var gjxzhjzhy = formData.fq_.synqysdsbNdxx.gjxzhjzhy;
  var qycyrs_qnpjrs = formData.fq_.synqysdsbNdxx.qycyrs_qnpjrs;
  var zcze_qnpjrs = formData.fq_.synqysdsbNdxx.zcze_qnpjrs;

  // 上年 B表 第四季度 或12月的小薇

  var synqysdsbYjdxx = formData.fq_.synqysdsbYjdxx.sfxxwlqy;

  if (qycyrs_qnpjrs != 0 && qycyrs_qnpjrs != undefined) {
    hyDm = formData.fq_.synqysdsbNdxx.hy_dm;
    var xw_b = sfxwqy_ND(gjxzhjzhy, hyDm, qycyrs_qnpjrs, zcze_qnpjrs, ynssde_b, snd);
    var xwb = xw_b ? 'Y' : 'N';
    return xwb;
  } else if (synqysdsbYjdxx != '' && synqysdsbYjdxx != undefined) {
    return synqysdsbYjdxx;
  } else if (cyrs != 0 && cyrs != undefined) {
    hyDm = formData.fq_.syndNbxx.hyDm;
    var xw = sfxwqy_ND(csgjfxzhjzhy, hyDm, cyrs, zcze, ynssde, snd);

    var xwbz = xw ? 'Y' : 'N';
    return xwbz;
  } else {
    return syndsjdh12ysfsxw != undefined ? syndsjdh12ysfsxw : '';
  }
}

function sfxwqy_ND(csgjfxzhjzhy, sshyDm, cyrs, zcze, ynssde, nd) {
  var isXwqy = false;

  var qzd;
  // 年度小薇起征点

  if (nd < 2018) {
    qzd = 500000;
  } else if (nd == 2018) {
    qzd = 1000000;
  } else {
    qzd = formData.fq_.xwqzdje;
  }

  if (csgjfxzhjzhy == null || csgjfxzhjzhy == '' || 'N' != csgjfxzhjzhy) {
    // 不满足第105项为“否”的企业的条件，返回0，
    return isXwqy;
  }

  if (ynssde == null || ynssde == '' || ynssde <= 0 || ynssde > qzd) {
    // 不满足A100000《中华人民共和国企业所得税年度纳税申报表（A类）》第23行应纳税所得额<=50万元且>0的条件，返回0
    return isXwqy;
  }
  if (sshyDm == null || sshyDm == '') {
    return isXwqy;
  }
  if (cyrs == null || cyrs == '' || cyrs <= 0) {
    return isXwqy;
  }
  if (zcze == null || zcze == '' || zcze < 0) {
    return isXwqy;
  }

  if (!isNaN(sshyDm)) {
    // 行业代码还有A、B、C等字母，但是属于父类，应该不可选，但是还是判断是否为数字的
    var hyDmInt = sshyDm.substring(0, 2);
    if (hyDmInt >= 6 && hyDmInt <= 46) {
      // 因为最大的数值为4690，所以可以直接判断<=46
      // 第102项“行业明细代码”属于“工业企业”的
      if (cyrs <= 100 && zcze <= 3000) {
        isXwqy = true;
      }
    } else {
      // 第102项“行业明细代码”不属于“工业企业”的
      if (cyrs <= 80 && zcze <= 1000) {
        isXwqy = true;
      }
    }
  } else {
    // 第102项“行业明细代码”不属于“工业企业”的
    if (cyrs <= 80 && zcze <= 1000) {
      isXwqy = true;
    }
  }
  return isXwqy;
}

/**
 *  当按月报：zsfsDm=402
 非季末月报，不管第17行数据，“小型微利企业”锁定“空”，不可修改。
 季末月报，不管第17行数据，“小型微利企业”锁定“否”，不可修改。
 * */
function getYbxwqy() {
  //取到所属期起如2019-03-31
  var ssQ = formData.fq_.sssq.sqQ;
  var ssZ = formData.fq_.sssq.sqZ;
  //取到月份如03
  var ssQMonth = formData.fq_.sssq.sqQ.substring(5, 7);
  var ssZMonth = formData.fq_.sssq.sqZ.substring(5, 7);
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  //按月报的执行逻辑块
  if (ssQMonth == ssZMonth && zsfsDm == '402') {
    //季末报返回N
    if (ssZMonth == '03' || ssZMonth == '06' || ssZMonth == '09' || ssZMonth == '12') {
      return 'N';
    } else {
      return '';
    }
  } else {
    return 1;
  }
}

// 初始化本期小薇判断
function getXwinit() {
  //年报标志 yjdndsbbj：1
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  if (yjdndsbbj) {
    if (yjdndsbbj == 1) {
      var qycyrsQnpjrs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.qycyrsQnpjrs;
      var zczeQnpjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zczeQnpjs;
      var gjxzhjzhy = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqGjxzhjzhy;
      var ynssdeLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ynssdeLj;
      var ndsfxwbz = getxwnbbz(qycyrsQnpjrs, zczeQnpjs, gjxzhjzhy, ynssdeLj);
      return ndsfxwbz;
    }
  }
  //402 判断是否是按月报，按月报，季末的返回N，非季末的返回空
  var aybxwqy = getYbxwqy();
  if (aybxwqy == 'N' || aybxwqy == '') {
    return aybxwqy;
  }
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var ynssdeLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ynssdeLj;
  var nhdsdse = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.nhdsdse;
  var synxw = getSnxwqy(); // 上一年是否是小薇
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var gsdq = swjgDm.substring(0, 5);
  var dq = swjgDm.substring(1, 3);
  var qzd = formData.fq_.xwqzdje;
  var sqq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqq;
  var sqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var sfqdxw300w = formData.fq_.sfqdxw300w; // 是否启动小微标准扩围为300万
  var hdse = sfqdxw300w == 'Y' ? 250000 : 100000;
  var nd = parseInt(sqz.split('-')[0], 10);

  hdse = nd >= 2021 ? 225000 : 250000;
  // 2022年之后
  hdse = nd >= 2022 ? 125000 : hdse;
  // 2023年之后 37500
  hdse = nd >= 2023 ? 150000 : hdse;

  var zfjglxDm = formData.fq_.zfjglxDm_nsrxx;
  //4.0修改获取总分机构类型代码
  var xsd2_39 = formData.kz_.temp.xsd2_39;

  // 暑期起止的月差 ，月差为11时说明是年报
  var yc = parseInt(sqz.split('-')[1], 10) - parseInt(sqq.split('-')[1], 10);

  // 年报是小薇选项直接是空锁定的
  if (yc == 11) {
    return '';
  }

  // 分支机构是小薇默认N ; 应纳大于100万默认N
  if (zsfsDm == '402') {
    if ((zfjglxDm == '2' || zfjglxDm == '3') && xsd2_39 == 'Y') {
      return 'N';
    }

    if (yc == 0) {
      if (sfqdxw300w == 'Y') {
        return 'N';
      }
      nhdsdse = nhdsdse * 12;
    } else if (yc == 2) {
      nhdsdse = nhdsdse * 4;
    }

    //        2020新版规则和期初数返回值hdXxwlqybz比较
    var xsd246qybz = formData.kz_.xsd246qybz;
    if (xsd246qybz == 'Y' && nd < 2022) {
      var hdXxwlqybz = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.hdXxwlqybz;
      if (hdXxwlqybz == 1) {
        return 'Y';
      } else if (hdXxwlqybz == 0) {
        return 'N';
      }
      console.warn('核心返回sbQysdshdzsyjdndsbqtxxVO.hdXxwlqybz不正确', hdXxwlqybz);
    }

    if (nhdsdse <= hdse) {
      if (sfqdxw300w == 'Y') {
        return 'Y';
      }
      if (dq == '63') {
        return 'Y';
      }

      return '';
    } else {
      return 'N';
    }
  } else {
    if (sfqdxw300w == 'Y') {
      var yf = parseInt(sqz.split('-')[1], 10);
      // 19版本 申报期止不是季末时 小薇默认空
      if (yf != 3 && yf != 6 && yf != 9 && yf != 12) {
        return '';
      }
      if ((zfjglxDm == '2' || zfjglxDm == '3') && xsd2_39 == 'Y') {
        return 'N';
      }
      // 实行核定应税所得率方式的纳税人（403、404），
      // 本纳税年度截至本期末的从业人数季度平均值不超过300人、
      // 资产总额季度平均值不超过5000万元、
      // 本表“国家限制或禁止行业”选择“否”
      // 且本期本表第12行“应纳税所得额”不超过300万元的纳税人，
      // 选择“是”，否则选择“否”。

      var qccyrs = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs;
      var qmcyrs = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs;
      var qczcze = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze;
      var qmzcze = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze;
      var gjxzhjzhy = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.gjxzhjzhy;
      var cyrsPjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.cyrsPjsLjs;
      var zczePjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zczePjsLjs;
      var ysbJds = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.ysbJds;

      cyrsPjsLjs = isNull(cyrsPjsLjs) ? 0 : cyrsPjsLjs;
      zczePjsLjs = isNull(zczePjsLjs) ? 0 : zczePjsLjs;
      ysbJds = isNull(ysbJds) ? 0 : ysbJds;

      var bqpjrs = ROUND((qccyrs + qmcyrs) / 2, 0);
      var bqpjzcze = ROUND((qczcze + qmzcze) / 2, 2);

      var pjrsLj = ROUND((bqpjrs + cyrsPjsLjs) / (ysbJds + 1), 0);
      var pjzczeLj = ROUND((bqpjzcze + zczePjsLjs) / (ysbJds + 1), 2);

      //xsd2.46版本后的平均人数和平均资产总额 直接取界面计算的
      var xsd246qybz = formData.kz_.xsd246qybz;
      if (xsd246qybz == 'Y') {
        pjrsLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qycyrsQnpjrs;
        pjzczeLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zczeQnpjs;
      }

      if (pjrsLj <= 300 && pjzczeLj <= 5000 && gjxzhjzhy == 'N' && ynssdeLj <= qzd) {
        return 'Y';
      }

      return 'N';
    }

    if (ynssdeLj > qzd) {
      return 'N';
    } else if (synxw == 'Y' && ynssdeLj <= qzd) {
      return 'Y';
    } else if ((synxw == '' || synxw == 'N') && ynssdeLj <= qzd) {
      if (gsdq == '12102' || dq == '63') {
        return 'Y';
      } else {
        return '';
      }
    }
  }
}

// 触发性本期小薇判断
// TODO: ynssdeLj1原为ynssdeLj，为了解决报错 暂改为ynssdeLj1
function getBqxwqy(ynssdeLj, gjxzhjzhy, cyrs, zcze, qccyrs, qmcyrs, qczcze, qmzcze, ynssdeLj1, qycyrsQnpjrs, zczeQnpjs) {
  //年报标志 yjdndsbbj：1
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  if (yjdndsbbj) {
    if (yjdndsbbj == 1) {
      var qycyrsQnpjrsSq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qycyrsQnpjrs;
      var zczeQnpjsSq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zczeQnpjs;
      var gjxzhjzhySq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.gjxzhjzhy;
      var ynssdeLjBq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ynssdeLj;
      var ndsfxwbz = getxwnbbz(qycyrsQnpjrsSq, zczeQnpjsSq, gjxzhjzhySq, ynssdeLj);
      return ndsfxwbz;
    }
  }
  //判断是否是按月报，按月报，季末的返回N，非季末的返回空
  var aybxwqy = getYbxwqy();
  if (aybxwqy == 'N' || aybxwqy == '') {
    return aybxwqy;
  }
  var sfsyxxwlqy = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.sfsyxxwlqy;
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var xwinit = getXwinit();
  var qzd = formData.fq_.xwqzdje;
  var sqq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqq;
  var sqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var sfqdxw300w = formData.fq_.sfqdxw300w; // 是否启动小微标准扩围为300万
  var zfjglxDm = formData.fq_.zfjglxDm_nsrxx;
  var xsd2_39 = formData.kz_.temp.xsd2_39;
  // 暑期起止的月差 ，月差为11时说明是年报
  var yc = parseInt(sqz.split('-')[1], 10) - parseInt(sqq.split('-')[1], 10);

  if (sfqdxw300w == 'Y') {
    if (zsfsDm == '402') {
      var nhdsdse = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.nhdsdse;
      var hdse = sfqdxw300w == 'Y' ? 250000 : 100000;
      var nd = parseInt(sqz.split('-')[0], 10);
      hdse = nd >= 2021 ? 225000 : 250000;
      // 2022年之后
      hdse = nd >= 2022 ? 125000 : hdse;
      // 2023年之后 37500*4=150000
      hdse = nd >= 2023 ? 150000 : hdse;

      if ((zfjglxDm == '2' || zfjglxDm == '3') && xsd2_39 == 'Y') {
        return 'N';
      }

      // 19年报 核定征收月报 小薇默认 N
      if (yc == 0) {
        return 'N';
      } else {
        nhdsdse = nhdsdse * 4;
        //        2020新版规则，季报，和期初数返回值hdXxwlqybz比较
        var xsd246qybz = formData.kz_.xsd246qybz;
        if (xsd246qybz == 'Y' && nd < 2022) {
          var hdXxwlqybz = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.hdXxwlqybz;
          if (hdXxwlqybz == 1) {
            return 'Y';
          } else if (hdXxwlqybz == 0) {
            return 'N';
          }
          console.warn('核心返回sbQysdshdzsyjdndsbqtxxVO.hdXxwlqybz不正确', hdXxwlqybz);
        }
        if (nhdsdse <= hdse) {
          return 'Y';
        }
        return 'N';
      }
    } else {
      var yf = parseInt(sqz.split('-')[1], 10);

      // 19版本 申报期止不是季末时 小薇默认N
      if (yf != 3 && yf != 6 && yf != 9 && yf != 12) {
        return '';
      }

      if ((zfjglxDm == '2' || zfjglxDm == '3') && xsd2_39 == 'Y') {
        return 'N';
      }

      // 实行核定应税所得率方式的纳税人（403、404），
      // 本纳税年度截至本期末的从业人数季度平均值不超过300人、
      // 资产总额季度平均值不超过5000万元、
      // 本表“国家限制或禁止行业”选择“否”
      // 且本期本表第12行“应纳税所得额”不超过300万元的纳税人，
      // 选择“是”，否则选择“否”。

      var cyrsPjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.cyrsPjsLjs;
      var zczePjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zczePjsLjs;
      var ysbJds = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.ysbJds;

      cyrsPjsLjs = isNull(cyrsPjsLjs) ? 0 : cyrsPjsLjs;
      zczePjsLjs = isNull(zczePjsLjs) ? 0 : zczePjsLjs;
      ysbJds = isNull(ysbJds) ? 0 : ysbJds;

      var bqpjrs = ROUND((qccyrs + qmcyrs) / 2, 2);
      var bqpjzcze = ROUND((qczcze + qmzcze) / 2, 2);

      var pjrsLj = ROUND((bqpjrs + cyrsPjsLjs) / (ysbJds + 1), 2);
      var pjzczeLj = ROUND((bqpjzcze + zczePjsLjs) / (ysbJds + 1), 2);

      //xsd2.46版本后的平均人数和平均资产总额 直接取界面计算的
      var xsd246qybz = formData.kz_.xsd246qybz;
      if (xsd246qybz == 'Y') {
        pjrsLj = qycyrsQnpjrs;
        pjzczeLj = zczeQnpjs;
      }

      if (pjrsLj <= 300 && pjzczeLj <= 5000 && gjxzhjzhy == 'N' && ynssdeLj <= qzd) {
        return 'Y';
      }

      return 'N';
    }
  }

  // 年报是小薇选项直接是空锁定的
  if (yc == 11) {
    var hyDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.hyDm;
    var nd = parseInt(sqz.split('-')[0], 10);
    // 本期年报是否符合小薇
    var bqsffhxw = sfxwqy_ND(gjxzhjzhy, hyDm, cyrs, zcze, ynssdeLj, nd);
    if (bqsffhxw) {
      return 'Y';
    }
    return 'N';
  }

  if (ynssdeLj > qzd) {
    return 'N';
  }

  if (xwinit == 'Y' && (zsfsDm == '403' || zsfsDm == '404')) {
    return 'Y';
  }
  return sfsyxxwlqy;
}

// 小薇是否锁定
function getXwsdpd(ynssdeLj, xwqzdje, zsfsDm, nhdsdse) {
  //yjdndsbbj等于1为年报
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  //为年报时小微按钮是否锁定逻辑
  if (yjdndsbbj) {
    if (yjdndsbbj == 1) {
      //			var sfzdxw = true;
      //			var zsfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zsfsDm;
      //			//yjdndsbbj为1说明是年报
      //			var yjdndsbbj= formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
      //			var sqGjxzhjzhy = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqGjxzhjzhy;
      //			var cyrsPjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.cyrsPjsLjs;
      //			var yjdYsbcs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjdYsbcs;
      //			var zczePjsLjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zczePjsLjs;
      //			var ynssdeLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ynssdeLj;
      //			var zfjglxDm_nsrxx = formData.fq_.zfjglxDm_nsrxx;
      //zsfsDm 为 403 404 时且 暑期是按年的  才可以报
      //			if(((zsfsDm==403||zsfsDm==404)&&yjdndsbbj==1)){
      //				sfzdxw = false;
      //			}
      /**同时满足以下条件判断为“是”否则为“否”，锁定不可修改：
			①申报所属年度第4季度预缴申报表中“国家限制或禁止行业”选择“否”，年度中间停止经营的，按照最后一次预缴申报中该项确定(实现时取初始化返回sqGjxzhjzhy)；
			②平均从业人数<=300（计算方法见右备注）（实现时=cyrsPjsLjs/yjdYsbcs）
			③平均资产总额(万元)<=5000，平均资产总额算法与平均人数算法一致。（实现时=zczePjsLjs/yjdYsbcs）
			④应纳税所得额<=3000000，应纳税所得额是指本表第14行“应纳税所得额”
			年度中间开业或者终止经营活动的，以实际经营期作为一个纳税年度确定上述相关指标*/
      //			if(sqGjxzhjzhy=='N'&&(cyrsPjsLjs/yjdYsbcs)<=300&&(zczePjsLjs/yjdYsbcs)<=5000&&ynssdeLj<=3000000){
      //				sfzdxw =true;
      //			}
      //纳税人税务登记信息中的“总分机构类型代码（ZFJGLX_DM）”为“分支机构（2）”或“分总机构（3）”的，小型微利企业标示默认为“否”，不可修改。。
      //			if(formData.fq_.zfjglxDm_nsrxx==2||formData.fq_.zfjglxDm_nsrxx==3){
      //				sfzdxw =true;
      //			}
      //			return sfzdxw;
      //年报默认锁定
      return true;
    }
  }
  var sqq = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqq;
  var sqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var yq = parseInt(sqq.split('-')[1], 10);
  var yz = parseInt(sqz.split('-')[1], 10);
  var yc = yz - yq;
  var synxw = getSnxwqy();
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var dq = swjgDm.substring(1, 3);
  var sfqdxw300w = formData.fq_.sfqdxw300w; // 是否启动小微标准扩围为300万
  var hdse = sfqdxw300w == 'Y' ? 300000 : 100000;
  // 应纳大于小薇金额 或者是 年报是 小薇选项 锁定； 19版本小微标准扩围为300万 后 小薇都是锁定的
  if (ynssdeLj > xwqzdje || yc == 11 || sfqdxw300w == 'Y') {
    return true;
  }

  if (zsfsDm == '402') {
    if (dq == '63') {
      return true;
    }

    if (yc == 0) {
      var je = nhdsdse * 12;
      if (je > hdse) {
        return true;
      }
    } else if (yc == 2) {
      var je1 = nhdsdse * 4;
      if (je1 > hdse) {
        return true;
      }
    }
  }

  if (synxw == 'Y' && ynssdeLj <= xwqzdje && (zsfsDm == '403' || zsfsDm == '404')) {
    return true;
  }

  return false;
}

// 已缴税额的计算

function getYjse(kyyjye, sqyjje, yyyjje) {
  var csz = formData.fq_.yjxxgs;

  if (csz == undefined || csz == null || csz == '' || csz == '1') {
    return kyyjye + sqyjje + yyyjje;
  }

  if (csz == '2') {
    return kyyjye + sqyjje;
  }

  if (csz == '3') {
    return sqyjje + yyyjje;
  }

  if (csz == '4') {
    return kyyjye + yyyjje;
  }

  if (csz == '5') {
    return kyyjye;
  }

  if (csz == '6') {
    return sqyjje;
  }

  if (csz == '7') {
    return yyyjje;
  }

  return kyyjye + sqyjje + yyyjje;
}

// 更正申报是要不参股表的证件类型名称重新赋值。GDSDZSWJ-8118
function setZjlxMc() {
  var gzbz = formData.kz_.temp.gzsbbz;
  if (gzbz != 'Y') {
    return;
  }
  var dmb = formData.kz_.basedata['sfzjlx'];
  var zjCT = formCT.sfzjlxCT;
  var cgbList = formData.kz_.temp.jmqycgwgqyxxbgbGridlb;
  var arr = new Array();
  if (!isNull(dmb)) {
    arr = dmb['item'];
  }

  for (var i = 0; i < cgbList.length; i++) {
    var dsxxList = cgbList[i].zgjmgrdrwgqygghdsqkGrid.zgjmgrdrwgqygghdsqkGridlb;

    for (var j = 0; j < dsxxList.length; j++) {
      dm = dsxxList[j].sfzjlx;
      mc = zjCT[dm];

      if (!isNull(dm) && !isNull(mc)) {
        dmb = {};
        // 去掉重复的TODO
        var item = {};
        item['dm'] = dm;
        item['mc'] = mc;
        arr.push(item);
        dmb['item'] = arr;
      }
    }
  }
}

/**
 * 核定应纳所得税额校验！
 */

function hdzsVaild(zsfsDm, skssqq, skssqz) {
  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }
  // 暑期起止的月差 ，月差为11时说明是年报
  var yc = parseInt(skssqz.split('-')[1], 10) - parseInt(skssqq.split('-')[1], 10);
  var msg = '';
  //CLOUDTAXND-591
  var swjgdm = formData.fq_.nsrjbxx.swjgDm;
  //1:江苏禁止征收方式为‘核定应纳所得税额’即402方式的申报
  if (swjgdm.substring(0, 3) == '132' && zsfsDm == 402) {
    msg = '核定方式为“核定应纳所得税额”的企业暂不支持网上申报，请前往税务大厅进行申报！';
    var a = layer.alert(
      msg,
      {
        type: 1,
        closeBtn: 0,
      },
      function () {
        // 关闭当前页面
        closeWin();
      },
    );
  }
  //2:核定应纳所得税额的纳税人不需要进行年报！
  else if (zsfsDm == 402 && yc == 11) {
    window.parent.cleanMeunBtn();
    msg = '核定应纳所得税额的纳税人不需要进行年报！';
    var a = layer.alert(
      msg,
      {
        skin: {
          icon: 6,
        },
        type: 1,
        closeBtn: 0,
      },
      function () {
        // 关闭当前页面
        closeWin();
      },
    );
  }
}

/**
 * 符合小薇 减免所得额的计算
 *
 */

function getJmsde(sfsyxxwlqy, ynssdeLj, ynsdseLj, hyDm, gjxzhjzhy, cyrs, zcze, sqq, sqz) {
  //yjdndsbbj为1说明是年报
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var swjgdm = formData.fq_.nsrjbxx.swjgDm;
  // 核定年应纳所得税额（判断是否为小型微利企业前）
  var hdnynssdePdsfwxxwlqyh = formData.kz_.basedata.hdnynssdePdsfwxxwlqyh;
  var nd = parseInt(sqz.split('-')[0], 10);
  var ssqqyf = sqq.replace(/-/g, '').substring(0, 6);
  //402的处理规则 年报和月季报规则一致
  if ((zsfsDm != 403 && zsfsDm != 404) || zsfsDm == '402') {
    if (sfsyxxwlqy != 'Y') {
      return 0;
    }
    if (zsfsDm == '402' && nd == 2021) {
      //2021年时，小微优惠前核定年应纳税额<=25万元，小微优惠金额为：优惠前核定税额*90%；小微优惠前核定年应纳税额>25万元且<=75万，小微优惠金额为：（小微优惠前核定年应纳税额）*60%+75000；小微优惠前核定年应纳税额>75万，小微优惠金额为0。
      if (hdnynssdePdsfwxxwlqyh <= 250000) {
        return hdnynssdePdsfwxxwlqyh * 0.9;
      } else if (hdnynssdePdsfwxxwlqyh > 250000 && hdnynssdePdsfwxxwlqyh <= 750000) {
        return hdnynssdePdsfwxxwlqyh * 0.6 + 75000;
      } else {
        return 0;
      }
    }
    if (zsfsDm == '402' && nd >= 2022) {
      //2022年及以后年度时，小微优惠前核定年应纳税额<=25万元，小微优惠金额为：优惠前核定税额×90%；小微优惠前核定年应纳税额>25万元且<=75万，小微优惠金额为：（小微优惠前核定年应纳税额）×80%+25000；小微优惠前核定年应纳税额>75万，小微优惠金额为0
      if (hdnynssdePdsfwxxwlqyh <= 250000) {
        return hdnynssdePdsfwxxwlqyh * 0.9;
      } else if (hdnynssdePdsfwxxwlqyh > 250000 && hdnynssdePdsfwxxwlqyh <= 750000) {
        return hdnynssdePdsfwxxwlqyh * 0.8 + 25000;
      } else {
        return 0;
      }
    }

    //21年以前 402的 默认0
    return 0;
  }

  // yjdndsbbj=="1"：年报
  if (yjdndsbbj && yjdndsbbj == '1') {
    if (sfsyxxwlqy != 'Y') {
      return 0;
    }

    /**
         * 2019年起，年度申报时：
         1、当本表“按年度填报信息”中“小型微利企业”判断为“是”的，
         （1）本表第14行“应纳税所得额”<=1000000时，本行=第14行×20%
         （2）本表第14行“应纳税所得额”>1000000且<=3000000时，本行=第14行×15%+50000
         （3）核定应税所得额的不填本行。
         公式带出锁定不可修改。
         * */
    if (nd >= 2019 && nd <= 2020) {
      if (ynssdeLj <= 1000000) {
        return mul(ynssdeLj, 0.2);
      } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
        return SUM(mul(ynssdeLj, 0.15), 50000);
      } else {
        return 0;
      }
    }

    /*
    	* 适用【B类年报】2021年1月1日属期起：公式带出锁定不可修改。

a.当“小型微利企业”选择“是”时，同时B100000第14行“应纳税所得额”＞0且≤100万时，B100000第17行“符合条件的小型微利企业减免企业所得税”=B100000第16行“应纳所得税额”*90%；

b.当“小型微利企业”选择“是”时，同时B100000第14行“应纳税所得额”＞100万且≤300万时，B100000第17行“符合条件的小型微利企业减免企业所得税”==B100000第16行“应纳所得税额”*60%+7.5万；
c.当“小型微利企业”选择“否”，本行数值为0
西藏地区（154）：满足上述小微条件时，本行≥按上述公式计算的结果。不满足上述条件时，本行企业可自行填报。
d.核定应税所得额的不填本行。
    	* */
    if (nd == 2021) {
      if (ynssdeLj <= 1000000) {
        return mul(mul(ynssdeLj, 0.25), 0.9);
      } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
        return SUM(mul(mul(ynssdeLj, 0.25), 0.6), 75000);
      } else {
        return 0;
      }
    } else if (nd == 2022) {
      if (ynssdeLj <= 1000000) {
        return mul(mul(ynssdeLj, 0.25), 0.9);
      } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
        return SUM(mul(mul(ynssdeLj, 0.25), 0.8), 25000);
      } else {
        return 0;
      }
    } else if (nd >= 2023) {
      if (zsfsDm == 403 || zsfsDm == 404) {
        if(ynssdeLj >0 && ynssdeLj <= 3000000){
          return mul(mul(ynssdeLj, 0.25), 0.8);
        }else {
          return 0;
        }
      }
    }
  } else {
    //月季报 情况
    if (sfsyxxwlqy != 'Y') {
      return 0;
    }
    var sfqdxw300w = formData.fq_.sfqdxw300w; // 是否启动小微标准扩围为300万
    // 小薇优惠的折算率
    var yhsl = 0.15;
    // 速算扣除数
    var sskcs = 0;
    if (sfqdxw300w == 'Y') {
      yhsl = ynssdeLj <= 1000000 ? 0.2 : 0.15;
      sskcs = ynssdeLj <= 1000000 ? 0 : 50000;
    }
    //厦门个性化 XMDZSWJ-755
    if (ssqqyf >= 201906 && nd <= 2020 && swjgdm.substring(1, 5) === '3502') {
      if (ynssdeLj <= 0.02) {
        return 0;
      } else if (0.02 < ynssdeLj && ynssdeLj <= 1000000) {
        return mul(ynssdeLj, 0.2);
      } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
        return SUM(mul(ynssdeLj, 0.15), 50000);
      } else {
        return 0;
      }
    }
    //2021年起JSONE-15805
    if (nd == 2021) {
      if (ynssdeLj <= 1000000) {
        if (swjgdm.substring(1, 5) === '3502' && ynssdeLj <= 0.02 && ynssdeLj >= 0) {
          return 0;
        }
        return mul(mul(ynssdeLj, 0.25), 0.9);
      } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
        return SUM(mul(mul(ynssdeLj, 0.25), 0.6), 75000);
      } else {
        return 0;
      }
    } else if (nd == 2022) {
      if (zsfsDm == 403 || zsfsDm == 404) {
        if (ynssdeLj <= 1000000) {
          if (swjgdm.substring(1, 5) === '3502' && ynssdeLj <= 0.02 && ynssdeLj >= 0) {
            return 0;
          }
          return mul(mul(ynssdeLj, 0.25), 0.9);
        } else if (ynssdeLj > 1000000 && ynssdeLj <= 3000000) {
          return SUM(mul(mul(ynssdeLj, 0.25), 0.8), 25000);
        } else {
          return 0;
        }
      }
    } else if (nd >= 2023) {
      if (zsfsDm == 403 || zsfsDm == 404) {
        if(ynssdeLj > 0 && ynssdeLj <= 3000000){
          return mul(mul(ynssdeLj, 0.25), 0.8);
        }else {
          return 0;
        }
      }
    }
    // 21年以前规则：符合小薇 减免所得额等于 应纳税所得额 * 15%
    return SUM(mul(ynssdeLj, yhsl), sskcs);
  }

  // 不符合小薇 减免所得额等于0
  return 0;
}

/**
 * 关闭当前页面
 */

function closeWin() {
  if (navigator.userAgent.indexOf('MSIE') > 0) {
    if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
      window.opener = null;
      window.close();
    } else {
      window.open('', '_top');
      window.top.close();
    }
  } else if (navigator.userAgent.indexOf('Firefox') > 0) {
    window.location.href = 'about:blank ';
    window.close();
  } else if (navigator.userAgent.indexOf('Chrome') > 0) {
    //top.open(location, '_self').close();
    top.open('about:blank ', '_self').close();
  } else {
    window.open('', '_top');
    window.top.close();
  }
}

/*
  20行自定义公式：
1.核定征收方式选择403、404，且第16-17行＞0时，
1.1享受“免征”优惠的，第20行＝（第16-17行）×40%；
1.2享受“减征”优惠的，第20行＝（第16-17行）×40%×减征幅度。
2.核定征收方式选择402时，且第19行＞0时，
2.1享受“免征”优惠的，第20行=[核定的年度应纳所得税额÷（4或者12）×截止申报所属期的实际应申报属期数]×40%；
2.2享受“减征”优惠的，第20行=[核定的年度应纳所得税额÷（4或者12）×截止申报所属期的实际应申报属期数]×40%×“减征幅度____%”。
PS:目前，核心初始化接口返回的年核定所得税额节点nhdsdse仍是本期申报的应纳税额（已经除以过4或12了）“截止申报所属期的实际应申报属期数”=核心返回节点“月季度已申报次数”yjdYsbcs+1，做上述计算时要注意。
*/
function getMzzzdfjmeLj(
  sfxsmzzzjmyh,
  xsd2_39,
  ynsdseLj,
  fhtjxxwlqyjmsdseLj,
  ybtsdseLj,
  nhdsdse,
  yjdYsbcs,
  zsfsDm,
  jzmzlx,
  jzfd,
  sfsyxxwlqy,
) {
  //yjdndsbbj为1说明是年报
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  var sssz = formData.fq_.sssq.sqZ;
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var swjgdm = formData.fq_.nsrjbxx.swjgDm;

  /**
   * 自2019年6月份或2季度起，内蒙古自治区执行以下税收政策，符合小型微利企业条件的企业，
   * 当表B100000第14行“应纳税所得额”小于等于1000000时，
   * 第20行“民族自治区地方的自治机关对本民族自治地方的企业应缴纳的企业所得税
   * 属于地方分享的部分减征或免征”等于“应纳税所得额”*5%*40%，自动计算带出，不可修改。
   * */
  if (yjdndsbbj == 1 && swjgdm.substring(1, 3) == 15 && sssz.split('-')[1] >= 4 && sfsyxxwlqy == 'Y') {
    if (ynsdseLj <= 1000000) {
      return ynsdseLj * 0.05 * 0.4;
    }
  }
  // 是否享受民族自治减免优惠 的配置不为Y 或者 非2.39以上版本时 默认返回0, 未选择减征免征类型也返回0
  if (sfxsmzzzjmyh != 'Y' || xsd2_39 != 'Y' || jzmzlx == '' || jzmzlx == null || jzmzlx == undefined) {
    return 0;
  }

  if ((zsfsDm == '403' || zsfsDm == '404') && ROUND(ynsdseLj - fhtjxxwlqyjmsdseLj, 2) > 0) {
    if (jzmzlx == '2') {
      return ROUND(ROUND((ynsdseLj - fhtjxxwlqyjmsdseLj) * 0.4 * 1000, 2) / 1000, 2);
    } else if (jzmzlx == '1' && jzfd != '') {
      return ROUND(ROUND((ynsdseLj - fhtjxxwlqyjmsdseLj) * 0.4 * jzfd * 1000, 2) / 1000, 2);
    } else {
      return 0;
    }
  } else if (zsfsDm == '402' && ybtsdseLj > 0) {
    var isFlagjd = formData.kz_.isFlagjd;
    var ysbJds = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.ysbJds;
    if (isFlagjd == 'Y') {
      yjdYsbcs = ysbJds;
    }
    if (jzmzlx == '2') {
      return ROUND(ROUND(nhdsdse * (yjdYsbcs + 1) * 0.4 * 1000, 2) / 1000, 2);
    } else if (jzmzlx == '1') {
      return ROUND(ROUND(nhdsdse * (yjdYsbcs + 1) * 0.4 * jzfd * 1000, 2) / 1000, 2);
    } else {
      return 0;
    }
  }

  return 0;
}

/*
 * 1. 第二季度和第三季：
1.1核定征收方式选择“核定应税所得率（能核算收入总额的）”“核定应税所得率（能核算成本费用总额的）”的，
享受“免征”优惠的，第20行＝（第16-17-L19行）×40%；
享受“减征”优惠的，第20行＝（第16-17行-L19行）×40%×减征幅度；
1.2核定征收方式选择“核定应纳所得税额”的，
享受“免征”优惠的，第20行＝[核心返回的nhdsdse×截止申报所属期的实际应申报属期数-L19行]×40%；
享受“减征”优惠的，第20行＝[核心返回的nhdsdse×截止申报所属期的实际应申报属期数-L19行]×40%×“减征幅度____%”。
2. 其他属期恢复原有规则
 */
function setMzzzdfjmeLj(jzfd, jzmzlx, zsfsDm, nhdsdse, yjdYsbcs, ynsdseLj, fhtjxxwlqyjmsdseLj, fhtjxwqyyhjzsdseLj) {
  var reval = 0;
  if (zsfsDm == '403' || zsfsDm == '404') {
    if (jzmzlx == '2') {
      reval = ROUND(ROUND((ynsdseLj - fhtjxxwlqyjmsdseLj - fhtjxwqyyhjzsdseLj) * 0.4 * 1000, 2) / 1000, 2);
    } else if (jzmzlx == '1' && jzfd != '') {
      reval = ROUND(ROUND((ynsdseLj - fhtjxxwlqyjmsdseLj - fhtjxwqyyhjzsdseLj) * 0.4 * 1000 * jzfd, 2) / 1000, 2);
    } else {
      reval = 0;
    }
  } else if (zsfsDm == '402') {
    var isFlagjd = formData.kz_.isFlagjd;
    var ysbJds = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.ysbJds;
    if (isFlagjd == 'Y') {
      yjdYsbcs = ysbJds;
    }
    if (jzmzlx == '2') {
      reval = ROUND(ROUND((nhdsdse * (yjdYsbcs + 1) - fhtjxwqyyhjzsdseLj) * 0.4 * 1000, 2) / 1000, 2);
    } else if (jzmzlx == '1' && jzfd != '') {
      reval = ROUND(ROUND((nhdsdse * (yjdYsbcs + 1) - fhtjxwqyyhjzsdseLj) * 0.4 * 1000 * jzfd, 2) / 1000, 2);
    } else {
      reval = 0;
    }
  } else {
    reval = 0;
  }
  return reval;
}

function getJzfd(jzmzlx) {
  if (jzmzlx != '1') {
    return 0;
  }

  var jzfd = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.jzfd;
  return jzfd;
}

/**陕西个性化公式提示，在数据库配置个性化公式执行*/
function showMassge() {
  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }
  var monthQ = formData.fq_.sssq.sqQ.split('-')[1];
  var monthZ = formData.fq_.sssq.sqZ.split('-')[1];
  if (monthZ - monthQ == 2) {
    if (window.top) {
      if (typeof isJmsb !== 'function' || (typeof isJmsb === 'function' && !isJmsb())) {
        layer.alert(
          '2019年最新小微企业优惠政策可以享受啦！优惠条件大幅放宽，优惠力度更大！所有行业企业，从业人数不超过300人，资产总额不超过5000万，截止本期累计应纳税所得额不超过300万、不从事国家限制和禁止行业均可以享受！',
          { type: 1, icon: 7 },
        );
      }
    }
  }
}
//4.0年度小微企业判断
function getxwnbbz(qycyrsQnpjrs, zczeQnpjs, gjxzhjzhy, ynssdeLj) {
  var sqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var nd = parseInt(sqz.split('-')[0], 10);
  var zsfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zsfsDm;
  //yjdndsbbj为1说明是年报
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  var zfjglxDm_nsrxx = formData.fq_.zfjglxDm_nsrxx;
  //纳税人税务登记信息中的“总分机构类型代码（ZFJGLX_DM）”为“分支机构（2）”或“分总机构（3）”的，小型微利企业标示默认为“否”，不可修改。。
  if (nd >= 2019 && (zfjglxDm_nsrxx == 2 || zfjglxDm_nsrxx == 3)) {
    return 'N';
  }

  if (nd < 2019) {
    var hyDm = formData.fq_.nsrjbxx.hyDm;
    var xw_b = sfxwqy_ND(gjxzhjzhy, hyDm, qycyrsQnpjrs, zczeQnpjs, ynssdeLj, nd);
    var xwbz = xw_b == true ? 'Y' : 'N';
    return xwbz;
  } else {
    if (gjxzhjzhy == 'N' && qycyrsQnpjrs <= 300 && zczeQnpjs <= 5000 && ynssdeLj <= 3000000) {
      return 'Y';
    }
  }

  //zsfsDm 为 403 404 时且 暑期是按年的  才可以报
  if (!((zsfsDm == 403 || zsfsDm == 404) && yjdndsbbj == 1)) {
    return '';
  }

  return 'N';
}

/**
 * 初始化报文中的征收方式代码（zsfsDm）的返回值403并没有返回yjfsDM,或期初返回zsfsDm=403且yjfsDM不等于2或3时，
 * @param zsfsDm
 * @param yjfsDm
 */
function clzsfsDm403(zsfsDm) {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var result = false;
  if (swjgDm.substring(0, 5) != '13502') {
    var yjfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjfsDm;
    var skssqq = formData.fq_.sssq.sqQ;
    if (isEmptyObject(yjfsDm) && zsfsDm == '403') {
      result = true;
    } else if (yjfsDm != '2' && yjfsDm != '3' && zsfsDm == '403' && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
      result = true;
    }
  }
  return result;
}

/**
 * 1.初始化报文中的征收方式代码（zsfsDm）的返回值404“按成本费用核定应纳税所得额”），自动选中本项，不可修改。
 *	2.对应填报第12至21行，第1至11行置灰，不得填报。
 */
function clzsfsDm404or402(zsfsDm) {
  var yjfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjfsDm;
  var skssqq = formData.fq_.sssq.sqQ;
  if (isEmptyObject(yjfsDm) && zsfsDm == '404') {
    return true;
  } else if (zsfsDm == '402') {
    return true;
  } else if (zsfsDm == '403' && (yjfsDm == '2' || yjfsDm == '3') && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return true;
  } else if (zsfsDm == '404' && (yjfsDm != '2' || yjfsDm != '3') && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return true;
  } else {
    return false;
  }
}

/**
 *
 * @param zsfsDm
 * @returns {Boolean}
 */
function clzsfsDm404(zsfsDm) {
  var yjfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjfsDm;
  var skssqq = formData.fq_.sssq.sqQ;
  if (isEmptyObject(yjfsDm) && zsfsDm == '404') {
    return true;
  } else if (yjfsDm != '2' && yjfsDm != '3' && zsfsDm == '404' && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return true;
  } else {
    return false;
  }
}

function yjfsDM2(zsfsDm) {
  var yjfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjfsDm;
  var skssqq = formData.fq_.sssq.sqQ;
  if (isEmptyObject(yjfsDm)) {
    return;
  }
  if ((zsfsDm == '403' || zsfsDm == '404') && yjfsDm == '2' && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return true;
  } else {
    return false;
  }
}

function yjfsDM3(zsfsDm) {
  var yjfsDm = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yjfsDm;
  var skssqq = formData.fq_.sssq.sqQ;
  if (isEmptyObject(yjfsDm)) {
    return;
  }
  if ((zsfsDm == '403' || zsfsDm == '404') && yjfsDm == '3' && DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return true;
  } else {
    return false;
  }
}

/**
 * 非空校验
 * @param obj
 * @returns {Boolean}
 */
function isEmptyObject(obj) {
  return obj === '' || obj === null || obj === undefined || JSON.stringify(obj) === '[]' || JSON.stringify(obj) === '{}'
}

/**
 * 处理ht多的节点
 * @param htValue
 * @returns {undefined}
 */
function handleHtBw(formData) {
  var jmxxGrid = formData.ht_.jmxxGrid;
  var yjxxGrid = formData.ht_.yjxxGrid;
  var sbxxGrid = formData.ht_.sbxxGrid;

  if (jmxxGrid != null && jmxxGrid.jmxxGridlb != null) {
    formData.ht_.jmxxGrid = {};
  }

  if (yjxxGrid != null && yjxxGrid.yjxxGridlb != null) {
    formData.ht_.yjxxGrid = {};
  }

  if (sbxxGrid != null && sbxxGrid.sbxxGridlb != null) {
    formData.ht_.sbxxGrid = {};
  }

  return formData;
}

var chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
var chnUnitSection = ['', '万', '亿', '万亿', '亿亿'];
var chnUnitChar = ['', '十', '百', '千'];

function sectionToChinese(section) {
  var strIns = '',
    chnStr = '';
  var unitPos = 0;
  var zero = true;
  while (section > 0) {
    var v = section % 10;
    if (v === 0) {
      if (!zero) {
        zero = true;
        chnStr = chnNumChar[v] + chnStr;
      }
    } else {
      zero = false;
      strIns = chnNumChar[v];
      strIns += chnUnitChar[unitPos];
      chnStr = strIns + chnStr;
    }
    unitPos++;
    section = Math.floor(section / 10);
  }
  return chnStr;
}

/**
 * 将阿拉伯数字转成中文
 * @param num
 * @returns {string}
 */
function numberToChinese(num) {
  var unitPos = 0;
  var strIns = '',
    chnStr = '';
  var needZero = false;

  if (num === 0) {
    return chnNumChar[0];
  }

  while (num > 0) {
    var section = num % 10000;
    if (needZero) {
      chnStr = chnNumChar[0] + chnStr;
    }
    strIns = sectionToChinese(section);
    strIns += section !== 0 ? chnUnitSection[unitPos] : chnUnitSection[0];
    chnStr = strIns + chnStr;
    needZero = section < 1000 && section > 0;
    num = Math.floor(num / 10000);
    unitPos++;
  }
  return chnStr;
}

/**
 * 对返回报文结果进行遍历
 * @param fhBw
 * @returns {undefined}
 */
function hadleFhBw(fhBw) {
  var messageValue = '';
  var dsRuleResult = fhBw[0].message.dsRuleResult;

  if (dsRuleResult != null && dsRuleResult.length >= 2) {
    var j = 0;
    $.each(dsRuleResult, function (i, item) {
      if (!isEmptyObject(item.ruleTip)) {
        messageValue += '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + numberToChinese(j + 1) + '、' + item.ruleTip + '<br>';
        j++;
      }
    });
    return messageValue;
  } else {
    if (!isEmptyObject(dsRuleResult.ruleTip)) {
      messageValue += '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;一、' + dsRuleResult.ruleTip + '<br>';
    }
    return messageValue;
  }
}

/**
 * 获取参数
 * @returns {{swjgDm: *, reqParams: *, fxsmStatus: *}}
 */
function getNbfxsmParams() {
  var sfyjdndsbDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.sfyjdndsbDm;
  var reqParams = {};
  reqParams.djxh = $('#djxh').val();
  reqParams.projectName = 'sbzx';
  reqParams.sssqQ = $('#sssqQ').val();
  reqParams.sssqZ = $('#sssqZ').val();
  reqParams.nsrsbh = $('#nsrsbh').val();
  reqParams.gdslxDm = $('#gdslxDm').val();
  reqParams.sfyjdndsbDm = sfyjdndsbDm;
  reqParams.sid = 'dzswj.ywzz.sb.qysdsb18yjd.nbfxsm';
  var fxsmStatus = '1';
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var dataValue = handleHtBw(formData);
  var qqbwData = JSON.stringify(dataValue);
  reqParams.qqbwData = qqbwData;
  return { reqParams: reqParams, fxsmStatus: fxsmStatus, swjgDm: swjgDm };
}

/**
 * 提示信息
 * @param reqParams
 * @param isSecondCall
 */
function extractedShowMessage(reqParams, isSecondCall) {
  var indexts = parent.layer.load(2, {
    content: '正在进行风险扫描，请稍等！',
    success: function (layero) {
      layero.find('.layui-layer-content').css({
        paddingTop: '40px',
        width: '200px',
        textAlign: 'center',
        backgroundPositionX: 'center',
      });
    },
  });

  parent.requestYwztData(reqParams, function (data) {
    parent.layer.close(indexts);
    data = JSON.parse(data);

    var bodyValue = data;
    var fhBw = bodyValue.fhMsg;
    var message = '';
    var flag = '';
    if (
      fhBw[0].message.dsRuleResult == 'zero' ||
      fhBw[0].message.dsRuleResult == 'qqBwError' ||
      fhBw[0].message.dsRuleResult == 'fhBwError'
    ) {
      message = fhBw[0].message.dsRuleResult;
    } else {
      message = hadleFhBw(fhBw);
      if (message != null) {
        flag = 'msgContent';
      }
    }

    if (message == 'zero') {
      parent.layer.alert(
        '扫描通过，即将进入申报表确认页面，请"确认"!',
        {
          icon: 1,
          btn: ['确认'],
          type: 1,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          // 执行回调函数
          ctips(isSecondCall);
        },
      );
    } else if (message == 'qqBwError') {
      parent.layer.alert(
        '请求报文有错，请检查！',
        {
          icon: 2,
          type: 1,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          return;
        },
      );
    } else if (message == 'fhBwError') {
      parent.layer.alert(
        '返回报文有错，请检查！',
        {
          icon: 2,
          type: 1,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          return;
        },
      );
    } else if (flag == 'msgContent') {
      var errs = message
        .replace(/&nbsp;/g, '')
        .replace(/<br>/g, '')
        .replace(/\n/g, '');
      var sHtml =
        '<button  onclick="copyErro()"   class="layui-btn layui-btn-xs"   style="border: 1px solid #53acf3;background-color: #fff;  color: #53acf3;" >复制文本</button><br/><br/>';
      sHtml += '<script>';
      sHtml += '	function copyErro(){';
      sHtml += 'var copyMessage ="' + errs + '";';
      sHtml += "var oInput = document.createElement('input');";
      sHtml += 'oInput.value = copyMessage;';
      sHtml += 'document.body.appendChild(oInput);';
      sHtml += 'oInput.select(); ';
      sHtml += 'document.execCommand("Copy"); ';
      sHtml += "oInput.className = 'oInput';";
      sHtml += "oInput.style.display = 'none';";
      sHtml +=
        'parent.layer.msg(\'<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 17px;"></i></span> 复制成功!\',{time:1000});';
      sHtml += '}';
      sHtml += '</script>';

      parent.layer.confirm(
        sHtml + '扫描未通过,具体信息：<br/>' + message,
        {
          area: ['600px', '300px'],
          title: '提示',
          type: 1,
          btn: ['修改表单', '继续申报'],
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
          btn2: function (index) {
            parent.layer.close(index);
            // 执行回调函数
            ctips(isSecondCall);
          },
        },
        function (index) {
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          parent.layer.close(index);
          return;
        },
      );
    }
  });
}

/**
 * 未配置
 * @param isSecondCall
 */
function extractedNotConfigured(isSecondCall) {
  //批量申报时弹框要频闭
  parent.layer.confirm(
    '未配置或未正确配置事中监控接口,请检查!',
    {
      title: '提示',
      type: 1,
      btn: ['确定', '继续申报'],
      cancel: function (index, layero) {
        $('body').unmask();
        if (typeof umMaskZdy == 'function') {
          umMaskZdy();
        }
        prepareMakeFlag = true;
        return;
      },
      btn2: function (index) {
        parent.layer.close(index);
        // 执行回调函数
        ctips(isSecondCall);
      },
    },
    function (index) {
      parent.layer.close(index);
      $('body').unmask();
      if (typeof umMaskZdy == 'function') {
        umMaskZdy();
      }
      prepareMakeFlag = true;
      return;
    },
  );
}

/**
 * 不强制扫描
 * @param btnName
 * @param swjgDm
 * @param isSecondCall
 * @param reqParams
 */
function extractedNotMandatoryScan(btnName, swjgDm, isSecondCall, reqParams) {
  parent.layer.confirm(
    '请选择"风险提示服务"或"' +
      btnName +
      '"<br/>风险提示服务：享受税收政策风险扫描服务，提高申报数据质量。<br/>' +
      btnName +
      '：跳过风险提示服务，继续' +
      btnName +
      '。',
    {
      icon: 3,
      title: '提示',
      type: 1,
      btn: ['风险提示服务', btnName],
      cancel: function (index, layero) {
        $('body').unmask();
        if (typeof umMaskZdy == 'function') {
          umMaskZdy();
        }
        prepareMakeFlag = true;
        return;
      },
      btn2: function (index) {
        parent.layer.close(index);
        if (swjgDm == '144' && yhsjly != undefined && yhsjly == '0') {
          parent.layer.confirm(
            '请确认是否提交申报，确认则提交，取消则返回申报表页面。',
            {
              title: '提示',
              type: 1,
              btn: ['确定', '取消'],
              cancel: function (index, layero) {
                $('body').unmask();
                if (typeof umMaskZdy == 'function') {
                  umMaskZdy();
                }
                prepareMakeFlag = true;
                return;
              },
            },
            function (index) {
              parent.layer.close(index);
              // 执行回调函数
              ctips(isSecondCall);
            },
            function (index) {
              parent.layer.close(index);
              $('body').unmask();
              if (typeof umMaskZdy == 'function') {
                umMaskZdy();
              }
              prepareMakeFlag = true;
              return;
            },
          );
        } else {
          // 执行回调函数
          ctips(isSecondCall);
        }
      },
    },
    function (index) {
      parent.layer.close(index);
      extractedShowMessage(reqParams, isSecondCall);
    },
  );
}

/**
 * 企业b年度风险扫描
 * @param isSecondCall
 */
function doBeforeCtipsVerify(isSecondCall) {
  var paramsValue = getNbfxsmParams();
  var reqParams = paramsValue.reqParams;
  var fxsmStatus = paramsValue.fxsmStatus;
  var swjgDm = paramsValue.swjgDm;

  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    fxsmStatus = '0';
  }

  swjgDm = swjgDm != '' && swjgDm != null && swjgDm != undefined ? swjgDm.substring(0, 3) : '';
  var btnName = '申报';
  if (swjgDm == '144' && yhsjly != undefined && yhsjly == '0') {
    btnName = '提交';
  }

  //江苏非网报区个性化校验
  if (swjgDm == '132') {
    var cwbsmjyBz = getCwbsmjyBz(); //获取财务报送免校验标志
    if (cwbsmjyBz == false) {
      var cwbbsfybsBz = formData.hq_.sfsbcwbb; //财务报表是否已报送标志
      if (cwbbsfybsBz == 'N') {
        qysdsb_cwbbbsts('2');
        prepareMakeFlag = true;
        return;
      }
    }
  }

  if (fxsmStatus == '0') {
    // 执行回调函数
    ctips(isSecondCall);
  } else if (fxsmStatus == '1') {
    /* 不强制扫描 有风险扫描的弹框提示 */
    extractedNotMandatoryScan(btnName, swjgDm, isSecondCall, reqParams);
  } else if (fxsmStatus == '2') {
    /* 强制扫描 没有风险扫描的弹框提示 */
    extractedShowMessage(reqParams, isSecondCall);
  } else {
    /* 未配置 */
    extractedNotConfigured(isSecondCall);
  }
}

function setFhtjxwqyyhjzsdseLj(skssqz, yhjnsds, zsfsDm, ynsdseLj, fhtjxxwlqyjmsdseLj, yyjsdseLj, nhdsdse, sqHzsdse) {
  var reVal = 0;
  var year = skssqz.split('-')[0];
  var month = skssqz.split('-')[1];
  if (year == 2020 && (month == 6 || month == 9)) {
    if (yhjnsds == 'Y') {
      if (zsfsDm == '404' || zsfsDm == '403') {
        reVal = MAX(ROUND(ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj, 2), 0);
      } else {
        reVal = ROUND(nhdsdse + sqHzsdse, 2);
      }
    } else {
      reVal = 0;
    }
  } else {
    reVal = 0;
  }
  return reVal;
}

function setYbtsdseLj(ynsdseLj, fhtjxxwlqyjmsdseLj, yyjsdseLj, zsfsDm) {
  var reVal = 0;
  if (zsfsDm != '402') {
    reVal = ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj;
  }

  if (reVal < 0) {
    reVal = 0;
  }

  return reVal;
}

function setYbtsdseLj2(zsfsDm, nhdsdse) {
  var reVal = 0;
  if (zsfsDm == '402') {
    reVal = nhdsdse;
  }

  if (reVal < 0) {
    reVal = 0;
  }

  return reVal;
}

function setYbtsdseLj2020(
  yhjnsds,
  zsfsDm,
  nhdsdse,
  sqHzsdse,
  skssqz,
  ynsdseLj,
  fhtjxxwlqyjmsdseLj,
  yyjsdseLj,
  fhtjxwqyyhjzsdseLj,
) {
  var reVal = 0;
  var year = skssqz.split('-')[0];
  var month = skssqz.split('-')[1];
  if (year == 2020 && (month == 6 || month == 9)) {
    if (zsfsDm != '402') {
      var subVal = ROUND(ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj - fhtjxwqyyhjzsdseLj, 2);
      reVal = subVal < 0 ? 0 : subVal;
    } else {
      if (yhjnsds == 'Y') {
        reVal = 0;
      } else {
        reVal = nhdsdse < 0 ? 0 : nhdsdse;
      }
    }
  } else if (year == 2020 && month == 12) {
    if (zsfsDm == '402') {
      reVal = ROUND(nhdsdse + sqHzsdse, 2);
    }
  }
  return reVal;
}

function setYbtsdseLjNew(ynsdseLj, fhtjxxwlqyjmsdseLj, yyjsdseLj, fhtjxwqyyhjzsdseLj) {
  var yhjnsds = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yhjnsds;
  var yjdndsbbj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yjdndsbbj;
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var nhdsdse = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.nhdsdse;
  var sqHzsdse = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqHzsdse;
  var skssqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var reVal = 0;
  var year = skssqz.split('-')[0];
  var month = skssqz.split('-')[1];
  if (year == 2020 && (month == 6 || month == 9)) {
    if (zsfsDm != '402') {
      var subVal = ROUND(ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj - fhtjxwqyyhjzsdseLj, 2);
      reVal = subVal < 0 ? 0 : subVal;
    } else {
      if (yhjnsds == 'Y') {
        reVal = 0;
      } else {
        reVal = nhdsdse < 0 ? 0 : nhdsdse;
      }
    }
  } else if (yjdndsbbj == '0' && year == 2020 && month == 12 && zsfsDm == '402') {
    reVal = ROUND(nhdsdse + sqHzsdse, 2);
  } else if (yjdndsbbj == '1') {
    reVal = ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj;
  } else {
    reVal = setYbtsdseLjOld(ynsdseLj, fhtjxxwlqyjmsdseLj, yyjsdseLj);
  }
  return reVal;
}

function setYbtsdseLjOld(ynsdseLj, fhtjxxwlqyjmsdseLj, yyjsdseLj) {
  var zsfsDm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zsfsDm;
  var nhdsdse = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.nhdsdse;
  var reVal = 0;
  if (zsfsDm != '402') {
    reVal = ynsdseLj - fhtjxxwlqyjmsdseLj - yyjsdseLj;
  } else {
    reVal = nhdsdse;
  }
  return reVal < 0 ? 0 : reVal;
}

function setQycyrsQnpjrs(qccyrs1, qmcyrs1, qccyrs2, qmcyrs2, qccyrs3, qmcyrs3, qccyrs4, qmcyrs4, ysbjds) {
  var avgCyrs1 = (qccyrs1 + qmcyrs1) / 2;

  var avgCyrs2 = (qccyrs2 + qmcyrs2) / 2;

  var avgCyrs3 = (qccyrs3 + qmcyrs3) / 2;

  var avgCyrs4 = (qccyrs4 + qmcyrs4) / 2;

  return ROUND((avgCyrs1 + avgCyrs2 + avgCyrs3 + avgCyrs4) / (ysbjds + 1), 0);
}

function setZczeQnpjs(qczcze1, qmzcze1, qczcze2, qmzcze2, qczcze3, qmzcze3, qczcze4, qmzcze4, ysbjds) {
  var zcze1Avg = (qczcze1 + qmzcze1) / 2;

  var zcze2Avg = (qczcze2 + qmzcze2) / 2;

  var zcze3Avg = (qczcze3 + qmzcze3) / 2;

  var zcze4Avg = (qczcze4 + qmzcze4) / 2;

  return ROUND((zcze1Avg + zcze2Avg + zcze3Avg + zcze4Avg) / (ysbjds + 1), 2);
}

function setQccyrs(isjbjmyb, qm, qccyrs1, qccyrs2, qccyrs3, qccyrs4) {
  var reval = 0;
  if (isjbjmyb == 'Y') {
    if (qm == 3) {
      reval = qccyrs1;
    } else if (qm == 6) {
      reval = qccyrs2;
    } else if (qm == 9) {
      reval = qccyrs3;
    } else {
      reval = qccyrs4;
    }
    return Number(reval);
  } else {
    return '';
  }
}

function setQmcyrs(isjbjmyb, qm, qmcyrs1, qmcyrs2, qmcyrs3, qmcyrs4) {
  var reval = 0;
  if (isjbjmyb == 'Y') {
    if (qm == 3) {
      reval = qmcyrs1;
    } else if (qm == 6) {
      reval = qmcyrs2;
    } else if (qm == 9) {
      reval = qmcyrs3;
    } else {
      reval = qmcyrs4;
    }
    return Number(reval);
  } else {
    return '';
  }
}

function setQczcze(isjbjmyb, qm, qczcze1, qczcze2, qczcze3, qczcze4) {
  var reval = 0;
  if (isjbjmyb == 'Y') {
    if (qm == 3) {
      reval = qczcze1;
    } else if (qm == 6) {
      reval = qczcze2;
    } else if (qm == 9) {
      reval = qczcze3;
    } else {
      reval = qczcze4;
    }
    return Number(reval);
  } else {
    return '';
  }
}

function setQmzcze(isjbjmyb, qm, qmzcze1, qmzcze2, qmzcze3, qmzcze4) {
  var reval = 0;
  if (isjbjmyb == 'Y') {
    if (qm == 3) {
      reval = qmzcze1;
    } else if (qm == 6) {
      reval = qmzcze2;
    } else if (qm == 9) {
      reval = qmzcze3;
    } else {
      reval = qmzcze4;
    }
    return Number(reval);
  } else {
    return '';
  }
}

//计算本年已申报的季度数
function getYsbjds(sqZ, bnjd1sfysb, bnjd2sfysb, bnjd3sfysb) {
  var sbyf = sqZ.split('-')[1];

  if (sbyf <= 3) {
    return 0;
  }

  if (sbyf >= 4 && sbyf <= 6 && bnjd1sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 4 && sbyf <= 6 && bnjd1sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N' && bnjd3sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N' && bnjd3sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N' && bnjd3sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N' && bnjd3sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'N') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'Y') {
    return 3;
  }
}

function cleanjdInfo(skssqz, skssqq, zsfsDm) {
  var qzMonth = skssqz.split('-')[1];
  var qqMonth = skssqq.split('-')[1];
  if (zsfsDm == '402' || (qzMonth - qqMonth == 0 && qzMonth != 3 && qzMonth != 6 && qzMonth != 9 && qzMonth != 12)) {
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs1 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs1 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs2 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs2 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs3 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs3 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs4 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs4 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze1 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze1 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze2 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze2 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze3 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze3 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze4 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze4 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze4 = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zczeQnpjs = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qycyrsQnpjrs = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.gjxzhjzhy = '';
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.sfsyxxwlqy = '';
  }
}

function setJmqyzjgxhlqysyLj(
  xsd246qybz,
  ybgxhldqyxtzsymzqysdsLj,
  hgttzgxhlsdmzqysdsLj,
  sgttzgxhlsdmzqysdsLj,
  cxqygfgxhlsdmzqysdsLj,
  yxzlxsrdmzqysdsLj,
) {
  if (xsd246qybz == 'Y') {
    return ROUND(
      ybgxhldqyxtzsymzqysdsLj + hgttzgxhlsdmzqysdsLj + sgttzgxhlsdmzqysdsLj + cxqygfgxhlsdmzqysdsLj + yxzlxsrdmzqysdsLj,
      2,
    );
  }
  return formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.jmqyzjgxhlqysyLj;
}

//提交时处理这次2.46XSD  20年6月属期或第2季度起适用改版的与之前版本的报文差异
function delSubmitWell(xsd246qybz) {
  if (xsd246qybz == 'N') {
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs1;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs1;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs2;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs2;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs3;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs3;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs4;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs4;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze1;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze1;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze2;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze2;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze3;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze3;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze4;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze4;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yhjnsds;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.fhtjxwqyyhjzsdseLj;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.bhzsm;
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ybgxhldqyxtzsymzqysdsLj;
  } else {
    delete formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.kjzgqzrq;

    var sqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
    var sbyf = parseInt(sqz.split('-')[1], 10);

    if (sbyf <= 3) {
      var qccyrs1 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs1;
      var qmcyrs1 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs1;
      var qczcze1 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze1;
      var qmzcze1 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze1;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs = qccyrs1;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs = qmcyrs1;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze = qczcze1;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze = qmzcze1;
    }

    if (sbyf >= 4 && sbyf <= 6) {
      var qccyrs2 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs2;
      var qmcyrs2 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs2;
      var qczcze2 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze2;
      var qmzcze2 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze2;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs = qccyrs2;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs = qmcyrs2;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze = qczcze2;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze = qmzcze2;
    }

    if (sbyf >= 7 && sbyf <= 9) {
      var qccyrs3 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs3;
      var qmcyrs3 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs3;
      var qczcze3 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze3;
      var qmzcze3 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze3;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs = qccyrs3;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs = qmcyrs3;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze = qczcze3;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze = qmzcze3;
    }

    if (sbyf >= 10 && sbyf <= 12) {
      var qccyrs4 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs4;
      var qmcyrs4 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs4;
      var qczcze4 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze4;
      var qmzcze4 = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze4;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs = qccyrs4;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs = qmcyrs4;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze = qczcze4;
      formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze = qmzcze4;
    }
  }

  var skssqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  //处理一些特殊节点
  if (skssqz > '2020-11-30') {
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yhjnsds = '';
  }

  var yhjnsds = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.yhjnsds;
  if (yhjnsds != 'N') {
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.bhzsm = '';
  }
}

function setJbr() {
  var zcval = '';
  var sqzMonth = formData.fq_.sssq.sqZ.split('-')[1];
  var sqqMonth = formData.fq_.sssq.sqQ.split('-')[1];
  var subMonth = sqzMonth - sqqMonth;
  var jbr = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbr;
  var ysJbr = formData.kz_.temp.zb.ysJbr;
  var xm = formData.fq_.smzxx.xm;
  var smzbz = formData.fq_.smzxx.smzbz;

  if (formData.fq_.sssq.sqZ < '2019-06-30') {
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbr = '';
    return;
  }

  if ((sqzMonth == 3 || sqzMonth == 6 || sqzMonth == 9 || sqzMonth == 12) && subMonth != 11) {
    if (ysJbr != '') {
      zcval = ysJbr;
    } else {
      if (smzbz == 'Y') {
        zcval = xm;
      } else {
        zcval = jbr;
      }
    }
  } else {
    if (smzbz == 'Y') {
      zcval = xm;
    } else {
      zcval = jbr;
    }
  }
  formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbr = zcval;
}

function setysdjJbrzyzjhm() {
  var zcval = '';
  var sqzMonth = formData.fq_.sssq.sqZ.split('-')[1];
  var sqqMonth = formData.fq_.sssq.sqQ.split('-')[1];
  var subMonth = sqzMonth - sqqMonth;
  var jbrzyzjhm = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbrzyzjhm;
  var ysZjhm = formData.kz_.temp.zb.ysZjhm;
  var zjhm = formData.fq_.smzxx.zjhm;
  var smzbz = formData.fq_.smzxx.smzbz;

  if (formData.fq_.sssq.sqZ < '2019-06-30') {
    formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbrzyzjhm = '';
    return;
  }

  if ((sqzMonth == 3 || sqzMonth == 6 || sqzMonth == 9 || sqzMonth == 12) && subMonth != 11) {
    if (ysZjhm != '') {
      if (smzbz == 'Y') {
        if (ysZjhm.length >= 15) {
          zcval = ysZjhm.replace(ysZjhm.substring(6, 14), '********');
        } else {
          zcval = ysZjhm;
        }
      } else {
        zcval = jbrzyzjhm;
      }
    } else {
      if (smzbz == 'Y') {
        if (zjhm.length >= 15) {
          zcval = zjhm.replace(zjhm.substring(6, 14), '********');
        } else {
          zcval = zjhm;
        }
      } else {
        zcval = jbrzyzjhm;
      }
    }
  } else {
    if (smzbz == 'Y') {
      if (zjhm.length >= 15) {
        zcval = zjhm.replace(zjhm.substring(6, 14), '********');
      } else {
        zcval = zjhm;
      }
    } else {
      zcval = jbrzyzjhm;
    }
  }
  formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.slxxForm.jbrzyzjhm = zcval;
}

function alertYhjnsds(yhjnsds, hzje) {
  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }
  var skssqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;
  var xsd246qybz = formData.kz_.xsd246qybz;
  var sfsyxxwlqy = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.sfsyxxwlqy;
  var isgd = formData.fq_.nsrjbxx.zgswjDm.indexOf('144') === 0;

  if (
    xsd246qybz === 'Y' &&
    DATE_CHECK_TIME_SIZE(skssqz, '2020-09-30') &&
    yhjnsds === 'Y' &&
    sfsyxxwlqy === 'Y' &&
    isgd &&
    hzje > 0
  ) {
    formData.kz_.oldYhjnsds = 'Y';
  } else {
    formData.kz_.oldYhjnsds = 'N';
    formData.kz_.yhjnsdstsbz = 'N';
  }
  var oldYhjnsds = formData.kz_.oldYhjnsds;
  var yhjnsdstsbz = formData.kz_.yhjnsdstsbz;
  if (oldYhjnsds == 'Y' && yhjnsdstsbz == 'N') {
    formData.kz_.yhjnsdstsbz = 'Y';
    parent.layer.open({
      content:
        '尊敬的纳税人，您当前符合小型微利企业延缓缴纳政策的规定，提示如下：<br> 1.您可通过填报本申报表第L15行暂延缴本季度应补缴的企业所得税;<br>2.延缴税款自动计算，在2020年4季度税款所属期申报时一并缴纳;<br>3.不会产生滞纳金;<br>4.不会影响纳税信用评价。',
      title: '提示',
      area: ['540px', '300px'],
      type: 1,
      btn: ['确定'],
    });
  }
}

/**
 * 云上电局 季末资产总额取值逻辑 2020第二季度后开始执行
 * @param bnjdsfysb  $.fq_.bnjd1sfysb  $.fq_.bnjd2sfysb  $.fq_.bnjd3sfysb
 * @param sqQmzcze  $..sbQysdshdzsyjdndsbqtxxVO.sqQmzcze1、2、3、4
 * @param jczczeT  $..kz_.temp.zb.jmzcze_t
 * @returns {*}
 */
function ysdjGetQmzcze(bnjdsfysb, sqQmzcze, jmzczeT) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  // 本季度已申报，返回核心接口的资产总额
  if (bnjdsfysb == 'Y') {
    // 前几季度
    if (lsbBz) {
      if (sqQmzcze == '' || sqQmzcze == 0 || sqQmzcze == undefined || sqQmzcze == null) {
        return 0.01;
      } else {
        // 0.0001 的情況零申报或静默申报应该 返回1
        if (ROUND(sqQmzcze, 2) == 0) {
          return 0.01;
        } else {
          return sqQmzcze;
        }
      }
    } else {
      return sqQmzcze;
    }
  } else {
    // 当前季度
    // 零申报处理
    if (lsbBz) {
      if (ROUND(jmzczeT, 2) == 0) {
        return 0.01;
      } else {
        return jmzczeT == 0 ? 0.01 : jmzczeT;
      }
    } else {
      return jmzczeT;
    }
  }
}

/**
 * 云上电局 季初资产总额取值逻辑 2020第二季度开始执行
 * @param bnjdsfysb
 * @param sqQczcze
 * @param jczczeT
 * @param sqQmzcze
 */
function ysdjGetQczcze(bnjdsfysb, sqQczcze, jczczeT, sqQmzcze) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (bnjdsfysb == 'Y') {
    // 前几季度
    if (lsbBz) {
      if (sqQczcze == '' || sqQczcze == 0 || sqQczcze == undefined || sqQczcze == null) {
        return 0.01;
      } else {
        if (ROUND(sqQczcze, 2) == 0) {
          return 0.01;
        } else {
          return sqQczcze;
        }
      }
    } else {
      return sqQczcze;
    }
  } else {
    //当前季度
    if (lsbBz) {
      // 第一季度 取同期财务报表期初资产总额，财报无数据则默认1； （其他季）：取期初数sqQmzcze，为0则默认1；
      if (typeof sqQmzcze != 'undefined') {
        if (sqQmzcze == 0 || sqQmzcze == '') {
          return 0.01;
        }
        return ROUND(sqQmzcze, 2) == 0 ? 0.01 : sqQmzcze;
      }
      return ROUND(jczczeT, 2) == 0 ? 0.01 : jczczeT;
    } else {
      // 除第一季度外 季初资产总额 取上一季度末资产总额
      if (typeof sqQmzcze != 'undefined') {
        return sqQmzcze;
      }
      return jczczeT;
    }
  }
}
/**
 * 云上电局从业人数取值逻辑 2020第二季度后开始执行
 * @param bnjdsfysb   $.fq_.bnjd1sfysb
 * @param sqCyrs   $..sbQysdshdzsyjdndsbqtxxVO.sqQccyrs1、2、3、4
 * @param ysSqQmcyrs   $..temp.zb.ysSqQmcyrs
 * @param sqQmcyrs 上期期末从业人数
 */
function ysdjGetcyrs(bnjdsfysb, sqCyrs, ysSqQmcyrs, sqQmcyrs) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (bnjdsfysb == 'Y') {
    if (lsbBz) {
      if (sqCyrs == '' || sqCyrs == 0 || sqCyrs == undefined || sqCyrs == null) {
        return 1;
      } else {
        return sqCyrs;
      }
    } else {
      return sqCyrs;
    }
  } else {
    if (lsbBz) {
      // 取期初sqQmcyrs，为空取客户资料从业人数，客户资料未设置则默认1；
      if (typeof sqQmcyrs != 'undefined') {
        if (sqQmcyrs == 0 || sqQmcyrs == '') {
          if (ysSqQmcyrs == 0 || ysSqQmcyrs == '') {
            return 1;
          } else {
            return ysSqQmcyrs;
          }
        }
        return sqQmcyrs;
      }
      return ysSqQmcyrs == 0 ? 1 : ysSqQmcyrs;
    } else {
      // 除第一季度 从业人数先从上期季末从业人数取 没有从客户资料取
      if (typeof sqQmcyrs != 'undefined') {
        if (sqQmcyrs != '' && sqQmcyrs != 0) {
          return sqQmcyrs;
        }
        return ysSqQmcyrs == 0 ? '' : ysSqQmcyrs;
      }
      return ysSqQmcyrs == 0 ? '' : ysSqQmcyrs;
    }
  }
}

function tqsbVerify() {
  var nsrztDm = formData.fq_.nsrjbxx.nsrztDm;
  var sfzdtqsb = formData.fq_.sfzdtqsb;
  var skssqz = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.nsrxxFrom.skssqz;

  //清算状态的纳税人可以提前申报，不做监控
  if (nsrztDm == '06' || sfzdtqsb != 'Y') {
    return;
  }

  //当前日期
  var dateNow = new Date();
  //暑期止对应的时间
  var dateSqz = new Date(skssqz);

  //暑期止大于等于当前时间说明提前申报
  if (dateSqz >= dateNow) {
    var msg = '为保护纳税人权益，电子税务局无法办理提前申报预缴企业所得税的业务，请进入申报期后再进行申报。';
    var a = parent.layer.confirm(
      msg,
      {
        area: ['380px', '200px'],
        title: '提示',
        type: 1,
        closeBtn: 0,
        btn: ['确定'],
      },
      function (data) {
        parent.layer.close(a);
        closeWin();
      },
    );
  }
}

//GDSDZSWJ-15809:广东个性化：优惠政策告知书弹框
var indexqysdsbGzs;
function yhzcGzsBtn() {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var isYsdj = parent.location.href.indexOf('isYsdj=Y') > -1;
  var dqdm = '';
  if (zgswjDm !== '' && zgswjDm != null && zgswjDm != undefined) {
    //dqdm=zgswjDm.substring(1,3);
  }

  if (dqdm != '44' || isYsdj) {
    return;
  }

  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || parent.location.href.indexOf('hbsb=Z') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }
  var sbnd = formData.fq_.sssq.sqZ.split('-')[0];
  var yhzcgzsUrl = pathRoot + '/static/sb/qysds_b_18yjd/yhzcgzs_2021.html?';
  var heigt = '418px';
  if (sbnd < 2021) {
    yhzcgzsUrl = pathRoot + '/static/sb/qysds_b_18yjd/yhzcgzs.html?';
    heigt = '500px';
  }
  // 弹出告知书
  indexqysdsbGzs = layer.open({
    type: 2,
    title: '',
    area: ['1050px', heigt],
    closeBtn: 0,
    content: yhzcgzsUrl,
  });
}

//厦门个性化
/*
 * 在B类年度首次申报（不包含更正申报），提交审核时，加入以下校验：
 * 当满足以下任一情况时，审核不通过，并给出提示：尊敬的纳税人，您填报的“按年度填报信息-从业人数（填写平均值）”的值与季度预缴申报表填报的从业人数不一致，若季度预缴填报错误，请更正第四季度企业所得税预缴申报表后再进行年度申报，若年度从业人数填报错误，请准确填报“按年度填报信息-从业人数（填写平均值）”。
 * 1、 该项≤300，且109项小型微利企业为“是”，且该纳税人全年从业人数平均值＞300；
 * 2、 该项＞300，且该纳税人全年从业人数平均值≤ 300，且第四季度预缴申报表中附报信息中“小型微利企业”为“是”。
 * PS：“该纳税人全年从业人数平均值”根据本年申报预缴记录，及核心初始化返回<sqQccyrs1/>、<sqQmcyrs1/>、<sqQccyrs2/>、<sqQmcyrs2/>、<sqQccyrs3/>、<sqQmcyrs3/>、<sqQccyrs4/>、<sqQmcyrs4/>这8个节点重新计算得出，计算规则与预缴时“从业人数-季度平均值”相同。
 * */
function vailqycyrsQnpjrs(
  sqQccyrs1,
  sqQmcyrs1,
  sqQccyrs2,
  sqQmcyrs2,
  sqQccyrs3,
  sqQmcyrs3,
  sqQccyrs4,
  sqQmcyrs4,
  ysbjds,
  qycyrsQnpjrs,
) {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var result = true;
  if (swjgDm.substring(0, 5) == '13502') {
    var qycyrsQnpjrs = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qycyrsQnpjrs;
    var sfsyxxwlqy = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.sfsyxxwlqy;
    var gnsrqncyrspjz = setQycyrsQnpjrs(
      sqQccyrs1,
      sqQmcyrs1,
      sqQccyrs2,
      sqQmcyrs2,
      sqQccyrs3,
      sqQmcyrs3,
      sqQccyrs4,
      sqQmcyrs4,
      ysbjds,
    );
    //该纳税人全年从业人数平均值
    if (qycyrsQnpjrs <= 300) {
      if (sfsyxxwlqy == 'Y' && gnsrqncyrspjz > 300) {
        result = false;
      }
    } else if (qycyrsQnpjrs > 300) {
      sfsyxxwlqy = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqXwbz;
      if (sfsyxxwlqy == 'Y' && gnsrqncyrspjz <= 300) {
        result = false;
      }
    }
  }
  return result;
}

function beforeHtmlLoadAction() {
  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  //云上电局财报校验
  if (parent.location.href.indexOf('isYsdj=Y') > -1 && ('3702' == swjgDm15 || '2102' == swjgDm15)) {
    var hsfs = formData.fq_.nsrjbxx.hsfs;
    var zfjglxDm = formData.fq_.nsrjbxx.zfjglxDm;

    //非独立核算跨地区经营汇总纳税企业分支机构 才校验
    if (hsfs == '20' || zfjglxDm == '2' || zfjglxDm == '3') {
      return;
    }
    var params = {};
    var qqbw = {};
    qqbw.djxh = formData.fq_.nsrjbxx.djxh;
    qqbw.tSsqzStr = formData.fq_.sssq.sqZ;
    params.qqbw = JSON.stringify(qqbw);
    params.sid = 'dzswj.wbzy.dj.djCwkjzdjhsrjbabgs.cxCwbaWqxdm';
    var chbnxxList = [];
    parent.requestYwztData(
      params,
      function (data) {
        if (!data) {
          return;
        }
        if (typeof data === 'string') {
          data = JSON.parse(data);
        }
        // 记录全局信息，不用每次都请求接口
        chbnxxList = data.chbnxx;
      },
      function (data) {
        console.log('查询财会备案信息异常', data);
      },
    );

    if (chbnxxList && chbnxxList.length > 0) {
      //财务会计制度备案代码非('223','224','233','215')
      for (var i = 0; i < chbnxxList.length; i++) {
        var chbnxx = chbnxxList[i];
        if (['223', '224', '233', '215'].indexOf(chbnxx.kjzdzzDm) > -1) {
          return;
        }
      }
    }

    if (formData.fq_.cwbbsb != 'Y') {
      parent.layer.open({
        title: '提示',
        type: 1,
        area: ['400px'],
        content: '当期未报送财务报表，请先报送财务报表。给您带来不便，请谅解。',
        btn: ['确定'],
        yes: function (index, layero) {
          CloseWebPage();
        },
        cancel: function (index, layero) {
          CloseWebPage();
        },
      });
    }
  }
}

function afterFormulaExcuted() {
  if (parent.location.href.indexOf('qqdcx=Y') > -1) {
    return;
  }
  var gzsbbz = formData.kz_.temp.gzsbbz;
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var swjgDm03 = swjgDm ? swjgDm.substring(0, 3) : '';
  var swjgDm05 = swjgDm ? swjgDm.substring(0, 5) : '';
  var jpathList = [];

  if (swjgDm05 == '13502' && 'Y' != gzsbbz) {
    // 从业人数和资产总额
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs1 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs1 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs1
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs1');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs1 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs1 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs1
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs1');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze1 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze1 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze1
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze1');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze1 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze1 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze1
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze1');
    }

    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs2 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs2 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs2
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs2');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs2 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs2 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs2
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs2');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze2 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze2 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze2
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze2');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze2 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze2 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze2
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze2');
    }

    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs3 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs3 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qccyrs3
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQccyrs3');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs3 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs3 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmcyrs3
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmcyrs3');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze3 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze3 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qczcze3
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQczcze3');
    }
    if (
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze3 &&
      formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze3 !=
        formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.qmzcze3
    ) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqQmzcze3');
    }

    //刷新主表18行数据
    if (formData.hq_.sbQysdshdzsyjdndsbqtxxVO.sqyjje) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.sqyjje');
    }
    if (formData.hq_.sbQysdshdzsyjdndsbqtxxVO.kyyjye) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.kyyjye');
    }
    if (formData.hq_.sbQysdshdzsyjdndsbqtxxVO.yyyjje) {
      jpathList.push('hq_.sbQysdshdzsyjdndsbqtxxVO.yyyjje');
    }
  }

  if (jpathList.length > 0) {
    formulaEngine.apply4List(jpathList);
  }
}

//福建个性化，按季度填报信息-资产总额-季度平均值 差异比校验
// 1、上期未申报则不进行校验，
// 2、该校验仅在正常申报带出，更正申报不校验
function zczeQnpjzValid(zczeQnpjs) {
  var gzsbbz = formData.kz_.temp.gzsbbz;

  if (gzsbbz == 'Y') {
    return true;
  }
  var zczeQnpjs = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.zczeQnpjs;
  var dqjdSsjd = formData.fq_.dqjdSsjd; //第几季度
  var sqjdZczeQnpjs = 0;
  if (dqjdSsjd == '1jd') {
    //第一季度查询上年第4季度主表ZCZE_QNPJS节点数据,fq_.sqjdZczeQnpjs
    if (formData.fq_.sqjdZczeQnpjs == '') {
      //上期未申报则不进行校验
      return true;
    } else {
      sqjdZczeQnpjs = formData.fq_.sqjdZczeQnpjs;
    }
  } else {
    //第二、三、四季度取核心接口返回sbQysdshdzsyjdndsbqtxxVO.zczeQnpjs
    sqjdZczeQnpjs = formData.hq_.sbQysdshdzsyjdndsbqtxxVO.zczeQnpjs;
  }

  //（本季数“资产总额-季度平均值”-上季“资产总额-季度平均值”)）/上季“资产总额-季度平均值”的绝对值大于0.3）
  if (ABS(ROUND((zczeQnpjs - sqjdZczeQnpjs) / sqjdZczeQnpjs, 4)) > 0.3) {
    return false;
  } else {
    return true;
  }
}

//福建个性化
//按季度填报信息-小型微利企业选择项 分机构校验
function xxwlqyFzjgValid() {
  var fzjgArr = ['分公司', '分部', '营业部', '项目部', '分中心', '代表处', '办事处', '支行', '分院'];
  var ret = true;
  var nsrmc = formData.fq_.nsrjbxx.nsrmc;
  for (var i = 0; i < fzjgArr.length; i++) {
    if (nsrmc.indexOf(fzjgArr[i]) != -1) {
      ret = false;
      break;
    }
  }
  return ret;
}

// 将应补退税额同步给vue外壳
function postYbtse() {
  var ybtsdseLj = formData.ht_.qysdshdzsSbbdxxVO.qysdshdzsyjdndsb.qysdsyddhndnssbbblFrom.ybtsdseLj;
  var message = { type: 'ybtse', ywbm: parent.ywbm, ybtse: ybtsdseLj };
  parent.postMessage2Vue(message);
}

export default {
  getSnxwqy,
  sfxwqy_ND,
  getYbxwqy,
  getXwinit,
  getBqxwqy,
  getXwsdpd,
  getYjse,
  setZjlxMc,
  hdzsVaild,
  getJmsde,
  closeWin,
  getMzzzdfjmeLj,
  setMzzzdfjmeLj,
  getJzfd,
  showMassge,
  getxwnbbz,
  clzsfsDm403,
  clzsfsDm404or402,
  clzsfsDm404,
  yjfsDM2,
  yjfsDM3,
  isEmptyObject,
  handleHtBw,
  sectionToChinese,
  numberToChinese,
  hadleFhBw,
  getNbfxsmParams,
  extractedShowMessage,
  extractedNotConfigured,
  extractedNotMandatoryScan,
  doBeforeCtipsVerify,
  setFhtjxwqyyhjzsdseLj,
  setYbtsdseLj,
  setYbtsdseLj2,
  setYbtsdseLj2020,
  setYbtsdseLjNew,
  setYbtsdseLjOld,
  setQycyrsQnpjrs,
  setZczeQnpjs,
  setQccyrs,
  setQmcyrs,
  setQczcze,
  setQmzcze,
  getYsbjds,
  cleanjdInfo,
  setJmqyzjgxhlqysyLj,
  delSubmitWell,
  setJbr,
  setysdjJbrzyzjhm,
  alertYhjnsds,
  ysdjGetQmzcze,
  ysdjGetQczcze,
  ysdjGetcyrs,
  tqsbVerify,
  yhzcGzsBtn,
  vailqycyrsQnpjrs,
  beforeHtmlLoadAction,
  afterFormulaExcuted,
  zczeQnpjzValid,
  xxwlqyFzjgValid,
  postYbtse,
};
