import { fetch } from '@/core/request';
import store from '@/pages/index/store';
const { sbzxCtl } = store.state['sb/dllfssrmxcj/form'];

/**
 * 当期已采集，作废提示重新采集，如不作废阻断采集
 */
function zfTips(){
	var dlfscjxx = formData.fq_.dllmxcjxx.dlfscjxx;
	if (dlfscjxx != undefined && dlfscjxx != "" && dlfscjxx.length > 0 && dlfscjxx[0].zlbscjuuid != undefined && dlfscjxx[0].zlbscjuuid != "") {
		window.$vue.$gtDialog.confirm({
			header: '提示',
			body: '本期已采集是否作废重新采集？',
			theme: 'info',
			cancelBtn: '否',
			confirmBtn: '是',
			closeOnOverlayClick: false,
			onCancel: () => {
				window.close();
			},
			onConfirm: async () => {
				await fetch({
					url: sbzxCtl + '/fsgjssb/dllfssrmxcj/v1/zfcjxx', 
					method: 'post', 
					data: { "zlbscjuuid": dlfscjxx[0].zlbscjuuid },
					noMessage: true,
				}).then(({ bizCode, body, bizMsg }) => {
					if (bizCode !== '00') {
						window.$vue.$gtDialog.error({
							header: '提示',
							body: "作废本期已采集电力信息失败。",
							closeOnOverlayClick: false,
							confirmBtn: '确定',
						});
					} else {
						if (body.zflag === 'Y') {
							window.$vue.$gtDialog.info({
								header: '提示',
								body: "作废本期已采集电力信息成功。",
								closeOnOverlayClick: false,
								confirmBtn: '确定',
							});
						} else {
							window.$vue.$gtDialog.info({
								header: '提示',
								body: "作废本期已采集电力信息失败。",
								closeOnOverlayClick: false,
								confirmBtn: '确定',
							});
						}
					}
				}).catch(({ message }) => {
					window.$vue.$gtDialog.error({
						header: '提示',
						body: "作废本期已采集电力信息失败，请稍侯再试。",
						closeOnOverlayClick: false,
						confirmBtn: '确定',
					});
				});
			},
		},{ type: 'confirm' });
	}
}
/**
 * 计算合计信息
 */
function calculate_fssrtysb_hjxx(t_checkedbz,xsdl,jfdl,bqynfe,jmfe,bqyjsfe,bqybtfe){
    var skxxGridlb=formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.skxxGridlb;
    var t_xsdlHj=0;
    var t_jfdlHj=0;
    var t_bqynfeHj=0;
    var t_jmfeHj=0;
    var t_bqyjsfeHj=0;
    var t_bqybtfeHj=0;
    for(var i=0;i<skxxGridlb.length;i++){
        if(skxxGridlb[i].t_checkedbz){
        	t_xsdlHj+=skxxGridlb[i].xsdl;
        	t_jfdlHj+=skxxGridlb[i].jfdl;
        	t_bqynfeHj+=skxxGridlb[i].bqynfe;
        	t_jmfeHj+=skxxGridlb[i].jmfe;
        	t_bqyjsfeHj+=skxxGridlb[i].bqyjsfe;
        	t_bqybtfeHj+=skxxGridlb[i].bqybtfe;
        }
    }
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_xsdlHj=t_xsdlHj;
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_jfdlHj=t_jfdlHj;
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_bqynfeHj=t_bqynfeHj;
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_jmfeHj=t_jmfeHj;
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_bqyjsfeHj=t_bqyjsfeHj;
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.t_bqybtfeHj=t_bqybtfeHj;
}

/**
 * 验证居民用电是否已存在（同一征收项目条件下）
 */
function yzJmydSfycz(zsxmDm,ydlx) {
	var skxxGridlb=formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.skxxGridlb;
	var n = 0;
	for(var i in skxxGridlb){
		if(skxxGridlb[i].zsxmDm== zsxmDm && skxxGridlb[i].ydlx== ydlx){
			n++;
			if(n>=2){
				return true;
			}
		}
	}
	return false;
}

//全选操作
function selectedAllFssr(allSelected){
    //选择或取消全选同时改变是否提交标志
    var skxxGridlb=formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.skxxGridlb;
    for(var i=0;i<skxxGridlb.length;i++){
        if(allSelected==true){
            skxxGridlb[i].t_checkedbz=true;
        }else{
            skxxGridlb[i].t_checkedbz=false;
        }
    }
    calculate_fssrtysb_hjxx();// 计算合计信息
    return allSelected;
}

/**
 * 删除未勾选的SkxxGridlb
 */
function delUnSelectedSkxxGridlb(){
    var skxxGridlb = formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.skxxGridlb;
    var skxxGridlb_submit= [];
    for(var i=0;i<skxxGridlb.length;i++){
        if(skxxGridlb[i].t_checkedbz){
            skxxGridlb_submit.push(skxxGridlb[i]);
        }
    }
    formData.ht_.dllfssrmxxxcjbywbw.dllfssrmxxxcjb.skxxGrid.skxxGridlb = skxxGridlb_submit;
}

export default {
	zfTips,
	calculate_fssrtysb_hjxx,
	yzJmydSfycz,
	selectedAllFssr,
	delUnSelectedSkxxGridlb
}