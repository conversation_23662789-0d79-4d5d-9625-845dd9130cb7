/* eslint-disable */
/**
 * Created by Administrator on 2016-05-07.
 */
/**
 * 删除报文中的节点
 * @param
 * @returns
 */
function delBranch() {
  const jsxmGridLb = formData.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbjsxmGrid.zzsjmssbmxbjsxmGridlbVO;
  for (let i = jsxmGridLb.length - 1; i >= 0; i--) {
    // 纳税人没有选择减免性质，则删除该节点
    if (jsxmGridLb[i].hmc === '' || jsxmGridLb[i].hmc === null) {
      jsxmGridLb.splice(i, 1);
    }
  }

  const msxmGridLb = formData.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbmsxmGrid.zzsjmssbmxbmsxmGridlbVO;
  for (let i = msxmGridLb.length - 1; i >= 0; i--) {
    // 纳税人没有选择减免性质，则删除该节点
    if (msxmGridLb[i].hmc === '' || msxmGridLb[i].hmc === null) {
      msxmGridLb.splice(i, 1);
    }
  }
}

/**
 * 删除报文中的节点,删除数组中某节点的值为空的下标数据
 * @param
 * @returns
 */
function delBranch2(data, key) {
  if (data !== null && data.length >= 1) {
    for (let i = data.length - 1; i >= 0; i--) {
      const grib = data[i];
      if (grib[key] === null || grib[key] === '') {
        data.splice(i, 1);
      }
    }
  }
}

/**
 * 删除节点,不包含合计/出口免税/跨境服务
 * @param data
 * @param key
 */
function delBranch3(flag, data, key) {
  if (flag === 1) {
    const { zzsjmssbmxbjsxmGridlbVO } = data;
    const { zzsjmssbmxbmsxmGridlbVO } = data;

    if (
      zzsjmssbmxbjsxmGridlbVO !== 'undefined' &&
      zzsjmssbmxbjsxmGridlbVO !== null &&
      zzsjmssbmxbjsxmGridlbVO.length > 0
    ) {
      for (let i = zzsjmssbmxbjsxmGridlbVO.length - 1; i >= 1; i--) {
        const grib = zzsjmssbmxbjsxmGridlbVO[i];
        if (grib[key] === null || grib[key] === '') {
          zzsjmssbmxbjsxmGridlbVO.splice(i, 1);
        }
      }
    }

    if (
      zzsjmssbmxbmsxmGridlbVO !== 'undefined' &&
      zzsjmssbmxbmsxmGridlbVO !== null &&
      zzsjmssbmxbmsxmGridlbVO.length > 0
    ) {
      for (let i = zzsjmssbmxbmsxmGridlbVO.length - 1; i >= 3; i--) {
        const grib = zzsjmssbmxbmsxmGridlbVO[i];
        if (grib[key] === null || grib[key] === '') {
          zzsjmssbmxbmsxmGridlbVO.splice(i, 1);
        }
      }
    }
  } else {
    const jsxmGridLb = data.zzsjmssbmxbjsxmGridlbVO;
    const msxmGridLb = data.zzsjmssbmxbmsxmGridlbVO;

    if (jsxmGridLb !== 'undefined' && jsxmGridLb !== null && jsxmGridLb.length > 0) {
      for (let i = jsxmGridLb.length - 1; i >= 0; i--) {
        const grib = jsxmGridLb[i];
        if (grib[key] === null || grib[key] === '') {
          jsxmGridLb.splice(i, 1);
        }
      }
    }

    if (msxmGridLb !== 'undefined' && msxmGridLb !== null && msxmGridLb.length > 0) {
      for (let i = msxmGridLb.length - 1; i >= 0; i--) {
        const grib = msxmGridLb[i];
        if (grib[key] === null || grib[key] === '') {
          msxmGridLb.splice(i, 1);
        }
      }
    }
  }
}

/**
 * 小规模差额征收起征点计算
 */
function xgmCezsqzd(
  bqfse,
  bqkce,
  ysfwxsqbhssr,
  bqfse5,
  bqkce5,
  ysfwxsqbhssr5,
  yzzzsbhsxse,
  xsczbdcbhsxse,
  msxse,
  ckmsxse,
) {
  // 存在差额征税
  if (ROUND(bqfse + bqkce + ysfwxsqbhssr, 2) > 0) {
    yzzzsbhsxse = ysfwxsqbhssr / 1.03;
  }
  // 存在差额征税
  if (ROUND(bqfse5 + bqkce5 + ysfwxsqbhssr5, 2) > 0) {
    xsczbdcbhsxse = ysfwxsqbhssr5 / 1.05;
  }
  return ROUND(yzzzsbhsxse + xsczbdcbhsxse + msxse + ckmsxse, 2);
}

/**
 * 改变计税依据、减免性质时重新计算减免税额，重复行所有行都重新计算
 */
function lhbsfssb_jmse(zsxmDm, ssjmxzDm) {
  const { sbxxGridlb } = formData.fxmtysbbdVO.fxmtySbb.sbxxGrid;
  const old_sbxxGridlb = formData.qcs.formContent.sbTysb.body.sbxxGrid.sbxxGridlb;
  for (let i = 0; i < sbxxGridlb.length; i++) {
    if (sbxxGridlb[i].zsxmDm == zsxmDm) {
      if (old_sbxxGridlb[i].ssjmxzDm == ssjmxzDm && old_sbxxGridlb[i].jmse == sbxxGridlb[i].bqjmsfe) {
        return old_sbxxGridlb[i].jmse;
      }
      return 0;
    }
  }
}

/**
 * 改变计税依据，根据起征点信息改变减免性质
 * @param zsJsyj
 * @param jsfyj
 * @param fjsQzdxx
 * @returns
 */
function lhbsfssb_ssjmxzDm(zsxmDm) {
  let ssjmxzDm = '';
  const qzdMxxxArr = [];
  const { zsJsyj } = formData.qcs.initData.tysbbInitData;
  const { sbxxGridlb } = formData.fxmtysbbdVO.fxmtySbb.sbxxGrid;
  const skssqq = formData.qcs.formContent.sbTysb.head.publicHead.sssq.rqQ;
  const skssqz = formData.qcs.formContent.sbTysb.head.publicHead.sssq.rqZ;
  const fjsQzdxx = formData.qcs.initData.tysbbInitData.fjsQzdMxxxs.fjsQzdMxxx;
  let fjsbqybtsfe = 0;
  const nsqxDm = calculateNsqxDm(skssqq, skssqz);
  for (let i = 0; i < sbxxGridlb.length; i++) {
    if (sbxxGridlb[i].zsxmDm === zsxmDm) {
      ssjmxzDm = typeof sbxxGridlb[i].ssjmxzDm === 'object' ? ssjmxzDm : sbxxGridlb[i].ssjmxzDm;
      fjsbqybtsfe = typeof sbxxGridlb[i].bqybtsfe !== 'number' ? fjsbqybtsfe : sbxxGridlb[i].bqybtsfe;
      break;
    }
  }

  // 获取纳服配置中起征点明细信息中的起征点信息
  if (fjsQzdxx !== undefined && $.isArray(fjsQzdxx)) {
    for (let i = 0; i < fjsQzdxx.length; i++) {
      qzdMxxxArr[fjsQzdxx[i].zsxmDm] = (parseFloat(fjsQzdxx[i].qzdje) - parseFloat(fjsQzdxx[i].ysbje)).toFixed(2);
    }
  }
  // 根据主税的计税依据判断起征点，未达起征点返回默认值
  if (
    qzdMxxxArr[`${zsxmDm}_${nsqxDm}`] !== undefined &&
    zsJsyj > 0 &&
    zsJsyj < qzdMxxxArr[`${zsxmDm}_${nsqxDm}`] &&
    fjsbqybtsfe !== 0
  ) {
    return '0099129999';
  }
  return ssjmxzDm;
}

function tlxfs_dktz_qckcje(qckcje) {
  const { dctlskdktzGridlb } = formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.dctlskdktzGrid;
  dctlskdktzGridlb[0].qckcje = ROUND(
    dctlskdktzGridlb[0].wtjgshje + qckcje - dctlskdktzGridlb[0].dklyje - dctlskdktzGridlb[0].qtlyje,
    2,
  );
  dctlskdktzGridlb[0].qckcynse = ROUND(dctlskdktzGridlb[0].qckcje * dctlskdktzGridlb[0].sl1, 2);
  if (dctlskdktzGridlb.length > 1) {
    for (let i = 1; i < dctlskdktzGridlb.length; i++) {
      dctlskdktzGridlb[i].qckcje = ROUND(
        dctlskdktzGridlb[i].wtjgshje +
          dctlskdktzGridlb[i - 1].qckcje -
          dctlskdktzGridlb[i].dklyje -
          dctlskdktzGridlb[i].qtlyje,
        2,
      );
      dctlskdktzGridlb[i].qckcynse = ROUND(dctlskdktzGridlb[i].qckcje * dctlskdktzGridlb[i].sl1, 2);
    }
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcjehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcje;
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcynsehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcynse;
  } else {
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcjehj = dctlskdktzGridlb[0].qckcje;
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcynsehj = dctlskdktzGridlb[0].qckcynse;
  }
}

function tlxfs_dktz_kc(wtjgshje, dklyje, qtlyje) {
  const { dctlskdktzGridlb } = formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.dctlskdktzGrid;
  dctlskdktzGridlb[0].qckcje = ROUND(
    dctlskdktzGridlb[0].wtjgshje +
      formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qckcje -
      dctlskdktzGridlb[0].dklyje -
      dctlskdktzGridlb[0].qtlyje,
    2,
  );
  dctlskdktzGridlb[0].qckcynse = ROUND(dctlskdktzGridlb[0].qckcje * dctlskdktzGridlb[0].sl1, 2);
  if (dctlskdktzGridlb.length > 1) {
    for (let i = 1; i < dctlskdktzGridlb.length; i++) {
      dctlskdktzGridlb[i].qckcje = ROUND(
        dctlskdktzGridlb[i].wtjgshje +
          dctlskdktzGridlb[i - 1].qckcje -
          dctlskdktzGridlb[i].dklyje -
          dctlskdktzGridlb[i].qtlyje,
        2,
      );
      dctlskdktzGridlb[i].qckcynse = ROUND(dctlskdktzGridlb[i].qckcje * dctlskdktzGridlb[i].sl1, 2);
    }
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcjehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcje;
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcynsehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcynse;
  } else {
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcjehj = dctlskdktzGridlb[0].qckcje;
    formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb4.qmkcynsehj = dctlskdktzGridlb[0].qckcynse;
  }
}

function dcxfs_dktz_qckcje(qckcje) {
  const { dctlskdktzGridlb } = formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.dctlskdktzGrid;
  dctlskdktzGridlb[0].qckcje = ROUND(
    dctlskdktzGridlb[0].wtjgshje + qckcje - dctlskdktzGridlb[0].dklyje - dctlskdktzGridlb[0].qtlyje,
    2,
  );
  dctlskdktzGridlb[0].qckcynse = ROUND(dctlskdktzGridlb[0].qckcje * dctlskdktzGridlb[0].sl1, 2);
  if (dctlskdktzGridlb.length > 1) {
    for (let i = 1; i < dctlskdktzGridlb.length; i++) {
      dctlskdktzGridlb[i].qckcje = ROUND(
        dctlskdktzGridlb[i].wtjgshje +
          dctlskdktzGridlb[i - 1].qckcje -
          dctlskdktzGridlb[i].dklyje -
          dctlskdktzGridlb[i].qtlyje,
        2,
      );
      dctlskdktzGridlb[i].qckcynse = ROUND(dctlskdktzGridlb[i].qckcje * dctlskdktzGridlb[i].sl1, 2);
    }
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcjehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcje;
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcynsehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcynse;
  } else {
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcjehj = dctlskdktzGridlb[0].qckcje;
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcynsehj = dctlskdktzGridlb[0].qckcynse;
  }
}

function dcxfs_dktz_kc(wtjgshje, dklyje, qtlyje) {
  const { dctlskdktzGridlb } = formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.dctlskdktzGrid;
  dctlskdktzGridlb[0].qckcje = ROUND(
    dctlskdktzGridlb[0].wtjgshje +
      formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qckcje -
      dctlskdktzGridlb[0].dklyje -
      dctlskdktzGridlb[0].qtlyje,
    2,
  );
  dctlskdktzGridlb[0].qckcynse = ROUND(dctlskdktzGridlb[0].qckcje * dctlskdktzGridlb[0].sl1, 2);
  if (dctlskdktzGridlb.length > 1) {
    for (let i = 1; i < dctlskdktzGridlb.length; i++) {
      dctlskdktzGridlb[i].qckcje = ROUND(
        dctlskdktzGridlb[i].wtjgshje +
          dctlskdktzGridlb[i - 1].qckcje -
          dctlskdktzGridlb[i].dklyje -
          dctlskdktzGridlb[i].qtlyje,
        2,
      );
      dctlskdktzGridlb[i].qckcynse = ROUND(dctlskdktzGridlb[i].qckcje * dctlskdktzGridlb[i].sl1, 2);
    }
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcjehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcje;
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcynsehj = dctlskdktzGridlb[dctlskdktzGridlb.length - 1].qckcynse;
  } else {
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcjehj = dctlskdktzGridlb[0].qckcje;
    formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb4.qmkcynsehj = dctlskdktzGridlb[0].qckcynse;
  }
}

function qtxfs_xfpmc_different(gridLb, zspmDm) {
  if (zspmDm === '' || zspmDm === null) {
    return true;
  }
  let count = 0;
  const xfpmcs = gridLb;
  for (let i = 0; i < xfpmcs.length; i++) {
    if (xfpmcs[i] === zspmDm) {
      count++;
      if (count === 2) {
        return false;
      }
    }
  }
  return true;
}

/**
 * 校验《本期减（免）税额明细表》中，[减（免）性质代码]相同时，[应税消费品名称]不能重复！
 * @param zspmDm    征收品目代码
 * @param ssjmxzDm    减免性质代码
 * @param gridLb    节点的父路径
 * @returns
 */
function jmsemxb_different(zspmDm, ssjmxzDm, gridLb) {
  if (zspmDm === '' || zspmDm === null) {
    return true;
  }
  const indexs = new Array();
  if (ssjmxzDm === '' || ssjmxzDm === null) {
    return true;
  }
  const nodeDms = gridLb;
  for (let i = 0; i < nodeDms.length; i++) {
    if (nodeDms[i].ssjmxzDm === ssjmxzDm) {
      indexs.push(i);
    }
  }
  if (zspmDm === '' || zspmDm === null) {
    return true;
  }
  let count = 0;
  for (let j = 0; j < indexs.length; j++) {
    if (nodeDms[indexs[j]].zspmDm === zspmDm) {
      count++;
      if (count === 2) {
        return false;
      }
    }
  }
  return true;
}

function jsbcpy_mxb_samebqjmse101020603(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020603') {
      // 柴油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020603') {
      // 柴油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}
function jsbcpy_mxb_samebqjmse101020604(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020604') {
      // 航空煤油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020604') {
      // 航空煤油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}
function jsbcpy_mxb_samebqjmse101020606(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020606') {
      // 溶剂油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020606') {
      // 溶剂油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}
function jsbcpy_mxb_samebqjmse101020607(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020607') {
      // 润滑油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020607') {
      // 润滑油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}
function jsbcpy_mxb_samebqjmse101020608(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020608') {
      // 燃料油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020608') {
      // 燃料油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}
function jsbcpy_mxb_samebqjmse101020605(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020605') {
      // 石脑油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020605') {
      // 石脑油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}

function jsbcpy_mxb_samebqjmse101020609(zspmDm, bqjmse, bqjmsejsbcpyGridlb, bqjmsl, bqjmsemxbGridlb) {
  if (zspmDm === null || zspmDm === '') {
    return true;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (zspmDm === bqjmsemxbGridlb[j].zspmDm && zspmDm === '101020609') {
      // 汽油
      bqjmse = ROUND(bqjmsemxbGridlb[j].bqjmse + bqjmse, 2);
    }
  }
  const k = 0;
  for (let i = 0; i < bqjmsejsbcpyGridlb.length; i++) {
    if (zspmDm === bqjmsejsbcpyGridlb[i].zspmDm && zspmDm === '101020609') {
      // 汽油
      if (bqjmse !== bqjmsejsbcpyGridlb[i].bqjmse) {
        return false;
      }
    }
  }
  return true;
}

/**
 * 校验：《本期减（免）税额明细表》中[XXX]的[减（免）税额]应小于等于主表相同品目对应的[应纳税额]
 * @param zspmDm    征收品目代码
 * @param gridLb    主表应纳税额节点的父路径
 * @returns
 */
function jmsemxb_compare_ynse(bqjmse, zspmDm, gridLb, xssl, xse) {
  if (zspmDm === '' || zspmDm === null) {
    return true;
  }
  const ynseDms = gridLb;
  for (let j = 0; j < ynseDms.length; j++) {
    if (ynseDms[j].zspmDm === zspmDm) {
      if (bqjmse > ynseDms[j].ynse) {
        return false;
      }
    }
  }
  return true;
}

/**
 * 校验：《本期减（免）税额明细表》中[XXX]的[减（免）税额]应小于等于主表相同品目对应的[应纳税额]
 * @param zspmDm    征收品目代码
 * @param gridLb    征收品目代码节点的父路径
 * @returns
 */
function jmsemxb_count_bqjmse(zspmDm, gridLb) {
  if (zspmDm === '' || zspmDm === null) {
    return 0;
  }
  const zspmDms = gridLb;
  let bqjmse = 0;
  for (let j = 0; j < zspmDms.length; j++) {
    if (zspmDms[j].zspmDm === zspmDm) {
      bqjmse += zspmDms[j].bqjmse;
    }
  }
  return bqjmse;
}

/**
 * 根据《本期减（免）税额明细表》中的[征收品目]找到主表相同品目对应的[应纳税额]
 * @param zspmDm    征收品目代码
 * @param ynse    主表中应纳税额
 * @param gridLb    主表应纳税额节点的父路径
 * @returns
 */
function jmsemxb_get_ynse(zspmDm, ynse, gridLb) {
  if (zspmDm === '' || zspmDm === null) {
    return null;
  }
  const ynseDms = gridLb;
  for (let j = 0; j < ynseDms.length; j++) {
    if (ynseDms[j].zspmDm === zspmDm) {
      return ynseDms[j].ynse;
    }
  }
  return null;
}

function dcjmsemxb_get_ynse(zspmDm, ynse, ynse1, gridLb) {
  if (zspmDm === '' || zspmDm === null) {
    return 0;
  }
  const ynseDms = gridLb;
  for (let j = 0; j < ynseDms.length; j++) {
    if (ynseDms[j].zspmDm === zspmDm) {
      return ynseDms[j].ynse;
    }
  }
  return 0;
}

function jmsemxb_get_bqjmse(zspmDm, bqjmse, bqjmsemxbGridlb) {
  if (zspmDm === '' || zspmDm === null) {
    return 0;
  }
  bqjmse = 0;
  for (let j = 0; j < bqjmsemxbGridlb.length; j++) {
    if (bqjmsemxbGridlb[j].zspmDm === zspmDm) {
      bqjmse = bqjmsemxbGridlb[j].bqjmse + bqjmse;
    }
  }
  return bqjmse;
}

/**
 * 小规模增值税减免税申报明细表，根据“减税性质代码及名称”获取起初报文中的期末余额
 * @param hmc    行代码
 * @param gridLb    起初报文中期末余额节点的路径
 * @returns
 */
function jmsesbmxb_get_qcs_qmye(hmc, swsxDm, gridLb) {
  if (hmc === '' || hmc === null) {
    return 0;
  }
  const qmyes = gridLb;
  if (qmyes !== '' && qmyes !== undefined && qmyes !== 'null' && qmyes !== 'undefined') {
    for (let j = 0; j < qmyes.length; j++) {
      if (qmyes[j].hmc === hmc && qmyes[j].swsxDm === swsxDm) {
        return qmyes[j].qmye;
      }
    }
  }
  return 0;
}
// function ssjmxzDmAndJmse(ssjmxzDm){
//  if(ssjmxzDm==null||ssjmxzDm==''){
// 	 return 0;
//  }
//  return formData.fdckfhdzssyyywbw.tdzzssbcsfdckfhdzssy.hdzssbbxxForm.jmse;
// }

function tdzzs_zspmdmzszmdm_different(zspmdmzszmdm, zszmMc) {
  const tdzzs_zspmdmzszmdm = formData.tdzzsnssbywbw.tdzzssbcsfdckfnsrsy.sbbxxfdckfGrid.sbbxxfdckfGridlb;
  const { sbxxGridlb } = formData.qcs.initData.tdzzssbInitData.qcsData.sbxxGrid;
  if (
    tdzzs_zspmdmzszmdm === null ||
    tdzzs_zspmdmzszmdm === undefined ||
    sbxxGridlb === null ||
    sbxxGridlb === undefined
  ) {
    return true;
  }
  let a = 0;
  for (let i = 0; i < sbxxGridlb.length; i++) {
    let count = 0;
    for (let j = 0; j < tdzzs_zspmdmzszmdm.length; j++) {
      if (sbxxGridlb[i].zszmMc === zszmMc && zszmMc !== '' && zszmMc !== null) {
        if (
          tdzzs_zspmdmzszmdm[j].zszmMc === sbxxGridlb[i].zszmMc &&
          tdzzs_zspmdmzszmdm[j].zspmDm === zspmdmzszmdm.substring(0, 9)
        ) {
          count++;
          if (count === 2) {
            return false;
          }
        }
      }
    }
  }
  for (let k = 0; k < tdzzs_zspmdmzszmdm.length; k++) {
    if (
      (tdzzs_zspmdmzszmdm[k].zszmMc === '' || tdzzs_zspmdmzszmdm[k].zszmMc === null) &&
      zspmdmzszmdm !== '' &&
      zspmdmzszmdm !== null &&
      (zszmMc === '' || zszmMc === null)
    ) {
      if (zspmdmzszmdm.substring(0, 9) === tdzzs_zspmdmzszmdm[k].zspmDm) {
        a++;
        if (a === 2) {
          return false;
        }
      }
    }
  }
  return true;
}

function tdzzs_zszmMc(zspmdmzszmdm, ewbhxh) {
  const { sbbxxfdckfGridlb } = formData.tdzzsnssbywbw.tdzzssbcsfdckfnsrsy.sbbxxfdckfGrid;
  const { sbbxxGridlb } = formData.qcs.formContent.tdzzssbb1.body.sbbxxForm;
  const { sbxxGridlb } = formData.qcs.initData.tdzzssbInitData.qcsData.sbxxGrid;
  if (
    sbbxxfdckfGridlb === null ||
    sbbxxfdckfGridlb === undefined ||
    sbbxxGridlb === null ||
    sbbxxGridlb === undefined ||
    sbxxGridlb === null ||
    sbxxGridlb === undefined
  ) {
    return '';
  }
  if (zspmdmzszmdm !== '' && zspmdmzszmdm !== null) {
    for (let i = 0; i < sbbxxGridlb.length; i++) {
      if (
        parseInt(ewbhxh) === 1 &&
        parseInt(sbbxxfdckfGridlb[sbbxxfdckfGridlb.length - 1].ewbhxh) === sbbxxfdckfGridlb.length - 1
      ) {
        if (
          zspmdmzszmdm === sbbxxGridlb[i].zspmdmzszmdm &&
          sbbxxfdckfGridlb[ewbhxh - 1].zszmMc !== sbbxxGridlb[i].zszmMc &&
          sbbxxGridlb[i].zszmMc !== '' &&
          sbbxxGridlb[i].zszmMc !== null &&
          sbbxxfdckfGridlb[ewbhxh - 1].zspmDm === sbbxxGridlb[i].zspmDm
        ) {
          return sbbxxfdckfGridlb[ewbhxh - 1].zszmMc;
        }
      }
      if (
        zspmdmzszmdm === sbbxxGridlb[i].zspmdmzszmdm &&
        sbbxxGridlb[i].zszmMc !== '' &&
        sbbxxGridlb[i].zszmMc !== null
      ) {
        return sbbxxGridlb[i].zszmMc;
      }
    }
    if (
      (zspmdmzszmdm === '101130501' || zspmdmzszmdm === '101130502' || zspmdmzszmdm === '101130503') &&
      typeof ewbhxh === 'number'
    ) {
      for (let j = 0; j < sbxxGridlb.length; j++) {
        if (zspmdmzszmdm === sbxxGridlb[j].zspmDm && sbbxxfdckfGridlb[ewbhxh - 1].zszmMc === sbxxGridlb[j].zszmMc) {
          return sbbxxfdckfGridlb[ewbhxh - 1].zszmMc;
        }
      }
      return '';
    }
  } else {
    return '';
  }
}

function tdzzs_zszm(zspmDm, zszmMc) {
  const { sbxxGridlb } = formData.qcs.initData.tdzzssbInitData.qcsData.sbxxGrid;
  if (sbxxGridlb === null || sbxxGridlb === undefined) {
    return '';
  }
  if (zszmMc !== '' && zszmMc !== null && zspmDm !== '' && zspmDm !== null) {
    for (let i = 0; i < sbxxGridlb.length; i++) {
      if (zszmMc === sbxxGridlb[i].zszmMc && zspmDm === sbxxGridlb[i].zspmDm) {
        return sbxxGridlb[i].zszmDm;
      }
    }
  }
  return '';
}
function tdzzs_zspm(zspmdmzszmdm) {
  const { sbxxGridlb } = formData.qcs.initData.tdzzssbInitData.qcsData.sbxxGrid;
  if (sbxxGridlb === null || sbxxGridlb === undefined) {
    return '';
  }
  if (zspmdmzszmdm !== '' && zspmdmzszmdm !== null) {
    for (let i = 0; i < sbxxGridlb.length; i++) {
      if (zspmdmzszmdm === sbxxGridlb[i].zspmdmzszmdm) {
        return sbxxGridlb[i].zspmDm;
      }
    }
  }
  return zspmdmzszmdm;
}
function tdzzs_yzl(zspmDm, zszmMc) {
  const { sbxxGridlb } = formData.qcs.initData.tdzzssbInitData.qcsData.sbxxGrid;
  const { sbbxxGridlb } = formData.qcs.formContent.tdzzssbb1.body.sbbxxForm;
  if (sbxxGridlb === null || sbxxGridlb === undefined || sbbxxGridlb === null || sbbxxGridlb === undefined) {
    return 0;
  }
  if (zszmMc !== '' && zszmMc !== null) {
    for (let i = 0; i < sbxxGridlb.length; i++) {
      if (zszmMc === sbxxGridlb[i].zszmMc) {
        return sbxxGridlb[i].sl1;
      }
    }
  } else {
    for (let j = 0; j < sbbxxGridlb.length; j++) {
      if (zspmDm === sbbxxGridlb[j].zspmDm && zspmDm === sbbxxGridlb[j].zspmdmzszmdm) {
        return parseFloat(sbbxxGridlb[j].yzl);
      }
    }
  }
  return 0;
}

function tdzzs_lock(zspmDm, zszmMc) {
  const { sbbxxGridlb } = formData.qcs.formContent.tdzzssbb1.body.sbbxxForm;
  let count = 0;
  if (sbbxxGridlb !== null && sbbxxGridlb !== undefined) {
    for (let i = 0; i < sbbxxGridlb.length; i++) {
      if (zspmDm === sbbxxGridlb[i].zspmDm) {
        count += 1;
      }
    }
    if (zszmMc !== '' && zszmMc !== null && zspmDm !== '' && zspmDm !== null) {
      for (let i = 0; i < sbbxxGridlb.length; i++) {
        if (zszmMc === sbbxxGridlb[i].zszmMc && zspmDm === sbbxxGridlb[i].zspmDm && count === 1) {
          return true;
        }
      }
    }
  }

  return false;
}

function calculateNsqxDm(skssqq, skssqz) {
  if (skssqq === '' || skssqz === '') {
    return '06'; // 默认为按月
  }
  const monthq = skssqq.substring(5, 7);
  const monthz = skssqz.substring(5, 7);
  const dayq = skssqq.substring(8);
  const dayz = skssqz.substring(8);
  if (Number(monthz) - Number(monthq) === 2) {
    return '08'; // 按季
  }
  if (Number(monthz) - Number(monthq) === 0) {
    if (Number(dayz) === Number(dayq)) {
      return '11'; // 按天
    }
    return '06';
  }
}

function changeJmsxDm(xh, jmsxDm) {
  if (jmsxDm === '' || jmsxDm === null) {
    const arr = formData.ywbw.grsdsjmssxbgbBzds.body.grsdsjmssxbgbHz.jmssxHzlb.jmssxHz;
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].bh === xh) {
        arr[i].uuid = '';
        arr[i].tzrdjxh = '';
        arr[i].btzdwdjxh = '';
        arr[i].yhjmly = '';
        arr[i].zspmDm = '';
        arr[i].jmzlxDm = '';
        arr[i].jmfsDm = '';
        arr[i].jmlxDm = '';
        arr[i].jmqxq = '';
        arr[i].jmqxz = '';
        arr[i].jzfd = 0;
        arr[i].jzsl = 0;
        arr[i].jzed = 0;
        arr[i].jmxzmc = 0;
        arr[i].jmxzdlDm = 0;
        arr[i].jmxzDm = 0;
        arr[i].jmxzxlDm = 0;
      }
    }
  }
  return '';
}

/**
 * 两种情况不需填写本选项：一是项目实施状态为“未完成”的；二是项目实施状态虽为“已完成”，但同一项目名称本年有两条明细的；
 * @param xmmc    项目名称
 * @param xmssztxx    项目实施状态选项
 * @param yfcg    研发成果
 * @param gridLb    节点的父路径
 * @returns
 */
function yfzcfzzhzbGridlb_yfcg(xmmc, xmssztxx, yfcg, gridLb) {
  if (yfcg === null || yfcg === '') {
    return true;
  }
  if (xmssztxx === null || xmssztxx === '') {
    return true;
  }

  if (xmssztxx === '01' && (yfcg !== null || yfcg !== '')) {
    return false;
  }

  if (xmmc === '' || xmmc === null) {
    return true;
  }
  if (xmssztxx === '02') {
    const indexs = new Array();
    const nodeDms = gridLb;
    for (let i = 6; i < nodeDms.length; i++) {
      if (nodeDms[i].xmmc === xmmc) {
        indexs.push(i);
      }
    }
    let count = 0;
    for (let j = 0; j < indexs.length; j++) {
      if (nodeDms[indexs[j]].xmmc === xmmc) {
        count++;
        if (count === 2) {
          return false;
        }
      }
    }
  }

  return true;
}

/**
 * 两种情况不需填写本选项：一是项目实施状态为“未完成”的；二是项目实施状态虽为“已完成”，但同一项目名称本年有两条明细的；
 * @param xmmc    项目名称
 * @param xmssztxx    项目实施状态选项
 * @param yfcgzsh    研发成果证书号
 * @param gridLb    节点的父路径
 * @returns
 */
function yfzcfzzhzbGridlb_yfcgzsh(xmmc, xmssztxx, yfcgzsh, gridLb) {
  if (yfcgzsh === null || yfcgzsh === '') {
    return true;
  }
  if (xmssztxx === null || xmssztxx === '') {
    return true;
  }

  if (xmssztxx === '01' && (yfcgzsh !== null || yfcgzsh !== '')) {
    return false;
  }

  if (xmmc === '' || xmmc === null) {
    return true;
  }
  if (xmssztxx === '02') {
    const indexs = new Array();
    const nodeDms = gridLb;
    for (let i = 6; i < nodeDms.length; i++) {
      if (nodeDms[i].xmmc === xmmc) {
        indexs.push(i);
      }
    }
    let count = 0;
    for (let j = 0; j < indexs.length; j++) {
      if (nodeDms[indexs[j]].xmmc === xmmc) {
        count++;
        if (count === 2) {
          return false;
        }
      }
    }
  }

  return true;
}

/**
 * 契税    评估价格大于等于成交价格时，计税价格强制等于评估价格，不可修改。
 * @param hdjsjg    评估价格
 * @param cjjg      成交价格
 * @returns
 */
function qs_jsjg(hdjsjg, cjjg) {
  const jsjg = formData.sb030QssbVO.zzjsjg;
  if (hdjsjg > 0 && cjjg >= 0) {
    if (hdjsjg >= cjjg) {
      return hdjsjg;
    }
  }
  return jsjg;
}

/**
 * 财务报表   附表研发支出的[其中：委托境外进行研发活动所发生的费用（包括存在关联关系的委托研发）]=
 * (1)当“委托方与受托方是否存在关联关系选项”为不存在，且“是否委托境外选项”为委托境外时，序号7.1等于序号7；
 * (2)当“委托方与受托方是否存在关联关系选项”为存在，且“是否委托境外选项”为委托境外时，序号7.1等于序号一至六的合计；
 * (因为前6行是固定行，不需要计算，第7行开始是动态行，需要计算，所以才使用自定义公式)
 */
function cwbbyfzc_f043() {
  const { yfzcfzzhzbGridlb } = formData.SB100VO.SB100BdxxVO.cjbdxml.yfzcfzzhzbVO.yfzcfzzhzbGrid;
  if (yfzcfzzhzbGridlb !== null && yfzcfzzhzbGridlb.length > 6) {
    for (i = 6; i < yfzcfzzhzbGridlb.length; i++) {
      const yfzcfzzhzbGridVo = yfzcfzzhzbGridlb[i];
      if (yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '01' && yfzcfzzhzbGridVo.sfwtjwxx === '02') {
        yfzcfzzhzbGridVo.f043 = ROUND(
          yfzcfzzhzbGridVo.f001 +
            yfzcfzzhzbGridVo.f002 +
            yfzcfzzhzbGridVo.f003 +
            yfzcfzzhzbGridVo.f004 +
            yfzcfzzhzbGridVo.f005 +
            yfzcfzzhzbGridVo.f006 +
            yfzcfzzhzbGridVo.f007 +
            yfzcfzzhzbGridVo.f008 +
            yfzcfzzhzbGridVo.f009 +
            yfzcfzzhzbGridVo.f010 +
            yfzcfzzhzbGridVo.f011 +
            yfzcfzzhzbGridVo.f012 +
            yfzcfzzhzbGridVo.f013 +
            yfzcfzzhzbGridVo.f014 +
            yfzcfzzhzbGridVo.f015 +
            yfzcfzzhzbGridVo.f016 +
            yfzcfzzhzbGridVo.f017 +
            yfzcfzzhzbGridVo.f018 +
            yfzcfzzhzbGridVo.f019 +
            yfzcfzzhzbGridVo.f020 +
            yfzcfzzhzbGridVo.f041,
          2,
        );
      } else if (yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '02' && yfzcfzzhzbGridVo.sfwtjwxx === '02') {
        yfzcfzzhzbGridVo.f043 = ROUND(yfzcfzzhzbGridVo.f042, 2);
      } else if (yfzcfzzhzbGridVo.sfwtjwxx === '01') {
        if (
          yfzcfzzhzbGridVo.tempf043 !== undefined &&
          yfzcfzzhzbGridVo.tempf043 !== null &&
          yfzcfzzhzbGridVo.tempf043 !== ''
        ) {
          yfzcfzzhzbGridVo.f043 = yfzcfzzhzbGridVo.tempf043;
        } else {
          yfzcfzzhzbGridVo.f043 = 0.0;
        }
      } else {
        yfzcfzzhzbGridVo.f043 = 0.0;
      }
    }
  }
}

/**
 * 财务报表   附表研发支出的[九、当期费用化支出可加计扣除总额]
 * （1）当项目明细中“资本化、费用化支出选项”为费用化且“研发形式”为委托研发、“委托方与受托方是否存在关联关系选项”为存在时=
 *     [序号8+最小值（其他相关费用序号6合计,序号8.1）-序号7.1]×80%，其中，“最小值”是指其他相关费用序号6合计与序号8.1相比的孰小值（下同）；
 * （2）当项目明细中“资本化、费用化支出选项”为费用化且“研发形式”为委托研发、“委托方与受托方是否存在关联关系选项”为不存在时=（序号7-序号7.1）×80%；
 * （3）当项目明细中“资本化、费用化支出选项”为费用化且“研发形式”为自主研发、合作研发和集中研发时=序号8+最小值（其他相关费用序号6合计,序号8.1）；
 * （4）其他情形不需填写本栏次；
 * (因为前6行是固定行，不需要计算，第7行开始是动态行，需要计算，所以才使用自定义公式)
 */
function cwbbyfzc_f046() {
  const { yfzcfzzhzbGridlb } = formData.SB100VO.SB100BdxxVO.cjbdxml.yfzcfzzhzbVO.yfzcfzzhzbGrid;
  if (yfzcfzzhzbGridlb !== null && yfzcfzzhzbGridlb.length > 6) {
    for (i = 6; i < yfzcfzzhzbGridlb.length; i++) {
      const yfzcfzzhzbGridVo = yfzcfzzhzbGridlb[i];
      if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '02' &&
        yfzcfzzhzbGridVo.yfxs === '2' &&
        yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '01'
      ) {
        yfzcfzzhzbGridVo.f046 = ROUND(
          (yfzcfzzhzbGridVo.f044 + MIN(yfzcfzzhzbGridVo.f041, yfzcfzzhzbGridVo.f045) - yfzcfzzhzbGridVo.f043) * 0.8,
          2,
        );
      } else if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '02' &&
        yfzcfzzhzbGridVo.yfxs === '2' &&
        yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '02'
      ) {
        yfzcfzzhzbGridVo.f046 = ROUND((yfzcfzzhzbGridVo.f042 - yfzcfzzhzbGridVo.f043) * 0.8, 2);
      } else if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '02' &&
        (yfzcfzzhzbGridVo.yfxs === '1' || yfzcfzzhzbGridVo.yfxs === '3' || yfzcfzzhzbGridVo.yfxs === '4')
      ) {
        yfzcfzzhzbGridVo.f046 = ROUND(yfzcfzzhzbGridVo.f044 + MIN(yfzcfzzhzbGridVo.f041, yfzcfzzhzbGridVo.f045), 2);
      } else {
        yfzcfzzhzbGridVo.f046 = 0.0;
      }
    }
  }
}

/**
 * 财务报表   附表研发支出的[当期资本化可加计扣除的研发费用率]
 * （1）当项目明细中“资本化、费用化支出选项”为资本化且“项目实施状态选项”为已完成、“研发形式”为委托研发、“委托方与受托方是否存在关联关系选项”为存在时=
 *    {[序号8+最小值（其他相关费用序号6合计,序号8.1)-序号7.1]×80%}/序号一至六的合计×100%；
 * （2）当项目明细中“资本化、费用化支出选项”为资本化且“项目实施状态选项”为已完成、“研发形式”为委托研发、“委托方与受托方是否存在关联关系选项”为不存在时=
 *     [（序号7-序号7.1）×80%]/序号7×100%；
 * （3）当项目明细中“资本化、费用化支出选项”为资本化且“项目实施状态选项”为已完成、“研发形式”为自主研发、合作研发和集中研发时=
 *     [序号8+最小值（其他相关费用序号6合计,序号8.1）]/序号一至六的合计×100%；
 * （4）其他情形不需填写本栏次；
 * （5）“当期资本化可加计扣除的研发费用率”应当记录在对应的无形资产明细账上，以便用于计算归集《研发项目可加计扣除研究开发费用情况归集表》序号10.1“其中：准予加计扣除的摊销额”；
 * (因为前6行是固定行，不需要计算，第7行开始是动态行，需要计算，所以才使用自定义公式)
 */
function cwbbyfzc_f047() {
  const { yfzcfzzhzbGridlb } = formData.SB100VO.SB100BdxxVO.cjbdxml.yfzcfzzhzbVO.yfzcfzzhzbGrid;
  if (yfzcfzzhzbGridlb !== null && yfzcfzzhzbGridlb.length > 6) {
    const yfzcfzzhzbGridVo5 = yfzcfzzhzbGridlb[5]; // 第6行的vo
    for (i = 6; i < yfzcfzzhzbGridlb.length; i++) {
      const yfzcfzzhzbGridVo = yfzcfzzhzbGridlb[i];
      if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '01' &&
        yfzcfzzhzbGridVo.xmssztxx === '02' &&
        yfzcfzzhzbGridVo.yfxs === '2' &&
        yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '01' &&
        yfzcfzzhzbGridVo.f001 +
          yfzcfzzhzbGridVo.f002 +
          yfzcfzzhzbGridVo.f003 +
          yfzcfzzhzbGridVo.f004 +
          yfzcfzzhzbGridVo.f005 +
          yfzcfzzhzbGridVo.f006 +
          yfzcfzzhzbGridVo.f007 +
          yfzcfzzhzbGridVo.f008 +
          yfzcfzzhzbGridVo.f009 +
          yfzcfzzhzbGridVo.f010 +
          yfzcfzzhzbGridVo.f011 +
          yfzcfzzhzbGridVo.f012 +
          yfzcfzzhzbGridVo.f013 +
          yfzcfzzhzbGridVo.f014 +
          yfzcfzzhzbGridVo.f015 +
          yfzcfzzhzbGridVo.f016 +
          yfzcfzzhzbGridVo.f017 +
          yfzcfzzhzbGridVo.f018 +
          yfzcfzzhzbGridVo.f019 +
          yfzcfzzhzbGridVo.f020 +
          yfzcfzzhzbGridVo.f041 !=
          0
      ) {
        yfzcfzzhzbGridVo.f047 = ROUND(
          ((yfzcfzzhzbGridVo.f044 + MIN(yfzcfzzhzbGridVo.f041, yfzcfzzhzbGridVo.f045) - yfzcfzzhzbGridVo.f043) * 0.8) /
            (yfzcfzzhzbGridVo.f001 +
              yfzcfzzhzbGridVo.f002 +
              yfzcfzzhzbGridVo.f003 +
              yfzcfzzhzbGridVo.f004 +
              yfzcfzzhzbGridVo.f005 +
              yfzcfzzhzbGridVo.f006 +
              yfzcfzzhzbGridVo.f007 +
              yfzcfzzhzbGridVo.f008 +
              yfzcfzzhzbGridVo.f009 +
              yfzcfzzhzbGridVo.f010 +
              yfzcfzzhzbGridVo.f011 +
              yfzcfzzhzbGridVo.f012 +
              yfzcfzzhzbGridVo.f013 +
              yfzcfzzhzbGridVo.f014 +
              yfzcfzzhzbGridVo.f015 +
              yfzcfzzhzbGridVo.f016 +
              yfzcfzzhzbGridVo.f017 +
              yfzcfzzhzbGridVo.f018 +
              yfzcfzzhzbGridVo.f019 +
              yfzcfzzhzbGridVo.f020 +
              yfzcfzzhzbGridVo.f041),
          2,
        );
      } else if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '01' &&
        yfzcfzzhzbGridVo.xmssztxx === '02' &&
        yfzcfzzhzbGridVo.yfxs === '2' &&
        yfzcfzzhzbGridVo.wtfystfsfczglgxxx === '02' &&
        yfzcfzzhzbGridVo.f042 !== 0
      ) {
        yfzcfzzhzbGridVo.f047 = ROUND(
          ((yfzcfzzhzbGridVo.f042 - yfzcfzzhzbGridVo.f043) * 0.8) / yfzcfzzhzbGridVo.f042,
          2,
        );
      } else if (
        yfzcfzzhzbGridVo.zbhfyhzcxx === '01' &&
        yfzcfzzhzbGridVo.xmssztxx === '02' &&
        (yfzcfzzhzbGridVo.yfxs === '1' || yfzcfzzhzbGridVo.yfxs === '3' || yfzcfzzhzbGridVo.yfxs === '4') &&
        yfzcfzzhzbGridVo.f001 +
          yfzcfzzhzbGridVo.f002 +
          yfzcfzzhzbGridVo.f003 +
          yfzcfzzhzbGridVo.f004 +
          yfzcfzzhzbGridVo.f005 +
          yfzcfzzhzbGridVo.f006 +
          yfzcfzzhzbGridVo.f007 +
          yfzcfzzhzbGridVo.f008 +
          yfzcfzzhzbGridVo.f009 +
          yfzcfzzhzbGridVo.f010 +
          yfzcfzzhzbGridVo.f011 +
          yfzcfzzhzbGridVo.f012 +
          yfzcfzzhzbGridVo.f013 +
          yfzcfzzhzbGridVo.f014 +
          yfzcfzzhzbGridVo.f015 +
          yfzcfzzhzbGridVo.f016 +
          yfzcfzzhzbGridVo5.f017 +
          yfzcfzzhzbGridVo5.f018 +
          yfzcfzzhzbGridVo5.f019 +
          yfzcfzzhzbGridVo.f020 +
          yfzcfzzhzbGridVo5.f041 !=
          0
      ) {
        yfzcfzzhzbGridVo.f047 = ROUND(
          (yfzcfzzhzbGridVo.f044 + MIN(yfzcfzzhzbGridVo.f041, yfzcfzzhzbGridVo.f045)) /
            (yfzcfzzhzbGridVo.f001 +
              yfzcfzzhzbGridVo.f002 +
              yfzcfzzhzbGridVo.f003 +
              yfzcfzzhzbGridVo.f004 +
              yfzcfzzhzbGridVo.f005 +
              yfzcfzzhzbGridVo.f006 +
              yfzcfzzhzbGridVo.f007 +
              yfzcfzzhzbGridVo.f008 +
              yfzcfzzhzbGridVo.f009 +
              yfzcfzzhzbGridVo.f010 +
              yfzcfzzhzbGridVo.f011 +
              yfzcfzzhzbGridVo.f012 +
              yfzcfzzhzbGridVo.f013 +
              yfzcfzzhzbGridVo.f014 +
              yfzcfzzhzbGridVo.f015 +
              yfzcfzzhzbGridVo.f016 +
              yfzcfzzhzbGridVo.f017 +
              yfzcfzzhzbGridVo.f018 +
              yfzcfzzhzbGridVo.f019 +
              yfzcfzzhzbGridVo.f020 +
              yfzcfzzhzbGridVo.f041),
          2,
        );
      } else {
        yfzcfzzhzbGridVo.f047 = 0.0;
      }
    }
  }
}

/**
 * 增值税预缴申报 预征项目在期初数不包含，该行锁定，不可修改。
 * @param yzxm    预征项目
 * @param yzxmGridlb      期初数预征项目
 * @returns
 */
function zzsyjsb_yzxm(yzxm, yzxmGridlb) {
  if (yzxmGridlb === null || yzxmGridlb === undefined) {
    return true;
  }
  for (let i = 0; i < yzxmGridlb.length; i++) {
    if (yzxm === yzxmGridlb[i].yzxmDm) {
      return false;
    }
  }
  return true;
}

/** 个人所得税设置任职、受雇单位、任职受雇单位 所属行业、行业代码为空 */
function setRzdwValueNull(flag) {
  if (flag) {
    formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.rzdwMc = '';
    formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.rzdwSshyMc = '';
    formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.rzdwSshyDm = '';
  }
}

/** 个人所得税经营单位名称为'' */
function setJydwValueNull(flag) {
  if (flag) {
    formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.jydwNsrmc = '';
  }
}

function indexOf(s, r) {
  r = r.replace('==', '=');
  return s.indexOf(r);
}
/** 本表中销项税额必须等于销售额乘以对应税率（允许存在有一块钱误差） （本表第2列中的第1栏至第5栏、第8栏至第13c栏有此校验）
 * 全国（除陕西外）增加校验,现陕西允许有正负10元钱的差额 */
function checkXxse(xxse, xse, ewbhxh, yzl13a, gzZzsTlzgsYzl, kshztlzcdnsrYzl, sjgsdq) {
  let flag = true;
  let ce = 1;
  const sls = {
    1: '0.16',
    2: '0.16',
    3: '0.13',
    4: '0.10',
    5: '0.06',
    8: '0.06',
    9: '0.05',
    10: '0.04',
    11: '0.03',
    12: '0.03',
    13: `${yzl13a}`,
    20: `${gzZzsTlzgsYzl}`,
    21: `${kshztlzcdnsrYzl}`,
    22: '0.05',
    23: '0.11',
  };
  if (sjgsdq.substring(0, 3) === '161') {
    ce = 10;
  }
  const tempXxse = ROUND(parseFloat(sls[ewbhxh]) * xse, 2);
  if (Math.abs(xxse - tempXxse) > ce) {
    flag = false;
    return flag;
  }
  return flag;
}

/** 个人所得税年12万设置申报日期,因为有暂存报文的时候，申报日期还是用了暂存报文，所以需要使用12公式触发 */
function setN12wSbrq() {
  const currDate = new Date();
  const year = currDate.getFullYear();
  const month = currDate.getMonth() + 1;
  const day = currDate.getDate();
  let monthStr = `${month}`;
  let dayStr = `${day}`;
  if (month < 9) {
    monthStr = `0${month}`;
  }
  if (day < 9) {
    dayStr = `0${day}`;
  }
  const tbrq = `${year}-${monthStr}-${dayStr}`;
  formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.tbrq1 = tbrq;
  formData.nsdsewysnsrgrsdssbYwbw.nsdsewysnsrgrsdssb.head.lrrq = tbrq;
  formData.grsdsjmssxbgbYwbw.grsdsjmssxbgbBzds.head.tbrq1 = tbrq;
  formData.grsdsjmssxbgbYwbw.grsdsjmssxbgbBzds.head.sbrq = tbrq;
  formData.grsdsjmssxbgbYwbw.grsdsjmssxbgbBzds.head.slrq = tbrq;
  formData.grsdsjmssxbgbYwbw.grsdsjmssxbgbBzds.head.lrrq = tbrq;
  // 因为是用12公式触发，所以返回true
  return true;
}

/** 水资源税税源信息采集根据选择的水源类型设置年取用水计划（地下水）或者年取用水计划（地表水）为0 */
function szyssycj_setDxdbs() {
  const sylxjhDm = formData.DJSzyssyxxVO.sylxjh_dm;
  if (sylxjhDm === '1') {
    // 选择了地表水，地下水清0
    formData.DJSzyssyxxVO.nqysjhdxs = 0;
  } else if (sylxjhDm === '2') {
    // 选择了地下水，地表水清0
    formData.DJSzyssyxxVO.nqysjhdbs = 0;
  } else {
    // 都清0
    formData.DJSzyssyxxVO.nqysjhdxs = 0;
    formData.DJSzyssyxxVO.nqysjhdbs = 0;
  }
}

// 水资源申报校验zspm
function checkZspm(zspm, zjbl) {
  const zspmList = formData.szyvo.szysSbb.skxxGrid.skxxGridlb;
  if (zspm === '' || zspm === null) {
    return true;
  }
  for (let i = 0; i < zspmList.length; i++) {
    if (zspm === zspmList[i].zspmDm) {
      return true;
    }
  }
  return false;
}
// 水资源申报校验zszm
function checkZszm(zszm, zjbl) {
  const zszmList = formData.szyvo.szysSbb.skxxGrid.skxxGridlb;
  if (zszm === '' || zszm === null) {
    return true;
  }
  for (let i = 0; i < zszmList.length; i++) {
    if (zszm === zszmList[i].zszmDm) {
      return true;
    }
  }
  return false;
}

// 水资源A计算ynse
function getSzyYnse(sqljqsl, bqqsl, bqljqsl, sl1, nqysjhdbs, nqysjhdxs) {
  const { skxxGridlb } = formData.szyvo.szysSbb.skxxGrid;
  const { csz } = formData.qcs.initData.cspzGrid;
  const csz1 = parseFloat(formData.qcs.initData.cspzGrid2.csz);
  const bs = csz.split(';')[0].split(','); // 倍数
  const qj = csz.split(';')[1].split(','); // 区间
  const len = qj.length; // 区间的长度
  let ljindex = 0; // 累计取水量所在的区间
  let sqindex = 0; // 上期取水量所在的区间
  let ynse = 0;
  if (formData.szyvo.szysSbb.syxxForm.qsxkzt === 'Y') {
    if (nqysjhdbs !== 0) {
      for (let i = 0; i < len; i++) {
        if (i === 0 && bqljqsl <= nqysjhdbs) {
          ljindex = i;
        } else if (i + 1 < len) {
          const x1 = parseFloat(qj[i]);
          const x2 = parseFloat(qj[i + 1]);
          const zjz1 = nqysjhdbs * (1 + x1);
          const zjz2 = nqysjhdbs * (1 + x2);
          if (bqljqsl > zjz1 && bqljqsl <= zjz2) {
            ljindex = i + 1;
            break;
          }
        } else {
          const x1 = parseFloat(qj[i]);
          const zjz2 = nqysjhdbs * (1 + x1);
          if (bqljqsl > zjz2) {
            ljindex = i + 1;
          }
        }
      }
      for (let j = 0; j < ljindex; j++) {
        if (j === 0 && sqljqsl <= nqysjhdbs) {
          sqindex = j;
        } else if (j + 1 < ljindex) {
          const x1 = parseFloat(qj[j]);
          const x2 = parseFloat(qj[j + 1]);
          const zjz1 = nqysjhdbs * (1 + x1);
          const zjz2 = nqysjhdbs * (1 + x2);
          if (sqljqsl > zjz1 && sqljqsl <= zjz2) {
            sqindex = j + 1;
            break;
          }
        } else {
          const x1 = parseFloat(qj[j]);
          const zjz2 = nqysjhdbs * (1 + x1);
          if (sqljqsl > zjz2) {
            sqindex = j + 1;
          }
        }
      }

      for (let x = sqindex; x <= ljindex; x++) {
        if (sqindex === ljindex) {
          ynse = bqqsl * sl1 * bs[x];
        } else if (x === sqindex) {
          ynse += (nqysjhdbs * (1 + parseFloat(qj[x])) - sqljqsl) * sl1 * parseFloat(bs[x]);
        } else if (x < ljindex) {
          ynse += (parseFloat(qj[x]) - parseFloat(qj[x - 1])) * nqysjhdbs * sl1 * parseFloat(bs[x]);
        } else {
          ynse += (bqljqsl - nqysjhdbs * (1 + parseFloat(qj[x - 1]))) * sl1 * parseFloat(bs[x]);
        }
      }
    } else {
      for (let i = 0; i < len; i++) {
        if (i === 0 && bqljqsl <= nqysjhdxs) {
          ljindex = i;
        } else if (i + 1 < len) {
          const x1 = parseFloat(qj[i]);
          const x2 = parseFloat(qj[i + 1]);
          const zjz1 = nqysjhdxs * (1 + x1);
          const zjz2 = nqysjhdxs * (1 + x2);
          if (bqljqsl > zjz1 && bqljqsl <= zjz2) {
            ljindex = i + 1;
            break;
          }
        } else {
          const x1 = parseFloat(qj[i]);
          const zjz2 = nqysjhdxs * (1 + x1);
          if (bqljqsl > zjz2) {
            ljindex = i + 1;
          }
        }
      }
      for (let j = 0; j < ljindex; j++) {
        if (j === 0 && sqljqsl <= nqysjhdxs) {
          sqindex = j;
        } else if (j + 1 < ljindex) {
          const x1 = parseFloat(qj[j]);
          const x2 = parseFloat(qj[j + 1]);
          const zjz1 = nqysjhdxs * (1 + x1);
          const zjz2 = nqysjhdxs * (1 + x2);
          if (sqljqsl > zjz1 && sqljqsl <= zjz2) {
            sqindex = j + 1;
            break;
          }
        } else {
          const x1 = parseFloat(qj[j]);
          const zjz2 = nqysjhdxs * (1 + x1);
          if (sqljqsl > zjz2) {
            sqindex = j + 1;
          }
        }
      }

      for (let x = sqindex; x <= ljindex; x++) {
        if (sqindex === ljindex) {
          ynse = bqqsl * sl1 * bs[x];
          // ynse = (nqysjhdxs*(1+parseFloat(qj[x]))-sqljqsl)*sl1*parseFloat(bs[x]) + (bqljqsl-nqysjhdxs*(1+parseFloat(qj[x])))*sl1*parseFloat(bs[x]);
        } else if (x === sqindex) {
          ynse += (nqysjhdxs * (1 + parseFloat(qj[x])) - sqljqsl) * sl1 * parseFloat(bs[x]);
        } else if (x < ljindex) {
          ynse += (parseFloat(qj[x]) - parseFloat(qj[x - 1])) * nqysjhdxs * sl1 * parseFloat(bs[x]);
        } else {
          ynse += (bqljqsl - nqysjhdxs * (1 + parseFloat(qj[x - 1]))) * sl1 * parseFloat(bs[x]);
        }
      }
    }
  } else {
    ynse = bqqsl * sl1 * csz1;
  }

  formData.szyvo.szysSbb.skxxGrid.skxxGridlb[0].bqynse = ynse.toFixed(2);
  formulaEngine.apply('szyvo.szysSbb.skxxGrid.skxxGridlb[0].bqynse', ynse.toFixed(2));
}

// 校验税款所属期是否为按次、按月、按季申报
function checkSkssq(skssqq, skssqz) {
  const qq_year = skssqq.split('-')[0];
  const qq_month = skssqq.split('-')[1];
  const qq_day = skssqq.split('-')[2];

  const qz_year = skssqz.split('-')[0];
  const qz_month = skssqz.split('-')[1];
  const qz_day = skssqz.split('-')[2];
  const end_day = new Date(qz_year, qz_month, 0).getDate(); // 当月的天数

  const t1 = qq_month === '01' && qq_day === '01' && qz_month === '03' && qz_day === end_day;
  const t2 = qq_month === '04' && qq_day === '01' && qz_month === '06' && qz_day === end_day;
  const t3 = qq_month === '07' && qq_day === '01' && qz_month === '09' && qz_day === end_day;
  const t4 = qq_month === '10' && qq_day === '01' && qz_month === '12' && qz_day === end_day;

  if (qq_year !== qz_year) {
    return false;
  }
  if (qq_month === qz_month && qq_day === qz_day) {
    // 按次
    return true;
  }
  if (qq_month === qz_month && qq_day === '01' && qz_day === end_day) {
    // 按月
    return true;
  }
  if (t1 || t2 || t3 || t4) {
    // 按季
    return true;
  }

  return false;
}

/**
 * 水资源 url没传属期时，把报文中属期传入queryString提供给回执页展示
 * @param skssqq
 * @param skssqz
 */
function setSkssq(skssqq, skssqz) {
  $('#sssqQ').val(skssqq);
  $('#sssqZ').val(skssqz);
  const _query_string_Obj = JSON.parse(`{${$('#_query_string_').val()}}`);
  _query_string_Obj.sssqQ = skssqq;
  _query_string_Obj.sssqZ = skssqz;
  const _query_string_ = JSON.stringify(_query_string_Obj).replace('{', '').replace('}', '');
  $('#_query_string_').val(_query_string_);
  return true;
}

/**
 * 水资源 逾期校验
 */
function sbbYqsbValidSzyssb(gdslxDm, sbywbm, zsxmDm, zspmDm, ssqq, ssqz, nsqxDm, sbqxDm) {
  // 仅正常申报使用，更正申报的逾期暂不处理
  if (parent.location.href.indexOf('gzsb=zx') === -1) {
    let params = '';
    const gdslxDm = arguments[0];
    const sbywbm = arguments[1];
    const zsxmDm = arguments[2];
    const zspmDm = arguments[3];
    const ssqq = arguments[4];
    const ssqz = arguments[5];
    const nsqxDm = arguments[6];
    const sbqxDm = arguments[7];
    const djxh = $('#djxh').val();
    const nsrsbh = $('#nsrsbh').val();
    const test = $('#test').val();

    params += `gdslxDm=${gdslxDm}&sbywbm=${sbywbm}&zsxmDm=${zsxmDm}&zspmDm=${zspmDm}&skssqq=${ssqq}&skssqz=${ssqz}&nsqxDm=${nsqxDm}&sbqxDm=${sbqxDm}`;
    if (test === 'true') {
      params += `&djxh=${djxh}&nsrsbh=${nsrsbh}&test=${test}`;
    }
    if (!gdslxDm || !sbywbm || !zsxmDm || !zspmDm || !ssqq || !ssqz || !nsqxDm || !sbqxDm) {
      console.info(`Error params, params = [${params}]`);
      params = '';
    }

    if (params && ssqq && ssqz && ssqq === ssqz) {
      // 按次申报，不检验
      console.info(`按次申报, 不进行校验. ssqq = ${ssqq}, ssqz = ${ssqz}`);
      params = '';
    }

    if (params) {
      const { yqsbbz } = parent.parent;
      params += `&yqsbbz=${yqsbbz}`;

      if (yqsbbz !== 'Y') {
        const sUrl = `${parent.pathRoot}/biz/yqsb/yqsbqc/enterYqsbUrl?${params}`;
        $.ajax({
          url: sUrl,
          type: 'GET',
          data: {},
          dataType: 'json',
          contentType: 'application/json',
          success(data) {
            const { sfkyqsbbz } = data;
            if (sfkyqsbbz && sfkyqsbbz === 'N') {
              $(window.parent.document.body).mask('&nbsp;');
              window.parent.cleanMeunBtn();
              const wfurl = data.wfurlList;
              const btnName = wfurl !== undefined && wfurl !== '' && wfurl !== null ? '去办理' : '确定';
              const b = parent.layer.confirm(
                data.msg,
                {
                  // area: ['250px','150px'],
                  title: '提示',
                  closeBtn: false,
                  btn: [btnName],
                },
                function (index) {
                  parent.layer.close(b);
                  if (wfurl !== undefined && wfurl !== '' && wfurl !== null) {
                    const { gnurl } = wfurl[0];
                    const url = `${parent.location.protocol}//${parent.location.host}${gnurl}`;
                    parent.parent.window.location.href = url;
                  } else if (navigator.userAgent.indexOf('MSIE') > 0) {
                    if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
                      parent.window.opener = null;
                      parent.window.close();
                    } else {
                      parent.window.open('', '_top');
                      parent.window.top.close();
                    }
                  } else if (navigator.userAgent.indexOf('Firefox') > 0) {
                    parent.window.location.href = 'about:blank ';
                    parent.window.close();
                  } else if (navigator.userAgent.indexOf('Chrome') > 0) {
                    parent.top.open(location, '_self').close();
                  } else {
                    parent.window.open('', '_top');
                    parent.window.top.close();
                  }
                },
              );
            } else if (sfkyqsbbz === 'Y') {
              // 弹框提示，不阻断
              // modify by dfw - 2018年11月26日15:09:17。
              // 可能会返回逾期提示(yqtsmsg)和处罚提示(msg)。
              // 处理逻辑->弹出逾期提示（继续申报） -> 弹出行政处罚（确定）
              if (data.msg) {
                if (data.yqtsmsg) {
                  const tab = parent.layer.confirm(
                    data.yqtsmsg,
                    {
                      title: '提示',
                      closeBtn: false,
                      btn: ['继续申报'],
                    },
                    function (index) {
                      parent.layer.close(tab);

                      const subTab = parent.layer.confirm(
                        data.msg,
                        {
                          title: '提示',
                          closeBtn: false,
                          btn: ['确定'],
                        },
                        function (index1) {
                          parent.layer.close(subTab);
                        },
                      );
                    },
                  );
                } else {
                  const tab = parent.layer.confirm(
                    data.msg,
                    {
                      title: '提示',
                      closeBtn: false,
                      btn: ['确定'],
                    },
                    function (index) {
                      parent.layer.close(tab);
                    },
                  );
                }
              } else if (data.yqtsmsg) {
                const tab = parent.layer.confirm(
                  data.yqtsmsg,
                  {
                    title: '提示',
                    closeBtn: false,
                    btn: ['继续申报'],
                  },
                  function (index) {
                    parent.layer.close(tab);
                  },
                );
              } else {
                // 不存在这种返回。阻断必须要给出提示。
                // layer.alert('无效的提示信息，请联系系统管理员！', {icon: 5});
              }
            }
          },
          error() {
            layer.alert('由于链接超时或网络异常导致逾期申报校验失败，请稍候刷新重试！', { icon: 5 });
          },
        });
      }
    }
  }
}

/**
 * 环保税B表自定义计算公式
 * @returns
 */

// IE8 不兼容JQ的$.isEmptyObject()方法

function isEmptyObject(obj) {
  return obj === '' || obj === null || obj === undefined || JSON.stringify(obj) === '[]' || JSON.stringify(obj) === '{}'
}

function HBSB_GET_JSJSDWMC() {
  const { sblx } = this.formData.ywbw.hbssbForm;
  if (sblx === '02') {
    // 简易申报计量单位为空
    return '';
  }
  const zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码
  const dmMcbz = arguments[2]; // 代码还是名称赋值标记

  if (isEmptyObject(zspmDm)) {
    return '';
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[3]) {
    return '';
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;

  if (!isEmptyObject(zszmDm)) {
    if (dmMcbz === '0') {
      // 名称赋值
      return zszmCT[zspmDm][zszmDm].jsjsdwmc;
    }
    if (dmMcbz === '1') {
      // 代码赋值
      return zszmCT[zspmDm][zszmDm].jldwDm;
    }
  }
  if (dmMcbz === '0' && zspmCT[zsxmDm][zspmDm].pmtzzBz === 'Y') {
    // 名称赋值

    return zspmCT[zsxmDm][zspmDm].jsjsdwmc;
  }
  if (dmMcbz === '1' && zspmCT[zsxmDm][zspmDm].pmtzzBz === 'Y') {
    // 代码赋值
    return zspmCT[zsxmDm][zspmDm].jldwDm;
  }

  return '';
}

function HBSB_GET_WRDLZ() {
  const { sblx } = this.formData.ywbw.hbssbForm;
  if (sblx === '02') {
    // 简易申报污染当量值为0且不可填
    return 0.0;
  }
  const zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码

  if (isEmptyObject(zspmDm)) {
    return 0.0;
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[2]) {
    return 0.0;
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;

  if (!isEmptyObject(zszmDm)) {
    if (zszmCT[zspmDm][zszmDm].tzzbz1 === 'Y') {
      return zszmCT[zspmDm][zszmDm].wrdlz;
    }
  }
  if (zspmCT[zsxmDm][zspmDm].tzzbz1 === 'Y' && zspmCT[zsxmDm][zspmDm].pmtzzBz === 'Y') {
    return zspmCT[zsxmDm][zspmDm].wrdlz;
  }
  return 0.0;
}

function HBSB_GET_WRDLS() {
  const ewbhxh = arguments[4]; // 二维表行序号
  const { wrdls } = this.formData.ywbw.sbskxxGrid.sbskxxGridlb[ewbhxh - 1];

  const { sblx } = this.formData.ywbw.hbssbForm;
  // 如果他本身就有数据了， 就不需要重新计算，  曾删行的时候会触发公式，如果重新计算会覆盖自己手填写的
  if (wrdls !== 0 && sblx === '02') {
    return wrdls;
  }

  if (isEmptyObject(zspmDm)) {
    return 0.0;
  }
  let zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码
  const zbz = arguments[2]; // 特征指标值
  const tzxs = this.formData.ywbw.sbskxxGrid.sbskxxGridlb[ewbhxh - 1].tzcwxs; // 特征系数

  if (isEmptyObject(zspmDm)) {
    return 0.0;
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[3]) {
    return 0.0;
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;

  let tzzbz1 = '';
  let wrdlz = 0.0;
  let tzzjsf = ''; // 特征值计算规则 1：特征污染当量值;2：特征污染当量数;3：特征产污系数
  if (!isEmptyObject(zszmDm)) {
    tzzbz1 = zszmCT[zspmDm][zszmDm].tzzbz1;
    wrdlz = zszmCT[zspmDm][zszmDm].wrdlz;
    tzzjsf = zszmCT[zspmDm][zszmDm].tzzjsf;
    if (sblx === '01' && wrdlz !== 0) {
      // 采用特征系数计算的，“污染当量数或计税依据”=“特征指标数量”×“特征系数”÷“污染当量值”
      if (tzzjsf === '3') {
        return (zbz * tzxs) / wrdlz;
      }

      // 采用特征值计算的，“污染当量数或计税依据”=“特征指标数量”×“特征值）
      if (tzzjsf === '2') {
        return zbz * wrdlz;
      }

      // 采用污染当量值计算的，“污染当量数或计税依据”=“特征指标数量”÷“污染当量值”
      if (tzzjsf === '1') {
        return zbz / wrdlz;
      }
    }
  }
  tzzbz1 = zspmCT[zsxmDm][zspmDm].tzzbz1;
  wrdlz = zspmCT[zsxmDm][zspmDm].wrdlz;
  tzzjsf = zspmCT[zsxmDm][zspmDm].tzzjsf;
  if (sblx === '01' && wrdlz !== 0 && zspmCT[zsxmDm][zspmDm].pmtzzBz === 'Y') {
    // 采用特征系数计算的，“污染当量数或计税依据”=“特征指标数量”×“特征系数”÷“污染当量值”
    if (tzzjsf === '3') {
      return (zbz * tzxs) / wrdlz;
    }

    // 采用特征值计算的，“污染当量数或计税依据”=“特征指标数量”×“特征值）
    if (tzzjsf === '2') {
      return zbz * wrdlz;
    }

    // 采用污染当量值计算的，“污染当量数或计税依据”=“特征指标数量”÷“污染当量值”
    if (tzzjsf === '1') {
      return zbz / wrdlz;
    }
  }
  return 0.0;
}

function HBSB_GET_TZCWXS() {
  const { sblx } = this.formData.ywbw.hbssbForm;
  if (sblx === '02') {
    return 0.0;
  }
  const zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码

  if (isEmptyObject(zspmDm)) {
    return 0.0;
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[2]) {
    return 0.0;
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;
  let tzzjsf = '';
  if (!isEmptyObject(zszmDm)) {
    tzzjsf = zszmCT[zspmDm][zszmDm].tzzjsf;
    if (tzzjsf === '3') {
      return zszmCT[zspmDm][zszmDm].tzcwxs;
    }
  }
  if (zspmCT[zsxmDm][zspmDm].pmtzzBz === 'Y') {
    tzzjsf = zspmCT[zsxmDm][zspmDm].tzzjsf;
    if (tzzjsf === '3') {
      return zspmCT[zsxmDm][zspmDm].tzcwxs;
    }
  }
  return 0.0;
}

function HBSB_GET_SL1() {
  const zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码

  if (isEmptyObject(zspmDm)) {
    return 0.0;
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[2]) {
    return 0.0;
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;

  const { sblx } = this.formData.ywbw.hbssbForm;
  if (sblx === '01') {
    if (!isEmptyObject(zszmDm)) {
      return zszmCT[zspmDm][zszmDm].sl1;
    }
  } else if (!isEmptyObject(zszmDm)) {
    if (zszmCT[zspmDm][zszmDm] && zszmCT[zspmDm][zszmDm].sl1 !== undefined && zszmCT[zspmDm][zszmDm].sl1 !== null) {
      return zszmCT[zspmDm][zszmDm].sl1;
    }
  }
  const { pmtzzBz } = zspmCT[zsxmDm][zspmDm];
  if (pmtzzBz === 'N' && sblx === '01') {
    return 0.0;
  }
  return zspmCT[zsxmDm][zspmDm].sl1;
}
/**
 * 环保税B表减免税额计算——默认规则
 * @returns {Number}
 */
function HBS_GET_JMSE() {
  const zspmDm = arguments[0];
  const zszmDm = arguments[1];
  const ynse = arguments[2];
  const jmxzDm = arguments[3];
  let jmse = 0;
  if (jmxzDm !== '' && jmxzDm !== null && this.formCT.jmxzCT !== undefined && zspmDm.indexOf('1012130') === -1) {
    jmse = (
      (this.formCT.jmxzCT[zspmDm][jmxzDm].jmed === undefined ? 1 : this.formCT.jmxzCT[zspmDm][jmxzDm].jmed) * ynse
    ).toFixed(2);
  }

  return jmse;
}

function HBSB_GET_FZSPMDM() {
  const zspmDm = arguments[0]; // 征收品目代码
  const zszmDm = arguments[1]; // 征收子目代码

  if (isEmptyObject(zspmDm)) {
    return '';
  }

  const zsxmDm = `${zspmDm.substring(0, 6)}000`;
  if (zsxmDm !== arguments[2]) {
    return '';
  }
  const { zspmCT } = this.formCT;
  const { zszmCT } = this.formCT;

  if (!isEmptyObject(zszmDm)) {
    return zszmCT[zspmDm][zszmDm].sjpmDm;
  }

  return zspmCT[zsxmDm][zspmDm].sjpmDm;
}

/**
 * 更正申报，不执行正常申报的公式时使用。 有关联公式时可能不起效
 * @param flag
 * @param trueValue
 * @param falseValue 传入公式左边的ng-model, 如果flag不通过，赋值原来的，相当于不赋值
 */
function gzsbSetValue(flag, trueValue, falseValue) {
  if (flag) {
    return eval(trueValue);
  }
  return eval(falseValue);
}

function HBSB_CHECK_YJSE() {
  const rtnData = {};
  rtnData.isTrue = true;
  rtnData.yjse = 0.0;

  const zspmDm = arguments[0];
  const yjse = arguments[1];
  const sybh = arguments[2];
  const zszmDm = arguments[3];
  const ewbhxh = arguments[4];
  const sbywbm = $('#ywbm').val().toUpperCase();

  if (yjse === 0) {
    // 填写的预缴税额为0
    rtnData.isTrue = true;
    rtnData.yjse = 0.0;
    return rtnData;
  }
  // 输入值不为0需做校验
  if (isEmptyObject(zspmDm)) {
    rtnData.isTrue = false;
    rtnData.yjse = 0.0;
    return rtnData;
  }
  const { skssqq } = this.formData.ywbw.hbssbForm;
  const { skssqz } = this.formData.ywbw.hbssbForm;
  const { nsryjxxGridlb } = this.formData.qcs.initData.nsryjxxGrid;

  let yjyehj = 0.0;

  if (nsryjxxGridlb.length > 0 && !isEmptyObject(nsryjxxGridlb[0].zspmDm)) {
    for (let i = 0; i < nsryjxxGridlb.length; i++) {
      // 预缴属期止只要在所属期止之前的都可用
      if (nsryjxxGridlb[i].zspmDm === zspmDm && nsryjxxGridlb[i].skssqz <= skssqz) {
        yjyehj += nsryjxxGridlb[i].yjyehj;
      }
    }
  }

  const syxx = this.formData.ywbw.sbskxxGrid.sbskxxGridlb;
  let key1 = '';
  // 环保税A
  if (sbywbm === 'HBS_A') {
    key1 = `${sybh}_${zspmDm}_${zszmDm}`;
  }

  // 环保税A
  if (sbywbm === 'HBS_B') {
    key1 = `${ewbhxh}_${zspmDm}_${zszmDm}`;
  }

  // 已用预缴余额合计
  let yyyjehj = 0;

  for (let i = 0; i < syxx.length; i++) {
    let key2 = '';
    // 环保税A
    if (sbywbm === 'HBS_A') {
      key2 = `${syxx[i].sybh1}_${syxx[i].zspmDm}_${syxx[i].zszmDm}`;
    }

    // 环保税B
    if (sbywbm === 'HBS_B') {
      key2 = `${syxx[i].ewbhxh}_${syxx[i].zspmDm}_${syxx[i].zszmDm}`;
    }

    if (key1 !== key2) {
      yyyjehj += syxx[i].yjse;
    }
  }

  // 可用预缴余额
  const kyyjye = yjyehj - yyyjehj >= 0 ? yjyehj - yyyjehj : 0;

  rtnData.isTrue = yjse <= kyyjye;
  rtnData.yjse = kyyjye;

  return rtnData;
}

/**
 * 当第14、15、16、17、18列均≠0，14列绝对值=15列+16列+17列+18列的绝对值
 * @param nsrsbh
 * @param qsannd
 * @param qernd
 * @param qyind
 * @param bn
 * @returns qsind
 */
function qysdsa17nd_fhbxzctzdynstzmxbGridlb_qsind(nsrsbh, qsannd, qernd, qyind, bn) {
  const { fhbxzctzdynstzmxbGridlb } = formData.ywbw.fhbxzctzdynstzmxb.fhbxzctzdynstzmxbGrid;
  let qsind = 0;
  for (let i = 0; i < fhbxzctzdynstzmxbGridlb.length; i++) {
    // 判断条件可能不足
    if (
      nsrsbh === fhbxzctzdynstzmxbGridlb[i].nsrsbh &&
      qsannd === fhbxzctzdynstzmxbGridlb[i].qsannd &&
      qernd === fhbxzctzdynstzmxbGridlb[i].qernd &&
      qyind === fhbxzctzdynstzmxbGridlb[i].qyind &&
      bn === fhbxzctzdynstzmxbGridlb[i].bn
    ) {
      qsind = fhbxzctzdynstzmxbGridlb[i].qsind;
      break;
    }
  }
  if (qsind !== 0 && qsannd !== 0 && qernd !== 0 && qyind !== 0 && bn !== 0) {
    qsind = Math.abs(qsannd + qernd + qyind + bn);
  }
  return qsind;
}

/**
 * 判断封面经办人是否为空，为辅助节点
 * @param jbr
 * @returns {String}
 */
function qysdsa17nd_A000000_jbrVaild(jbr, node) {
  if (jbr == '' || jbr == undefined || jbr == null) {
    return '';
  }
  if (node == 'jbrzyzjhm') {
    const { smzbz } = formData.qcs.initData.nsrjbxx.smzxx;
    if (smzbz == 'Y') {
      return formData.qcs.initData.nsrjbxx.smzxx.zjhm;
    }
    return formData.qcs.initData.qysdsndAInitData.jbrzyzjhm;
  }
  return formData.qcs.initData.qysdsndAInitData.dlsbrq;
}

function qysdsa17nd_A109000_lastSeasonYjbData(flag) {
  const { sbQysdsczzsndsb2014NsrqtxxVO } = formData.qcs.initData.qysdsndAInitData;
  if (sbQysdsczzsndsb2014NsrqtxxVO == undefined || sbQysdsczzsndsb2014NsrqtxxVO == null) {
    return 0;
  }
  if (flag == 'ZJGLJYFTDSDSE') {
    return isNull(sbQysdsczzsndsb2014NsrqtxxVO.zjgftse) ? 0 : sbQysdsczzsndsb2014NsrqtxxVO.zjgftse;
  }
  if (flag == 'CZJZFPSDSE_LJ') {
    return isNull(sbQysdsczzsndsb2014NsrqtxxVO.zjgczjzftse) ? 0 : sbQysdsczzsndsb2014NsrqtxxVO.zjgczjzftse;
  }
  if (flag == 'FZJGYFTSDSE_LJ') {
    return isNull(sbQysdsczzsndsb2014NsrqtxxVO.fzjgftse) ? 0 : sbQysdsczzsndsb2014NsrqtxxVO.fzjgftse;
  }
  if (flag == 'ZJGDLSCJYBMYFTSDSE_LJ') {
    return isNull(sbQysdsczzsndsb2014NsrqtxxVO.scjydlbmse) ? 0 : sbQysdsczzsndsb2014NsrqtxxVO.scjydlbmse;
  }
  return 0;
}
/**
 * 计算两个日期之间的天数
 * @param startDate
 * @param endDate
 * @returns {Number}
 */
function getDateDiff(startDate, endDate) {
  if (isEmptyObject(startDate) || isEmptyObject(endDate)) {
    return 0;
  }
  const startTime = new Date(Date.parse(startDate.replace(/-/g, '/'))).getTime();
  const endTime = new Date(Date.parse(endDate.replace(/-/g, '/'))).getTime();
  const dates = Math.abs(startTime - endTime) / (1000 * 60 * 60 * 24);
  return dates;
}

/**
 * 大连个性化
 * 计算两个日期之间的天数
 * @param startDate
 * @param endDate
 * @returns {Number}
 */
function getDateDiffByDl(startDate, endDate) {
  if (isEmptyObject(startDate) || isEmptyObject(endDate)) {
    return 0;
  }
  const startTime = new Date(Date.parse(startDate.replace(/-/g, '/'))).getTime();
  const endTime = new Date(Date.parse(endDate.replace(/-/g, '/'))).getTime();
  const dates = (startTime - endTime) / (1000 * 60 * 60 * 24);
  return dates;
}

/**
 * 传入formData... ，防止数据不在的时候公式编译失败。
 * @param str
 * @returns {Boolean}
 */
function hasNotZeroData(str) {
  try {
    const data = eval(str);
    if (Number(data) !== 0) {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
}

function dataEquals(str, val) {
  try {
    const data = eval(str);
    if (Number(data) === Number(val)) {
      return true;
    }
    return false;
  } catch (e) {
    return false;
  }
}

function sfjmba(hmc1, ssjmxzDm, jmsspsxDm, jmxxGridlb, jmzlxDm, gzsbBz) {
  if (jmxxGridlb !== undefined && jmxxGridlb.length > 0 && gzsbBz === 1) {
    if (ssjmxzDm !== '' && ssjmxzDm !== null && jmsspsxDm !== '' && jmsspsxDm !== null) {
      for (let i = 0; i < jmxxGridlb.length; i++) {
        if (
          ssjmxzDm === jmxxGridlb[i].ssjmxzhzDm &&
          jmsspsxDm === jmxxGridlb[i].jmsspsxDm &&
          jmzlxDm === jmxxGridlb[i].jmzlxDm
        ) {
          return true;
        }
      }
      return false;
    }
  }
  return true;
}

// 成品油消费税获取主表对应品目的xssl
function cpyxfs_getXsslByZspmDm(zspmDm, sbsjxxcpyGridlb, cpyxsslhj, bqzykcsl) {
  if (sbsjxxcpyGridlb !== undefined) {
    for (let i = 0; i < sbsjxxcpyGridlb.length; i++) {
      if (sbsjxxcpyGridlb[i].zspmDm === zspmDm) {
        return sbsjxxcpyGridlb[i].xssl;
      }
    }
  }
  return bqzykcsl;
}

// 成品油消费税获取对应商品和服务税收分类编码的委托加工收回数量
function cpyxfs_getWtjgshslBySphfwssflbm(sphfwssflbm, stfdkdjskqkGridlb, bqwtjgshsl, wtjgshslhj, tempSphfwssflbm) {
  if (stfdkdjskqkGridlb !== undefined && sphfwssflbm !== undefined) {
    for (let i = 0; i < stfdkdjskqkGridlb.length; i++) {
      if (stfdkdjskqkGridlb[i].sphfwssflbm === sphfwssflbm) {
        return stfdkdjskqkGridlb[i].wtjgshsl;
      }
    }
  }
  return bqwtjgshsl;
}

// 成品油消费税获取按照【商品和服务税收分类编码】对应的征收品目汇总本期委托加工收回用于连续生产数量
function cpyxfs_getBqwtjgshyylxscslBySphfwssflbm(wtjghsdysxfplyqkGridlb, sphfwssflbm, bqwtjgshyylxscsl) {
  const { sphfwssflbmCT } = this.formCT;
  let zspmDm = '';
  if (sphfwssflbm !== undefined && sphfwssflbmCT !== undefined) {
    zspmDm = sphfwssflbmCT[sphfwssflbm] !== null ? sphfwssflbmCT[sphfwssflbm].zspmdm : '';
  }
  let hssl = 0;
  if (wtjghsdysxfplyqkGridlb !== undefined && zspmDm !== '101020604' && zspmDm !== '101020606') {
    for (let i = 0; i < wtjghsdysxfplyqkGridlb.length; i++) {
      // 其他煤油不参与比对
      if (
        wtjghsdysxfplyqkGridlb[i].zspmDm === zspmDm &&
        wtjghsdysxfplyqkGridlb[i].sphfwssflbm !== '1070101020200000000'
      ) {
        hssl += wtjghsdysxfplyqkGridlb[i].bqwtjgshyylxscsl;
      }
    }
  }
  return hssl;
}

// 成品油消费税获取《本期准予扣除税额计算表》中对应品目的[委托加工收回数量]
function cpyxfs_getWtjghsslByZspmDm(sphfwssflbm, wtjghssl, dksejkcjsForm) {
  const { sphfwssflbmCT } = this.formCT;
  let zspmDm = '';
  if (sphfwssflbm !== undefined && sphfwssflbmCT !== undefined) {
    zspmDm = sphfwssflbmCT[sphfwssflbm] !== null ? sphfwssflbmCT[sphfwssflbm].zspmdm : '';
  }
  if (dksejkcjsForm !== undefined) {
    if (zspmDm === '101020609') {
      // 汽油
      return dksejkcjsForm[0].wtjghssl;
    }
    if (zspmDm === '101020603') {
      // 柴油
      return dksejkcjsForm[1].wtjghssl;
    }
    if (zspmDm === '101020605') {
      // 石脑油
      return dksejkcjsForm[2].wtjghssl;
    }
    if (zspmDm === '101020607') {
      // 润滑油
      return dksejkcjsForm[3].wtjghssl;
    }
    if (zspmDm === '101020608') {
      // 燃料油
      return dksejkcjsForm[4].wtjghssl;
    }
  }
  return 0;
}

/**
 * 检查数据是否为空 公式编译时，会把没为定义的参数赋值为{}, 这个是一个对象。
 *
 * @method isNullForStr
 * @param param
 *            {Object} 参数对象
 * @returns {Boolean} 检查结果为空或未定义返回true，不为空返回false
 */
function isNullForStr(param) {
  if (
    param === null ||
    param === 'null' ||
    param === undefined ||
    param === 'undefined' ||
    param === '' ||
    typeof param === 'object'
  ) {
    return true;
  }
  return false;
}

/**
 * 获取附加税更正减免性质在码表的下标
 */
function fjsgzsb_getIndex(jmxzDm, option, rdpzuuid, zspmDm) {
  const sbxx = formData.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
  if (parent.location.href) {
    if (parent.location.href.indexOf('appid=') > -1 || parent.location.href.indexOf('gzsb=zx') > -1) {
      for (const i in sbxx) {
        if (sbxx[i].rdpzuuid === rdpzuuid) {
          jmxzDm = sbxx[i].ssjmxzDm;
        }
      }
    }
  }
  let jmxzIndex = 0;
  if (option !== undefined && option !== null) {
    for (let i = 0; i < option.length; i++) {
      // let tempValue = i+"";
      if (option[i] !== null && option[i].dm === jmxzDm && option[i].pc === zspmDm) {
        jmxzIndex = i;
        return `${jmxzIndex}`;
      }
    }
  }
  return '';
}

/**
 * 获取附加税更正减免性质在码表的下标
 */
function fjsgzsb_getIndexYwzt(jmxzDm, option, rdpzuuid, zspmDm) {
  if (parent.location.href) {
    if (parent.location.href.indexOf('gzsb=zx') > -1) {
      // 更正申报的已申报数据在hq_.fjssbbw节点下
      let sbxx;
      if (parent.location.href.indexOf('lhsbywbm=fjssb') > -1) {
        sbxx = formData.hq_fs_.fjssbbw.fjssbb.sbxxGrid.sbxxGridlbVO;
      } else {
        sbxx = formData.hq_.fjssbbw.fjssbb.sbxxGrid.sbxxGridlbVO;
      }
      for (const i in sbxx) {
        if (sbxx[i].rdpzuuid === rdpzuuid) {
          jmxzDm = sbxx[i].ssjmxzDm;
        }
      }
    }
  }
  let jmxzIndex = 0;
  if (option !== undefined && option !== null) {
    for (let i = 0; i < option.length; i++) {
      if (option[i] !== null && option[i].dm === jmxzDm && option[i].pc === zspmDm) {
        jmxzIndex = i;
        return `${jmxzIndex}`;
      }
    }
  }
  return '';
}

/**
 * 获取附加税申报减免性质代码在码表的下标索引
 * @param idx
 * @param jmxzDm
 * @param swsxDm
 * @param zspmDm
 * @param option
 */
function fjssb_getIndexYwzt(jmxzDm, option, zspmDm, swsxDm, index) {
  // 更正申报就返回原来的值,fjsgzsb_getIndexYwzt方法已经赋值
  if (parent.location.href) {
    if (parent.location.href.indexOf('gzsb=zx') > -1) {
      let sbxx;
      if (parent.location.href.indexOf('lhsbywbm=fjssb') > -1) {
        sbxx = formData.ht_fs_.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
      } else if (parent.location.href.indexOf('xfssb') > -1) {
        sbxx = formData.ht_.xfssbSbbdxxVO.xfsFjs.fjssb.fjsxxGrid.fjsxxGridlb;
      } else {
        sbxx = formData.ht_.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
      }
      return sbxx[index] ? sbxx[index].t_tempJmxzDm : '';
    }
  }
  let jmxzIndex = '';
  if (option !== undefined && option !== null) {
    for (let i = 0; i < option.length; i++) {
      if (option[i] !== null && option[i].dm === jmxzDm && option[i].pc === zspmDm && option[i].swsxDm === swsxDm) {
        jmxzIndex = i;
        return `${jmxzIndex}`;
      }
    }
  }
  return '';
}

/**
 * 获取增值税预缴减免性质在码表的下标
 */
function zzsyjsbsb_getIndex(jmxzDm, option, rdpzuuid, zspmDm) {
  const sbxx = formData.qcs.initData.zzsyjsbInitData.sfzrdxxVO.sfzrdxxVOlb;
  if (rdpzuuid !== null && rdpzuuid !== '') {
    for (const i in sbxx) {
      if (sbxx[i].rdpzuuid === rdpzuuid) {
        jmxzDm = sbxx[i].ssjmxzDm !== undefined ? sbxx[i].ssjmxzDm : '';
      }
    }
  }
  let jmxzIndex = 0;
  if (option !== undefined && option !== null) {
    for (let i = 0; i < option.length; i++) {
      if (option[i] !== null && option[i].dm === jmxzDm && option[i].pc === zspmDm) {
        jmxzIndex = i;
        return `${jmxzIndex}`;
      }
    }
  }
  return jmxzDm;
}


/**
 * 从url地址中获取参数
 */
function GET_PARAM(link, csm) {
  const paraObj = {};
  if (link) {
    const qs = link.split('&');
    for (let i = 0; i < qs.length; i++) {
      const kv = qs[i].split('=');
      if (kv[0] && kv[1]) {
        paraObj[kv[0]] = kv[1];
      }
    }
  }
  if (paraObj[csm]) {
    const csz = paraObj[csm] * 1;
    if (isNaN(csz)) {
      return 0;
    }
    return csz;
  }
  return 0;
}

/**
 * 判断是否存在于数组中。如果存在返回下标索引。否则返回-1
 * 参数列表:
 * key ,需要校验是否存在的key值。
 * column ,值所在列名。
 * array 数组
 * param 二级数组参数。
 */
function getIndexFromArray(key, column, array, param) {
  if (!array) {
    return -1;
  }
  if (!param) {
    for (const i in array) {
      if (array[i][column] && array[i][column] === key) {
        return i;
      }
    }
  } else {
    let array2 = [];
    let pc = '';
    let path = param;
    if (param.indexOf('[') > -1 && param.indexOf(']') > -1) {
      pc = param.substring(param.indexOf('[') + 1, param.indexOf(']'));
      path = param.substr(param.indexOf(']') + 1);
    }
    const pcs = pc.indexOf('&') === -1 ? [] : pc.split('&');
    const paths = path.split('.');
    for (const i in array) {
      if (pcs.length === 0) {
        array2 = pushIntoArray(array2, getItemByPath(array[i], paths));
      } else if (array[i][pcs[0]] && array[i][pcs[0]] === pcs[1]) {
        array2 = pushIntoArray(array2, getItemByPath(array[i], paths));
      }
    }
    for (const i in array2) {
      if (array2[i][column] && array2[i][column] === key) {
        return i;
      }
    }
  }

  return -1;
}

function pushIntoArray(arr, items) {
  if (!items) {
    return arr;
  }
  let isArray = false;
  if (Array) {
    if (items instanceof Array) {
      isArray = true;
    }
  } else if (items.length !== undefined && typeof items !== 'string') {
    isArray = true;
  }
  if (isArray) {
    for (const i in items) {
      arr = pushIntoArray(arr, items[i]);
    }
  } else {
    arr.push(items);
  }
  return arr;
}

function getItemByPath(obj, paths) {
  const item = [];
  let iterator = obj;
  for (const i in paths) {
    if (paths[i]) {
      if (!iterator) {
        break;
      }
      if (i !== 'indexOf') {
        iterator = iterator[paths[i]];
      }
    }
  }
  return iterator;
}
/**
 * 判断特定行业个人所得税年度申报表主表减免税额和附表中的减免税额是不是一致
 * param
 * fblx 附表类型 1：个人所得税减免税事项报告表 2：商业健康保险税前扣除表
 * jmse1/jmse2无实际意义，用于触发校验
 * */
function tdhyndsbb_jmsejy(fblx, sfzjlxdm, sfzjhm, xm, jmse1, jmse2) {
  if (fblx == '1') {
    // 主表减免税额
    let jmse = 0;
    // 主表list
    const { tdhyndsdssbbMx } = this.formData.tdhyndsdssbbBzds.body.tdhyndsdssbb;
    if (tdhyndsdssbbMx != null && tdhyndsdssbbMx != undefined && tdhyndsdssbbMx.length > 0) {
      for (i = 0; i < tdhyndsdssbbMx.length; i++) {
        const tdhyndsdssbbMxObj = tdhyndsdssbbMx[i];
        if (tdhyndsdssbbMxObj != null && tdhyndsdssbbMxObj != undefined) {
          if (
            tdhyndsdssbbMxObj.zzlxdm == sfzjlxdm &&
            tdhyndsdssbbMxObj.zzhm == sfzjhm &&
            tdhyndsdssbbMxObj.nsrxm == xm
          ) {
            jmse += ROUND(tdhyndsdssbbMxObj.jmse, 2);
          }
        }
      }
    }
    // 附表减免税额
    let fbjmse = 0;
    // 附表list
    const { jmssxMx } = this.formData.grsdsjmssxbgbBzds.body.grsdsjmssxbgbMx.jmssxMxlb;
    if (jmssxMx != null && jmssxMx != undefined && jmssxMx.length > 0) {
      for (i = 0; i < jmssxMx.length; i++) {
        const jmssxMxObj = jmssxMx[i];
        if (jmssxMxObj != null && jmssxMxObj != undefined) {
          if (jmssxMxObj.sfzjlxDm == sfzjlxdm && jmssxMxObj.sfzjhm == sfzjhm && jmssxMxObj.nsrxm == xm) {
            fbjmse += ROUND(jmssxMxObj.jmse, 2);
          }
        }
      }
    }
    if (fbjmse != jmse) {
      return false;
    }
    return true;
  }
  if (fblx == '2') {
    // 主表减免税额
    let jmse = 0;
    // 主表list
    const { tdhyndsdssbbMx } = this.formData.tdhyndsdssbbBzds.body.tdhyndsdssbb;
    if (tdhyndsdssbbMx != null && tdhyndsdssbbMx != undefined && tdhyndsdssbbMx.length > 0) {
      for (i = 0; i < tdhyndsdssbbMx.length; i++) {
        const tdhyndsdssbbMxObj = tdhyndsdssbbMx[i];
        if (tdhyndsdssbbMxObj != null && tdhyndsdssbbMxObj != undefined) {
          if (
            tdhyndsdssbbMxObj.zzlxdm == sfzjlxdm &&
            tdhyndsdssbbMxObj.zzhm == sfzjhm &&
            tdhyndsdssbbMxObj.nsrxm == xm
          ) {
            jmse += ROUND(tdhyndsdssbbMxObj.qnsqkcxmsyjkxje, 2);
          }
        }
      }
    }
    // 附表减免税额
    let fbjmse = 0;
    // 附表list
    const { syjkbxmx } = this.formData.syjkbxmxlb;
    if (syjkbxmx != null && syjkbxmx != undefined && syjkbxmx.length > 0) {
      for (i = 0; i < syjkbxmx.length; i++) {
        const syjkbxmxObj = syjkbxmx[i];
        if (syjkbxmxObj != null && syjkbxmxObj != undefined) {
          if (syjkbxmxObj.sfzjlxdm == sfzjlxdm && syjkbxmxObj.sfzjhm == sfzjhm && syjkbxmxObj.xm == xm) {
            fbjmse += ROUND(syjkbxmxObj.bqkcje, 2);
          }
        }
      }
    }
    if (fbjmse != jmse) {
      return false;
    }
    return true;
  }
}
/**
 * 判断是否包括数字
 */
function checkhasNum(r) {
  if (/[0-9]/.test(r)) {
    return true;
  }
  return false;
}

/**
 * 判断限售股转让所得扣缴个人所得税报告表1-7列是否存在完全相同的数据
 * */
function xsgzrsgkjsb_xtljy(xh, nsrxm, sfzjlxDm, sfzjhm, zqzhh, gpDm, gpmc, mgjsjg) {
  if (
    xh !== undefined &&
    nsrxm !== '' &&
    sfzjlxDm !== '' &&
    sfzjhm !== '' &&
    zqzhh !== '' &&
    gpDm !== '' &&
    gpmc !== '' &&
    mgjsjg !== 0
  ) {
    const { xsgzrsdkjgrsdsbgbMx } = this.formData.xsgzrsdkjgrsdsbgbBzds.xsgzrsdkjgrsdsbgbBody.xsgzrsdkjgrsdsbgb;
    if (xsgzrsdkjgrsdsbgbMx !== null && xsgzrsdkjgrsdsbgbMx !== undefined && xsgzrsdkjgrsdsbgbMx.length > 0) {
      for (i = 0; i < xsgzrsdkjgrsdsbgbMx.length; i++) {
        const xsgzrsdkjgrsdsbgbMxObj = xsgzrsdkjgrsdsbgbMx[i];
        if (
          xsgzrsdkjgrsdsbgbMxObj !== null &&
          xsgzrsdkjgrsdsbgbMxObj !== undefined &&
          xsgzrsdkjgrsdsbgbMxObj.xh !== undefined
        ) {
          if (xh !== xsgzrsdkjgrsdsbgbMxObj.xh) {
            if (
              nsrxm === xsgzrsdkjgrsdsbgbMxObj.nsrxm &&
              sfzjlxDm === xsgzrsdkjgrsdsbgbMxObj.sfzjlxDm &&
              sfzjhm === xsgzrsdkjgrsdsbgbMxObj.sfzjhm &&
              zqzhh === xsgzrsdkjgrsdsbgbMxObj.zqzhh &&
              gpDm === xsgzrsdkjgrsdsbgbMxObj.gpDm &&
              gpmc === xsgzrsdkjgrsdsbgbMxObj.gpmc &&
              mgjsjg === xsgzrsdkjgrsdsbgbMxObj.mgjsjg
            ) {
              xsgzrsdkjgrsdsbgbMx[xh - 1].cfxh = xsgzrsdkjgrsdsbgbMxObj.xh;
              return false;
            }
          }
        }
      }
    }
    return true;
  }
  return true;
}

/**
 * 广东小规模个性化：GDSDZSWJ-8100
 * 是否发生过销售不动产业务
 */
function gdXsbdcyw(lc1, lc4, lc9, lc13) {
  const total = lc1 + lc4 + lc9 + lc13;
  const { nsqxDm } = formData.qcs.initData.zzsxgmsbInitData;
  if (((nsqxDm === '06' && total > 30000) || (nsqxDm === '08' && total > 90000)) && lc4 > 0) {
    const defaultValue = formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse;
    const { gdLock } = formData.qcs.initData.zzsxgmsbInitData;
    let message = '';
    message += "<table style='font-size:14px;'>";
    message += '	<tr>';
    message += "		<td colspan='2' width='60%'>本期发生销售不动产业务？</td>";
    message += "		<td width='20%' valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="visible";' value='1' ${
      gdLock && gdLock === '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;是&nbsp;&nbsp;`;
    message += '		</td>';
    message += "		<td valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="hidden";' value='2' ${
      !gdLock || gdLock !== '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;否`;
    message += '		</td>';
    message += '	</tr>';
    message += `	<tr id='xsbdc' ${!gdLock || gdLock !== '1' ? "style='visibility: hidden;'" : ''} >`;
    message += "		<td colspan='2'>当期不动产不含税销售额：</td>";
    message += "		<td colspan='2'>";
    message += `			<input type='text' placeholder='请输入销售额' id='myInput' value='${
      defaultValue || defaultValue === 0 ? defaultValue : ''
    }' style='border:1px solid #fff;border-bottom-color:#b5b5b5;width:100%;'/>`;
    message += '		</td>';
    message += '	</tr>';
    message += '</table>';

    window.parent.layer.confirm(message, {
      type: 1,
      area: ['450px', '210px'],
      btn: ['确定'],
      title: '提示',
      closeBtn: 0,
      yes(index, layero) {
        const radioValue = $(layero).find(':radio[name="sfbqxsbdc"]:checked').val();
        if (radioValue === '1') {
          const value = $(layero).find('#myInput').val();
          if (value === '' || value === undefined || !/(^[-]{0,1}\d+$)|(^[-]{0,1}\d+\.\d+$)/g.test(value)) {
            $(layero).find('#myInput').val('');
            $(layero).find('#myInput').attr('placeholder', '请输入正确的不含税销售额');
          } else {
            const val = ROUND(Number(value), 2);
            formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse = val;
            formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse', val);
            window.parent.layer.closeAll();
            // 调整
            result = 1;
          }
        } else {
          formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse = 0;
          formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse', 0);
          window.parent.layer.closeAll();
          // 调整
          result = -1;
        }

        formData.qcs.initData.zzsxgmsbInitData.gdLock = result;
        formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdLock', result);
        const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
        const { viewEngine } = $('#frmSheet')[0].contentWindow;
        const { body } = $('#frmSheet')[0].contentWindow.document;
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
      },
    });
  } else {
    formData.qcs.initData.zzsxgmsbInitData.gdLock = 0;
    setTimeout(function () {
      formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdLock', 0);
      const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
      const { viewEngine } = $('#frmSheet')[0].contentWindow;
      const { body } = $('#frmSheet')[0].contentWindow.document;
      viewEngine.formApply($viewAppElement);
      viewEngine.tipsForVerify(body);
    }, 500);
  }
}

/**
 * 业务中台使用
 * 广东小规模个性化：GDSDZSWJ-8100
 * 是否发生过销售不动产业务
 */
function gdXsbdcywYwzt(lc1, lc4, lc9, lc13) {
  const total = lc1 + lc4 + lc9 + lc13;
  const { nsqxDm } = formData.fq_;
  if (((nsqxDm === '06' && total > 30000) || (nsqxDm === '08' && total > 90000)) && lc4 > 0) {
    const defaultValue = formData.ss_.gdDqbdcbhsxse;
    const { gdLock } = formData.ss_;
    let message = '';
    message += "<table style='font-size:14px;'>";
    message += '	<tr>';
    message += "		<td colspan='2' width='60%'>&nbsp;&nbsp;本期发生销售不动产业务？</td>";
    message += "		<td width='20%' valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="visible";' value='1' ${
      gdLock && gdLock === '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;是&nbsp;&nbsp;`;
    message += '		</td>';
    message += "		<td valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="hidden";' value='2' ${
      !gdLock || gdLock !== '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;否`;
    message += '		</td>';
    message += '	</tr>';
    message += `	<tr id='xsbdc' ${!gdLock || gdLock !== '1' ? "style='visibility:hidden;''" : ''} >`;
    message += "		<td colspan='2'>当期不动产不含税销售额：</td>";
    message += "		<td colspan='2'>";
    message += `			<input type='text' placeholder='请输入销售额' id='myInput' value='${
      defaultValue || defaultValue === 0 ? defaultValue : ''
    }' style='border:1px solid #fff;border-bottom-color:#b5b5b5;width:100%;'/>`;
    message += '		</td>';
    message += '	</tr>';
    message += '</table>';

    window.parent.layer.confirm(message, {
      type: 1,
      area: ['450px', '210px'],
      btn: ['确定'],
      title: '提示',
      closeBtn: 0,
      yes(index, layero) {
        const radioValue = $(layero).find(':radio[name="sfbqxsbdc"]:checked').val();
        if (radioValue === '1') {
          const value = $(layero).find('#myInput').val();
          if (value === '' || value === undefined || !/(^[-]{0,1}\d+$)|(^[-]{0,1}\d+\.\d+$)/g.test(value)) {
            $(layero).find('#myInput').val('');
            $(layero).find('#myInput').attr('placeholder', '请输入正确的不含税销售额');
          } else {
            const val = ROUND(Number(value), 2);
            formData.ss_.gdDqbdcbhsxse = val;
            formulaEngine.apply('ss_.gdDqbdcbhsxse', val);
            window.parent.layer.closeAll();
            // 调整
            result = 1;
          }
        } else {
          formData.ss_.gdDqbdcbhsxse = 0;
          formulaEngine.apply('ss_.gdDqbdcbhsxse', 0);
          window.parent.layer.closeAll();
          // 调整
          result = -1;
        }

        formData.ss_.gdLock = result;
        formulaEngine.apply('ss_.gdLock', result);
        const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
        const { viewEngine } = $('#frmSheet')[0].contentWindow;
        const { body } = $('#frmSheet')[0].contentWindow.document;
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
      },
    });
  } else {
    formData.ss_.gdLock = 0;
    setTimeout(function () {
      formulaEngine.apply('ss_.gdLock', 0);
      const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
      const { viewEngine } = $('#frmSheet')[0].contentWindow;
      const { body } = $('#frmSheet')[0].contentWindow.document;
      viewEngine.formApply($viewAppElement);
      viewEngine.tipsForVerify(body);
    }, 500);
  }
}

/**
 * JSONE-2115
 * 是否发生过销售不动产业务
 */
function xsbdcyw(xseHj, qzd, lc4fw) {
  if (xseHj > qzd && lc4fw > 0) {
    const defaultValue = formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse;
    const { gdLock } = formData.qcs.initData.zzsxgmsbInitData;
    let message = '';
    message += "<table style='font-size:14px;'>";
    message += '	<tr>';
    message +=
      "		<td colspan='2'>&nbsp;&nbsp;按照现行政策规定，小规模纳税人发生增值税应税销售行为，合计月销售额超过10万元（按季30万元），但扣除本期发生的销售不动产销售额后，未超过10万元（按季30万元）的，销售货物、劳务、服务、无形资产的月销售额可以免征增值税。您本期是否发生销售不动产的销售额？</td>";
    message += '    </tr>';
    message += '	<tr>';
    message += "		<td width='60%' valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="visible";' value='1' ${
      gdLock && gdLock === '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;是&nbsp;&nbsp;`;
    message += '		</td>';
    message += "		<td valign='middle'>";
    message += `			<input type='radio' name='sfbqxsbdc' onclick='document.getElementById("xsbdc").style.visibility="hidden";' value='2' ${
      !gdLock || gdLock !== '1' ? "checked='checked'" : ''
    }/>&nbsp;&nbsp;否`;
    message += '		</td>';
    message += '	</tr>';
    message += `	<tr id='xsbdc' ${!gdLock || gdLock !== '1' ? "style='visibility:hidden;''" : ''} >`;
    message += '		<td>为帮助您判断是否可以享受免征增值税政策，请准确录入本期销售不动产的销售额：</td>';
    message += '		<td>';
    message += `			<input type='text' placeholder='请输入销售额' id='myInput' value='${
      defaultValue || defaultValue === 0 ? defaultValue : ''
    }' style='border:1px solid #fff;border-bottom-color:#b5b5b5;width:100%;'/>`;
    message += '		</td>';
    message += '	</tr>';
    message += '</table>';

    window.parent.layer.confirm(message, {
      type: 1,
      area: ['450px', '290px'],
      btn: ['确定'],
      title: '提示',
      closeBtn: 0,
      yes(index, layero) {
        const radioValue = $(layero).find(':radio[name="sfbqxsbdc"]:checked').val();
        if (radioValue === '1') {
          const value = $(layero).find('#myInput').val();
          if (value === '' || value === undefined || !/(^[-]{0,1}\d+$)|(^[-]{0,1}\d+\.\d+$)/g.test(value)) {
            $(layero).find('#myInput').val('');
            $(layero).find('#myInput').attr('placeholder', '请输入正确的不含税销售额');
          } else {
            const val = ROUND(Number(value), 2);
            formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse = val;
            formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].bdcxse = val;
            formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse', val);
            window.parent.layer.closeAll();
            if (xseHj - val > qzd) {
              window.parent.layer.alert('本期销售额已达起征点，请继续申报。');
            } else {
              const lc1hw = formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[0].yzzzsbhsxse;
              const lc2hw =
                formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[0].swjgdkdzzszyfpbhsxse;
              const lc1fw = formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].yzzzsbhsxse;
              const lc2fw =
                formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].swjgdkdzzszyfpbhsxse;
              const lc4fw = formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].xsczbdcbhsxse;
              const lc5fw =
                formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].swjgdkdzzszyfpbhsxse1;
              if (lc1hw - lc2hw > 0 || lc1fw - lc2fw > 0 || lc4fw - lc5fw !== val) {
                window.parent.layer.alert(
                  '剔除不动产销售额后，您本期销售额未达起征点，请将除不动产销售额之外的本期应征增值税销售额（不含开具及代开专用发票销售额）对应填写在第10栏“小微企业免税销售额”或第11栏“未达起征点销售额”中；适用增值税差额征收政策的纳税人填写差额后的销售额，差额部分填写在附列资料对应栏次中。',
                );
              } else {
                window.parent.layer.alert('剔除不动产销售额后，本期销售额未达起征点，请继续申报');
              }
            }
            // 调整
            result = 1;
          }
        } else {
          formData.qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse = 0;
          formData.zzssyyxgmnsrySbSbbdxxVO.zzssyyxgmnsr.zzsxgmGrid.zzsxgmGridlb[1].bdcxse = 0;
          formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdDqbdcbhsxse', 0);
          window.parent.layer.closeAll();
          window.parent.layer.alert('本期销售额已达起征点，请继续申报。');
          // 调整
          result = -1;
        }

        formData.qcs.initData.zzsxgmsbInitData.gdLock = result;
        formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdLock', result);
        const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
        const { viewEngine } = $('#frmSheet')[0].contentWindow;
        const { body } = $('#frmSheet')[0].contentWindow.document;
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
      },
    });
  } else {
    formData.qcs.initData.zzsxgmsbInitData.gdLock = 0;
    setTimeout(function () {
      formulaEngine.apply('qcs.initData.zzsxgmsbInitData.gdLock', 0);
      const $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
      const { viewEngine } = $('#frmSheet')[0].contentWindow;
      const { body } = $('#frmSheet')[0].contentWindow.document;
      viewEngine.formApply($viewAppElement);
      viewEngine.tipsForVerify(body);
    }, 500);
  }
}
/**
 * 收入支出表_月报（适用科学事业单位会计制度的单位)中的
 *五、本年非财政补助结转结余_本月数应满足公式：四、弥补以前年度经营亏损后的经营结余_本月数0.00＜=0时，五、本年非财政补助结转结余_本月数=2.事业结转结余_本月数
 *四、弥补以前年度经营亏损后的经营结余_本月数0.00大于0时，规则不变。
 *
 * @param mbbys 弥补以前年度经营亏损后的经营结余
 * @param sybys  2.事业结转结余
 * @param zgswskfjdm  主管税务机关代码
 * @returns   本年非财政补助结转结余月
 */
function bMbjyjy(mbbys, sybys, zgswskfjdm) {
  const subzgswjdm = zgswskfjdm.substr(0, 3);
  const Ybys = 0;
  if ((subzgswjdm == '144') | (subzgswjdm == '244')) {
    if (mbbys > 0) {
      return Ybys;
    }
    return sybys;
  }
  return Ybys;
}

/**
 * 消费税,是否存在分配表
 */
function isExistXfsFpb() {
  const sbbfl = formData.xfssbSbbdxxVO.xfssbblxDm;
  let fpb = '';
  switch (sbbfl) {
    case '01':
      fpb = formData.xfssbSbbdxxVO.xfsYlsb.xfssb1_fb6;
      break;
    case '02':
      fpb = formData.xfssbSbbdxxVO.xfsYlpfsb.xfssb2_fb2.fzjgGrid ? formData.xfssbSbbdxxVO.xfsYlpfsb.xfssb2_fb2 : '';
      break;
    case '03':
      fpb = formData.xfssbSbbdxxVO.xfsJlsb.xfssb3_fb6;
      break;
    case '04':
      fpb = formData.xfssbSbbdxxVO.xfsCpylsb.xfssb4_fb14;
      break;
    case '05':
      fpb = formData.xfssbSbbdxxVO.xfsClsb.xfssb5_fb4;
      break;
    case '06':
      fpb = formData.xfssbSbbdxxVO.xfsQtsb.xfssb6_fb6;
      break;
    case '07':
      fpb = formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb5;
      break;
    case '08':
      fpb = formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb5;
      break;
    default:
      return false;
  }
  return fpb !== undefined && fpb !== null && fpb !== '';
}

/**
 * 消费税从分配表获取计税依据
 * @returns {*}
 */
function getFjsjsyjFromXfsFpb() {
  const sbbfl = formData.xfssbSbbdxxVO.xfssbblxDm;
  let fpb = '';
  switch (sbbfl) {
    case '01':
      fpb = formData.xfssbSbbdxxVO.xfsYlsb.xfssb1_fb6;
      break;
    case '02':
      fpb = formData.xfssbSbbdxxVO.xfsYlpfsb.xfssb2_fb2.fzjgGrid ? formData.xfssbSbbdxxVO.xfsYlpfsb.xfssb2_fb2 : '';
      break;
    case '03':
      fpb = formData.xfssbSbbdxxVO.xfsJlsb.xfssb3_fb6;
      break;
    case '04':
      fpb = formData.xfssbSbbdxxVO.xfsCpylsb.xfssb4_fb14;
      break;
    case '05':
      fpb = formData.xfssbSbbdxxVO.xfsClsb.xfssb5_fb4;
      return;
    case '06':
      fpb = formData.xfssbSbbdxxVO.xfsQtsb.xfssb6_fb6;
      break;
    case '07':
      fpb = formData.xfssbSbbdxxVO.xfsDcsb.xfssb7_fb5;
      break;
    case '08':
      fpb = formData.xfssbSbbdxxVO.xfsTlsb.xfssb8_fb5;
      break;
    default:
      return 0;
  }
  if (fpb) {
    return fpb.zjgForm.zjgfpse;
  }
  return 0;
}

const qyzdqtrycjAndQytysbcjBz = {
  qyzdrq: true,
  qytysb: true,
};
/**
 *  “企业重点群体人员采集”、“企业退役士兵采集”采集，使用该方法的业务：
 *  增值税一般纳税人申报、增值税小规模申报、通用申报、定期定额自行申报
 *  cjbz  区分 “企业重点群体人员采集”和“企业退役士兵采集”
 */
function redirectQyzdqtrycjAndQytysbcjPage(cjbz) {
  if (cjbz === 'qyzdrq') {
    pathname = '/sxsq-cjpt-web/biz/sxsq/qyzdqtrycj';
    title = '企业重点群体人员采集';
    successMsg = '维护企业重点群体人员信息成功。';
    swsxDm = 'SXA033200001';
  } else if (cjbz === 'qytysb') {
    pathname = '/sxsq-cjpt-web/biz/sxsq/qytysbcj';
    title = '企业退役士兵采集';
    successMsg = '维护企业退役士兵人员信息成功。';
    swsxDm = 'SXA033100001';
  }
  const { search } = top.window.location;
  const searchList = search.substring(1).split('&');
  let sssqQ = '';
  for (const index in searchList) {
    const value = searchList[index];
    if (value.indexOf('sssqQ') !== -1) {
      const valueList = value.split('=');
      sssqQ = valueList[1];
      break;
    }
  }
  if (sssqQ === '' || sssqQ === null || sssqQ === undefined) {
    let qcs = formData.qcs || formData.dqdeqcs;
    if (qcs === undefined || qcs === null) {
      qcs = formData;
      sssqQ = qcs.fq_.nsrjbxx.tbrq;
    } else {
      sssqQ = qcs.initData.nsrjbxx.tbrq;
    }
  }
  const pathsearch = `?swsxDm=${swsxDm}&gdslxDm=${parent.gdslxDm}&nd=${sssqQ.substring(0, 4)}`;
  const callback = function () {
    const mainUrl = `${window.location.protocol}//${window.location.host}/${window.location.pathname.split('/')[1]}`;
    let qcs = formData.qcs || formData.dqdeqcs;
    let djxh;
    let zgswjDm;
    let nsrsbh;
    if (qcs === undefined || qcs === null) {
      qcs = formData;
      djxh = qcs.fq_.nsrjbxx.djxh;
      zgswjDm = qcs.fq_.nsrjbxx.zgswjDm;
      nsrsbh = qcs.fq_.nsrjbxx.nsrsbh;
    } else {
      djxh = qcs.initData.nsrjbxx.djxh;
      zgswjDm = qcs.initData.nsrjbxx.zgswjDm;
      nsrsbh = qcs.initData.nsrjbxx.nsrsbh;
    }
    const jsonData = {
      djxh,
      swjgDm: zgswjDm,
      nsrsbh,
      sssqQ,
      gdslxDm: parent.gdslxDm,
    };
    $.ajax({
      type: 'POST',
      url: `${mainUrl}/nssb/qyzdqtryAndQytysbCj/geCjxx.do`,
      dataType: 'json',
      contentType: 'application/json',
      data: JSON.stringify(jsonData),
      success(qydata) {
        const { qytysbcj } = qydata.taxML;
        const { qyzdrq } = qydata.taxML;
        let formDataStr = JSON.stringify(formData);
        formDataStr = formDataStr.replace(/"qyzdrq":"(.*?)"/g, `"qyzdrq":"${qyzdrq}"`);
        formDataStr = formDataStr.replace(/"qytysbcj":"(.*?)"/g, `"qytysbcj":"${qytysbcj}"`);
        formData = jQuery.parseJSON(formDataStr);
        let frmSheet = $(window.parent.document).find("iframe[id='frmMain']");
        const { formulaEngine } = frmSheet[0].contentWindow;
        formulaEngine.applyImportFormulas(false);
        frmSheet = $(window.parent.frames.frmMain.document).find("iframe[id='frmSheet']");
        const $viewAppElement = frmSheet.contents().find('#viewCtrlId');
        const { viewEngine } = frmSheet[0].contentWindow;
        const { body } = frmSheet[0].contentWindow.document;
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
      },
    });
  };
  layer.open({
    type: 2,
    title,
    shadeClose: true,
    shade: 0.8,
    maxmin: true,
    area: ['1200px', '75%'],
    btn: ['重置', '提交'],
    success(layero, index) {
      layer.full(index);
      const iframe = $(layero).find('iframe');
      iframe.css('max-height', $(document).height() - 103);
    },
    full() {
      // 找到当前弹出层的iframe元素
      const iframe = $(layero).find('iframe');
      iframe.css('max-height', $(layero).height() - 103);
    },
    restore() {
      // 找到当前弹出层的iframe元素
      const iframe = $(layero).find('iframe');
      iframe.css('max-height', $(layero).height() - 103);
    },
    btn1(index, layero) {
      const iframeWin = window[layero.find('iframe')[0].name];
      iframeWin.frmMain.resetForm();
      qyzdqtrycjAndQytysbcjBz[cjbz] = true;
    },
    btn2(index, layero) {
      if (qyzdqtrycjAndQytysbcjBz[cjbz]) {
        const iframeWin = window[layero.find('iframe')[0].name];
        const bool = iframeWin.frmMain.submitFormData();
        if (bool) {
          qyzdqtrycjAndQytysbcjBz[cjbz] = false;
          // layer.msg(successMsg);
          // 这里关闭弹框
          // layer.close(index);
          // callback();
        }
      } else {
        layer.alert('不能重复提交！', { icon: 2 });
      }
      return false;
    },
    content: pathname + pathsearch,
    cancel: callback,
  });
}

/**
 *  “企业重点群体人员采集”、“企业退役士兵采集”采集，使用该方法的业务：
 *  增值税一般纳税人申报、增值税小规模申报、通用申报、定期定额自行申报
 *  cjbz  区分 “企业重点群体人员采集”和“企业退役士兵采集”
 */
function redirectQyzdqtrycjAndQytysbcjPageByYwzt(cjbz) {
  if (cjbz === 'qyzdrq') {
    pathname = '/sxsq-cjpt-web/biz/sxsq/qyzdqtrycj';
    title = '企业重点群体人员采集';
    successMsg = '维护企业重点群体人员信息成功。';
    swsxDm = 'SXA033200001';
  } else if (cjbz === 'qytysb') {
    pathname = '/sxsq-cjpt-web/biz/sxsq/qytysbcj';
    title = '企业退役士兵采集';
    successMsg = '维护企业退役士兵人员信息成功。';
    swsxDm = 'SXA033100001';
  }
  const search = parent.queryString;
  const searchList = search.substring(1).split('&');
  let sssqQ = '';
  for (const index in searchList) {
    const value = searchList[index];
    if (value.indexOf('sssqQ') !== -1) {
      const valueList = value.split('=');
      sssqQ = valueList[1];
      break;
    }
  }
  const pathsearch = `?swsxDm=${swsxDm}&gdslxDm=${parent.gdslxDm}&nd=${sssqQ.substring(0, 4)}`;
  const callback = function () {
    const mainUrl = `${window.location.protocol}//${window.location.host}/${window.location.pathname.split('/')[1]}`;
    const qcs = formData.fq_;
    const { djxh } = qcs.nsrjbxx;
    const { zgswjDm } = qcs.nsrjbxx;
    const { nsrsbh } = qcs.nsrjbxx;
    const jsonData = {
      djxh,
      swjgDm: zgswjDm,
      nsrsbh,
      sssqQ,
      gdslxDm: parent.gdslxDm,
      sid: 'dzswj.ywzz.sb.common.qyzdrqAndqytysbCj',
      noCache: 'Y',
      _random: Math.random(),
    };
    $.ajax({
      type: 'POST',
      url: `${mainUrl}/ywzt/getData.do`,
      async: true,
      data: jsonData,
      success(qydata) {
        const data = parent.resolveYwztResponse(qydata);
        const { qytysbcj } = data;
        const { qyzdrq } = data;
        let formDataStr = JSON.stringify(formData);
        formDataStr = formDataStr.replace(/"qyzdrq":"(.*?)"/g, `"qyzdrq":"${qyzdrq}"`);
        formDataStr = formDataStr.replace(/"qytysbcj":"(.*?)"/g, `"qytysbcj":"${qytysbcj}"`);
        formData = jQuery.parseJSON(formDataStr);
        let frmSheet = $(window.parent.document).find("iframe[id='frmMain']");
        const { formulaEngine } = frmSheet[0].contentWindow;
        formulaEngine.applyImportFormulas(false);
        frmSheet = $(window.parent.frames.frmMain.document).find("iframe[id='frmSheet']");
        const $viewAppElement = frmSheet.contents().find('#viewCtrlId');
        const { viewEngine } = frmSheet[0].contentWindow;
        const { body } = frmSheet[0].contentWindow.document;
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
      },
    });
  };
  layer.open({
    type: 2,
    title,
    shadeClose: true,
    shade: 0.8,
    maxmin: true,
    area: ['1200px', '75%'],
    btn: ['重置', '提交'],
    success(layero, index) {
      layer.full(index);
    },
    btn1(index, layero) {
      const iframeWin = window[layero.find('iframe')[0].name];
      iframeWin.frmMain.resetForm();
      qyzdqtrycjAndQytysbcjBz[cjbz] = true;
    },
    btn2(index, layero) {
      if (qyzdqtrycjAndQytysbcjBz[cjbz]) {
        const iframeWin = window[layero.find('iframe')[0].name];
        const bool = iframeWin.frmMain.submitFormData();
        if (bool) {
          qyzdqtrycjAndQytysbcjBz[cjbz] = false;
          // layer.msg(successMsg);
          // 这里关闭弹框
          // layer.close(index);
          // callback();
        }
      } else {
        layer.alert('不能重复提交！', { icon: 2 });
      }
      return false;
    },
    content: pathname + pathsearch,
    cancel: callback,
  });
}

function redirectQytysbcjPage() {}

function setKdqjyDlJbrzyzjhm(bz, zjhm) {
  const { smzbz } = formData.fq_.nsrjbxx.smzxx;
  const { zjlx } = formData.fq_.nsrjbxx.smzxx;
  const smzjhm = formData.fq_.nsrjbxx.smzxx.zjhm;
  const tjz = formData.ht_.qysdskdqhznsfzjgnbywbw.qysdsyjdyjnssbbal.nsrqtxxForm.jbrzyzjhm;

  if (bz === 'xsbz') {
    if (smzbz == 'Y') {
      if (zjlx == '201') {
        return `${zjhm.substring(0, 6)}********${zjhm.substring(14)}`;
      }
      return zjhm;
    }
    return tjz;
  }
  if (smzbz == 'Y') {
    return smzjhm;
  }

  return zjhm;
}

function setKdqjyJbr(smzbz, smzxm) {
  const { jbr } = formData.ht_.qysdskdqhznsfzjgnbywbw.qysdsyjdyjnssbbal.nsrqtxxForm;
  if (smzbz === 'Y') {
    return smzxm;
  }
  return jbr;
}

function setKdqjyBlrysfzjlxDm(smzbz, zjlx) {
  const { blrysfzjlxDm } = formData.ht_.qysdskdqhznsfzjgnbywbw.qysdsyjdyjnssbbal.nsrqtxxForm;
  if (smzbz === 'Y') {
    return zjlx;
  }
  return blrysfzjlxDm;
}

// 附加税季中转公式06100120010100062
function calPhjmse(zspmDm, changed) {
  let bqsfsyxgmyhzc;
  let sbxxGridlbVOArr;
  let yywxsphjm = '';
  let ywztBz_ = false;
  if (typeof formulaEngine.otherParams.ywzt !== 'undefined' && formulaEngine.otherParams.ywzt === 'Y') {
    ywztBz_ = true;
  }
  if (ywztBz_) {
    if (parent.location.href.indexOf('lhsbywbm=fjssb') > -1) {
      bqsfsyxgmyhzc = formData.ht_fs_.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.bqsfsyxgmyhzc;
      sbxxGridlbVOArr = formData.ht_fs_.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
      yywxsphjm = formData.ht_fs_.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.yywxsphjm;
    } else {
      bqsfsyxgmyhzc = formData.ht_.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.bqsfsyxgmyhzc;
      sbxxGridlbVOArr = formData.ht_.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
      yywxsphjm = formData.ht_.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.yywxsphjm;
    }
  } else {
    bqsfsyxgmyhzc = formData.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.bqsfsyxgmyhzc;
    sbxxGridlbVOArr = formData.fjsSbbdxxVO.fjssbb.sbxxGrid.sbxxGridlbVO;
    yywxsphjm = formData.fjsSbbdxxVO.fjssbb.fjsnsrxxForm.yywxsphjm;
  }

  let index = 0;
  for (let i = 0; i < sbxxGridlbVOArr.length; i++) {
    if (zspmDm == sbxxGridlbVOArr[i].zspmDm) {
      index = i;
      break;
    }
  }
  const { bqynsfe } = sbxxGridlbVOArr[index];
  const afterCalPhjmse = ROUND((bqynsfe - sbxxGridlbVOArr[index].jme) * sbxxGridlbVOArr[index].phjzbl, 2);
  if (bqsfsyxgmyhzc == 'Y' && (bqynsfe > 0 || (bqynsfe < 0 && yywxsphjm == 'Y'))) {
    if (ywztBz_) {
      if (sbxxGridlbVOArr[index].t_jmxzChanged == 'Y') {
        return afterCalPhjmse;
      }
    } else if (sbxxGridlbVOArr[index].jmxzChanged == 'Y') {
      sbxxGridlbVOArr[index].jmxzChanged = '';
      return afterCalPhjmse;
    }
    // phjmse必定数字,增加是否为数字的校验;4.0计税依据联动修改问题优化后,新增标志t_phjmseBz
    if (
      sbxxGridlbVOArr[index].phjmse === 0 ||
      sbxxGridlbVOArr[index].phjmse === '0' ||
      isEmptyObject(sbxxGridlbVOArr[index].phjmse) ||
      typeof sbxxGridlbVOArr[index].phjmse === 'number' ||
      !isEmptyObject(changed) ||
      sbxxGridlbVOArr[index].t_phjmseBz !== 'Y'
    ) {
      return afterCalPhjmse;
    }
    return sbxxGridlbVOArr[index].phjmse;
  }
  return 0;
}
/**
 * 将"YYYY-MM-DD"格式转换为"XX年XX月XX日"
 * @returns
 */
function slrqFormat() {
  let { slrq } = formData.ht_.kjqysdssbvo.slxxForm;
  slrq = `${slrq.substring(0, slrq.indexOf('-'))}年${slrq.substring(
    slrq.indexOf('-') + 1,
    slrq.lastIndexOf('-'),
  )}月${slrq.substring(slrq.lastIndexOf('-') + 1)}日`;
  return slrq;
}

/**
 * 报送资料调整。“申报享受税收减免”增加了以下事项：无偿援助项目免征增值税、钻石交易免征增值税、滴灌带和滴灌管产品免征增值税、黄金期货交易免征增值税、有机肥免征增值税、上海期货保税交割免征增值税、熊猫普制金币免征增值税、原油和铁矿石期货保税交割业务免征增值税。办理上述事项时需上传相应电子资料并归档。
 * @param jmxzDm 减免性质代码
 * @returns boolean
 */
function bszlcl(hmc) {
  const jmsx = {
    '01081503': '上海期货保税交割免征增值税',
    '01083907': '熊猫普制金币免征增值税',
    '01081506': '原油和铁矿石期货保税交割业务免征增值税',
    '01124302': '无偿援助项目免征增值税',
    '01081505': '钻石交易免征增值税',
    '01081502': '黄金期货交易免征增值税',
    '01092203': '有机肥免征增值税',
  };
  // 附列资料代码，必报标志--Y为必报，N为条件报送，附列资料名称
  const flzl = {
    '0001124302': [
      { flzlDm: '003360', bbBz: 'Y', flzlmc: '《外国政府和国际组织无偿援助项目在华采购货物明细表》' },
      { flzlDm: '003361', bbBz: 'Y', flzlmc: '销售合同复印件' },
      { flzlDm: '003362', bbBz: 'Y', flzlmc: '对外贸易经济合作部出具的证明材料' },
      { flzlDm: '003363', bbBz: 'N', flzlmc: '委托协议' },
    ],
    '0001081505': [{ flzlDm: '000957', bbBz: 'N', flzlmc: '上海钻石交易所会员资格证明复印件' }],
    '0001081502': [{ flzlDm: '001513', bbBz: 'Y', flzlmc: '黄金期货交易资格证明材料' }],
    '0001092203': [
      { flzlDm: '000791', bbBz: 'N', flzlmc: '肥料登记证复印件' },
      { flzlDm: '000187', bbBz: 'N', flzlmc: '有机肥产品质量技术检测合格报告' },
      { flzlDm: '003364', bbBz: 'N', flzlmc: '省级农业行政主管部门办理备案的证明复印件' },
      { flzlDm: '000958', bbBz: 'N', flzlmc: '生产企业的肥料登记证复印件' },
      { flzlDm: '001159', bbBz: 'N', flzlmc: '生产企业提供的产品质量技术检验合格报告' },
      { flzlDm: '003364', bbBz: 'N', flzlmc: '省级农业行政主管部门办理备案的证明复印件' },
    ],
    '0001081503': [
      { flzlDm: '001688', bbBz: 'Y', flzlmc: '当期期货保税交割的书面说明' },
      { flzlDm: '003365', bbBz: 'Y', flzlmc: '上海期货交易所交割单、保税仓单等资料' },
    ],
    '0001083907': [
      { flzlDm: '001154', bbBz: 'N', flzlmc: '“中国熊猫普制金币授权经销商”相关资格证书复印件' },
      { flzlDm: '000550', bbBz: 'N', flzlmc: '《中国熊猫普制金币经销协议》复印件' },
      { flzlDm: '003366', bbBz: 'N', flzlmc: '中国银行业监督管理委员会批准其开办个人黄金买卖业务的相关批件材料复印件' },
    ],
    '0001081506': [
      { flzlDm: '001688', bbBz: 'Y', flzlmc: '当期期货保税交割的书面说明' },
      {
        flzlDm: '001551',
        bbBz: 'Y',
        flzlmc: '上海国际能源交易中心股份有限公司或大连商品交易所的交割结算单、保税仓单等资料',
      },
    ],
  };
  if (hmc === null || hmc === '' || hmc === 'MS__' || typeof hmc === 'object') {
    return true;
  }
  const jmxzDm = hmc.split('_')[1];
  if (!flzl.hasOwnProperty(jmxzDm)) {
    return true;
  }

  if ($.isEmptyObject(otherParams.flzlState)) {
    return false;
  }
  const flzlArr = flzl[jmxzDm];
  let tjbs = false; // 条件报送
  let bb = false; // 必报
  const tjbsIndex = 0;
  for (let i = 0; i < flzlArr.length; i++) {
    const zl = flzlArr[i];
    if (zl.bbBz === 'Y') {
      bb = true;
    } else {
      tjbs = true;
    }
  }
  for (let i = 0; i < flzlArr.length; i++) {
    const zl = flzlArr[i];
    if (!bb && tjbs) {
      // 全部为条件报送，有个为Y就可。
      if (otherParams.flzlState[zl.flzlDm] === 'Y') {
        return true;
      }
    } else {
      // 必报的数据缺失，直接提示错误
      if (
        zl.bbBz === 'Y' &&
        (otherParams.flzlState[zl.flzlDm] === 'N' ||
          otherParams.flzlState[zl.flzlDm] === null ||
          otherParams.flzlState[zl.flzlDm] === '')
      ) {
        return false;
      }
    }
  }
  if (!bb && tjbs) {
    return false;
  }
  return true;
}

/**
 * 减税降费监控，返回弹框结果
 * @param isSecondCall
 */
function jsjfMonitor(isSecondCall) {
  if (isJmsb()) {
    return false;
  }
  // 业务需覆盖实现的方法
  let jmxzList = '';
  if (typeof getJmxzList === 'function') {
    jmxzList = getJmxzList();
  }
  // 减税降费监控提示
  if (
    otherParams.jsjfMonitorSwitch !== undefined &&
    otherParams.jsjfMonitorSwitch === 'Y' &&
    jmxzList !== undefined &&
    jmxzList.length > 0
  ) {
    const jsjfReq = {};
    jsjfReq.jsjfQueries = jmxzList;
    // 发送ajax请求获取减税降费审核指标
    const jsjfRes = getJsjfIndicators(JSON.stringify(jsjfReq));
    if (jsjfRes !== undefined && jsjfRes !== '') {
      // 弹框提示语
      confirm_submit = getJsjfTips(jsjfRes);
      // 弹框
      layer.confirm(
        confirm_submit,
        {
          icon: -1,
          title: '提示',
          btn: ['申报准确', '需要更正'],
          area: ['720px'],
          btnAlign: 'c',
          btn2(index) {
            $('body').unmask();
            if (typeof umMaskZdy === 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            layer.close(index);
          },
          cancel(index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy === 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            layer.close(index);
          },
        },
        function (index) {
          if (typeof parent.makeTypeDefualt !== 'undefined' && parent.makeTypeDefualt === 'HTML') {
            maskZdy('正在提交，请稍候...');
          } else {
            maskZdy('正在提交，请稍候...');
          }

          /**
           * 申报提交时框架校验成功后调用业务特有校验（一般在ywbm.js中实现）
           * 然后在业务特有校验中调用doBeforSubmitForm，在提交表单之前做一些事情。如弹出申报前的确认提示
           * 最后由doBeforSubmitForm调用submitForm实现申报提交
           */
          doAfterVerify(doBeforSubmitForm, submitForm, isSecondCall);

          layer.close(index);
        },
      );
      return true;
    }
  }
  return false;
}

/**
 * 发送ajax请求获取减税降费审核指标
 * @param jsjfReq
 * @returns {string|*}
 */
function getJsjfIndicators(jsjfReq) {
  let jsjfRes;
  if (jsjfReq === undefined || jsjfReq === '') {
    return '';
  }
  const paramJson = {};
  paramJson.sid = 'com.foresee.dzswj.api.sb.service.IJsjfService';
  paramJson.sidType = '02';
  paramJson.invokeMethod = 'getReviewIndicators';
  paramJson.djxh = $('#djxh').val();
  paramJson.nsrsbh = $('#nsrsbh').val();
  paramJson.projectName = 'sbzx';
  paramJson.qqbw = jsjfReq;
  parent.requestYwztData(
    paramJson,
    function (data) {
      jsjfRes = data;
    },
    function (response) {
      parent.layer.alert(`调用服务失败，失败原因：${response}`);
      jsjfRes = '';
    },
  );
  return jsjfRes;
}

/**
 * 根据减税降费审核指标拼凑提示信息
 * @param jsjfRes
 */
function getJsjfTips(jsjfRes) {
  let tipsMsg = '';
  const sameNsrFlag = isSameNsrmc(jsjfRes);
  if (sameNsrFlag) {
    tipsMsg += `纳税人名称：【${jsjfRes[0].nsrmc}】<br/>`;
  }
  for (let i = 0; i < jsjfRes.length; i++) {
    // 纳税人名称
    const { nsrmc } = jsjfRes[i];
    if (!sameNsrFlag) {
      tipsMsg += `纳税人名称：【${nsrmc}】<br/>`;
    }
    // 减免项目名称
    const { jmxmmc } = jsjfRes[i];
    // 税收减免性质代码
    const { ssjmxzDm } = jsjfRes[i];
    // 省级行政区划是否显示
    const { sjXzqhShow } = jsjfRes[i];
    // 行政区划名称
    const { xzqhMc } = jsjfRes[i];
    // 省级行业是否显示
    const { sjHyShow } = jsjfRes[i];
    // 行业名称
    const { hyMc } = jsjfRes[i];
    // 省级登记注册类型是否显示
    const { sjDjzclxShow } = jsjfRes[i];
    // 登记注册类型名称
    const { djzclxMc } = jsjfRes[i];
    // 特定纳税人-相等
    const { tdnsrmcEq } = jsjfRes[i];
    // 特定纳税人-相等 是否显示
    const { tdnsrmcEqShow } = jsjfRes[i];
    // 特定纳税人-包含
    const { tdnsrmcLk } = jsjfRes[i];
    // 特定纳税人-包含 是否显示
    const { tdnsrmcLkShow } = jsjfRes[i];

    const rowMsg = `<p style="text-indent:2em;">【${jmxmmc}】（减免性质代码【${ssjmxzDm}】）政策一般适用`;
    if (sjXzqhShow === true && xzqhMc !== undefined && xzqhMc !== '') {
      tipsMsg += `${rowMsg}【行政区划：${xzqhMc}】</p>`;
    }
    if (sjHyShow === true && hyMc !== undefined && hyMc !== '') {
      tipsMsg += `${rowMsg}【行业名称：${hyMc}】</p>`;
    }
    if (sjDjzclxShow === true && djzclxMc !== undefined && djzclxMc !== '') {
      tipsMsg += `${rowMsg}【登记注册类型：${djzclxMc}】</p>`;
    }
    if (tdnsrmcEqShow === true && tdnsrmcEq !== undefined && tdnsrmcEq !== '') {
      tipsMsg += `${rowMsg}【纳税人名称：${tdnsrmcEq}】</p>`;
    }
    if (tdnsrmcLkShow === true && tdnsrmcLk !== undefined && tdnsrmcLk !== '') {
      tipsMsg += `${rowMsg}【纳税人名称包含：${tdnsrmcLk}】</p>`;
    }
  }
  tipsMsg += '<p style="text-indent:2em;">请确认申报是否准确？</p>';
  return tipsMsg;
}

/**
 * 纳税人名称是否相同
 * @param jsjfRes
 */
function isSameNsrmc(jsjfRes) {
  let flag = true;
  const { nsrmc } = jsjfRes[0];
  for (let i = 1; i < jsjfRes.length; i++) {
    if (nsrmc !== jsjfRes[i].nsrmc) {
      flag = false;
    }
  }
  return flag;
}

// 成品油代扣代缴校验信息查询公共服务
function dkdjBdxxCxCommon(reqParams) {
  let result;
  reqParams.sid = 'com.foresee.dzswj.api.sb.service.IDkdjSkrkAndSbxxCxSvc';
  reqParams.sidType = '02';
  reqParams.invokeMethod = 'dkdjSkrkAndSbxxCx';
  reqParams.projectName = 'sbzx-cjpt-web';
  parent.requestYwztData(
    reqParams,
    function (data) {
      if (typeof data === 'string') {
        data = JSON.parse(data);
      }
      result = data;
    },
    function (response) {
      parent.layer.alert(`调用服务失败，失败原因：${response}`);
      result = '';
    },
    '',
    'sbzx-cjpt-web',
  );
  return result;
}

export default {
  delBranch,
  delBranch2,
  delBranch3,
  xgmCezsqzd,
  lhbsfssb_jmse,
  lhbsfssb_ssjmxzDm,
  tlxfs_dktz_qckcje,
  tlxfs_dktz_kc,
  dcxfs_dktz_qckcje,
  dcxfs_dktz_kc,
  qtxfs_xfpmc_different,
  jmsemxb_different,
  jsbcpy_mxb_samebqjmse101020603,
  jsbcpy_mxb_samebqjmse101020604,
  jsbcpy_mxb_samebqjmse101020606,
  jsbcpy_mxb_samebqjmse101020607,
  jsbcpy_mxb_samebqjmse101020608,
  jsbcpy_mxb_samebqjmse101020605,
  jsbcpy_mxb_samebqjmse101020609,
  jmsemxb_compare_ynse,
  jmsemxb_count_bqjmse,
  jmsemxb_get_ynse,
  dcjmsemxb_get_ynse,
  jmsemxb_get_bqjmse,
  jmsesbmxb_get_qcs_qmye,
  calculateNsqxDm,
  changeJmsxDm,
  yfzcfzzhzbGridlb_yfcg,
  yfzcfzzhzbGridlb_yfcgzsh,
  cwbbyfzc_f043,
  cwbbyfzc_f046,
  cwbbyfzc_f047,
  zzsyjsb_yzxm,
  indexOf,
  checkXxse,
  isEmptyObject,
  gzsbSetValue,
  qysdsa17nd_fhbxzctzdynstzmxbGridlb_qsind,
  qysdsa17nd_A000000_jbrVaild,
  qysdsa17nd_A109000_lastSeasonYjbData,
  getDateDiff,
  hasNotZeroData,
  dataEquals,
  sfjmba,
  isNullForStr,
  GET_PARAM,
  getIndexFromArray,
  pushIntoArray,
  getItemByPath,
  checkhasNum,
};
