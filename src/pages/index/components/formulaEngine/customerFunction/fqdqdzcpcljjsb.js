import store from '@/pages/index/store'
const { dzbdbmList, sbzxCtl } = store.state['sb/fqdqdzcpcljjsb/form']

// 校验逾期申报方法
function initVaild(sfjyyqhtqsb){
	return;
	let reqParams = {};
	reqParams.ZsxmDm = '30175';
	reqParams.Ywbm = 'fqdqdzcpcljjsb';
	reqParams.SssqQ = formData.fq_.fqdqdzcpcljjsbInitData.sssqq;
	reqParams.SssqZ = formData.fq_.fqdqdzcpcljjsbInitData.sssqz;
	reqParams.NsqxDm = '06';
	reqParams.SbqxDm = '04';
	reqParams.Yqsbbz = '';

	if(sfjyyqhtqsb !== "false" && reqParams.yqsbbz !== "Y") {
		$vue.$fetch({url: sbzxCtl + '/fsgjssb/sbqxjy/v1/yqsbJy', method: 'post', data: reqParams})
			.then((res) => {
				let data = res.body;
				let sfkyqsbbz = data.sfkyqsbbz;
				let wfurlList = data.wfurlList;
				let msg = data.msg;
				let yqtsmsg = data.yqtsmsg;
				if (sfkyqsbbz === 'Y') {
					if (yqtsmsg && yqtsmsg !== '') {
						if (msg && msg !== '') {
							$vue.$gtDialog.confirm({
								header:'提示',
								theme: 'info',
								body: yqtsmsg,
								closeOnOverlayClick: false,
								closeOnEscKeydown: false,
								closeBtn: false,
								cancelBtn: null,
								confirmBtn: '继续申报',
								onConfirm: function() {
									$vue.$gtDialog.confirm({
										header:'提示',
										theme: 'info',
										body: msg,
										closeOnOverlayClick: false,
										closeOnEscKeydown: false,
										closeBtn: false,
										cancelBtn: null,
										confirmBtn: '确定'
									});
								}});
						} else {
							$vue.$gtDialog.confirm({
								header:'提示',
								theme: 'info',
								body: yqtsmsg,
								closeOnOverlayClick: false,
								closeOnEscKeydown: false,
								closeBtn: false,
								cancelBtn: null,
								confirmBtn: '继续申报'
							});
						}
					} else {
						$vue.$gtDialog.confirm({
							header:'提示',
							theme: 'info',
							body: msg,
							closeOnOverlayClick: false,
							closeOnEscKeydown: false,
							closeBtn: false,
							cancelBtn: null,
							confirmBtn: '确定'
						});
					}
				} else {
					$vue.$gtDialog.confirm({
						header:'提示',
						theme: 'info',
						body: msg,
						confirmBtn: '去办理',
						closeOnOverlayClick: false,
						closeOnEscKeydown: false,
						onCancel: function() {
							changeUrlToWFUrl(wfurlList);
						}, onConfirm: function() {
							changeUrlToWFUrl(wfurlList);
						}}, {type: 'confirm'});
				}
			});
	}
}

/*
 * 跳转显示违法处罚信息的页面,没有关闭页面
 */
function changeUrlToWFUrl(wfurlList){
	if(wfurlList && wfurlList.length!=0){
		var gnurl = wfurlList[0].gnurl;
		var url = parent.location.protocol + "//" + parent.location.host + gnurl;
		window.top.location.href = url;
	} else {
		closeWindow();
	}
}

/*
 * 关闭页面
 */
function closeWindow(){
    // 重定向到门户页面
    window.top.location.href = window.top.location.origin + window.top.STATIC_ENV_CONFIG.VUE_APP_HOME_URL;
}

export default {
	initVaild
}