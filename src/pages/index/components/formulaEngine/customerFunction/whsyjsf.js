import pdf_pzzldm from '/public/static/sb/whsyjsf/form/nf_dm_sb_pzzldm.json';

/**
 * 主税（增值税）未申报提示，贵州个性化需求(阻断性关闭)
 */
function zswsbTips() {
    if (formData.hq_.zsSfysb != 'Y') {  // zsSfysb为主税是否已申报的标志
        parent.layer.confirm('主税（增值税）未申报',{
            type: 1,
            title:'提示',
            closeBtn: false,
            icon:2,
            area:['448px','220px'],
            btn : ['确定']
        },function(index){
            if (navigator.userAgent.indexOf("MSIE") > 0) {
                if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
                    window.opener = null;
                    window.close();
                } else {
                    window.open('', '_top');
                    window.top.close();
                }
            } else if (navigator.userAgent.indexOf("Firefox") > 0) {
                window.location.href = 'about:blank ';
                window.close();
            } else if (navigator.userAgent.indexOf("Chrome") > 0 || (isIE() && navigator.userAgent.indexOf("Trident") > 0)) {
                top.open('about:blank ', '_self').close();
            }else {
                window.open('', '_top');
                window.top.close();
            }
        });
    }
}

/**
 * 主税（增值税）未申报提示(提示性关闭)
 */

function zswsbTips2() {
    if (formData.hq_.zsSfysb != 'Y') { // zsSfysb为主税是否已申报的标志
        layer.alert('主税（增值税）未申报', {
            title:'提示',
            icon:0,
            closeBtn: false,
            area:['400px','220px'],
            btn : ['确定']
        })
    }
}

function verifyYzsrbysAndMzsrbys(yzsrbys,mzsrbys) {
    if (yzsrbys > 0 && mzsrbys > 0){
        parent.layer.confirm('第1栏[应征收入本月（期）数]与第2栏[免征收入本月（期）数]不能同时填写大于0的数据，请修改。',{
            title:'提示',
            icon:2,
            closeBtn: false,
            area:['400px','220px'],
            btn : ['确定']
        },function(index){
            parent.layer.close(index);
        });
        return false;
    }
    return true;
}

//*********-484_青岛个性化提示
function jzxxTip() {
	 var tips = "自2019年7月1日至2024年12月31日，在山东省范围内，文化事业建设费按照缴纳义务人应缴费额的50%减征";
	 layer.open({
		  type: 1,
		  title: '提示',
		  btn:['确定'],
		  closeBtn:false,
		  icon:0,
		  content: tips
	}); 
}

function importPreCheck(){
    if(formData.hq_.ggysfyxcekc == "Y"){
        return true;
    }
    var gridlb = formData.hq_.sbxxGrid.sbxxGridlb;
    for(var i = 0; i < gridlb.length;i++){
        if(gridlb[i].zspmDm == "302170100"){
            return true;
        }
    }
    var $txbz = formData.hq_.ggysfyxcekc;
    var $rdbz=formData.fq_.ylyBz;
    var $swjg = formData.fq_.nsrjbxx.swjgDm.substring(0,5);
    var $swjg3 = formData.fq_.nsrjbxx.swjgDm.substring(0,3);
    return $txbz=='Y' ||
        ($txbz == '' && ( $swjg == '12102' || ( $swjg3=='137' && $swjg != '13702')) ) ||
        ($rdbz == 'Y' && $swjg != '12102');
    //return false;
}
/**
 * 导入excel
 */
function importExcel() {
    if(importPreCheck()){
        pop09(pop09CallBack);
    }else{
        layer.alert("您无需填写《应税服务减除项目清单 》。");
    }
}

var popIndex = 0;
function pop09(callBack){
    var sbywbm = $("#ywbm").val();
    if($('#importBtn').attr('upload-flag')==undefined){
        $('#importBtn').attr('upload-flag',true);//防止重复初始化上传控件
        var link = location.origin+(cp.indexOf("/")==0?"":"/")+cp+'/ywzt/importExcelFile.do?ywbm='+sbywbm;
        if (location.href.indexOf("test=true") > -1) {
            link += "&djxh=" + $("#djxh").val() + "&nsrsbh=" + $("#nsrsbh").val()
                + "&test=true";
        }
        $("#modelDownload").attr("href",location.origin+(cp.indexOf("/")==0?"":"/")+cp+'/biz/sb/whsyjsf_ygz/form/TAX_SB_204_WHSYJSFSBYGZ_V1.0.xls')
            .attr("download","TAX_SB_204_WHSYJSFSBYGZ_V1.0.xls").show();
        var UPLOAD_FILES = {};
        upload.render({
            elem: '#selectFile'
            ,url: link
            ,auto: false
            ,bindAction: '#importBtn'
            ,exts:'xls'
            ,accept:'file'
            ,field:'xls'
            ,before: function(obj){
                popIndex = layer.load("数据上传中：");
            }, choose: function (obj) {
                //选择文件后触发
                UPLOAD_FILES = obj.pushFile();
                clearFile(UPLOAD_FILES);
                obj.preview(function(index, file, result){
                    $("#selectedFileName").val(file.name);
                    obj.pushFile(UPLOAD_FILES);
                });
            }
            ,done: function(importData){
                if(typeof callBack =='function'){
                    callBack(importData,popIndex);
                }
                clearFile(UPLOAD_FILES);
                $("#selectedFileName").val("");
            }
            ,error: function(){
                layer.close(popIndex);
                layer.alert("文件导入异常！");
            }
        });
    }

    layer.open({
        type: 1
        ,area: ['600px']
        ,title:['Excel数据导入']
        ,scrollbar: false
        ,content: $("#pop-09")
        ,btn:[]
        ,success:function(){
            //$("#selectedFileName").val("");
            $("#pop-09").css("height","150px");
        },cancel:function(){
            $("#pop-09").css("height","190px");
        },yes:function(){
            //$("#pop-09").css("height","120px");
            return false;
        },btn2:function(){
            if($("#selectedFileName").val() == "" || $("#selectedFileName").val() == "请选择文件！"){
                $("#selectedFileName").val("请选择文件！");
                return false;
            }
            $("#pop-09").css("height","190px");
        },btn3:function(){
            $("#pop-09").css("height","190px");
        }
    });
}


//清空文件队列
function clearFile(UPLOAD_FILES) {
    for (var x in UPLOAD_FILES) {
        delete UPLOAD_FILES[x];
    }
}



var pop09CallBack = function(importData,index){
    if (importData!=null && importData!="" && typeof importData == "string") {
        importData = JSON.parse(importData);
    }

    if(importData.rtnCode == "999"){
        layer.alert("文件导入失败！"+importData.errInfo.msg);
    }else if (importData.rtnCode == "000") {
        var _wbcsh_in_formData = JSON.parse(importData.body);
        var _wbcsh_in_dom = $("#wbcsh").val();
        // console.log("合并前_wbcsh_in_formData:");
        // console.dir(_wbcsh_in_formData);
        if (_wbcsh_in_dom == "" || _wbcsh_in_dom == "null") {

        } else {
            /* 合并初始化数据 */
            _wbcsh_in_dom = JSON.parse(Base64.decode(_wbcsh_in_dom));
            _wbcsh_in_formData = $.extend(true, _wbcsh_in_dom, _wbcsh_in_formData);
        }
        var wbcsh = Base64.encode(JSON.stringify(_wbcsh_in_formData));
        //$("#wbcsh").val(Base64.encode(JSON.stringify(_wbcsh_in_formData)));
        // console.log("合并后_wbcsh_in_formData:");
        // console.dir(_wbcsh_in_formData);
        // delete formData.wbcshInit;
        var $viewAppElement = $("#frmSheet").contents().find("#viewCtrlId");
        var viewEngine = $("#frmSheet")[0].contentWindow.viewEngine;
        var body = $("#frmSheet")[0].contentWindow.document.body;
        formulaEngine.executeWbcshFormulaByParam(wbcsh);
        formulaEngine.applyAssociatedFormulaVerify(null);
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
    }else{

    }
    layer.close(popIndex);
}

/**
 *
 * @param zsSfysb
 * @param zzsybnsr
 * @param yzsrbys
 * @param mzsrbys
 * @param zzsYnsjye
 * @param xgmQzd
 * @param fpxse
 */
function getYzsrbys(zsSfysb, zzsybnsr, yzsrbys, mzsrbys, zzsYnsjye, xgmQzd,fpxse) {
    var formDataVO = getFormDataVO();
    if (formDataVO.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.sbbhead.sbsxDm1 === '21') {
        return 0;
    }
    if (zsSfysb === 'Y') {
        if (zzsybnsr === 'Y') {
            if(fpxse !== ""){
                return ROUND(fpxse, 2);
            } else {
                return ROUND(yzsrbys, 2);
            }
        } else {
            if (zzsYnsjye > xgmQzd) {
                if(fpxse !== ""){
                    return ROUND(fpxse, 2);
                }else {
                    return ROUND(yzsrbys, 2);
                }
            } else {
                return 0;
            }
        }
    } else {
        if (zzsybnsr === 'Y' && fpxse !== "") {
            return ROUND(fpxse, 2);
        } else if (zzsybnsr !== 'Y' && fpxse !== "") {
            if (formDataVO.fq_.zzsBhsXse > xgmQzd) {
                return ROUND(fpxse, 2);
            }
        }
        return 0;
    }
}

/**
 *
 * @param zsSfysb
 * @param zzsybnsr
 * @param yzsrbys
 * @param mzsrbys
 * @param zzsYnsjye
 * @param xgmQzd
 * @param fpxse
 */
function getMzsrbys(zsSfysb, zzsybnsr, yzsrbys, mzsrbys, zzsYnsjye, xgmQzd,fpxse) {
    var formDataVO = getFormDataVO();
    if (formDataVO.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.sbbhead.sbsxDm1 === '21') {
        return 0;
    }
    if (zsSfysb === 'Y') {
        if (zzsybnsr === 'Y') {
            return 0;
        } else {
            if (zzsYnsjye > xgmQzd) {
                return 0;
            } else {
                if(fpxse !== ""){
                    return ROUND(fpxse, 2);
                }else{
                    return ROUND(yzsrbys + mzsrbys, 2);
                }
            }
        }
    } else {
        if (zzsybnsr !== 'Y' && fpxse !== "") {
            if (formDataVO.fq_.zzsBhsXse <= xgmQzd) {
                return ROUND(fpxse, 2);
            }
        }
        return 0;
    }
}

/**
 * 适用于无需考虑广告业或娱乐业的判断
 * 避免单独广告业时，formData.hq_节点不存在而导致js报错。
 * 处理思路： 不存在娱乐业时，使用formData.ggy 取值
 * @returns {*}
 */
function getFormDataVO(){
    var formDataVO = formData;
    if(window.location.href.indexOf("302170100") == -1){
        formDataVO = formData.ggy;
    }
    return formDataVO;
}

// 将应补退税额同步给vue外壳
function postYbtse() {
    var zspmDmList = $("#zspmDmList").val();
    var ggyYbtse = 0;
    if (zspmDmList.indexOf("302170200") > -1) {
        ggyYbtse = formData.ggy.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.SbsjxxForm.bqybtfe;
    }
    var ylyYbtse = 0;
    if (zspmDmList.indexOf("302170100") > -1) {
        ylyYbtse = formData.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.SbsjxxForm.bqybtfe;
    }

    var message = {"type":"ybtse", "ywbm":parent.ywbm, "ybtse":ggyYbtse+ylyYbtse};
    parent.postMessage2Vue(message);
}

// pdf用到的t_中间节点统一赋值
function generatePdfAssignments() {
    if(formData.ht_){
        formData.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.SbsjxxForm.t_zspmMc = "娱乐业文化事业建设费";
    }
    if(formData.ggy){
        formData.ggy.ht_.whsyjsfsbygzbdxxVO.whsyjsfsbbygz.SbsjxxForm.t_zspmMc = "广告业文化事业建设费";
        var gridlb = formData.ggy.ht_.whsyjsfsbygzbdxxVO.ysfwxmjcqdGrid.ysfwxmjcqdGridlb;
        gridlb.forEach(function (item){
            item.t_pzzlMc = item.pzzlDm1 ? pdf_pzzldm[item.pzzlDm1] :'';
        })
    }
}

export default{
    postYbtse,
    getMzsrbys,
    getYzsrbys,
    pop09CallBack,
    clearFile,
    pop09,
    importExcel,
    importPreCheck,
    jzxxTip,
    verifyYzsrbysAndMzsrbys,
    zswsbTips2,
    zswsbTips,
    getFormDataVO,
    generatePdfAssignments
}