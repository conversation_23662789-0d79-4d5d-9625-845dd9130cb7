var DEFAULT_VERSION = 8.0;
var ua = navigator.userAgent.toLowerCase();
var isIE = ua.indexOf("msie")>-1;
var safariVersion;
if(isIE){
	safariVersion =  ua.match(/msie ([\d.]+)/)[1];
}
if(safariVersion <= DEFAULT_VERSION ){
	//ie8 数组对象indexOf方法兼容
	if (!Array.prototype.indexOf)
	{
		Array.prototype.indexOf = function(elt /*, from*/)
		{
			var len = this.length >>> 0;
			var from = Number(arguments[1]) || 0;
			from = (from < 0)
				? Math.ceil(from)
				: Math.floor(from);
			if (from < 0)
				from += len;

			for (; from < len; from++)
			{
				if (from in this &&
					this[from] === elt)
					return from;
			}
			return -1;
		};
	}
};
/**
 * 根据方法名执行对应方法
 * @param mathFlag
 * @param data
 * @param scope
 */
function extMethods(mathFlag,newData,olddata,scope){
	//房地产开发企业行业代码：7010，填写此行给予提示
	if ("zb4row"==mathFlag){
		zb4row();
	}else if ("changeXzqh"==mathFlag){
		//GEARS-9487 2.0企业所得税月季报A表2018版，GT3-ZJ-金税三期标准服务清册_V2.29新增功能
		changeXzqh(newData);
	}else if ("zb14row" == mathFlag) {
		//主表14行 减：特定业务预缴（sbbSfksbValid征）所得税额” 福建个性化提示（SW2017112-2268）
		zb14row();
	}else if ("zb15row" == mathFlag) {
		zb15row();
	}else if ("yzsmts" == mathFlag) {
		yzsmts(newData);
	}else if ("gxjsqyGzs" == mathFlag) {
		gxjsqyGzs(newData);
	}else if ("fbfylrow" == mathFlag) {
        fbfylrow();
    }else if ("openYxzTips2" == mathFlag) {
    	openYxzTips2();
    }else if ("txMssrsx" == mathFlag) {
    	//点击填写免税优惠事项
    	txMssrsx(scope);
    }else if ("txSdjmsx" == mathFlag) {
    	//点击填写所得减免优惠事项
    	txSdjmsx(scope);
    }else if ("txJmsdssx" == mathFlag) {
    	//点击填写所得减免优惠事项
    	txJmsdssx(scope);
    }else if ("changeFbsxXszc" == mathFlag) {
    	//切换附报事项的政策
    	changeFbsxXszc(scope,newData);
    }else if("jmsdssxChangeBnlj" == mathFlag){
    	//减免所得税优惠事项输入本年累计，暂时只有35行可修改
    	jmsdssxChangeBnlj(scope,newData);
    }else if("jmsdssxFbmdts" == mathFlag){
    	//减免所得税优惠事项点击本年累计金额时，判断不在白名单内的是否要提示
    	jmsdssxFbmdts(scope,newData);
    }else if("mssrsxFbmdts" == mathFlag){
    	//免税收入优惠事项点击本年累计金额时，判断不在白名单内的是否要提示
    	mssrsxFbmdts(scope,newData);
    }else if("yysrLjChange" == mathFlag){
    	yysrLjChange(scope);
    }else if("yycbLjChange" == mathFlag){
    	yycbLjChange(scope);
    }else if("lrzeLjChange" == mathFlag){
    	lrzeLjChange(scope);
    }else if("checkfbsxGrid" == mathFlag){
        checkfbsxGrid(scope);
    }else if("jcClickTcts" == mathFlag){
    	//从业人数，资产总额第2,3,4季度点击时需要提示纳税人想修改数据请更正
    	jcClickTcts(scope,newData);
    }else if("fpblChange" == mathFlag){
		fpblChange(scope);
	}else if("hideOpenpage" === mathFlag){
        hideOpenpage(scope,newData);
    }else if("onclickTs" === mathFlag){
		onclickTs(scope,newData);
	}else if("mssrFjtsJe" === mathFlag){
		mssrFjtsJe(scope,newData);
	}else if("sdjmFjts" === mathFlag){
		sdjmFjts(scope,newData);
	}else if("sdjmFjtsJe" === mathFlag){
		sdjmFjtsJe(scope,newData);
	}


}

function  changeXzqh(xzqhInedx){
	if(isEmptyObject(xzqhInedx)){
		var _jpath1 = "ht_.ywbw.A200000Ywbd.sbxx.fpbl";
        var _jpath2 = "ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq";
		var _jpath3 = "ss_.t_fzjgReadOnly";
		parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl = 0;
        parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq = 0;
		parent.formData.ss_.t_fzjgReadOnly='Y';
        parent.formulaEngine.apply(_jpath1,  "");
        parent.formulaEngine.apply(_jpath2,  "");
        parent.formulaEngine.apply(_jpath3,  "Y");

        // 3、刷新校验结果和控制结果
        viewEngine.formApply($('#viewCtrlId'));
        viewEngine.tipsForVerify(document.body);
        parent.layer.close(index);
        return;
	}
	var gzsbbz=parent.formData.kz_.temp.gzsbbz;
	var qygxhXmBz=parent.formData.fq_.qygxhXmBz;
	if(gzsbbz=='Y'){
		if( qygxhXmBz=='Y'){
			parent.formData.ss_.t_fzjgReadOnly='N';
			var _jpath3 = "ss_.t_fzjgReadOnly";
			parent.formulaEngine.apply(_jpath3,  "N");

	        // 3、刷新校验结果和控制结果
	        viewEngine.formApply($('#viewCtrlId'));
	        viewEngine.tipsForVerify(document.body);
		}
		   return;
	}

    var xzqhDm=parent.formCT.xzqhCT[xzqhInedx].dm;
	// var xzqhDm = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.xzqhszDm;
	if(!isEmptyObject(xzqhDm)){
	var fpbl = 0;
	var fpse = 0;
	var djxh = parent.formData.fq_.nsrjbxx.djxh;
	var gdslxDm = parent.formData.fq_.nsrjbxx.gdslxDm;
	var nsrsbh = parent.formData.fq_.nsrjbxx.nsrsbh;
	var zgswjDm = parent.formData.fq_.nsrjbxx.zgswjDm;
	var sbqylx = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;

	var skssqq = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
	var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var sbsxDm1 = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbsxDm1;


	var mainUrl = window.location.protocol+"//"+window.location.host+"/"+window.location.pathname.split("/")[1];
	var jsonData = {};
	jsonData.sbywbm = "QYSDS_A_21YJD";
	jsonData.gdslxDm = gdslxDm;
	jsonData.skssqq = skssqq;
	jsonData.skssqz = skssqz;
	jsonData.sssqQ = skssqq;
	jsonData.sssqZ = skssqz;
	jsonData.djxh = djxh;
	jsonData.nsrsbh = nsrsbh;
	jsonData.swjgDm = zgswjDm;
	jsonData.xzqhDm = xzqhDm;
	jsonData['sid'] = "dzswj.ywzz.sb.qysdsa21yjd.getOtherData";
	var index = parent.layer.load(2, {
		content: '正在加载跨省分支机构信息...',
		success: function (layero) {
			layero.find('.layui-layer-content').css({
				'paddingTop': '40px',
				'width': '200px',
				'textAlign': 'center',
				'backgroundPositionX': 'center'
			});
		}});

	parent.parent.requestYwztData(jsonData,function (data) {
        console.log(data);
        if(!isEmptyObject(data)){

            var xmlDoc = $.parseXML(data);

            $(xmlDoc).find('fzjgxxGrid > fzjgxxGridlb').each(function() {
                var fzjgnsrsbh = $(this).children("fzjgnsrsbh").text();
                var jkfpbl = $(this).children("fpbl").text();
                var jkfpse = $(this).children("fpse").text();
                if(fzjgnsrsbh == nsrsbh){
                    fpbl = SUM(Number(jkfpbl),fpbl);
                    fpse = SUM(Number(jkfpse),fpse);
                }
            })
            if(sbqylx == 2){
                parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl = parent.ROUND(fpbl,10);
                parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq = parent.ROUND(fpse,2);
                if(zgswjDm.substring(1,5)=='3502'){
                	var _jpath1 = "ss_.t_fzjgReadOnly";
                	if(fpbl!=0){
                		parent.formData.ss_.t_fzjgReadOnly='Y';
						parent.formulaEngine.apply(_jpath1,  "Y");
                	}else{
                		parent.formData.ss_.t_fzjgReadOnly='';
						parent.formulaEngine.apply(_jpath1,  "");
                	}
                }
            }else{
                parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl = 0;
                parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq = 0;
            }

        }
        var _jpath1 = "ht_.ywbw.A200000Ywbd.sbxx.fpbl";
        var _jpath2 = "ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq";
        parent.formulaEngine.apply(_jpath1,  "");
        parent.formulaEngine.apply(_jpath2,  "");

        // 3、刷新校验结果和控制结果
        viewEngine.formApply($('#viewCtrlId'));
        viewEngine.tipsForVerify(document.body);
        parent.layer.close(index);
    })
	}
}

function zb4row(){
	var hyDm=parent.formData.fq_.nsrjbxx.hyDm;
	//副营行业中是否有房地产行业
	var fdchy=parent.formData.fq_.fdchy;
	var tdywjsdynssdeLj=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.tdywjsdynssdeLj

	if (hyDm != "7010" && fdchy != "Y" && tdywjsdynssdeLj !== 0) {
		var msg= "本行属于房地产开发企业销售未完工开发产品取得的预售收入，您不属于房地产开发企业，不得填报本行次。";
		top.$vue.$gtDialog.error({
			header:'提示',
			body: msg,
			closeOnOverlayClick: false,
			closeOnEscKeydown: false,
			closeBtn: false,
			onConfirm: function() {
				parent.formData.ht_.ywbw.A200000Ywbd.sbxx.tdywjsdynssdeLj=0;
				//3、刷新校验结果和控制结果
				var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.tdywjsdynssdeLj";
				parent.formulaEngine.apply(_jpath,  "");
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}
		});
	}
}


 /**
  * 非空校验
  * @param obj
  * @returns {Boolean}
  */
 function isEmptyObject(obj){
 	if(obj==""||obj==null||obj==undefined){
 		return true;
 	}else{
 		return false;
 	}
 }

 function SUM() {
		var ps;
		if (arguments.length <= 0) {
			return null;
		} else if (arguments.length == 1) {
			ps = arguments[0];
		} else {
			ps = arguments;
		}
		if (ps.length) {
			var ret = 0;
			var fix = 1;
			for (var i = 0; i < ps.length; i++) {
				if (ps[i] instanceof Array) {
					var t = SUM(ps[i]) * fix;
					while(t % 1 !=0){
						fix *= 10;
						t *= 10;
						ret *= 10;
					}
					ret += t;
				} else {
					var t = ps[i] * fix;
					while(t % 1 != 0){
						fix *= 10;
						t *= 10;
						ret *= 10;
					}
					ret += t;
				}
			}
			return ret/fix;
		} else if (!isNaN(ps)) {
			return ps;
		}
	}


 //甘肃个性化提示
 function jsrgtisp(val){

	var dqDm= (parent.formData.fq_.nsrjbxx.swjgDm).substring(1,3);
	var jsrg=val.value;

	if(dqDm!="62"||jsrg!="Y"){

		return;


	}

	var msg="根据《财政部 国家税务总局关于完善股权激励和技术入股有关所得税政策的通知》（财税〔2016〕101号）文件规定，企业以技术成果投资入股到境内居民企业，被投资企业支付的对价全部为股票（权）的，企业可以选择适用递延纳税优惠政策。本年内发生以技术成果投资入股且选择适用递延纳税优惠政策的纳税人，选择“是”；本年内未发生以技术成果投资入股或者以技术成果投资入股但选择继续按现行有关税收政策执行的纳税人，选择“否””，请您确认是否要进行修改！";

	var b = parent.layer.confirm(msg,{
	 	area: ['550px','280px'],
	 	title:'提示',
		btn : ['是','否'],
		btn2:function(index){

			parent.formData.ht_.ywbw.A200000Ywbd.sbxx.sffsjsrgdynssx='N';
		//3、刷新校验结果和控制结果
			var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.sffsjsrgdynssx";
			parent.formulaEngine.apply(_jpath,  "");
			   viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
				parent.layer.close(b);

		}
		},function(index){

			parent.layer.close(b);
});


 }

 function zb15row(){
		var yjfs= parent.formData.hq_.qtxx.yjfs;
		var sbqylx = parent.formData.hq_.qtxx.sbqylx;
		var sfqdxw300w = parent.formData.kz_.temp.zb.sfqdxw300w; // 是否启动小微标准扩围为300万
		if(sfqdxw300w!="Y"||yjfs!="3"||sbqylx=="2"){
			return;
		}
		var xwbz="";
		var ybtsdseLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.ybtsdseLj;
		var sjyyjsdseLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.sjyyjsdseLj;
		var hsse=parent.ROUND(ybtsdseLj+sjyyjsdseLj,2)
		var tsnsrlxDm = parent.formData.hq_.qtxx.tsnsrlxDm
		var kdqsszyDm = parent.formData.fq_.kdqsszyDm;
		var zfjglxDm = parent.formData.fq_.zfjglxDm;
		var skssqz= parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
		 var yf_z=parseInt((skssqz).split("-")[1], 10);
		 var zfjglxDm_nsrxx = parent.formData.fq_.zfjglxDm_nsrxx;//纳税人信息扩展表中的总分机构类型
		 var xsd2_39 = parent.formData.kz_.temp.zb.xsd2_39;//是否是xsd2.39版本
		if ( hsse>250000) {
		// 19版本 申报期止不是季末时 小薇默认空
		 if(yf_z!=3&&yf_z!=6&&yf_z!=9&&yf_z!=12){
			 xwbz="";
		   }else{
			   xwbz= 'N';
		   }

	   }else{

			var qccyrs = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs;
			var qmcyrs = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs;
			var qczcze = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze;
			var qmzcze = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze;
			var gjxzhjzhy = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy;
			var cyrsPjsLjs= parent.formData.hq_.qtxx.cyrsPjsLjs;
			var zczePjsLjs= parent.formData.hq_.qtxx.zczePjsLjs;
			var ysbJds= parent.formData.hq_.qtxx.ysbJds;

			cyrsPjsLjs=parent.isNull(cyrsPjsLjs)?0:cyrsPjsLjs;
			zczePjsLjs=parent.isNull(zczePjsLjs)?0:zczePjsLjs;
			ysbJds=parent.isNull(ysbJds)?0:ysbJds;

			var bdpjrs=parent.ROUND((qccyrs+qmcyrs)/2,2);
			var bdpjzcze=parent.ROUND((qczcze+qmzcze)/2,2);

			var pjrs=parent.ROUND((cyrsPjsLjs+bdpjrs)/(ysbJds+1),2);
			var pjzcze=parent.ROUND((zczePjsLjs+bdpjzcze)/(ysbJds+1),2);

			 if(yf_z!=3&&yf_z!=6&&yf_z!=9&&yf_z!=12){
				 xwbz="";
			   }else if((zfjglxDm_nsrxx=="2"||zfjglxDm_nsrxx=="3")&&xsd2_39==="Y"&&tsnsrlxDm != "05"&&tsnsrlxDm != "06" &&tsnsrlxDm != "10"){
					return "N";
			  }else if(pjrs<=300&&pjzcze<=5000&&gjxzhjzhy=="N"&&hsse<=250000){
				 xwbz="Y";
			 }else{
				 xwbz="N";
			 }


		}

		parent.formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy = xwbz;
		// 3、刷新校验结果和控制结果
		var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy";
		parent.formulaEngine.apply(_jpath, "");
		viewEngine.tipsForVerify(document.body);
		viewEngine.formApply($('#viewCtrlId'));

	}


 function zczets(bz) {
	 var sbqylx=parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	 var skssqz=parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	 var sbqmyf=skssqz.split("-")[1];
	 if(sbqylx=="2"||(sbqmyf!="03"&&sbqmyf!="06"&&sbqmyf!="09"&&sbqmyf!="12")){
		 return;
	 }

		var qczcze = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze;
		var qmzcze = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze;
		var zczePjsLjs= parent.formData.hq_.qtxx.zczePjsLjs;
		var ysbJds= parent.formData.hq_.qtxx.ysbJds;
		zczePjsLjs=parent.isNull(zczePjsLjs)?0:zczePjsLjs;
		ysbJds=parent.isNull(ysbJds)?0:ysbJds;
		var bdpjzcze=parent.ROUND((qczcze+qmzcze)/2,2);
		var pjzcze=parent.ROUND((zczePjsLjs+bdpjzcze)/(ysbJds+1),2);

		if(pjzcze<=5000){
			 return;
		}

		var msg="本栏次填报单位为“万元”，您填报的本纳税年度截至本期末的资产总额季度平均值超过5000万元，不符合小型微利企业条件，请您再次确认资产总额填报金额是否准确。";
		var b = parent.layer.confirm(msg,{
		 	area: ['350px','220px'],
		 	title:'提示',
			btn : ['是','否'],
			btn2:function(index){
				var _jpath ="";
				if(bz==="1"){
					var sqQmzcze=parent.formData.hq_.qtxx.sqQmzcze;
					//有要素信息优先获取要素信息
					if(parent.formData.wbcsh!=null&&parent.formData.wbcsh!= "undefined"&&parent.formData.wbcsh["ht_.ywbw.A200000Ywbd.sbxx.qmzcze"]!=null&&parent.formData.wbcsh["ht_.ywbw.A200000Ywbd.sbxx.qmzcze"]!= "undefined"){
						sqQmzcze = parent.formData.wbcsh["ht_.ywbw.A200000Ywbd.sbxx.qmzcze"];
					}
					parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze=sqQmzcze;
				   //3、刷新校验结果和控制结果
				 	 _jpath = "ht_.ywbw.A200000Ywbd.sbxx.qczcze";
				}

				if(bz==="2"){
					parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze=0;
				   //3、刷新校验结果和控制结果
				 	 _jpath = "ht_.ywbw.A200000Ywbd.sbxx.qmzcze";
				}

				  parent.formulaEngine.apply(_jpath,  "");
				   viewEngine.tipsForVerify(document.body);
					viewEngine.formApply($('#viewCtrlId'));
					parent.layer.close(b);

			}
			},function(index){
				parent.layer.close(b);
	    });


	}

 function infoCsgjfxzhjzhy(){

	 var tips = "您填报了从事国家限制或禁止行业，按照规定不能享受小型微利企业所得税优惠政策，请您再次确认填报是否准确。";

		var a = parent.layer.confirm(tips, {
			area : [ '448px', '225px' ],
			icon:6,
			title : '提示',
			btn : [ '确定', '取消' ],
			btn2 : function(index) {
				parent.formData.ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy='N';
				var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy";
				parent.formulaEngine.apply(_jpath, "");
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}
		});
 }


//主表14行 减：特定业务预缴（征）所得税额” 福建个性化提示（SW2017112-2268）
function zb14row(){
	var zgswjDm = parent.formData.fq_.nsrjbxx.zgswjDm;
	var skssqqnd=(parent.formData.fq_.sssq.sqQ).split('-')[0];
	var yjfs=parent.formData.hq_.qtxx.yjfs;
	var sbqylx = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	var dqdm="";
	if(zgswjDm!==""&&zgswjDm!=null&&zgswjDm!=undefined){
		dqdm=zgswjDm.substring(0,3);
		}

	if(dqdm!="135"||yjfs!="1"|| sbqylx=="2"||zgswjDm.substring(0,5)=='13502'){
		return;
	}

        var msg = "本行填报建筑企业总机构直接管理的跨地区设立的项目部，按照税收规定已经向项目所在地主管税务机关预缴企业所得税的本年累计金额。";
        var b = parent.layer.confirm(msg, {
            area: ['350px', '220px'],
            title: '提示',
            btn: ['确定', '取消'],
            btn2: function (index) {

                var jaqyYjze = parent.formData.hq_.qtxx.jaqyYjze;
                var tdywyjzsdsej = parent.formData.hq_.sqsbxx.tdywjsdynssdeLj;
                parent.formData.ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj = parent.ROUND(jaqyYjze + tdywyjzsdseLj, 2);

                var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj";
                parent.formulaEngine.apply(_jpath, "");
                viewEngine.tipsForVerify(document.body);
                viewEngine.formApply($('#viewCtrlId'));
                parent.layer.close(b);

            }
        }, function (index) {
            parent.layer.close(b);
        });


 }



function yzsmts(newData){
	//批量申报时弹框要频闭
	var plsbbz = parent.location.href.indexOf('isPlLsb')>-1 || parent.isJmsb();
	if(plsbbz){
		return;
	}

	if(newData==="Y"){
		parent.formData.kz_.temp.zb.yhxgbz='N';
		return;
	}

	var zgswjDmStr = parent.formData.fq_.nsrjbxx.zgswjDm;
	var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var dqdmstr="";
	if(zgswjDmStr!==""&&zgswjDmStr!=null&&zgswjDmStr!=undefined){
		dqdmstr=zgswjDmStr.substring(1,3);
		}
	var hzje=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.fhtjxwqyyhjzsdseLj;
	if((hzje<=0 || parent.DATE_CHECK_TIME("2020-09-30", skssqz) )&&dqdmstr=="44" ){
		return;
	}

	 var tips = "您单位填报的数据符合小型微利企业条件，可暂延缓缴纳本季度应补缴的企业所得税，请确认是否放弃享受延缓缴纳政策。如放弃享受的，请填写情况说明。";
	 if(dqdmstr=="44"){
			tips="您单位填报的数据符合小型微利企业条件，可暂延缓缴纳本季度应补缴的企业所得税，请确认是否放弃享受延缓缴纳政策。如放弃享受的，请填写情况说明。";
		}
		 parent.layer.confirm(tips, {
			area : [ '448px', '200px' ],
			 icon:6,
			title : '提示',
			btn : [ '确定', '取消' ],
			btn2 : function(index) {
				parent.layer.close(index);
				parent.formData.ht_.ywbw.A200000Ywbd.sbxx.yhjnsds='Y';
				var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.yhjnsds";
				parent.formulaEngine.apply(_jpath, "");
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}
		},function(index){
			parent.formData.kz_.temp.zb.yhxgbz='Y';
				parent.layer.close(index);

				var zgswjDm = parent.formData.fq_.nsrjbxx.zgswjDm;
				var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
				var dqdm="";
				if(zgswjDm!==""&&zgswjDm!=null&&zgswjDm!=undefined){
					dqdm=zgswjDm.substring(0,3);
					}

				if(dqdm=="111"&& (skssqz=="2020-9-30" || skssqz=="2020-09-30")){
					parent.formData.ht_.ywbw.A200000Ywbd.sbxx.bhzsm='我公司已知晓符合条件的小型微利企业延缓缴纳所得税额政策，我公司自愿放弃享受该政策，特此说明。';
					var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.bhzsm";
					parent.formulaEngine.apply(_jpath, "");
					viewEngine.tipsForVerify(document.body);
					viewEngine.formApply($('#viewCtrlId'));

				}

				if(dqdm=="144"){
				        	parent.formData.kz_.gzbhzsm="";;
			        		var sHtml = "";
			        		sHtml += "<table   style='font-size:13px;height:100%;width:100%; ' >";
			        		sHtml += "	<tr  style='font-size:13px;height:10px;width:100%; ' >";
			        		sHtml += "<td   style='font-size:13px;height:10px;width:100%; ''><span>本人已知晓小型微利企业延缓缴纳政策，确认放弃享受延缓缴纳政策</span></td>" ;
			        		sHtml += "	</tr>";
			        		sHtml += "	<tr  style='font-size:13px;height:100px;width:100%; '>";
			        		sHtml += "<td  style='font-size:13px;height:100px;width:100%; '><textarea style='height:100px;width:100%; ' id='smid' name='smid'  onkeyup='hsyqcheck()'></textarea></td>" ;
			        		sHtml += "	</tr>";
			        		sHtml += "</table>";
			        		sHtml +="<script>";
			        			sHtml +="function hsyqcheck(){";
			        			sHtml +="var sm=document.getElementById('smid').value;";
			        			sHtml +=" formData.kz_.gzbhzsm=sm;";
			        			sHtml +=" }";
			   			        sHtml +="</script>";

			        			 parent.layer.open({
			        		            content: sHtml,
			        		        	area : [ '460px','320px' ], //固定宽高400px
			        		            title: '放弃享受延缓缴纳所得税政策情况说明',
			        		            btn : ['确定','取消'],
			        		            type:1,
			        		            closeBtn: 0,
			        		            btn2: function(index){
			        		            parent.layer.close(index);
			        					parent.formData.ht_.ywbw.A200000Ywbd.sbxx.yhjnsds='Y';
			        					var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.yhjnsds";
			        					parent.formulaEngine.apply(_jpath, "");
			        					viewEngine.tipsForVerify(document.body);
			        					viewEngine.formApply($('#viewCtrlId'));
			        					},
			        		            yes: function (tp) {
			        		            	var gzbhzsm=parent.formData.kz_.gzbhzsm;
			        		            	if(gzbhzsm==""){
			        		            		var msg="【放弃享受延缓缴纳所得税政策情况说明】不能为空！";
			        		            		parent.layer.alert(msg, {title:"提示",icon: 5});
			        		            		return;
			        		            	}
			        		            	if(gzbhzsm.length<=10){
			        		            		var msg="【放弃享受延缓缴纳所得税政策情况说明】录入内容应在10个字以上！";
			        		            		parent.layer.alert(msg, {title:"提示",icon: 5});
			        		            		return;
			        		            	}
			        		            	  parent.layer.close(tp);
			        		            	parent.formData.ht_.ywbw.A200000Ywbd.sbxx.bhzsm="本人已知晓小型微利企业延缓缴纳政策，确认放弃享受延缓缴纳政策。"+gzbhzsm;
			        		            	var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.bhzsm";
			        						parent.formulaEngine.apply(_jpath, "");
			        						viewEngine.tipsForVerify(document.body);
			        						viewEngine.formApply($('#viewCtrlId'));


			        		            },

			        		        });

				}

	    });
}


try{
	window.onload = yjfpxxts();
}catch(e){
	window.onload = yjfpxxts;
}



function yjfpxxts(){

	//批量申报时弹框要频闭
	var plsbbz = parent.location.href.indexOf('isPlLsb')>-1 || parent.isJmsb();
	if(plsbbz ||  parent.formData==undefined){
		return;
	}
	var fpxx= parent.formData.fq_.yjfpxx;

	if(fpxx=="" || fpxx==null || fpxx==undefined){
		return;
	}

	if(fpxx.yjfpxxList.length==0){
		return;
	}

	if(parent.csz=='Y'){
		return;
	}
	parent.csz="Y"
	//font-weight:bold;
	var sHtml = "";
	sHtml += "<table  style='border-collapse:separate; border-spacing:0px 5px;'>";
	sHtml += "	<tr>";
	sHtml += "		<td style='font-weight:bold; font-size:13px' align='center'>接受不合规发票提示信息</td>";
	sHtml += "	</tr>";
	sHtml += "	<tr>";
	sHtml += "		<td >&nbsp;</td>";
	sHtml += "	</tr>";
	sHtml += "	<tr>";
	sHtml += "<td  style='font-size:11px'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;尊敬的纳税人:" ;
	sHtml += "</td>";
	sHtml += "	</tr>";
   sHtml += "	<tr >";
	sHtml += "	<td style='font-size:11px'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您单位取得下列增值税专用发票，存在企业所得税税前不能扣除的风险。根据《企业所得税税前扣除凭证管理办法》（国家税务总局公告2018年第28号）第十二条规定，不合规发票不得在企业所得税前扣除。" ;
	sHtml +="</td>";
	sHtml += "	</tr>";
	sHtml += "	<tr>";
	sHtml += "		<td  style='font-size:11px'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您单位应当在当年度企业所得税汇算清缴期结束前，按照国家税务总局公告2018年第28号第十三条、第十四条规定，补开、换开发票或补充资料，证实支出真实性。</td>";
	sHtml += "	</tr>";
	sHtml += "	<tr>";
	sHtml += "<td  style='font-size:11px'  align='right' >国家税务总局北京市税务局" ;
	sHtml += "</td>";
	sHtml += "	</tr>";
	sHtml += "</table>";


	 parent.layer.open({
            content: sHtml,
        	area : [ '800px','360px' ], //固定宽高400px
            title: '提示',
            btn : [ '不合规发票详细信息' ],
            closeBtn: 0,
            yes: function (tp) {
               	  parent.layer.close(tp);
               	 yjfpxx()
            },

        });


}


function yjfpxx(){
	var fpxx= parent.formData.fq_.yjfpxx;

	if(fpxx=="" || fpxx==null || fpxx==undefined){
		return;
	}

	if(fpxx.yjfpxxList.length==0){
		return;
	}


    var fpxxlist= parent.formData.fq_.yjfpxx.yjfpxxList;
	//font-weight:bold;
	var sHtml = "";
	sHtml += "<table >";
	sHtml += "	<tr>";
	sHtml += "	<td style='font-weight:bold; font-size:13px' align='center'   colspan='9'>不合规发票详细信息</td>";
	sHtml += "	</tr>";
	sHtml += "	<tr>"
	sHtml += "<td>&nbsp;</td>";
	sHtml += "	</tr>";
	sHtml += "<tr>";
	sHtml += "<td  style='font-size:11px;width:9%;'>发票代码</td>";
	sHtml += "<td   style='font-size:11px;width:7%;'>发票号码</td>";
	sHtml += "<td  style='font-size:11px;width:12%;'>购方纳税人识别号</td>";
	sHtml += "<td  style='font-size:11px;width:15%;'>购方纳税人名称</td>";
	sHtml += "<td   style='font-size:11px;width:15%;'>销方纳税人识别号</td>";
	sHtml += "<td  style='font-size:11px;width:15%;'>销方纳税人名称</td>";
	sHtml += "<td style='font-size:11px;width:8%;'>金额</td>";
	sHtml += "<td  style='font-size:11px;width:8%;'>税额</td>";
	sHtml += "<td   style='font-size:11px;width:8%;'>开票日期</td>";
	sHtml += "</tr>";

		for(var i=0;i<fpxxlist.length;i++){
			sHtml += "<tr>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].fpdm+"</td>";
			sHtml += "<td   style='font-size:11px'>"+fpxxlist[i].fphm+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].gfnsrsbh+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].gfnsrmc+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].xfnsrsbh+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].xfnsrmc+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].je+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].se+"</td>";
			sHtml += "<td  style='font-size:11px'>"+fpxxlist[i].kprq+"</td>";
		}

	sHtml += "</table>";


	 parent.layer.open({
            content: sHtml,
        	area : [ '980px','360px' ], //固定宽高400px
            title: '提示',
            btn : [ '确定' ],
            closeBtn: 0,
            yes: function (tp) {
               	  parent.layer.close(tp);
      },

        });


}

/**
 * 青岛个性化
 * 当“营业收入”行次填报金额小于上期，弹出信息提示“该行次为季度累计值，除退换货等特殊情形外，本期营业收入累计金额应大于等于上期累计金额。
 * @param currVal
 */
function yysrLjChange(scope){
	var yysrLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.yysrLj;
	var swjgDm15 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(1,5);
	var swjgDm03 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(0,3);

	// 北京个性化
	var sbqylx = scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	var yjfs = scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.yjfs;
	if(swjgDm03 == "111" && (sbqylx == 0 || sbqylx == 1) && yjfs == "1" && yysrLj >= 100000000){
		parent.layer.confirm("尊敬的纳税人，您填报的数据大于1亿元，请确定无误！",{
			title:'提示',
			icon: 6,
			btn : ['阅读完毕']
		},function(index){
			parent.layer.close(index);
		});
		return;
	}

	// 青岛个性化
	if(scope.formData.fq_.sqyyxx){
		var sqyysr = scope.formData.fq_.sqyyxx.yysrLj;
		if(swjgDm15 == "3702" && yysrLj< sqyysr){
			var msg="该行次为季度累计值，除退换货等特殊情形外，本期营业收入累计金额应大于等于上期累计金额。";
			layer.alert(msg, {title:"提示",icon: 5});
		}
	}
}

/**
 * 青岛个性化
 * 当“营业成本”行次填报金额小于上期，弹出信息提示“该行次为季度累计值，除退换货等特殊情形外，本期营业成本累计金额应大于等于上期累计金额。
 * @param currVal
 */
function yycbLjChange(scope){
	var yycbLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.yycbLj;
	var swjgDm15 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(1,5);
	var swjgDm03 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(0,3);

	// 北京个性化
	if(swjgDm03 == "111" && yycbLj >= 100000000){
		parent.layer.confirm("尊敬的纳税人，您填报的数据大于1亿元，请确定无误！",{
			title:'提示',
			icon: 6,
			btn : ['阅读完毕']
		},function(index){
			parent.layer.close(index);
		});
		return;
	}

	// 青岛个性化
	if(scope.formData.fq_.sqyyxx){
		var sqyycb = scope.formData.fq_.sqyyxx.yycbLj;
		if(swjgDm15 == '3702' && yycbLj< sqyycb){
			var msg="该行次为季度累计值，除退换货等特殊情形外，本期营业成本累计金额应大于等于上期累计金额。";
			layer.alert(msg, {title:"提示",icon: 5});
			return;
		}
	}

}

function lrzeLjChange(scope){
	var lrzeLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.lrzeLj;
	var swjgDm15 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(1,5);
	var swjgDm03 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(0,3);

	// 北京个性化
	if(swjgDm03 == "111" && lrzeLj >= 100000000){
		parent.layer.confirm("尊敬的纳税人，您填报的数据大于1亿元，请确定无误！",{
			title:'提示',
			icon: 6,
			btn : ['阅读完毕']
		},function(index){
			parent.layer.close(index);
		});
	}

	//判断是否需要选中第一行
	var sfyjmsdsyh = scope.formData.fq_.sfyjmsdsyh;
	var gzbz = scope.formData.kz_.temp.gzsbbz;
	if("N"==sfyjmsdsyh && gzbz != "Y"){
		var sjlreLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;// 第10行
			//再判断是否是小微企业
			var sfsyxxwlqy = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
			if("Y"==sfsyxxwlqy){
				scope.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
				var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect";
				parent.formulaEngine.apply(_jpath, true);
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}else {
				scope.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = false;
				var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect";
				parent.formulaEngine.apply(_jpath, false);
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}
	}
}

function fpblChange(scope){
	var fpbl = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl;
	if(isEmptyObject(fpbl)){
		scope.formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl = 0;
		var _jpath = "ht_.ywbw.A200000Ywbd.sbxx.fpbl";
		parent.formulaEngine.apply(_jpath, "");
		viewEngine.tipsForVerify(document.body);
		viewEngine.formApply($('#viewCtrlId'));
	}
}

/**
*
*如果季末资产总额> 《资产负债表》期末资产总额 *1.1或季末资产总额<《资产负债表》期末资产总额 *0.9 则提示
*季末资产总额____与《资产负债表》的期末资产总额___(单位万元)不一致，请审核确认
*  该提示为弹窗提示性校验，非强制
* @param currVal
*/
function qmzczeChange(val){
	var skssqq = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
	var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var subSwjgDm = (parent.formData.fq_.nsrjbxx.swjgDm).substring(1,5);

	// 以下是：青岛个性化
	//如果不是季报或者查询出来的财务报表资产负债表信息为空 则不进行下面操作
	if(skssqz.split('-')[1]-skssqq.split('-')[1]!=2 || parent.isNull(parent.formData.fq_.zcfzbxx)){
		return;
	}else if(parent.isNull(parent.formData.fq_.zcfzbxx.zcfzbxx[0].qmyeZc)){
		return;
	}
	var qmyeZc = parent.formData.fq_.zcfzbxx.zcfzbxx[0].qmyeZc;
	var qmzcze=0;
	if(val=="qmzcze1"){
		qmzcze=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1;
	}else if(val=="qmzcze2"){
		qmzcze=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2;
	}else if(val=="qmzcze3"){
		qmzcze=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3;
	}else if(val=="qmzcze4"){
		qmzcze=parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4;
	}
}


//GDSDZSWJ-16766:广东个性化：高薪技术企业策告知书弹框
function gxjsqyGzs(newData){
	var zgswjDm = parent.formData.fq_.nsrjbxx.zgswjDm;
	var dqdm="";
	if(zgswjDm!==""&&zgswjDm!=null&&zgswjDm!=undefined){
		dqdm=zgswjDm.substring(1,3);
		}

	if(dqdm!="44"){
		return;
	}

	var pathName = document.location.pathname;
	var index = pathName.substr(1).indexOf("/");
	var result = pathName.substr(0, index + 1);
	 // 弹出告知书
	 parent.indexgxjsqyGzs = parent.layer.open({
       type: 2,
       title: "",
       area: ['950px', '450px'],
       closeBtn: 0,
       content: result+"/abacus/sb/qysds_a_18yjd/gxjsqygzs.html?"
   });

}

function fbfylrow(){
    var zgswjDm = parent.formData.fq_.nsrjbxx.zgswjDm;
    if(zgswjDm.substring(0,5)=='13502'){
        var msg="你单位未认定为非营利组织，请携带资料向主管税务机关申请。";
        parent.layer.open({
            type : 1,
            title :  [ '提示' ],
            area : [ '400px', '220px' ],
            content : msg,
            btn : [ '确定' ],
            yes : function(index) {
                parent.formData.ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fhtjdfylzzdsrLj= 0;

                var _jpath = "ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fhtjdfylzzdsrLj";
                parent.formulaEngine.apply(_jpath, "");
                viewEngine.tipsForVerify(document.body);
                viewEngine.formApply($('#viewCtrlId'));
                parent.layer.close(index);
            },
			cancel:function (index){
                parent.formData.ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fhtjdfylzzdsrLj= 0;

                var _jpath = "ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fhtjdfylzzdsrLj";
                parent.formulaEngine.apply(_jpath, "");
                viewEngine.tipsForVerify(document.body);
                viewEngine.formApply($('#viewCtrlId'));
                parent.layer.close(index);
			}
        });
    }
}

/**
 * 江苏企业A个性化：非名单内企业永续债点击提示
 */
function openYxzTips2(){
	var swjgDm = parent.formData.fq_.nsrjbxx.swjgDm;
	var h7yxzBz = parent.formData.hq_.h7yxzBz;
	if(swjgDm != null && swjgDm != undefined && swjgDm.substring(0,3) == '132' && h7yxzBz == 'N'){
			layer.open({
				title: '提示',
				content:'<p>如需填写永续债，请到大厅申报或联系主管税务局增加永续债填写名单</p>'
			});
	}
}


/**
 * 选择免税收入优惠事项
 *
 */
function txMssrsx(scope){
	//设置排序
	var fylBmdBz = "N";
    var swjgDm3 = (parent.formData.fq_.nsrjbxx.swjgDm).substring(0,3);
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	var sbqylx =  scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    var skssqq =parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
	var skssqz =parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var xgrqDate= new Date("2021-09-30");
    var skssqqDate= new Date(skssqq);
	var skssqzDate= new Date(skssqz);
    var nf_z = parseInt((skssqz).split("-")[0], 10);
	var yf_z = parseInt((skssqz).split("-")[1], 10);

	if(sbqylx == 2){
        parent.layer.confirm("您的企业类型为“跨地区经营汇总纳税企业分支机构”，第7行优惠事项不允许填写",{
            title:'提示',
			area: ['448px'],
			icon:6,
            btn : ['确定']
        },function(index){
            parent.layer.close(index);
        });
        return ;
	}
	//设置序号
	var nsrmc = scope.formData.fq_.nsrjbxx.nsrmc;
	var hyDm = scope.formData.fq_.nsrjbxx.hyDm;

	if("Y"==qygxhXmBz){
		fylBmdBz = scope.formData.kz_.bmdBzxx.fylBmdBz;
		if("中国清洁发展机制基金管理中心"===nsrmc || "财政部政府和社会资本合作中心"===nsrmc
				 || "中国保险保障基金有限责任公司"===nsrmc
				 || "中国奥林匹克委员会"===nsrmc
				 || "中国残疾人奥林匹克委员会"===nsrmc
				 || hyDm.indexOf("66")==0 || hyDm.indexOf("69")==0
				 || hyDm.indexOf("68")==0
				 || hyDm==="6635" || nsrmc.indexOf("小额贷款")>-1){
			//10-13行其中一行置顶
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[1].t_xh = 2;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[0].t_xh = 3;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[2].t_xh = 4;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[3].t_xh = 5;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[4].t_xh = 6;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[5].t_xh = 7;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].t_xh = 8;

			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[7].t_xh = 9;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[8].t_xh = 10;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 11;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 12;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 13;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 14;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 15;

            if(skssqzDate>=xgrqDate&&yf_z>=9){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 16;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 17;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 18;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 19;
            }

			if(skssqzDate >= new Date("2022-09-30")){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[29].t_xh = 11;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 12;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 13;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 14;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 15;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 16;

                if(yf_z>=9){
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 17;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 18;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 19;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 20;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 21;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 22;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 23;
				}
			}

		}else{
			//10-13，17-18隐藏
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[1].t_xh = 1;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[0].t_xh = 2;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[2].t_xh = 3;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[3].t_xh = 4;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[4].t_xh = 5;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[5].t_xh = 6;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].t_xh = 7;

			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[7].t_xh = 8;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[8].t_xh = 9;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 10;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 11;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 12;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 13;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 14;

            if(skssqzDate>=xgrqDate &&yf_z>=9){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 15;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 16;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 17;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 18;

            }

            if(skssqzDate >= new Date("2022-09-30")){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[29].t_xh = 10;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 11;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 12;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 13;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 14;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 15;

                if(yf_z>=9){
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 16;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 17;

                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 18;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 19;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 20;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 21;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 22;
				}
			}
		}
		viewEngine.tipsForVerify(document.body);
        viewEngine.formApply($('#viewCtrlId'));
	} else if(swjgDm3 == "111"){
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 10;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 11;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 12;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 13;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 14;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[17].t_xh = 15;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[18].t_xh = 16;
        parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[21].t_xh = 17;

        if(skssqzDate>=xgrqDate &&yf_z>=9){
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 18;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 19;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 20;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 21;

        }

        if(skssqzDate >= new Date("2022-09-30")){
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[29].t_xh = 10;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 11;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 12;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 13;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 14;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 15;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[17].t_xh = 16;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[18].t_xh = 17;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[21].t_xh = 18;

            if(yf_z>=9){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 19;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 20;

                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 21;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 22;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 23;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 24;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 25;
			}
        }

        viewEngine.tipsForVerify(document.body);
        viewEngine.formApply($('#viewCtrlId'));

	}else{

		if("中国清洁发展机制基金管理中心"===nsrmc || "财政部政府和社会资本合作中心"===nsrmc
				 || "中国保险保障基金有限责任公司"===nsrmc
				 || "中国奥林匹克委员会"===nsrmc
				 || "中国残疾人奥林匹克委员会"===nsrmc
				 || hyDm.indexOf("66")==0 || hyDm.indexOf("69")==0
				 || hyDm.indexOf("68")==0
				 || hyDm==="6635" || nsrmc.indexOf("小额贷款")>-1){
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[0].t_xh = 2;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[1].t_xh = 3;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[2].t_xh = 4;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[3].t_xh = 5;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[4].t_xh = 6;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[5].t_xh = 7;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].t_xh = 8;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[7].t_xh = 9;
			parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[8].t_xh = 10;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[13].t_xh = 11;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 12;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 13;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 14;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 15;
			// parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 16;

            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 11;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 12;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 13;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 14;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 15;

            if(skssqzDate>=xgrqDate &&yf_z>=9){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 16;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 17;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 18;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 19;
            }

            if(skssqzDate >= new Date("2022-09-30")){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[29].t_xh = 11;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 12;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 13;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 14;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 15;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 16;
                if(yf_z>=9){
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 17;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 18;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 19;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 20;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 21;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 22;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 23;
				}
			}

			viewEngine.tipsForVerify(document.body);
	        viewEngine.formApply($('#viewCtrlId'));
		}else{
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 10;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 11;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 12;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 13;
            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 14;

            if(skssqzDate>=xgrqDate &&yf_z>=9){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 15;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 16;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 17;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 18;

            }

            if(skssqzDate >= new Date("2022-09-30")){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[29].t_xh = 10;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_xh = 11;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[14].t_xh = 12;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[15].t_xh = 13;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].t_xh = 14;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh = 15;

                if(yf_z>=9){
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 16;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 17;

                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 18;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 19;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 20;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 21;
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 22;
                }
			}

            viewEngine.tipsForVerify(document.body);
            viewEngine.formApply($('#viewCtrlId'));

		}
	}
    var mssrGridlb = scope.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb;
    if(mssrGridlb!=null && mssrGridlb.length>0){
        //重新设置序号(序号＋1)
        if( (hyDm.indexOf("66")==0 || hyDm.indexOf("69")==0 || hyDm.indexOf("68")==0)  && (swjgDm3 != "111")){
            if(hyDm==="6635" || nsrmc.indexOf("小额贷款")>-1){
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[17].t_xh=0;
                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[21].t_xh=1;
                for(var i=0;i<mssrGridlb.length;i++){
                    parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_xh=Number(parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_xh)+1;
                	if(i==19 && "Y"==qygxhXmBz){
                        var jjqtxh= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_xh;
                        var yjkfxh=parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh;
                        if(jjqtxh!='' && yjkfxh!='' && jjqtxh==yjkfxh){
                            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 16;
                            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 17;
                            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 18;
                            parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 19;
                            if(nf_z>=2022){
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 18;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 19;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 20;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 21;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 22;
                            }

                            if(skssqzDate >= new Date("2022-09-30")){
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_xh = 17;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_xh = 18;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 19;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 20;

                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_xh = 19;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_xh = 20;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_xh = 21;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_xh = 22;
                                parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_xh = 23;
                            }
						}
					}
                }
                viewEngine.tipsForVerify(document.body);
                viewEngine.formApply($('#viewCtrlId'));
            }
        }
	}

	parent.formData.fq_.mssryhsxSfxg = "Y";
	var mssrsxIndex = layer.open({
        type: 1,
        area: ['920px','500px'],
		offset:"0px",
        title:['免税、减计收入、加计扣除类型选项表'],
        scrollbar: false,
        closeBtn: false,
        content:$("#mssrsxTable"),
        btn: [ '确定'],
        btn1: function(index,layero){
            var swjgDm3 = (scope.formData.fq_.nsrjbxx.swjgDm).substring(0,3);
        	var mssrGridlb = scope.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb;
        	var sfdndiq = scope.formData.fq_.sfdndiq;
					var nopass = parent.formulaEngine.idxVariable2NoPass;
					if (nopass['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].ssjmxzDm']) {
						top.$vue.$gtDialog.error({
								header:'提示',
								body: nopass['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].ssjmxzDm']['gt4_20053_01_0056'].tips,
								closeOnOverlayClick: false,
								closeOnEscKeydown: false,
						});
						return;
					}
					if (nopass['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].ssjmxzDm']) {
						top.$vue.$gtDialog.error({
								header:'提示',
								body: nopass['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].ssjmxzDm']['gt4_20053_01_0057'].tips,
								closeOnOverlayClick: false,
								closeOnEscKeydown: false,
						});
						return;
					}
        	if(mssrGridlb!=null && mssrGridlb.length>0){
        		var j=1;
        		for(var i=0;i<mssrGridlb.length;i++){
        			var mssrbnljje = mssrGridlb[i].yhjmje;
        			var mssrQcsJe = mssrGridlb[i].t_mssrQcsJe;
        			if(mssrbnljje<0){
        				layer.alert("本年累计金额应大于等于0！");
        				return ;
        			}
        			var mssrsxSelect = mssrGridlb[i].t_mssrsxSelect;
        			if(mssrsxSelect){
						parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_nxh = j;
						j++;
        				parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_mssrSfyxz = "Y";
        			}else{
        				parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_mssrSfyxz = "N";
        			}
        			/*if("N"==sfdndiq && mssrbnljje<mssrQcsJe){
        				layer.alert("填报本年累计金额为【"+mssrbnljje+"】，本年上期填报的本年累计金额为【"+mssrQcsJe+"】,本次填报金额小于上期申报金额，请确认填报是否正确。");
        				//return ;
        			}*/

        		}
        	}
        	if(skssqzDate>=xgrqDate &&yf_z>=9){
        	var jjkc1Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrsxSelect;
			var jjkc2Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrsxSelect;
			var jjkc3Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrsxSelect;
			var jjkc4Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrsxSelect;
				if(nf_z>=2022){
					var jjkc5Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrsxSelect;
					var jjkc6Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrsxSelect;

					var selectVerify = false;
					var bjmsg = "";
                    if(swjgDm3 == "111" && ( (jjkc1Select==true && jjkc5Select==true) || (jjkc2Select==true && jjkc5Select==true) || (jjkc3Select==true && jjkc5Select==true) || (jjkc4Select==true && jjkc5Select==true) ) ){
                        bjmsg +="尊敬的纳税人您好，您选择了科技型中小企业按100%加计扣除优惠，JJKC013不支持与JJKC023外的加计扣除选项同时选择，请确认企业优惠选项并选择填报。</br></br>";
                        selectVerify = true;
                    }
                    if(swjgDm3 == "111" && ((jjkc1Select==true && jjkc6Select==true) || (jjkc2Select==true && jjkc6Select==true) || (jjkc3Select==true && jjkc6Select==true) || (jjkc4Select==true && jjkc6Select==true))){
                        bjmsg +="尊敬的纳税人您好，您选择了科技型中小企业按100%加计扣除优惠，JJKC023不支持与JJKC013外的加计扣除选项同时选择，请确认企业优惠选项并选择填报。</br></br>";
                        selectVerify = true;
                    }
                    if(selectVerify && bjmsg!=""){
                        layer.alert(bjmsg);
						return ;
					}

					if( (jjkc1Select==true && jjkc2Select==true)  || (jjkc1Select==true && jjkc5Select==true) || (jjkc2Select==true && jjkc5Select==true)  ){
                        layer.alert("制造业企业研发费用加计扣除不得与科技型中小企业研发费用加计扣除、其他企业研发费用加计扣除同时填报，请确认企业类型并选择一行填报。");
                        return ;
                    }else if( (jjkc3Select==true && jjkc4Select==true) || (jjkc3Select==true && jjkc6Select==true) || (jjkc4Select==true && jjkc6Select==true) ){
                        layer.alert("制造业企业进行创意设计活动而发生的相关费用加计扣除与其他企业、科技型中小企业进行创意设计活动而发生的相关费用加计扣除不得同时填报，请确认企业类型并选择一行填报。");
                        return ;
                    }
				}else{
					if( jjkc1Select==true && jjkc2Select==true   ){
						layer.alert("制造业研发费用加计扣除与非制造业研发费用加计扣除不得同时填报，请确认企业行业并选择一行填报。");
						return ;
					}else if(jjkc3Select==true && jjkc4Select==true ){
						layer.alert("制造业与非制造业进行创意设计活动而发生的相关费用加计扣除不得同时填报，请确认企业行业并选择一行填报。");
						return ;
					}
				}
        	}

        	parent.formData.fq_.mssryhsxSfxg = "N";

			var _jpath = "ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[#].t_nxh";
			parent.formulaEngine.apply(_jpath, "");
			viewEngine.tipsForVerify(document.body);
			viewEngine.formApply($('#viewCtrlId'));

			var flag=false;
			var msg = "";
			var index=0;
			// 全国版提示(厦门\青岛\北京除外)
			var swjgDm5 = (parent.formData.fq_.nsrjbxx.swjgDm).substring(0,5);
			if(skssqzDate>=xgrqDate &&yf_z>=9){
				var snnbXsyffyjjkcyhbz=parent.formData.hq_.qtxx.snnbXsyffyjjkcyhbz;
				var jjkc1= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].yhjmje;
				var jjkc2= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].yhjmje;
				var jjkc3= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].yhjmje;
				var jjkc4= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].yhjmje;
				var gxjsqyyh1= parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje;
				var gxjsqyyh2= parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje;
				if(nf_z>=2022){
                    var jjkc5= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].yhjmje;
                    var jjkc6= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].yhjmje;
                    var jjkc7= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].yhjmje;
				}

				if((swjgDm3!='137' || swjgDm5=='13702') && snnbXsyffyjjkcyhbz=='Y' && jjkc1==0 && jjkc2==0 && jjkc3==0 && jjkc4==0 && yf_z==9 && (nf_z<2022 || (nf_z>=2022 && jjkc5==0 && jjkc6==0) ) ){
			 		flag=true;
			 		var xh=index>0?(index+1)+"、":"";
			 		if(swjgDm5=='13502' && nf_z>=2022) {
                        msg+=xh+"你公司上一年度企业所得税纳税申报享受了研发费用加计扣除优惠，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照规定享受研发费用加计扣除优惠。</br></br>";
                    }else{
                        msg+=xh+"你公司上一年度企业所得税纳税申报享受了研发费用加计扣除优惠，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。</br></br>";
                    }
					index++;
				}


				if(swjgDm5!="13502" && swjgDm3!="137"&&(gxjsqyyh1 >0 || gxjsqyyh2>0) && jjkc1==0 && jjkc2==0 && jjkc3==0 && jjkc4==0 && yf_z==9 && (nf_z<2022 || (nf_z>=2022 && jjkc5==0 && jjkc6==0) ) ){
					flag=true;
					var xh=index>0?(index+1)+"、":"";
				 	msg+=xh+"你公司申报享受了高新技术企业所得税优惠，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。</br></br>";
				 	index++;
				}
				 if(swjgDm5=='13502' && yf_z==9 && nf_z==2021){
					//厦门白名单标志
					var gxjsBmdBz = parent.formData.kz_.bmdBzxx.gxjsBmdBz;
					if(gxjsBmdBz=='Y' && jjkc1==0 && jjkc2==0 && jjkc3==0 && jjkc4==0){
						flag=true;
                        var xh=index>0?(index+1)+"、":"";
                        msg+=xh+"你公司属于高新技术企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。</br></br>";
                        index++;
					}
				 }else if(swjgDm5=='13502' && yf_z==9 && nf_z>=2022){
				 	//高新技术白名单
					var gxjsBmdBz = parent.formData.kz_.bmdBzxx.gxjsBmdBz;
                    //技术先进型白名单
                    if(gxjsBmdBz=='Y' && jjkc1==0 && jjkc2==0 && jjkc3==0 && jjkc4==0 && jjkc5==0 && jjkc6==0){
                    	flag=true;
                    	var xh=index>0?(index+1)+"、":"";
                    	msg+=xh+"你公司属于高新技术企业或技术先进型服务企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照规定享受研发费用加计扣除优惠。</br></br>";
                    	index++;
                    }
				 }


			    var  hydm= parent.formData.fq_.nsrjbxx.hydm;
			    var  hymc= parent.formData.fq_.nsrjbxx.hymc;
			    var  hylb= parent.formData.kz_.temp.zb.hylb;
			    var jjkc1Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrsxSelect;
				var jjkc2Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrsxSelect;
				var jjkc3Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrsxSelect;
				var jjkc4Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrsxSelect;
                if(nf_z>=2022){
                    var jjkc5Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrsxSelect;
                    var jjkc6Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrsxSelect;
                    var jjkc7Select= parent.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrsxSelect;
				}

                var hyVerify=false;
			    var txVerify=false;
			    var msgstr="";

				//6大禁止行业
				if (hydm>=1600&&hydm<=1699  || hydm>=5100&&hydm<=5299  || hydm>=6100&&hydm<=6299  || hydm>=7000&&hydm<=7099 || hydm>=7100&&hydm<=7299 || hydm>=9000&&hydm<=9099  ){
					hyVerify=true;
				}

				if(jjkc1>0 || jjkc1Select==true ){
					txVerify=true;
					msgstr=parent.isNull(msgstr)?"JJKC011":msgstr+"、JJKC011";
				}
				if(jjkc2>0 || jjkc2Select==true ){
					txVerify=true;
					msgstr=parent.isNull(msgstr)?"JJKC012":msgstr+"、JJKC012";
				}
				if((jjkc3>0 || jjkc3Select==true) && (swjgDm3 == "111" || swjgDm5 == "13502")){
					txVerify=true;
					msgstr=parent.isNull(msgstr)?"JJKC021":msgstr+"、JJKC021";
				}
				if((jjkc4>0 || jjkc4Select==true) && (swjgDm3 == "111" || swjgDm5 == "13502")){
					txVerify=true;
					msgstr=parent.isNull(msgstr)?"JJKC022":msgstr+"、JJKC022";
				}
				if(nf_z>=2022){
                    if(jjkc5>0 || jjkc5Select==true ){
                        txVerify=true;
                        msgstr=parent.isNull(msgstr)?"JJKC013":msgstr+"、JJKC013";
                    }
                    if((swjgDm5 == "13502" || swjgDm3 == '111')  && (jjkc6>0 || jjkc6Select==true) ){
                        txVerify=true;
                        msgstr=parent.isNull(msgstr)?"JJKC023":msgstr+"、JJKC023";
                    }

                    if(jjkc7>0 || jjkc7Select==true){
                        flag=true;
                        var xh=index>0?(index+1)+"、":"";
                        msg +=xh+"JJKC031行填报企业出资给非营利性研究开发机构、高等学校和政府性科学基金用于基础研究的支出加计扣除金额，请确认是否符合政策条件。</br></br>"
                        index++;
                    }
				}

				if (swjgDm5 != "13502" &&  swjgDm5 != "13702" && swjgDm3 != "111" && swjgDm3 !='137' &&  hyVerify  && txVerify ) {
					flag=true;
					var xh=index>0?(index+1)+"、":"";
					msg +=xh+ "你公司行业代码显示为"+ hydm+ "，属于"+ hylb+ "，税收规定不适用研发费用加计扣除的行业，不应填报"+msgstr+"行。如税务登记信息有误，请您及时修正。</br></br>";
					index++;
				}else if( (swjgDm5 == "13502" || (swjgDm3 =='137'&& swjgDm5 != "13702" && yf_z==9 ) || (swjgDm3 == '111' && nf_z>=2022 && jjkc1Select!=true && jjkc2Select!=true && jjkc3Select!=true && jjkc4Select!=true) ) && hyVerify  && txVerify ){
					//北京仅有JJKC013 、 JJKC023弹出此阻断校验
					flag=true;
					var tipsmsg = "你公司行业代码显示为"+ hydm+ "，属于"+ hylb+ "，税收规定不适用研发费用加计扣除的行业，不应填报"+msgstr+"行。如税务登记信息有误，请您及时修正。";
					var verityIndex = parent.layer.open({
						type: 1,
						area:['448px', '300px'],
						skin: 'iconStyle',
						title:"提示",
						scrollbar: false,
						closeBtn: false,
						content: tipsmsg,
						btn: ['确定'],
						yes: function(index,layero){
							parent.layer.close(verityIndex);
						}
					});
					return;
				}else if(swjgDm3 == "111" && hyVerify  && txVerify && yf_z==9 && nf_z >=2021){
					//仅2021年第三季度或9月份生效
                    flag=true;
                    var tipsmsg = "尊敬的纳税人您好，您企业所属行业属于烟草制造业、住宿和餐饮业、批发和零售业、房地产业、租赁和商务服务业、娱乐业，按照财税〔2015〕119号文规定，将不得享受研发费用加计扣除优惠政策。若您企业所属行业信息有误或有变化，请先退出申报界面，通过电子税务局变更行业信息后再进行申报。";
                    var verityIndex = parent.layer.open({
                        type: 1,
                        area:['448px', '300px'],
						skin: 'iconStyle',
                        title:"提示",
                        scrollbar: false,
                        closeBtn: false,
                        content: tipsmsg,
                        btn: ['确定'],
                        yes: function(index,layero){
                            parent.layer.close(verityIndex);
                        }
                    });
                    return;
                }


				var zzyhyVerify=false;
				var fzzyhyVerify=false;
				var zzytsVerify=false;
				var fzzytsVerify=false;
				var fzzytscysjVerify=false;
                var zzytscysjVerify=false;
				var zzyMsgstr="";
				var fzzyMsgstr="";
				var fzzycysjMsgstr="";
				var zzycysjMsgstr="";
				if(hydm>=1300&&hydm<=4390){
					zzyhyVerify=true;
				}else{
					fzzyhyVerify=true
				}

				 if(jjkc1>0 || jjkc1Select==true ){
						fzzytsVerify=true;
						fzzyMsgstr=parent.isNull(fzzyMsgstr)?"JJKC011":fzzyMsgstr+"、JJKC011";
					}

				if(jjkc2>0 || jjkc2Select==true ){
					zzytsVerify=true;
					zzyMsgstr=parent.isNull(zzyMsgstr)?"JJKC012":zzyMsgstr+"、JJKC012";
				}

				 if(jjkc3>0 || jjkc3Select==true ){
					 fzzytscysjVerify=true;
					 fzzycysjMsgstr=parent.isNull(fzzycysjMsgstr)?"JJKC021":fzzycysjMsgstr+"、JJKC021";
					}

				if(jjkc4>0 || jjkc4Select==true ){
                    zzytscysjVerify=true;
                    zzycysjMsgstr=parent.isNull(zzycysjMsgstr)?"JJKC022":zzycysjMsgstr+"、JJKC022";
				}

                if(nf_z>=2022){
                    //上一年度汇缴申报表的《基础信息表》“210科技型中小企业”的“210-3 年（所属期下一年度）入库编码2”
                    var sqsnnbRkbh2 = parent.formData.kz_.temp.zb.sqsnnbRkbh2;
                    var zdmsg="";
                    var zdindex=0;
                    if(sqsnnbRkbh2==''&& (jjkc5Select || jjkc5>0)){
                        flag=true;
                        var xh=index>0?(index+1)+"、":"1、";
                        index++;
                        msg += xh+"JJKC013行填报科技型中小企业开展研发活动发生的研究开发费用加计扣除金额，制造业企业、其他企业研发费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";
                    }
                    if(sqsnnbRkbh2==''&& (jjkc6Select || jjkc6>0) ){
                        flag=true;
                        var xh=index>0?(index+1)+"、":"1、";
                        index++;
                        msg += xh+"JJKC023行填报科技型中小企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额，制造业企业、其他企业进行创意设计活动而发生的相关费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";
                    }

                    //全国版阻断行校验
                    var contents = "";
                    if(yf_z>=9 && zzyhyVerify==true && (jjkc5Select || jjkc5>0) ){
						contents += "JJKC013行填报非制造业的科技型中小业企业开展研发活动发生的研究开发费用加计扣除金额，您税务登记行业是制造业，请选择“企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除（制造业按100%加计扣除）”项目填报。</br></br>";
					}
					if(yf_z>=9 && zzyhyVerify==true && (jjkc6Select || jjkc6>0)){
                        contents += "JJKC023行填报非制造业的科技型中小业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额，您税务登记行业是制造业，请选择“企业为获得创新性、创意性、突破性的产品进行创意设计活动发生的相关费用加计扣除（制造业按100%加计扣除）”项目填报。</br>";
					}
					if(!isEmptyObject(contents)){
                        var a = parent.layer.open({
                            type: 1,
                            area: ['400px', '300px'],
                            title: "提示",
                            scrollbar: false,
                            closeBtn: false,
                            content: contents,
                            btn: ['确定'],
                            yes: function (index, layero) {
                                parent.layer.close(a);
                            }
                        });
                        return;
					}

                    if(zzyhyVerify==true || !isEmptyObject(sqsnnbRkbh2)){
                    	if(swjgDm5=="13502"){
                            if(jjkc2Select || jjkc2>0){
                                var xh=zdindex>0?(zdindex+1)+"、":"1、";
                                zdindex++;
                                zdmsg += xh+"JJKC012行填报其他企业开展研发活动发生的研究开发费用75%加计扣除金额，制造业企业、科技型中小企业研发费用研发费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";
                            }
                            if(jjkc4Select || jjkc4>0){
                                var xh=zdindex>0?(zdindex+1)+"、":"1、";
                                zdindex++;
                                zdmsg += xh+"JJKC022行填报其他企业获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除，制造业企业、科技型中小企业获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";
                            }
						}else{
                            if(jjkc2Select || jjkc2>0){
                                flag=true;
                                var xh=index>0?(index+1)+"、":"1、";
                                index++;
                                msg += xh+"JJKC012行填报其他企业开展研发活动发生的研究开发费用75%加计扣除金额，制造业企业、科技型中小企业研发费用研发费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";

                            }
                            if(jjkc4Select || jjkc4>0){
                                flag=true;
                                var xh=index>0?(index+1)+"、":"1、";
                                index++;
                                msg += xh+"JJKC022行填报其他企业获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除，制造业企业、科技型中小企业获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额不在本行填报，请确认填报是否准确。</br></br>";

                            }
						}
						if(!isEmptyObject(zdmsg)){
                            var f = parent.layer.open({
                                type: 1,
                                area: ['400px', '300px'],
                                title: "提示",
                                scrollbar: false,
                                closeBtn: false,
                                content: zdmsg,
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    parent.layer.close(f);
                                }
                            });
                            return;
						}
					}
                }

				 if (swjgDm5 != "13702" &&  zzyhyVerify  && zzytsVerify ) {
				 	var verifyMsg="";
					if(nf_z<2022){
                        flag=true;
                        verifyMsg="根据登记信息行业，申报企业属于制造业，"+zzyMsgstr+"行填报非制造业企业开展研发活动发生的研究开发费用75%加计扣除金额，制造业研发费用100%加计扣除金额不在"+zzyMsgstr+"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。</br></br>";
					}
					 //XMDZSWJ-2645
					if(swjgDm5 == "13502"&& nf_z==2021 && yf_z>9){
					 	flag=true;
						verifyMsg="根据登记信息行业，申报企业属于制造业，"+zzyMsgstr+"行填报非制造业企业开展研发活动发生的研究开发费用75%加计扣除金额，制造业研发费用100%加计扣除金额不在"+zzyMsgstr+"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。如你公司上季度申报数据有误，请先行更正上季度申报表。";
					}

					 if(swjgDm5 != "13502" && swjgDm3 != "137" && swjgDm3 != "111") {
						 //全国版为提示性校验
						 if(verifyMsg!=''){
                             var xh=index>0?(index+1)+"、":"";
                             index++;
                             msg += xh+verifyMsg;
						 }
					 }else {
						 //厦门、山东、北京个性化为强制性校验
						 //北京个性化提示语不一样，并且只在2021年9月进行提示
						 if(swjgDm3 === "111") {
							 verifyMsg="尊敬的纳税人您好，您企业所业属于制造业，应享受制造业研发费用100%加计扣除优惠政策，目前选择为非制造业优惠政策，请您确认填报研发费用加计扣除是否准确。若您企业行业信息有误或有变化，请先退出申报界面，通过电子税务局变更行业信息后再进行申报。";
							 if(!(yf_z==9 && nf_z >=2021)) {
								 flag=false;
							 }
						 }
						 if((swjgDm3 === "111" && yf_z==9 && nf_z >=2021) || swjgDm5 == "13502" ||( swjgDm3 == "137"&& yf_z==9)) {
							 var f = parent.layer.open({
								 type: 1,
								 area: ['400px', '300px'],
								 title: "提示",
								 scrollbar: false,
								 closeBtn: false,
								 content: verifyMsg,
								 btn: ['确定'],
								 yes: function (index, layero) {
									 parent.layer.close(f);
								 }
							 });
							 return;
						 }
					 }

				 }

				 if (swjgDm5 != "13702" &&  fzzyhyVerify  && fzzytsVerify ) {
					 flag=true;
					 var verifyMsg=fzzyMsgstr+"行填报制造业企业开展研发活动发生的研究开发费用加计扣除金额，"+(nf_z>=2022?"科技型中小企业、其他企业研发费用加计扣除金额不在":"其他行业研发费用加计扣除金额不在")+fzzyMsgstr+"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。</br></br>";
					 if(swjgDm5 != "13502" && swjgDm3 != "137" && swjgDm3 != "111") {
						 //全国版为提示性校验
						  var xh=index>0?(index+1)+"、":"";
							index++;
						 msg +=xh + verifyMsg;
					 }else {
						 //厦门、山东、北京个性化为强制性校验
						 //北京个性化提示语不一样，并且只在2021年9月进行提示
						 if(swjgDm3 === "111") {
							 verifyMsg="尊敬的纳税人您好，您企业所属行业不属于制造业，不应享受研发费用100%加计扣除优惠政策，若您企业所属行业信息有误或有变化，请先退出申报界面，通过电子税务局变更行业信息后再进行申报。";
							 if(!(yf_z==9 && nf_z >=2021)) {
								 flag=false;
							 }
						 }
						 if((swjgDm3 === "111" && yf_z==9 && nf_z >=2021) || swjgDm5 == "13502" || (swjgDm3 == "137"&& yf_z==9)) {
							 var f = parent.layer.open({
								 type: 1,
								 area: ['400px', '300px'],
								 title: "提示",
								 scrollbar: false,
								 closeBtn: false,
								 content: verifyMsg,
								 btn: ['确定'],
								 yes: function (index, layero) {
									 parent.layer.close(f);
								 }
							 });
							 return;
						 }
					 }
					 if(swjgDm3 == "137"&& yf_z != 9){
						 flag=false;
					 }
				 }

				 if (swjgDm5 != "13702" &&  fzzyhyVerify  && fzzytscysjVerify ) {
					 flag=true;
					 var verifyMsg=fzzycysjMsgstr+"行填报制造业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额，其他行业相关费用加计扣除金额不在"+fzzycysjMsgstr+"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。</br></br>";
					 if(swjgDm5 == "13502" && nf_z >=2022){
                         verifyMsg=fzzycysjMsgstr+"行填报制造业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额，科技型中小企业、其他企业进行创意设计活动而发生的相关费用加计扣除金额不在"+fzzycysjMsgstr+"行填报，请确认填报是否准确。</br></br>";
					 }
					 if(swjgDm5 != "13502" && swjgDm3 != "137" && swjgDm3 != "111") {
					 	 //全国版为提示性校验
						  var xh=index>0?(index+1)+"、":"";
							index++;
						 msg += xh+verifyMsg;
					 } else {
					 	 //厦门、山东、北京个性化为强制性校验
						 //北京个性化提示语不一样，并且只在2021年9月进行提示
						 if(swjgDm3 === "111") {
							 verifyMsg="尊敬的纳税人您好，您企业所属行业不属于制造业，不应享受研发费用100%加计扣除优惠政策，若您企业所属行业信息有误或有变化，请先退出申报界面，通过电子税务局变更行业信息后再进行申报。";
							 if(!(yf_z==9 && nf_z >=2021)) {
								 flag=false;
							 }
						 }
						 if((swjgDm3 === "111" && yf_z==9 && nf_z >=2021) || swjgDm5 == "13502" || (swjgDm3 == "137"&& yf_z==9)) {
							 var e = parent.layer.open({
								 type: 1,
								 area:['400px', '300px'],
								 title:"提示",
								 scrollbar: false,
								 closeBtn: false,
								 content: verifyMsg,
								 btn: ['确定'],
								 yes: function(index,layero){
									 parent.layer.close(e);
								 }
							 });
							 return;
						 }
					 }
					 if(swjgDm3 == "137"&& yf_z != 9){
						 flag=false;
					 }
				 }
                if (swjgDm5 != "13702" &&  zzyhyVerify  && zzytscysjVerify ) {
                    flag=true;
                    var verifyMsg="根据登记信息行业，申报企业属于制造业，"+zzycysjMsgstr+"行填报非制造业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用75%加计扣除金额，制造业相关费用100%加计扣除金额不在"+zzycysjMsgstr+"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。</br></br>";
                    if(swjgDm5 != "13502" && swjgDm3 != "137" && swjgDm3 != "111") {
                    	if(nf_z>=2022){
                            flag=false;
                            verifyMsg='';
						}else{
                            //全国版为提示性校验
                            var xh=index>0?(index+1)+"、":"";
                            index++;
                            msg += xh+verifyMsg;
						}
                    }else {
                        //厦门、山东、北京个性化为强制性校验
                        //北京个性化提示语不一样，并且只在2021年9月进行提示
                        if(swjgDm3 === "111") {
                            verifyMsg="尊敬的纳税人您好，您企业所业属于制造业，应享受制造业研发费用100%加计扣除优惠政策，目前选择为非制造业优惠政策，请您确认填报研发费用加计扣除是否准确。若您企业行业信息有误或有变化，请先退出申报界面，通过电子税务局变更行业信息后再进行申报。";
							if(!(yf_z==9 && nf_z >=2021)) {
								flag=false;
							}
                        }
						if((swjgDm3 === "111" && yf_z==9 && nf_z >=2021) || swjgDm5 == "13502" || (swjgDm3 == "137"&& yf_z==9)) {
							var f = parent.layer.open({
								type: 1,
								area: ['400px', '300px'],
								title: "提示",
								scrollbar: false,
								closeBtn: false,
								content: verifyMsg,
								btn: ['确定'],
								yes: function (index, layero) {
									parent.layer.close(f);
								}
							});
							return;
						}
                    }
                    if(swjgDm3 == "137"&& yf_z != 9){
						 flag=false;
					 }
                }
                //山东个性化
                if(swjgDm3 === "137" && swjgDm5 != "13702") {
					var msgVo = parent.dsjdwxsyffyjjkcyhVerify();
					if(msgVo.isTs==='Y') {
						flag=true;
						var xh=index>0?(index+1)+"、":"";
						msg += xh+ msgVo.msg +"</br></br>";
						index++;
					}
				}
			}
			//营业收入
			var yysrLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.yysrLj;
			if(mssrGridlb[16].yhjmje>yysrLj*0.1){
				flag=true;
				var xh=index>0?(index+1)+"、":"";
				msg += xh+ "您填写的减计收入额大于营业收入的10%，请确认填写金额是否准确" +"</br></br>";
				index++;
			}

			if (!isEmptyObject(msg)) {
				var xh=index>1?(1)+"、":"";
				if(!isStartWith(msg,"1、")){
                    msg=xh+msg;
				}
				var e= layer.confirm(msg, {
					type:1,
					area: ['600px'],
					title: '提示',
					btn: ['确定', '取消'],
					btn2: function (index) {
						layer.close(e);
						return ;
					}
				}, function (index) {
					layer.close(e);
					layer.close(mssrsxIndex);
				});
			}

			 if( ! flag ){
				 layer.close(mssrsxIndex);
			 }

        }
    });
}



/**
 * 选择所得减免优惠事项
 *
 */
function txSdjmsx(scope){
	parent.formData.fq_.sdjmyhsxSfxg = "Y";
    var sbqylx =  scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if(sbqylx == 2){
        parent.layer.confirm("您的企业类型为“跨地区经营汇总纳税企业分支机构”，第8行优惠事项不允许填写",{
            title:'提示',
            btn : ['确定']
        },function(index){
            parent.layer.close(index);
        });
        return ;
    }
	var sdjmsxIndex = layer.open({
        type: 1,
        area: ['850px','500px'],
        title:['所得减免类型选项表'],
        scrollbar: false,
        closeBtn: false,
        content:$("#sdjmsxTable"),
        btn: [ '确定'],
        btn1: function(index,layero){
        	var sdjmGridlb = scope.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb;
        	var j=1;
        	if(sdjmGridlb!=null && sdjmGridlb.length>0){
        		for(var i=0;i<sdjmGridlb.length;i++){
        			var sdjmbnljje = sdjmGridlb[i].yhjmje;
        			if(sdjmbnljje<0){
        				layer.alert("本年累计金额应大于等于0！");
        				return ;
        			}
        			var sdjmsxSelect = sdjmGridlb[i].t_sdjmsxSelect;
        			if(sdjmsxSelect){
						parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].t_nxh = j;
						j++;
        				parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].t_sdjmSfyxz = "Y";
        			}else{
        				parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].t_sdjmSfyxz = "N";
        			}
        		}
        		// 全国版提示(厦门除外)
        		var swjgDm5 = (parent.formData.fq_.nsrjbxx.swjgDm).substring(0,5);
        		if(swjgDm5!="13502"){
					if(sdjmGridlb[5].t_sdjmsxSelect&&sdjmGridlb[6].t_sdjmsxSelect){
						parent.layer.confirm("SD042符合条件的中关村国家自主创新示范区特定区域技术转让项目所得减免征收企业所得税不能与SD041符合条件的一般技术转让项目所得减免征收企业所得税同时选择！",{
							title:'提示',
							icon: 7,
							btn : ['确定']
						},function(index){
							parent.layer.close(index);
						});
						return ;
					}
				}
        	}
        	parent.formData.fq_.sdjmyhsxSfxg = "N";
			var _jpath = "ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[#].t_nxh";
			parent.formulaEngine.apply(_jpath, "");
			viewEngine.tipsForVerify(document.body);
			viewEngine.formApply($('#viewCtrlId'));
            layer.close(sdjmsxIndex);
        }
    });
}

/**
 * 选择减免所得税优惠事项
 *
 */
function txJmsdssx(scope){
	var sjlreLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;// 第10行
    var sbqylx =  scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if(sbqylx == 2){
        parent.layer.confirm("您的企业类型为“跨地区经营汇总纳税企业分支机构”，第13行优惠事项不允许填写",{
            title:'提示',
			area: ['448px','244px'],
			icon:6,
            btn : ['确定']
        },function(index){
            parent.layer.close(index);
        });
        return ;
    }
	if(sjlreLj<=0){
		parent.layer.confirm("尊敬的纳税人，主表第10行实际利润额为【"+sjlreLj+"】小于等于0时，无需填写优惠事项！如需填写优惠事项，请修改主表第10行为大于0的数据。",{
			title:'提示',
			area: ['448px','200px'],
			icon:6,
			btn : ['确定']
		},function(index){
			parent.layer.close(index);
		});
		return ;
	}
	//判断是否需要选中第一行
	var sfyjmsdsyh = scope.formData.fq_.sfyjmsdsyh;
    var gzbz = scope.formData.kz_.temp.gzsbbz;
	if("N"==sfyjmsdsyh && gzbz != "Y"){
		if(sjlreLj>0){
			//再判断是否是小微企业
			var sfsyxxwlqy = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
			if("Y"==sfsyxxwlqy){
				scope.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
				var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect";
				parent.formulaEngine.apply(_jpath, true);
				viewEngine.tipsForVerify(document.body);
				viewEngine.formApply($('#viewCtrlId'));
			}
		} else {
			scope.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = false;
			var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect";
			parent.formulaEngine.apply(_jpath, false);
			viewEngine.tipsForVerify(document.body);
			viewEngine.formApply($('#viewCtrlId'));
		}
	}
	// 第13.36以下纳税人识别号自动选中：12100000MB0111288J、12100000MB1A69033G
	if(scope.formData.fq_.nsrjbxx.nsrsbh=='12100000MB0111288J' || scope.formData.fq_.nsrjbxx.nsrsbh=='12100000MB1A69033G'){
		scope.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[35].t_jmsdssxSelect = true;
		var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[35].t_jmsdssxSelect";
		parent.formulaEngine.apply(_jpath, true);
		viewEngine.tipsForVerify(document.body);
		viewEngine.formApply($('#viewCtrlId'));
	}
	//厦门个性化信息
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	if("Y"==qygxhXmBz){
        viewEngine.tipsForVerify(document.body);
        viewEngine.formApply($('#viewCtrlId'));
	}
	parent.formData.fq_.jmsdsyhsxSfxg = "Y";
    parent.formData.kz_.temp.showjmsdslist = "Y";
    var jpathList = [];
    jpathList.push("fq_.jmsdsyhsxSfxg");
    jpathList.push("kz_.temp.showjmsdslist");

    if(jpathList.length > 0){
        parent.formulaEngine.apply4List(jpathList);
        viewEngine.formApply($('#viewCtrlId'));
        viewEngine.tipsForVerify(document.body);
    }

	var jmsdssxIndex = layer.open({
        type: 1,
        area: ['850px','500px'],
        title:['减免所得税类型选项表'],
        scrollbar: false,
        closeBtn: false,
        content:$("#jmsdssxTable"),
        btn: [ '确定'],
        btn1: function(index,layero){
					var nopass = parent.formulaEngine.idxVariable2NoPass;
					if (nopass['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].ssjmxzDm']) {
						top.$vue.$gtDialog.error({
								header:'提示',
								body: nopass['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].ssjmxzDm']['gt4_20053_01_0058'].tips,
								closeOnOverlayClick: false,
								closeOnEscKeydown: false,
						});
						return;
					}
        	var tipsMsg = jyJmsdsyhsxSftg('tips');
        	if(tipsMsg!=null && tipsMsg!=undefined && tipsMsg!=""){
        		layer.alert(tipsMsg);
				return ;
        	}
        	tipsMsg = scope.formData.kz_.zbqtxx.jmsdsyhsxxx.errorMsg;
        	if(tipsMsg!=null && tipsMsg!=undefined && tipsMsg!=""){
        		layer.alert(tipsMsg);
				return ;
        	}
        	// 北京个性化
        	jyJmsdsyhsxSftg_BJ(scope);
        	// 厦门个性化
        	jyJmsdsyhsxSftg_XM(scope);

        	//重新给个序号展示
        	var jmsdGridlb = parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
        	if(jmsdGridlb!=null && jmsdGridlb.length>0){
				var j=1;
				for(var i=0;i<jmsdGridlb.length;i++){
					if(jmsdGridlb[i].t_jmsdssxSelect!=null && jmsdGridlb[i].t_jmsdssxSelect){
						parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_nxh = j;
						j++;
					}
				}
			}

        	parent.formData.fq_.sfyjmsdsyh = "Y";
        	parent.formData.fq_.jmsdsyhsxSfxg = "N";
            parent.formData.kz_.temp.showjmsdslist = "N";
            jpathList.push("ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].yhjmje");

            if(jpathList.length > 0){
                parent.formulaEngine.apply4List(jpathList);
                viewEngine.formApply($('#viewCtrlId'));
                viewEngine.tipsForVerify(document.body);
            }

            layer.close(jmsdssxIndex);
        }
    });
}

/*
 *  北京个性化校验
 */
function jyJmsdsyhsxSftg_BJ(){
	var swjgDm3 = (parent.formData.fq_.nsrjbxx.swjgDm).substring(0,3);
	var sfsyxxwlqy = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
    var plsbbz = parent.location.href.indexOf('isPlLsb')>-1 || parent.isJmsb();
	if(swjgDm3 == "111" && "Y"==sfsyxxwlqy){
		var jmsdGridlb = parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
		// 没有勾选小微
		if(jmsdGridlb[0].t_jmsdssxSelect == false){
			var sjlreLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;// 第10行
			var ynsdseLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.ynsdseLj;// 第12行
			var yhjmje = 0;
			if(sjlreLj>0&&sjlreLj<=1000000){
				yhjmje = parent.ROUND(ynsdseLj*0.9,2);
			} else if(sjlreLj>1000000&&sjlreLj<=3000000){
				yhjmje = parent.ROUND(ynsdseLj*0.6+75000,2);
			}
			if(yhjmje>0){
				var qtyhBnljje = 0;
				for(var i=0;i<jmsdGridlb.length;i++){
					if("JMSE00100"!=jmsdGridlb[i].yhswsx && jmsdGridlb[i].t_jmsdssxSelect==true){
						qtyhBnljje = qtyhBnljje + jmsdGridlb[i].yhjmje;
					}
				}
				if(yhjmje>qtyhBnljje && !plsbbz){
					parent.layer.confirm("尊敬的纳税人，企业既符合小型微利企业所得税优惠，又符合该表其他优惠条件的，系统将自动默认选择最优惠政策！",{
						title:'提示',
						icon: 6,
						btn : ['确定']
					},function(index){
						parent.layer.close(index);
					});
					parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
					var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect";
					parent.formulaEngine.apply(_jpath, '');
					for(var i=1;i<jmsdGridlb.length;i++){
						if("JMSE00100"!=jmsdGridlb[i].yhswsx){
							parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_jmsdssxSelect = false;
							_jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb["+i+"].t_jmsdssxSelect";
							parent.formulaEngine.apply(_jpath, '');
						}
					}
					viewEngine.tipsForVerify(document.body);
					viewEngine.formApply($('#viewCtrlId'));
				}
			}
		}
	}
}

/*
 *  厦门个性化校验
 */
function jyJmsdsyhsxSftg_XM(scope){
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	if("Y"==qygxhXmBz){
		var sjlreLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;// 第10行
		var ynsdseLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.ynsdseLj;// 第12行
		var jmsdseLj = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.jmsdseLj;// 第13行
		var skssqq  = Number(scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq.split("-")[1]);// 属期起
		var skssqznd = Number(scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz.split("-")[0]);//属期止(年)
		var skssqz = Number(scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz.split("-")[1]);// 属期止(月)
		var sfsyxxwlqy = scope.formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;// 小型微利企业
		var sfsyxxwlqyId = $("#sfsyxxwlqyId").is(":checked");//减免选项 JMSE00100
        var plsbbz = parent.location.href.indexOf('isPlLsb')>-1 || isJmsb();//批量申报或静默申报
		var cfBz = false;

		if(skssqznd>2021||(skssqznd==2021&&skssqz>=3)){
			/*
			* 2021第一季度或3月及以后属期适用
			* 1.当主表0.02＜A200000第10行≤1000000（100万）时，且主表A200000按季度填报信息“小型微利企业”选择“是”时，
			* 主表第13行≠主表第12行，且减免税选项表里没有选  JMSE00100 ,且0＜13行＜A200000第10行*22.5%时
			*/
			if(skssqz-skssqq>=2 && sjlreLj>0.02 && sjlreLj<=1000000 && sfsyxxwlqy == "Y" && jmsdseLj!=ynsdseLj && (!sfsyxxwlqyId) && jmsdseLj>0 && (jmsdseLj<parent.ROUND(sjlreLj*0.225,2))){
				cfBz = true;
			}
			/*
			* 2021第一季度或3月及以后属期适用
			* 2.当主表1000000（100万）＜A200000第10行≤3000000（300万），且主表A200000按季度填报信息“小型微利企业”选择“是”时，主表第13行≠主表第12行，
			* 且减免税选项表里没有选代码0404992（JMSE00100）,且0＜13行＜A200000第10行*15%+75000时
			* 满足上述任一条件，用户点击《所得减免类型选项表》“确定”按钮，进行提示。
			*/
			if(skssqz-skssqq>=2 && sjlreLj>1000000 && sjlreLj<=3000000 && sfsyxxwlqy == "Y" && jmsdseLj!=ynsdseLj && (!sfsyxxwlqyId) && jmsdseLj>0 && (jmsdseLj<(parent.ROUND((sjlreLj*0.15),2)+75000))){
				cfBz = true;
			}
		}else{
			/*
			* 2021第一季度或3月及之前属期适用
			* 1.当主表0.02＜A200000第10行≤1000000（100万）时，且主表A200000按季度填报信息“小型微利企业”选择“是”时，
			* 主表第13行≠主表第12行，且减免税选项表里没有选  JMSE00100 ,且0＜13行＜主表第12行*90%；
			*/
			if(skssqz-skssqq>=2 && sjlreLj>0.02 && sjlreLj<=1000000 && sfsyxxwlqy == "Y" && jmsdseLj!=ynsdseLj && (!sfsyxxwlqyId) && jmsdseLj>0 && (jmsdseLj<parent.ROUND(ynsdseLj*0.9,2))){
				cfBz = true;
			}
			/*
			* 2021第一季度或3月及之前属期适用
			* 2.当主表1000000（100万）＜A200000第10行≤3000000（300万），且主表A200000按季度填报信息“小型微利企业”选择“是”时，主表第13行≠主表第12行，
			* 且减免税选项表里没有选代码0404992（JMSE00100）,且0＜13行＜主表12行*60%+7.5万；
			* 满足上述任一条件，用户点击《所得减免类型选项表》“确定”按钮，进行提示。
			*/
			if(skssqz-skssqq>=2 && sjlreLj>1000000 && sjlreLj<=3000000 && sfsyxxwlqy == "Y" && jmsdseLj!=ynsdseLj && (!sfsyxxwlqyId) && jmsdseLj>0 && (jmsdseLj<(parent.ROUND((ynsdseLj*0.6),2)+75000))){
				cfBz = true;
			}
		}

		if(cfBz && ! plsbbz){
            parent.layer.confirm("尊敬的纳税人，根据您填报的数据，您可以享受更为优惠的小型微利企业优惠政策。",{
                title:'提示',
                icon: 6,
                area: ['350px','240px'],
                closeBtn:false,
                btn : '确定',
                yes: function(index,layero){
                    // 选择小微优惠按钮
                    parent.layer.close(index);
                    /*// 重新打开选择框
                    txJmsdssx(scope);*/

                    var jmsdGridlb =parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
                    for(var i=0;i<jmsdGridlb.length;i++){
                        parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_jmsdssxSelect = false;
					}
                    parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
                    var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[#]";
                    parent.formulaEngine.apply4ParentNode(_jpath);
                    _jpath = "ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy";
                    parent.formulaEngine.apply(_jpath, '');
                    // 新校验结果和控制结果
                    viewEngine.tipsForVerify(document.body);
                    viewEngine.formApply($('#viewCtrlId'));
                    // 进行保存操作
                    saveJyJmsdsyhsxxx(scope,"Y","N");
                }
            });
		}else{
			//判断白名单内是否有选择
			var jmsdGridlb = parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
			var jmsdssxSelect = false;
			if(!isEmptyObject(jmsdGridlb)){
				for(var i=0;i<jmsdGridlb.length;i++){
					jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
					if(jmsdssxSelect){
						break ;
					}
				}
			}
			if(jmsdssxSelect==false && sjlreLj>0){
				var	gxjsBmdBz = parent.formData.kz_.bmdBzxx.gxjsBmdBz;
				var sfbmdYhtsy = "";
				if("Y"==gxjsBmdBz){
					sfbmdYhtsy = "您是高新技术企业白名单内，当前未享受任何税收优惠，请确认！";
				}
				if(!isEmptyObject(sfbmdYhtsy)){
					parent.layer.confirm(sfbmdYhtsy,{
						title:'提示',
						icon: 6,
						area: ['350px','240px'],
						cancel : function (index) {
							return false;
						},
						btn : ['返回修改','确认'],
						btn2:function(index){
							// 继续申报按钮
							parent.layer.close(index);
						}
					},function(index){
						// 选择小微优惠按钮
						parent.layer.close(index);
						// 重新打开选择框
						txJmsdssx(scope);
					});
				}
			}
		}
	}
}

function saveJyJmsdsyhsxxx(scope,xwyhBz,jxsbBz){
	var mainUrl = window.location.protocol+"//"+window.location.host+"/"+window.location.pathname.split("/")[1];
	var dataUrll = mainUrl + "/ywzt/getData.do?projectName=sbzx";
	var nsrsbh = scope.formData.fq_.nsrjbxx.nsrsbh;
	var djxh = scope.formData.fq_.nsrjbxx.djxh;
	var sssqQ = scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;// 属期起
	var sssqZ = scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;// 属期止
    //批量申报时弹框要频闭
    var plsbbz = parent.location.href.indexOf('isPlLsb')>-1 || isJmsb();
	var saveQqbw = {
		"sid":"dzswj.ywzz.sb.qysdsa21yjd.saveJyJmsdsyhsxxx",
		"nsrsbh": nsrsbh,
		"djxh": djxh,
		"sssqQ": sssqQ,
		"sssqZ": sssqZ,
		"xwyhBz": xwyhBz,
		"jxsbBz": jxsbBz
	};
	try {
		$.ajax({
			type : "POST",
			url : dataUrll,
			async:true,
			data : saveQqbw,
			dataType : "json",
			success : function(res){
				if (typeof res === "string") {
					res = JSON.parse(res);
				}
				if (res.rtnCode == "000"&&!plsbbz) {
					console.log("保存企业所得税月季报保存减免所得税类型选项信息成功！");
				} else {
					layer.alert(res.errInfo.msg);
					return;
				}
			},
			error:function(data){
				layer.alert("保存企业所得税月季报保存减免所得税类型选项信息失败，请重新加载再试!");
				layer.close(index);
			},
			complete:function(){
				layer.close(index);
			}
		});
	} catch(e){
		layer.alert("保存企业所得税月季报保存减免所得税类型选项信息失败，请重新加载再试!");
		layer.close(index);
	}
}

/**
 * 减免所得税优惠事项，判断是否有校验不通过
 * @param resultType 需要要获取提示语还是判断结果，默认是判断结果
 */
function jyJmsdsyhsxSftg(resultType){
	var _formData = parent.formData;
	var sfsyxxwlqy = _formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
	var sbqylx = _formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	var skssqq=_formData.fq_.sssq.sqQ;
	var skssqz=_formData.fq_.sssq.sqZ;
	 var skssqqDate = new Date(skssqq);
	 var skssqzDate = new Date(skssqz);
     var hbgDate = new Date('2022-01-01');
     var hbgDate2 = new Date('2022-09-30');
	var jmsdGridlb = _formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
	//判断第一行是否选择,如果是小微企业，并且其他未选择，则第一行必选
	var dihSfxz = jmsdGridlb[0].t_jmsdssxSelect;
	var flag = true;
	var tipsMsg = "";
	//厦门个性化信息
	var gxjsBmdBz = "N";
	var qygxhXmBz = _formData.fq_.qygxhXmBz;
	if("Y"==qygxhXmBz){
		gxjsBmdBz = _formData.kz_.bmdBzxx.gxjsBmdBz;
	}
	var sjlreLj = _formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;// 第10行
    var ynsdseLj = parent.formData.ht_.ywbw.A200000Ywbd.sbxx.ynsdseLj;// 第12行
	if("Y"==sfsyxxwlqy && dihSfxz==false && sjlreLj>0){
		var qtyhIsSelect = false;
		for(var i=0;i<jmsdGridlb.length;i++){
			if(i!=0){
				var t_jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
				if(t_jmsdssxSelect){
					qtyhIsSelect = true;
					break ;
				}

			}
		}
		if(!qtyhIsSelect){
			//其他优惠未选择
			flag = false;
			tipsMsg = "该企业符合小型微利企业优惠政策条件，在不享受其他优惠情况下，不得放弃享受小微优惠。[减免所得税类型选项表]第1行优惠为必选!";
			if("tips"==resultType){
				return tipsMsg;
			}
			return flag;
		}
	}
	var djxsyhFlag = true;
	//[减免所得税类型选项表]第1行至36行任一行填报数据大于0时，再填写第1行至36行中的其他行次时（
	//除第2行与第3行，第29行与3-12行、14-16行、18-21行、25行、34行），
	//提示：[减免所得税类型选项表]第1行至36行（除第2行与第3行，第29行与3-12行、
	//14-16行、18-21行、25行、34行）不能叠加享受减免所得税优惠，请准确选择优惠项目！

	//已选择的行次
	var ytxhc = new Array();
	if(jmsdGridlb!=null && jmsdGridlb!=undefined){
		for(var i=0;i<jmsdGridlb.length;i++){
			var t_jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
			if(t_jmsdssxSelect){
				var jmsdssxBnljje = jmsdGridlb[i].yhjmje;
				var yhswsx = jmsdGridlb[i].yhswsx;
				if(jmsdssxBnljje<0&&yhswsx!="JMSE00202"){
					return "[减免所得税类型选项表]本年累计金额应大于等于0！";
				}

				if(jmsdssxBnljje<=0&&yhswsx=="JMSE00202"){
					return "[经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税]本年累计金额应大于0！";
				}

				if(yhswsx=="JMSE00610" && jmsdssxBnljje > parent.ROUND(ynsdseLj*0.4,2)){
                    return "代码JMSE00610[本年累计金额]应大于等于0，且小于等于主表12行*40%【"+parent.ROUND(ynsdseLj*0.4,2)+"】";
				}

				var xh = jmsdGridlb[i].t_xh;
				ytxhc.push(xh);

				//判断是否符合新政策和原政策
				var xszc = parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc;
				var jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
				var jmsdssxlx = jmsdGridlb[i].t_jmsdssxlx;
				if("yyrjjcdlyhjxzx"==jmsdssxlx){
					if(xszc.indexOf("0")==-1){
						tipsMsg = "主表[附报事项]中Y01001选择“原政策” 时 ，才能勾选或填写"+yhswsx+"！";
					}
				}else if("JMSE00302B"==yhswsx || "JMSE00303B"==yhswsx || "JMSE00304B"==yhswsx || "JMSE00306B"==yhswsx || "JMSE00307B"==yhswsx || "JMSE00309B"==yhswsx){
					if(xszc.indexOf("1")==-1){
						tipsMsg = "主表[附报事项]中Y01001选择“新政策” 时 ，才能勾选或填写"+yhswsx+"！";
					}
				}else if("JMSE00301B"==yhswsx){
					if(xszc.indexOf("1")==-1){
						tipsMsg = "主表[附报事项]中Y01001选择“新政策” 时 ，才能勾选或填写"+yhswsx+"！";
					}
				}else if("JMSE00305B"==yhswsx || "JMSE00310B"==yhswsx){
					if(xszc.indexOf("1")==-1){
						tipsMsg = "主表[附报事项]中Y01001选择“新政策” 时 ，才能勾选或填写"+yhswsx+"！";
					}
				}
			}
		}
	}

	if("Y"==qygxhXmBz){
		/*
		 * 厦门的提示
		 * （1）当sbqylx=1时,强制提示：[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第29行与（第3-12行、14行、16-18行、20行-22行、25行、31行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目！
		 * （2）当sbqylx≠1时,强制提示：[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第29行与（第3-12行、14行、16-18行、20行-22行、25行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目！
		 * */
		//1.先判断是否只有2和3的
		if( (skssqqDate<hbgDate && ytxhc.length>3)|| (skssqqDate>=hbgDate && skssqzDate <hbgDate2  && ytxhc.length>5) || (skssqzDate>=hbgDate2 && ytxhc.length>6)  ){
			djxsyhFlag = false;
		}else if(  (skssqqDate<hbgDate &&ytxhc.length==3)     ||  (skssqqDate>=hbgDate && skssqzDate <hbgDate2 &&ytxhc.length>=3 && ytxhc.length<=5)  ||  (skssqzDate>=hbgDate2 &&ytxhc.length>=3 && ytxhc.length<=6) ) {
			for(var i=0;i<ytxhc.length;i++){
				var xh = ytxhc[i];
				if(skssqqDate<hbgDate){
					if(xh!=29){
						if(sbqylx!="1" || (xh!=30 && xh!=31)){
							djxsyhFlag = false;
						}
					}
				}else{
					if(xh!=27){
						if(sbqylx!="1" || (xh!=28 &&xh!=29 &&xh!=30 && xh!=31 && (xh!=32 || skssqzDate <hbgDate2 ) )){
							djxsyhFlag = false;
						}
					}
				}

			}
		}else if(ytxhc.length==2){
			//2.再判断选择2个的是否只有2和3的
			if(ytxhc.indexOf(2)>-1){
				if(ytxhc.indexOf(3)==-1){
					djxsyhFlag = false;
				}
			}else if(ytxhc.indexOf(3)>-1){
				if(skssqqDate<hbgDate ){
					if(ytxhc.indexOf(2)==-1&&ytxhc.indexOf(29)==-1){
						djxsyhFlag = false;
					}
				}else{
					if(ytxhc.indexOf(2)==-1&&ytxhc.indexOf(27)==-1){
						djxsyhFlag = false;
					}
				}

			}else if( (skssqqDate<hbgDate&&ytxhc.indexOf(29)>-1) || (skssqqDate>=hbgDate&&ytxhc.indexOf(27)>-1)){
				//3.再判断选择2个的是否有29行和允许和29行一起享受的
				//在29，3-12,14-16,18-21,25,31的数量
				var yxdjYsCount = 0;
				//不在29，3-12,14-16,18-21,25,31的数量
				var byxdjYsCount = 0;
				for(var i=0;i<ytxhc.length;i++){
					var xh = ytxhc[i];
					if(skssqqDate<hbgDate ){
						if(xh!=29){
							if((xh>=3&&xh<=12) || xh==14 || (xh>=16&&xh<=18) || (xh>=20&&xh<=22) || xh==25 || xh==30 || xh==31){
								//如果是第31行，则只有总机构可以选择优惠
								if(sbqylx!="1" && (xh==31 || xh==30)){
									djxsyhFlag = false;
								}
							}else{
								djxsyhFlag = false;
							}
						}
					}else{
						if(xh!=27){

							if((xh>=3&&xh<=10) || xh==12 || (xh>=14&&xh<=16) || (xh>=18&&xh<=20) || xh==23 || xh==28 || xh==29 || xh==30 || xh==31 || ( xh==32 &&skssqzDate >=hbgDate2 ) ){
								//如果是第31行，则只有总机构可以选择优惠
								if(sbqylx!="1" && ( xh==28 || xh==29 || xh==31 || xh==30 ||   ( xh==32 &&skssqzDate >=hbgDate2 ) )){
									djxsyhFlag = false;
								}
							}else{
								djxsyhFlag = false;
							}

						}

					}

				}
			}else if (ytxhc.indexOf(28)>-1 && skssqqDate>=hbgDate ) {
				if(sbqylx!="1"||(ytxhc.indexOf(27)==-1 && ytxhc.indexOf(29)==-1 && ytxhc.indexOf(30)==-1 && ytxhc.indexOf(31)==-1 && ( ytxhc.indexOf(32)==-1 || skssqzDate<hbgDate2) )   ){
					djxsyhFlag = false;
				}
			}else if (ytxhc.indexOf(29)>-1 && skssqqDate>=hbgDate ) {
				if(sbqylx!="1"||(ytxhc.indexOf(27)==-1 && ytxhc.indexOf(28)==-1 && ytxhc.indexOf(30)==-1 && ytxhc.indexOf(31)==-1 && ( ytxhc.indexOf(32)==-1 || skssqzDate<hbgDate2) )   ){
					djxsyhFlag = false;
				}
			}else if (ytxhc.indexOf(30)>-1) {
				if(skssqqDate<hbgDate){
					if(sbqylx!="1"||(ytxhc.indexOf(31)==-1&&ytxhc.indexOf(29)==-1)){
						djxsyhFlag = false;
					}
				}else{
					if(sbqylx!="1"||(ytxhc.indexOf(27)==-1 && ytxhc.indexOf(28)==-1 && ytxhc.indexOf(29)==-1 && ytxhc.indexOf(31)==-1 && ( ytxhc.indexOf(32)==-1 || skssqzDate<hbgDate2) )   ){
						djxsyhFlag = false;
					}
				}

			} else if (ytxhc.indexOf(31)>-1) {
				if(skssqqDate<hbgDate){
					if(sbqylx!="1"||(ytxhc.indexOf(30)==-1&&ytxhc.indexOf(29)==-1)){
						djxsyhFlag = false;
					}
				}else{
					if(sbqylx!="1"||(ytxhc.indexOf(27)==-1 && ytxhc.indexOf(28)==-1 && ytxhc.indexOf(29)==-1 && ytxhc.indexOf(30)==-1 && ( ytxhc.indexOf(32)==-1 || skssqzDate<hbgDate2)  )   ){
						djxsyhFlag = false;
					}
				}

			}else if (ytxhc.indexOf(32)>-1 && skssqzDate>=hbgDate2 ) {
				if(sbqylx!="1"||(ytxhc.indexOf(27)==-1 && ytxhc.indexOf(28)==-1 && ytxhc.indexOf(30)==-1 && ytxhc.indexOf(31)==-1 && ytxhc.indexOf(29)==-1)   ){
					djxsyhFlag = false;
				}
			} else{
				djxsyhFlag = false;
			}
		}
	}else{
		/*全国版本提示
		（1）当sbqylx=1时,强制提示：[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行、32行、34行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！
		（2）当sbqylx≠1时,强制提示：[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！
		*/
		//1.先判断是否只有2和3的
		if( (skssqqDate<hbgDate && ytxhc.length>3)  || (skssqqDate>=hbgDate &&skssqzDate<hbgDate2 &&  ytxhc.length>5) || (skssqzDate>=hbgDate2 &&  ytxhc.length>6) ){
			djxsyhFlag = false;
		}else if(  (skssqqDate<hbgDate &&ytxhc.length==3)  || (skssqqDate>=hbgDate &&skssqzDate<hbgDate2 &&ytxhc.length>=3 && ytxhc.length<=5)  ||  (skssqzDate>=hbgDate2 &&ytxhc.length>=3 && ytxhc.length<=6) ) {
			for(var i=0;i<ytxhc.length;i++){
				var xh = ytxhc[i];
			if(skssqqDate<hbgDate){
				if(xh!=29){
					if(sbqylx!="1" || (xh!=34 && xh!=32)){
						djxsyhFlag = false;
					}
				}
			}else{
				if(xh!=27){
					if(sbqylx!="1" || (xh!=31 && xh!=32 &&xh!=33 && xh!=34 &&  (xh!=35 || skssqzDate<hbgDate2 ) )){
						djxsyhFlag = false;
					}
				}
			}
			}
		}else if(ytxhc.length==2){
			//2.再判断选择2个的是否只有2和3的
			if(ytxhc.indexOf(2)>-1){
				if(ytxhc.indexOf(3)==-1){
					djxsyhFlag = false;
				}
			}else if(ytxhc.indexOf(3)>-1){

				if(skssqqDate<hbgDate){
					if(ytxhc.indexOf(2)==-1&&ytxhc.indexOf(29)==-1){
						djxsyhFlag = false;
					}
				}else{
					if(ytxhc.indexOf(2)==-1&&ytxhc.indexOf(27)==-1){
						djxsyhFlag = false;
					}
				}

			}else if( (skssqqDate<hbgDate&&ytxhc.indexOf(29)>-1) || (skssqqDate>=hbgDate&&ytxhc.indexOf(27)>-1) ){
				//3.再判断选择2个的是否有29行和允许和29行一起享受的
				//在29，3-12,14-16,18-21,25,34的数量
				var yxdjYsCount = 0;
				//不在29，3-12,14-16,18-21,25,34的数量
				var byxdjYsCount = 0;
				for(var i=0;i<ytxhc.length;i++){
					var xh = ytxhc[i];
					if(skssqqDate<hbgDate){
						if(xh!=29){
							if((xh>=3&&xh<=12) || (xh>=14&&xh<=16) || (xh>=18&&xh<=21) || xh==25 || xh==32 || xh==34){
								//如果是第34行或者是第32行，则只有总机构可以选择优惠
								if(sbqylx!="1" && (xh==34 || xh==32)){
									djxsyhFlag = false;
								}
							}else{
								djxsyhFlag = false;
							}
						}
					}else{
						if(xh!=27){
							if((xh>=3&&xh<=10) || (xh>=12&&xh<=14) || (xh>=16&&xh<=19) || xh==23 || xh==31 || xh==32 || xh==33 || xh==34 || (xh==35&&skssqzDate>=hbgDate2)){
								//如果是第34行或者是第32行，则只有总机构可以选择优惠
								if(sbqylx!="1" && (xh==31 || xh==32 || xh==33 || xh==34 || (xh==35&&skssqzDate>=hbgDate2))){
									djxsyhFlag = false;
								}
							}else{
								djxsyhFlag = false;
							}
						}
					}

				}
			} else if (ytxhc.indexOf(31)>-1 && skssqqDate>=hbgDate) {
				if(sbqylx!="1"||(ytxhc.indexOf(32)==-1 && ytxhc.indexOf(33)==-1&& ytxhc.indexOf(34)==-1 && ytxhc.indexOf(27)==-1 && ( ytxhc.indexOf(35)==-1 || skssqzDate<hbgDate2)  )){
					djxsyhFlag = false;
				}
			}else if (ytxhc.indexOf(33)>-1 && skssqqDate>=hbgDate) {
				if(sbqylx!="1"||(ytxhc.indexOf(31)==-1 && ytxhc.indexOf(32)==-1&& ytxhc.indexOf(34)==-1 && ytxhc.indexOf(27)==-1 && ( ytxhc.indexOf(35)==-1 || skssqzDate<hbgDate2)  )){
					djxsyhFlag = false;
				}
			}else if (ytxhc.indexOf(32)>-1) {
				if( skssqqDate<hbgDate){
					if(sbqylx!="1"||(ytxhc.indexOf(34)==-1&&ytxhc.indexOf(29)==-1)){
						djxsyhFlag = false;
					}
				}else{
					if(sbqylx!="1"||(ytxhc.indexOf(31)==-1 && ytxhc.indexOf(33)==-1&& ytxhc.indexOf(34)==-1 && ytxhc.indexOf(27)==-1 && ( ytxhc.indexOf(35)==-1 || skssqzDate<hbgDate2)  )){
						djxsyhFlag = false;
					}
				}

			} else if (ytxhc.indexOf(34)>-1) {
				if( skssqqDate<hbgDate){
					if(sbqylx!="1"||(ytxhc.indexOf(32)==-1&&ytxhc.indexOf(29)==-1)){
						djxsyhFlag = false;
					}
				}else{
					if(sbqylx!="1"||(ytxhc.indexOf(31)==-1 && ytxhc.indexOf(32)==-1&& ytxhc.indexOf(33)==-1 && ytxhc.indexOf(27)==-1 && ( ytxhc.indexOf(35)==-1 || skssqzDate<hbgDate2)  )){
						djxsyhFlag = false;
					}
				}

			}else if (ytxhc.indexOf(35)>-1 && skssqzDate>=hbgDate2) {
				if(sbqylx!="1"||(ytxhc.indexOf(31)==-1 && ytxhc.indexOf(32)==-1&& ytxhc.indexOf(33)==-1 && ytxhc.indexOf(27)==-1 && ytxhc.indexOf(34)==-1 )){
					djxsyhFlag = false;
				}
			} else{
				djxsyhFlag = false;
			}
		}
	}



	if(!djxsyhFlag){
		flag = false;
		if("Y"==qygxhXmBz){
			if(sbqylx=="1"){
				if( skssqqDate<hbgDate){
					tipsMsg = "[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第29行与（第3-12行、14行、16-18行、20行-22行、25行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，29行与30行、31行可以同时允许叠加享受，但不能享受其他优惠。";
				}else if(skssqqDate>=hbgDate && skssqzDate<hbgDate2) {
					tipsMsg = "[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第27行与（第3-10行、12行、14-16行、18行-20行、23行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与28行、29行、30行、31行可以同时允许叠加享受，但不能享受其他优惠。";
				}else{
					tipsMsg = "[减免所得税类型选项表]第1行至33行中，只有第2行与第3行，第27行与（第3-10行、12行、14-16行、18行-20行、23行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与28行、29行、30行、31行、32行可以同时允许叠加享受，但不能享受其他优惠。";
				}



			}else{
				if( skssqqDate<hbgDate){
					tipsMsg = "[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第29行与（第3-12行、14行、16-18行、20行-22行、25行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目！";
				}else if(skssqqDate>=hbgDate && skssqzDate<hbgDate2) {
					tipsMsg = "[减免所得税类型选项表]第1行至32行中，只有第2行与第3行，第27行与（第3-10行、12行、14-16行、18行-20行、23行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目！";
				}else{
					tipsMsg = "[减免所得税类型选项表]第1行至33行中，只有第2行与第3行，第27行与（第3-10行、12行、14-16行、18行-20行、23行 ）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目！";
				}

			}
		}else{
			if(sbqylx=="1"){
				if( skssqqDate<hbgDate){
					if(_formData.fq_.nsrjbxx.nsrsbh=='12100000MB0111288J' || _formData.fq_.nsrjbxx.nsrsbh=='12100000MB1A69033G'){
						tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，29行与32行、34行可以同时允许叠加享受，但不能享受其他优惠。";
					}else {
						tipsMsg = "[减免所得税类型选项表]第1行至35行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，29行与32行、34行可以同时允许叠加享受，但不能享受其他优惠。";
					}
				}else{
					var cs="";
					var hs=35;
					var hsj=36;
					if(skssqzDate>=hbgDate2){
						cs="、35行";
						hs=hs+1;
						hsj=36+1;
					}

					if(_formData.fq_.nsrjbxx.nsrsbh=='12100000MB0111288J' || _formData.fq_.nsrjbxx.nsrsbh=='12100000MB1A69033G'){
						tipsMsg = "[减免所得税类型选项表]第1行至"+hsj+"行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与31行、32行、33行、34行"+cs+"可以同时允许叠加享受，但不能享受其他优惠。";
					}else {
						tipsMsg = "[减免所得税类型选项表]第1行至"+hs+"行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与31行、32行、33行、34行"+cs+"可以同时允许叠加享受，但不能享受其他优惠。";
					}
				}

			}else{
				if( skssqqDate<hbgDate){
					tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！";
				}else{
					tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-10行、12-14行、16-19行、23行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！";
				}

			}
		}
	}else{
		//再判断厦门个性化2，3行同时选择时校验是否通过
		if("Y"==gxjsBmdBz&&ytxhc.indexOf(2)>-1&&ytxhc.indexOf(3)>-1){
			var JMSE201 = parent.ROUND(jmsdGridlb[1].yhjmje/0.1,2);
			var JMSE202_0125 = parent.ROUND(jmsdGridlb[2].yhjmje/0.125,2);
			var JMSE202_025 = parent.ROUND(jmsdGridlb[2].yhjmje/0.25,2);
			var sjlreLj = _formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;
			if(parent.ROUND(JMSE201+JMSE202_0125,2)!=parent.ROUND(sjlreLj,2)
					&& (parent.ROUND(JMSE201+JMSE202_0125,2)>parent.ROUND(sjlreLj+1,2) || parent.ROUND(JMSE201+JMSE202_0125,2)<parent.ROUND(sjlreLj-1,2))
					&& (parent.ROUND(JMSE201+JMSE202_025,2)>parent.ROUND(sjlreLj+1,2)  || parent.ROUND(JMSE201+JMSE202_025,2)<parent.ROUND(sjlreLj-1,2))){
				flag = false;
				tipsMsg = "JMSE00201项行次/10%【"+JMSE201+"】+(JMSE00202项行次/12.5%【"+JMSE202_0125+"】或者JMSE00202项行次/25%【"+JMSE202_025+"】)应等于主表第10行【"+sjlreLj+"】，允许误差值±1。";
			}
		}
	}
	if("tips"==resultType){
		return tipsMsg;
	}
	return flag;
}

//所减免所得税优惠事项计算本年累计
function jsJmsdssxBnlj(jmsdssxSl1,index,jejslx,bnljIndex){
	if((index=="1" || index=="2") && !isEmptyObject(jejslx)&& (jejslx==="C1" ||jejslx==="C2")){
		var jsyj=1;
		if(jejslx==="C2"){
			jsyj=2;
		}
		parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx = jsyj;
		parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx = jejslx;
		jejslx="";
		var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx";
	    parent.formulaEngine.apply(_jpath, "");
	}
	if(index=="2"&& !isEmptyObject(jejslx)&& (jejslx==="A1" ||jejslx==="B1")){
		jejslx="";
	}

	if(!isEmptyObject(jejslx)){
		parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].jejslx = jejslx;
	}
	parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].t_jmsdssxSl1 = jmsdssxSl1;
	var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb["+index+"].t_jmsdssxSl1";
    parent.formulaEngine.apply(_jpath, jmsdssxSl1);
    viewEngine.tipsForVerify(document.body);
    viewEngine.formApply($('#viewCtrlId'));
    if(bnljIndex!=null && bnljIndex!=undefined){
    	layer.close(bnljIndex);
    }
}

function changeFbsxXszc(scope){
	var t_xszcy = parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_xszcy;
	var t_xszcx = parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_xszcx;
	var t_sqxszc = parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_sqxszc;
	var sfts="N";
     if(!isEmptyObject(t_sqxszc)){
    	var  xszc=t_xszcy+","+t_xszcx
	sfts=(xszc+"").indexOf(t_sqxszc)==-1?"Y":"N";
    }
	if((!isEmptyObject(t_sqxszc)) && sfts=="Y"){

		var sqXszcMc = "";
		if("0"==t_sqxszc){
			sqXszcMc = "原政策";
		}else if("1"==t_sqxszc){
			sqXszcMc = "新政策";
		}
		var xszcTsIndex = layer.open({
	        type: 1,
	        area: ['448px','250px'],
			skin: 'iconStyle',
	        title:['提示'],
	        scrollbar: false,
	        closeBtn: false,
	        content:"&nbsp;&nbsp;&nbsp;&nbsp;上期申报享受政策为"+sqXszcMc+"，原政策与新政策执行条件不一致；该选项应与选择填报优惠项目的行次也保持一致。请准确填报优惠执行依据。",
	        btn: [ '确定'],
	        btn1: function(){
	        /*

	        	parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc = t_sqxszc;
	        	var _jpath = "ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc";
	            parent.formulaEngine.apply(_jpath, t_sqxszc);
	            viewEngine.tipsForVerify(document.body);
	            viewEngine.formApply($('#viewCtrlId'));*/
	        	layer.close(xszcTsIndex);
	        }
	    });
	} /*else {
		cleanFbsxXX();
		var qygxhXmBz = parent.formData.fq_.qygxhXmBz;
		if(isEmptyObject(t_sqxszc) && (!isEmptyObject(xszc))  && "Y"==qygxhXmBz){
			//厦门个性化,选择原政策或者新政策时提示
			var xszcTsIndex = layer.open({
				type: 1,
				area: ['400px','250px'],
				title:['提示'],
				scrollbar: false,
				closeBtn: false,
				content:"&nbsp;&nbsp;&nbsp;&nbsp;Y01001软件集成电路优惠是符合软件、集成电路税收优惠政策条件的纳税人填报的，请您确认是否填报正确。",
				btn: ['确定','取消'],
				btn1: function(){
					layer.close(xszcTsIndex);
				},
				btn2: function(){
					parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc = '';
					parent.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_fsbxSelect = false;
					var _jpath = "ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc";
					parent.formulaEngine.apply(_jpath, '');
					viewEngine.tipsForVerify(document.body);
					viewEngine.formApply($('#viewCtrlId'));
					layer.close(xszcTsIndex);
				}
			});
		}

	}*/

}

//切换了新旧政策需要清空相关的优惠信息
function cleanFbsxXX(){
	var sdjmGridlb = parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb;
	for(var i=0;i<sdjmGridlb.length;i++){
		var sdjmsxSelect = sdjmGridlb[i].t_sdjmsxSelect;
		var jejslx = sdjmGridlb[i].jejslx;
		var yhswsx = sdjmGridlb[i].yhswsx;
		if(sdjmsxSelect){
			//已选择的优惠事项是否和新旧政策相关
			var sfhxjzcYhsx = false;
			if("jcdlscxm" == jejslx){
				sfhxjzcYhsx = true;
			}
			if(xszc == ""){
				if("SD071" == yhswsx || "SD072" == yhswsx){
					sfhxjzcYhsx = true;
				}
			}
			if(sfhxjzcYhsx){
				parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].t_sdjmsxSelect = false;
				var _jpath = "ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb["+i+"].t_sdjmsxSelect";
				parent.formulaEngine.apply(_jpath, "");
				parent.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].yhjmje = 0;
				_jpath = "ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb["+i+"].yhjmje";
				parent.formulaEngine.apply(_jpath, "");
			}
		}
	}
	var jmsdGridlb = parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
	for(var i=0;i<jmsdGridlb.length;i++){
		var jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
		var jmsdssxlx = jmsdGridlb[i].t_jmsdssxlx;
		if(jmsdssxSelect){
			//已选择的优惠事项是否和新旧政策相关
			var sfhxjzcYhsx = false;
			if("yyrjjcdlyhjxzx"==jmsdssxlx || "rjjcdlqyxzc"==jmsdssxlx){
				sfhxjzcYhsx = true;
			}
			if(sfhxjzcYhsx){
				parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_jmsdssxSelect = false;
				var _jpath = "ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb["+i+"].t_jmsdssxSelect";
				parent.formulaEngine.apply(_jpath, "");
			}
		}
	}
	viewEngine.tipsForVerify(document.body);
	viewEngine.formApply($('#viewCtrlId'));
}

function jmsdssxChangeBnlj(scope,index){
	var bnlj = parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].yhjmje;
	if(bnlj!=0){
		parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].t_jmsdssxSelect = true;
	}else{
		parent.formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].t_jmsdssxSelect = false;
	}
	// 北京个性化
	var swjgDm3 = (parent.formData.fq_.nsrjbxx.swjgDm).substring(0,3);
	if(swjgDm3 == "111" && index == "34" && bnlj > 0){
		parent.layer.confirm("尊敬的纳税人，该行次多用于应急填报国务院新出台的政策，一般情况不得随意填报，请核实您享受的优惠政策，并将优惠金额填报到正确的行次！",{
			title:'提示',
			icon: 6,
			btn : ['阅读完毕']
		},function(index){
			parent.layer.close(index);
		});
	}
	viewEngine.tipsForVerify(document.body);
    viewEngine.formApply($('#viewCtrlId'));
}

function jmsdssxFbmdts(scope,bmdlx){
	//厦门个性化信息
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	if("Y"!=qygxhXmBz){
		return ;
	}
	var gxjsBmdBz = scope.formData.kz_.bmdBzxx.gxjsBmdBz;
	var tipMsg = "";
	if("gxjs"==bmdlx && "N"==gxjsBmdBz){
		tipMsg = "尊敬的纳税人，您不属于高新技术企业，不能填报本行次!";
	}
	if(tipMsg!=""){
		layer.alert(tipMsg);
	}
}

function mssrsxFbmdts(scope,bmdlx){
	//厦门个性化信息
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	if("Y"!=qygxhXmBz){
		return ;
	}

	var fylBmdBz = scope.formData.kz_.bmdBzxx.fylBmdBz;
	var tipMsg = "";
	if("fyl"==bmdlx && "N"==fylBmdBz){
		tipMsg = "你单位未认定为非营利组织，不能填报本行次，请携带资料向主管税务机关申请！";
	}
	if(tipMsg!=""){
		layer.alert(tipMsg);
	}
}

/*GDSDZSWJ-17679 广东个性化
*/
var cyrsqhjtjyts="N";
var cyrsysqsjjyts="N";
function cyrsOnblur(mc){
	var subSwjgDm = (parent.formData.fq_.nsrjbxx.swjgDm).substring(1,3);
	var   sbqylx=parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var yf_z = parseInt((skssqz).split("-")[1], 10);
   if(subSwjgDm!="44" || sbqylx=="2" ||  (yf_z!=3&&yf_z!=6&&yf_z!=9&&yf_z!=12)){
    return;
	}

	var qccyrs1= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1;
	var qmcyrs1= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1;
	var qccyrs2= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2;
	var qmcyrs2= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2;
	var qccyrs3= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3;
	var qmcyrs3= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3;
	var qccyrs4= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4;
	var qmcyrs4= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4;
	var sfqhqy=parent.formData.fq_.sfqhqy;
	var ssjd="";
	if(yf_z<=3){
		ssjd="jd1";
	}
	if(yf_z>=4&&yf_z<=6){
		ssjd="jd2";
	}
	if(yf_z>=7&&yf_z<=9){
		ssjd="jd3";
	}
	if(yf_z>=10){
		ssjd="jd4";
	}

	/*从业人数>10000且不是千户集团企业为异常，弹框提示：
	填报从业人数大于10000且不是千户集团企业，请核实从业人员数据是否正确，如不正确请修改填报数据。*/
	if(sfqhqy=="N"){
		var bz=false;
		if((mc=="qccyrs1"||mc=="qmcyrs1")&&ssjd=="jd1" && (qccyrs1>10000 || qmcyrs1>10000 )){
			bz=true;
		}

		if((mc=="qccyrs2"||mc=="qmcyrs2")&&ssjd=="jd2" && (qccyrs2>10000 || qmcyrs2>10000 )){
			bz=true;
		}

		if((mc=="qccyrs3"||mc=="qmcyrs3")&&ssjd=="jd3" && (qccyrs3>10000 || qmcyrs3>10000 )){
			bz=true;
		}

		if((mc=="qccyrs4"||mc=="qmcyrs4")&&ssjd=="jd4" && (qccyrs4>10000 || qmcyrs4>10000 )){
			bz=true;
		}
		if(bz&&cyrsqhjtjyts=="N"){
			cyrsqhjtjyts="Y";
			var tsindex = layer.open({
		        type: 1,
		        area: ['450px','300px'],
		        title:['提示'],
		        scrollbar: false,
		        closeBtn: false,
		        content:"填报从业人数大于10000且不是千户集团企业，请核实从业人员数据是否正确，如不正确请修改填报数据。",
		        btn: [ '确定'],
		        btn1: function(){
		        	cyrsqhjtjyts="N";
		        	layer.close(tsindex);
		        }
		    });
		}

	}

/*	2021年1季度或3月属期起及以后属期：本期申报表中第2至4季度从业人数数据：与上期季初从业人数、季末从业人数不一致，
	系统提示：本季度填报的从业人数和以前季度填报的数据不一致，请核实本期填报的各项从业人数为准确数据，保证本期数据填报准确。*/

	var sqQccyrs1=parent.formData.hq_.sqsbxx.qccyrs1;
	var sqQmcyrs1=parent.formData.hq_.sqsbxx.qmcyrs1;
	var sqQccyrs2=parent.formData.hq_.sqsbxx.qccyrs2;
	var sqQmcyrs2=parent.formData.hq_.sqsbxx.qmcyrs2;
	var sqQccyrs3=parent.formData.hq_.sqsbxx.qccyrs3;
	var sqQmcyrs3=parent.formData.hq_.sqsbxx.qmcyrs3;

	var sfts=false;

	if((mc=="qccyrs1"||mc=="qmcyrs1")&&(ssjd=="jd2" || ssjd=="jd3" ||  ssjd=="jd4") && (qccyrs1!=sqQccyrs1&&sqQccyrs1>0 || qmcyrs1!=sqQmcyrs1&&sqQccyrs1>0 )){
		sfts=true;
	}

	if((mc=="qccyrs2"||mc=="qmcyrs2")&&(ssjd=="jd3" ||  ssjd=="jd4" ) && (qccyrs2!=sqQccyrs2&&sqQccyrs2>0 || qmcyr2!=sqQmcyrs2&&sqQmcyrs2>0 )){
		sfts=true;
	}

	if((mc=="qccyrs3"||mc=="qmcyrs3")&&ssjd=="jd4" && (qccyrs3!=sqQccyrs3&&sqQccyrs3>0 || qmcyrs3!=sqQmcyrs3&&sqQmcyrs3>0 )){
		sfts=true;
	}

	if(sfts&&cyrsysqsjjyts=="N"){
		cyrsysqsjjyts="Y";
		var rsndex = layer.open({
	        type: 1,
	        area: ['450px','300px'],
	        title:['提示'],
	        scrollbar: false,
	        closeBtn: false,
	        content:"本季度填报的从业人数和以前季度填报的数据不一致，请核实本期填报的各项从业人数为准确数据，保证本期数据填报准确。",
	        btn: [ '确定'],
	        btn1: function(){
	        	cyrsysqsjjyts="N";
	        	layer.close(rsndex);
	        }
	    });
	}

}


/*GDSDZSWJ-17679 广东个性化
*/
var zczeQhjtjyts="N";
var zczeSqsjjyts="N";
function zczeOnblur(mc){
	var subSwjgDm = (parent.formData.fq_.nsrjbxx.swjgDm).substring(1,3);
	var   sbqylx=parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	var skssqz = parent.formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
	var yf_z = parseInt((skssqz).split("-")[1], 10);
   if(subSwjgDm!="44" || sbqylx=="2" ||  (yf_z!=3&&yf_z!=6&&yf_z!=9&&yf_z!=12)){
    return;
	}

	var qczcze1= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1;
	var qmzcze1= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1;
	var qczcze2= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2;
	var qmzcze2= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2;
	var qczcze3= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3;
	var qmzcze3= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3;
	var qczcze4= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze4;
	var qmzcze4= parent.formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4;
	var sfqhqy=parent.formData.fq_.sfqhqy;
	var ssjd="";
	if(yf_z<=3){
		ssjd="jd1";
	}
	if(yf_z>=4&&yf_z<=6){
		ssjd="jd2";
	}
	if(yf_z>=7&&yf_z<=9){
		ssjd="jd3";
	}
	if(yf_z>=10){
		ssjd="jd4";
	}

	/*资产总额>999999.99且不是千户集团企业为异常，
	弹框提示：请检查资产总额，资产总额不为负数，资产总额是否填报过大。资产总额单位为万元，请确认填报的资产总额是否正确。*/
	if(sfqhqy=="N"){
		var bz=false;
		if((mc=="qczcze1"||mc=="qmzcze1")&&ssjd=="jd1" && (qczcze1>999999.99 || qmzcze1>999999.99 )){
			bz=true;
		}

		if((mc=="qczcze2"||mc=="qmzcze3")&&ssjd=="jd2" && (qczcze2>999999.99 || qmzcze2>999999.99 )){
			bz=true;
		}

		if((mc=="qczcze3"||mc=="qmzcze3")&&ssjd=="jd3" && (qczcze3>999999.99 || qmzcze3>999999.99 )){
			bz=true;
		}

		if((mc=="qczcze4"||mc=="qmzcze4")&&ssjd=="jd4" && (qczcze4>999999.99 || qmzcze4>999999.99 )){
			bz=true;
		}
		if(bz&&zczeQhjtjyts=="N"){
			zczeQhjtjyts="Y";
			var tsindex = layer.open({
		        type: 1,
		        area: ['450px','300px'],
		        title:['提示'],
		        scrollbar: false,
		        closeBtn: false,
		        content:"请检查资产总额，资产总额不为负数，资产总额是否填报过大。资产总额单位为万元，请确认填报的资产总额是否正确。",
		        btn: [ '确定'],
		        btn1: function(){
		        	zczeQhjtjyts="N";
		        	layer.close(tsindex);
		        }
		    });
		}

	}

/*	2021年1季度或3月属期起及以后属期：本期申报表中第2至4季度资产总额数据：与上期季初资产总额、季末资产总额不一致，
	系统提示：本季度填报的资产总额和以前季度填报的数据不一致，请核实本期填报的各项资产总额为准确数据，保证本期数据填报准确。*/

	var sqQczcze1=parent.formData.hq_.sqsbxx.qczcze1;
	var sqQmzcze1=parent.formData.hq_.sqsbxx.qmzcze1;
	var sqQczcze2=parent.formData.hq_.sqsbxx.qczcze2;
	var sqQmzcze2=parent.formData.hq_.sqsbxx.qmzcze2;
	var sqQczcze3=parent.formData.hq_.sqsbxx.qczcze3;
	var sqQmzcze3=parent.formData.hq_.sqsbxx.qmzcze3;

	var sfts=false;

	if((mc=="qczcze1"||mc=="qmzcze1")&&(ssjd=="jd2" || ssjd=="jd3"||ssjd=="jd4")&& (qczcze1!=sqQczcze1&&sqQczcze1>0 || qmzcze1!=sqQmzcze1&&sqQmzcze1>0 )){
		sfts=true;
	}

	if((mc=="qczcze2"||mc=="qmzcze2")&&(ssjd=="jd3"|| ssjd=="jd4" ) && (qczcze2!=sqQczcze2&&sqQczcze2>0 || qmzcze2!=sqQmzcze2&&sqQmzcze2>0 )){
		sfts=true;
	}

	if((mc=="qczcze3"||mc=="qmzcze3")&&ssjd=="jd4" && (qczcze3!=sqQczcze3&&sqQczcze3>0 || qmzcze3!=sqQmzcze3&&sqQmzcze3>0 )){
		sfts=true;
	}

	if(sfts&&zczeSqsjjyts=="N"){
		zczeSqsjjyts="Y";
		var rsndex = layer.open({
	        type: 1,
	        area: ['450px','300px'],
	        title:['提示'],
	        scrollbar: false,
	        closeBtn: false,
	        content:"本季度填报的资产总额和以前季度填报的数据不一致，请核实本期填报的各项资产总额为准确数据，保证本期数据填报准确。",
	        btn: [ '确定'],
	        btn1: function(){
	        	zczeSqsjjyts="Y";
	        	layer.close(rsndex);
	        }
	    });
	}

}
function checkfbsxGrid(scope){
    var jpathList=[];
    var fbsxGridlb = scope.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb;
    for (var i = 0; i < fbsxGridlb.length; i++) {
        if(fbsxGridlb[i].t_fsbxSelect == false){
            scope.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[i].jehxxz = 0;
            scope.formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[i].xszc = '';
            jpathList.push("ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb["+i+"]");
        }
    }
    parent.formulaEngine.apply4List(jpathList);

    // 3、刷新校验结果和控制结果
    viewEngine.formApply($('#viewCtrlId'));
    viewEngine.tipsForVerify(document.body);
}

function jcClickTcts(scope,tslx){
	var qygxhXmBz = scope.formData.fq_.qygxhXmBz;
	if("Y"==qygxhXmBz){
		var dqjdSsjd = scope.formData.fq_.dqjdSsjd;
		var tipMsg = "";
		var sbxx = scope.formData.hq_.sqsbxx;
		var bnjd1sfysb = scope.formData.fq_.bnjd1sfysb;
		var bnjd2sfysb = scope.formData.fq_.bnjd2sfysb;
		var bnjd3sfysb = scope.formData.fq_.bnjd3sfysb;
		if("zcze2"==tslx && ("2jd"==dqjdSsjd || "3jd"==dqjdSsjd ) &&  bnjd1sfysb=='Y'){
			var qczcze1 = sbxx.qczcze1;
			if((!isEmptyObject(qczcze1)) && Number(qczcze1)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-资产总额-一季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}else if("zcze3"==tslx && ("3jd"==dqjdSsjd || "4jd"==dqjdSsjd) &&  bnjd2sfysb=='Y'){
			var qczcze2 = sbxx.qczcze2;
			if((!isEmptyObject(qczcze2)) && Number(qczcze2)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-资产总额-二季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}else if("zcze4"==tslx && "4jd"==dqjdSsjd &&  bnjd3sfysb=='Y'){
			var qczcze3 = sbxx.qczcze3;
			if((!isEmptyObject(qczcze3)) && Number(qczcze3)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-资产总额-三季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}else if("cyrs2"==tslx && ("2jd"==dqjdSsjd || "3jd"==dqjdSsjd) &&  bnjd1sfysb=='Y'){
			var qccyrs1 = sbxx.qccyrs1;
			if((!isEmptyObject(qccyrs1)) && Number(qccyrs1)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-从业人数-一季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}else if("cyrs3"==tslx && ("3jd"==dqjdSsjd || "4jd"==dqjdSsjd) &&  bnjd2sfysb=='Y'){
			var qccyrs2 = sbxx.qccyrs2;
			if((!isEmptyObject(qccyrs2)) && Number(qccyrs2)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-从业人数-二季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}else if("cyrs4"==tslx && "4jd"==dqjdSsjd &&  bnjd3sfysb=='Y'){
			var qccyrs3 = sbxx.qccyrs3;
			if((!isEmptyObject(qccyrs3)) && Number(qccyrs3)>0){
				tipMsg = "该值应等于上一期预缴申报的“按季度填报信息-从业人数-三季度-季末”的值，若您上一期预缴申报时数据填报错误，请先更正上一期预缴申报表!";
			}
		}
		if(!isEmptyObject(tipMsg)){
			var tsIndex = layer.open({
		        type: 1,
		        area: ['350px','250px'],
		        title:['提示'],
		        scrollbar: false,
		        closeBtn: false,
		        content:"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+tipMsg,
		        btn: [ '确定'],
		        btn1: function(){
		        	layer.close(tsIndex);
		        }
		    });
		}
	}
}

//隐藏其他列表，展示选择列表
function hideOpenpage(scope,newData){
	var className = "."+newData;
    for(var i=0;i<$(className).length;i++){
        var xzlbGrid=$(className)[i].style.display;
        if(xzlbGrid=="none"){
            $(className)[i].style.display="";
		}else{
            $(className)[i].style.display="none";
		}
	}
}

/*
* 青岛个性化 SW2017150-570
* 针对第7行：JJKC011、JJKC012、JJKC021、JJKC022
* 当纳税人点击时，对纳税人进行提示
* 适用2021年第3季度（按季预缴）或9月份（按月预缴）属期及以后属期（sbqylx=0或1）
* */
function onclickTs(scope,newData) {
	//税务机关代码
	var swjgDm = scope.formData.fq_.nsrjbxx.swjgDm;
	//2021年第三季度或9月份
	var gbd3jd = scope.formData.kz_.temp.zb.gbd3jd;
	//申报企业类型
	var sbqylx = scope.formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	//行业代码
	var hyDm   = Number(scope.formData.fq_.nsrjbxx.hydm);
	//行业名称
	var hymc = scope.formData.fq_.nsrjbxx.hymc;
	var hylb = scope.formData.kz_.temp.zb.hylb;
	//提示语、序号
	var msg = "";
	var index = 0;
	/*
	* 当纳税人税务登记信息中行业信息属于以下行业之一时，对纳税人进行提示
	*	（1）烟草制造业( >=1600 and <=1699)
	*	（2）住宿和餐饮业( >=6100 and <=6299)
	*   （3）批发和零售业( >=5100 and <=5299)
	*	（4）房地产业( >=7000 and <=7099)
	*	（5）租赁和商务服务业( >=7100 and <=7299)
	*	（6）娱乐业( >=9000 and <=9099)
	*/
	if(swjgDm!=undefined&&swjgDm.substring(0,5)=='13702'&&gbd3jd=='Y'&&sbqylx!='2'&& newData!=='JJKC021' && newData!=='JJKC022' &&((hyDm>=1600 && hyDm<=1699)||(hyDm>=6100 && hyDm<=6299)||(hyDm>=5100 && hyDm<=5299)||(hyDm>=7000 && hyDm<=7099)||(hyDm>=7100 && hyDm<=7299)||(hyDm>=9000 && hyDm<=9099))){
		var xh=index>0?(index+1)+"、":"";
		msg="你公司行业代码显示为"+hyDm+"，属于"+hylb+"，税收规定不适用研发费用加计扣除的行业，不应填报"+ newData +"行。如税务登记信息有误，请您及时修正。</br></br>";
		index++;
	}
	if(swjgDm!=undefined&&swjgDm.substring(0,5)=='13702'&&gbd3jd=='Y'&&sbqylx!='2') {
		//行业代码
		var hyDm = Number(scope.formData.fq_.nsrjbxx.hyDm);

		//对制造业和非织造业制定不同的提示语
		if(newData==='JJKC011'&&(hyDm<1300||hyDm>4390)) {
			//当纳税人税务登记信息中行业门类不是“制造业”时（登记信息中所属行业不为国标行业代码C，1300-4390)
			var xh=index>0?(index+1)+"、":"";
			msg +=xh+  newData + "行填报制造业企业开展研发活动发生的研究开发费用加计扣除金额，其他行业研发费用加计扣除金额不在"+ newData +"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。";
			index++;
		}else if(newData==='JJKC021'&&(hyDm<1300||hyDm>4390)) {
			var xh=index>0?(index+1)+"、":"";
			msg +=xh+  newData + "行填报制造业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用加计扣除金额，其他行业相关费用加计扣除金额不在"+ newData +"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。";
			index++;
		}else if(newData==='JJKC012'&&(hyDm>=1300&&hyDm<=4390)) {
			//当纳税人税务登记信息中行业门类是“制造业”时（登记信息中所属行业为国标行业代码C，1300-4390)
			var xh=index>0?(index+1)+"、":"";
			msg += xh+ "根据登记信息行业，申报企业属于制造业，"+ newData +"行填报非制造业企业开展研发活动发生的研究开发费用75%加计扣除金额，制造业研发费用100%加计扣除金额不在"+ newData +"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。";
			index++;
		}else if(newData==='JJKC022'&&(hyDm>=1300&&hyDm<=4390)) {
            var xh=index>0?(index+1)+"、":"";
            msg += xh+ "根据登记信息行业，申报企业属于制造业，"+ newData +"行填报非制造业企业为获得创新性、创意性、突破性的产品进行创意设计活动而发生的相关费用75%加计扣除金额，制造业相关费用100%加计扣除金额不在"+ newData +"行填报，请确认是否属于制造业。如税务登记信息有误，请您及时修正。";
            index++;
        }
		if(!isEmptyObject(msg)) {
			var xh=index>1?(1)+"、":"";
			msg=xh+msg;
			var jkcjmIndex = parent.layer.open({
				type:1,
				area: ['600px'],
				title: '提示',
				scrollbar: false,
				closeBtn: false,
				content: msg,
				btn: ['确定'],
				yes: function(index,layero){
					parent.layer.close(jkcjmIndex);
				}
			});
		}
	}
}

/*
* 福建个性化 SW2017112-4606
*
* 当纳税人点击时，对纳税人进行提示
*
* */
function mssrFjtsJe(scope,index){
	var gzsbbz=parent.formData.kz_.temp.gzsbbz;
	//税务机关代码
	var swjgDm = scope.formData.fq_.nsrjbxx.swjgDm;
	var sjdm=swjgDm.substring(0,5);
	var dqdm=swjgDm.substring(0,3);
	//非福建区域的 不提示
	if(dqdm !='135' || sjdm=="13502"){
		return;
	}
	  if (isEmptyObject(index)) {
	        return;
	    }
	    var mssrsxVo = scope.formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[index];
	    if (mssrsxVo == null || mssrsxVo == undefined || mssrsxVo == "") {
	        return;
	    }
	    var yhswsx = mssrsxVo.yhswsx;
	    //mssrsxSelect的值由于先触发点击监听事件再改变值，所以获取到的值和实际的值相反
	    var mssrsxSelect = mssrsxVo.t_mssrsxSelect;
	    var xh = mssrsxVo.t_xh;
	    var yhjmje = mssrsxVo.yhjmje;
	    if (yhjmje<=0) {
	        return;
	    }

	  var  msg="";
	if(yhswsx=="MSSR010"){
		 msg="一、填报此栏，请留存备查资料：<br/>"
		+"1.国债净价交易交割单；<br/>2.购买、转让国债的证明，包括持有时间、票面金额、利率等相关材料；<br/>3.应收利息（投资收益）科目明细账或按月汇总表；<br/>4.减免税计算过程的说明。<br/>"
		+"二、填报此栏，须符合以下条件：<br/>"
	    +"填报根据《国家税务总局关于企业国债投资业务企业所得税处理问题的公告》（2011年第36号）等相关税收政策规定的，纳税人持有国务院财政部门发行的国债取得的利息收入。";
	}else if (yhswsx=="MSSR021"){
		 msg="填报此栏，须符合以下条件：<br/>"
		+"填报根据《中华人民共和国企业所得税法实施条例》第八十三条规定，纳税人取得的投资收益，不含持有H股、创新企业CDR、永续债取得的投资收益。";
	}else if (yhswsx=="MSSR022"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		+"1.相关记账凭证、本公司持股比例以及持股时间超过12个月的情况说明；<br/>2.被投资企业股东会（或股东大会）利润分配决议或公告、分配表；<br/>3.投资收益、应收股利科目明细账或按月汇总表。"
		+"<br/>二、填报此栏，须符合以下条件：<br/>"
		+"填报根据《财政部 国家税务总局 证监会关于沪港股票市场交易互联互通机制试点有关税收政策的通知》（财税〔2014〕81号）等相关税收政策规定，内地居民企业通过沪港通投资且连续持有H股满12个月取得的股息红利所得。";
	}else if (yhswsx=="MSSR023"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		+"1.相关记账凭证、本公司持股比例以及持股时间超过12个月的情况说明；<br/>2.被投资企业股东会（或股东大会）利润分配决议或公告、分配表；<br/>3.投资收益、应收股利科目明细账或按月汇总表。"
		+"<br/>二、填报此栏，须符合以下条件：<br/>"
		+"填报根据《财政部 国家税务总局 证监会关于深港股票市场交易互联互通机制试点有关税收政策的通知》（财税〔2016〕127号）等相关税收政策规定，内地居民企业通过深港通投资且连续持有H股满12个月取得的股息红利所得。";
	}else if (yhswsx=="MSSR024"){
		 msg="填报此栏，须符合以下条件：<br/>";
		msg=msg+"填报根据《财政部 税务总局 证监会关于创新企业境内发行存托凭证试点阶段有关税收政策的公告》（2019年第52号）等相关税收政策规定，居民企业持有创新企业CDR取得的股息红利所得。";
	} else if (yhswsx=="MSSR025"){
		msg="填报此栏，须符合以下条件：<br/>";
		msg=msg+"填报根据《财政部 税务总局关于永续债企业所得税政策问题的公告》（2019年第64号）等相关税收政策规定，居民企业取得的可以适用企业所得税法规定的居民企业之间的股息、红利等权益性投资收益免征企业所得税规定的永续债利息收入。";
	}  else if (yhswsx=="MSSR030"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.非营利组织免税资格有效认定文件或其他相关证明；<br/>"
	           +"2.非营利组织认定资料；<br/>"
	           +"3.当年资金来源及使用情况、公益活动和非营利活动的明细情况；<br/>"
	           +"4.当年工资薪金情况专项报告，包括薪酬制度、工作人员整体平均工资薪金水平、工资福利占总支出比例、重要人员工资薪金信息（至少包括工资薪金水平排名前10的人员）；<br/>"
	           +"5.当年财务报表；<br/>"
	           +"6.登记管理机关出具的事业单位、社会团体、基金会、社会服务机构、宗教活动场所、宗教院校当年符合相关法律法规和国家政策的事业发展情况或非营利活动的材料；<br/>"
	           +"7.应纳税收入及其有关的成本、费用、损失，与免税收入及其有关的成本、费用、损失分别核算的情况说明；<br/>"
	           +"8.取得各类免税收入的情况说明；<br/>"
	           +"9.各类免税收入的凭证。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"填报根据《财政部 国家税务总局关于非营利组织企业所得税免税收入问题的通知》（财税〔2009〕122号）、《财政部 税务总局关于非营利组织免税资格认定管理有关问题的通知》（财税〔2018〕13号）等相关税收政策规定，认定的符合条件的非营利组织，取得的捐赠收入等免税收入，但不包括从事营利性活动所取得的收入。";
	}else if (yhswsx=="MSSR040"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.购买证券投资基金记账凭证；<br/>2.证券投资基金分配公告；<br/>3.免税的分配收入明细账及按月汇总表。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"填报根据《财政部 国家税务总局关于企业所得税若干优惠政策的通知》（财税〔2008〕1号）第二条第二项等相关税收政策规定，投资者从证券投资基金分配中取得的收入。";
	}else if (yhswsx=="MSSR050"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.购买地方政府债券证明，包括持有时间、票面金额、利率等相关材料；<br/>2.应收利息（投资收益）科目明细账或按月汇总表；<br/>3.减免税计算过程的说明。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"填报根据《财政部 国家税务总局关于地方政府债券利息所得免征所得税问题的通知》（财税〔2011〕76号）、《财政部 国家税务总局关于地方政府债券利息免征所得税问题的通知》（财税〔2013〕5号）等相关税收政策规定，取得的2009年、2010年和2011年发行的地方政府债券利息所得，2012年及以后年度发行的地方政府债券利息收入。";
	}else if (yhswsx=="JJSR010"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.购买铁路债券证明资料，包括持有时间、票面金额、利率等相关资料；<br/>2.应收利息（投资收益）科目明细账或按月汇总表；<br/>3.减免税计算过程的说明。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"财政部税务总局公告2019第57号";
	}else if (yhswsx=="JJSR020"){
		 msg="填报此栏，须符合以下条件：<br/>";
		msg=msg+"财政部公告2019第76号";
	}else if (yhswsx=="JJSR030"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.企业实际资源综合利用情况（包括综合利用的资源、技术标准、产品名称等）的说明；<br/>2.综合利用资源生产产品取得的收入核算情况说明。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"财税（2008）117号列举项目";
	}else if (yhswsx=="JJSR041"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.相关利息收入的核算情况说明；<br/>2.相关贷款合同。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"财政部税务总局公告2020第22号";
	}else if (yhswsx=="JJSR042"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.相关保费收入的核算情况说明；<br/>2.相关保险合同。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"财政部税务总局公告2020第22号";
	}else if (yhswsx=="JJSR043"){
		msg="一、填报此栏，请留存备查资料：<br/>";
		msg=msg+"1.相关利息收入的核算情况说明；<br/>2.相关贷款合同；<br/>3.省级金融管理部门（金融办、局等）出具的小额贷款公司准入资格文件。";
		 msg=msg+"<br/>二、填报此栏，须符合以下条件：<br/>";
		msg=msg+"财政部税务总局公告2020第22号";
	}


	if((index=="22" || index=="23" ||  index=="24" ||  index=="25" ||  index=="26" ||  index=="27") && gzsbbz !="Y"){
		msg="一般企业开展研发活动中实际发生的研发费用，再按照实际发生额的75%在税前加计扣除；制造业企业开展研发活动中实际发生的研发费用，自2021年1月1日起，再按照实际发生额的100%在税前加计扣除，请确认是否填报准确！";
	}


	if (isEmptyObject(msg)) {
        return;
    }
	var fjts = parent.layer.open({
		type:1,
		area: ['620px'],
		title: '提示',
		scrollbar: false,
		closeBtn: false,
		content: msg,
		btn: ['确定'],
		yes: function(index,layero){
			parent.layer.close(fjts);
		}
	});
}



/*
* 福建个性化 SW2017112-4606
*
* 当纳税人点击时，对纳税人进行提示
*
* */
function sdjmFjts(scope,index){
	var gzsbbz=parent.formData.kz_.temp.gzsbbz;
	//税务机关代码
	var swjgDm = scope.formData.fq_.nsrjbxx.swjgDm;
	var sjdm=swjgDm.substring(0,5);
	var dqdm=swjgDm.substring(0,3);
	//非福建区域的 不提示
	if(dqdm !='135' || sjdm=="13502"){
		return;
	}
	  if (isEmptyObject(index)) {
	        return;
	    }
	    var sdjmVo = scope.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[index];
	    if (sdjmVo == null || sdjmVo == undefined || sdjmVo == "") {
	        return;
	    }
	    var yhswsx = sdjmVo.yhswsx;
	    //mssrsxSelect的值由于先触发点击监听事件再改变值，所以获取到的值和实际的值相反
	    var sdjmsxSelect = sdjmVo.t_sdjmsxSelect;
	    var xh = sdjmVo.t_xh;
	    if (sdjmsxSelect==true) {
	        return;
	    }

	    var  msg="";
	if(yhswsx=="SD011"){
		msg="1.企业从事相关业务取得的资格证书或证明资料，包括有效期内的远洋渔业企业资格证书、从事农作物新品种选育的认定证书、动物防疫条件合格证、林木种子生产经营许可证、兽医的资格证明等；<br/>"
                  +"2.与农户签订的委托养殖合同（“公司+农户”经营模式的企业）；<br/>"
                  +"3.与家庭承包户签订的内部承包合同（国有农场实行内部家庭承包经营）；<br/>"
                  +"4.农产品初加工项目及工艺流程说明（两个或两个以上的分项目说明）；<br/>"
                  +"5.同时从事适用不同企业所得税待遇项目的，每年度单独计算减免税项目所得的计算过程及其相关账册，期间费用合理分摊的依据和标准；<br/>"
                  +"6.生产场地证明资料，包括土地使用权证、租用合同等；<br/>"
                  +"7.企业委托或受托其他企业或个人从事符合规定的农林牧渔业项目的委托合同、受托合同、支出明细等证明材料。";
	}else if (yhswsx=="SD012"){
		msg="1.企业从事相关业务取得的资格证书或证明资料，包括有效期内的远洋渔业企业资格证书、从事农作物新品种选育的认定证书、动物防疫条件合格证、林木种子生产经营许可证、兽医的资格证明等；<br/>"
            +"2.与农户签订的委托养殖合同（“公司+农户”经营模式的企业）；<br/>"
            +"3.与家庭承包户签订的内部承包合同（国有农场实行内部家庭承包经营）；<br/>"
            +"4.农产品初加工项目及工艺流程说明（两个或两个以上的分项目说明）；<br/>"
            +"5.同时从事适用不同企业所得税待遇项目的，每年度单独计算减免税项目所得的计算过程及其相关账册，期间费用合理分摊的依据和标准; <br/>"
            +"6.生产场地证明资料，包括土地使用权证、租用合同等; <br/>"
            +"7.企业委托或受托其他企业或个人从事符合规定的农林牧渔业项目的委托合同、受托合同、支出明细等证明材料。";
	}else if (yhswsx=="SD021"){
		msg="1.有关部门批准该项目文件; <br/>"
            +"2.公共基础设施项目建成并投入运行后取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.公共基础设施项目完工验收报告; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）; <br/>"
            +"5.公共基础设施项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"6.符合《公共基础设施项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料。";
	} else if (yhswsx=="SD022"){
		msg="1.有关部门批准该项目文件; <br/>"
            +"2.公共基础设施项目建成并投入运行后取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.公共基础设施项目完工验收报告; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）; <br/>"
            +"5.公共基础设施项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"6.符合《公共基础设施项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料。";

	}else if (yhswsx=="SD030"){
		msg="1.符合《环境保护、节能节水项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料; <br/>"
            +"2.环境保护、节能节水项目取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.环境保护、节能节水项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）。";
	}else if (yhswsx=="SD041"){
		msg="1.所转让的技术产权证明；<br/>"
            +"2.企业发生境内技术转让：<br/>"
            +"（1）技术转让合同（副本）；<br/>"
            +"（2）技术合同登记证明；<br/>"
            +"（3）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（4）实际缴纳相关税费的证明资料；<br/>"
            +"3.企业向境外转让技术：<br/>"
            +"（1）技术出口合同（副本）；<br/>"
            +"（2）技术出口合同登记证书或技术出口许可证；<br/>"
            +"（3）技术出口合同数据表；<br/>"
            +"（4）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（5）实际缴纳相关税费的证明资料；<br/>"
            +"（6）有关部门按照商务部、科技部发布的《中国禁止出口限制出口技术目录》出具的审查意见；<br/>"
            +"4.转让技术所有权的，其成本费用情况；转让使用权的，其无形资产费用摊销情况；<br/>"
            +"5.技术转让年度，转让双方股权关联情况。";
	}else if (yhswsx=="SD042"){
		msg="1.所转让的技术产权证明；<br/>"
            +"2.企业发生境内技术转让：<br/>"
            +"（1）技术转让合同（副本）；<br/>"
            +"（2）技术合同登记证明；<br/>"
            +"（3）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（4）实际缴纳相关税费的证明资料；<br/>"
            +"3.企业向境外转让技术：<br/>"
            +"（1）技术出口合同（副本）；<br/>"
            +"（2）技术出口合同登记证书或技术出口许可证；<br/>"
            +"（3）技术出口合同数据表；<br/>"
            +"（4）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（5）实际缴纳相关税费的证明资料；<br/>"
            +"（6）有关部门按照商务部、科技部发布的《中国禁止出口限制出口技术目录》出具的审查意见；<br/>"
            +"4.转让技术所有权的，其成本费用情况；转让使用权的，其无形资产费用摊销情况；<br/>"
            +"5.技术转让年度，转让双方股权关联情况。";
	}else if (yhswsx=="SD050"){
		msg="1.清洁发展机制项目立项有关文件；<br/>"
            +"2.企业将温室气体减排量转让的HFC和PFC类CDM项目，及将温室气体减排量转让的N20类CDM项目的证明材料；<br/>"
            +"3.将温室气体减排量转让收入上缴给国家的证明资料；<br/>"
            +"4.清洁发展机制项目第一笔减排量转让收入凭证（原始凭证及账务处理凭证）；<br/>"
            +"5.清洁发展机制项目所得单独核算资料，以及合理分摊期间共同费用的核算资料。";
	}else if (yhswsx=="SD060"){
		msg="1.能源管理合同；<br/>"
            +"2.国家发展改革委、财政部公布的第三方机构出具的合同能源管理项目情况确认表，或者政府节能主管部门出具的合同能源管理项目确认意见；<br/>"
            +"3.项目转让合同、项目原享受优惠的备案文件（项目发生转让的，受让节能服务企业）；<br/>"
            +"4.合同能源管理项目取得第一笔生产经营收入凭证（原始凭证及账务处理凭证）；<br/>"
            +"5.合同能源管理项目应纳税所得额计算表；<br/>"
            +"6.合同能源管理项目所得单独核算资料，以及合理分摊期间共同费用的核算资料。";
	}

	if (isEmptyObject(msg)) {
        return;
    }
	  msg="填报此栏，请留存备查资料：<br/>"+msg;
	var fjts = parent.layer.open({
		type:1,
		area: ['620px'],
		title: '提示',
		scrollbar: false,
		closeBtn: false,
		content: msg,
		btn: ['确定'],
		yes: function(index,layero){
			parent.layer.close(fjts);
		}
	});
}


/*
* 福建个性化 SW2017112-4606
*
* 当纳税人点击时，对纳税人进行提示
*
* */
function sdjmFjtsJe(scope,index){
	var gzsbbz=parent.formData.kz_.temp.gzsbbz;
	//税务机关代码
	var swjgDm = scope.formData.fq_.nsrjbxx.swjgDm;
	var sjdm=swjgDm.substring(0,5);
	var dqdm=swjgDm.substring(0,3);
	//非福建区域的 不提示
	if(dqdm !='135' || sjdm=="13502"){
		return;
	}
	  if (isEmptyObject(index)) {
	        return;
	    }
	    var sdjmVo = scope.formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[index];
	    if (sdjmVo == null || sdjmVo == undefined || sdjmVo == "") {
	        return;
	    }
	    var yhswsx = sdjmVo.yhswsx;
	    var sdjmsxSelect = sdjmVo.t_sdjmsxSelect;
	    var xh = sdjmVo.t_xh;
	    var yhjmje = sdjmVo.yhjmje;
	    if (yhjmje<=0) {
	        return;
	    }
	    var  msg="";
	if(yhswsx=="SD011"){
		 msg="一、填报此栏，请留存备查资料：<br/>"
	           	  +"1.企业从事相关业务取得的资格证书或证明资料，包括有效期内的远洋渔业企业资格证书、从事农作物新品种选育的认定证书、动物防疫条件合格证、林木种子生产经营许可证、兽医的资格证明等；<br/>"
                  +"2.与农户签订的委托养殖合同（“公司+农户”经营模式的企业）；<br/>"
                  +"3.与家庭承包户签订的内部承包合同（国有农场实行内部家庭承包经营）；<br/>"
                  +"4.农产品初加工项目及工艺流程说明（两个或两个以上的分项目说明）；<br/>"
                  +"5.同时从事适用不同企业所得税待遇项目的，每年度单独计算减免税项目所得的计算过程及其相关账册，期间费用合理分摊的依据和标准；<br/>"
                  +"6.生产场地证明资料，包括土地使用权证、租用合同等；<br/>"
                  +"7.企业委托或受托其他企业或个人从事符合规定的农林牧渔业项目的委托合同、受托合同、支出明细等证明材料。"
			     +"<br/>二、填报此栏，须符合以下条件：<br/>"
			     +"条例第86条第1款、财税（2008）149号、财税（2011）26号、税务总局公告2011第48号";
	}else if (yhswsx=="SD012"){
		 msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.企业从事相关业务取得的资格证书或证明资料，包括有效期内的远洋渔业企业资格证书、从事农作物新品种选育的认定证书、动物防疫条件合格证、林木种子生产经营许可证、兽医的资格证明等；<br/>"
            +"2.与农户签订的委托养殖合同（“公司+农户”经营模式的企业）；<br/>"
            +"3.与家庭承包户签订的内部承包合同（国有农场实行内部家庭承包经营）；<br/>"
            +"4.农产品初加工项目及工艺流程说明（两个或两个以上的分项目说明）；<br/>"
            +"5.同时从事适用不同企业所得税待遇项目的，每年度单独计算减免税项目所得的计算过程及其相关账册，期间费用合理分摊的依据和标准; <br/>"
            +"6.生产场地证明资料，包括土地使用权证、租用合同等; <br/>"
            +"7.企业委托或受托其他企业或个人从事符合规定的农林牧渔业项目的委托合同、受托合同、支出明细等证明材料。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"条例第86条第2款、财税（2008）149号、财税（2011）26号、税务总局公告2011第48号";
	}else if (yhswsx=="SD021"){
		 msg="一、填报此栏，请留存备查资料：<br/>"
		   +"1.有关部门批准该项目文件; <br/>"
            +"2.公共基础设施项目建成并投入运行后取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.公共基础设施项目完工验收报告; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）; <br/>"
            +"5.公共基础设施项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"6.符合《公共基础设施项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"财税（2008）116号文规定项目";
	} else if (yhswsx=="SD022"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		     +"1.有关部门批准该项目文件; <br/>"
            +"2.公共基础设施项目建成并投入运行后取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.公共基础设施项目完工验收报告; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）; <br/>"
            +"5.公共基础设施项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"6.符合《公共基础设施项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"税务总局公告2019第67号";


	}else if (yhswsx=="SD030"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.符合《环境保护、节能节水项目企业所得税优惠目录》规定范围、条件和标准的情况说明及证据资料; <br/>"
            +"2.环境保护、节能节水项目取得的第一笔生产经营收入凭证（原始凭证及账务处理凭证）; <br/>"
            +"3.环境保护、节能节水项目所得分项目核算资料，以及合理分摊期间共同费用的核算资料; <br/>"
            +"4.项目权属变动情况及转让方已享受优惠情况的说明及证明资料（优惠期间项目权属发生变动的）。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"财税（2009）166号、财税（2016）131号";
	}else if (yhswsx=="SD041"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.所转让的技术产权证明；<br/>"
            +"2.企业发生境内技术转让：<br/>"
            +"（1）技术转让合同（副本）；<br/>"
            +"（2）技术合同登记证明；<br/>"
            +"（3）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（4）实际缴纳相关税费的证明资料；<br/>"
            +"3.企业向境外转让技术：<br/>"
            +"（1）技术出口合同（副本）；<br/>"
            +"（2）技术出口合同登记证书或技术出口许可证；<br/>"
            +"（3）技术出口合同数据表；<br/>"
            +"（4）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（5）实际缴纳相关税费的证明资料；<br/>"
            +"（6）有关部门按照商务部、科技部发布的《中国禁止出口限制出口技术目录》出具的审查意见；<br/>"
            +"4.转让技术所有权的，其成本费用情况；转让使用权的，其无形资产费用摊销情况；<br/>"
            +"5.技术转让年度，转让双方股权关联情况。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"国税函（2009）212号、财税（2010）111号、国税总局公告2013年第62号、国税总局公告2015年第52号";
	}else if (yhswsx=="SD042"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.所转让的技术产权证明；<br/>"
            +"2.企业发生境内技术转让：<br/>"
            +"（1）技术转让合同（副本）；<br/>"
            +"（2）技术合同登记证明；<br/>"
            +"（3）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（4）实际缴纳相关税费的证明资料；<br/>"
            +"3.企业向境外转让技术：<br/>"
            +"（1）技术出口合同（副本）；<br/>"
            +"（2）技术出口合同登记证书或技术出口许可证；<br/>"
            +"（3）技术出口合同数据表；<br/>"
            +"（4）技术转让所得归集、分摊、计算的相关资料；<br/>"
            +"（5）实际缴纳相关税费的证明资料；<br/>"
            +"（6）有关部门按照商务部、科技部发布的《中国禁止出口限制出口技术目录》出具的审查意见；<br/>"
            +"4.转让技术所有权的，其成本费用情况；转让使用权的，其无形资产费用摊销情况；<br/>"
            +"5.技术转让年度，转让双方股权关联情况。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"国税函（2009）212号、财税（2010）111号、国税总局公告2013年第62号、国税总局公告2015年第52号";
	}else if (yhswsx=="SD050"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.清洁发展机制项目立项有关文件；<br/>"
            +"2.企业将温室气体减排量转让的HFC和PFC类CDM项目，及将温室气体减排量转让的N20类CDM项目的证明材料；<br/>"
            +"3.将温室气体减排量转让收入上缴给国家的证明资料；<br/>"
            +"4.清洁发展机制项目第一笔减排量转让收入凭证（原始凭证及账务处理凭证）；<br/>"
            +"5.清洁发展机制项目所得单独核算资料，以及合理分摊期间共同费用的核算资料。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"财税（2018）84号";
	}else if (yhswsx=="SD060"){
		msg="一、填报此栏，请留存备查资料：<br/>"
		    +"1.能源管理合同；<br/>"
            +"2.国家发展改革委、财政部公布的第三方机构出具的合同能源管理项目情况确认表，或者政府节能主管部门出具的合同能源管理项目确认意见；<br/>"
            +"3.项目转让合同、项目原享受优惠的备案文件（项目发生转让的，受让节能服务企业）；<br/>"
            +"4.合同能源管理项目取得第一笔生产经营收入凭证（原始凭证及账务处理凭证）；<br/>"
            +"5.合同能源管理项目应纳税所得额计算表；<br/>"
            +"6.合同能源管理项目所得单独核算资料，以及合理分摊期间共同费用的核算资料。"
            +"<br/>二、填报此栏，须符合以下条件：<br/>"
            +"财税（2010）110号、国税总局公告2013第77号";
	}

	if (isEmptyObject(msg)) {
        return;
    }

	var fjts = parent.layer.open({
		type:1,
		area: ['620px'],
		title: '提示',
		scrollbar: false,
		closeBtn: false,
		content: msg,
		btn: ['确定'],
		yes: function(index,layero){
			parent.layer.close(fjts);
		}
	});
}

function isStartWith(str,key) {
    var reg = new RegExp("^" + key);
    return reg.test(str);
}

