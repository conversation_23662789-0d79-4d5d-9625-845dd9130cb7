
function setBcsbsrwbzsrmbje(je,hl){
	var wbmxxx= formData.ht_.kjqysdssbvo.wbmxGrid.WbmxGridlb;
	var cont=0;
	
	for(var i=0;i<wbmxxx.length;i++){
		var mc=wbmxxx[i].mc;
		if(mc!==""&&mc!==null&&mc!==undefined){
			cont++;
		}
	}
	if(cont<=1){
	return ROUND(je*hl,2);
	}else{
		var bcsbsrwbzsrmbje=formData.ht_.kjqysdssbvo.fdyqkjqkForm.bcsbsrwbzsrmbje;
		return  bcsbsrwbzsrmbje;
	}
	
}

/**
 * 已实名制的，默认获取当前登录人员的姓名，自动带出不允许修改。如果取不到实名制信息时置空
 * @param smzbz 
 * @param smzxm
 * @returns
 */
function setJbr(smzbz,smzxm){
	var jbr=formData.ht_.kjqysdssbvo.slxxForm.jbr;
	if (smzbz=='Y') {
		return smzxm;
	}
	return jbr;
}

/**
 * 已实名制的，默认获取当前登录人员的身份证号码，自动带出不允许修改。如果取不到实名制信息时置空
 * @param bz
 * @param zjhm
 * @returns
 */
function setBlrysfzjhm(bz,zjhm){
	var smzbz=formData.fq_.smzxx.smzbz;
	var smzjhm=formData.fq_.smzxx.zjhm;
	var blrysfzjhm=formData.ht_.kjqysdssbvo.slxxForm.blrysfzjhm;
	var zjlx=formData.fq_.smzxx.zjlx;
	
	if(bz=='xsbz'){
		if(smzbz=='Y'){
			if(zjlx=='201'){
				return zjhm.substring(0,6)+"********"+ zjhm.substring(14);
			}else{
				return zjhm;
			}
		}else{
			return blrysfzjhm;
		}
	}else{
		if(smzbz=='Y'){
			return smzjhm;
		}else{
			return zjhm;
		}
  }
}

/**
 * 录入支付日期时， 当系统当前日期超过“支付日期”+7 提示：该申报已逾期！
 * @param slrq
 * @param zdkjfssjZfrq
 * @returns {Boolean}
 */
function flagDate(zdkjfssjZfrq){
	if (zdkjfssjZfrq===""||zdkjfssjZfrq===null) {
		return true;
	}else{
		var dateTemp = zdkjfssjZfrq.split("-");
		var nDate = new Date(dateTemp[1] + '-' + dateTemp[2] + '-' + dateTemp[0]); //转换为MM-DD-YYYY格式  
		  var millSeconds = Math.abs(nDate) + (7 * 24 * 60 * 60 * 1000);
		  var rDate = new Date(millSeconds);
		  var year = rDate.getFullYear();
		  var month = rDate.getMonth() + 1;
		  if (month < 10) month = "0" + month;
		  var date = rDate.getDate();
		  if (date < 10) date = "0" + date;
		  var jqdate=year + "-" + month + "-" + date;
		
		  var currentTime = getCurrentSystemTime();
		 
		  if (DATE_CHECK_TIME(jqdate,currentTime)) {
				return false;
			}else{
				return true;
			}
	}
}
/**
 * 将"YYYY-MM-DD"格式转换为"XX年XX月XX日"
 * @returns 
 */
function slrqFormat(){
	var slrq=formData.ht_.kjqysdssbvo.slxxForm.slrq;
	slrq=slrq.substring(0,slrq.indexOf('-'))+"年"+slrq.substring(slrq.indexOf('-')+1,slrq.lastIndexOf('-'))+"月"+slrq.substring(slrq.lastIndexOf('-')+1)+"日";
	return slrq;
}

function sfyxsb(){
	var sbrq=formData.ht_.kjqysdssbvo.kjywrjbxxForm.skssqq;
	var sfyxsb=formData.fq_.sfyxsb;

	if(DATE_CHECK_TIME(sbrq , "2019-10-01")&&sfyxsb!=="Y"){
		var msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;【扣缴企业所得税申报（2019版）】启用时间为2019年10月1日，当前属期请在【扣缴企业所得税申报（2015版）】完成申报，谢谢！ ";
		var a =layer.confirm(msg,{
		 	area: ['320px','260px'],
		 	title:'提示',
		 	closeBtn : 0,
			btn : ['确定']
			},function(data){
			layer.close(a);
			closeWin();
	});
	
	}
	
}

/**
 * 关闭当前页面
 */

function closeWin() {

	if (navigator.userAgent.indexOf("MSIE") > 0) {
		if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
			window.opener = null;
			window.close();
		} else {
			window.open('', '_top');
			window.top.close();
		}
	} else if (navigator.userAgent.indexOf("Firefox") > 0) {
		window.location.href = 'about:blank ';
		window.close();
	} else if (navigator.userAgent.indexOf("Chrome") > 0) {
		top.open(location, '_self').close();
	} else {
		window.open('', '_top');
		window.top.close();
	}

}

/**
 * 获得当前系统时间 格式"yyyy-MM-dd"
 */
function getCurrentSystemTime(){
	//获取当前日期
	var currentDate = new Date();
	var currentMonth = currentDate.getMonth()+1;
	var currentDay = currentDate.getDate();
	if (currentMonth < 10){
		currentMonth = "0"+currentMonth;
	}
	if (currentDay < 10){
		currentDay = "0"+currentDay;
	}
	return currentDate.getFullYear()+"-"+currentMonth+"-"+currentDay;
}



	/**
	 * 设置在中国的联系地址、邮政编码
	 * @param kjywrZcdz
	 * @param kjywrYwdz
	 * @param kjywryzbm
	 */
function setLxdzYzbm(Zcdz,Ywdz,yzbm){
	if(!isEmptyObject(Zcdz)){
		return Zcdz+"；"+yzbm;
	}else{
		return Ywdz+"；"+yzbm;
	}
}

/**
 * 在中国的联系地址、邮政编码：联系地址与邮政编码之间用分号隔开，联系地址长度100个字符，邮政编码长度6个字符
 * 在居民国（地区）的联系地址、邮政编码：联系地址与邮政编码之间用分号（中文分号）隔开，总长度不超过107字符。
 */
function checkLxdqyzbm(lxdqyzbm,bz){
	if(isEmptyObject(lxdqyzbm)){
		return false;
	}else if((lxdqyzbm).lastIndexOf("；")==-1){
		return false;
	}else{
		var lastIndexOf=lxdqyzbm.lastIndexOf("；");
		var yzbm=lxdqyzbm.substring(lastIndexOf+1);
		var lxdz=lxdqyzbm.substring(-1,lastIndexOf);
		if(bz=='zg'){
			var regYzbm=/^\d{6}$/;
			if(lxdz.length<=100&&(isEmptyObject(yzbm) || regYzbm.test(yzbm))){
				return true;
			}else{
				return false;
			}
		}else{
			if(lxdqyzbm.length<=107){
				return true;
			}else{
				return false;
			}
		}
	}
}


/**
 * 检验电子邮箱
 * @param dzxx
 * @returns {Boolean}
 */
function checkDzxx(dzxx){
	//var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})$/;
	//取消协定附表[10.电子邮箱]中对点后域名的校验
	var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)/;
	if(reg.test(dzxx)){
		return true;
	}else{
		return false;
	}
}

/**
 * 设置非居民纳税人签章或签字日期，自动带出系统日期，不可修改。
 */
function setFjmnsrqzrq(){
	return getCurrentSystemTime();
}

/**
 * 设置扣缴义务人名称
 * @param kjywrmc
 * @param kjywrywmc
 */
function setKjywrmc(kjywrmc,kjywrywmc){
	if(!isEmptyObject(kjywrmc)){
		return kjywrmc;
	}else{
		return kjywrywmc;
	}
}

/**
 * 设置受理日期
 */
function setSlrq(){
	return getCurrentSystemTime();
}

/**
 * 提交时对协定待遇表报文做特殊处理
 */
function delXddybdBw(xdbChangeFlag,syssxdtkJmse){
	var nsrxxsbvo=formData.ht_.nsrxxsbvo;
	var xddybw = formData.ht_.kjqysdssbvo.xddyYwbd;
	if (isEmptyObject(xddybw)) {
		return;
	}
	var dzbd=formData.kz_.dzbd;
	
	if(isEmptyObject(nsrxxsbvo)){
	    if(!xdbChangeFlag&&syssxdtkJmse<=0){
	    	delete formData.ht_.kjqysdssbvo.xddyYwbd;
	    	var xdbd="BDA0611121---";
	    	if(dzbd.indexOf(xdbd)>-1){
	    		var star=dzbd.indexOf(xdbd);
	    		var end=star+xdbd.length;
	    		var dzbdStr=dzbd.substring(0,star)+dzbd.substring(end);
	    		formData.kz_.dzbd=dzbdStr;
	    	}
	     }
	}else{
		var dzxx=formData.ht_.kjqysdssbvo.xddyYwbd.xddyxx.dzxx;
		var ssxdmc=formData.ht_.kjqysdssbvo.xddyYwbd.xddyxx.ssxdmc;
		var ssxdsdlxDm=formData.ht_.kjqysdssbvo.xddyYwbd.xddyxx.ssxdsdlxDm;
		 if(isEmptyObject(dzxx)&&isEmptyObject(ssxdmc)&&isEmptyObject(ssxdsdlxDm)&&syssxdtkJmse<=0&&!xdbChangeFlag){
			 	delete formData.ht_.kjqysdssbvo.xddyYwbd;
		    	var xdbd="BDA0611121---";
		    	if(dzbd.indexOf(xdbd)>-1){
		    		var star=dzbd.indexOf(xdbd);
		    		var end=star+xdbd.length;
		    		var dzbdStr=dzbd.substring(0,star)+dzbd.substring(end);
		    		formData.kz_.dzbd=dzbdStr;
		    	}
		 }
	}
}

/**
 * 对于属期在2020年及之后，当第12行享受协定待遇金额大于0时，增加非强制性提示：享受协定待遇应填报《非居民纳税人享受协定待遇信息报告表》。
 * @param jmse
 * @param ssqq
 * @returns {Boolean}
 */
function fzdjy(jmse,ssqq){
	if(jmse>0&&DATE_CHECK_TIME_SIZE('2020-01-01',ssqq)){
		return false;
	}else{
		return true;
	}
}

/**
 * 初始化更正申报的数据
 */
function initGzsbData(){
	
	//将数字转化为字符串
	formData.ht_.kjqysdssbvo.nsrjbxxForm.sbsdlxjdm=formData.ht_.kjqysdssbvo.nsrjbxxForm.sbsdlxjdm+"";
	formData.ht_.kjqysdssbvo.fdyqkjqkForm.htbz=formData.ht_.kjqysdssbvo.fdyqkjqkForm.htbz+"";
	formData.ht_.kjqysdssbvo.kjywrjbxxForm.kjywlx=formData.ht_.kjqysdssbvo.kjywrjbxxForm.kjywlx+"";
	if(formData.ht_.kjqysdssbvo.xddyYwbd!=undefined){
		formData.ht_.kjqysdssbvo.xddyYwbd.xddyxx.ssxdsdlxDm=formData.ht_.kjqysdssbvo.xddyYwbd.xddyxx.ssxdsdlxDm+"";
	}

	formData.ht_.kjqysdssbvo.fdyqkjqkForm.sfzfbz1=formData.ht_.kjqysdssbvo.fdyqkjqkForm.sfzfbz1+"";
	var wbmxGridlb=formData.ht_.kjqysdssbvo.wbmxGrid.WbmxGridlb
	for(var i=0;i<wbmxGridlb.length;i++){
		wbmxGridlb[i].mc=wbmxGridlb[i].mc+"";
	}
	//法定源泉扣缴时（kjywlx=1），指定扣缴这里应该全为空，初始化不需要加载值，不管核心有无返回。
	var kjywlx=formData.ht_.kjqysdssbvo.kjywrjbxxForm.kjywlx
	if(kjywlx=="1"){
		var zgswjgzdkjqkForm='{"zdkjwsbh": "","zdskjsff": "","swjghdLrl": 0,"zdkjfssjBz": "","zdkjfssjZfrq": "","zdkjfssjZfje": 0,"zdkjSjyjnQysdse": 0,"znjssqq": "","znjssqz": ""}';
		var zgswjgzdkjqkFormObj=eval('('+zgswjgzdkjqkForm+')');
		formData.ht_.kjqysdssbvo.zgswjgzdkjqkForm=zgswjgzdkjqkFormObj;
	}else{
		var fdyqkjqkForm='{"htmc": "","htbh": "","htzxqssj": "","htzxzzsj": "","sfzfbz1": "","htjewz": "","htbz": "","kjywfssjBz": "","kjywfssjZfrq": "","bcsbsrrmbje": 0,"bcsbsrwbmc": "","bcsbsrwbje": 0,"bcsbsrwbhl": 0,"bcsbsrwbzsrmbje": 0,"bcsbsrrmbjehj": 0,"kce": 0,"ynssde": 0,"fdyqKjsysl": 0.1,"yjnQysdse": 0,"fdyqKjjmqysdse": 0,"syssxdtk": "","syssxdtkJmse": 0,"gnsfyhxm": "","gnsfyhxmJmse": 0,"fdyqKjsjyjnqysdse": 0,"dynsxxbgbh": "","bh": "","xthtbh":""}';
		var fdyqkjqkFormObj=eval('('+fdyqkjqkForm+')');
		formData.ht_.kjqysdssbvo.fdyqkjqkForm=fdyqkjqkFormObj;
	}
	
}


//DLGSWSBSSC-570
function ywControlBtn(dzbdbm) {
	if (dzbdbm==="BDA0611121") {
		var swjgDm = formData.fq_.nsrjbxx.swjgDm;
		var subSwjgDm = swjgDm.substring(1,3);
		if(subSwjgDm != "11"){
			var msg="根据《国家税务总局关于发布<非居民纳税人享受协定待遇管理办法>的公告》（国家税务总局公告2019年第35号）规定，请扣缴义务人如实填写本表，并将加盖非居民纳税人签章或签字的《非居民纳税人享受协定待遇信息报告表》纸质件留存备查。详情请咨询主管税务机关。";
			layer.alert(msg, {title:"提示"});
		}
	} 
}

/**
 * 申报提交时框架校验成功后的业务特有校验
 * @param callBeforSubmitForm ：回调方法，调用表单提交前的业务特有提示
 * @param callSubmitForm ：回调方法，调用表单提交
 * @param params : 回调参数
 */
function doBeforeCtipsVerify(isSecondCall) {
	// 执行回调函数
	ctips(isSecondCall);
}
/*联系电话和邮政编码只能为数字且不超过限定长度*/
function checkReg(mid,len){
	var res=true;
	if(len==6&&mid!=null&&mid!=''){
        var reg=/^\d{0,6}$/;
        res=reg.test(mid);
	}else if(len==11&&mid!=null&&mid!=''){
        var reg=/^\d{0,11}$/;
        res=reg.test(mid);
	}
    return res;
}


/**
 * 重写暂存
 */
function tempSaveOverride(){
	debugger;
	//删除数据包过大
	delete formData.kz_.temp.zb.allFdhtbaxx;
	tempSave();
}


function afterFormulaExcuted(){
	var swjgDm = formData.fq_.nsrjbxx.swjgDm;
	var subSwjgDm = swjgDm.substring(1,3);
	if(subSwjgDm=="52"){
		
	    var br1space = "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var contentmsg = "<div class=\"layui-text\"  style=\"font-size:14px\">尊敬的纳税人：\n" +
            br1space + "  如果您是首次办理扣缴企业所得税申报，您是否已办理“扣缴税款登记”业务?\n" + '</div>';
		  layer.confirm(contentmsg,{
	            area: ['400px', '300px'],
	            title: '提示',
	            btn: ["是", "否"],
	            btn2: function (index) {
	            	  layer.close(index);
	            	var  backUrl = parent.location.protocol + "//" + parent.location.host+"/sxsq-cjpt-web/biz/sxsq/kjskdj?swsxDm=SXA011008001&tcbz=Y";
	            	  parent.window.location.href = backUrl;
					  parent.window.top.location.href =backUrl;
	            }
	        }, function (index) {
	        	  layer.close(index);
	        	  
	        	  var br2space = "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
	              var nbsparr="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
	              var br2 = "<br/>";
	              var content = "<div class=\"layui-text\"  style=\"font-size:16px\">尊敬的纳税人：\n" +
	                  br2space + " 根据《国家税务总局关于发布<中华人民共和国非居民企业所得税预缴申报表（2019年版）>等报表的公告》（国家税务总局公告2019年第16号）、《国家税务总局关于非居民企业所得税源泉扣缴有关问题的公告》（国家税务总局公告2017年第37号）和《非居民承包工程作业和提供劳务税收管理暂行办法》（国家税务总局令第19号）等相关法律法规规定，如果您申报的所得类型为股息、利息、特许权使用费、财产转让等，请您选择“法定源泉扣缴申报“：如果您申报的所得类型为承包工程劳务、提供劳务服务等,请您选择“指定扣缴申报”。如果您符合享受税收协定待遇条件，请您在申报时填写附表《非居民纳税人享受税收协定待遇信息报告表》。详情请咨询主管税务机关。\n" +
	                  br2space +nbsparr+nbsparr+nbsparr+nbsparr  + " 国家税务总局贵州省税务局\n" +
	                  br2space +nbsparr+nbsparr+nbsparr+nbsparr  +  " 2021年6月15日\n" 
	                  + '</div>';

	              var b = parent.layer.open({
	                  type: 1,
	                  btn: ['确定'],
	                  title: "提示",
	                  content: content,
	                  area: ['600px', '450px'],
	                  closeBtn: 0,
	                  yes: function (index, layero) {
	                      parent.layer.close(b);
	                  }
	              });
	        	  
	        });

	}

}

/**
 * 获取url参数
 */
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象
    var r = window.location.search.substr(1).match(reg);  // 匹配目标参数
    if (r != null) return unescape(r[2]); return null; // 返回参数值
}

function getfbgjdhzmDm(szDm){
	if(isNull(szDm)){
		return "";
	}
	var zmDm="";
	if (!formCT.gjhdqCT1){
		//静态码表 zsxmCT
		getDmFromCodeTable([{
			'url':'nf_dm_gy_gjhdq1.json',
			'name':'gjhdqCT1',
			'node':'',
			'dm':'root.dm',
			'mc':'root.mc',
			'filter':''}],'gjhdqCT1','','');
	}
	if (!formCT.gjhdqszCT){
		//静态码表 zsxmCT
		getDmFromCodeTable([{
			'url':'nf_dm_gy_gjhdqsz.json',
			'name':'gjhdqszCT',
			'node':'',
			'dm':'',
			'mc':'',
			'filter':''}],'gjhdqszCT','','');
	}
	var  szCt=formCT.gjhdqCT1;
	var  zmCt=formCT.gjhdqszCT;
	var mcobj=szCt[szDm];
	if(!isNull(mcobj)){
		var mc=mcobj.split("|")[1];
		for(var a in zmCt){
			var zmmcObj=zmCt[a];
			var zmmc=zmmcObj.split("|")[1];
			if(mc==zmmc){
				zmDm=a;
				break;
			}
		}
	}

	return zmDm;
}

// 是否展示非居民企业递延缴纳预提所得税信息报告表相关附表
function showDyjnytsdsbg(t_kjywlx, gnsfyhxm, gnsfyhxmJmse) {
	// 解决控制台公式执行此方法报错
}

function xdSheetlist(syssxdtk){
	return true;
}

function syncFetch(_url, _async, data) {
	let res;
	const xhr = new XMLHttpRequest();
	xhr.open('GET', _url, _async);
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			res = JSON.parse(xhr.response);
		}
	};

	xhr.send(JSON.stringify(data));
	return res;
}

/**
 * 从码表中获取值
 * @param codeTableAttrs 初始化码表需要的参数（参照ng-codetable把属性改成json对象，如果ng-codetable含有contact需要传入数组，统一都传入数组）
 * @param name 码表对象的名称（含有contact传入数组需要知道取最终码表对象的名称）
 * @param key 码表中对应dm的值，根据该值获取相应的value值
 * @param fieldName 码表中对应dm的value值是Object，根据该值获取Object中fieldName对应的值（可以不传）
 *
 * 实现步骤
 *  1、根据传入attributes初始化码表
 *  2、根据传入的name和key查找码表中相应key的值
 */
function getDmFromCodeTable(codeTableAttrs, name, key, fieldName, defVal) {
    if (defVal == undefined) {
        defVal = "";
    }
    
	var callBack = function() {
		var codeObject = formCT[name];
		if (undefined === codeObject) {
			return defVal;
		} else {
			var codeValue = codeObject[key];
			if (codeValue && fieldName) {
				codeValue = codeValue[fieldName];
			}

			if (!codeValue) {
				codeValue = defVal;
			}

			return codeValue;
		}
	}

	if (formCT[name]) {
		return callBack();
	} else {
		if (codeTableAttrs && codeTableAttrs.length > 0) {
			//初始化码表
			initCodeTable(undefined, codeTableAttrs[0]);
			return callBack();
		} else {
			return defVal;
		}
	}
}

/**
 * 初始化码表
 * 从码表数据来源层面来说支持三种方式的码表
 *  1、码表数据来源于url（json文件和普通的getDmb.do）
 *  2、码表数据来源于期初数model
 *  3、码表数据来源于带参数的请求params（/nssb/getDtdmb.do）
 * 码表请求支持同步异步配置async（默认为异步true）
 * 码表支持累加contact，对于两个来源的数据码表的name一样，最后的结果会做合并操作
 * @param $scope
 * @param attributes
 */
function initCodeTable($scope, attributes) {
    var _name = attributes["name"];
    var _url = attributes["url"];
    var _model = attributes["model"];
    var _params = attributes["params"];
    var _jsons = {};
   
    if (undefined === formCT[_name] || JSON.stringify(formCT[_name]) === "{}") {//判断是否已缓存
        if ((undefined !== _url && "" !== _url) || (undefined !== _params && "" !== _params)) {//URL来源
            getDmFromUrl($scope, attributes, _jsons);
        } else if (undefined !== _model && "" !== _model) {//期初数来源
            getDmFromModel($scope, attributes, _jsons);
        } else {
            //codetable指令相关参数缺失
            console.log("ERROR:codetable指令相关参数缺失，请检查...");
            return;
        }
    }
}

/**
 * 从url（json文件和普通的getDmb.do）获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromUrl($scope, attributes, _jsons) {
    // 默认为同步
    var _async = false;
    var _node = attributes["node"];
    var _name = attributes["name"];
    var _url = attributes["url"];

    var _params = attributes["params"];
    var _dynamicParam = attributes["dynamic"];
    var _data;

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    var data = {};
	var index = location.pathname.lastIndexOf('/');
	_url = location.pathname.substr(0, index) + "/static/sb/kjqysdsbg/form/" + _url;
        
	// 允许添加参数，param为key, dynamicParam为value，可以多个逗号隔开，但个数必须一致
	if (_params && _dynamicParam) {
		var aryParam = _params.split(',');
		var aryDynamic = _dynamicParam.split(',');
		if (aryParam && aryDynamic && aryDynamic.length === aryDynamic.length) {
			for (var idx = 0; idx < aryParam.length; idx++) {
				_data = jsonPath($scope ? $scope.formData : formData, aryDynamic[idx])[0];
				if (_data) {
					_url = _url + (idx === 0 ? "?" : "&") + aryParam[idx] + "=" + _data;
				} else {
					// 发现有不符合的，则不进行拼接
					break;
				}
			}
		}
	}

	const response = syncFetch(_url, _async, data);
	_data = response;
	if (undefined === _url || "" === _url) {
		if (typeof response === "string") {
			response = JSON.parse(response);
		}
		if (response && response["dtdmbxx"]) {
			response = response["dtdmbxx"];
		}
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}
		if (typeof _data === "string") {
			_data = JSON.parse(_data);
		}
		if (_data && _data["root"]) {
			Object.keys(_data).forEach((k)=> {
				const v = _data[k];
				_jsons[v["dm"]] = v;
			});
			_data = _jsons;
		}
	} else {
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}

		var doFilter = false;
		Object.keys(_data).forEach((k)=> {
			const v = _data[k];
			doFilter = false;
			if (filterKey && filterValue && v) {
				if (v[filterKey] !== filterValue) {
					doFilter = true;
				}
			}
			if (!doFilter) {
				_jsons[k] = v;
			}
		});
	}

	formCT[_name] = _jsons;
}

/**
 * 从期初数model获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromModel($scope, attributes, _jsons) {
    var _name = attributes["name"];
    var _multi = attributes["multi"];
    var _data;

    var _model = attributes["model"];
    var _dm = attributes["dm"];

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    _data = jsonPath($scope ? $scope.formData : formData, _model)[0];
    if (undefined === _data || "" === _data) {
        console.log("ERROR:codetable指令缓存代码表获取的data为空，对应的model为:" + _model + ",name为:" + _name);
        return;
    }

    if (undefined !== _dm && "" !== _dm) {
        var doFilter = false;
        Object.keys(_data).forEach((k)=> {
			const v = _data[k];
            doFilter = false;
            if (filterKey && filterValue) {
                if (v[filterKey] !== filterValue) {
                    doFilter = true;
                }
            }
            if (!doFilter) {
                if (_multi === 'true') {
                    if (!_jsons[v[_dm]])
                        _jsons[v[_dm]] = [];
                    _jsons[v[_dm]].push(v);
                } else {
                    _jsons[v[_dm]] = v;
                }
            }
        });
        _data = _jsons;
    }

	formCT[_name] = _data;
}

export default {
	setBcsbsrwbzsrmbje,
	setJbr,
	setBlrysfzjhm,
	flagDate,
	slrqFormat,
	sfyxsb,
	getCurrentSystemTime,
	setLxdzYzbm,
	checkLxdqyzbm,
	checkDzxx,
	setFjmnsrqzrq,
	setKjywrmc,
	setSlrq,
	delXddybdBw,
	fzdjy,
	initGzsbData,
	ywControlBtn,
	checkReg,
	getfbgjdhzmDm,
	showDyjnytsdsbg,
	xdSheetlist,
	getDmFromCodeTable
}