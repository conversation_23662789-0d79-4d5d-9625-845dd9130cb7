import store from '@/pages/index/store';
var sbzxCtl = window.STATIC_ENV_CONFIG.API_PREFIX ? window.STATIC_ENV_CONFIG.API_PREFIX : '/sbzx/api/sdsfsgjssb';
var kczrzxsrDm = '30714'; // 30714

function initKczrzxsrDm() {
  formData.fq_.kczrzxsrDm = kczrzxsrDm;
}

function hyDm() {
  var userType = arguments[0];
  var nsrjbxx_hyDm = arguments[1];
  var sbSbbcTjqtxxVO_hyDm = arguments[2];
  var shengDm = arguments[3];
  if ('510000' == shengDm && 'USER_TYPE_ZRR' == userType) {
    formData.ht_.hxzgsb10531Request.sbSbbcTjqtxxVO.hyDm = '8290';
    return '8290';
  } else if ('USER_TYPE_ZRR' == userType) {
    return sbSbbcTjqtxxVO_hyDm;
  } else {
    return nsrjbxx_hyDm;
  }
}

function scenceCs() {
  var href = window.location.href;
  var hrefParams = href.substring(href.indexOf('?') + 1);
  if (hrefParams.indexOf('Pzxh' !== -1)) return 'sbcwgz';
  return 'zcsb';
}

/**
 * 计算合计信息
 */
function calculate_fssrtysb_hjxx(t_checkedbz, yjfjs, jcx, jfyj, bqynsfe, jmsfe, bqyjsfe, bqybtsfe) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var yjfjsHj = 0;
  var jcxHj = 0;
  var jfyjHj = 0;
  var bqynsfeHj = 0;
  var jmsfeHj = 0;
  var bqyjsfeHj = 0;
  var bqybtsfeHj = 0;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].t_checkedbz) {
      yjfjsHj += Number(skxxGridlb[i].yjfjs);
      jcxHj += Number(skxxGridlb[i].jcx);
      jfyjHj += Number(skxxGridlb[i].jfyj);
      bqynsfeHj += Number(skxxGridlb[i].bqynsfe);
      jmsfeHj += Number(skxxGridlb[i].jmsfe);
      bqyjsfeHj += Number(skxxGridlb[i].bqyjsfe);
      bqybtsfeHj += Number(skxxGridlb[i].bqybtsfe);
    }
  }
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.t_yjfjshj = yjfjsHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.t_jcxhj = jcxHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.jfyjhj = jfyjHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.bqynsfehj = bqynsfeHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.jmsfehj = jmsfeHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.bqyjsfehj = bqyjsfeHj;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.bqybtsfehj = bqybtsfeHj;
}

//全选操作
function selectedAllFssr(allSelected) {
  //选择或取消全选同时改变是否提交标志
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (allSelected == true) {
      skxxGridlb[i].t_checkedbz = true;
    } else {
      skxxGridlb[i].t_checkedbz = false;
    }
  }
  calculate_fssrtysb_hjxx(); // 计算合计信息
  return allSelected;
}

/**
 * 删除未勾选的SkxxGridlb
 */
function delUnSelectedSkxxGridlb() {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var sbdllfssrndhsqjbjbxxGridlb =
    formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjbjbxx.sbdllfssrndhsqjbjbxxGrid
      .sbdllfssrndhsqjbjbxxGridlb;
  var sbdllfssrndhsqjbGridlb =
    formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjb.sbdllfssrndhsqjbGrid.sbdllfssrndhsqjbGridlb;

  var skxxGridlbSubmit = [];
  var sbfssrtdxmGridSubmit = [];
  var sbdllfssrndhsqjbjbxxGridSubmit = [];
  var sbdllfssrndhsqjbGridSubmit = [];

  var tdxmxxcj = formData.fq_.tdxmxxcj;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].t_checkedbz) {
      skxxGridlbSubmit.push(skxxGridlb[i]);

      // 处理特定项目采集
      if (!isNull(tdxmxxcj) && tdxmxxcj.indexOf(skxxGridlb[i].zsxmDm) !== -1) {
        for (var j = 0; j < sbfssrtdxmGridlb.length; j++) {
          var pcuuidCj = sbfssrtdxmGridlb[j].pcuuid;
          if (skxxGridlb[i].pcuuid === pcuuidCj) {
            sbfssrtdxmGridSubmit.push(sbfssrtdxmGridlb[j]);
            break;
          }
        }
      }

      // 处理电力类汇算清缴
      if (isDlhsqj(skxxGridlb[i].zsxmDm, skxxGridlb[i].zszmDm, skxxGridlb[i].zszmMc)) {
        for (var k = 0; k < sbdllfssrndhsqjbGridlb.length; k++) {
          var dlluuidHsqj = sbdllfssrndhsqjbGridlb[k].dlluuid;
          if (skxxGridlb[i].dlluuid === dlluuidHsqj) {
            sbdllfssrndhsqjbjbxxGridSubmit.push(sbdllfssrndhsqjbjbxxGridlb[k]);
            sbdllfssrndhsqjbGridSubmit.push(sbdllfssrndhsqjbGridlb[k]);
            break;
          }
        }
      }
    }
  }

  formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb = skxxGridlbSubmit;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb = sbfssrtdxmGridSubmit;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjbjbxx.sbdllfssrndhsqjbjbxxGrid.sbdllfssrndhsqjbjbxxGridlb =
    sbdllfssrndhsqjbjbxxGridSubmit;
  formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjb.sbdllfssrndhsqjbGrid.sbdllfssrndhsqjbGridlb =
    sbdllfssrndhsqjbGridSubmit;

  // 提交报文不存在特定项目时，把特定项目表的节点清空
  if (sbfssrtdxmGridSubmit.length === 0) {
    formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid = {};
  }

  if (sbdllfssrndhsqjbGridSubmit.length === 0) {
    formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjb.sbdllfssrndhsqjbGrid = {};
  }

  if (sbdllfssrndhsqjbjbxxGridSubmit.length === 0) {
    formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbdllfssrndhsqjbjbxx.sbdllfssrndhsqjbjbxxGrid = {};
  }
}

/**
 * 校验未勾选项目
 */
function jySelectXm(allSelected, checkedbz) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].t_checkedbz) {
      return true;
    }
  }
  return false;
}

//提交前校验
function doAfterVerify(callBeforSubmitForm, callSubmitForm, params) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var sftj = false;
  var checkedbz1 = false;
  var checkedbz2 = false;
  var yjfjs1 = 0;
  var yjfjs2 = 0;
  //汇算清缴
  var checkedbz3 = false;
  var checkedbz4 = false;
  var yjfjs3 = 0;
  var yjfjs4 = 0;
  var checkMap = {};
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].t_checkedbz && skxxGridlb[i].zspmDm == '301580110') {
      if (!isNull(skxxGridlb[i].zszmDm)) {
        checkedbz3 = skxxGridlb[i].t_checkedbz;
        yjfjs3 = skxxGridlb[i].yjfjs;
      } else {
        checkedbz1 = skxxGridlb[i].t_checkedbz;
        yjfjs1 = skxxGridlb[i].yjfjs;
      }
    }
    if (skxxGridlb[i].t_checkedbz && skxxGridlb[i].zspmDm == '301580210') {
      if (!isNull(skxxGridlb[i].zszmDm)) {
        checkedbz4 = skxxGridlb[i].t_checkedbz;
        yjfjs4 = skxxGridlb[i].yjfjs;
      } else {
        checkedbz2 = skxxGridlb[i].t_checkedbz;
        yjfjs2 = skxxGridlb[i].yjfjs;
      }
    }
    if (skxxGridlb[i].t_checkedbz) {
      var checkKey =
        skxxGridlb[i].zsxmDm +
        '_' +
        skxxGridlb[i].zspmDm +
        '_' +
        skxxGridlb[i].zszmDm +
        '_' +
        skxxGridlb[i].skssqq +
        '_' +
        skxxGridlb[i].skssqz +
        '_' +
        isNull(skxxGridlb[i].fqbz)
          ? ''
          : skxxGridlb[i].fqbz;
      if (checkMap.hasOwnProperty(checkKey)) {
        var msg = '【' + skxxGridlb[i].zsxmMc + '】-【' + skxxGridlb[i].zspmMc + '】';
        if (!isNull(skxxGridlb[i].zszmDm)) {
          var zszmMc = isNull(skxxGridlb[i].zszmMc) ? formCT.zspmCT[skxxGridlb[i].zszmDm].zszmMc : skxxGridlb[i].zszmMc;
          msg += '-【' + zszmMc + '】';
        }
        msg += '在[' + skxxGridlb[i].skssqq + ']至[' + skxxGridlb[i].skssqz + ']存在重复申报，请核实后重新填写！';
        layer.alert(msg, { icon: 2 });
        $('body').unmask();
        prepareMakeFlag = true;
        return;
      } else {
        checkMap[checkKey] = checkMap;
      }
      sftj = true;
    }
  }

  var swjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 3);
  if (
    swjgDm == '144' &&
    formData.fq_.isSjdwgs == 'N' &&
    (checkedbz1 != checkedbz2 ||
      (checkedbz1 && checkedbz2 && yjfjs2 * 30000 != yjfjs1 * 10000) ||
      checkedbz3 != checkedbz4 ||
      (checkedbz3 && checkedbz4 && yjfjs4 * 30000 != yjfjs3 * 10000))
  ) {
    layer.alert(
      '温馨提示：<br>1.自备电厂企业认定国家重大水利工程建设基金时应同时认定“南水北调工程建设基金”和“三峡工程后续工作资金”2个征收品目，且每期申报国家重大水利工程建设基金时，需将当前所属期的自备电厂自发自用电量按照3:1的比例拆分填写“南水北调工程建设基金”和“三峡工程后续工作资金”两个征收品目的“应缴费基数”进行申报缴费。<br>2.自备电厂企业需申报缴纳国家重大水利工程建设基金和可再生能源发展基金2项电力基金。',
      {
        title: '提示',
        icon: 2,
        area: ['400px'],
      },
    );
    // $("body").unmask();
    prepareMakeFlag = true;
    return;
  }
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  if (swjgDm != '137' || formData.fq_.nsrjbxx.swjgDm.substring(0, 5) == '13702') {
    for (var i = 0; i < sbfssrtdxmGridlb.length; i++) {
      if (!isNull(sbfssrtdxmGridlb[i].pcuuid)) {
        var flag = true;
        var xmpmMc = '';
        for (var j = 0; j < skxxGridlb.length; j++) {
          if (sbfssrtdxmGridlb[i].pcuuid == skxxGridlb[j].pcuuid) {
            xmpmMc =
              skxxGridlb[j].zsxmMc +
              '|' +
              skxxGridlb[j].zspmMc +
              '|' +
              (isNull(skxxGridlb[j].zszmMc) ? '' : skxxGridlb[j].zszmMc);
            if (
              sbfssrtdxmGridlb[i].zsxmDm == skxxGridlb[j].zsxmDm &&
              sbfssrtdxmGridlb[i].zspmDm == skxxGridlb[j].zspmDm &&
              sbfssrtdxmGridlb[i].zszmDm == skxxGridlb[j].zszmDm &&
              sbfssrtdxmGridlb[i].htbh == skxxGridlb[j].htbh &&
              sbfssrtdxmGridlb[i].wyjbz == skxxGridlb[j].wyjbz &&
              sbfssrtdxmGridlb[i].jkqx == skxxGridlb[j].jkqx &&
              sbfssrtdxmGridlb[i].fqbz == skxxGridlb[j].fqbz &&
              skxxGridlb[j].t_checkedbz
            ) {
              flag = false;
            }
          }
        }
        if (flag) {
          var mag =
            '【' +
            xmpmMc +
            '】中合同编号【' +
            sbfssrtdxmGridlb[i].htbh +
            '】，《非税收入通用申报》勾选的税款信息和《非税收入特定项目采集表》存在填写数据不一致，请返回修改！';
          layer.alert(mag, { icon: 2 });
          prepareMakeFlag = true;
          return;
        }
      }
    }
  }
  if (sftj) {
    callBeforSubmitForm(callSubmitForm, params);
  } else {
    layer.alert('请选择需要申报的项目！', { icon: 2 });
    // $("body").unmask();
    prepareMakeFlag = true;
    return;
  }
}

/**
 * 非税收入通用申报的代征企业跳转《电力类非税收入明细信息采集表》增加提示
 * @returns
 */
function dzqyToDl() {
  var zsxmDmStr = formData.fq_.dzqyZsxmStr;
  var skssqq = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqq;
  var skssqz = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqz;
  var flag = false;

  if (zsxmDmStr != null && zsxmDmStr != '') {
    var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
    for (var i = 0; i < skxxGridlb.length; i++) {
      var zsxmDm = skxxGridlb[i].zsxmDm;
      if (!isNull(zsxmDm) && zsxmDmStr.indexOf(zsxmDm) !== -1) {
        flag = true;
        break;
      }
    }
  }

  if (flag) {
    var tips =
      '如为代征企业，请在资料报送与信息采集下的“电力类非税收入明细信息采集表”功能处，填报《电力类非税收入明细信息采集表》。';
    // parent.layer.confirm(tips, {
    //     title: '提示',
    //     btn: ['确定', '取消'],
    //     btn1: function (index) {
    //         /*var url = location.protocol + "//" + location.host + "/sbzx-cjpt-web/biz/sb/dllfssrmxcj?ywzt=Y&gdslxDm=1&sssqQ=" + skssqq + "&sssqZ=" + skssqz;
    //         window.open(url);*/

    //         parent.layer.close(index);
    //     },
    //     btn2: function (index) {
    //         parent.layer.close(index);
    //     }
    // });

    $vue.$gtDialog.confirm({
      header: '提示',
      theme: 'info',
      body: tips,
      closeOnOverlayClick: false,
      closeOnEscKeydown: false,
    });
  }
}

/**
 * 返回引导页
 */
function backBegin() {
  window.location.href = 'javascript:history.go(-1)';
}

/**
 * 比较两个时间的大小，可以相等 str1<=str2 str1 yyyy-mm-dd str2 yyyy-mm-dd return boolean
 */
function DATE_CHECK_TIME_SIZE(str1, str2) {
  if (str1 && str2 && str1.length == 10 && str2.length == 10) {
    var start = parseDate(str1);
    var end = parseDate(str2);
    return start <= end;
  } else {
    return false;
  }
}

/**
 * 根据时间字符串 生成时间对象
 * dateStr 时间字符串 yyyy-mm-dd
 */
function parseDate(dateStr) {
  var isoExp = /^\s*(\d{4})-(\d\d)-(\d\d)\s*$/; //正则
  var date = new Date(NaN);
  var parts = isoExp.exec(dateStr); //正则验证
  if (parts) {
    var month = Number(parts[2]);
    //设置时间
    date.setFullYear(parts[1], month - 1, parts[3]);
    //判断是否正确
    if (month != date.getMonth() + 1) {
      date.setTime(NaN);
    }
  }
  return date;
}

/**
 * 申报成功的依申请明细信息保存数据,
 */
function setYsqxxSb() {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  formData.kz_.ysqxx = {};
  var ysqxx = {};
  var sbmxlist = [];
  for (var i = 0; i < skxxGridlb.length; i++) {
    var item = {};
    var cfzsxmBz = 'N';
    if (sbmxlist.length > 0) {
      for (var j = 0; j < sbmxlist.length; j++) {
        if (skxxGridlb[i].zsxmDm == sbmxlist[j].zsxmDm) {
          sbmxlist[j].jsyj = sbmxlist[j].jsyj + skxxGridlb[i].jfyj;
          sbmxlist[j].ynse = sbmxlist[j].ynse + skxxGridlb[i].bqynsfe;
          sbmxlist[j].ybtse = sbmxlist[j].ybtse + skxxGridlb[i].bqybtsfe;
          cfzsxmBz = 'Y';
          break;
        }
      }
    }
    if (cfzsxmBz == 'N') {
      item.zsxmDm = skxxGridlb[i].zsxmDm;
      item.mxSkssqq = skxxGridlb[i].skssqq;
      item.mxSkssqz = skxxGridlb[i].skssqz;
      item.jsyj = skxxGridlb[i].jfyj;
      item.ynse = skxxGridlb[i].bqynsfe;
      item.ybtse = skxxGridlb[i].bqybtsfe;
      sbmxlist[sbmxlist.length] = item;
    }
  }
  ysqxx.yzpzzlDm = 'BDA0611054';
  ysqxx.yzpzmc = '非税收入通用申报';
  ysqxx.sbmxlist = sbmxlist;
  formData.kz_.ysqxx = ysqxx;
}

/**
 * 选择项目品目子目信息塞入期初数信息
 */
function selectSbxx(nowIndex, sfsfzrd, zsxmDm) {
  if (sfsfzrd != 'Y') {
    var zspmDm = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb[nowIndex].zspmDm;
    var zszmDm = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb[nowIndex].zszmDm;
    var skxxGridlb = formData.hq_.sbxxVoList.skxxGridlb;
    var htSkxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
    var zsxmIndex = -1;
    var zspmIndex = -1;
    var zszmIndex = -1;
    for (var i = 0; i < skxxGridlb.length; i++) {
      if (skxxGridlb[i].sfsfzrd == 'N' && zsxmDm != undefined && zsxmDm != '' && skxxGridlb[i].zsxmDm == zsxmDm) {
        zsxmIndex = i;
        if (zspmDm != undefined && zspmDm != '' && skxxGridlb[i].zspmDm == zspmDm) {
          zspmIndex = i;
          if (zszmDm != undefined && zszmDm != '' && skxxGridlb[i].zszmDm == zszmDm) {
            zszmIndex = i;
          }
        }
      }
    }
    var skxxIndex = -1;
    if (zszmIndex != -1) {
      skxxIndex = zszmIndex;
    } else if (zspmIndex != -1) {
      skxxIndex = zspmIndex;
    } else if (zsxmIndex != -1) {
      skxxIndex = zsxmIndex;
    }
    if (skxxIndex != -1) {
      htSkxxGridlb[nowIndex].zspmGrid = skxxGridlb[skxxIndex].zspmGrid;
      htSkxxGridlb[nowIndex].zszmGrid = skxxGridlb[skxxIndex].zszmGrid;
      if (
        (zspmDm == undefined || zspmDm == '') &&
        skxxGridlb[skxxIndex].zspmDm != undefined &&
        skxxGridlb[skxxIndex].zspmDm != '' &&
        skxxGridlb[skxxIndex].zspmDm.length == 9
      ) {
        const { zspmOption } = store.state['sb/fssrtysb/form'];
        if (zspmOption) {
          for (let i = 0; i < zspmOption.length; i++) {
            const item = zspmOption[i];
            if (skxxGridlb[skxxIndex].zspmDm === item.zspmDm) {
              htSkxxGridlb[nowIndex].zspmDm = skxxGridlb[skxxIndex].zspmDm;
              break;
            }
          }
        }
      }
      if (
        (zszmDm == undefined || zszmDm == '') &&
        skxxGridlb[skxxIndex].zszmDm != undefined &&
        skxxGridlb[skxxIndex].zszmDm != '' &&
        skxxGridlb[skxxIndex].zszmDm.length > 9
      ) {
        const { zszmCT } = store.state['sb/fssrtysb/form'];
        if (zszmCT && zszmCT[skxxGridlb[skxxIndex].zszmDm]) {
          htSkxxGridlb[nowIndex].zszmDm = skxxGridlb[skxxIndex].zszmDm;
        }
      }

      //			 htSkxxGridlb[nowIndex].skssqq = skxxGridlb[skxxIndex].skssqq;
      //			 htSkxxGridlb[nowIndex].skssqz = skxxGridlb[skxxIndex].skssqz;

      var lxZsxmNsqx = formData.fq_.lxZsxmNsqx;
      var lxNsqx = [];
      for (var i = 0; i < lxZsxmNsqx.length; i++) {
        if (zsxmDm == lxZsxmNsqx[i].ZSXM_DM) {
          lxNsqx.push(lxZsxmNsqx[i].NSQX_DM);
        }
      }

      if (lxNsqx.indexOf(skxxGridlb[skxxIndex].nsqxDm) > -1) {
        htSkxxGridlb[nowIndex].nsqxDm = skxxGridlb[skxxIndex].nsqxDm;

        // var skssqq = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqq;
        // var skssqz = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqz;
        // htSkxxGridlb[nowIndex].skssqq = skssqq;
        // htSkxxGridlb[nowIndex].skssqz = skssqz;

        // 更新对应特定项目采集表的nsqxDm、skssqq、skssqz
        if (!isNull(zsxmDm) && formData.fq_.tdxmxxcj.indexOf(zsxmDm) !== -1) {
          var sbfssrtdxmGridlb =
            formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
          if (sbfssrtdxmGridlb && sbfssrtdxmGridlb.length > 0) {
            for (var j = 0; j < sbfssrtdxmGridlb.length; j++) {
              var sbfssrtdxm = sbfssrtdxmGridlb[j];
              var pcuuidCj = sbfssrtdxm.pcuuid;
              if (pcuuidCj === htSkxxGridlb[nowIndex].pcuuid) {
                sbfssrtdxm.nsqxDm = htSkxxGridlb[nowIndex].nsqxDm;
                // sbfssrtdxm.skssqq = skssqq;
                // sbfssrtdxm.skssqz = skssqz;
              }
            }
          }
        }
      }

      htSkxxGridlb[nowIndex].sbqxDm = skxxGridlb[skxxIndex].sbqxDm;
      htSkxxGridlb[nowIndex].jkqxDm = skxxGridlb[skxxIndex].jkqxDm;
      htSkxxGridlb[nowIndex].sfsfzrd = skxxGridlb[skxxIndex].sfsfzrd;
      htSkxxGridlb[nowIndex].zsbl = skxxGridlb[skxxIndex].zsbl;
    }
    var parentNode = 'ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb[' + nowIndex + ']';
    var rowxx = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb[nowIndex];
    formulaEngine.apply(parentNode + '.zspmDm', rowxx.zspmDm, [nowIndex]);
    formulaEngine.apply(parentNode + '.zszmDm', rowxx.zszmDm, [nowIndex]);
    formulaEngine.apply(parentNode + '.nsqxDm', rowxx.zszmDm, [nowIndex]);
  }
}

/**
 * 获取税率
 */
function getSfl(sfsfzrd, zspmDm, zszmDm, qcsfl, skssqz, userType, bz, index) {
  const { zspmOption, zszmOption, kczrzxsrZspmCT2022, kczrzxsrZspmCT2023 } = store.state['sb/fssrtysb/form'];
  var sfl = 0;
  if (!isNull(qcsfl)) {
    //认定为Y和核定时取期初数
    sfl = qcsfl;
  }
  if (sfsfzrd != 'Y') {
    if ('USER_TYPE_ZRR' == userType && zspmDm == '304339000') {
      sfl = 1;
    } else if (!isNull(zspmDm) && zspmDm.indexOf(kczrzxsrDm) === 0) {
      const kczrzxsrZspmCT = (Number(skssqz.substring(0, 4)) < 2023 ? kczrzxsrZspmCT2022 : kczrzxsrZspmCT2023) ?? {};
      // 由于选品目时级联 tbFssbData 会对非认定强刷所属期（限制跨属期）
      if (kczrzxsrZspmCT[zspmDm]) {
        sfl = kczrzxsrZspmCT[zspmDm].sl;
      } else {
        // 跨属期清空特定表的已填内容 处理效果同 tbFssbData
        if (bz === 'tdxm') {
          var sbfssrtdxmGridlbNode =
            this.formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index];
          sbfssrtdxmGridlbNode.zspmDm = '';
          sbfssrtdxmGridlbNode.zspmMc = '';
          sbfssrtdxmGridlbNode.zszmDm = '';
          sbfssrtdxmGridlbNode.zszmMc = '';
        } else if (bz === 'zb') {
          var skxxGridlbNode = this.formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb[index];
          skxxGridlbNode.zspmDm = '';
          skxxGridlbNode.zspmMc = '';
          skxxGridlbNode.zszmDm = '';
          skxxGridlbNode.zszmMc = '';
        }
      }
    } else if (!isNull(zszmDm)) {
      if (zszmOption) {
        for (let i = 0; i < zszmOption.length; i++) {
          const item = zszmOption[i];
          if (zszmDm === item.zszmDm) {
            sfl = item.fdsl;
            break;
          }
          if (sfl === '0' || !sfl) sfl = 1;
        }
      }
    } else if (isNull(zszmDm) && !isNull(zspmDm)) {
      if (zspmOption) {
        for (let i = 0; i < zspmOption.length; i++) {
          const item = zspmOption[i];
          if (zspmDm === item.zspmDm) {
            sfl = item.sl;
            break;
          }
        }
        if (sfl === '0' || !sfl) sfl = 1;
      }
    }
  }
  return sfl;
}

/**
 * 检查数据是否为空
 *
 * @method isNull
 * @param param
 *            {Object} 参数对象
 * @returns {Boolean} 检查结果为空或未定义返回true，不为空返回false
 */
function isNull(param) {
  if (param === null || param === 'null' || param === undefined || param === 'undefined' || '' === param) {
    return true;
  }
  return false;
}

/**
 * 校验征收子目
 */
function checkZszmDm(sfsfzrd, zspmDm, zszmDm, zgswskfjDm) {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var userType = formData.fq_.userType;
  if (isNull(swjgDm) && 'USER_TYPE_ZRR' == userType) {
    swjgDm = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.zgswskfjDm;
  }
  if (
    (swjgDm.substring(0, 3) == '144' || swjgDm.substring(0, 3) == '163') &&
    sfsfzrd != 'Y' &&
    !isNull(zspmDm) &&
    isNull(zszmDm)
  ) {
    const { zszmCT } = store.state['sb/fssrtysb/form'];
    if (zszmCT) {
      for (var index in zszmCT) {
        if (zszmCT[index].zspmDm === zspmDm && !isNull(zspmDm)) {
          return false;
        }
      }
    }
  }
  return true;
}


function syncFetch(_url, _async, data) {
  let res;
  const xhr = new XMLHttpRequest();
  xhr.open('POST', _url, _async);
  xhr.setRequestHeader('Content-type', 'application/json');
  xhr.setRequestHeader('AuthorizationMock', sessionStorage.getItem('token'));
  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4) {
      res = JSON.parse(xhr.response);
    }
  };

  xhr.send(JSON.stringify(data));
  return res;
}

/**
 * 初始化街道乡镇码表
 */
function initJdxzCT(xzqhszDm) {
  if (!isNull(xzqhszDm)) {
    let data1 = syncFetch(sbzxCtl + '/fsgjssb/fssrtysb/v1/getJdxz', false, { XzqhszDm: xzqhszDm });
    if (data1 != null) {
      if (data1.Response.Error != null && data1.Response.Error.Message != null) {
        return;
      }

      data1 = data1.Response.Data;
      var body = JSON.parse(data1.Body);
      var jdxzList = body.jdxzList;
      var jdxzObjet = {};
      for (var i = 0; i < jdxzList.length; i++) {
        jdxzObjet[jdxzList[i].jdxzDm] = jdxzList[i];
      }
      formCT.jdxzCT = jdxzObjet;
    }

    let data2 = syncFetch(sbzxCtl + '/fsgjssb/fssrtysb/v1/getSwjgxx', false, {
      Dqdm: formData.fq_.dqdm,
      XzqhszDm: xzqhszDm,
    });
    if (data2 != null) {
      if (data2.Response.Error != null && data2.Response.Error.Message != null) {
        return;
      }

      data2 = data2.Response.Data;
      var body = JSON.parse(data2.Body);
      var swjglb = body.swjglb;
      var dataCT = {};
      for (var i = 0; i < swjglb.length; i++) {
        if (isNull(dataCT[swjglb[i].swjgdm])) {
          dataCT[swjglb[i].swjgdm] = swjglb[i].swjgmc;
        }
      }
      // 给下拉框赋值
      formCT.swjgCT = dataCT;
    }
  } else {
    formData.ht_.hxzgsb10531Request.sbSbbcTjqtxxVO.jdxzDm = '';
    // 清空税务机关数据
    // 给下拉框赋值
    formCT.swjgCT = {};
  }
}

/**
 * 根据识别号查名称
 */
function getMcBySbh(sbh) {
  if (!isNull(sbh)) {
    $vue
      .$fetch({ url: sbzxCtl + '/fsgjssb/fssrtysb/v1/getMcBySbh', method: 'post', data: { jbrsbh: sbh } })
      .then((res) => {
        const body = res.body;
        if (!isNull(body.djnsrxx) && body.djnsrxx.length > 0) {
          formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.slxxForm.swdlrmc = data.djnsrxx[0].nsrmc;
        }
      });
  }
}

function fqbzJy(fqbz, htbh, index, zspmDm, zszmDm, sffqjk) {
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var pmzmKey = zspmDm;
  if (!isNull(fqbz) && !isNull(htbh) && !isNull(zspmDm) && sffqjk == '1' && index > 0) {
    for (var i = 1; i <= index; i++) {
      var pmzmArrKey = sbfssrtdxmGridlb[index - i].zspmDm;
      if (
        sbfssrtdxmGridlb[index].htbh == sbfssrtdxmGridlb[index - i].htbh &&
        pmzmKey == pmzmArrKey &&
        sbfssrtdxmGridlb[index - i].sffqjk == sffqjk
      ) {
        if (fqbz == sbfssrtdxmGridlb[index - i].fqbz) {
          return 'cf';
        } else if (fqbz != sbfssrtdxmGridlb[index - i].fqbz * 1 + 1) {
          return 'kq';
        }
        break;
      }
    }
  }
  return 'true';
}

/**
 * 同步申报表项目数据
 * TODO: 要素页的逻辑是通过主表处理映射回特定表的，所以需要和金三（特定表处理好了再映射回主表）处理成一致：如果不是指定税种或属期未选，都会强刷回纳税人属期
 */
function tbFssbData(zspmDm, zszmDm, index) {
  if (!isNull(zspmDm)) {
    var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
    // diff: 只和已认定的进行比较
    var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
    // .filter(
    //   ({ sfsfzrd }) => sfsfzrd === 'Y',
    // );
    var parentNode = 'ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[' + index + ']';
    var rowxx = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index];
    var pmzmCj = zspmDm + '_' + zszmDm;
    var czbz = false;
    for (var i = 0; i < skxxGridlb.length; i++) {
      var pmzmSk = skxxGridlb[i].zspmDm + '_' + skxxGridlb[i].zszmDm;
      if (pmzmCj == pmzmSk) {
        czbz = true;
        sbfssrtdxmGridlb[index].skssqq = skxxGridlb[i].skssqq;
        sbfssrtdxmGridlb[index].skssqz = skxxGridlb[i].skssqz;
        sbfssrtdxmGridlb[index].sfl = skxxGridlb[i].sfl;
        formulaEngine.apply(parentNode + '.sfl', rowxx.sfl, [index]);
        break;
      }
    }
    //如《非税收入通用申报表》无对应，则默认与表头的【费款所属期起止】一致
    if (czbz == false || isNull(sbfssrtdxmGridlb[index].skssqq)) {
      sbfssrtdxmGridlb[index].skssqq = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqq;
    }
    if (czbz == false || isNull(sbfssrtdxmGridlb[index].skssqz)) {
      sbfssrtdxmGridlb[index].skssqz = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqz;
    }
    formulaEngine.apply(parentNode + '.skssqq', rowxx.skssqq, [index]);
    formulaEngine.apply(parentNode + '.skssqz', rowxx.skssqz, [index]);
  }
}

/**
 * 移除特定项目表的空行数据
 */
function removeEmptyRow() {
  if (!isNullJpath('formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb')) {
    var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
    if (sbfssrtdxmGridlb.length == 1 && isNull(sbfssrtdxmGridlb[0].pcuuid) && isNull(sbfssrtdxmGridlb[0].htbh)) {
      delete formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
    }
  }
}

var zrrSwjgDmDef = '';
/**
 * 获取征收品目子目码表
 */
function getPmZmCT(userType, zrrswjg) {
  // var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  // var zsxmDms = formData.fq_.zsxmDms;
  // var skssqq = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqq;
  // var skssqz = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.nsrxxForm.skssqz;
  // if (!zrrswjg) {
  //     zrrswjg = zrrSwjgDmDef;
  // }
  // if ("USER_TYPE_ZRR" == userType && zrrswjg != "") {
  //     swjgDm = zrrswjg;
  // }
  // if (!isNull(zsxmDms) && !isNull(swjgDm)) {
  //     zsxmDms = zsxmDms.substring(1).replace(/_/g, ",");
  //     var getZspmlbMethod = $.ajax({
  //         headers: {
  //             "AuthorizationMock": parent.token,
  //             "contentType": "application/json"
  //         },
  //         type: "POST",
  //         contentType: "application/json",
  //         url: parent.ctrl + "/fsgjssb/fssrtysb/v1/getZspmlb",
  //         dataType: "json",
  //         async: false,
  //         data: JSON.stringify({
  //             "SssqQ": skssqq,
  //             "SssqZ": skssqz,
  //             "SwjgDm": swjgDm,
  //             "Cxlb": "Y",
  //             "ZsxmDm": zsxmDms
  //         })
  //     });
  //     var getZszmlbMethod = $.ajax({
  //         headers: {
  //             "AuthorizationMock": parent.token,
  //             "contentType": "application/json"
  //         },
  //         type: "POST",
  //         contentType: "application/json",
  //         url: parent.ctrl + "/fsgjssb/fssrtysb/v1/getZszmlb",
  //         dataType: "json",
  //         async: false,
  //         data: JSON.stringify({
  //             "SssqQ": skssqq,
  //             "SssqZ": skssqz,
  //             "SwjgDm": swjgDm,
  //             "Cxlb": "Y",
  //             "ZsxmDm": zsxmDms
  //         })
  //     });
  //     $.when(getZspmlbMethod, getZszmlbMethod).then(function (data1, data2) {
  //         if (data1[0] != null) {
  //             if (data1[0].Response.Error != null && data1[0].Response.Error.Message != null) {
  //                 parent.layer.alert("获取征收品目数据失败，失败原因：" + data1[0].Response.Error.Message, {
  //                     title: "提示",
  //                     icon: 5
  //                 });
  //                 return;
  //             }
  //             data1 = data1[0].Response.Data;
  //             var body = jQuery.parseJSON(data1.Body);
  //             if (body.zspmGridlb) {
  //                 var zspmGridlb = body.zspmGridlb;
  //                 var json = {};
  //                 for (var i in zspmGridlb) {
  //                     json[zspmGridlb[i].zspmDm] = zspmGridlb[i];
  //                 }
  //                 formEngine.cacheCodeTable("zspmCT", json);
  //             }
  //         }
  //         if (data2[0] != null) {
  //             if (data2[0].Response.Error != null && data2[0].Response.Error.Message != null) {
  //                 parent.layer.alert("获取征收子目数据失败，失败原因：" + data2[0].Response.Error.Message, {
  //                     title: "提示",
  //                     icon: 5
  //                 });
  //                 return;
  //             }
  //             data2 = data2[0].Response.Data;
  //             var body = jQuery.parseJSON(data2.Body);
  //             if (body.zszmGridlb) {
  //                 var zszmGridlb = body.zszmGridlb;
  //                 var json = {};
  //                 for (var i in zszmGridlb) {
  //                     json[zszmGridlb[i].zszmDm] = zszmGridlb[i];
  //                 }
  //                 formEngine.cacheCodeTable("zszmCT", json);
  //             }
  //         }
  //     });
  // }
}

/**
 * 校验特定项目合同编号填写
 */
function checkHtbh(htbh, pcuuid, index) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var skxx = {};
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].pcuuid === pcuuid) {
      skxx = skxxGridlb[i];
      break;
    }
  }
  if (!skxx.t_checkedbz) return true;
  if (isNull(htbh)) {
    if (index != 0 || (index == 0 && !isNull(pcuuid))) {
      return false;
    }
  }
  return true;
}

/**
 * 用来校验矿产资源专项收入某些特定字段是否必填
 */
function checkKcReq(zsxmDm, tag, pcuuid, index) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var skxx = {};
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].pcuuid === pcuuid) {
      skxx = skxxGridlb[i];
      break;
    }
  }
  if (!skxx.t_checkedbz) return true;
  if (zsxmDm != formData.fq_.kczrzxsrDm) return true;
  if (isNull(tag)) {
    if (index != 0 || (index == 0 && !isNull(pcuuid))) {
      return false;
    }
  }
  return true;
}

/**
 * 控制应缴费基数
 */
function yjfjsController(zsxmDm, zszmDm, index) {
  var tdxmxxcj = formData.fq_.tdxmxxcj;
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  //厦门核定信息要锁死
  if (skxxGridlb[index].t_sfhdBz == 'Y') {
    return true;
  } else if (!isNull(zsxmDm) && !isNull(tdxmxxcj) && tdxmxxcj.indexOf(zsxmDm) != -1) {
    //特定项目采集的要锁死
    return true;
  } else if (isDlhsqj(zsxmDm, zszmDm, skxxGridlb[index].zszmMc)) {
    //《电力类非税收入年度汇算清缴表》填写的30168/30149/30158/30150/30202汇算清缴项目信息要锁死
    return true;
  }
  return false;
}

/**
 * 校验数字
 */
function checkNum(num, intLen, floLen) {
  var numArr = (num + '').split('.');
  if (numArr[0].length > intLen || (!isNull(numArr[1]) && numArr[1].length > floLen)) {
    return false;
  }
  return true;
}

/**
 * 获取六项特定项目纳税期限
 * sfsfzrd为Y从核心接口获取nsqxDM,为N,则从CS_DJ_MRQXGZ数据库查询
 * @param zsxmDm
 * @returns {*}
 */
function getXmxxNsqxDm(zspmDm, sfsfzrd, index) {
  var zsxmDm = isNull(zspmDm) ? '' : zspmDm.substring(0, 5);
  var skxxGridlb = formData.hq_.sbxxVoList.skxxGridlb;
  if (sfsfzrd === 'Y') {
    var t_rdpzuuid =
      formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].t_rdpzuuid;
    for (var i = 0; i < skxxGridlb.length; i++) {
      if (zsxmDm === skxxGridlb[i].zsxmDm && t_rdpzuuid === skxxGridlb[i].rdpzuuid) {
        return skxxGridlb[i].nsqxDm;
      }
    }
  } else {
    var nsqxDm =
      formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].nsqxDm;
    var nsqxDmArr = listNsqxDm(zsxmDm, zspmDm);
    if (nsqxDmArr.length === 0) {
      return isNull(nsqxDm) ? '11' : nsqxDm;
    }
    if (!isNull(nsqxDm) && nsqxDmArr.indexOf(nsqxDm) > -1) {
      return nsqxDm;
    }
    return nsqxDmArr[0];
  }
}

//特定项目按次所属期校验
function xmxxAcVaild(ssqq, ssqz, zspmDm, pcuuid, index, sfsfzrd) {
  var nsqxDm = getXmxxNsqxDm(zspmDm, sfsfzrd, index);
  if (!isNull(zspmDm) && (index != 0 || (index == 0 && !isNull(pcuuid)))) {
    if ((!isNull(ssqq) || !isNull(ssqz)) && nsqxDm) {
      if (nsqxDm == '11' && ssqq != ssqz) {
        return false;
      }
    } else {
      if (nsqxDm && nsqxDm == '11') {
        return false;
      }
      if (!nsqxDm) {
        return false;
      }
    }
  }
  return true;
}

function xmxxZspmValid(htbh, sffqjk, zspmDm, zsxmDm, pcuuid, index) {
  if (sffqjk === '1') return true;
  var skxx = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var skxxItem = skxx.find(item => item.pcuuid === pcuuid);
  if (!skxxItem.t_checkedbz) return true;
  var xmxx = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  if (index != 0 || (index == 0 && !isNull(pcuuid))) {
    if (xmxx && xmxx.length) {
      for (var i = 0; i < xmxx.length; i++) {
        if (i != index && htbh == xmxx[i].htbh && zsxmDm == xmxx[i].zsxmDm && zspmDm == xmxx[i].zspmDm && sffqjk == xmxx[i].sffqjk) {
          if (zspmDm == xmxx[i].zspmDm) {
            return false;
          }
        }
      }
    }
  }
  return true;
}

function xmxxFqBzVaild(fqbz, sffqjk, htbh, zspmDm, zsxmDm, zszmDm, pcuuid, index) {
  var firstBz = false;
  if (fqbz == '1') {
    firstBz = true;
  }
  var xmxx = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  if (index != 0 || (index == 0 && !isNull(pcuuid))) {
    if (xmxx && xmxx.length) {
      for (var i = 0; i < xmxx.length; i++) {
        if (i != index && htbh == xmxx[i].htbh && zsxmDm == xmxx[i].zsxmDm && zspmDm == xmxx[i].zspmDm && sffqjk == 1) {
          if (xmxx[i].fqbz == '1') {
            firstBz = true;
          }
        }
      }
    }
  }
  if (!firstBz && sffqjk == 1) {
    return false;
  }
  return true;
}
function getTsfsfzrd(htbh, zsxmDm, zspmDm, zszmDm, sffqjk, index) {
  var sfzrdBz = 'N';
  var mutilRdBz = false;
  var count = 0;
  var sl = 0;
  var rdpzuuid = '';
  var zsdmKey = zsxmDm + '_' + zspmDm + '_' + zszmDm;
  var skxxGridlb = formData.hq_.sbxxVoList.skxxGridlb;
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  for (var i = 0; i < sbfssrtdxmGridlb.length; i++) {
    sbfssrtdxmGridlb[i].sffqjk += ''
  }
  //匹配是否存在多条数据与特定表某一行一致
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (zsxmDm == skxxGridlb[i].zsxmDm) {
      var zspmTemp = skxxGridlb[i].zspmDm == '#' ? '' : skxxGridlb[i].zspmDm;
      var zszmTemp = skxxGridlb[i].zszmDm == '#' ? '' : skxxGridlb[i].zszmDm;
      if (zsdmKey == skxxGridlb[i].zsxmDm + '_' + zspmTemp + '_' + zszmTemp) {
        count++;
        sfzrdBz = skxxGridlb[i].sfsfzrd;
        sl = skxxGridlb[i].sfl;
        rdpzuuid = skxxGridlb[i].rdpzuuid;
      }
    }
  }

  //按期申报中skxxGridlb已被过滤，所以一般count==1.其他申报进去才有该情况。
  if (count > 1) {
    for (var k = 0; k < sbfssrtdxmGridlb.length; k++) {
      if (
        k != index &&
        zsdmKey == sbfssrtdxmGridlb[i].zsxmDm + '_' + sbfssrtdxmGridlb[i].zspmDm + '_' + sbfssrtdxmGridlb[i].zszmDm &&
        sffqjk == 1 &&
        htbh == sbfssrtdxmGridlb[i].htbh
      ) {
        mutilRdBz = true;
        formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].sfl =
          sbfssrtdxmGridlb[k].sfl;
        formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].t_rdpzuuid =
          sbfssrtdxmGridlb[k].t_rdpzuuid;
        sfzrdBz = sbfssrtdxmGridlb[i].t_sfsfzrd;
        break;
      }
    }
    if (!mutilRdBz) {
      sfzrdBz = 'N';
    }
  } else if (count == 1) {
    if (sfzrdBz == 'Y') {
      formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].sfl = sl;
      formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb[index].t_rdpzuuid =
        rdpzuuid;
    }
    return sfzrdBz;
  }
  //核心没有返回sfsfzrd，则为N
  return sfzrdBz;
}
function createFqbzCT() {
  var csz = 0;
  var length = 0;
  var fqNum = [
    '第一期',
    '第二期',
    '第三期',
    '第四期',
    '第五期',
    '第六期',
    '第七期',
    '第八期',
    '第九期',
    '第十期',
    '第十一期',
    '第十二期',
    '第十三期',
    '第十四期',
    '第十五期',
    '第十六期',
    '第十七期',
    '第十八期',
    '第十九期',
    '第二十期',
  ];
  var fqnumCsz = formData.fq_.fqnumCsz;
  var fqbzGrid = {};
  if (fqnumCsz && fqnumCsz.length > 0) {
    if (fqnumCsz[0].csz > 0) {
      length = fqnumCsz[0].csz;
    } else {
      length = 8;
    }
  } else {
    length = 8;
  }
  formCT.fqbzCT = {};
  for (var i = length; i > 0; i--) {
    var temp = getMbKey(i);
    if (!fqbzGrid[i]) {
      fqbzGrid[i] = [];
    }
    fqbzGrid[i].dm = i * 1 + '';
    fqbzGrid[i].mc = fqNum[i - 1];
    formCT.fqbzCT[temp] = fqbzGrid[i];
  }
}
function getMbKey(key) {
  var temp = key / 10;
  if (temp < 1) {
    return '00' + key;
  } else if (temp >= 1 && temp <= 10) {
    return '0' + key;
  } else if (temp > 10) {
    return key;
  }
}

/**
 *
 * @param json
 */
function dmbFilter_zspmCT(json) {
  var zspmDmstr = ',';
  var _new_json = {};
  var zspmStr = formData.fq_.hzCsz; //配置的品目需过滤
  var wrdglpmCsz = formData.fq_.wrdglpmCsz; //配置的品目未认定选项需过滤
  var aqysb = 'Y'; //是否按期应申报
  var skxxGridlb = formData.hq_.sbxxVoList.skxxGridlb; //核心返回认定数据
  var skxxGridlbHt = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  for (var i = 0; i < skxxGridlbHt.length; i++) {
    if (skxxGridlbHt[i].sfsfzrd != 'Y' && isNull(skxxGridlbHt[i].rdpzuuid)) {
      aqysb = 'N';
      break;
    }
  }
  for (var k = 0; k < skxxGridlb.length; k++) {
    zspmDmstr += skxxGridlb[k].zspmDm + ',';
  }
  for (var i in json) {
    //按期申报逻辑只显示有认定的
    if (aqysb == 'Y') {
      if (zspmDmstr.indexOf(json[i].zspmDm) != -1) {
        json[i].sfsfzrdBz = 'Y';
      } else {
        json[i].sfsfzrdBz = 'N';
      }
    } else {
      //其他申报全部显示
      json[i].sfsfzrdBz = 'Y';
    }
    if (zspmStr.indexOf(json[i].zspmDm) == -1) {
      if (!isNull(wrdglpmCsz) && wrdglpmCsz.indexOf(json[i].zspmDm) != -1) {
        json[i].xsbz = 'N';
      } else {
        json[i].xsbz = 'Y';
      }
    } else {
      if(zspmStr.indexOf('#'+json[i].zspmDm) > -1){
        json[i].xsbz = 'N';
      }else{
        json[i].xsbz = 'Y';
      }
    }
  }
  _new_json = jQuery.extend(true, _new_json, json);
  return _new_json;
}

/**
 * 生成32位uuid
 */
function uuid2() {
  var s = [];
  var hexDigits = '0123456789abcdef';
  for (var i = 0; i < 32; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23];
  var uuid = s.join('');
  return uuid;
}

//自然人默认带出9519-其他群众团体
function initHyDm(userType) {
  var hyDm = '';
  if (userType == 'USER_TYPE_ZRR') {
    hyDm = '9519';
    formData.ht_.hxzgsb10531Request.sbSbbcTjqtxxVO.hyMc = '其他群众团体';
  } else {
    hyDm = formData.ht_.hxzgsb10531Request.sbSbbcTjqtxxVO.hyDm;
  }
  return hyDm;
}

//校验htbh字符长度
function checkHtbhLen(htbh) {
  if (!isNull(htbh)) {
    var re = /[\u4E00-\u9FA5]/g; //测试中文字符的正则
    var zwsize = isNull(htbh.match(re)) ? 0 : htbh.match(re).length;
    var htbhSize = htbh.length + zwsize * 2;
    if (htbhSize > 300) {
      return false;
    }
  }
  return true;
}

/**
 * 深复制空对象方法
 */
function cloneObj(obj) {
  var newObj = {};
  if (Object.prototype.toString.call(obj) === '[object Array]') {
    newObj = [];
  }
  for (var key in obj) {
    var val = obj[key];
    newObj[key] = typeof val === 'object' ? cloneObj(val) : typeof val === 'number' ? 0 : '';
  }
  return newObj;
}

/**
 * 初始化申报信息
 * 执行顺序在前，写个性化公式触发会在后面所以建立空方法
 * @returns
 */
function initWbcjxx() {}

/**
 * 获取url参数
 */
function getUrlParam(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'); // 构造一个含有目标参数的正则表达式对象
  var r = window.location.href.substr(window.location.href.indexOf('?')).match(reg); // 匹配目标参数
  if (r != null) return unescape(r[2]);
  return null; // 返回参数值
}

//判断jpath路径数据否为空或jpath路径不存在
function isNullJpath(jpath) {
  try {
    var result = eval(jpath);
    if (isNull(result)) {
      return true;
    }
  } catch (e) {
    //执行表达式出错,则说明路径某一级不存在
    return true;
  }
  return false;
}

function checkDllfssr(zsxmDm) {
  var dllZsxmDm = formData.fq_.dzqyZsxmStr;
  if (!isNull(zsxmDm) && !isNull(dllZsxmDm) && dllZsxmDm.indexOf(zsxmDm) !== -1) {
    var tips = '如为代征企业，请选择“电力类非税收入明细信息采集表”按钮，填报《电力类非税收入明细信息采集表》。';
    // parent.layer.confirm(tips, {
    //     title: '提示',
    //     btn: ['确定', '取消'],
    //     btn1: function (index) {
    //         parent.layer.close(index);
    //     },
    //     btn2: function (index) {
    //         parent.layer.close(index);
    //     }
    // });

    $vue.$gtDialog.confirm({
      header: '提示',
      theme: 'info',
      body: tips,
      closeOnOverlayClick: false,
      closeOnEscKeydown: false,
    });
  }
}

/**
 * 校验同一期，同一征收项目，同一合同编号，缴款期限要相同
 */
function checkJkqx(jkqx, htbh, sffqjk, zsxmDm, fqbz, index) {
  if (index > 0 && sffqjk === '1' && !isNull(htbh) && !isNull(fqbz)) {
    var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
    var key = zsxmDm + '_' + htbh + '_' + fqbz;
    for (var i in sbfssrtdxmGridlb) {
      if (i !== index) {
        var key2 = sbfssrtdxmGridlb[i].zsxmDm + '_' + sbfssrtdxmGridlb[i].htbh + '_' + sbfssrtdxmGridlb[i].fqbz;
        if (key === key2 && jkqx !== sbfssrtdxmGridlb[i].jkqx) {
          return false;
        }
      }
    }
  }
  return true;
}

function listNsqxDm(zsxmDm, zspmDm, zszmDm) {
  var nsqxDmArr = [];
  if (!isNull(zszmDm)) {
    const { zszmCT } = store.state['sb/fssrtysb/form'];
    if (zszmCT) {
      var zszmItem = zszmCT[zszmDm];
      var zszmMc = !isNull(zszmItem) ? zszmItem.zszmMc : '';
      if (isDlhsqj(zsxmDm, zszmDm, zszmMc)) {
        nsqxDmArr.push('10');
        return nsqxDmArr;
      }
    }
  }
  var lxZsxmNsqx = formData.fq_.lxZsxmNsqx;
  for (var i in lxZsxmNsqx) {
    if (zspmDm === lxZsxmNsqx[i].ZSPM_DM) {
      nsqxDmArr.push(lxZsxmNsqx[i].NSQX_DM);
    } else if (
      zsxmDm === lxZsxmNsqx[i].ZSXM_DM &&
      (isNull(lxZsxmNsqx[i].ZSPM_DM) || zspmDm.indexOf(lxZsxmNsqx[i].ZSPM_DM.replace(/%/g, '')) === 0)
    ) {
      nsqxDmArr.push(lxZsxmNsqx[i].NSQX_DM);
    } else if (zsxmDm === lxZsxmNsqx[i].ZSXM_DM) {
      nsqxDmArr.push(lxZsxmNsqx[i].NSQX_DM);
    }
  }
  return nsqxDmArr;
}

/**
 * 校验属期
 */
function fssrtysbSsqVaild(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, nsqxDm, sbxxNsqxDm) {
  if (nsqxDm !== sbxxNsqxDm) return true;
  var nsqxDmArr = listNsqxDm(zsxmDm, zspmDm, zszmDm);
  if (nsqxDmArr.indexOf(nsqxDm) === -1) {
    return true;
  }
  if (!isNull(zspmDm) && !isNull(skssqq) && !isNull(skssqz)) {
    var changeNsqxDm = getNsqxDm(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, '');
    if (isNull(changeNsqxDm)) {
      if (nsqxDm === '11') {
        return skssqq === skssqz;
      } else if (nsqxDm === '06') {
        return skssqq.substring(0, 7) === skssqz.substring(0, 7);
      } else if (nsqxDm === '08') {
        return (
          skssqq.substring(0, 4) === skssqz.substring(0, 4) &&
          parseInt(skssqz.substring(5, 7)) - parseInt(skssqq.substring(5, 7)) === 2
        );
      } else if (nsqxDm === '10') {
        return skssqz.substring(0, 4) === skssqq.substring(0, 4);
      }
    }
  }
  return true;
}

/**
 * 校验月、季度、年报费款所属期起是否第一天
 */
function fssrtysbFirstDayVaild(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, nsqxDm, sbxxNsqxDm) {
  if (nsqxDm !== sbxxNsqxDm) return true;
  var nsqxDmArr = listNsqxDm(zsxmDm, zspmDm, zszmDm);
  if (nsqxDmArr.indexOf(nsqxDm) === -1) {
    return true;
  }
  if (!isNull(zspmDm) && !isNull(skssqq) && !isNull(skssqz)) {
    if (skssqq !== skssqz) {
      var changeNsqxDm = getNsqxDm(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, '');
      if (isNull(changeNsqxDm)) {
        if (nsqxDm === '10') {
          return skssqq.substring(5, 10) === '01-01';
        } else if (nsqxDm === '08') {
          var monthDay = skssqq.substring(5, 10);
          return monthDay === '01-01' || monthDay === '04-01' || monthDay === '07-01' || monthDay === '10-01';
        } else if (nsqxDm === '06') {
          return skssqq.substring(8, 10) === '01';
        }
      }
    }
  }
  return true;
}

/**
 * 校验月、季度、年报费款所属期止是否最后一天
 */
function fssrtysbLastDayVaild(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, nsqxDm, sbxxNsqxDm) {
  if (nsqxDm !== sbxxNsqxDm) return true;
  var nsqxDmArr = listNsqxDm(zsxmDm, zspmDm, zszmDm);
  if (nsqxDmArr.indexOf(nsqxDm) === -1) {
    return true;
  }
  if (!isNull(zspmDm) && !isNull(skssqq) && !isNull(skssqz)) {
    if (skssqq !== skssqz) {
      var changeNsqxDm = getNsqxDm(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, '');
      if (isNull(changeNsqxDm)) {
        if (nsqxDm === '10') {
          return skssqz.substring(5, 10) === '12-31';
        } else if (nsqxDm === '08') {
          return getLastDayOfSeason(new Date(skssqz)) === skssqz;
        } else if (nsqxDm === '06') {
          return getLastDayMonth(new Date(skssqz)) === skssqz;
        }
      }
    }
  }
  return true;
}

/**
 * 获取当月最后一天
 * @param data
 * @returns {string}
 */
function getLastDayMonth(data) {
  var lastDay = new Date(data.getFullYear(), data.getMonth() + 1, 0);
  return timeFormat(lastDay);
}

/**
 * 获取当季最后一天
 * @param date
 */
function getLastDayOfSeason(date) {
  var month = date.getMonth() + 1;
  date.setDate(1);
  if (month <= 3) {
    date.setMonth(3);
  } else if (4 <= month && month <= 6) {
    date.setMonth(6);
  } else if (7 <= month && month <= 9) {
    date.setMonth(9);
  } else if (10 <= month && month <= 12) {
    date.setMonth(12);
  }
  date.setDate(0);
  return timeFormat(date);
}

function timeFormat(date) {
  var y = date.getFullYear();
  var m = date.getMonth() + 1;
  var d = date.getDate();
  if (parseInt(m) < 10) {
    m = '0' + m;
  }
  if (parseInt(d) < 10) {
    d = '0' + d;
  }
  return y + '-' + m + '-' + d;
}

/**
 * 根据修改后的属期重新计算纳税期限
 */
function getNsqxDm(skssqq, skssqz, zsxmDm, zspmDm, zszmDm, sfsfzrd, nsqxDm) {
  if (isNull(skssqq) || isNull(skssqz) || nsqxDm === '99') {
    return nsqxDm;
  }
  if (isNull(zsxmDm) && isNull(zspmDm) && isNull(zszmDm) && !isNull(getUrlParam('NsqxDm'))) {
    return getUrlParam('NsqxDm');
  }
  var nsqxDmArr = listNsqxDm(zsxmDm, zspmDm, zszmDm);
  if (skssqq === skssqz) {
    return '11';
  }
  var monthQq = skssqq.substring(5, 7);
  var monthQz = skssqz.substring(5, 7);
  if (skssqq.substring(0, 7) === skssqz.substring(0, 7) && nsqxDmArr.indexOf('06') !== -1) {
    var lastDayOfMonth = getLastDayMonth(new Date(skssqz));
    if (skssqq.substring(8, 10) === '01' && skssqz === lastDayOfMonth) {
      return '06';
    }
  } else if (
    skssqq.substring(0, 4) === skssqz.substring(0, 4) &&
    parseInt(monthQz) - parseInt(monthQq) === 2 &&
    nsqxDmArr.indexOf('08') !== -1
  ) {
    var monthDay = skssqq.substring(5, 10);
    var lastDayOfSeason = getLastDayOfSeason(new Date(skssqz));
    if (
      (monthDay === '01-01' || monthDay === '04-01' || monthDay === '07-01' || monthDay === '10-01') &&
      skssqz === lastDayOfSeason
    ) {
      return '08';
    }
  } else if (
    skssqq.substring(0, 4) === skssqz.substring(0, 4) &&
    parseInt(monthQz) - parseInt(monthQq) === 11 &&
    nsqxDmArr.indexOf('10') !== -1
  ) {
    if (skssqq.substring(5, 10) === '01-01' && skssqz.substring(5, 10) === '12-31') {
      return '10';
    }
  }
  return nsqxDm;
}

/**
 * 是否为电力类非税年度汇算清缴
 * 项目为30168/30149/30158/30150/30202，且对应的征收子目为汇算清缴
 */
function isDlhsqj(zsxmDm, zszmDm, zszmMc) {
  var dllZsxmDms = '30168_30149_30158_30150_30202';
  var dllZszmDms = formData.fq_.dllZszmDms;
  dllZszmDms = dllZszmDms ? dllZszmDms : '';
  if (!isNull(zsxmDm) && dllZsxmDms.indexOf(zsxmDm) != -1 && !isNull(zszmDm) && dllZszmDms.indexOf(zszmDm) != -1) {
    return true;
  }
  return false;
}

/**
 * 根据征收品目和减免性质代码获取减免性质id
 */
function getJmxzIdByPmjm(zspmDm, ssjmxzDm) {
  var option = formData.fq_.jmxxlist.option;
  if (!isNull(option) && !isNull(zspmDm) && !isNull(ssjmxzDm)) {
    for (var i = 0; i < option.length; i++) {
      if (option[i].pc == zspmDm && option[i].dm == ssjmxzDm) {
        return option[i].jmxzId;
      }
    }
  }
  return '';
}

function setBqybtsfe(hsqjhbndybtjjjebnlj, dlluuid) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].dlluuid === dlluuid) {
      skxxGridlb[i].bqybtsfe = hsqjhbndybtjjjebnlj;
      calculate_fssrtysb_hjxx();
      break;
    }
  }
}

// 数字格式化，补全小数位数
function numberFormat(number, decimals, decPoint) {
  var strNumber = (number + '').replace(/[^0-9+-Ee.]/g, '');
  var n = !isFinite(+strNumber) ? 0 : +strNumber;
  var prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
  var dec = typeof decPoint !== 'string' ? '.' : decPoint;
  var s = ''
  s = (prec ? n.toFixed(prec) : Math.round(n) + '').split('.');
  if ((s[1] || '').length < prec) {
    s[1] = s[1] || '';
    s[1] += new Array(prec - s[1].length + 1).join('0');
  }
  return s.join(dec);
}

function generatePdfAssignments() {
  var skxxGrid = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid
  var skxxGridlb = skxxGrid.skxxGridlb;
  skxxGridlb.forEach(item => {
    item.t_pdf_zsxmMc = item.zsxmMc || ''
    item.t_pdf_zspmMc = item.zspmMc || ''
    item.t_pdf_zszmMc = item.zszmMc || ''
    item.t_pdf_ssjmxzMc = item.ssjmxzMc || ''
    // 处理数字
    item.yjfjs = numberFormat(item.yjfjs, 6)
    item.jcx = numberFormat(item.jcx, 4)
    item.jfyj = numberFormat(item.jfyj, 6)
    item.sfl = numberFormat(item.sfl, 6)
    item.sskcs = numberFormat(item.sskcs, 2)
    item.zsbl = numberFormat(item.zsbl, 6)
    item.bqynsfe = numberFormat(item.bqynsfe, 2)
    item.jmsfe = numberFormat(item.jmsfe, 2)
    item.bqyjsfe = numberFormat(item.bqyjsfe, 2)
    item.bqybtsfe = numberFormat(item.bqybtsfe, 2)
  })
  skxxGrid.bqynsfehj = numberFormat(skxxGrid.bqynsfehj, 2)
  skxxGrid.jmsfehj = numberFormat(skxxGrid.jmsfehj, 2)
  skxxGrid.bqyjsfehj = numberFormat(skxxGrid.bqyjsfehj, 2)
  skxxGrid.bqybtsfehj = numberFormat(skxxGrid.bqybtsfehj, 2)
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var crfsOptions = {
    1: '竞争出让',
    2: '协议出让'
  }
  var ksscgmOptions = {
    1: '小型',
    2: '中型',
    3: '大型'
  }
  var FQQS_MAX_NUM = 99
  var cnNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  var unit = '十';
  var fqqsList = []
  for (var i = 1; i <= FQQS_MAX_NUM; i++) {
    if (i < 10) {
      fqqsList.push(`第${cnNums[i]}期`)
    } else if (i < 20) {
      fqqsList.push(`第${unit}${cnNums[i % 10] === '零' ? '' : cnNums[i % 10]}期`)
    } else {
      fqqsList.push(`第${cnNums[Math.floor(i / 10)]}${unit}${cnNums[i % 10] === '零' ? '' : cnNums[i % 10]}期`)
    }
  }
  sbfssrtdxmGridlb.forEach(item => {
    item.t_pdf_zsxmMc = item.zsxmMc || ''
    item.t_pdf_zspmMc = item.zspmMc || ''
    item.t_pdf_zszmMc = item.zszmMc || ''
    item.t_pdf_fkfsMc = item.sffqjk === '1' ? '是' : '否'
    item.t_pdf_fqbz = item.fqbz ? fqqsList[item.fqbz - 1] : ''
    item.t_pdf_crfsMc = crfsOptions[item.crfs] || ''
    item.t_pdf_ksscgmMc = ksscgmOptions[item.ksscgm] || ''
    item.t_pdf_kzMc = item.t_kzMc || ''
    // 处理数字
    item.htzjk = numberFormat(item.htzjk, 2)
    item.jfyj = numberFormat(item.jfyj, 6)
    item.sfl = numberFormat(item.sfl, 6)
    item.bqynsfe = numberFormat(item.bqynsfe, 2)
    item.wyjbz = numberFormat(item.wyjbz, 6)
  })
}

function zspmVertify(xzqhszDm, t_allSelected, t_checkedbz, zspmDm) {
  var checkDqpm = formData.fq_.checkDqpm;
  var resTar;
  if (checkDqpm && checkDqpm.length) {
    for (var i = 0; i < checkDqpm.length; i++) {
      if (xzqhszDm.indexOf(checkDqpm[i].xzqh) == 0 && zspmDm == checkDqpm[i].zspmDm && t_allSelected != undefined && t_checkedbz) {
        resTar = checkDqpm[i];
        break;
      } 
    }
  }
  if (resTar) return false;
  return true;
}

function vertifyKcKz(zsxmDm, zspmDm, t_zspmMc, t_yjzspm, kz, t_kzDm, index) {
  if (zsxmDm !== formData.fq_.kczrzxsrDm) return true;
  if (!zspmDm) return true;
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var tdxmPcuuid = sbfssrtdxmGridlb[index].pcuuid;
  var isCheckFlag = true;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].pcuuid === tdxmPcuuid) {
      isCheckFlag = skxxGridlb[i].t_checkedbz;
      break;
    }
  }
  if (!isCheckFlag) return true;
  var vertifyList = [
    '矿业权出让收益（出让金额）', 
    '矿业权出让收益（收益率-探矿权、采矿权成交价）',
    '采矿权使用费',
    '采矿权使用费（部本级）',
    '探矿权使用费',
    '探矿权使用费（部本级）'
  ];
  if (vertifyList.includes(t_zspmMc) || vertifyList.includes(t_yjzspm)) {
    var resFlag = Boolean(kz) && Boolean(t_kzDm);
    return resFlag;
  }
  return true;
}

function vertifyKcSlsj(zsxmDm, zspmDm, t_zspmMc, t_yjzspm, kqslsj ,index) {
  if (zsxmDm !== formData.fq_.kczrzxsrDm) return true;
  if (!zspmDm) return true;
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var tdxmPcuuid = sbfssrtdxmGridlb[index].pcuuid;
  var isCheckFlag = true;
  for (var i = 0; i < skxxGridlb.length; i++) {
    if (skxxGridlb[i].pcuuid === tdxmPcuuid) {
      isCheckFlag = skxxGridlb[i].t_checkedbz;
      break;
    }
  }
  if (!isCheckFlag) return true;
  var zspmMc = '探矿权使用费（部本级）';
  if (t_zspmMc === zspmMc || t_yjzspm === zspmMc) {
    var resFlag = Boolean(kqslsj);
    return resFlag;
  }
  return true;
}

function isLockGtItems(zsxmDm, index) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var fyxmList = ['30146', '30147', '30148'];
  if (fyxmList.indexOf(zsxmDm) != -1 && !isNull(skxxGridlb[index].crrmc)) return true;
  return false;
}

function isLockGtZspm(zsxmDm, index) {
  var sbfssrtdxmGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.sbfssrtdxmcjb.sbfssrtdxmGrid.sbfssrtdxmGridlb;
  var fyxmList = ['30146', '30147', '30148'];
  if (sbfssrtdxmGridlb[index]) {
    if (fyxmList.indexOf(zsxmDm) != -1 && !isNull(sbfssrtdxmGridlb[index].crrmc)) return true;
  }
  return false;
}

// 水土的减免性质为 0099120601 0099092301 0099092302 0099061001 0099061002 0099129902 减免费额为本期应纳费额 不可修改
// 水利建设收入的减免性质为 0099129901 减免费额为本期应纳费额 不可修改
function speCalcuJmsfe(zsxmDm, id, index, sskcs, jmsfe) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var bqynsfe = skxxGridlb[index].bqynsfe;
  var jmsfe = skxxGridlb[index].jmsfe;
  var isSpe = isSpeZsxmAndJmxz(zsxmDm, id, index, skxxGridlb[index].jfyj);
  if (!isSpe) return jmsfe || 0;
  if (skxxGridlb[index].nsqxDm === '06' && skxxGridlb[index].jfyj > 100000) return 0;
  if (skxxGridlb[index].nsqxDm === '08' && skxxGridlb[index].jfyj > 300000) return 0;
  return bqynsfe;
}

function isSpeZsxmAndJmxz(zsxmDm, id, index, jfyj) {
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var ssjmxzDm = skxxGridlb[index].ssjmxzDm
  var zsxmStr = '30176_30221';
  if (!zsxmDm || zsxmStr.indexOf(zsxmDm) == -1) return false;
  if (!ssjmxzDm) return false;
  if (skxxGridlb[index].nsqxDm === '06' && jfyj > 100000) return false;
  if (skxxGridlb[index].nsqxDm === '08' && jfyj > 300000) return false;
  var jmxzList;
  if (zsxmDm === '30176') {
    jmxzList = ['0099120601', '0099092301', '0099092302', '0099061001', '0099061002', '0099129902'];
  } else {
    jmxzList = ['0099129901'];
  }
  var res = jmxzList.indexOf(ssjmxzDm) != -1;
  return res;
}

function getSlJmxzId(swjgDm, zsxmDm, zspmDm, nsqxDm, jfyj, index) {
  // 福建 税务机关dm 135- 内蒙古税务机关dm 115-
  var option = formData.fq_.jmxxlist.option;
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var jmxzId = skxxGridlb[index].id;
  if (zsxmDm !== '30221') return jmxzId;
  if (swjgDm.indexOf('135') === 0 || swjgDm.indexOf('115') === 0) return jmxzId;
  var canFlag = (nsqxDm === '06' && jfyj > 0 && jfyj <= 100000) || (nsqxDm === '08' && jfyj > 0 && jfyj <= 300000);
  if (canFlag) {
    // 填充 ssjmxzDm ssjmxzMc
    skxxGridlb[index].ssjmxzDm = '0099129901';
    for (var i = 0; i < option.length; i++) {
      if (option[i].dm === '0099129901') {
        skxxGridlb[index].ssjmxzMc = option[i].mc;
        break;
      }
    }
    return zspmDm + '_0099129901_SXA031901053';
  }
  skxxGridlb[index].ssjmxzDm = '';
  skxxGridlb[index].ssjmxzMc = '';
  skxxGridlb[index].jmsfe = 0;
  return '';
}

function vertifyJmxzId(swjgDm, zsxmDm, id, nsqxDm, jfyj, jmsfe, index) {
  if (zsxmDm === '30221') {
    if (!vertifySlJmxzId(swjgDm, zsxmDm, id, nsqxDm, jfyj)) return true;
    if (!vertifyMaxSlJmxzId(swjgDm, zsxmDm, id, nsqxDm, jfyj, index)) return true
  }
  if (jmsfe > 0 && !id) return false;
  return true;
}

function vertifySlJmxzId(swjgDm, zsxmDm, id, nsqxDm, jfyj) {
  if (zsxmDm !== '30221') return true;
  if (swjgDm.indexOf('135') === 0 || swjgDm.indexOf('115') === 0) return true;
  var canFlag = (nsqxDm === '06' && jfyj > 0 && jfyj <= 100000) || (nsqxDm === '08' && jfyj > 0 && jfyj <= 300000);
  if (canFlag) {
    if (!id) return false;
    return true;
  }
  return true;
}

function vertifyMaxSlJmxzId(swjgDm, zsxmDm, id, nsqxDm, jfyj, index) {
  if (zsxmDm !== '30221') return true;
  if (swjgDm.indexOf('135') === 0 || swjgDm.indexOf('115') === 0) return true;
  var skxxGridlb = formData.ht_.hxzgsb10531Request.fssrtysbywbw.fssrtysb.skxxGrid.skxxGridlb;
  var canFlag = (nsqxDm === '06' && jfyj > 100000) || (nsqxDm === '08' && jfyj > 300000);
  if (canFlag) {
    if (!id || skxxGridlb[index].ssjmxzDm != '0099129901') return true;
    return false;
  }
  return true;
}

// 校验自行申报主管税务机关跟街道乡镇对照是否一致
function vertifyJdxzDm(sbSbbcTjqtxxVO) {
  const { t_swjgList, zgswskfjDm, jdxzDm, xzqhszDm } = sbSbbcTjqtxxVO;
  if (!t_swjgList || !xzqhszDm || !jdxzDm || !zgswskfjDm) return true;
  const compareStr = zgswskfjDm.slice(0, 5);
  const res = t_swjgList.find((item) => {
    const itemStr = item.zgswjgDm.slice(0, 5);
    return itemStr === compareStr;
  });
  if (res) return true;
  return false;
}

// 比较两个时间的大小
function checkDateSize(str1, str2) {
  if (!str1 || !str2) return false;
  // 截取时间 前10位
  if (str1.length > 10) str1 = str1.slice(0, 10);
  if (str2.length > 10) str2 = str2.slice(0, 10);
  return DATE_CHECK_TIME_SIZE(str1, str2);
}

function vertifySsq(skssqq, skssqz) {
  if (!skssqq || !skssqz) return true;
  const skssqqYear = skssqq.substring(0, 4);
  const skssqzYear = skssqz.substring(0, 4);
  return skssqqYear === skssqzYear;
}

export default {
  hyDm,
  scenceCs,
  calculate_fssrtysb_hjxx,
  selectedAllFssr,
  delUnSelectedSkxxGridlb,
  jySelectXm,
  initWbcjxx,
  doAfterVerify,
  dzqyToDl,
  backBegin,
  DATE_CHECK_TIME_SIZE,
  parseDate,
  setYsqxxSb,
  selectSbxx,
  getSfl,
  isNull,
  checkZszmDm,
  initJdxzCT,
  getMcBySbh,
  fqbzJy,
  tbFssbData,
  removeEmptyRow,
  getPmZmCT,
  checkHtbh,
  checkKcReq,
  yjfjsController,
  checkNum,
  getXmxxNsqxDm,
  xmxxAcVaild,
  xmxxZspmValid,
  xmxxFqBzVaild,
  getTsfsfzrd,
  createFqbzCT,
  getMbKey,
  dmbFilter_zspmCT,
  uuid2,
  initHyDm,
  checkHtbhLen,
  cloneObj,
  getUrlParam,
  isNullJpath,
  checkDllfssr,
  checkJkqx,
  fssrtysbSsqVaild,
  fssrtysbFirstDayVaild,
  fssrtysbLastDayVaild,
  getNsqxDm,
  isDlhsqj,
  getJmxzIdByPmjm,
  setBqybtsfe,
  initKczrzxsrDm,
  generatePdfAssignments,
  zspmVertify,
  vertifyKcKz,
  vertifyKcSlsj,
  isLockGtItems,
  isLockGtZspm,
  speCalcuJmsfe,
  isSpeZsxmAndJmxz,
  getSlJmxzId,
  vertifyJmxzId,
  vertifySlJmxzId,
  vertifyMaxSlJmxzId,
  vertifyJdxzDm,
  checkDateSize,
  vertifySsq
};
