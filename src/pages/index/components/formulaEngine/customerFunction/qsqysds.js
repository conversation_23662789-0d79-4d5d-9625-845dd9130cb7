/**
 * 办税人：已实名制的，默认获取当前登录人员的姓名
 * @param smzbz
 * @param smzxm
 * @returns
 */
function setBsrxm(smzbz, smzxm) {
  var bsrxm = formData.ht_.jmqyqsqysdssbSbbdxxVO.qyqssdssbb.qyqssdsBlxxForm.bsrxm;
  if (smzbz === 'Y') {
    return smzxm;
  }
  return bsrxm;
}

/**
 * 办理人员身份证件类型：已实名制的，默认获取当前登录人员的证件类型
 * @param smzbz
 * @param zjlx
 */
function setBlrysfzjlx(smzbz, zjlx) {
  var blrysfzjlx = formData.ht_.jmqyqsqysdssbSbbdxxVO.qyqssdssbb.qyqssdsBlxxForm.blrysfzjlxDm + '';
  if (smzbz === 'Y') {
    return zjlx;
  }
  return blrysfzjlx;
}

/**
 * 理人员身份证件号码：已实名制的，默认获取当前登录人员的证件号码
 * @param smzbz
 * @param zjlx
 */
function setBlrysfzjhm(bz, zjhm) {
  var smzbz = formData.fq_.smzxx.smzbz;
  var smzjhm = formData.fq_.smzxx.zjhm;
  var blrysfzjhm = formData.ht_.jmqyqsqysdssbSbbdxxVO.qyqssdssbb.qyqssdsBlxxForm.blrysfzjhm;
  var zjlx = formData.fq_.smzxx.zjlx;

  if (bz === 'xsbz') {
    if (smzbz === 'Y') {
      if (zjlx === '201') {
        return zjhm.substring(0, 6) + '********' + zjhm.substring(14);
      } else {
        return zjhm;
      }
    } else {
      return blrysfzjhm;
    }
  } else {
    if (smzbz === 'Y') {
      return smzjhm;
    } else {
      return zjhm;
    }
  }
}

function alertWfwz(skssqz) {
  //只需要提醒一次
  if (!isNull(formData.fq_.showWfwzTips)) {
    return;
  }
  formData.fq_.showWfwzTips = 'N';
  var currentTime = DATE_GET_CURRENT_DATE('yyyy-MM-dd');
  var subDays = DATE_GET_TIME_INTERVAL_DAYS(currentTime, skssqz);
  if (currentTime > skssqz && subDays > 15) {
    var _msg = '纳税人在清算结束起15日内未进行企业所得税申报，申报成功以后将自动触发违法违章处理登记！';
    $vue.$gtDialog.confirm({
      header: '提示',
      theme: 'info',
      body: _msg,
    });
  }
}

/**
 * 设置分配的财产金额
 */
function setFpccje(ewbhxh, sycc) {
  if (ewbhxh == null) {
    return;
  }
  var syccfpGridlb = formData.ht_.jmqyqsqysdssbSbbdxxVO.syccjshfpmxb.syccfpGrid.syccfpGridlb[ewbhxh];
  if (syccfpGridlb == null) {
    return;
  }
  var fpccje = syccfpGridlb.fpccje;
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  if (swjgDm && swjgDm.substring(0, 5) == '13502' && sycc <= 0) {
    fpccje = 0;
  }
  syccfpGridlb.fpccje = fpccje;
  return fpccje;
}

/**
 * 判断TZFHHHRZJZL_DM字段(后端返回节点sfzjzlDm)属于101,102,103,199;
 * 并且TZFJJXZ_DM不属于171,172,175,400,410,411,412,413,420,421,422,423,430,431,432,433
 */
function checkTzfDm(gdmc) {
  if (checkGdmcIntzfxx(gdmc)) {
    var tzfxx = formData.fq_.tzgcqk;
    for (var i = 0; i < tzfxx.length; i++) {
      if (gdmc === tzfxx[i].gdmc) {
        var sfzjzlDm = tzfxx[i].sfzjzlDm;
        var tzfjjxzDm = tzfxx[i].tzfjjxzDm;
        if (
          '101_102_103_199'.indexOf(sfzjzlDm) > -1 &&
          '171_172_175_400_410_411_412_413_420_421_422_423_430_431_432_433'.indexOf(tzfjjxzDm) === -1
        ) {
          return true;
        }
      }
    }
  }
  return false;
}

/**
 * 判断当前股东名称是否在投资方信息内
 * @param gdmc
 * @returns {boolean}
 */
function checkGdmcIntzfxx(gdmc) {
  var tzfxx = formData.fq_.tzgcqk;
  if (isNull(tzfxx) || isNull(gdmc)) {
    return false;
  }
  return findArrayItemValueByParam(tzfxx, 'gdmc', gdmc, 'gdmc', null) != null;
}

export default {
  setBsrxm,
  setBlrysfzjlx,
  setBlrysfzjhm,
  alertWfwz,
  setFpccje,
  checkTzfDm,
  checkGdmcIntzfxx,
};
