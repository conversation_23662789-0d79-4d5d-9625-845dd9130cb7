/* eslint-disable */

/**
 * 不格式化源代码，便于同原电局更新的代码比对
 * 修改内容：
 * 1.修改$的dom取值
 * 2.修改$.isArray方法为isArray
 * 3.修改弹框为封装的弹框
 * 4.删除公式执行报错的区域个性化方法体
 *
 *
 */

import store from '@/pages/index/store';
const { dzbdbmList, sbzxCtl } = store.state['sb/qysds_a_yjd/form'];
// 导出pdf 拿到码表 generatePdfAssignments中使用
import pdf_fzjgxsyhqk from '/public/static/sb/qysds_a_yjd/form/dm_fzjgxsyhqk.json';
import pdf_jscglx from '/public/static/sb/qysds_a_yjd/form/dm_sb_jscglx.json';


/**
 * 厦门从一键零申报进到普通申报表，要重写这个方法，不然会提交qysds_a_18yjd_yjlsb/make请求
 * @param qzbz
 */
function submitPdf(qzbz) {
  var href = parent.window.location.href;
  if (href.indexOf('?') > -1) {
    href = href.substr(0, href.indexOf('?'));
  }
  if (href.indexOf(';') > -1) {
    href = href.substr(0, href.indexOf(';'));
  }
  //添加异步提交对应的标志
  var asyncSubmit = $('#myform').find('#asyncSubmit').val();

  href = href.replace('qysds_a_18yjd_yjlsb', 'qysds_a_21yjd');

  var url =
    href +
    '/make?_bizReq_path_=' +
    $('#_bizReq_path_').val() +
    '&_query_string_=' +
    encodeURIComponent($('#_query_string_').val()) +
    '&ysqxxid=' +
    $('#ysqxxid').val() +
    '&_re_contextpath_=' +
    $('#contextPath').val() +
    '&qzbz=' +
    qzbz +
    (!'Y' == asyncSubmit ? '' : '&asyncSubmit=' + asyncSubmit);
  if (formData.fq_.jysbbz === 'Y') {
    url = url + '&jysbbz=Y';
  }
  $('#frmMain', window.parent.document).attr('src', url);
  // 关闭父级蒙层
  parent.layer.closeAll();
  window.parent.hideFrameHead();
}

//获取上一年小薇企业标志
function getSnxwqy() {
  var snd = formData.fq_.sssq.sqQ.split('-')[0] - 1;
  var synsfysbnb = formData.kz_.temp.zb.synsfysbnb; // 上一年是否已申报年报

  // 上年 A年报数据
  var cyrs = formData.fq_.syndNbxx.cyrs;
  var ynssde = formData.fq_.syndNbxx.ynssde;
  var zcze = formData.fq_.syndNbxx.zcze;
  var csgjfxzhjzhy = formData.fq_.syndNbxx.csgjfxzhjzhy;
  var hyDm =
    formData.fq_.syndNbxx.hyDm == '' || formData.fq_.syndNbxx.hyDm == undefined
      ? formData.fq_.nsrjbxx.hyDm
      : formData.fq_.syndNbxx.hyDm;

  // 上一年 第四季度 或12月的小薇
  var syndsjdh12ysfsxw = formData.fq_.syndsjdh12ysfsxw.sfxxwlqy;

  // 上年 B年报数据

  var ynssde_b =
    formData.fq_.synqysdsbNdxx.acbfy_ynssde != 0
      ? formData.fq_.synqysdsbNdxx.acbfy_ynssde
      : formData.fq_.synqysdsbNdxx.asrze_ynssbe;
  var gjxzhjzhy = formData.fq_.synqysdsbNdxx.gjxzhjzhy;
  var qycyrs_qnpjrs = formData.fq_.synqysdsbNdxx.qycyrs_qnpjrs;
  var zcze_qnpjrs = formData.fq_.synqysdsbNdxx.zcze_qnpjrs;

  // 上年 B表 第四季度 或12月的小薇

  var synqysdsbYjdxx = formData.fq_.synqysdsbYjdxx.sfxxwlqy;

  if (synsfysbnb == 'Y') {
    // 存在年报A数据的时候根据年报数据判断小薇

    var xw = sfxwqy_ND(csgjfxzhjzhy, hyDm, cyrs, zcze, ynssde, snd);

    var xwbz = xw ? 'Y' : 'N';
    return xwbz;
  } else if (syndsjdh12ysfsxw != '' && syndsjdh12ysfsxw != undefined) {
    // 上一年
    // 第四季度
    // 或12月的小薇

    return syndsjdh12ysfsxw;
  } else if (qycyrs_qnpjrs != 0 && qycyrs_qnpjrs != undefined) {
    // 以上都不存在的时候
    // 说明他上一年是核定
    // 今年转查账，
    // 根据上年核定申报的数据判断

    var xw_b = sfxwqy_ND(gjxzhjzhy, hyDm, qycyrs_qnpjrs, zcze_qnpjrs, ynssde_b, snd);
    var xwb = xw_b ? 'Y' : 'N';
    return xwb;
  } else {
    return synqysdsbYjdxx != undefined ? synqysdsbYjdxx : '';
  }
}

/**
 * 判断上一年是否是小威
 *
 * @param csgjfxzhjzhy
 * @param sshyDm
 * @param cyrs
 * @param zcze
 * @param ynssde
 * @param nd
 * @returns {Boolean}
 */

function sfxwqy_ND(csgjfxzhjzhy, sshyDm, cyrs, zcze, ynssde, nd) {
  var isXwqy = false;

  // 年度小薇起征点
  var qzd;

  if (nd < 2018) {
    qzd = 500000;
  } else {
    qzd = formData.kz_.temp.zb.xwqzdje;
  }

  if (csgjfxzhjzhy == null || csgjfxzhjzhy == '' || 'N' != csgjfxzhjzhy) {
    // 不满足第105项为“否”的企业的条件，返回0，
    return isXwqy;
  }

  if (ynssde > qzd) {
    // 不满足A100000《中华人民共和国企业所得税年度纳税申报表（A类）》第23行应纳税所得额<=50万元且>0的条件，返回0
    return isXwqy;
  }
  if (sshyDm == null || sshyDm == '') {
    return isXwqy;
  }
  if (cyrs == null || cyrs == '' || cyrs <= 0) {
    return isXwqy;
  }
  if (zcze == null || zcze == '' || zcze < 0) {
    return isXwqy;
  }

  if (!isNaN(sshyDm)) {
    // 行业代码还有A、B、C等字母，但是属于父类，应该不可选，但是还是判断是否为数字的
    var hyDmInt = sshyDm.substring(0, 2);
    if (hyDmInt >= 6 && hyDmInt <= 46) {
      // 因为最大的数值为4690，所以可以直接判断<=46
      // 第102项“行业明细代码”属于“工业企业”的
      if (cyrs <= 100 && zcze <= 3000) {
        isXwqy = true;
      }
    } else {
      // 第102项“行业明细代码”不属于“工业企业”的
      if (cyrs <= 80 && zcze <= 1000) {
        isXwqy = true;
      }
    }
  } else {
    // 第102项“行业明细代码”不属于“工业企业”的
    if (cyrs <= 80 && zcze <= 1000) {
      isXwqy = true;
    }
  }
  return isXwqy;
}

/**
 * 当A200000第11行-本表第1+2+…+28行<=0时，本行数据清空
 *
 * @param value
 */
function cal_qysds_a_18yjd_A201030_29Row(value, jmzlxDm, jzfd) {
  if (value <= 0) {
    formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.jzmzlx = '';
    formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.jzfd = 0;
  } else {
    if (!isNull(jmzlxDm) && jmzlxDm == '2') {
      return ROUND(ROUND(value * 0.4 * 100, 2) / 100, 2);
    } else if (!isNull(jmzlxDm) && jmzlxDm == '1') {
      return ROUND(ROUND(ROUND(ROUND(value * 0.4 * 100, 2) / 100, 2) * jzfd * 100, 2) / 100, 2);
    }
  }
  return 0;
}

function cal_qysds_a_18yjd_A201030_1Row(xxwlqy, sjlreLj) {
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var fhtjdxxwlqyjmqysdsLj = formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.fhtjdxxwlqyjmqysdsLj;
  var je = formData.kz_.temp.zb.xwqzdje;
  var sfqdxw300w = formData.kz_.temp.zb.sfqdxw300w; // 是否启动小微标准扩围为300万
  var yhsl = 0.15; // 小薇优惠的折算率
  var sskcs = 0;
  if (sfqdxw300w == 'Y') {
    yhsl = sjlreLj <= 1000000 ? 0.2 : 0.15;
    sskcs = sjlreLj <= 1000000 ? 0 : 50000;
  }

  if (xxwlqy == 'Y' && (sjlreLj > 0.03 || (sfqdxw300w == 'Y' && sjlreLj > 0)) && sjlreLj <= je) {
    return ROUND(ROUND((sjlreLj * yhsl + sskcs) * 100, 2) / 100, 2);
  }
  return 0;
}

function vaild_qysds_a_18yjd_A201030_1Row(fhtjdxxwlqyjmqysdsLj, xxwlqy, sjlreLj, bz) {
  var je = formData.kz_.temp.zb.xwqzdje;
  var sfqdxw300w = formData.kz_.temp.zb.sfqdxw300w; // 是否启动小微标准扩围为300万
  var yhsl = 0.15; // 小薇优惠的折算率
  var sskcs = 0;
  var jyz = 0.03;
  if (sfqdxw300w == 'Y') {
    yhsl = sjlreLj <= 1000000 ? 0.2 : 0.15;
    sskcs = sjlreLj <= 1000000 ? 0 : 50000;
    jyz = 0.02;
  }
  if (xxwlqy == 'Y' && sjlreLj > jyz && sjlreLj <= je) {
    if (bz == 'vaild1') {
      return (
        fhtjdxxwlqyjmqysdsLj == ROUND(ROUND((sjlreLj * yhsl + sskcs) * 100, 2) / 100, 2) || fhtjdxxwlqyjmqysdsLj == 0
      );
    } else if (bz == 'vaild2') {
      var t_hj1_28 = formData.kz_.temp.fb3.t_hj1_28;
      return fhtjdxxwlqyjmqysdsLj != 0 || t_hj1_28 != 0;
    }
  }
  if (bz == 'vaild1') {
    return true;
  } else if (bz == 'vaild2') {
    return true;
  }
}

function vaild_qysds_a_18yjd_A201030_2_28Row() {
  var sum = 0;
  for (var i = 0; i < arguments.length; i++) {
    if (arguments[i] != 0) {
      sum++;
    }
  }
  if (sum >= 2) {
    return false;
  } else {
    return true;
  }
}

// 初始化本期小薇判断
function getXwinit() {
  var sbqylx = formData.hq_.qtxx.sbqylx;
  var tsnsrlxDm = formData.hq_.qtxx.tsnsrlxDm;
  var kdqsszyDm = formData.fq_.kdqsszyDm;
  var zfjglxDm = formData.fq_.zfjglxDm;
  var sjlreLj = formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;
  var synsfsxwqy = formData.kz_.temp.zb.synsfsxwqy; // 上一年是否是小薇
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var gsdq = swjgDm.substring(0, 5);
  var dq = swjgDm.substring(1, 3);
  var qzd = formData.kz_.temp.zb.xwqzdje;
  var sfqdxw300w = formData.kz_.temp.zb.sfqdxw300w; // 是否启动小微标准扩围为300万
  var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var yf_q = parseInt(skssqq.split('-')[1], 10);
  var yf_z = parseInt(skssqz.split('-')[1], 10);

  var zfjglxDm_nsrxx = formData.fq_.zfjglxDm_nsrxx; // 纳税人信息扩展表中的总分机构类型
  var xsd2_39 = formData.kz_.temp.zb.xsd2_39; // 是否是xsd2.39版本

  // 分支机构是小薇默认N ; 应纳大于100万默认N
  if (sfqdxw300w != 'Y') {
    if (
      sjlreLj > qzd ||
      sbqylx == '2' ||
      tsnsrlxDm == '05' ||
      tsnsrlxDm == '06' ||
      tsnsrlxDm == '10' ||
      kdqsszyDm == '0' ||
      (zfjglxDm == '2' && kdqsszyDm == '1')
    ) {
      return 'N';
    }
  }

  // 19年以后： 启动小微标准扩围为300万 后
  if (sfqdxw300w == 'Y') {
    var qccyrs = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs;
    var qmcyrs = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs;
    var qczcze = formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze;
    var qmzcze = formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze;
    var gjxzhjzhy = formData.ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy;
    var yjfs = formData.hq_.qtxx.yjfs;
    var ybtsdseLj = formData.ht_.ywbw.A200000Ywbd.sbxx.ybtsdseLj;
    var sjyyjsdseLj = formData.ht_.ywbw.A200000Ywbd.sbxx.sjyyjsdseLj;
    var cyrsPjsLjs = formData.hq_.qtxx.cyrsPjsLjs;
    var zczePjsLjs = formData.hq_.qtxx.zczePjsLjs;
    var ysbJds = formData.hq_.qtxx.ysbJds;

    cyrsPjsLjs = isNull(cyrsPjsLjs) ? 0 : cyrsPjsLjs;
    zczePjsLjs = isNull(zczePjsLjs) ? 0 : zczePjsLjs;
    ysbJds = isNull(ysbJds) ? 0 : ysbJds;

    var bdpjrs = ROUND((qccyrs + qmcyrs) / 2, 2);
    var bdpjzcze = ROUND((qczcze + qmzcze) / 2, 2);

    var pjrs = ROUND((cyrsPjsLjs + bdpjrs) / (ysbJds + 1), 2);
    var pjzcze = ROUND((zczePjsLjs + bdpjzcze) / (ysbJds + 1), 2);

    //xsd2.46版本后的平均人数和平均资产总额 直接取界面计算的
    var xsd246qybz = formData.kz_.xsd246qybz;
    if (xsd246qybz == 'Y') {
      pjrs = formData.ht_.ywbw.A200000Ywbd.sbxx.qycyrsQnpjrs;
      pjzcze = formData.ht_.ywbw.A200000Ywbd.sbxx.zczeQnpjs;
    }

    // 19版本 当纳税人属于跨地区经营汇总纳税企业的分支机构 的小薇默认空
    if (sbqylx == '2') {
      return '';
    }
    // 19版本 申报期止不是季末时 小薇默认空
    if (yf_z != 3 && yf_z != 6 && yf_z != 9 && yf_z != 12) {
      return '';
    }
    if (
      ((yf_q == 3 && yf_z == 3) ||
      (yf_q == 6 && yf_z == 6) ||
      (yf_q == 9 && yf_z == 9) ||
      (yf_q == 12 && yf_z == 12)) && yjfs == '3'
    ) {
      return 'N';
    }

    var sbnd = parseInt(skssqz.split('-')[0], 10);
    if (sbnd >= 2021) {
      if (
        tsnsrlxDm == '10' ||
        ((zfjglxDm_nsrxx == '2' || zfjglxDm_nsrxx == '3') && tsnsrlxDm != '05' && tsnsrlxDm != '06')
      ) {
        return 'N';
      }
    } else {
      if (
        (zfjglxDm_nsrxx == '2' || zfjglxDm_nsrxx == '3') &&
        xsd2_39 == 'Y' &&
        tsnsrlxDm != '05' &&
        tsnsrlxDm != '06' &&
        tsnsrlxDm != '10'
      ) {
        return 'N';
      }
    }

    if (yjfs == '3' && (sbqylx == '0' || sbqylx == '1')) {
      var se = ROUND(sjyyjsdseLj + ybtsdseLj, 2);
      if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && se <= 250000 && sbnd <= 2022) {
        return 'Y';
      }
      if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && se <= 150000 && sbnd > 2022) {
        return 'Y';
      } else {
        return 'N';
      }
    }

    if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && sjlreLj <= qzd) {
      return 'Y';
    }
    return 'N';
  }

  // 19年以前 ：当 应纳小于100万 且 上一年为N 或上一年没信息（新开企业）时 返回空纳税人手动填写
  if (synsfsxwqy == 'N' || synsfsxwqy == '') {
    // 大连/青海个性化
    if (gsdq == '12102' || dq == '63') {
      return 'Y';
    } else {
      return '';
    }
  }

  return 'Y';
}

// 触发性本期小薇判断

function getBqxwqy(sjlreLj, qccyrs, qmcyrs, qczcze, qmzcze, gjxzhjzhy, qycyrsQnpjrs, zczeQnpjs, lc14_16) {
  var sjlreLj = formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;
  var synsfsxwqy = formData.kz_.temp.zb.synsfsxwqy;
  var sfsyxxwlqy = formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
  var sbqylx = formData.hq_.qtxx.sbqylx;
  var qzd = formData.kz_.temp.zb.xwqzdje;
  var tsnsrlxDm = formData.hq_.qtxx.tsnsrlxDm;
  var kdqsszyDm = formData.fq_.kdqsszyDm;
  var zfjglxDm = formData.fq_.zfjglxDm;
  var sfqdxw300w = formData.kz_.temp.zb.sfqdxw300w; // 是否启动小微标准扩围为300万
  var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var yf_q = parseInt(skssqq.split('-')[1], 10);
  var yf_z = parseInt(skssqz.split('-')[1], 10);

  var zfjglxDm_nsrxx = formData.fq_.zfjglxDm_nsrxx; // 纳税人信息扩展表中的总分机构类型
  var xsd2_39 = formData.kz_.temp.zb.xsd2_39; // 是否是xsd2.39版本

  // 特殊纳税人的只有就版本用到新版本不用判断
  if (sfqdxw300w != 'Y') {
    if (
      sjlreLj > qzd ||
      sbqylx == '2' ||
      tsnsrlxDm == '05' ||
      tsnsrlxDm == '06' ||
      tsnsrlxDm == '10' ||
      kdqsszyDm == '0' ||
      (zfjglxDm == '2' && kdqsszyDm == '1')
    ) {
      return 'N';
    }
  }

  // 19年以后： 启动小微标准扩围为300万 后
  if (sfqdxw300w == 'Y') {
    var yjfs = formData.hq_.qtxx.yjfs;
    var cyrsPjsLjs = formData.hq_.qtxx.cyrsPjsLjs;
    var zczePjsLjs = formData.hq_.qtxx.zczePjsLjs;
    var ysbJds = formData.hq_.qtxx.ysbJds;

    cyrsPjsLjs = isNull(cyrsPjsLjs) ? 0 : cyrsPjsLjs;
    zczePjsLjs = isNull(zczePjsLjs) ? 0 : zczePjsLjs;
    ysbJds = isNull(ysbJds) ? 0 : ysbJds;

    var bdpjrs = ROUND((qccyrs + qmcyrs) / 2, 2);
    var bdpjzcze = ROUND((qczcze + qmzcze) / 2, 2);

    var pjrs = ROUND((cyrsPjsLjs + bdpjrs) / (ysbJds + 1), 2);
    var pjzcze = ROUND((zczePjsLjs + bdpjzcze) / (ysbJds + 1), 2);

    //xsd2.46版本后的平均人数和平均资产总额 直接取界面计算的
    var xsd246qybz = formData.kz_.xsd246qybz;

    if (xsd246qybz == 'Y') {
      pjrs = qycyrsQnpjrs;
      pjzcze = zczeQnpjs;
    }

    // 19版本 当纳税人属于跨地区经营汇总纳税企业的分支机构 的小薇默认空
    if (sbqylx == '2') {
      return '';
    }
    // 19版本 申报期止不是季末时 小薇默认N
    if (yf_z != 3 && yf_z != 6 && yf_z != 9 && yf_z != 12) {
      return '';
    }
    if (
      ((yf_q == 3 && yf_z == 3) ||
      (yf_q == 6 && yf_z == 6) ||
      (yf_q == 9 && yf_z == 9) ||
      (yf_q == 12 && yf_z == 12)) && yjfs == '3'
    ) {
      return 'N';
    }

    var sbnd = parseInt(skssqz.split('-')[0], 10);
    if (sbnd >= 2021) {
      if (
        tsnsrlxDm == '10' ||
        ((zfjglxDm_nsrxx == '2' || zfjglxDm_nsrxx == '3') && tsnsrlxDm != '05' && tsnsrlxDm != '06')
      ) {
        return 'N';
      }
    } else {
      if (
        (zfjglxDm_nsrxx == '2' || zfjglxDm_nsrxx == '3') &&
        xsd2_39 == 'Y' &&
        tsnsrlxDm != '05' &&
        tsnsrlxDm != '06' &&
        tsnsrlxDm != '10'
      ) {
        return 'N';
      }
    }

    if (yjfs == '3' && (sbqylx == '0' || sbqylx == '1')) {
      // 如果暑期起的月份 等于暑期止的月份 说明是按月申报
      if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && lc14_16 <= 250000 && sbnd <= 2022) {
        return 'Y';
      }
      if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && lc14_16 <= 150000 && sbnd > 2022) {
        return 'Y';
      } else {
        return 'N';
      }
    }

    // GDSDZSWJ-11924修改4.0
    // if(yjfs=="1"&&(sbqylx=="0"||sbqylx=="1")){
    // if(pjrs<=300&&pjzcze<=5000&&gjxzhjzhy=="N"&&sjlreLj<=qzd){
    // return "N";
    // }
    // }
    if (pjrs <= 300 && pjzcze <= 5000 && gjxzhjzhy == 'N' && sjlreLj <= qzd) {
      return 'Y';
    }
    return 'N';
  }

  // 19年以前
  if (synsfsxwqy == 'Y' && sbqylx != '2') {
    return 'Y';
  }

  return sfsyxxwlqy;
}

// 获取分配比例
function getfpbl(srze, gzze, zcze, nsrsbh, t_fzjgsrzeHj, t_fzjggzzeHj, t_fzjgzczeHj, ynsdse) {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var kdqsszyqyDm = formData.fq_.kdqsszyDm;
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
  var fdlbmfpblHj = 0; // 记录非独立生产经营部门的分配比例之和
  var sfczdlbm = false; // 判断是否存在独立部门
  var dlbmID = 0; // 用于记录独立部门的下标
  var blhj = 0; // 用于记录除最后一行外的比例合计
  var srzebl = 0;
  var gzzebl = 0;
  var zczebl = 0;
  var fpbl = 0;
  var defaultVal = ROUND(0, 16);
  var zjgftbz = false; // 总机构是否参与分摊标志
  var zgfIndex = 0; // 用于记录总机构下标

  for (var i = 0, len = fzjgxx.length; i < len; i++) {
    // 只有一个分支机构时
    if (fzjgxx.length == 1) {
      fzjgxx[0].fpbl = ROUND(1, 10);
      break;
    }

    srzebl = t_fzjgsrzeHj == 0 ? defaultVal : ROUND((fzjgxx[i].srze / t_fzjgsrzeHj) * 0.35, 16);
    gzzebl = t_fzjggzzeHj == 0 ? defaultVal : ROUND((fzjgxx[i].gzze / t_fzjggzzeHj) * 0.35, 16);
    zczebl = t_fzjgzczeHj == 0 ? defaultVal : ROUND((fzjgxx[i].zcze / t_fzjgzczeHj) * 0.3, 16);
    fpbl = ROUND(srzebl + gzzebl + zczebl, 10);
    fpbl = ROUND(ROUND(fpbl * 100000, 10) / 100000, 10);

    if (fzjgxx[i].fzjglxlb == 'dlbm') {
      sfczdlbm = true;
      dlbmID = i;
    } else {
      fdlbmfpblHj += fpbl;
    }

    if (i != fzjgxx.length - 1) {
      blhj += fpbl;
    }

    if (fzjgxx[i].fzjglxlb == 'zjg') {
      zjgftbz = true;
      zgfIndex = i;
    }

    // 每一行的分配比例和分配税额赋值
    fzjgxx[i].fpbl = fpbl;
  }

  if (sfczdlbm) {
    // 独立生产经营部门分配比例和分配税额要重新计算

    fzjgxx[dlbmID].fpbl = ROUND(
      ROUND((1 - fdlbmfpblHj) * 100000, 10) / 100000 > 0 ? ROUND((1 - fdlbmfpblHj) * 100000, 10) / 100000 : 0,
      10,
    );
    var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if (sbqylx == '1') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl = fzjgxx[dlbmID].fpbl;
      var jpathList = [];
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl');
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq');
      formulaEngine.apply4List(jpathList);
    }

    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpblHj = ROUND(
      ROUND((fdlbmfpblHj + fzjgxx[dlbmID].fpbl) * 100000, 10) / 100000,
      10,
    );
  } else {
    // 不存在独立部门时最后一行重新计算

    fzjgxx[fzjgxx.length - 1].fpbl = ROUND(
      ROUND((1 - blhj) * 100000, 10) / 100000 > 0 ? ROUND((1 - blhj) * 100000, 10) / 100000 : 0,
      10,
    );

    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpblHj = ROUND(
      ROUND((blhj + fzjgxx[fzjgxx.length - 1].fpbl) * 100000, 10) / 100000,
      10,
    );
  }

  // 总机构参与分摊时 总机构分摊所得税额 的重新计算

  if (zjgftbz) {
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgftsdse = ROUND(fzjgxx[zgfIndex].fpbl * ynsdse, 2);
    var _jpath1 = 'ht_.ywbw.A202000Ywbd.zjgxxForm.zjgftsdse';
    formulaEngine.apply(_jpath1, '');
  }
  if (swjgDm.substring(0, 5) == '13502' && kdqsszyqyDm != '2') {
    for (var i = 0; i < fzjgxx.length; i++) {
      fzjgxx[i].fpbl = 0;
    }
  }
  var _jpath2 = 'ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].fpbl';
  formulaEngine.apply(_jpath2, '');
}

/**
 * A202000分配表分配税额的计算： 总机构不参与分配的情况
 *
 */
function getFpse(fzjgftdsdse, fpbl, fzjglxlb, nsrsbh, xsdfjmje) {
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
  var fdlbmfpseHj = 0; // 记录非独立生产经营部门的分配税额之和
  var sfczdlbm = false; // 判断是否存在独立部门
  var dlbmID = 0; // 用于记录独立部门的下标
  var sehj = 0; // 用于记录除最后一行外的税额合计
  var defaultVal = ROUND(0, 16);
  var fzjgfpse = 0;
  var zjgftbz = formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgftbz;
  // 总机构参与分配时 另外一条公式计算
  if (zjgftbz === 'Y') {
    return;
  }
  var xsdfjmjeHj = 0;

  for (var i = 0, len = fzjgxx.length; i < len; i++) {
    var xsdfjmje = fzjgxx[i].xsdfjmje;
    if (typeof xsdfjmje == 'string') {
      xsdfjmje = ROUND(xsdfjmje, 2);
    }
    xsdfjmjeHj += xsdfjmje;

    // 只有一个分支机构时
    if (fzjgxx.length == 1) {
      fzjgxx[0].fzjgfpse = MAX(0, ROUND(fzjgftdsdse - xsdfjmje, 2));
      break;
    }

    fpbl = fzjgxx[i].fpbl;
    fzjgfpse = ROUND(fzjgftdsdse * fpbl - xsdfjmje, 2);
    if (fzjgxx[i].fzjglxlb === 'dlbm') {
      sfczdlbm = true;
      dlbmID = i;
    } else {
      fdlbmfpseHj += fzjgfpse;
    }

    if (i !== fzjgxx.length - 1) {
      sehj += fzjgfpse;
    }

    // 每一行的分配比例和分配税额赋值
    fzjgxx[i].fzjgfpse = fzjgfpse;
  }

  if (sfczdlbm) {
    // 独立生产经营部门分配比例和分配税额要重新计算
    fzjgxx[dlbmID].fzjgfpse = MAX(0, ROUND(fzjgftdsdse - fdlbmfpseHj - xsdfjmjeHj, 2));
    var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if (sbqylx == '1') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq = fzjgxx[dlbmID].fzjgfpse;
      var jpathList = [];
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl');
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq');
      formulaEngine.apply4List(jpathList);
    }

    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(fdlbmfpseHj + fzjgxx[dlbmID].fzjgfpse, 2);
  } else {
    // 不存在独立部门时最后一行重新计算
    fzjgxx[fzjgxx.length - 1].fzjgfpse = MAX(0, ROUND(fzjgftdsdse - sehj - xsdfjmjeHj, 2));
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(sehj + fzjgxx[fzjgxx.length - 1].fzjgfpse, 2);
  }
  var _jpath2 = 'ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].fzjgfpse';
  formulaEngine.apply(_jpath2, '');
}

/**
 * A202000分配表分配税额的计算： 总机构参与分配的情况
 *
 */
function getFpseZjg(fpbl, fzjglxlb, nsrsbh, ynsdse, xsdfjmje) {
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;

  var fdlbmfpseHj = 0; // 记录非独立生产经营部门的分配税额之和
  var sfczdlbm = false; // 判断是否存在独立部门
  var dlbmID = 0; // 用于记录独立部门的下标
  var sehj = 0; // 用于记录除最后一行外的税额合计
  var defaultVal = ROUND(0, 16);
  var fzjgfpse = 0;
  var zjgftbz = formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgftbz;

  // 总机构不参与分配时 另外一条公式计算
  if (zjgftbz != 'Y') {
    return;
  }
  var xsdfjmjeHj = 0;
  for (var i = 0, len = fzjgxx.length; i < len; i++) {
    xsdfjmje = fzjgxx[i].xsdfjmje;
    xsdfjmjeHj += xsdfjmje;

    // 只有一个分支机构时
    if (fzjgxx.length == 1) {
      if (fzjgxx[0].fzjglxlb === 'zjg') {
        fzjgxx[0].fzjgfpse = ROUND(ynsdse - xsdfjmje, 2);
      }
      break;
    }
    fpbl = fzjgxx[i].fpbl;
    fzjgfpse = MAX(0, ROUND(ynsdse * fpbl - xsdfjmje, 2));
    if (fzjgxx[i].fzjglxlb === 'dlbm') {
      sfczdlbm = true;
      dlbmID = i;
    } else {
      fdlbmfpseHj += fzjgfpse;
    }

    if (i !== fzjgxx.length - 1) {
      sehj += fzjgfpse;
    }

    // 每一行的分配比例和分配税额赋值
    fzjgxx[i].fzjgfpse = fzjgfpse;
  }

  if (sfczdlbm) {
    // 独立生产经营部门分配比例和分配税额要重新计算
    fzjgxx[dlbmID].fzjgfpse = MAX(0, ROUND(ynsdse - fdlbmfpseHj - xsdfjmjeHj, 2));
    var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if (sbqylx == '1') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq = fzjgxx[dlbmID].fzjgfpse;
      var jpathList = [];
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl');
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq');
      formulaEngine.apply4List(jpathList);
    }
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(fdlbmfpseHj + fzjgxx[dlbmID].fzjgfpse, 2);
  } else {
    // 不存在独立部门时最后一行重新计算

    fzjgxx[fzjgxx.length - 1].fzjgfpse = MAX(0, ROUND(ynsdse - sehj - xsdfjmjeHj, 2));
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(sehj + fzjgxx[fzjgxx.length - 1].fzjgfpse, 2);
  }
  var _jpath2 = 'ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].fzjgfpse';
  formulaEngine.apply(_jpath2, '');
}

// 更正申报是要不参股表的证件类型名称重新赋值。GDSDZSWJ-8118
function setZjlxMc() {
  var gzbz = formData.kz_.temp.gzsbbz;
  if (gzbz != 'Y' && 'zx' != gzbz) {
    return;
  }
  var dmb = formData.kz_.basedata['sfzjlx'];
  var zjCT = formCT.sfzjlxCT;
  var cgbList = formData.kz_.temp.cgwgqyxxbgbVO;
  var arr = new Array();
  if (!isNull(dmb)) {
    arr = dmb['item'];
  }

  for (var i = 0; i < cgbList.length; i++) {
    var dsxxList = cgbList[i].dsxxGrid.dsxxGridlb;

    for (var j = 0; j < dsxxList.length; j++) {
      dm = dsxxList[j].sfzjlx;
      mc = zjCT[dm];

      if (!isNull(dm) && !isNull(mc)) {
        dmb = {};
        // 去掉重复的TODO
        var item = {};
        item['dm'] = dm;
        item['mc'] = mc;
        arr.push(item);
        dmb['item'] = arr;
      }
    }
  }
}

function isNull(param) {
  if (param === null || param === 'null' || param === undefined || param === 'undefined' || '' === param) {
    return true;
  }
  return false;
}

/**
 * 逾期申报校验
 *
 */

function yqsbVaild() {
  var yqsbbz = parent.parent.yqsbbz;
  var sbqx = formData.hq_.sbxxGrid.sbxxGridlb[0].sbqx;
  var gdslxDm = formData.fq_.nsrjbxx.gdslxDm;

  if (yqsbbz != 'Y') {
    var sbiniturl =
      parent.pathRoot + '/biz/yqsb/yqsbqc/enterYqsbUrl?gdslxDm=' + gdslxDm + '&sbqx=' + sbqx + '&yqsbbz=' + yqsbbz;
    $.ajax({
      url: sbiniturl,
      type: 'GET',
      data: {},
      dataType: 'json',
      contentType: 'application/json',
      success: function (data) {
        var sfkyqsbbz = data.sfkyqsbbz;

        if (sfkyqsbbz == 'N') {
          $(window.parent.document.body).mask('&nbsp;');
          window.parent.cleanMeunBtn();

          var b = parent.layer.confirm(
            data.msg,
            {
              // area: ['250px','150px'],
              title: '提示',
              type: 1,
              btn: ['确定'],
              // btn2:function(index){}
            },
            function (index) {
              parent.layer.close(b);

              var wfurl = data.wfurlList;

              if (wfurl != undefined && wfurl != '' && wfurl != null) {
                var gnurl = wfurl[0].gnurl;
                var url = parent.location.protocol + '//' + parent.location.host + gnurl;
                parent.parent.window.location.href = url;
              } else {
                if (navigator.userAgent.indexOf('MSIE') > 0) {
                  if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
                    window.opener = null;
                    window.close();
                  } else {
                    window.open('', '_top');
                    window.top.close();
                  }
                } else if (navigator.userAgent.indexOf('Firefox') > 0) {
                  window.location.href = 'about:blank ';
                  window.close();
                } else if (navigator.userAgent.indexOf('Chrome') > 0) {
                  top.open(location, '_self').close();
                } else {
                  window.open('', '_top');
                  window.top.close();
                }
              }
            },
          );
        }
      },
      error: function () {
        layer.alert('链接超时或网络异常', {
          icon: 5,
          title: '提示',
        });
      },
    });
  }
}

// 弥补亏损的提示:当暑期在1-5月内且年报没有申报时，8行的弥补亏损不带出初始化值，这时候要给予对应的提示
function mbksVerify() {
  var synsfysbnb = formData.kz_.temp.zb.synsfysbnb; // 上一年是否已申报年报
  var sfqymbksjk = formData.kz_.temp.zb.sfqymbksjk; // 是否启用弥补亏损监控
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var month = parseInt(skssqz.split('-')[1], 10);

  if (synsfysbnb == 'N' && month < 6 && sfqymbksjk == 'Y') {
    if (typeof isJmsb !== 'function' || (typeof isJmsb === 'function' && !isJmsb())) {
      var msg =
        '因您的弥补亏损数据需关联上年度年度申报数据，请完成年度申报后再填写弥补亏损数据，如因未能在第一季度预缴前完成年度申报，建议第二季度预缴再弥补。';
      // $vue.$gtDialog.warning({
      // 	header: '提示',
      // 	body: msg
      // })
    }
  }
}

/**
 * 初始化A202000表
 */
// function zjxm_qcs() {
//     var gdzcjszjkcmxbGridlb = ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
//     if(gdzcjszjkcmxbGridlb==undefined || gdzcjszjkcmxbGridlb==null || gdzcjszjkcmxbGridlb==""){
//         return;
//     }
//     if(gdzcjszjkcmxbGridlb.length==0){
//         return;
//     }else{
//         for(var j = 0;j < gdzcjszjkcmxbGridlb.length;j++){
//             formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb[j].t_qcs='Y';
//         }
//     }
// }

function zyhyts(yhswsx) {
  var fb2 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
  var hyDm = formData.kz_.temp.fb2.hyDm;
  var xsd2_39 = formData.kz_.temp.zb.xsd2_39;
  var tscs = formData.kz_.temp.fb2.t_tscs;
  var sftx = false;
  // 当登记信息中所属行业为国标行业代码为C，1300-4390；I，6300-6599；时可以录入； 不提示
  if ((1300 <= hyDm && hyDm <= 4390) || (6300 <= hyDm && hyDm <= 6599)) {
    return true;
  }
  //判断第二行是否有填写数据
  if (yhswsx != 0 && yhswsx == 'JSZJ0010') {
    sftx = true;
    parent.layer.alert(
      '本项为享受制造业和信息传输、软件和信息技术服务业固定资产加速折旧政策的企业录入，当前企业登记信息显示不属于可以享受该项政策的行业，若行业信息有误，请及时变更登记信息，点击“确定”可继续填报。',
      {
        title: '提示',
        area: ['448px'],
        icon: 6,
      },
      function (index) {
        parent.layer.close(index);
      },
    );
  } else {
    formData.kz_.temp.fb2.t_tscs = '0';
  }

  //非重要行业填写第二行的时候弹出提示  ， hyDm为空的时候是非重要行业

  if (hyDm == '' && sftx && tscs == '0') {
    return false;
  } else {
    return true;
  }
  //     var msg="";
  //     if(xsd2_39==="Y"){
  //         msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;本行为享受制造业和信息传输、软件和信息技术服务业固定资产加速折旧政策的企业录入，当前企业登记信息显示不属于可以享受该项政策的行业，若行业信息有误，请及时变更登记信息，按确定可继续填报"
  //     }else{
  //         msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;第二行为享受6大行业4大领域固定资产加速折旧政策的企业录入，当前企业登记信息显示不属于可以享受该项政策的行业，若行业信息有误，请及时变更登记信息，按确定可继续填报"
  //     }
  //
  //
  //     parent.layer.alert(msg,{title: "提示"});
  //     formData.kz_.temp.fb2.t_tscs='1';
  // }
  //
  //
}

/**
 * 判断对象是否为空
 *
 */
function isNotEmptyObj(obj) {
  if (obj != null && obj != undefined && obj !== '' && obj !== 'null' && obj !== 'undefined') {
    return true;
  }
  return false;
}

function isEmptyObj(obj) {
  if (isNotEmptyObj(obj)) {
    return false;
  }
  return true;
}

function getSsjmxz(yhswsx) {
  if (isEmptyObj(yhswsx)) {
    return '';
  } else if (yhswsx == 'JSZJ0010') {
    return '0004129927';
  } else if (yhswsx == 'JSZJ0020') {
    return '0004129921';
  } else if (yhswsx == 'JSZJ0030') {
    return '0004039905';
  } else if (yhswsx == 'JSZJ0040') {
    return '0004039906';
  } else if (yhswsx == 'JSZJ0050') {
    return '0004039910';
  } else if (yhswsx == 'JSZJ0060') {
    return '0004039911';
  } else if (yhswsx == 'JSZJ1010') {
    return '0004129926';
  } else if (yhswsx == 'JSZJ1020') {
    return '0004120605';
  } else if (yhswsx == 'JSZJ1030') {
    return '0004039903';
  } else if (yhswsx == 'JSZJ1040') {
    return '0004039904';
  } else if (yhswsx == 'JSZJ1050') {
    return '0004039908';
  } else if (yhswsx == 'JSZJ1060') {
    return '0004039909';
  } else if (yhswsx == 'JSZJ1070') {
    return '0004129928';
  } else if (yhswsx == 'JSZJ1080') {
    return '0004129929';
  } else if (yhswsx == 'JSZJ1090') {
    return '0004129930';
  } else if (yhswsx == 'JSZJ1110') {
    return '0004021950';
  } else if (yhswsx == 'JSZJ1100') {
    return '0004129926';
  }

  return '';
}

function getEwbhxh(yhswsx) {
  if (isEmptyObj(yhswsx)) {
    return;
  } else if (yhswsx == 'JSZJ0010') {
    return 1;
  } else if (yhswsx == 'JSZJ0020') {
    return 2;
  } else if (yhswsx == 'JSZJ0030') {
    return 3;
  } else if (yhswsx == 'JSZJ0040') {
    return 4;
  } else if (yhswsx == 'JSZJ1010') {
    return 1;
  } else if (yhswsx == 'JSZJ1020') {
    return 2;
  } else if (yhswsx == 'JSZJ1030') {
    return 3;
  } else if (yhswsx == 'JSZJ1040') {
    return 4;
  }
}

/**
 * 优惠减免性质判断是否存在上期。根据优惠减免性质启用期起判断
 * @param yhswsx
 * @returns {boolean}
 */
function isSqs(yhswsx) {
  if (yhswsx == '') {
    return false;
  }
  var ssqq = formData.fq_.sssq.sqQ;
  if (yhswsx == 'JSZJ0050' || yhswsx == 'JSZJ0060' || yhswsx == 'JSZJ1050' || yhswsx == 'JSZJ1060') {
    return ssqq > '2021-12-01';
  } else if (yhswsx == 'JSZJ1020') {
    return false;
  } else {
    return ssqq > '2021-02-01';
  }
}

/**
 * 避免重复循环上期数列表
 * @type {*[]}
 */
var hqGdzc_gdzcjszjkcMxbGridlb = [];

function getSqzj() {
  //不是当年第一期才取接口数据
  var sfdndiq = formData.fq_.sfdndiq;
  if ('Y' === sfdndiq) {
    return;
  }
  var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
  var tisqqDate = new Date('2021-04-01');
  var zzrqDate = new Date('2022-01-01');
  var skssqqDate = new Date(skssqq);
  var gdzcjszjkcmxbGridlb2 = [];
  var gdzcjszjkcmxbGridlb3 = [];
  if (!isEmptyObj(formData.hq_.sqsbxx) && !isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid)) {
    var gdzcjszjkcmxbGridlb = formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb;
    for (var i in gdzcjszjkcmxbGridlb) {
      if (gdzcjszjkcmxbGridlb[i].ewbhgjz == 'JSZJ' && gdzcjszjkcmxbGridlb[i].ewbhxh != 0) {
        var syxxVo = {};
        syxxVo.bnxsyhdzcyz = gdzcjszjkcmxbGridlb[i].bnxsyhdzcyz;
        syxxVo.ewbhgjz = gdzcjszjkcmxbGridlb[i].ewbhgjz;
        syxxVo.ssjmxzDm = isEmptyObj(gdzcjszjkcmxbGridlb[i].ssjmxzDm) ? '' : gdzcjszjkcmxbGridlb[i].ssjmxzDm;
        syxxVo.yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
        //修正t_yhsxmc缺失
        var t_yhsxmc = '';
        syxxVo.t_yhsxmc = t_yhsxmc;
        syxxVo.bnljzjkcjedns = gdzcjszjkcmxbGridlb[i].bnljzjkcjedns;
        syxxVo.bnljzjkcjedxs = gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs;
        syxxVo.bnljzjkcjedaz = gdzcjszjkcmxbGridlb[i].bnljzjkcjedaz;
        syxxVo.bnljzjkcjedjs = gdzcjszjkcmxbGridlb[i].bnljzjkcjedjs;
        syxxVo.bnljzjkcjedzz = gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz;
        syxxVo.ewbhxh = gdzcjszjkcmxbGridlb[i].ewbhxh;
        syxxVo.t_qcs = 'Y';

        if (formData.fq_.qygxhXmBz == 'Y') {
          syxxVo.t_bnljzjkcjedxs2_tx = 'N';
          var bnljzjkcjedns_js = ROUND(
            gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs - gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz >= 0
              ? gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs - gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz
              : 0,
            2,
          );
          if (bnljzjkcjedns_js != gdzcjszjkcmxbGridlb[i].bnljzjkcjedns) {
            syxxVo.t_bnljzjkcjedxs2_tx = 'Y';
            syxxVo.bnljzjkcjedns = bnljzjkcjedns_js;
          }
        } else {
          syxxVo.t_bnljzjkcjedxs2_tx = 'N';
        }
        //syxxVo.t_yhsxmc="";
        gdzcjszjkcmxbGridlb2.push(syxxVo);
      } else if (gdzcjszjkcmxbGridlb[i].ewbhgjz == 'YCKC' && gdzcjszjkcmxbGridlb[i].ewbhxh != 0) {
        var yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;

        var jybz = false;
        if (skssqqDate >= tisqqDate) {
          //疫情防控重点保障物资生产企业单价500万元以上设备一次性扣除 4月份以后不带上期数据，只带3月份或者第一季度申报的数据
          if (yhswsx != 'JSZJ1020') {
            jybz = true;
          }
        } else {
          jybz = true;
        }
        if (jybz) {
          var syxxVo = {};
          syxxVo.bnxsyhdzcyz = gdzcjszjkcmxbGridlb[i].bnxsyhdzcyz;
          syxxVo.ewbhgjz = gdzcjszjkcmxbGridlb[i].ewbhgjz;
          syxxVo.ssjmxzDm = isEmptyObj(gdzcjszjkcmxbGridlb[i].ssjmxzDm) ? '' : gdzcjszjkcmxbGridlb[i].ssjmxzDm;
          syxxVo.yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
          var t_yhsxmc = '';
          syxxVo.t_yhsxmc = t_yhsxmc;
          syxxVo.bnljzjkcjedns = gdzcjszjkcmxbGridlb[i].bnljzjkcjedns;
          syxxVo.bnljzjkcjedxs = gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs;
          syxxVo.bnljzjkcjedaz = gdzcjszjkcmxbGridlb[i].bnljzjkcjedaz;
          syxxVo.bnljzjkcjedjs = gdzcjszjkcmxbGridlb[i].bnljzjkcjedjs;
          syxxVo.bnljzjkcjedzz = gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz;
          syxxVo.ewbhxh = gdzcjszjkcmxbGridlb[i].ewbhxh;
          syxxVo.t_qcs = 'Y';

          if (formData.fq_.qygxhXmBz == 'Y') {
            syxxVo.t_bnljzjkcjedxs3_tx = 'N';
            var bnljzjkcjedns_js = ROUND(
              gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs - gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz >= 0
                ? gdzcjszjkcmxbGridlb[i].bnljzjkcjedxs - gdzcjszjkcmxbGridlb[i].bnljzjkcjedzz
                : 0,
              2,
            );
            if (bnljzjkcjedns_js != gdzcjszjkcmxbGridlb[i].bnljzjkcjedns) {
              syxxVo.t_bnljzjkcjedxs3_tx = 'Y';
              syxxVo.bnljzjkcjedns = bnljzjkcjedns_js;
            }
          } else {
            syxxVo.t_bnljzjkcjedxs3_tx = 'N';
          }

          gdzcjszjkcmxbGridlb3.push(syxxVo);
        }
      }
    }
  }
  var yqyhswsx = formData.fq_.dyjdyqycxkcObj.yhswsx;

  if (!isEmptyObj(yqyhswsx) && skssqqDate >= tisqqDate && skssqqDate < zzrqDate) {
    var dyjdyqycxkcObj = formData.fq_.dyjdyqycxkcObj;
    var syxxVo = {};
    syxxVo.bnxsyhdzcyz = dyjdyqycxkcObj.bnxsyhdzcyz;
    syxxVo.ewbhgjz = dyjdyqycxkcObj.ewbhgjz;
    syxxVo.ssjmxzDm = isEmptyObj(dyjdyqycxkcObj.ssjmxzDm) ? '' : dyjdyqycxkcObj.ssjmxzDm;
    var yhswsx = dyjdyqycxkcObj.yhswsx;
    yhswsx = yhswsx.replace(/^\s*|\s*$/g, '');
    syxxVo.yhswsx = yhswsx;
    var t_yhsxmc = '';
    syxxVo.t_yhsxmc = t_yhsxmc;
    syxxVo.bnljzjkcjedns = Number(dyjdyqycxkcObj.bnljzjkcjedns);
    syxxVo.bnljzjkcjedxs = Number(dyjdyqycxkcObj.bnljzjkcjedxs);
    syxxVo.bnljzjkcjedaz = Number(dyjdyqycxkcObj.bnljzjkcjedaz);
    syxxVo.bnljzjkcjedjs = Number(dyjdyqycxkcObj.bnljzjkcjedjs);
    syxxVo.bnljzjkcjedzz = Number(dyjdyqycxkcObj.bnljzjkcjedzz);
    syxxVo.ewbhxh = dyjdyqycxkcObj.ewbhxh;
    syxxVo.t_bnljzjkcjedxs3_tx = 'N';
    syxxVo.t_qcs = 'Y';
    gdzcjszjkcmxbGridlb3.push(syxxVo);
  }

  var jpathList = [];

  if (gdzcjszjkcmxbGridlb2.length > 0) {
    jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].t_bnljzjkcjedxs2_tx');
    formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2 = gdzcjszjkcmxbGridlb2;
  }
  if (gdzcjszjkcmxbGridlb3.length > 0) {
    jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3[#].t_bnljzjkcjedxs3_tx');
    formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3 = gdzcjszjkcmxbGridlb3;
  }

  formulaEngine.apply4List(jpathList);
}

function getSqs(yhswsx, lx) {
  if (isEmptyObj(formData.hq_.sqsbxx)) {
    return 0;
  } else if (isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid)) {
    return 0;
  } else if (isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb)) {
    return 0;
  } else if (isEmptyObj(yhswsx)) {
    return 0;
  } else {
    var gdzcjszjkcmxbGridlb = formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb;
    if (gdzcjszjkcmxbGridlb && gdzcjszjkcmxbGridlb.length > 0 && hqGdzc_gdzcjszjkcMxbGridlb.length == 0) {
      for (var i = 0; i < gdzcjszjkcmxbGridlb.length; i++) {
        var t_yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
        hqGdzc_gdzcjszjkcMxbGridlb.push(t_yhswsx);
      }
    }
    if (hqGdzc_gdzcjszjkcMxbGridlb.indexOf(yhswsx) > -1) {
      for (var i = 0; i < gdzcjszjkcmxbGridlb.length; i++) {
        var t_yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
        if (yhswsx == t_yhswsx) {
          if (isEmptyObj(gdzcjszjkcmxbGridlb[i][lx])) {
            return 0;
          } else {
            return gdzcjszjkcmxbGridlb[i][lx];
          }
        }
      }
      //
      return 0;
    } else {
      return 0;
    }
  }
}

function hasHn(yhswsx, hn) {
  if (hn == 'hn1') {
    var gdzcjszjkcmxbGridlb = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
    var qygxhXmBz = formData.fq_.qygxhXmBz;
    if (yhswsx == null || yhswsx == '' || gdzcjszjkcmxbGridlb == null || gdzcjszjkcmxbGridlb.length == 0) {
      return true;
    }
    var count = 0;
    for (var j = 0; j < gdzcjszjkcmxbGridlb.length; j++) {
      if (
        yhswsx == 'JSZJ0010' &&
        (gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ0030' || gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ0040')
      ) {
        count++;
        if (count > 0) {
          return false;
        }
      } else if (yhswsx == 'JSZJ0030' && gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ0010') {
        count++;
        if (count > 0) {
          return false;
        }
      } else if (yhswsx == 'JSZJ0040' && gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ0010') {
        count++;
        if (count > 0) {
          return false;
        }
      }
    }
    return true;
  } else if (hn == 'hn2') {
    var gdzcjszjkcmxbGridlb = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
    if (yhswsx == null || yhswsx == '' || gdzcjszjkcmxbGridlb == null || gdzcjszjkcmxbGridlb.length == 0) {
      return true;
    }
    for (var j = 0; j < gdzcjszjkcmxbGridlb.length; j++) {
      if (
        yhswsx == 'JSZJ1010' &&
        (gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ1030' || gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ1040')
      ) {
        return false;
      } else if (yhswsx == 'JSZJ1030' && gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ1010') {
        return false;
      } else if (yhswsx == 'JSZJ1040' && gdzcjszjkcmxbGridlb[j].yhswsx == 'JSZJ1010') {
        return false;
      }
    }
    return true;
  }
}

function isHnjg(yhswsx) {
  var gdzcjszjkcmxbGridlb = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
  if (yhswsx == null || yhswsx == '' || gdzcjszjkcmxbGridlb == null || gdzcjszjkcmxbGridlb.length == 0) {
    return true;
  }
  var count = 0;
  for (var j = 0; j < gdzcjszjkcmxbGridlb.length; j++) {
    if (gdzcjszjkcmxbGridlb[j].yhswsx == yhswsx) {
      count++;
      if (count == 2) {
        return false;
      }
    }
  }
  return true;
}

function zjxmyhdm_different1(yhswsx) {
  var gdzcjszjkcmxbGridlb = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
  if (yhswsx == null || yhswsx == '' || gdzcjszjkcmxbGridlb == null || gdzcjszjkcmxbGridlb.length == 0) {
    return true;
  }
  var count = 0;
  for (var j = 0; j < gdzcjszjkcmxbGridlb.length; j++) {
    if (gdzcjszjkcmxbGridlb[j].yhswsx == yhswsx) {
      count++;
      if (count == 2) {
        return false;
      }
    }
  }
  return true;
}

function zjxmyhdm_different2(yhswsx) {
  var gdzcjszjkcmxbGridlb = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
  if (yhswsx == null || yhswsx == '' || gdzcjszjkcmxbGridlb == null || gdzcjszjkcmxbGridlb.length == 0) {
    return true;
  }
  var count = 0;
  for (var j = 0; j < gdzcjszjkcmxbGridlb.length; j++) {
    if (gdzcjszjkcmxbGridlb[j].yhswsx == yhswsx) {
      count++;
      if (count == 2) {
        return false;
      }
    }
  }
  return true;
}

/**
 * 初始化A202000表
 */
function setFpbxx() {
  var gzbz = formData.kz_.temp.gzsbbz;
  if (gzbz == 'Y') {
    return;
  }
  var fpb = formData.ht_.ywbw.A202000Ywbd;
  if (fpb == undefined || fpb == null || fpb == '') {
    return;
  }
  var fzjgxxList = formData.hq_.fzjgxxGrid.fzjgxxGridlb;

  // 把核心接口返回的分支机构信息初始化到表单界面对应的节点中
  if (fzjgxxList != null && fzjgxxList != undefined) {
    for (var i = 0; i < fzjgxxList.length; i++) {
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i] = {
        djxh: '',
        fzjgzgswjDm: '',
        zgswjgDm: '',
        zgswjgmc: '',
        fzjglxlb: '',
        nsrsbh: '',
        fzjgmc: '',
        srze: 0,
        gzze: 0,
        zcze: 0,
        fpbl: 0,
        fzjgxsqyxyhqk: '',
        fzjgfpse: 0,
        sfxsdfjm: '',
        xsdfjmje: 0,
        xsdfjmfd: 0,
        t_fzjgnsrsbh_fzjglxlb: '',
      };
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].fzjglxlb = fzjgxxList[i].fzjglxlb;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].nsrsbh = fzjgxxList[i].fzjgnsrsbh;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].fzjgmc = fzjgxxList[i].fzjgmc;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].srze = fzjgxxList[i].fzjgsrze;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].gzze = fzjgxxList[i].fzjggzze;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].zcze = fzjgxxList[i].fzjgzcze;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].djxh = fzjgxxList[i].fzjgdjxh;
      // formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].mzjmje = fzjgxxList[i].mzjmje==undefined?"":fzjgxxList[i].xsdfjmje;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].sfxsdfjm =
        fzjgxxList[i].sfxsdfjm == undefined ? '' : fzjgxxList[i].sfxsdfjm;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].xsdfjmje =
        fzjgxxList[i].xsdfjmje == undefined ? '' : fzjgxxList[i].xsdfjmje;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].xsdfjmfd =
        fzjgxxList[i].xsdfjmfd == undefined ? '' : fzjgxxList[i].xsdfjmfd;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].fzjgzgswjDm =
        fzjgxxList[i].zgswjgDm == undefined ? '' : fzjgxxList[i].zgswjgDm;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].zgswjgDm =
        fzjgxxList[i].zgswjgDm == undefined ? '' : fzjgxxList[i].zgswjgDm;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].zgswjgmc =
        fzjgxxList[i].zgswjgmc == undefined ? '' : fzjgxxList[i].zgswjgmc;
      formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].t_fzjgnsrsbh_fzjglxlb =
        fzjgxxList[i].fzjgnsrsbh + '_' + fzjgxxList[i].fzjglxlb;
    }
  }
}

// 获取分配比例
function getfpblhfpse(
  srze,
  gzze,
  zcze,
  nsrsbh,
  t_fzjgsrzeHj,
  t_fzjggzzeHj,
  t_fzjgzczeHj,
  fzjglxlb,
  fzjgftdsdse,
  ynsdse,
) {
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;

  var fdlbmfpblHj = 0; // 记录非独立生产经营部门的分配比例之和
  var fdlbmfpseHj = 0; // 记录非独立生产经营部门的分配税额之和
  var sfczdlbm = false; // 判断是否存在独立部门
  var dlbmID = 0; // 用于记录独立部门的下标
  var blhj = 0; // 用于记录除最后一行外的比例合计
  var sehj = 0; // 用于记录除最后一行外的税额合计
  var srzebl = 0;
  var gzzebl = 0;
  var zczebl = 0;
  var fpbl = 0;
  var defaultVal = ROUND(0, 16);

  for (var i = 0, len = fzjgxx.length; i < len; i++) {
    // 只有一个分支机构时
    if (fzjgxx.length == 1) {
      fzjgxx[0].fpbl = ROUND(1, 10);
      fzjgxx[0].fzjgfpse = ROUND(fzjgftdsdse, 2);
      break;
    }

    srzebl = t_fzjgsrzeHj == 0 ? defaultVal : ROUND((fzjgxx[i].srze / t_fzjgsrzeHj) * 0.35, 16);
    gzzebl = t_fzjggzzeHj == 0 ? defaultVal : ROUND((fzjgxx[i].gzze / t_fzjggzzeHj) * 0.35, 16);
    zczebl = t_fzjgzczeHj == 0 ? defaultVal : ROUND((fzjgxx[i].zcze / t_fzjgzczeHj) * 0.3, 16);
    fpbl = ROUND(srzebl + gzzebl + zczebl, 10);
    fpbl = ROUND(ROUND(fpbl * 100000, 10) / 100000, 10);
    fzjgfpse = ROUND(fzjgftdsdse * fpbl, 2);

    if (fzjgxx[i].fzjglxlb == 'dlbm') {
      sfczdlbm = true;
      dlbmID = i;
    } else {
      fdlbmfpblHj += fpbl;
      fdlbmfpseHj += fzjgfpse;
    }

    if (i != fzjgxx.length - 1) {
      blhj += fpbl;
      sehj += fzjgfpse;
    }

    // 每一行的分配比例和分配税额赋值
    fzjgxx[i].fpbl = fpbl;
    fzjgxx[i].fzjgfpse = fzjgfpse;
  }

  if (sfczdlbm) {
    // 独立生产经营部门分配比例和分配税额要重新计算

    fzjgxx[dlbmID].fpbl = ROUND(
      ROUND((1 - fdlbmfpblHj) * 100000, 10) / 100000 > 0 ? ROUND((1 - fdlbmfpblHj) * 100000, 10) / 100000 : 0,
      10,
    );

    fzjgxx[dlbmID].fzjgfpse = ROUND(fzjgftdsdse - fdlbmfpseHj, 2);
    var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    if (sbqylx == '1') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl = fzjgxx[dlbmID].fpbl;
      formData.ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq = fzjgxx[dlbmID].fzjgfpse;
      var jpathList = [];
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.dlscjybmftbl');
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.zjgdlscjybmyftsdseBq');
      formulaEngine.apply4List(jpathList);
    }

    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpblHj = ROUND(
      ROUND((fdlbmfpblHj + fzjgxx[dlbmID].fpbl) * 100000, 10) / 100000,
      10,
    );
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(fdlbmfpseHj + fzjgxx[dlbmID].fzjgfpse, 2);
  } else {
    // 不存在独立部门时最后一行重新计算

    fzjgxx[fzjgxx.length - 1].fpbl = ROUND(
      ROUND((1 - blhj) * 100000, 10) / 100000 > 0 ? ROUND((1 - blhj) * 100000, 10) / 100000 : 0,
      10,
    );

    fzjgxx[fzjgxx.length - 1].fzjgfpse = ROUND(fzjgftdsdse - sehj, 2);

    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpblHj = ROUND(
      ROUND((blhj + fzjgxx[fzjgxx.length - 1].fpbl) * 100000, 10) / 100000,
      10,
    );
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_fpseHj = ROUND(sehj + fzjgxx[fzjgxx.length - 1].fzjgfpse, 2);
  }
}

function validFpbl(fpbl) {
  // $..A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].fpbl>=0&&$..A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].fpbl<=1
  var gridlb = fpbl.substring(0, fpbl.lastIndexOf('[')); // 截取jpath中最后一个节点所在的列表路径
  var _lst = eval(gridlb); // 通过eval 将列表路径转成集合数据
  var ret = [];
  for (var i = 0; i < _lst.length; i++) {
    ret[i] = _lst[i].fpbl >= 0 && _lst[i].fpbl <= 1;
  }
  return ret;
}

/**
 * 关闭当前页面
 */

function closeWin() {
  if (navigator.userAgent.indexOf('MSIE') > 0) {
    if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
      window.opener = null;
      window.close();
    } else {
      window.open('', '_top');
      window.top.close();
    }
  } else if (navigator.userAgent.indexOf('Firefox') > 0) {
    window.location.href = 'about:blank ';
    window.close();
  } else if (navigator.userAgent.indexOf('Chrome') > 0) {
    top.open(location, '_self').close();
  } else {
    window.open('', '_top');
    window.top.close();
  }
}
/**
 * 服务于rule_A200000.json文件公式06100103010100001K和06100103010100202
 */
function validation() {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var kdsAndkxqFlag = formData.hq_.qtxx.kdsAndkxqFlag;
  var valid = {
    fpbl: 'N',
    fzjgfpse: 'N',
  };

  var key1 = 'Y';
  var key2 = 'Y';
  if (formData.hq_.fzjgxxGrid != null) {
    if (formData.hq_.fzjgxxGrid.fzjgxxGridlb != null && formData.hq_.fzjgxxGrid.fzjgxxGridlb.length > 0) {
      for (var i = 0; i < formData.hq_.fzjgxxGrid.fzjgxxGridlb.length; i++) {
        if (
          formData.hq_.fzjgxxGrid.fzjgxxGridlb[i].fzjgdjxh == formData.fq_.nsrjbxx.djxh ||
          (formData.hq_.fzjgxxGrid.fzjgxxGridlb[i].fzjgnsrsbh == formData.fq_.nsrjbxx.nsrsbh &&
            formData.hq_.fzjgxxGrid.fzjgxxGridlb[i].fzjgmc == formData.fq_.nsrjbxx.nsrmc)
        ) {
          key1 = isNull(formData.hq_.fzjgxxGrid.fzjgxxGridlb[i].fpbl) ? 'Y' : 'N';
          key2 = isNull(formData.hq_.fzjgxxGrid.fzjgxxGridlb[i].fpse) ? 'Y' : 'N';
        }
      }
    }
    if (key1 == 'Y' && formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx == '2') {
      valid.fpbl = 'Y';
    }
    if (key2 == 'Y' && formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx == '2') {
      valid.fzjgfpse = 'Y';
    }
  }

  return valid;
}

/**
 * 重写下载模板方法 将期初数传入后台，然后把期初数中的识别号和名称写入模板
 */
function modelDownload() {
  var mainUrl = window.location.protocol + '//' + window.location.host + '/' + window.location.pathname.split('/')[1];
  var url = mainUrl + '/nssb/qysds/qysdsA18yjdFile.do';

  // 定义一个form表单
  var form = $('<form>');
  form.attr('style', 'display:none');
  form.attr('target', '');
  // 请求类型
  form.attr('method', 'post');
  // 请求地址
  form.attr('action', url);
  // 将表单放置在web中
  $('body').append(form);

  // 传递期初数
  var hqFzjgxxGridlb = encodeURIComponent(JSON.stringify(formData.hq_.fzjgxxGrid.fzjgxxGridlb));
  var input = $('<input>');
  input.attr('type', 'hidden');
  input.attr('name', 'formData');
  input.attr('value', hqFzjgxxGridlb);
  form.append(input);

  // 表单提交
  form.submit();
}

/**
 * 修改上传页面的提示语
 *
 * @returns {string}
 */
function ywControlUploadTip() {
  var divHtml =
    '<div class="textRM">备注：当前只支持上传xls的附件类型' +
    '<span class="redtext" style="color: red;">支持最大上传值为10M.</span></div>' +
    '<div class="textRM"><span class="redtext" style="color: red;">执行导入后，将会覆盖分支机构所得税分配表所有数据，请保证导入文件的数据正确，如需修改本表数据请修改导入文件再重新导入！</span></div>';
  return divHtml;
}

// 获取总机构是否参与分摊的标志
function getZjgftbz(cs) {
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var bz = 'N';
  var zjgIndex = 0;
  for (var i = 0; i < fzjgxx.length; i++) {
    if (fzjgxx[i].fzjglxlb == 'zjg') {
      zjgIndex = i;
      bz = 'Y';
      break;
    }
  }
  if (cs == 'xb') {
    return zjgIndex;
  } else {
    return bz;
  }
}

function valiZjgyftsdseBq(zjgftbz, ynsdse, zjgIndex, zjgftsdse, ybtsdseLj, zjgftbl, zjgyftsdseBq) {
  var se = ROUND(ybtsdseLj * zjgftbl, 2);
  if (zjgftbz == 'Y') {
    var bl = ROUND(formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[zjgIndex].fpbl, 4);
    var ftse = ROUND(ynsdse * bl, 2);
    return se == ftse;
  } else {
    return se == zjgyftsdseBq;
  }
}

function getzjgbl(zjgftbz, ynsdse, zjgIndex, zjgftsdse, ybtsdseLj, zjgftbl, zjgyftsdseBq) {
  if (zjgftbz === 'Y') {
    var bl = ROUND(formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[zjgIndex].fpbl, 4);
    return bl;
  } else {
    return 0.0;
  }
}

function getZjgftsdse(zjgftbz, ynsdse, fpbl, zjgIndex, zjgftbl, fzjgfpse) {
  if (zjgftbz != 'Y') {
    return;
  }
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var se = fzjgxx[zjgIndex].fzjgfpse;
  formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgftsdse = ROUND(se, 2);
  var jpathList = [];
  jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.zjgczjzftbl');
  jpathList.push('ht_.ywbw.A202000Ywbd.zjgxxForm.zjgftsdse');
  formulaEngine.apply4List(jpathList);
}

// 主表第九行 实际利润额 的自定义公式
function getSjlreLj(yjfs, sbqylx, lrzeLj, tdywjsdynssdeLj, bzssrLj, mssrLj, gdzcjszjkctjeLj, sdjmLj, mbyqndksLj) {
  var sjlreLj = 0;
  if (yjfs == '1' && (sbqylx == '0' || sbqylx == '1')) {
    sjlreLj = lrzeLj + tdywjsdynssdeLj - bzssrLj - mssrLj - gdzcjszjkctjeLj - sdjmLj - mbyqndksLj;
  } else {
    sjlreLj = formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj;
  }
  return sjlreLj;
}

// 主表第15行 本期应补（退）所得税额 的自定义公式
function getYbtsdseLj(tsnsrlxDm, ynsdseLj, jmsdseLj, jdyjbl, sjyyjsdseLj, tdywyjzsdseLj, yjfs, sbqylx) {
  var ybtsdselj = 0;
  var sqHzsdse = formData.hq_.qtxx.sqHzsdse;
  sqHzsdse = sqHzsdse == undefined || sqHzsdse == null || sqHzsdse === '' ? 0 : sqHzsdse;
  if (sbqylx != '0' && sbqylx != '1') {
    return ybtsdselj;
  }
  if (tsnsrlxDm == '06' && (yjfs == '1' || yjfs == '2')) {
    ybtsdselj = MAX(0, accSub(accSub(accMul(accSub(ynsdseLj, jmsdseLj), jdyjbl), sjyyjsdseLj), tdywyjzsdseLj));
  } else if (yjfs == '1' || yjfs == '2') {
    ybtsdselj = MAX(0, ynsdseLj - jmsdseLj - sjyyjsdseLj - tdywyjzsdseLj);
  } else if (yjfs == '3') {
    //自行填写
    return formData.ht_.ywbw.A200000Ywbd.sbxx.ybtsdseLj;
  } else {
    ybtsdselj = 0;
  }
  return ybtsdselj;
}
/**
 * 企业所得税申报提交需要的数据计算逻辑前移到公式中,计税依据
 */
function getJsyj(sbqylx, yjfs, row9, row11, row15, row16, row21) {
  var jsyj = 0.0;

  if (sbqylx == '2') {
    return 0.0;
  } else if (sbqylx == '1') {
    if (row16 > 0) {
      if (yjfs == '1' || yjfs == '2') {
        return row9;
      } else if (yjfs == '3') {
        return 0.0;
      }
    } else {
      if (yjfs == '1' || yjfs == '2') {
        return row9;
      } else if (yjfs == '3') {
        return 0.0;
      }
    }
  } else if (sbqylx == '0') {
    if (yjfs == '1' || yjfs == '2') {
      return row9;
    } else if (yjfs == '3') {
      return 0.0;
    }
  }
  return 0.0;
}
/**
 * 企业所得税申报提交需要的数据计算逻辑前移到公式中,应补退税额
 */
function getYbtse(sbqylx, yjfs, row9, row11, row15, row16, row21) {
  if (sbqylx == '2') {
    return row21;
  } else if (sbqylx == '1') {
    if (row16 > 0) {
      if (yjfs == '1' || yjfs == '2') {
        return row16;
      } else if (yjfs == '3') {
        return row16;
      }
    } else {
      if (yjfs == '1' || yjfs == '2') {
        return row15;
      } else if (yjfs == '3') {
        return row15;
      }
    }
  } else if (sbqylx == '0') {
    if (yjfs == '1' || yjfs == '2') {
      return row15;
    } else if (yjfs == '3') {
      return row15;
    }
  }

  return 0.0;
}
/**
 * 企业所得税申报提交需要的数据计算逻辑前移到公式中,应补退税额
 */

function getYnse(sbqylx, yjfs, row9, row11, row15, row16, row21) {
  if (sbqylx == '2') {
    return 0.0;
  } else if (sbqylx == '1') {
    if (row16 > 0) {
      if (yjfs == '1' || yjfs == '2') {
        return row11;
      } else if (yjfs == '3') {
        return row15;
      }
    } else {
      if (yjfs == '1' || yjfs == '2') {
        return row11;
      } else if (yjfs == '3') {
        return row15;
      }
    }
  } else if (sbqylx == '0') {
    if (yjfs == '1' || yjfs == '2') {
      return row11;
    } else if (yjfs == '3') {
      return row15;
    }
  }
  return 0.0;
}

function getJzfd(jzmzlx) {
  if (jzmzlx != '1') {
    return 0;
  }

  var jzfd = formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.jzfd;
  return jzfd;
}

/*自定义公式 用于判断当前纳税人是否可以申报当前税种
 * 上一年如果正式方式是核定，今年上半年如果征收方式既没有核对 也没有查找时 不可以申报A表只能申报B表
 *
 */
function sbbSfksbValid() {
  var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
  if (DATE_CHECK_TIME_SIZE('2020-01-01', skssqq)) {
    return;
  }
  // 是否核定标志
  var sfhdtsBz = formData.fq_.sfhdtsBz;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  //当上一年是核定征收方式且本年是没有认定核定方式的 前半年只能报B表
  if (yf_z <= 6 && sfhdtsBz == 'Y') {
    var msg =
      '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;根据国家税务总局 关于印发《企业所得税核定征收办法》（试行）的通知（国税发[2008]30号）第十一条，税务机关应在每年6月底前对上年度实行核定征收企业所得税的纳税人进行重新鉴定。重新鉴定工作完成前，纳税人可暂按上年度的核定征收方式预缴企业所得税，请于鉴定工作完成后再申报本表。 ';
    var a = layer.confirm(
      msg,
      {
        area: ['380px', '300px'],
        type: 1,
        title: '提示',
        closeBtn: 0,
        btn: ['确定'],
      },
      function (data) {
        layer.close(a);
        closeWin();
      },
    );
  }
}

// JCDA2019-5518 单户-申报缴款-企业所得税，建议增加重新取数功能。
//调用 getYsData() ，具体逻辑在原子服务内作判断
//财报取数
function getCwbbData() {
  var index = layer.load(2, { shade: 0.3 });
  var cbQsBz = 'Y';
  var zzsQsBz = 'N';
  getCbAndZzsYsData(cbQsBz, zzsQsBz);
  layer.close(index);
}
//增值税取数
function getZzsData() {
  var index = layer.load(2, { shade: 0.3 });
  var cbQsBz = 'N';
  var zzsQsBz = 'Y';
  getCbAndZzsYsData(cbQsBz, zzsQsBz);
  layer.close(index);
}
/**
 * 要素申报-获取要素信息，把数据加载到表单里
 */
var ysData_params = {
  SB: ['reset'],
};
function getCbAndZzsYsData(cbQsBz, zzsQsBz) {
  if (parent.location.href.indexOf('yslookBack=Y') === -1 && parent.location.href.indexOf('lookBack=Y') > -1) {
    return;
  }

  if (parent.location.href.indexOf('qqdcx=Y') > -1) {
    return;
  }
  var yjfs = formData.hq_.qtxx.yjfs;
  var sbqylx = formData.hq_.qtxx.sbqylx;
  var ssqz = formData.fq_.sssq.sqZ;
  var bool =
    ssqz.split('-')[1] == '03' ||
    ssqz.split('-')[1] == '06' ||
    ssqz.split('-')[1] == '09' ||
    ssqz.split('-')[1] == '12';
  if (parent.location.href.indexOf('qysds_a_21yjd?') > -1) {
    if (cbQsBz == 'Y') {
      if (sbqylx != 2 && (yjfs == 1 || bool)) {
      } else {
        layer.alert('当前企业类型，不可填报1至3栏及按季度填报信息栏次，无需取数！', { icon: 5, title: '提示' });
        return;
      }
    } else if (zzsQsBz == 'Y') {
      if (sbqylx != 2 && yjfs == 1) {
      } else {
        layer.alert('当前企业类型，不可填报1至3栏及按季度填报信息栏次，无需取数！', { icon: 5, title: '提示' });
        return;
      }
    }
  }
  // url中的参数一并带回给后台
  var params = parent.location.href.substring(parent.location.href.indexOf('?') + 1);
  if (params.lastIndexOf('#') == params.length - 1) {
    params = params.substr(0, params.length - 1);
  }
  var data = {};
  //财报取数标志
  data.cbQsBz = cbQsBz;
  //增值税取数标志
  data.zzsQsBz = zzsQsBz;
  var paramArr = params.split('&');
  for (var i = 0; i < paramArr.length; i++) {
    var param = paramArr[i].split('=');
    data[param[0]] = param[1];
  }

  // 生产环境的url可能没有带上完整的参数,固定放入djxh,ywbm,ssq,ywlx
  data.ywbm = $('#ywbm').val();
  data.djxh = $('#djxh').val();
  data.sssqQ = $('#sssqQ').val();
  data.sssqZ = $('#sssqZ').val();
  data.sbqylx = sbqylx;
  if (parent.ywlx !== undefined) {
    data.ywlx = parent.ywlx;
  } else {
    var index_start = parent.location.href.indexOf('biz/') + 4;
    var index_end = index_start + parent.location.href.substring(index_start).indexOf('/');
    data.ywlx = parent.location.href.substring(index_start, index_end).toUpperCase();
  }

  /**
   * 要素请求时，对指定的参数从当前路径下取值,不从父页面取值。例如：reset 以及重置时更新的值
   */
  if (location.href.indexOf('?') > -1 && ysData_params[data.ywlx]) {
    var params = location.href.substring(location.href.indexOf('?') + 1);
    if (params.lastIndexOf('#') == params.length - 1) {
      params = params.substr(0, params.length - 1);
    }
    var paramArr = params.split('&');
    for (var i = 0; i < paramArr.length; i++) {
      var param = paramArr[i].split('=');
      if (ysData_params[data.ywlx].indexOf(param[0]) > -1) {
        data[param[0]] = param[1];
      }
    }
  }
  var sbzxWebContent = parent.location.pathname.substring(0, parent.location.pathname.substring(1).indexOf('/') + 1);
  var url = sbzxWebContent + '/ywzt/getYsData.do';

  if ((data.ywlx === 'CWBB' || data.ywlx === 'CWBB1') && parent.location.href.indexOf('isYsdj=Y') > -1) {
    // CLOUDTAX-2596 云上电局2.0财报请求plcwbb的要素
    url = '/sbzx-cjpt-web/nssb/qs/getYsData.do';
  } else if (parent.location.href.indexOf('ywzt=Y') === -1) {
    // 2.0不应该发起请求4.0的要素请求
    return;
  }

  if (typeof data.ywlx !== 'object' && data.ywlx !== 'SXSQ' && data.ywlx !== 'SXSL') {
    $.ajax({
      url: url,
      type: 'POST',
      dataType: 'json',
      async: false,
      data: data,
      success: function (data) {
        if (data && data.rtnCode === '000') {
          var wbcsh = data.body;
          //TODO 请求要素数据之前，wbcsh已经赋值，再设置wbcsh的值会覆盖原有值
          if (typeof wbcsh !== 'undefined' && wbcsh !== null && wbcsh !== '') {
            // 请求到要素数据的情况才设置值，防止请求到空的要素数据覆盖了之前设置的wbcsh的值
            formulaEngine.otherParams['wbcsh'] = Base64.encode(JSON.stringify(JSON.parse(wbcsh)));
            // 强制刷新标识，使用场景：纳税人手工修改表单数据，要数报文未发生改变。
            formulaEngine.otherParams['qzsx'] = 'Y';
            // 1、更新formData wbcsh节点数据模型
            $('#wbcsh').val(Base64.encode(JSON.stringify(JSON.parse(wbcsh))));
            var $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
            var viewEngine = $('#frmSheet')[0].contentWindow.viewEngine;
            var body = $('#frmSheet')[0].contentWindow.document.body;
            //2、执行外部初始化公式 CLOUDTAX-3243（手工修改"季末资产"后再点取数未带出数据）
            //注：出于性能考虑，这里改为只执行对应公式，其他无关联公式不执行。
            // var types = ['11'];
            // formulaEngine.applyImportFormulasBytypes(types,true);
            // 适用要素传值模式节点值没变不触发公式的情况
            var list = [];
            if (formulaEngine.getFormulaById('06100103010100192ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100192ysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100192ysdj2') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100192ysdj2'));
            }

            if (formulaEngine.getFormulaById('06100103010100216ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100216ysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100217ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100217ysdj'));
            }
            if (formulaEngine.getFormulaById('06100103010100302') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100302'));
            }

            if (formulaEngine.getFormulaById('06100103010100270') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100270'));
            }

            if (formulaEngine.getFormulaById('06100103010100305') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100305'));
            }

            if (formulaEngine.getFormulaById('0610010qczcze4ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('0610010qczcze4ysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100276') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100276'));
            }

            if (formulaEngine.getFormulaById('061001qmzcze4ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('061001qmzcze4ysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100293') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100293'));
            }

            if (formulaEngine.getFormulaById('06100103010100282') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100282'));
            }

            if (formulaEngine.getFormulaById('06100103010100267') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100267'));
            }

            if (formulaEngine.getFormulaById('06100103010100290') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100290'));
            }

            if (formulaEngine.getFormulaById('06100103010100279') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100279'));
            }

            if (formulaEngine.getFormulaById('0610010qmcyrsysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('0610010qmcyrsysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100299') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100299'));
            }

            if (formulaEngine.getFormulaById('06100103010100296') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100296'));
            }

            if (formulaEngine.getFormulaById('0610010qccyrs4ysdj') != undefined) {
              list.push(formulaEngine.getFormulaById('0610010qccyrs4ysdj'));
            }

            if (formulaEngine.getFormulaById('06100103010100273') != undefined) {
              list.push(formulaEngine.getFormulaById('06100103010100273'));
            }

            var sfsyxxwlqy = formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
            if ('Y' == sfsyxxwlqy) {
              formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
            }
            var jpathList = [];
            jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.yysrLj');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.yycbLj');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.lrzeLj');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.sjlreLj');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.ynsdseLj');
            jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.jmsdseLj');
            jpathList.push('ht_.ywbw..mssrGrid.mssrGridlb[#].yhjmje');
            formulaEngine.apply4List(jpathList);

            formulaEngine.calculationPlanningOfList(list, undefined, true);
            formulaEngine.executeWbcshFormula();
            formulaEngine.applyAssociatedFormulaVerify(null);
            // 3、刷新form表单
            viewEngine.formApply($viewAppElement);
            // 4、渲染校验公式、控制公式效果
            viewEngine.tipsForVerify(body);
          }
          // 用户点完取数后将表单数据暂存，纳税人下次打开申报表优先取暂存数据，其次获取财报数据。
          var bo = JSON.stringify(wbcsh).indexOf('cbOrZzsZcbwEmpty') > -1;
          if (JSON.stringify(wbcsh).indexOf('cbOrZzsZcbwEmpty') > -1) {
            if (cbQsBz == 'Y') {
              layer.alert('未找到对应属期的财务报表', { icon: 5, title: '提示' });
            } else {
              layer.alert('未找到对应属期的增值税报表', { icon: 5, title: '提示' });
            }
          } else {
            autoSaveRun();
            layer.alert('取数成功！', { icon: 6, title: '提示' });
          }
        } else {
          console.log('getYsData请求异常, 返回数据:' + data);
        }
      },
      error: function (xhr, textStatus, errorThrown) {
        console.log('getYsData请求' + textStatus + '异常: ' + errorThrown);
      },
      complete: function (xhr, ts) {},
      timeout: 5000,
    });
  }
}
// 【云上电局】JCDA2019-9610 从财报取数，符合下述条件1或2才取，否则不取。
// 从增值税报表取数，符合下述条件1才取，否则不取。
// 条件1：sbqylx=0或1，且yjfs=1
// 条件2：sbqylx=0或1，且是季报或季末月报
function ysDataFilter(formData) {
  if (formData && !isJmsb()) {
    var yjfs = formData.hq_.qtxx.yjfs;
    var sbqylx = formData.hq_.qtxx.sbqylx;
    var ssqz = formData.fq_.sssq.sqZ;
    var bool =
      ssqz.split('-')[1] == '03' ||
      ssqz.split('-')[1] == '06' ||
      ssqz.split('-')[1] == '09' ||
      ssqz.split('-')[1] == '12';
    if (parent.location.href.indexOf('qysds_a_18yjd?') > -1) {
      if (sbqylx != 2 && (yjfs == 1 || bool)) {
        return;
      } else {
        $('#wbcsh').val('');
      }
    }
  } else {
    return;
  }
}

function gdgxh(swjgDm) {
  var swjgDm = swjgDm.substring(0, 3);
  if (swjgDm == '144') {
    return true;
  } else {
    return false;
  }
}
function tstip(content) {
  return true;

  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    return;
  }

  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  var yjfs = formData.ht_.ywbw.A200000Ywbd.nsrxx.yjfs;
  if (!(yjfs == '1' && (sbqylx == '1' || sbqylx == '0'))) {
    $('#btnCbsj', parent.document).hide();
    return true;
  }
  if (
    parent.location.href.indexOf('gzsb=zx') == -1 &&
    parent.location.href.indexOf('DZSWJ_TGC') == -1 &&
    parent.location.href.indexOf('qddm=YSDJ') == -1 &&
    parent.location.href.indexOf('isYsdj=Y') == -1 &&
    parent.location.href.indexOf('hqcbsj=Y') == -1
  ) {
    //type：0为消息弹框(如window.alert)，只会显示一条覆盖其他。
    layer.open({
      type: 1,
      area: ['448px'],

      content: "<div style = 'line-height: 24px;'>" + content + '</div>',
      title: '提示',
      btn: ['确定'],
    });
  }
  return true;
}

async function getcwbbxx() {
  let reqParams = {};
  reqParams.djxh = formData.fq_.nsrjbxx.djxh;
  reqParams.sid = 'dzswj.ywzz.sb.qysdsa18yjd.queryCwbbData';

  let sssqQ = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
  let sssqZ = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  let swjgDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  let swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  reqParams.sssqZ = sssqZ;
  if (swjgDm === '44') {
    reqParams.sssqQ = sssqQ;
  }
  reqParams.zlbsxlDm = 'ZL1001001,ZL1001050,ZL1001002,ZL1001003,ZL1001051,ZL1001052';

  /*获取财务报表数据*/
  let _data = await $vue.$fetch({ url: sbzxCtl + '/ywzt/getData.do', data: reqParams });
  //	todo 暂无接口 待接口出来后再改
  _data = new Function('return ' + _data)();
  let bodydata = new Function('return ' + _data.body)();
  if (
    (swjgDm === '44' || (swjgDm == '37' && swjgDm15 != '3702')) &&
    ('ZL1001051' == bodydata.zlbsxlDm || 'ZL1001052' == bodydata.zlbsxlDm)
  ) {
    return;
  }
  let cwbb = bodydata.cwbb;
  if (cwbb.length > 0) {
    let yysrLj = 0.0;
    let yycbLj = 0.0;
    let lrzeLj = 0.0;
    for (let i = 0; i < cwbb.length; i++) {
      //SDSDSYHB-3531 山东区域 企业会计制度（ZL1001002） 不需要带出 营业收入和 营业成本
      if (swjgDm != '37' || swjgDm15 == '3702' || bodydata.zlbsxlDm != 'ZL1001002') {
        if (cwbb[i].mc.indexOf('yysr') > -1 && cwbb[i].bqje != '') {
          yysrLj = parseFloat(cwbb[i].bqje);
          formData.ht_.ywbw.A200000Ywbd.sbxx.t_yysrLj = yysrLj;
        }
        if (cwbb[i].mc.indexOf('yycb') > -1 && cwbb[i].bqje != '') {
          yycbLj = parseFloat(cwbb[i].bqje);
          formData.ht_.ywbw.A200000Ywbd.sbxx.t_yycbLj = yycbLj;
        }
      }

      if (cwbb[i].mc.indexOf('lrze') > -1 && cwbb[i].bqje != '') {
        lrzeLj = parseFloat(cwbb[i].bqje);
        // SDSDSYHB-3491  取到数才赋值，没取到数允许，不弹出校验
        formData.ht_.ywbw.A200000Ywbd.sbxx.t_lrzeLj = lrzeLj;
      }
    }
    if (swjgDm != '37' || swjgDm15 == '3702' || bodydata.zlbsxlDm != 'ZL1001002') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.yysrLj = yysrLj;
      let _jpath = 'ht_.ywbw.A200000Ywbd.sbxx.yysrLj';
      formulaEngine.apply(_jpath, yysrLj);
      formData.ht_.ywbw.A200000Ywbd.sbxx.yycbLj = yycbLj;
      _jpath = 'ht_.ywbw.A200000Ywbd.sbxx.yycbLj';
      formulaEngine.apply(_jpath, yycbLj);
    }
    formData.ht_.ywbw.A200000Ywbd.sbxx.lrzeLj = lrzeLj;
    let _jpath = 'ht_.ywbw.A200000Ywbd.sbxx.lrzeLj';
    formulaEngine.apply(_jpath, lrzeLj);
  }
}

function zdGetcwbbxx(sbqylx, yjfs) {
  //	todo 注释，提供接口再放开
  // getcwbbxx();
  return true;
}
// 云上电局所得税a单户取数帮助按钮
function getHelp() {
  layer.alert(
    '企业所得税，默认带出财务申报表数据，您可通过【取数-取增值税收入】更改取数来源。<br/>' +
      '请注意：记账完成后，先前往【批量-财务报表】执行取数操作，才能取到财报的数据。',
    { title: '提示' },
  );
}

//是否弹框重置报表提示 重写方法dom操作
function ifTipsReset() {
  // if (dzbdbmList.includes('BDA0611034') && !formData.ht_.ywbw.A201010Ywbd
  // 	|| dzbdbmList.includes('BDA0611035') && !formData.ht_.ywbw.A201020Ywbd
  // 	|| dzbdbmList.includes('BDA0611036') && !formData.ht_.ywbw.A201030Ywbd
  // ) {
  // 	return true
  // }
  return false;
}
// JCDA2019-7978 云上所得税a单户取数按钮气泡提示语
var settings = {
  trigger: 'hover',
  closeable: false,
  width: 410,
  style: '',
  delay: 300,
};
function initPopover() {
  if (parent.location.href.indexOf('isYsdj=Y') == -1) {
    return;
  }

  if (parent.$('img.show-pop').length == 0) {
    setTimeout(initPopover, 500);
  }
  parent.$('img.show-pop').webuiPopover('destroy').webuiPopover(settings);
}
initPopover();

//计算本年已申报的季度数
function getYsbjds(sqZ, bnjd1sfysb, bnjd2sfysb, bnjd3sfysb) {
  var sbyf = sqZ.split('-')[1];

  if (sbyf <= 3) {
    return 0;
  }

  if (sbyf >= 4 && sbyf <= 6 && bnjd1sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 4 && sbyf <= 6 && bnjd1sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 7 && sbyf <= 9 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N' && bnjd3sfysb === 'N') {
    return 0;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'N' && bnjd3sfysb === 'Y') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N' && bnjd3sfysb === 'N') {
    return 1;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'N' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'N' && bnjd3sfysb === 'Y') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'N') {
    return 2;
  }

  if (sbyf >= 10 && sbyf <= 12 && bnjd1sfysb === 'Y' && bnjd2sfysb === 'Y' && bnjd3sfysb === 'Y') {
    return 3;
  }
}

/**
 * 提交申报时 对提交报文进行处理
 */
function updateSubmitData() {
  var sfsyxxwlqy = formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
  var xsxwqy = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect;
  if (sfsyxxwlqy === 'N' || (sfsyxxwlqy === 'Y' && xsxwqy)) {
    formData.ht_.ywbw.A200000Ywbd.sbxx.bxsxwyhly = '';
  }
  // 勾选优惠项中的其他时，如果为空的默认减免税代码 0004129999
  var mssrGridlb = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb;
  var defaultSsjmxzDmList = ['JJSR999', 'MSSR999', 'JJSRQT', 'MSSRQT'];
  for (var i = 0; i < mssrGridlb.length; i++) {
    var t_mssrsxSelect = mssrGridlb[i].t_mssrsxSelect;
    var yhswsx = mssrGridlb[i].yhswsx;
    var ssjmxzDm = mssrGridlb[i].ssjmxzDm;
    if (t_mssrsxSelect && isNull(ssjmxzDm) && defaultSsjmxzDmList.includes(yhswsx)) {
      formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].ssjmxzDm = '0004129999';
    }
  }
  var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
  for (var i = 0; i < jmsdGridlb.length; i++) {
    var t_jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
    var yhswsx = jmsdGridlb[i].yhswsx;
    var ssjmxzDm = jmsdGridlb[i].ssjmxzDm;
    if (t_jmsdssxSelect && isNull(ssjmxzDm) && yhswsx == 'JMSE99999') {
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].ssjmxzDm = '0004129999';
    }
  }
  //xsd 2.46版本启用标志
  var xsd246qybz = formData.kz_.xsd246qybz;
  //带出的表单列表
  var dzbdlist = document.getElementById('dzbdbmList').value;
  if (dzbdlist.indexOf('BDA0611033') > -1) {
    var sdjmGridlb = formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb;
    if (
      sdjmGridlb.length >= 15 &&
      sdjmGridlb[12].ewbhxh != undefined &&
      sdjmGridlb[12].ewbhxh == 13 &&
      sdjmGridlb[12].t_jejslxMc != undefined &&
      sdjmGridlb[12].t_jejslxMc == '其他' &&
      sdjmGridlb[13].ewbhxh != undefined &&
      sdjmGridlb[13].ewbhxh == 14 &&
      sdjmGridlb[14].ewbhxh != undefined &&
      sdjmGridlb[14].ewbhxh == 15
    ) {
      formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[12].ewbhxh = 15;
      formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[13].ewbhxh = 13;
      formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[14].ewbhxh = 14;
    }
  }

  //主表报文处理：一、  2.46版本未启用  要把2.46新加的节点去掉；
  if (xsd246qybz != 'Y' && dzbdlist.indexOf('BDA0611033') > -1) {
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze4;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.bhzsm;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.qycyrsQnpjrs;
    delete formData.ht_.ywbw.A200000Ywbd.sbxx.zczeQnpjs;
  }

  if (xsd246qybz != 'Y' && dzbdlist.indexOf('BDA0611034') > -1) {
    delete formData.ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.ybgxhltzsyLj;
    delete formData.ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fpjzzcqekc;
  }

  if (xsd246qybz != 'Y' && dzbdlist.indexOf('BDA0611036') > -1) {
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.qt1lj;
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.qt3lj;
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.zyywsrzb;
  }

  //二、2.46版本启用要对一些节点特殊传值
  if (xsd246qybz == 'Y' && dzbdlist.indexOf('BDA0611033') > -1) {
    var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
    var bnjd1sfysb = formData.fq_.bnjd1sfysb;
    var bnjd2sfysb = formData.fq_.bnjd2sfysb;
    var bnjd3sfysb = formData.fq_.bnjd3sfysb;
    //申报期止月份
    var sbyf = parseInt(skssqz.split('-')[1], 10);
    var qccyrs1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1;
    var qccyrs1 = qccyrs1.toString();
    if (qccyrs1.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1 = ROUND(qccyrs1, 0);
    }

    var qccyrs2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2;
    var qccyrs2 = qccyrs2.toString();
    if (qccyrs2.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2 = ROUND(qccyrs2, 0);
    }

    var qccyrs3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3;
    var qccyrs3 = qccyrs3.toString();
    if (qccyrs3.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3 = ROUND(qccyrs3, 0);
    }

    var qccyrs4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4;
    var qccyrs4 = qccyrs4.toString();
    if (qccyrs4.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4 = ROUND(qccyrs4, 0);
    }

    var qmcyrs1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1;
    var qmcyrs1 = qmcyrs1.toString();
    if (qmcyrs1.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1 = ROUND(qmcyrs1, 0);
    }

    var qmcyrs2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2;
    var qmcyrs2 = qmcyrs2.toString();
    if (qmcyrs2.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2 = ROUND(qmcyrs2, 0);
    }

    var qmcyrs3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3;
    var qmcyrs3 = qmcyrs3.toString();
    if (qmcyrs3.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3 = ROUND(qmcyrs3, 0);
    }

    var qmcyrs4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4;
    var qmcyrs4 = qmcyrs4.toString();
    if (qmcyrs4.indexOf('.00') > -1) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4 = ROUND(qmcyrs4, 0);
    }

    if (bnjd1sfysb == 'N' && sbyf > 3) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1 = '';
    }

    if (bnjd2sfysb == 'N' && (sbyf < 4 || sbyf > 6)) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2 = '';
    }

    if (bnjd3sfysb == 'N' && (sbyf < 7 || sbyf > 9)) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3 = '';
    }

    if (sbyf < 10) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze4 = '';
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4 = '';
    }

    if (sbyf <= 3) {
      var qccyrs1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1;
      var qmcyrs1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1;
      var qczcze1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1;
      var qmzcze1 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs = qccyrs1;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs = qmcyrs1;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze = qczcze1;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze = qmzcze1;
    }

    if (sbyf >= 4 && sbyf <= 6) {
      var qccyrs2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2;
      var qmcyrs2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2;
      var qczcze2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2;
      var qmzcze2 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs = qccyrs2;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs = qmcyrs2;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze = qczcze2;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze = qmzcze2;
    }

    if (sbyf >= 7 && sbyf <= 9) {
      var qccyrs3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3;
      var qmcyrs3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3;
      var qczcze3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3;
      var qmzcze3 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs = qccyrs3;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs = qmcyrs3;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze = qczcze3;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze = qmzcze3;
    }

    if (sbyf >= 10 && sbyf <= 12) {
      var qccyrs4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4;
      var qmcyrs4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4;
      var qczcze4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze4;
      var qmzcze4 = formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs = qccyrs4;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs = qmcyrs4;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze = qczcze4;
      formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze = qmzcze4;
    }
  }

  if (DATE_CHECK_TIME_SIZE('2020-12-31', skssqz) && dzbdlist.indexOf('BDA0611036') > -1) {
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.szdqncxysLj;
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.ewwmjcdljaswslLj;
    delete formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.bsyyjcdljaswslLj;
  }

  if (dzbdlist.indexOf('BDA0611035') > -1) {
    var gdzcjszjkcmxbGridlb2 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
    for (var i in gdzcjszjkcmxbGridlb2) {
      var yhswsx = gdzcjszjkcmxbGridlb2[i].yhswsx;
      var t_yhsxmc = gdzcjszjkcmxbGridlb2[i].t_yhsxmc;
      var gdzcjszjCT = formCT.gdzcjszjCT;
      if (!isNull(yhswsx) && isNull(t_yhsxmc) && !isNull(gdzcjszjCT[yhswsx])) {
        formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[i].t_yhsxmc = gdzcjszjCT[yhswsx];
      }
    }
  }
}

/**
 A201020计算合计值，兼容改版
 arguments[0]传246改版之前的合计值计算
 arguments[1]传246改版之后的合计值计算
 合计值传数组，原值传 index:field 字符串
 */
function a201020CalHj() {
  var xsd246qybz = formData.kz_.xsd246qybz;
  var obj = xsd246qybz == 'Y' ? arguments[1] : arguments[0];
  var sum = 0;

  if (obj instanceof Array) {
    for (var i = 0; i < obj.length; i++) {
      sum += obj[i];
    }
  } else {
    var arr = obj.split(':');
    var index = arr[0];
    var field = arr[1];
    sum = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb[index][field];
  }
  return sum;
}

/**
 A201010计算合计值，兼容改版
 arguments[0]传246改版之前的合计值计算
 arguments[1]传246改版之后的合计值计算
 合计值传数组，原值传 index.field 字符串
 */
function calHjWithOldVersion() {
  var xsd246qybz = formData.kz_.xsd246qybz;
  var obj = xsd246qybz == 'Y' ? arguments[1] : arguments[0];
  var sum = 0;

  if (obj instanceof Array) {
    for (var i = 0; i < obj.length; i++) {
      sum += obj[i];
    }
  } else {
    sum = formData.ht_.ywbw;
    var arr = obj.split('.');
    for (var i = 0; i < arr.length; i++) {
      sum = sum[arr[i]];
    }
  }
  return sum;
}

// $(document).ready(function(){
//
// 	// 修改左侧菜单A201020附表名称
// 	var sssqz = new Date(getQueryVariable('sssqZ'));
// 	var date246 = new Date('2020-06-30');
// 	if(sssqz>=date246){
// 		$("#divSheetlist [dzbdbm='BDA0611035']").attr('title', '《A201020资产加速折旧、摊销（扣除）优惠明细表》');
// 		$("#divSheetlist [dzbdbm='BDA0611035']").text('《A201020资产加速折旧、摊销（扣除）优惠明细表》');
// 	}
// });

function getQueryVariable(variable) {
  var query = window.location.search.substring(1);
  var vars = query.split('&');
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=');
    if (pair[0] == variable) {
      return pair[1];
    }
  }
  return false;
}

/**
 * 云上电局 季末资产总额取值逻辑 2020第二季度后开始执行
 * @param bnjd1sfysb  $..fq_.bnjd1sfysb
 * @param sqQmzcze  $..qtxx.sqQmzcze1、2、3、4
 * @param jczczeTYsdj  $..kz_.temp.zb.jmzcze_t_ysdj
 * @param jczczeT  $..kz_.temp.zb.jmzcze_t
 * @returns {*}
 */
function ysdjGetQmzcze(bnjdsfysb, sqQmzcze, jmzczeTYsdj, jmzczeT) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();

  // 本季度已申报，返回核心接口的资产总额
  if (bnjdsfysb == 'Y') {
    // 前几季度
    if (lsbBz) {
      if (sqQmzcze == '' || sqQmzcze == 0 || sqQmzcze == undefined || sqQmzcze == null) {
        return 0.01;
      } else {
        // 0.0001 的情況零申报或静默申报应该 返回1
        if (ROUND(sqQmzcze, 2) == 0) {
          return 0.01;
        } else {
          return sqQmzcze;
        }
      }
    } else {
      return sqQmzcze;
    }
  } else {
    // 当前季度
    // 零申报处理
    if (lsbBz) {
      if (ROUND(jmzczeTYsdj, 2) == 0) {
        return 0.01;
      } else {
        return jmzczeTYsdj;
      }
    } else {
      return jmzczeT == 0 ? '' : jmzczeT;
    }
  }
}

/**
 * 云上电局季初资产总额取值逻辑 2020第二季度后开始执行
 * @param bnjd1sfysb
 * @param sqQczcze
 * @param jczczeQuanguo
 * @param jczczeT
 * @param sqQmzcze
 */
function ysdjGetQczcze(bnjdsfysb, sqQczcze, jczczeQuanguo, jczczeT, sqQmzcze) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (bnjdsfysb == 'Y') {
    // 前几季度
    if (lsbBz) {
      if (sqQczcze == '' || sqQczcze == 0 || sqQczcze == undefined || sqQczcze == null) {
        return 0.01;
      } else {
        if (ROUND(sqQczcze, 2) == 0) {
          return 0.01;
        } else {
          return sqQczcze;
        }
      }
    } else {
      return sqQczcze;
    }
  } else {
    //当前季度
    if (lsbBz) {
      // 第一季度 取同期财务报表期初资产总额，财报无数据则默认1； （其他季）：取期初数sqQmzcze，为0则默认1；
      if (typeof sqQmzcze != 'undefined') {
        if (sqQmzcze == 0 || sqQmzcze == '') {
          return 0.01;
        }
        return ROUND(sqQmzcze, 2) == 0 ? 0.01 : sqQmzcze;
      }
      if (jczczeQuanguo != 0 && ROUND(jczczeQuanguo, 2) == 0) {
        return 0.01;
      } else {
        return jczczeQuanguo == 0 ? jczczeT : jczczeQuanguo;
      }
    } else {
      // 除第一季度外 季初资产总额 取上一季度末资产总额
      if (typeof sqQmzcze != 'undefined') {
        return sqQmzcze;
      }
      return jczczeQuanguo;
    }
  }
}

/**
 * 云上电局从业人数取值逻辑 2020第二季度后开始执行
 * @param bnjd1sfysb
 * @param sqCyrs
 * @param ysSqQmcyrs
 */
function ysdjGetcyrs(bnjdsfysb, sqCyrs, ysSqQmcyrs, sqQmcyrs) {
  var lsbBz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (bnjdsfysb == 'Y') {
    if (lsbBz) {
      if (sqCyrs == '' || sqCyrs == 0 || sqCyrs == undefined || sqCyrs == null) {
        return 1;
      } else {
        return sqCyrs;
      }
    } else {
      return sqCyrs;
    }
  } else {
    if (lsbBz) {
      // 取期初sqQmcyrs，为空取客户资料从业人数，客户资料未设置则默认1；
      if (typeof sqQmcyrs != 'undefined') {
        if (sqQmcyrs == 0 || sqQmcyrs == '') {
          if (ysSqQmcyrs == 0 || ysSqQmcyrs == '') {
            return 1;
          } else {
            return ysSqQmcyrs;
          }
        }
        return sqQmcyrs;
      }
      return ysSqQmcyrs == 0 || ysSqQmcyrs == '' ? 1 : ysSqQmcyrs;
    } else {
      // 除第一季度 从业人数先从上期季末从业人数取 没有从客户资料取
      if (typeof sqQmcyrs != 'undefined') {
        if (sqQmcyrs != '' && sqQmcyrs != 0) {
          return sqQmcyrs;
        }
        return ysSqQmcyrs == 0 ? '' : ysSqQmcyrs;
      }
      return ysSqQmcyrs == 0 ? '' : ysSqQmcyrs;
    }
  }
}

// 本表第4行至第28.3行（除第23行、第28.2行）不能叠加享受减免所得税优惠，请准确选择优惠项目
function djxsjmsdsyh(
  c1,
  c2,
  c3,
  c4,
  c5,
  c6,
  c7,
  c8,
  c9,
  c10,
  c11,
  c12,
  c13,
  c14,
  c15,
  c16,
  c17,
  c18,
  c19,
  c20,
  c21,
  c22,
  c23,
  c24,
  c25,
) {
  var xsd246qybz = formData.kz_.xsd246qybz;
  if (xsd246qybz == 'Y') {
    var arry = [
      c1,
      c2,
      c3,
      c4,
      c5,
      c6,
      c7,
      c8,
      c9,
      c10,
      c11,
      c12,
      c13,
      c14,
      c15,
      c16,
      c17,
      c18,
      c19,
      c20,
      c21,
      c22,
      c23,
      c24,
      c25,
    ];
    var index = 0;
    for (var i = 0; i < arry.length; i++) {
      if (arry[i] > 0) {
        index++;
      }
    }
    if (index >= 2) {
      return false;
    } else {
      return true;
    }
  } else {
    return true;
  }
}

function alertFhtjdyxzlxsrLj(fhtjdyxzlxsrLj) {
  var isbj = formData.fq_.nsrjbxx.swjgDm.substring(1, 3) === '11';
  var xsd246qybz = formData.kz_.xsd246qybz;
  if (xsd246qybz === 'Y' && isbj && fhtjdyxzlxsrLj > 0) {
    parent.layer.open({
      content: '您填报享受了永续债利息收入免征企业所得税优惠（本表第7行），请您再次确认填报是否正确。',
      title: '提示',
      type: 1,
      btn: ['确定', '取消'],
      btn2: function () {
        formData.ht_.ywbw.A201010Ywbd.msjjsrjjkcjmyhmxbForm.fhtjdyxzlxsrLj = 0;
        var $viewAppElement = $('#frmSheet').contents().find('#viewCtrlId');
        var viewEngine = $('#frmSheet')[0].contentWindow.viewEngine;
        var body = $('#frmSheet')[0].contentWindow.document.body;
        viewEngine.dynamicFormApply($viewAppElement, formData, formEngine);
        viewEngine.formApply($viewAppElement);
      },
    });
  }
}

function check6Zcyz(value) {
  if (value >= 5000000) {
    var msg = '本行用于填报500万元以下设备器具，请检查自查原值是否正确';
    parent.layer.open({
      title: '提示',
      type: 1,
      content: msg,
      btn: ['确定'],
    });
  }
}

// function check7Zcyz(value){
//     if(value<5000000 && value>0){
//         formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb[7].zcyz = 0;
//         var msg="本行用于填报500万元以上设备，请检查自查原值是否正确";
//         parent.layer.open({
//             title:"提示",
//             type:1,
//             content:msg,
//             btn: ['确定']
//         });
//
//     }
// }
//当第23行=0时，本行项目名称中的“主营业务收入占比”锁定为0
function zyywsrb(xbdqgllqyjaswLj) {
  var xsd246qybz = formData.kz_.xsd246qybz;
  if (xsd246qybz == 'Y' && xbdqgllqyjaswLj == 0) {
    formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.zyywsrzb = 0;
  }
}

//A201010表 第14行锁定 true锁定
function zgcawhqdbjdazwfqzfsrLjReadonly() {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var result = false;
  //贵州、陕西直接锁定
  if (swjgDm.substring(1, 3) == '52' || swjgDm.substring(1, 3) == '61') {
    result = true;
  } else if (swjgDm.substring(1, 3) == '11' && formData.kz_.xsd246qybz == 'Y') {
    //北京 除nsrsbh为511000005000059830以外的锁定
    var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
    if (nsrsbh != '511000005000059830') {
      result = true;
    }
  } else if (swjgDm.substring(1, 3) == '63' && formData.kz_.xsd246qybz == 'Y') {
    result = true;
  }
  return result;
}

//A201010表 第13行锁定 true锁定
function zgawhqdbjdazwsfsrLjReadonly() {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var result = false;
  //贵州、陕西直接锁定
  if (swjgDm.substring(1, 3) == '52' || swjgDm.substring(1, 3) == '61') {
    result = true;
  } else if (swjgDm.substring(1, 3) == '11' && formData.kz_.xsd246qybz == 'Y') {
    //北京 除nsrsbh为511000005000187177以外的锁定
    var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
    if (nsrsbh != '511000005000187177') {
      result = true;
    }
  } else if (swjgDm.substring(1, 3) == '63' && formData.kz_.xsd246qybz == 'Y') {
    result = true;
  }
  return result;
}

//A201030表 当主表L15行 “是否延缓缴纳所得税”变为“是”时，第29行选择为‘无’
function jzmzlxL15(yhjnsds) {
  var xsd246qybz = formData.kz_.xsd246qybz;
  if (yhjnsds == 'Y' && xsd246qybz == 'Y') {
    return (formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.jzmzlx = '');
  }
}

function setJbrzyzjhm(bz, zjhm, zjlx) {
  var sfqdxw300w = formData.kz_.temp.zb.sfqdxw300w;
  var smzbz = formData.fq_.smzxx.smzbz;
  var smzjhm = formData.fq_.smzxx.zjhm;
  var blrysfzjhm = formData.ht_.ywbw.A200000Ywbd.qtxx.blrysfzjhm;
  if (sfqdxw300w == 'Y') {
    if (bz === 'xsbz') {
      if (zjhm != undefined && zjhm != null && zjhm != '' && smzbz == 'Y') {
        if (zjlx == '201') {
          return zjhm.substring(0, 6) + '********' + zjhm.substring(14);
        } else {
          return zjhm;
        }
      } else {
        return blrysfzjhm == undefined || blrysfzjhm == null ? '' : blrysfzjhm;
      }
    } else {
      if (smzjhm != undefined && smzjhm != null && smzjhm != '' && smzbz == 'Y') {
        return smzjhm;
      } else {
        return zjhm == undefined || zjhm == null ? '' : zjhm;
      }
    }
  } else {
    if (smzbz == 'Y') {
      return smzjhm;
    } else {
      return blrysfzjhm;
    }
  }
}

function sfsbjccgb(sffsjsrgdynssx) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);

  if (dqDm != '3502') {
    return;
  }
  if (sffsjsrgdynssx != 'Y') {
    $("#divSheetlist [dzbdbm='BDA0610913_18yjd']").attr('hidden', true);
    $("#divSheetlist [dzbdbm='BDA0610913_18yjd']").attr('style', 'display:none');
    if (formData.ht_.ywbw.dynsbabYwbd) {
      formData.ht_.ywbw.dynsbabYwbd.dynsbabGrid.dynsbabGridlb[0].jscgmc = '';
    }
  } else {
    $("#divSheetlist [dzbdbm='BDA0610913_18yjd']").attr('hidden', false);
    $("#divSheetlist [dzbdbm='BDA0610913_18yjd']").attr('style', 'display:inline-block');
  }
}

/**
 * 申报提交前的处理
 * @param isSecondCall
 */
var indexYffyjjkcyhzcTxtx;
function doBeforeCtipsVerify(isSecondCall) {
  var sbqylx = formData.hq_.qtxx.sbqylx;
  var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;

  //批量申报时弹框要频闭
  var plsbbz =
    parent.location.href.indexOf('lookBack=Y') > -1 ||
    parent.location.href.indexOf('yslookBack=Y') > -1 ||
    parent.location.href.indexOf('isPlLsb') > -1 ||
    parent.location.href.indexOf('hbsb=Z') > -1 ||
    isJmsb();

  var skssqqDate = new Date(skssqq);
  var skssqzDate = new Date(skssqz);
  var hbgDate1 = new Date('2022-10-01');
  var hbgDate2 = new Date('2022-12-31');
  if (!plsbbz && sbqylx != '2' && skssqqDate >= hbgDate1 && skssqzDate <= hbgDate2) {
    var gxjsfzqj = formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[3].jehxxz;
    if (isNull(gxjsfzqj)) {
      var sftxqxjsyh = false;
      var msg = '';

      var gdzcjszjkcmxbGridlb3 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
      for (var i = 0; i < gdzcjszjkcmxbGridlb3.length; i++) {
        if (gdzcjszjkcmxbGridlb3[i].yhswsx == 'JSZJ1100') {
          sftxqxjsyh = true;
          if (!isNull(msg)) {
            msg = msg + '、高新技术企业单价500万元以下设备器具一次性扣除';
          } else {
            msg = '高新技术企业单价500万元以下设备器具一次性扣除';
          }
        }
        if (gdzcjszjkcmxbGridlb3[i].yhswsx == 'JSZJ1110') {
          sftxqxjsyh = true;
          if (!isNull(msg)) {
            msg = msg + '、高新技术企业单价500万元以下设备器具一次性扣除';
          } else {
            msg = '高新技术企业单价500万元以下设备器具一次性扣除';
          }
        }
      }
      var gxjjkcXz = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[30].t_mssrsxSelect;
      var gxyh1 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect;
      var gxyh2 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSelect;
      if (gxjjkcXz) {
        sftxqxjsyh = true;
        if (!isNull(msg)) {
          msg = msg + '、高新技术企业设备器具加计扣除（按100%加计扣除）';
        } else {
          msg = '高新技术企业设备器具加计扣除（按100%加计扣除）';
        }
      }

      if (gxyh1) {
        sftxqxjsyh = true;
        if (!isNull(msg)) {
          msg = msg + '、国家需要重点扶持的高新技术企业减按15%的税率征收企业所得税';
        } else {
          msg = '国家需要重点扶持的高新技术企业减按15%的税率征收企业所得税';
        }
      }

      if (gxyh2) {
        sftxqxjsyh = true;
        if (!isNull(msg)) {
          msg = msg + '、经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税';
        } else {
          msg = '经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税';
        }
      }

      if (sftxqxjsyh) {
        parent.layer.confirm(
          '您享受' +
            msg +
            '等高新技术企业优惠，请确认是否符合高新技术企业资格，并在“附报事项”中的“高新技术企业资格证书发证时间”事项填写发证时间。',
          {
            area: ['600px', '300px'],
            title: '提示',
            type: 1,
            btn: ['确定'],
            cancel: function (index, layero) {
              parent.layer.close(index);
              $('body').unmask();
              if (typeof umMaskZdy == 'function') {
                umMaskZdy();
              }
              prepareMakeFlag = true;
              return;
            },
          },
          function (index) {
            parent.layer.close(index);
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        );
        return;
      }
    }
  }
  //执行多缴退税提醒
  djtstxBtn();
  //fxsmServer(isSecondCall);
}

//GDSDZSWJ-16766:广东个性化：广东省电子税务局2020年企业所得税申报系统补充,多缴退税提醒
var indexdjtstx;
function djtstxBtn(isSecondCall) {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var sssqQ = formData.fq_.sssq.sqQ;
  var nd;
  var dqdm = '';
  if (zgswjDm !== '' && zgswjDm != null && zgswjDm != undefined) {
    dqdm = zgswjDm.substring(1, 3);
  }
  if (sssqQ !== '' && sssqQ != null && sssqQ != undefined) {
    nd = sssqQ.substring(0, 4);
  }
  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (dqdm != '44' || plsbbz) {
    // 执行风险扫描服务
    fxsmServer(isSecondCall);
    return;
  }

  var tdsdqBz = formData.fq_.tdsdqBz;
  if (!tdsdqBz || 'N' == tdsdqBz) {
    // 执行风险扫描服务
    fxsmServer(isSecondCall);
    return;
  }

  var tdsdqBz2019 = tdsdqBz.indexOf('Y2019') > -1;
  var tdsdqBz2020 = tdsdqBz.indexOf('Y2020') > -1;
  if (tdsdqBz2019) {
    nd = '2019';
    if (tdsdqBz2020) {
      return tdsdqDjtstxHtml(nd, true);
    }
    return tdsdqDjtstxHtml(nd, false);
  } else if (tdsdqBz2020) {
    nd = '2020';
    return tdsdqDjtstxHtml(nd, false);
  }
}

function tdsdqDjtstxHtml(nd, reqcall2020) {
  var tdsfListSkssqStr = formData.fq_.tdsfListSkssqStr;
  var callback = 'parent.fxsmServer()';
  if (reqcall2020) {
    callback = 'parent.tdsdqDjtstxHtml(2020,false)';
  }
  var pathName = document.location.pathname;
  var index = pathName.substr(1).indexOf('/');
  var result = pathName.substr(0, index + 1);
  // 弹出告知书
  indexdjtstx = layer.open({
    type: 2,
    title: '',
    area: ['550px', '250px'],
    closeBtn: 0,
    content:
      result +
      '/abacus/sb/qysds_a_18yjd/djtstx.html?callback=' +
      callback +
      '&nd=' +
      nd +
      '&tdsfListSkssqStr=' +
      tdsfListSkssqStr,
  });
}

/**
 * 企业a月季度风险扫描
 * @param isSecondCall
 */
function fxsmServer(isSecondCall) {
  var paramsValue = getFxsmParams();
  var reqParams = paramsValue.reqParams;
  var fxsmStatus = paramsValue.fxsmStatus;
  var swjgDm = paramsValue.swjgDm;

  //批量申报时弹框要频闭
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (plsbbz) {
    fxsmStatus = '0';
  }

  swjgDm = swjgDm != '' && swjgDm != null && swjgDm != undefined ? swjgDm.substring(0, 3) : '';
  var btnName = '申报';
  if (swjgDm == '144' && yhsjly != undefined && yhsjly == '0') {
    btnName = '提交';
  }

  if (fxsmStatus == '0') {
    // 执行回调函数
    ctips(isSecondCall);
  } else if (fxsmStatus == '1') {
    /* 不强制扫描 有风险扫描的弹框提示 */
    extractedNotMandatoryScan(btnName, swjgDm, isSecondCall, reqParams);
  } else if (fxsmStatus == '2') {
    /* 强制扫描 没有风险扫描的弹框提示 */
    extractedShowMessage(reqParams, isSecondCall);
  } else {
    /* 未配置 */
    extractedNotConfigured(isSecondCall);
  }
}

/**
 * 获取风险扫描参数
 * @returns {{swjgDm: *, reqParams: *, fxsmStatus: *}}
 */
function getFxsmParams() {
  var reqParams = {};
  reqParams.djxh = $('#djxh').val();
  reqParams.projectName = 'sbzx';
  reqParams.sssqQ = $('#sssqQ').val();
  reqParams.sssqZ = $('#sssqZ').val();
  reqParams.nsrsbh = $('#nsrsbh').val();
  reqParams.gdslxDm = $('#gdslxDm').val();
  reqParams.sid = 'dzswj.ywzz.sb.qysdsa18yjd.fxsmfw';
  var fxsmStatus = '1';
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var dataValue = handleHtBw(formData);
  var qqbwData = JSON.stringify(dataValue);
  reqParams.qqbwData = qqbwData;
  return { reqParams: reqParams, fxsmStatus: fxsmStatus, swjgDm: swjgDm };
}

/**
 * 不强制风险扫描
 * @param btnName
 * @param swjgDm
 * @param isSecondCall
 * @param reqParams
 */
function extractedNotMandatoryScan(btnName, swjgDm, isSecondCall, reqParams) {
  parent.layer.confirm(
    '请选择"风险提示服务"或"' +
      btnName +
      '"<br/>风险提示服务：享受税收政策风险扫描服务，提高申报数据质量。<br/>' +
      btnName +
      '：跳过风险提示服务，继续' +
      btnName +
      '。',
    {
      icon: 3,
      title: '提示',
      type: 1,
      btn: ['风险提示服务', btnName],
      cancel: function (index, layero) {
        $('body').unmask();
        if (typeof umMaskZdy == 'function') {
          umMaskZdy();
        }
        prepareMakeFlag = true;
        return;
      },
      btn2: function (index) {
        parent.layer.close(index);
        if (swjgDm == '144' && yhsjly != undefined && yhsjly == '0') {
          parent.layer.confirm(
            '请确认是否提交申报，确认则提交，取消则返回申报表页面。',
            {
              title: '提示',
              type: 1,
              btn: ['确定', '取消'],
              cancel: function (index, layero) {
                parent.layer.close(index);
                $('body').unmask();
                if (typeof umMaskZdy == 'function') {
                  umMaskZdy();
                }
                prepareMakeFlag = true;
                return;
              },
            },
            function (index) {
              parent.layer.close(index);
              // 执行回调函数
              ctips(isSecondCall);
            },
            function (index) {
              parent.layer.close(index);
              $('body').unmask();
              if (typeof umMaskZdy == 'function') {
                umMaskZdy();
              }
              prepareMakeFlag = true;
              return;
            },
          );
        } else {
          // 执行回调函数
          ctips(isSecondCall);
        }
      },
    },
    function (index) {
      parent.layer.close(index);
      extractedShowMessage(reqParams, isSecondCall);
    },
  );
}

/**
 * 调用风险扫描接口，处理返回提示信息
 * @param reqParams
 * @param isSecondCall
 */
function extractedShowMessage(reqParams, isSecondCall) {
  var indexts = parent.layer.load(2, {
    content: '正在进行风险扫描，请稍等！',
    success: function (layero) {
      layero.find('.layui-layer-content').css({
        paddingTop: '40px',
        width: '200px',
        textAlign: 'center',
        backgroundPositionX: 'center',
      });
    },
  });

  parent.requestYwztData(reqParams, function (data) {
    parent.layer.close(indexts);
    if (typeof data != 'object') {
      data = JSON.parse(data);
    }

    var bodyValue = data;
    var fhBw = bodyValue.fhMsg;
    var message = '';
    var flag = '';
    if (
      fhBw[0].message.dsRuleResult == 'zero' ||
      fhBw[0].message.dsRuleResult == 'qqBwError' ||
      fhBw[0].message.dsRuleResult == 'fhBwError'
    ) {
      message = fhBw[0].message.dsRuleResult;
    } else {
      message = hadleFhBw(fhBw);
      if (message != null) {
        flag = 'msgContent';
      }
    }

    if (message == 'zero') {
      parent.layer.alert(
        '扫描通过，即将进入申报表确认页面，请"确定"!',
        {
          title: '提示',
          icon: 6,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          // 执行回调函数
          ctips(isSecondCall);
        },
      );
    } else if (message == 'qqBwError') {
      parent.layer.alert(
        '请求报文有错，请检查！',
        {
          icon: 2,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          return;
        },
      );
    } else if (message == 'fhBwError') {
      parent.layer.alert(
        '返回报文有错，请检查！',
        {
          icon: 2,
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          return;
        },
      );
    } else if (flag == 'msgContent') {
      var errs = message
        .replace(/&nbsp;/g, '')
        .replace(/<br>/g, '')
        .replace(/\n/g, '');
      var sHtml =
        '<button  onclick="copyErro()"   class="layui-btn layui-btn-xs"   style="border: 1px solid #53acf3;background-color: #fff;  color: #53acf3;" >复制文本</button><br/><br/>';
      sHtml += '<script>';
      sHtml += '	function copyErro(){';
      sHtml += 'var copyMessage ="' + errs + '";';
      sHtml += "var oInput = document.createElement('input');";
      sHtml += 'oInput.value = copyMessage;';
      sHtml += 'document.body.appendChild(oInput);';
      sHtml += 'oInput.select(); ';
      sHtml += 'document.execCommand("Copy"); ';
      sHtml += "oInput.className = 'oInput';";
      sHtml += "oInput.style.display = 'none';";
      sHtml +=
        'parent.layer.msg(\'<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 17px;"></i></span> 复制成功!\',{time:1000});';
      sHtml += '}';
      sHtml += '</script>';

      parent.layer.confirm(
        sHtml + '扫描未通过,具体信息：<br/>' + message,
        {
          area: ['600px', '300px'],
          title: '提示',
          type: 1,
          btn: ['修改表单', '继续申报'],
          cancel: function (index, layero) {
            $('body').unmask();
            if (typeof umMaskZdy == 'function') {
              umMaskZdy();
            }
            prepareMakeFlag = true;
            return;
          },
          btn2: function (index) {
            parent.layer.close(index);
            // 执行回调函数
            ctips(isSecondCall);
          },
        },
        function (index) {
          $('body').unmask();
          if (typeof umMaskZdy == 'function') {
            umMaskZdy();
          }
          prepareMakeFlag = true;
          parent.layer.close(index);
          return;
        },
      );
    }
  });
}

/**
 * 风险扫描试用情况未配置时提示
 * @param isSecondCall
 */
function extractedNotConfigured(isSecondCall) {
  //批量申报时弹框要频闭
  parent.layer.confirm(
    '未配置或未正确配置事中监控接口,请检查!',
    {
      title: '提示',
      type: 1,
      btn: ['确定', '继续申报'],
      cancel: function (index, layero) {
        $('body').unmask();
        if (typeof umMaskZdy == 'function') {
          umMaskZdy();
        }
        prepareMakeFlag = true;
        return;
      },
      btn2: function (index) {
        parent.layer.close(index);
        // 执行回调函数
        ctips(isSecondCall);
      },
    },
    function (index) {
      parent.layer.close(index);
      $('body').unmask();
      if (typeof umMaskZdy == 'function') {
        umMaskZdy();
      }
      prepareMakeFlag = true;
      return;
    },
  );
}

/**
 * 风险扫描请求报文处理
 * @param htValue
 * @returns {undefined}
 */
function handleHtBw(formData) {
  var jmxxGrid = formData.ht_.jmxxGrid;
  var yjxxGrid = formData.ht_.yjxxGrid;
  var sbxxGrid = formData.ht_.sbxxGrid;

  if (jmxxGrid != null && jmxxGrid.jmxxGridlb != null) {
    formData.ht_.jmxxGrid = {};
  }

  if (yjxxGrid != null && yjxxGrid.yjxxGridlb != null) {
    formData.ht_.yjxxGrid = {};
  }

  if (sbxxGrid != null && sbxxGrid.sbxxGridlb != null) {
    formData.ht_.sbxxGrid = {};
  }

  return formData;
}

/**
 * 对风险扫描接口返回报文结果进行遍历
 * @param fhBw
 * @returns {undefined}
 */
function hadleFhBw(fhBw) {
  var messageValue = '';
  var dsRuleResult = fhBw[0].message.dsRuleResult;

  if (dsRuleResult != null && dsRuleResult.length >= 2) {
    var j = 0;
    $.each(dsRuleResult, function (i, item) {
      if (!isEmptyObject(item.ruleTip)) {
        messageValue += '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + numberToChinese(j + 1) + '、' + item.ruleTip + '<br>';
        j++;
      }
    });
    return messageValue;
  } else {
    if (!isEmptyObject(dsRuleResult.ruleTip)) {
      messageValue += '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;一、' + dsRuleResult.ruleTip + '<br>';
    }
    return messageValue;
  }
}

/**
 * 将阿拉伯数字转成中文
 * @param num
 * @returns {string}
 */
function numberToChinese(num) {
  var unitPos = 0;
  var strIns = '',
    chnStr = '';
  var needZero = false;

  if (num === 0) {
    return chnNumChar[0];
  }

  while (num > 0) {
    var section = num % 10000;
    if (needZero) {
      chnStr = chnNumChar[0] + chnStr;
    }
    strIns = sectionToChinese(section);
    strIns += section !== 0 ? chnUnitSection[unitPos] : chnUnitSection[0];
    chnStr = strIns + chnStr;
    needZero = section < 1000 && section > 0;
    num = Math.floor(num / 10000);
    unitPos++;
  }
  return chnStr;
}

var chnNumChar = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
var chnUnitSection = ['', '万', '亿', '万亿', '亿亿'];
var chnUnitChar = ['', '十', '百', '千'];

function sectionToChinese(section) {
  var strIns = '',
    chnStr = '';
  var unitPos = 0;
  var zero = true;
  while (section > 0) {
    var v = section % 10;
    if (v === 0) {
      if (!zero) {
        zero = true;
        chnStr = chnNumChar[v] + chnStr;
      }
    } else {
      zero = false;
      strIns = chnNumChar[v];
      strIns += chnUnitChar[unitPos];
      chnStr = strIns + chnStr;
    }
    unitPos++;
    section = Math.floor(section / 10);
  }
  return chnStr;
}

//附表2取上期数据自定义方法
function sumifArr(dzcs, index, cs, bycs) {
  var sbFbTwoGrid = formData.hq_.sbFbTwoGrid;
  if (isEmptyObject(sbFbTwoGrid)) {
    return 0;
  } else {
    var sbFbTwoGridlb = formData.hq_.sbFbTwoGrid.sbFbTwoGridlb;
    if (isEmptyObject(sbFbTwoGridlb)) {
      return 0;
    } else {
      for (var i = 0; i < sbFbTwoGridlb.length; i++) {
        var dzcsVal = sbFbTwoGridlb[i][dzcs];
        if (index == dzcsVal) {
          var csVal = sbFbTwoGridlb[i][cs];
          return csVal;
        }
      }
      return 0;
    }
  }
}

//主表总分机构取分配表数据自定义方法
function getZfjgsjxx(dzcs, index, cs, bycs) {
  var A202000Ywbd = formData.ht_.ywbw.A202000Ywbd;
  if (isEmptyObject(A202000Ywbd)) {
    return 0;
  } else {
    var fzjgxxGrid = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid;
    if (isEmptyObject(fzjgxxGrid)) {
      return 0;
    } else {
      var fzjgxxGridlb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
      for (var i = 0; i < fzjgxxGridlb.length; i++) {
        var dzcsVal = fzjgxxGridlb[i][dzcs];
        if (index == dzcsVal) {
          var csVal = fzjgxxGridlb[i][cs];
          return csVal;
        }
      }
      return 0;
    }
  }
}

function setSjlreLj(sjlreLj, xsd246qybz) {
  if (sjlreLj <= 0 || xsd246qybz !== 'Y') {
    formData.ht_.ywbw.A201030Ywbd.jmsdsyhMxbForm.qt2lj = 0;
  }
}

//江苏非网报区A201010表第7行填写增加提示(初始化时第一次不执行，后面填写每次都执行)
var executeCount = 0;
function openYxzTips(value) {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  if (swjgDm.startsWith('132')) {
    if (executeCount > 0) {
      layer.open({
        title: '提示',
        content:
          '<p>该行填报根据《财政部 税务总局关于<span style="color:red">永续债</span>企业所得税政策问题的公告》（2019年第64号）等相关税收政策规定，该行填报居民企业取得的可以适用企业所得税法规定的居民企业之间的股息、红利等权益性投资收益免征企业所得税规定的<span style="color:red">永续债</span>利息收入。</p>',
      });
    }
    executeCount = executeCount + 1;
  }
}

function ydyjskCx() {
  var pathName = document.location.pathname;
  var index = pathName.substr(1).indexOf('/');
  var result = pathName.substr(0, index + 1);
  var url = location.protocol + '//' + location.host + result + '/sb/html.do?redirect_uri=qysdsydyjskcx';
  window.open(url);
}

/*** 是否为申报初始化 ***/
var gzmap = {};
function checkcsh(key) {
  if (!gzmap.hasOwnProperty(key)) {
    gzmap[key] = false;
  }
  if (!gzmap[key]) {
    gzmap[key] = true;
    return true;
  }
  return false;
}

/*** 减免所得税优惠事项，当不选中时，把金额设置为0 ***/
function setJmsdsyhsxBnlj(isSelect, index) {
  var bnlj = 0;
  if (isSelect) {
    bnlj = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[index].yhjmje;
  }
  return bnlj;
}
// 缓存获取叠加优惠规则，避免多次生成
// #revise 重构缓存
var djyhsxRulesStorage;
/**
 * 减免所得税弹窗点击“确定”按钮时，生成校验提示
 * @param resultType 'tips'|'result' 需要要获取提示语还是判断结果，默认是判断结果
 * @returns {string|boolean} 校验提示|校验结果
 */
function jyJmsdsyhsxSftg(resultType) {
  //重新设置序号
  var sdjmyhsxSfyszxh = formData.fq_.sdjmyhsxSfyszxh;
  if ('Y' != sdjmyhsxSfyszxh) {
    sdjmyhsxSetXh();
  }
  // 返回的校验提示
	var tipsMsg = "";
  // 返回的校验提示
  var flag = true;
	// 13行减免优惠数组
	var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
	// 记录已选择行的优惠税务事项代码
	var yhswsxSelectedList = new Array();
	// *1 优先阻断的提示、原/新政策是否勾选
	if (jmsdGridlb != null && jmsdGridlb != undefined) {
		// 小型微利企业
		var sfsyxxwlqy = formData.ht_.ywbw.A200000Ywbd.sbxx.sfsyxxwlqy;
		// 符合小微 且 没选小微减免 且 第10行实际利润额 > 0 时，需要额外校验
		var shouldVerifyXxwlqy =  "Y" == sfsyxxwlqy && jmsdGridlb[0].t_jmsdssxSelect == false && formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj > 0;
		// 是否选择了其他优惠事项
		var qtyhIsSelect = false;
		// Y01001 选项 '0,1' 原/新政策
		var xszc = formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].xszc;
		for (var i = 0, len = jmsdGridlb.length; i < len; i++) {
			var t_jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
			if (t_jmsdssxSelect) {
				// 优惠税务事项代码
				var yhswsx = jmsdGridlb[i].yhswsx;
				// 会被后面逻辑覆盖的低优先级提示语：判断是否符合新政策和原政策
				// 所属类别标识
				var jmsdssxlx = jmsdGridlb[i].t_jmsdssxlx;
				if ("yyrjjcdlyhjxzx" == jmsdssxlx && xszc.indexOf("0") == -1) {
          flag = false;
					tipsMsg = "主表[附报事项]中Y01001选择“原政策” 时 ，才能勾选或填写" + yhswsx + "！";
				} else if (("JMSE00302B" == yhswsx || "JMSE00303B" == yhswsx || "JMSE00304B" == yhswsx || "JMSE00306B" == yhswsx || "JMSE00307B" == yhswsx || "JMSE00309B" == yhswsx || "JMSE00301B" == yhswsx || "JMSE00305B" == yhswsx || "JMSE00310B" == yhswsx) && xszc.indexOf("1") == -1) {
          flag = false;
					tipsMsg = "主表[附报事项]中Y01001选择“新政策” 时 ，才能勾选或填写" + yhswsx + "！";
				}

				// 后续逻辑处理
				(shouldVerifyXxwlqy && i != 0) && (qtyhIsSelect = true);
				yhswsxSelectedList.push(yhswsx);
			}
		}
		if (shouldVerifyXxwlqy && !qtyhIsSelect) return resultType === "tips" ? "该企业符合小型微利企业优惠政策条件，在不享受其他优惠情况下，不得放弃享受小微优惠。[减免所得税类型选项表]第1行优惠为必选!" : false;
	}

	// *2 是否不符合叠加享受优惠
	/**
	 * 生成当前的叠加优惠事项规则
	 * @param {string} skssqq 税款所属期起
	 * @param {string} skssqz 税款所属期止
	 * @param {string|int} sbqylx 申报企业类型
	 * @returns {object} 叠加享受优惠规则 { dm1: { dmA: true, dmB: true ,...} ,... }
	 */
	var getDjyhsxRules = function(skssqq, skssqz, sbqylx) {
		if (djyhsxRulesStorage !== undefined && djyhsxRulesStorage["" + skssqq + "_" + skssqz + "_" + sbqylx]) {
			// 取缓存规则
			return djyhsxRulesStorage["" + skssqq + "_" + skssqz + "_" + sbqylx] || {};
		} else {
			// 创建规则
			// 维护下方 rules 时只需要按叠加需求表中二维表进行逐行配置（不需要包含自身）即可，要注意码值可能因属期不同产生差异性
			var rules = {};
			var skssqqDate = new Date(skssqq);
			var skssqzDate = new Date(skssqz);
			// 纳税人是否为跨地区经营汇总纳税企业总机构
			var isZjg = sbqylx == "1";
			if (skssqqDate >= new Date('2023-01-01')) {
				// 2023 年版
				rules = {
					"JMSE00201": ["JMSE00202"],
					"JMSE00202": ["JMSE00201"],
        };
        if (isZjg) {
          rules["JMSE00202"].push("JMSE00601");
          rules["JMSE00601"] = ["JMSE00202", "JMSE006037", "JMSE00608", "JMSE00609", "JMSE00610", "JMSE00611", "JMSE00606"];
          rules["JMSE006037"] = ["JMSE00601", "JMSE00608", "JMSE00609", "JMSE00610", "JMSE00611", "JMSE00606"];
          rules["JMSE00608"] = ["JMSE00601", "JMSE006037", "JMSE00609", "JMSE00610", "JMSE00611", "JMSE00606"];
          rules["JMSE00609"] = ["JMSE00601", "JMSE006037", "JMSE00608", "JMSE00610", "JMSE00611", "JMSE00606"];
          rules["JMSE00610"] = ["JMSE00601", "JMSE006037", "JMSE00608", "JMSE00609", "JMSE00611", "JMSE00606"];
          rules["JMSE00611"] = ["JMSE00601", "JMSE006037", "JMSE00608", "JMSE00609", "JMSE00610", "JMSE00606"];
          rules["JMSE00606"] = ["JMSE00601", "JMSE006037", "JMSE00608", "JMSE00609", "JMSE00610", "JMSE00611"];
        }
			} else {
				// 2023 年以前版本
				rules = {
					"JMSE00201": ["JMSE00202"],
					"JMSE00202": ["JMSE00201", "JMSE00601"],
					"JMSE00301A": ["JMSE00601"],
					"JMSE00302A": ["JMSE00601"],
					"JMSE00303A": ["JMSE00601"],
					"JMSE00304A": ["JMSE00601"],
					"JMSE00305A": ["JMSE00601"],
					"JMSE00306A": ["JMSE00601"],
					"JMSE00307A": ["JMSE00601"],
					"JMSE00308A": ["JMSE00601"],
					"JMSE00309A": ["JMSE00601"],
					"JMSE00302B": ["JMSE00601"],
					"JMSE00303B": ["JMSE00601"],
					"JMSE00304B": ["JMSE00601"],
					"JMSE00306B": ["JMSE00601"],
					"JMSE00307B": ["JMSE00601"],
					"JMSE00308B": ["JMSE00601"],
					"JMSE00309B": ["JMSE00601"],
					"JMSE00501": ["JMSE00601"],
					"JMSE00601": ["JMSE00202",	"JMSE00301A",	"JMSE00302A",	"JMSE00303A",	"JMSE00304A",	"JMSE00305A",	"JMSE00306A",	"JMSE00307A",	"JMSE00308A",	"JMSE00309A", "JMSE00302B",	"JMSE00303B",	"JMSE00304B", "JMSE00306B",	"JMSE00307B",	"JMSE00308B",	"JMSE00309B", "JMSE00501"],
				};
				if (skssqqDate < new Date('2022-01-01')) {
          if (isZjg) {
            rules["JMSE00601"].push("JMSE00604", "JMSE00606");
            rules["JMSE00604"].push("JMSE00601", "JMSE00606");
            rules["JMSE00606"].push("JMSE00601", "JMSE00604");
          }
				} else {
					// 失效 JMSE00306A、JMSE00307A
					delete rules["JMSE00306A"];
					delete rules["JMSE00307A"];
					rules["JMSE00601"] = rules["JMSE00601"].filter(function(item) {
						return item != "JMSE00306A" || item != "JMSE00307A";
					})
					if (isZjg) {
						rules["JMSE00601"].push("JMSE00607", "JMSE00608", "JMSE00609", "JMSE00606");
						rules["JMSE00607"] = ["JMSE00601", "JMSE00608", "JMSE00609", "JMSE00606"];
						rules["JMSE00608"] = ["JMSE00601", "JMSE00607", "JMSE00609", "JMSE00606"];
						rules["JMSE00609"] = ["JMSE00601", "JMSE00607", "JMSE00608", "JMSE00606"];
						rules["JMSE00606"] = ["JMSE00601", "JMSE00607", "JMSE00608", "JMSE00609"];
						if (skssqzDate >= new Date('2022-09-30')) {
							// 新增 JMSE00610 南沙
							rules["JMSE00610"] = ["JMSE00601", "JMSE00607", "JMSE00608", "JMSE00609", "JMSE00606"];
							rules["JMSE00601"].push("JMSE00610");
							rules["JMSE00607"].push("JMSE00610");
							rules["JMSE00608"].push("JMSE00610");
							rules["JMSE00609"].push("JMSE00610");
							rules["JMSE00606"].push("JMSE00610");
						}
					}
				}
			}
			// 将规则统一转换为对象再输出，提高后续规则库匹配的性能
			var rulesObj = {};
			var keys = Object.keys(rules);
			keys.forEach(function(key) {
				var value = rules[key];
				rulesObj[key] = value.reduce(function(prev, cur) {
					prev[cur] = true;
					return prev;
				}, {})
			})
			// 缓存，避免频繁计算生成规则
      if (djyhsxRulesStorage === undefined) djyhsxRulesStorage = {};
			djyhsxRulesStorage["" + skssqq + "_" + skssqz + "_" + sbqylx] = rulesObj;
			return rulesObj;
		}
	};
	// 根据已选优惠事项，逐一判断是否符合叠加优惠享受
	var isYhswsxValid = function(rules, yhswsxList) {
		var len = yhswsxList.length;
		for (var i = 0; i < len; i++) {
			var yhswsx = yhswsxList[i];
			if (rules.hasOwnProperty(yhswsx)) {
				var yhswsxRules = rules[yhswsx];
				for (var j = 0; j < len; j++) {
					// 二次遍历已选数组，除去本身以外在规则配置外，则不符合叠加享受
					if (i !== j && !yhswsxRules[yhswsxList[j]]) return false;
				}
			} else {
				// 如果数字没有在规则中定义，完全不兼容其他优惠事项
				if (yhswsxList.length > 1) return false;
			}
		}
		return true;
	}

	// 企业类型
	var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	// 所属期起止
	var skssqq = formData.fq_.sssq.sqQ;
	var skssqz = formData.fq_.sssq.sqZ;
	// 通过不享受叠加优惠的校验
	var djxsyhFlag = isYhswsxValid(getDjyhsxRules(
		skssqq,
		skssqz,
		sbqylx,
	), yhswsxSelectedList);
	// 根据属期生成不同的不享受叠加提示语
	if (!djxsyhFlag) {
    flag = false;
		var skssqqDate = new Date(skssqq);
		var skssqzDate = new Date(skssqz);
		var date1st = new Date('2022-01-01');
		var date2nd = new Date('2022-09-30');
		var date3rd = new Date('2023-01-01');
		var date4th = new Date('2024-01-01');
		var nsrsbh = parent.formData.fq_.nsrjbxx.nsrsbh;
		// 冬奥
		var isDongAo = nsrsbh == '12100000MB0111288J' || nsrsbh == '12100000MB1A69033G';
		// 是总机构
		var isZjg = sbqylx == "1";
		if (skssqqDate < date1st) {
			if (isZjg) {
				tipsMsg = "[减免所得税类型选项表]第1行至" + (isDongAo ? 36 : 35) + "行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，29行与32行、34行可以同时允许叠加享受，但不能享受其他优惠。"
			} else {
				tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第29行与（3-12行、14-16行、18-21行、25行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！";
			}
		} else if (skssqzDate < date2nd) {
			if (isZjg) {
				tipsMsg = "[减免所得税类型选项表]第1行至" + (isDongAo ? 36 : 35) + "行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与31行、32行、33行、34行可以同时允许叠加享受，但不能享受其他优惠。";
			} else {
				tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！";
			}
		} else if (skssqqDate < date3rd) {
			if (isZjg) {
				tipsMsg = "[减免所得税类型选项表]第1行至" + (isDongAo ? 37 : 36) + "行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，27行与31行、32行、33行、34行、35行可以同时允许叠加享受，但不能享受其他优惠。";
			} else {
				tipsMsg = "[减免所得税类型选项表]第1行至36行中，只有第2行与第3行，第27行与（3-10行、12-14行、16-19行、23行）其中一行能叠加享受减免所得税优惠，请准确选择优惠项目！";
			}
		} else if (skssqqDate < date4th) {
			if (isZjg) {
				tipsMsg = "[减免所得税类型选项表]第1行至" + (isDongAo ? 38 : 37) + "行中，只有第2行与第3行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，第27行与第3行、第31-36行可以同时允许叠加享受，但不能享受其他优惠。 "
			} else {
				tipsMsg = "[减免所得税类型选项表]第1行至37行中，只有第2行与第3行能叠加享受减免所得税优惠，请准确选择优惠项目！"
			}
		} else {
			if (isZjg) {
				tipsMsg = "[减免所得税类型选项表]第1行至" + (isDongAo ? 35 : 34) + "行中，只有第2行与第3行，允许叠加享受减免所得税优惠，请准确选择优惠项目，若您是总分机构纳税人，第24行与第3行、第28-33行可以同时允许叠加享受，但不能享受其他优惠。 "
			} else {
				tipsMsg = "[减免所得税类型选项表]第1行至34行中，只有第2行与第3行能叠加享受减免所得税优惠，请准确选择优惠项目！"
			}
		}
	}
	return resultType === "tips" ? tipsMsg : flag;
}

/**
 * 减免所得税类型选项表第29行计算公式
 * 1.企业类型为一般企业的，不可修改，主表12行*40%；
 若同时选择其他优惠，（主表12行-选中的其他项目）*40%
 2.企业类型为“跨地区经营汇总纳税企业总机构”，
 且总机构和分支机构至少存在一个属于西部大开发地区的，
 即内蒙古自治区、广西壮族自治区、重庆市、四川省、
 贵州省、云南省、西藏自治区、陕西省、甘肃省、
 青海省、宁夏回族自治区、新疆维吾尔自治区和新疆生产建设兵团。
 湖南省湘西土家族苗族自治州、湖北省恩施土家族苗族自治州、
 吉林省延边朝鲜族自治州和江西省赣州市，本行次可以录入。
 * */
function getJMSE00601(jmsdssxSelect, sbqylx, ynsdseLj) {
  if (!jmsdssxSelect) {
    //没有选中，返回0
    return 0.0;
  }
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  //厦门个性化一般企业不计算
  if ('Y' == qygxhXmBz && '0' == sbqylx) {
    return 0.0;
  }
  var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
  if ('0' == sbqylx) {
    //一般企业
    //获取其他项加起来的金额
    var qtyhBnljje = 0;
    for (var i = 0; i < jmsdGridlb.length; i++) {
      if ('JMSE00601' != jmsdGridlb[i].yhswsx && jmsdGridlb[i].t_jmsdssxSelect == true) {
        qtyhBnljje = qtyhBnljje + jmsdGridlb[i].yhjmje;
      }
    }
    var bnljje = ROUND((ynsdseLj - qtyhBnljje) * 0.4, 2);
    return bnljje;
  } else if ('1' == sbqylx) {
    //总机构企业，返回当前各自的金额，总机构企业自动填写
    var bnljje = jmsdGridlb[28].yhjmje;
    return bnljje;
  }
  return 0.0;
}

/**
 * 判断分支机构是否属于西部大开发地区
 * */
function getFzzgSfsyXbdkf() {
  // 企业类型为一般企业的，并且税务机关为陕西（161）、青海（163）、贵州（152），允许勾选，等于主表12行*40%，不可修改；
  // 若同时选择其他优惠，（主表12行-选中的其他项目）*40%；其他税务机关，锁定不能勾选；
  var sfsyXbdkfdq = false;
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  if (sbqylx == 0) {
    var zgswskfjDm = formData.fq_.nsrjbxx.zgswskfjDm.substring(0, 3);
    if (zgswskfjDm == '161' || zgswskfjDm == '163' || zgswskfjDm == '152') {
      sfsyXbdkfdq = true;
    }
  }
  //是否属于西部大开发地区
  /*var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
	if(sbqylx == 0){
		sfsyXbdkfdq = false;
	} else if(sbqylx == 1) {
		var fzjgxxGridlb = null;
		var fzjgxxGrid = formData.hq_.fzjgxxGrid;
		if(fzjgxxGrid!=null && fzjgxxGrid!=undefined){
			fzjgxxGridlb = fzjgxxGrid.fzjgxxGridlb;
		}
		//满足分支机构的税务机关代码的前3位
		var xgdkfSwjgq3 = ['115','145','150','152','153','154','161','162','163','164','165'];
		var xgdkfSwjgq5 = ['15102','14331','14228','12224','13607'];
		var swjgq3 = "";
		var swjgq5 = "";
		if(fzjgxxGridlb!=null && fzjgxxGridlb!=undefined && fzjgxxGridlb.length>0){
			for(var i=0;i<fzjgxxGridlb.length;i++){
				if(fzjgxxGridlb[i].zgswjgDm!=null && fzjgxxGridlb[i].zgswjgDm!=undefined && fzjgxxGridlb[i].zgswjgDm!=""){
					swjgq3 = fzjgxxGridlb[i].zgswjgDm.substring(0,3);
					swjgq5 = fzjgxxGridlb[i].zgswjgDm.substring(0,5);
				}
				if(xgdkfSwjgq3.indexOf(swjgq3)>-1){
					sfsyXbdkfdq = true;
				}
				if(xgdkfSwjgq5.indexOf(swjgq5)>-1){
					sfsyXbdkfdq = true;
				}
				if((swjgq3=="151") && (swjgq5!="15102")){
					sfsyXbdkfdq = true;
				}
			}
		}
	}*/
  return sfsyXbdkfdq;
}

/**
 * 判断分支机构是否存在一个海南的企业
 * */
function getFzzgSfyhndq() {
  var sfyhndq = 'N';
  var fzjgxxGridlb = null;
  var fzjgxxGrid = formData.hq_.fzjgxxGrid;
  if (fzjgxxGrid != null && fzjgxxGrid != undefined) {
    fzjgxxGridlb = fzjgxxGrid.fzjgxxGridlb;
  }
  if (fzjgxxGridlb != null && fzjgxxGridlb != undefined && fzjgxxGridlb.length > 0) {
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      if (
        fzjgxxGridlb[i].fzjgnsrsbh != null &&
        fzjgxxGridlb[i].fzjgnsrsbh != undefined &&
        fzjgxxGridlb[i].fzjgnsrsbh != ''
      ) {
        if (fzjgxxGridlb[i].fzjgnsrsbh.toString().indexOf('9146') == 0) {
          sfyhndq = 'Y';
        }
      }
      if (fzjgxxGridlb[i].zgswjgmc != null && fzjgxxGridlb[i].zgswjgmc != undefined && fzjgxxGridlb[i].zgswjgmc != '') {
        if (fzjgxxGridlb[i].zgswjgmc.toString().indexOf('海南省') > -1) {
          sfyhndq = 'Y';
        }
      }
      if (fzjgxxGridlb[i].zgswjgDm != null && fzjgxxGridlb[i].zgswjgDm != undefined && fzjgxxGridlb[i].zgswjgDm != '') {
        if (startWidthStr(fzjgxxGridlb[i].zgswjgDm, '146') || startWidthStr(fzjgxxGridlb[i].zgswjgDm, '246')) {
          sfyhndq = 'Y';
        }
      }
      // 等于Y提前退出
      if (sfyhndq == 'Y') {
        return sfyhndq;
      }
    }
  }
  return sfyhndq;
}

/*
 * 北京个性化
 */
function getQycyrsQnpjrsTip(qycyrsQnpjrs) {
  var bjSwjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 3);
  if (bjSwjgDm == '111' && isNotEmptyObj(qycyrsQnpjrs) && qycyrsQnpjrs > 0) {
    var tipsMsg = '';
    if (qycyrsQnpjrs > 10000) {
      tipsMsg =
        '尊敬的纳税人，您填报的本纳税年度截至本期末的从业人数季度平均值超过10000人，请您再次确认从业人数填报数值是否准确！';
    } else if (qycyrsQnpjrs > 300 && qycyrsQnpjrs < 10000) {
      tipsMsg =
        '尊敬的纳税人，您填报的本纳税年度截至本期末的从业人数季度平均值超过300，不符合小型微利企业条件，请您再次确认从业人数填报是否准确！';
    }
    if (isNotEmptyObj(tipsMsg)) {
      parent.layer.confirm(
        tipsMsg,
        {
          title: '提示',
          icon: 6,
          btn: ['阅读完毕'],
        },
        function (index) {
          parent.layer.close(index);
        },
      );
    }
  }
}

/*
 * 北京个性化
 */
function getZczeQnpjsTip(zczeQnpjs) {
  var bjSwjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 3);
  if (bjSwjgDm == '111' && isNotEmptyObj(zczeQnpjs) && zczeQnpjs > 0) {
    var tipsMsg = '';
    if (zczeQnpjs > 5000) {
      tipsMsg =
        '尊敬的纳税人，本栏次填报的单位为万元，您填报的本纳税年度截至本期末的资产总额季度平均值超过5000万元，不符合小型微利企业条件，请您再次确认资产总额填报金额是否准确！';
    }
    if (isNotEmptyObj(tipsMsg)) {
      parent.layer.confirm(
        tipsMsg,
        {
          title: '提示',
          icon: 6,
          btn: ['阅读完毕'],
        },
        function (index) {
          parent.layer.close(index);
        },
      );
    }
  }
}

/*
 * 北京个性化
 */
function getBjgxhTip(value) {
  var bjSwjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 3);
  var plsbbz = parent.location.href.indexOf('isPlLsb') > -1 || isJmsb();
  if (bjSwjgDm == '111' && isNotEmptyObj(value) && value > 0 && !plsbbz) {
    parent.layer.confirm(
      '尊敬的纳税人，该行次多用于应急填报国务院新出台的政策，一般情况不得随意填报，请核实您享受的优惠政策，并将优惠金额填报到正确的行次！',
      {
        title: '提示',
        icon: 6,
        btn: ['阅读完毕'],
      },
      function (index) {
        parent.layer.close(index);
      },
    );
  }
}

function beforeHtmlLoadAction() {
  /**
   * XMDZSWJ-2177 申报查询及打印，无法查看到第主表第9行的数据
   * 全渠道查询打印的时候不执行这个方法
   * 由于该方法中的formData.ht_.ywbw.A200000Ywbd.sbxx.mbyqndksLj = 0;
   * 会导致 mbyqndksLj 这个节点一直赋值的是0，而不是数据库中的数据。
   */
  var qqdcxReturnResult = formData.kz_.qqdcxReturnResult;
  if (qqdcxReturnResult !== undefined) {
    return;
  }
  if (typeof formData.hq_.qtxx.mbksehj === 'undefined') {
    formData.ht_.ywbw.A200000Ywbd.sbxx.mbyqndksLj = 0;
  }

  if (formData.fq_.nsrjbxx.swjgDm.substring(0, 5) == '13502') {
    $('.areaHeadBtn', parent.document).prepend(
      '<li><a id="btnBackForm" class="btn btn06" style="" onclick="javascript:window.frames[0].backForm();">上一步</a></li>',
    );
    if (formData.fq_.zfjglxDm == '1' && (formData.fq_.kdqsszyDm == '3' || formData.fq_.kdqsszyDm == '4')) {
      parent.layer.open({
        title: ['提示'],
        area: ['30%', '32%'],
        shade: 0.4,
        type: 1,
        content: '跨地区汇总备案类型错误，请至办税大厅修改。',
        btn: ['确定'],
        scrollbar: false, //屏蔽浏览器滚动条
        yes: function (lb) {
          //直接关闭当前浏览器窗口
          CloseWebPage();
        },
        cancel: function () {
          //直接关闭当前浏览器窗口
          CloseWebPage();
        },
      });
      return;
    }

    if (
      formData.fq_.zfjglxDm == '2' &&
      (formData.fq_.kdqsszyDm == '3' || formData.fq_.kdqsszyDm == '4') &&
      formData.fq_.jdjnbs === 'Y'
    ) {
      parent.layer.open({
        title: ['提示'],
        area: ['30%', '32%'],
        shade: 0.4,
        type: 1,
        content: '跨地区汇总备案类型错误，请至办税大厅修改。',
        btn: ['确定'],
        scrollbar: false, //屏蔽浏览器滚动条
        yes: function (lb) {
          //直接关闭当前浏览器窗口
          CloseWebPage();
        },
        cancel: function () {
          //直接关闭当前浏览器窗口
          CloseWebPage();
        },
      });
      return;
    }

    if (
      !(
        formData.ht_.ywbw.A200000Ywbd.nsrxx.yjfs == '1' &&
        (formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx == '1' || formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx == '0')
      )
    ) {
      $('#btnCbsj', parent.document).hide();
      return;
    }
  }
}

function xmqylxjy6(sbqylx) {
  if (
    formData.fq_.nsrjbxx.swjgDm.substring(0, 5) == '13502' &&
    formData.kz_.temp.gzsbbz != 'Y' &&
    sbqylx == '1' &&
    formData.fq_.snsbsfsyxxwlqy == 'Y' &&
    formData.fq_.snsbsbqylx == '1'
  ) {
    var snsbXgrq = formData.fq_.snsbXgrq;
    if (!isNull(snsbXgrq)) {
      snsbXgrq = snsbXgrq.substring(0, 10);
    }

    var snnbxgrqDate = new Date(snsbXgrq);
    var snbsfsb = formData.fq_.snbsfsb;
    var nowData = new Date();
    var msg = '';
    if (snbsfsb == 'N') {
      msg =
        '根据您上一纳税年度填报的数据，您上年度为小型微利企业，本年度不应对分支机构分配税款，请到主管税务机关变更汇总纳税企业情况登记信息，建议您变更后再进行申报。';
    }

    if (snbsfsb == 'Y' && snnbxgrqDate < nowData) {
      msg =
        '根据您上一纳税年度填报的数据，您上年度为小型微利企业，本年度不应对分支机构分配税款，需到主管税务机关变更汇总纳税企业情况登记信息，请您变更后再进行申报。';
    }
    if (msg != '') {
      layer.open({
        title: ['提示'],
        area: ['448px', '245px'],
        shade: 0.4,
        icon: 6,
        type: 1,
        content: msg,
        btn: ['确定'],
        scrollbar: false, //屏蔽浏览器滚动条
        yes: function (index) {
          layer.close(index);
          var snsbXgrq = formData.fq_.snsbXgrq;
          if (!isNull(snsbXgrq)) {
            snsbXgrq = snsbXgrq.substring(0, 10);
          }
          var snnbxgrqDate = new Date(snsbXgrq);
          var nowData = new Date();
          var snbsfsb = formData.fq_.snbsfsb;
          if (snbsfsb == 'Y' && snnbxgrqDate < nowData) {
            closeWin();
          }
        },
        cancel: function () {
          layer.close(index);
          var snsbXgrq = formData.fq_.snsbXgrq;
          if (!isNull(snsbXgrq)) {
            snsbXgrq = snsbXgrq.substring(0, 10);
          }
          var snnbxgrqDate = new Date(snsbXgrq);
          var snbsfsb = formData.fq_.snbsfsb;
          var nowData = new Date();
          if (snbsfsb == 'Y' && snnbxgrqDate < nowData) {
            closeWin();
          }
        },
      });
    }
  }
}

function CloseWebPage() {
  var win = null;
  var dzswjTop;
  if (
    typeof parent.parent.dzswjTop != 'undefined' &&
    typeof parent.parent.dzswjTop.isOuterFrame != 'undefined' &&
    parent.parent.dzswjTop.isOuterFrame()
  ) {
    dzswjTop = parent.parent.dzswjTop;
  } else {
    dzswjTop = top;
  }
  if (typeof dzswjTop.isOuterFrame === 'function' && dzswjTop.isOuterFrame()) {
    //存在外框
    win = window;
    dzswjTop.postMessage({ method: 'taxCloseTab', data: { key: parent.ywbm.toLowerCase() } }, '*');
  } else {
    win = window.top;
  }
  if (navigator.userAgent.indexOf('MSIE') > 0) {
    if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
      window.opener = null;
      window.close();
    } else {
      window.open('', '_top');
      win.close();
    }
  } else if (navigator.userAgent.indexOf('Firefox') > 0) {
    window.location.href = 'about:blank ';
    window.close();
  } else if (navigator.userAgent.indexOf('Chrome') > 0 || (isIE() && navigator.userAgent.indexOf('Trident') > 0)) {
    win.open('about:blank ', '_self').close();
  } else {
    window.open('', '_top');
    win.close();
  }
}
//判断重要行业
function isZyqy() {
  var hyDm = formData.fq_.nsrjbxx.hyDm;
  var hyDm02 = hyDm.substring(0, 2);
  var hyDm03 = hyDm.substring(0, 3);

  // var zyhydm04arr = ['1310', '1320', '1340', '1440', '1810', '1820', '1830', '1910', '2110', '2120', '2130', '2140', '2320', '2330', '2450', '2681', '2682', '2683', '2684', '2689', '2710', '2720', '2730', '2740', '2750', '2770', '2760', '2921', '2922', '2923', '2924', '2925', '2926', '2927', '2928', '2929', '3340', '3360', '3490', '3610', '3620', '3630', '3640', '3650', '3660', '3720', '3770', '3940', '3990', '4030', '4090', '6330', '6410', '6420', '6490', '6510', '6520', '6530', '6540', '6550'];
  // var zyhydm03arr = ['133','135','136','137','139','141','142','143','145','146','149','171','172','173','174','175','176','177','178','192','193','194','195','201','202','203','204','221','222','223','231','241','242','243','244','268','281','282','292','331','332','333','335','337','338','339','341','342','343','344','345','346','347','348','351','352','353','354','355','356','357','358','359','371','373','374','375','376','379','381','382','383','384','385','386','387','389','391','392','393','395','396','397','401','402','404','631','632','659'];
  var zyhydm03arr = ['268', '292'];
  var zyhydm02arr = [
    '13',
    '14',
    '17',
    '18',
    '19',
    '20',
    '21',
    '22',
    '23',
    '24',
    '26',
    '27',
    '28',
    '33',
    '34',
    '35',
    '36',
    '37',
    '38',
    '39',
    '40',
    '63',
    '64',
    '65',
  ];
  // return zyhydm04arr.indexOf(hyDm) > -1 || zyhydm03arr.indexOf(hyDm03) > -1;
  return zyhydm02arr.indexOf(hyDm02) > -1 || zyhydm03arr.indexOf(hyDm03) > -1;
}

function setJmsdsyhsxSelect(sfsyxxwlqy) {
  if ('N' == sfsyxxwlqy) {
    if (formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect === false) {
      return;
    }
    //讲13.1行设置为不选中
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = false;
    var _jpath2 = 'ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect';
    formulaEngine.apply(_jpath2, '');
  } else {
    // 判断第一行是否选择,如果是小微企业，并且其他未选择，则第一行必选
    var dihSfxz = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect;
    var sjlreLj = formData.ht_.ywbw.A200000Ywbd.sbxx.sjlreLj; // 第10行

    if ('Y' == sfsyxxwlqy && dihSfxz == false) {
      //除第一行外其他行是否有选择优惠标志
      var qtyhIsSelect = false;
      var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
      for (var i = 0; i < jmsdGridlb.length; i++) {
        if (i != 0) {
          var t_jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
          if (t_jmsdssxSelect) {
            qtyhIsSelect = true;
            break;
          }
        }
      }
      if (!qtyhIsSelect) {
        if (formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect === true) {
          return;
        }
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect = true;
        var _jpath2 = 'ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[0].t_jmsdssxSelect';
        formulaEngine.apply(_jpath2, '');
      }
    }
  }
}

/**
 * 设置附报事项上期数信息
 */
function setFbsxSqxx() {
  if (
    isEmptyObj(formData.hq_.sqsbxx) ||
    isEmptyObj(formData.hq_.sqsbxx.fbsxGrid) ||
    isEmptyObj(formData.hq_.sqsbxx.fbsxGrid.fbsxGridlb) ||
    formData.hq_.sqsbxx.fbsxGrid.fbsxGridlb.length < 1
  ) {
    return;
  }
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  //当年第一期 和 sbqylx=2 的不取接口数据
  var sfdndiq = formData.fq_.sfdndiq;
  if ('Y' === sfdndiq || sbqylx == '2') {
    return;
  }
  var fbsxGridlb = formData.hq_.sqsbxx.fbsxGrid.fbsxGridlb;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var sbnd = parseInt(skssqz.split('-')[0], 10);
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  for (var i = 0; i < fbsxGridlb.length; i++) {
    var fbsxVo = fbsxGridlb[i];
    var ewbhgjz = fbsxVo.ewbhgjz;
    var xxbz = fbsxVo.xxbz;
    var jehxxz = fbsxVo.jehxxz;
    if ('Y' != xxbz || isEmptyObj(jehxxz)) {
      continue;
    }

    if ('K01001' === ewbhgjz && sbnd >= 2022) {
      formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqje = 0;
      var _jpath = 'ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqje';
      formulaEngine.apply(_jpath, '');
    } else if ('K01001' === ewbhgjz && sbnd < 2022) {
      formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqje = Number(jehxxz);
      var _jpath = 'ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqje';
      formulaEngine.apply(_jpath, '');
    } else if ('K01002' === ewbhgjz) {
      formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[1].t_sqje = Number(jehxxz);
      var _jpath = 'ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[1].t_sqje';
      formulaEngine.apply(_jpath, '');
    } else if ('Y01001' === ewbhgjz) {
      if (formData.kz_.temp.gzsbbz != 'Y') {
        formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_sqxszc = '' + jehxxz;
        var _jpath = 'ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[2].t_sqxszc';
        formulaEngine.apply(_jpath, '');
      }
    } else if ('Y01002' === ewbhgjz && sbnd == 2022 && yf_z > 10) {
      formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[3].t_sqje = jehxxz;
      var _jpath = 'ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[3].t_sqxszc';
      formulaEngine.apply(_jpath, '');
    }
  }
}

/**
 * 设置免税收入优惠事项上期数信息
 */
function setMssrsxSqs() {
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var xgrqDate = new Date('2021-09-30');
  var skssqzDate = new Date(skssqz);
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  if (skssqzDate >= xgrqDate && sbqylx != '2' && yf_z >= 10) {
    var dsjdhjyJmxx = formData.fq_.dsjdhjyJmxx;
    if (!isNull(dsjdhjyJmxx)) {
      var jmxxList = dsjdhjyJmxx.jmxx;
      var jjkc1je = 0;
      var jjkc1Bz = 'N';
      var jjkc2je = 0;
      var jjkc2Bz = 'N';
      var jjkc3je = 0;
      var jjkc3Bz = 'N';
      var jjkc4je = 0;
      var jjkc4Bz = 'N';
      var jjkc5je = 0;
      var jjkc5Bz = 'N';
      var jjkc6je = 0;
      var jjkc6Bz = 'N';
      var jjkc7je = 0;
      var jjkc7Bz = 'N';
      for (var i = 0; i < jmxxList.length; i++) {
        var yhswsx = jmxxList[i].yhswsx;
        var yhjmje = jmxxList[i].yhjmje;
        if (yhswsx == 'JJKC011') {
          jjkc1je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc1Bz = 'Y';
        }
        if (yhswsx == 'JJKC012') {
          jjkc2je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc2Bz = 'Y';
        }
        if (yhswsx == 'JJKC021') {
          jjkc3je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc3Bz = 'Y';
        }
        if (yhswsx == 'JJKC022') {
          jjkc4je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc4Bz = 'Y';
        }
        if (yhswsx == 'JJKC013') {
          jjkc5je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc5Bz = 'Y';
        }
        if (yhswsx == 'JJKC023') {
          jjkc6je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc6Bz = 'Y';
        }
        if (yhswsx == 'JJKC031') {
          jjkc7je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc7Bz = 'Y';
        }
      }
      var jpathList = [];
      if (jjkc1Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe = Number(jjkc1je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe');
      }

      if (jjkc2Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe = Number(jjkc2je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe');
      }

      if (jjkc3Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe = Number(jjkc3je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe');
      }

      if (jjkc4Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe = Number(jjkc4je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe');
      }

      if (jjkc5Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe = Number(jjkc5je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe');
      }

      if (jjkc6Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe = Number(jjkc6je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe');
      }

      if (jjkc7Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe = Number(jjkc7je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe');
      }

      if (jpathList.length > 0) {
        formulaEngine.apply4List(jpathList);
      }
    }
  }

  if (
    isEmptyObj(formData.hq_.sqsbxx) ||
    isEmptyObj(formData.hq_.sqsbxx.mssrGrid) ||
    isEmptyObj(formData.hq_.sqsbxx.mssrGrid.mssrGridlb) ||
    formData.hq_.sqsbxx.mssrGrid.mssrGridlb.length < 1
  ) {
    return;
  }
  //不是当年第一期才取接口数据
  var sfdndiq = formData.fq_.sfdndiq;
  if ('Y' === sfdndiq) {
    return;
  }
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  var fylBmdBz = formData.kz_.bmdBzxx.fylBmdBz;
  var hqGridlb = formData.hq_.sqsbxx.mssrGrid.mssrGridlb;
  var gridlb = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb;
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var qtjmxzDm = skssqz >= '2023-09-01' ? 'JJSR999' : 'JJSRQT';
  for (var i = 0; i < hqGridlb.length; i++) {
    var vo = hqGridlb[i];
    var yhswsx = vo.yhswsx;
    var yhjmje = vo.yhjmje;
    if (
      isEmptyObj(yhswsx) ||
      isEmptyObj(yhjmje) ||
      yhswsx == 'JJKC011' ||
      yhswsx == 'JJKC012' ||
      yhswsx == 'JJKC021' ||
      yhswsx == 'JJKC022'
    ) {
      continue;
    }
    for (var j = 0; j < gridlb.length; j++) {
      if (yhswsx === gridlb[j].ssjmxzDm || yhswsx === gridlb[j].yhswsx) {
        //其他优惠比较特殊，免税收入的其他和减计收入的其他是的减免性质代码是一样的,所以是其他优惠时，需要判断yhswsx是否一致
        if ('0004129999' == vo.ssjmxzDm) {
          if (yhswsx != gridlb[j].yhswsx) {
            continue;
          }
        }
        //厦门第7行其他和非营利设置为0
        if ('Y' == qygxhXmBz) {
          if ('0004129999' == vo.ssjmxzDm) {
            yhjmje = 0;
          } else if ('0004120601' == vo.ssjmxzDm && fylBmdBz != 'Y') {
            yhjmje = 0;
          }
        }
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[j].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[j].t_mssrQcsJe = Number(yhjmje);
        var _jpath = 'ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[' + j + '].t_mssrQcsJe';
        formulaEngine.apply(_jpath, yhjmje);
        break;
      }
    }
  }
}

/**
 * 设置所得减免优惠事项上期数信息
 */
function setSdjmsxSqs() {
  if (
    isEmptyObj(formData.hq_.sqsbxx) ||
    isEmptyObj(formData.hq_.sqsbxx.sdjmGrid) ||
    isEmptyObj(formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb) ||
    formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb.length < 1
  ) {
    return;
  }
  //不是当年第一期才取接口数据
  var sfdndiq = formData.fq_.sfdndiq;
  if ('Y' === sfdndiq) {
    return;
  }
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  var hqGridlb = formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb;
  var gridlb = formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb;
  for (var i = 0; i < hqGridlb.length; i++) {
    var vo = hqGridlb[i];
    var yhswsx = vo.yhswsx;
    var yhjmje = vo.yhjmje;
    if (isEmptyObj(yhswsx) || isEmptyObj(yhjmje)) {
      continue;
    }
    for (var j = 0; j < gridlb.length; j++) {
      if (yhswsx === gridlb[j].ssjmxzDm || yhswsx === gridlb[j].yhswsx) {
        //厦门第8行其他和设置为0
        if ('Y' == qygxhXmBz) {
          if ('0004129999' == vo.ssjmxzDm || '0004021204' == vo.ssjmxzDm) {
            yhjmje = 0;
          }
        }
        formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[j].t_sdjmSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[j].t_sdjmsxQcsJe = Number(yhjmje);
        formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[j].t_sdjmsxSelect = true;
        var _jpath = 'ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[' + j + '].t_sdjmsxQcsJe';
        formulaEngine.apply(_jpath, yhjmje);
        break;
      }
    }
  }
}

var backForm = function () {
  parent.window.location.reload();
};

/*
 * 提交申报前的校验
 */
function doAfterVerify(callBeforSubmitForm, callSubmitForm, params) {
  // 北京个性化
  var bjSwjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 3);
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  var yjfs = formData.ht_.ywbw.A200000Ywbd.nsrxx.yjfs;
  if (bjSwjgDm == '111' && (sbqylx == 0 || sbqylx == 1) && yjfs == '1') {
    var tipMsg = '';
    var xh = 1;
    //JJSR030综合利用资源生产产品取得的收入在计算应纳税所得额时减计收入的本年累计金额
    var JJSR030 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[16].yhjmje;
    //营业收入_本年累计金额
    var yysrLj = formData.ht_.ywbw.A200000Ywbd.sbxx.yysrLj;
    if (JJSR030 > yysrLj * 0.1) {
      tipMsg =
        '尊敬的纳税人：<br/>' +
        xh +
        '、A200000《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”为：' +
        yysrLj +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、第7.13行“综合利用资源生产产品取得的收入在计算应纳税所得额时减计收入本年累计金额”为：' +
        JJSR030 +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000《企业所得税月(季)度预缴纳税申报表(A)》第7.13行“综合利用资源生产产品取得的收入在计算应纳税所得额时减计收入本年累计金额”大于A200000《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入本年累计金额”*10%，请核实申报数据填报是否有误。<br/>';
    }
    var JJSR041 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[17].yhjmje; //"JJSR041"
    if (JJSR041 > yysrLj * 0.1) {
      if (isNotEmptyObj(tipMsg)) {
        xh++;
      }
      tipMsg =
        tipMsg +
        xh +
        '、A200000《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”为：' +
        yysrLj +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》中涉及优惠代码：JJSR041：“金融机构取得的涉农贷款利息收入在计算应纳税所得额时减计收入在计算应纳税所得额时减计收入_本年累计金额”为' +
        JJSR041 +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》涉及优惠代码：JJSR041：“金融机构取得的涉农贷款利息收入在计算应纳税所得额时减计收入大于《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”*10%，请核实。<br/>';
    }
    var JJSR042 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[18].yhjmje; //"JJSR042"
    if (JJSR042 > yysrLj * 0.1) {
      if (isNotEmptyObj(tipMsg)) {
        xh++;
      }
      tipMsg =
        tipMsg +
        xh +
        '、A200000《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”为：' +
        yysrLj +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》中涉及优惠代码：JJSR042：“保险机构取得的涉农保费收入在计算应纳税所得额时减计收入在计算应纳税所得额时减计收入_本年累计金额”为' +
        JJSR042 +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》涉及优惠代码：JJSR042：“保险机构取得的涉农保费收入在计算应纳税所得额时减计收入大于《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”*10%，请核实。<br/>';
    }
    var JJSR043 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[21].yhjmje; //"JJSR043"
    if (JJSR043 > yysrLj * 0.1) {
      if (isNotEmptyObj(tipMsg)) {
        xh++;
      }
      tipMsg =
        tipMsg +
        xh +
        '、A200000《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”为：' +
        yysrLj +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》中涉及优惠代码：JJSR043：“小额贷款公司取得的农户小额贷款利息收入在计算应纳税所得额时减计收入_本年累计金额”为' +
        JJSR043 +
        '元；<br/>';
      xh++;
      tipMsg =
        tipMsg +
        xh +
        '、A200000第7行中《免税、减计收入、加计扣除类型选项表》涉及优惠代码：JJSR043：“小额贷款公司取得的农户小额贷款利息收入在计算应纳税所得额时减计收入大于《企业所得税月(季)度预缴纳税申报表(A)》第1行“营业收入_本年累计金额”*10%，请核实。<br/>';
    }
    if (isNotEmptyObj(tipMsg)) {
      // 弹框要有两个按钮
      parent.layer.confirm(
        tipMsg,
        {
          title: '申报校验',
          area: ['800px', '400px'],
          cancel: function (index) {
            return false;
          },
          icon: 6,
          btn: ['确认申报', '返回修改'],
          btn2: function (index) {
            parent.layer.close(index);
            $('body').unmask();
            prepareMakeFlag = true;
            return;
          },
        },
        function (index) {
          parent.layer.close(index);
          // 执行回调函数
          doBeforSubmitFormSdsA21yjd(callBeforSubmitForm, submitForm, params);
        },
      );
    } else {
      // 执行回调函数
      doBeforSubmitFormSdsA21yjd(callBeforSubmitForm, submitForm, params);
    }
  } else {
    // 执行回调函数
    doBeforSubmitFormSdsA21yjd(callBeforSubmitForm, submitForm, params);
  }
}

/**
 * 提交前事件，校验提示
 * @param submitForm
 * @param isSecondCall
 */
function doBeforSubmitFormSdsA21yjd(callBeforSubmitForm, submitForm, isSecondCall) {
  doBeforSubmitFormSdsA21yjdDeliver(callBeforSubmitForm, submitForm, isSecondCall);
}

function doBeforSubmitFormSdsA21yjdDeliver(callBeforSubmitForm, submitForm, isSecondCall) {
  callBeforSubmitForm(submitForm, isSecondCall);
}

function lineNum(xh) {
  var x = xh.indexOf('[');
  var y = xh.indexOf(']');
  var line = xh.substr(x + 1, y - x - 1);
  return parseInt(line) + 1;
}

/**
 * 厦门个性化：
 * param:resType 返回类型，要获取判断结果还是提示语
 */
function bmdSfxsJmsdYh(sjlreLj, jmsdseLj, resType) {
  var flag = true;
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  if (sjlreLj < 0 || 'Y' != qygxhXmBz) {
    return flag;
  }
  var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
  var jmsdssxSelect = false;
  if (!isEmptyObject(jmsdGridlb)) {
    for (var i = 0; i < jmsdGridlb.length; i++) {
      jmsdssxSelect = jmsdGridlb[i].t_jmsdssxSelect;
      if (jmsdssxSelect) {
        break;
      }
    }
  }
  var sfbmdYhtsy = '';
  if (jmsdssxSelect == false && sjlreLj > 0) {
    var gxjsBmdBz = formData.kz_.bmdBzxx.gxjsBmdBz;
    var sfbmdYhtsy = '';
    if ('Y' == gxjsBmdBz) {
      sfbmdYhtsy = '您是高新技术企业白名单内，当前未享受任何税收优惠，请确认！';
    }
    if (!isEmptyObject(sfbmdYhtsy)) {
      flag = false;
    }
  }
  if (flag == false && 'tsy' == resType) {
    return sfbmdYhtsy;
  }
  return flag;
}

function disableFpblfbse(swjgDm, nsrsbh, fzjglxlb) {
  var dqdm = swjgDm.substring(0, 5);
  if (dqdm != '13502') {
    return false;
  }

  var fzjgxxList = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
  var bz = '';
  if (fzjgxxList != null && fzjgxxList != undefined) {
    for (var i = 0; i < fzjgxxList.length; i++) {
      if (fzjgxxList.length - 1 == i) {
        bz = fzjgxxList[i].nsrsbh + '_' + fzjgxxList[i].fzjglxlb;
      }
    }
  }

  var jrz = nsrsbh + '_' + fzjglxlb;
  if (bz === jrz) {
    return false;
  }
  return true;
}

/**
 * 厦门个性化公式：清除规则不一致节点
 * */
function xmgxhQcgzbyzJd() {
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  if ('Y' != qygxhXmBz) {
    return;
  }
  var sfdndiq = formData.fq_.sfdndiq;
  var gzbz = formData.kz_.temp.gzsbbz;
  if ('Y' != gzbz && 'zx' != gzbz && 'N' == sfdndiq) {
    xmgxhQcgzbyzJdTs();
  }
}

/**
 * 过滤外部初始化要素
 * @param wbcsh 外部初始化
 */
function filterWbcsh(wbcsh) {
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  var skssqq = formData.fq_.sssq.sqQ;
  var skssqqDate = new Date(skssqq);
  var hbgDate = new Date('2022-01-01');

  var filterKeys = [];
  //第13行弹框行序号由公式执行,不用wbcsh的覆盖,因为覆盖后没有执行到公式,可能有BUG,所以把wbchs的删除
  if ('Y' == qygxhXmBz && skssqqDate >= hbgDate) {
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[7].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[3].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[4].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[5].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[6].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[11].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[21].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[20].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[12].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[13].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[14].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[15].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[16].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[17].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[18].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[19].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[25].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[26].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[27].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_xh');
  }

  if ('Y' != qygxhXmBz && skssqqDate >= hbgDate) {
    var list = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
    for (var i = 0; i < list.length; i++) {
      if (i > 9 && i <= 30) {
        filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[' + i + '].t_xh');
      }
    }
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[32].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh');
  }

  var sbqylx = formData.hq_.qtxx.sbqylx;
  if (sbqylx == '2') {
    filterKeys.push('ht_.ywbw.A200000Ywbd.sbxx.yysrLj');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sbxx.yycbLj');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sbxx.lrzeLj');
  } else {
    if (!formData.fq_.cbytxx) {
      formData.fq_.cbytxx = {};
    }
    if (wbcsh['ht_.ywbw.A200000Ywbd.sbxx.yysrLj'] != undefined) {
      formData.fq_.cbytxx.yysrLj = wbcsh['ht_.ywbw.A200000Ywbd.sbxx.yysrLj'];
    }
    if (wbcsh['ht_.ywbw.A200000Ywbd.sbxx.yycbLj'] != undefined) {
      formData.fq_.cbytxx.yycbLj = wbcsh['ht_.ywbw.A200000Ywbd.sbxx.yycbLj'];
    }
    if (wbcsh['ht_.ywbw.A200000Ywbd.sbxx.lrzeLj'] != undefined) {
      formData.fq_.cbytxx.lrzeLj = wbcsh['ht_.ywbw.A200000Ywbd.sbxx.lrzeLj'];
    }
  }

  for (var i = 0; i < filterKeys.length; i++) {
    delete wbcsh[filterKeys[i]];
  }

  if ('Y' != qygxhXmBz) {
    return wbcsh;
  }
  var gzbz = formData.kz_.temp.gzsbbz;
  if ('Y' == gzbz || 'zx' == gzbz) {
    //--JMSE00201 JMSE00202同时选上时,则取C2计算 t_jslx需要赋值2，否则默认1
    if (
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect'] &&
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect'] == true &&
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] &&
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] == 'C2'
    ) {
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx'] = 2;
    }

    if (
      wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect'] == true &&
      (wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] == '' ||
        wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] == null)
    ) {
      if (wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSelect'] == true) {
        var yhjmje1 = Number(wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje']);
        var yhjmje2 = Number(wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje']);
        var ynsdseLj = Number(wbcsh['ht_.ywbw.A200000Ywbd.sbxx.ynsdseLj']);

        var X = ROUND((ynsdseLj - yhjmje1 / 0.4) / yhjmje2, 0);
        if (X === 1) {
          wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx'] = 1;
          wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] = 'C1';
        } else {
          wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx'] = 2;
          wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] = 'C2';
        }
      } else {
        wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jslx'] = 1;
        wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].jejslx'] = 'C1';
      }
    }

    wbcsh = xmgxhQcgzbyzJdTs(wbcsh);
    return wbcsh;
  }
  return wbcsh;
}

/**
 * 厦门个性化公式：清除规则不一致节点
 * */
 function xmgxhQcgzbyzJdTs(wbcsh) {
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  if ('Y' != qygxhXmBz) {
    return;
  }
  var sfdndiq = formData.fq_.sfdndiq;
  //1.白名单
  var gxjsBmdBz = formData.kz_.bmdBzxx.gxjsBmdBz;
  var fylBmdBz = formData.kz_.bmdBzxx.fylBmdBz;
  var dmqyBmdBz = formData.kz_.bmdBzxx.dmqyBmdBz;
  var jsxjxfwBmdBz = formData.kz_.bmdBzxx.jsxjxfwBmdBz;
  var tipsContent = '';
  var gzbz = formData.kz_.temp.gzsbbz;
  //可能要处理的节点
  var sfczMssrFylyh = 'N';
  var sfczMssrQtyh = 'N';
  var sfczMssrQtyh1 = 'N';
  var sfczMssrQtyh2 = 'N';
  var sfczSdjmFhtj2Yh = 'N';
  var sfczSdjmQtYh = 'N';
  var sfczJmsdGxjsYh = 'N';
  var sfczJmsdJsxjYh = 'N';
  var sfczJmsdDmqyYh = 'N';
  var sfczJmsdQtYh = 'N';
  var sfczMssrcxqyyh = 'N';
  var sfczMssryxzyh = 'N';
  var jpathList = [];
  //更正申报判断当期，正常申报判断上期数（并且不是当年首次申报）
  if ('Y' == gzbz || 'zx' == gzbz) {
    //更正取当期,即取外部数据报文
    if (!isEmptyObject(wbcsh)) {
      //第7行：非营利企业
      var fylje = wbcsh['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].yhjmje'];
      if (fylje > 0 && fylBmdBz == 'N') {
        sfczMssrFylyh = 'Y';
      }
      //第7行：其他优惠
      var mssrQtje1 = wbcsh['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].yhjmje'];
      if (mssrQtje1 > 0) {
        sfczMssrQtyh1 = 'Y';
      }
      var mssrQtje2 = wbcsh['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].yhjmje'];
      if (mssrQtje2 > 0) {
        sfczMssrQtyh2 = 'Y';
      }

      //第7行	MSSR024、MSSR025
      var mssrcxqy = wbcsh['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[4].yhjmje'];
      var mssryxz = wbcsh['ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[5].yhjmje'];
      if (mssrcxqy > 0) {
        sfczMssrcxqyyh = 'Y';
      }
      if (mssryxz > 0) {
        sfczMssryxzyh = 'Y';
      }

      //第8行：SD042和其他
      var sdjmFhtj2yhJe = wbcsh['ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[6].yhjmje'];
      var sdjmQtyhJe = wbcsh['ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[12].yhjmje'];
      if (sdjmFhtj2yhJe > 0) {
        sfczSdjmFhtj2Yh = 'Y';
      }
      if (sdjmQtyhJe > 0) {
        sfczSdjmQtYh = 'Y';
      }

      //技术先进型企业
      var jsxjxfwje1 = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].yhjmje'];
      if (!isEmptyObject(jsxjxfwje1)) {
        jsxjxfwje1 = Number(jsxjxfwje1);
      } else {
        jsxjxfwje1 = 0;
      }
      var jsxjxfwje2 = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].yhjmje'];
      if (!isEmptyObject(jsxjxfwje2)) {
        jsxjxfwje2 = Number(jsxjxfwje2);
      } else {
        jsxjxfwje2 = 0;
      }
      if (jsxjxfwBmdBz === 'N' && (jsxjxfwje1 > 0 || jsxjxfwje2 > 0)) {
        sfczJmsdJsxjYh = 'Y';
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].t_jmsdssxSelect = false;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].t_jmsdssxSelect = false;
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].yhjmje');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].yhjmje');
      }

      //高新企业
      var gsjsje1 = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje'];
      if (!isEmptyObject(gsjsje1)) {
        gsjsje1 = Number(gsjsje1);
      } else {
        gsjsje1 = 0;
      }
      var gsjsje2 = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje'];
      if (!isEmptyObject(gsjsje2)) {
        gsjsje2 = Number(gsjsje2);
      } else {
        gsjsje2 = 0;
      }
      if (gxjsBmdBz === 'N' && (gsjsje1 > 0 || gsjsje2 > 0)) {
        sfczJmsdGxjsYh = 'Y';
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect = false;
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje');
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSelect = false;
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje');
      }

      //动漫企业
      var dmqyje = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].yhjmje'];
      if (!isEmptyObject(dmqyje)) {
        dmqyje = Number(dmqyje);
      } else {
        dmqyje = 0;
      }
      if (dmqyBmdBz === 'N' && dmqyje > 0) {
        sfczJmsdDmqyYh = 'Y';
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].yhjmje = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].yhjmje');
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].t_jmsdssxSelect = false;
      }

      //其他行
      var qtje = wbcsh['ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].yhjmje'];
      if (!isEmptyObject(qtje)) {
        qtje = Number(qtje);
      } else {
        qtje = 0;
      }
      if (qtje > 0) {
        sfczJmsdQtYh = 'Y';
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].yhjmje = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].yhjmje');
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_jmsdssxSl1 = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_jmsdssxSelect = false;
      }
    }
  } else if ('N' == sfdndiq) {
    //正常申报取往期，并且不是当期首次申报
    if (
      !isEmptyObj(formData.hq_.sqsbxx) &&
      !isEmptyObj(formData.hq_.sqsbxx.mssrGrid) &&
      !isEmptyObj(formData.hq_.sqsbxx.mssrGrid.mssrGridlb) &&
      formData.hq_.sqsbxx.mssrGrid.mssrGridlb.length > 0
    ) {
      var hqGridlb = formData.hq_.sqsbxx.mssrGrid.mssrGridlb;
      for (var i = 0; i < hqGridlb.length; i++) {
        var vo = hqGridlb[i];
        var yhswsx = vo.yhswsx;
        var yhjmje = vo.yhjmje;
        if (isEmptyObj(yhswsx) || isEmptyObj(yhjmje)) {
          continue;
        }
        //选择其他，并且金额大于0
        if (yhjmje > 0) {
          if ('0004129999' == vo.ssjmxzDm) {
            sfczMssrQtyh = 'Y';
          } else if ('0004120601' == vo.ssjmxzDm && 'N' == fylBmdBz) {
            sfczMssrFylyh = 'Y';
          }
        }
      }
    }

    if (
      !isEmptyObj(formData.hq_.sqsbxx) &&
      !isEmptyObj(formData.hq_.sqsbxx.sdjmGrid) &&
      !isEmptyObj(formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb) &&
      formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb.length > 0
    ) {
      var hqGridlb = formData.hq_.sqsbxx.sdjmGrid.sdjmGridlb;
      for (var i = 0; i < hqGridlb.length; i++) {
        var vo = hqGridlb[i];
        var yhswsx = vo.yhswsx;
        var yhjmje = vo.yhjmje;
        if (isEmptyObj(yhswsx) || isEmptyObj(yhjmje)) {
          continue;
        }
        //选择其他，并且金额大于0
        if (yhjmje > 0) {
          if ('0004129999' == vo.ssjmxzDm) {
            sfczSdjmQtYh = 'Y';
          } else if ('0004021204' == vo.ssjmxzDm) {
            sfczSdjmFhtj2Yh = 'Y';
          }
        }
      }
    }
  }
  if (jpathList.length > 0) {
    formulaEngine.apply4List(jpathList);
  }
  //判断有多少条提示语
  var tipsCount = 0;
  if (sfczMssrFylyh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.尊敬的纳税人，你单位未被认定为非营利组织，主表A200000第7行不应选择MSSR030“符合条件的非营利组织的收入免征企业所得税”，现已置为0。<br/>';
  }
  if (sfczMssrcxqyyh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.主表A200000第7行不应选择MSSR024“创新企业CDR股息红利所得免征企业所得税”项目，现已置为0。<br/>';
  }
  if (sfczMssryxzyh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent + tipsCount + '.主表A200000第7行不应选择MSSR025“永续债利息收入免征企业所得税”项目，现已置为0。<br/>';
  }
  if (sfczMssrQtyh1 == 'Y') {
    tipsCount++;
    tipsContent = tipsContent + tipsCount + '.主表A200000第7行不应选择MSSRQT“免税收入其他”项目，现已置为0。<br/>';
  }
  if (sfczMssrQtyh2 == 'Y') {
    tipsCount++;
    tipsContent = tipsContent + tipsCount + '.主表A200000第7行不应选择JJSRQT“减计收入其他”项目，现已置为0。<br/>';
  }
  if (sfczMssrQtyh == 'Y') {
    tipsCount++;
    tipsContent = tipsContent + tipsCount + '.主表A200000第7行不应选择“其他”项目，现已置为0。<br/>';
  }
  if (sfczSdjmFhtj2Yh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.主表A200000第8行不应选择SD042“符合条件的中关村国家自主创新示范区特定区域技术转让项目所得减免征收企业所得税”项目，现已置为0。<br/>';
  }
  if (sfczSdjmQtYh == 'Y') {
    tipsCount++;
    tipsContent = tipsContent + tipsCount + '.主表A200000第8行不应选择SD999“其他”项目，现已置为0。<br/>';
  }
  if (sfczJmsdGxjsYh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.尊敬的纳税人，您不是高新技术企业，主表第13行不应选择JMSE00201“高新技术企业”或者JMSE00202“经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税”，现已置为0。<br/>';
  }

  if (sfczJmsdJsxjYh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.尊敬的纳税人，您不是技术先进型服务，主表第13行不应选择JMSE00401或JMSE00402“技术先进型服务企业”，现已置为0。<br/>';
  }

  if (sfczJmsdDmqyYh == 'Y') {
    tipsCount++;
    tipsContent =
      tipsContent +
      tipsCount +
      '.尊敬的纳税人，您不是动漫企业，主表第13行不应选择JMSE00501“动漫企业自主开发、生产动漫产品定期减免企业所得税”，现已置为0。<br/>';
  }
  if (sfczJmsdQtYh == 'Y') {
    tipsCount++;
    tipsContent = tipsContent + tipsCount + '.主表第13行不应选择JMSE99999“其他”优惠，现已置为0。';
  }
  if (tipsCount > 0) {
    //弹框提示
    var index = layer.open({
      title: '提示',
      type: 1,
      area: ['500px', '400px'],
      content: tipsContent,
      btn: ['确定'],
      yes: function () {
        layer.close(index);
      },
      cancel: function () {
        layer.close(index);
      },
    });
  }
  if ('Y' == gzbz || 'zx' == gzbz) {
    //13行.当厦门不在高新技术企业白名单，技术先进服务型的企业，动漫白名单内的企业 这三种白名单内时，要过滤相应的要素节点
    var filterKeys = [];
    //第13行弹框行序号由公式执行,不用wbcsh的覆盖,因为覆盖后没有执行到公式,可能有BUG,所以把wbchs的删除
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[7].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[3].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[4].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[5].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[6].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[8].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[9].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[10].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[11].t_xh');

    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[21].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[20].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[12].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[13].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[14].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[15].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[16].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[17].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[18].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[19].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_xh');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh');
    if (jsxjxfwBmdBz === 'N') {
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].t_jmsdssxSl1');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].t_jmsdssxSl1');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[22].t_jmsdssxSelect');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[23].t_jmsdssxSelect');
    }

    if (gxjsBmdBz === 'N') {
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSl1');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSl1');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[1].t_jmsdssxSelect');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[2].t_jmsdssxSelect');
    }

    if (dmqyBmdBz === 'N') {
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].t_jmsdssxSl1');
      filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[24].t_jmsdssxSelect');
    }
    //13行.其他行直接重置为0
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].yhjmje');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_jmsdssxSl1');
    filterKeys.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_jmsdssxSelect');
    //第7行非营利
    if (fylBmdBz === 'N') {
      filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].yhjmje');
      filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].t_mssrsxSelect');
      filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[6].t_mssrSfyxz');
    }
    //第7行其他
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].yhjmje');
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].yhjmje');
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_mssrsxSelect');
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_mssrsxSelect');
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[19].t_mssrSfyxz');
    filterKeys.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[20].t_mssrSfyxz');
    //第8行：SD042和其他
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[6].yhjmje');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[12].yhjmje');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[6].t_sdjmsxSelect');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[12].t_sdjmsxSelect');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[6].t_sdjmSfyxz');
    filterKeys.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[12].t_sdjmSfyxz');

    for (var i = 0; i < filterKeys.length; i++) {
      delete wbcsh[filterKeys[i]];
    }

    if (typeof filterWbcshDeliver == 'function') {
      wbcsh = filterWbcshDeliver(wbcsh);
    }
    if (typeof filterWbcshCDeliver == 'function') {
      wbcsh = filterWbcshCDeliver(wbcsh);
    }
    if (typeof validateYsData == 'function') {
      wbcsh = validateYsData(wbcsh);
      if (typeof wbcsh == 'string') {
        wbcsh = JSON.parse(wbcsh);
      }
    }
    return wbcsh;
  }
}

function sort20000Grid() {
  var jpathList = [];
  var mssrGridlb = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb;
  if (mssrGridlb != null && mssrGridlb.length > 0) {
    var j = 1;
    for (var i = 0; i < mssrGridlb.length; i++) {
      if (mssrGridlb[i].t_mssrsxSelect != null && mssrGridlb[i].t_mssrsxSelect) {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[i].t_nxh = j;
        j++;
      }
    }
    jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[#].t_nxh');
  }

  var sdjmGridlb = formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb;
  if (sdjmGridlb != null && sdjmGridlb.length > 0) {
    var k = 1;
    for (var i = 0; i < sdjmGridlb.length; i++) {
      if (sdjmGridlb[i].t_sdjmsxSelect != null && sdjmGridlb[i].t_sdjmsxSelect) {
        formData.ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[i].t_nxh = k;
        k++;
      }
    }
    jpathList.push('ht_.ywbw.A200000Ywbd.sdjmGrid.sdjmGridlb[#].t_nxh');
  }

  var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
  if (jmsdGridlb != null && jmsdGridlb.length > 0) {
    var p = 1;
    for (var i = 0; i < jmsdGridlb.length; i++) {
      if (jmsdGridlb[i].t_jmsdssxSelect != null && jmsdGridlb[i].t_jmsdssxSelect) {
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_nxh = p;
        p++;
      }
    }
    jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[#].t_nxh');
  }

  if (jpathList.length > 0) {
    formulaEngine.apply4List(jpathList);
  }
}

/**
 * 附表2判断行业是否变更
 *
 */
function fb2GetHyDmSfbg() {
  //当上期数选择的资产加速折旧优惠表的项目和纳税的行业代码不一致时，则认为变更，暂时只有厦门版本可以区分，全国版本不能区分
  //无法判断是否变更行业返回空
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  if ('Y' != qygxhXmBz) {
    return '';
  }
  var sfdndiq = formData.fq_.sfdndiq;
  if ('Y' === sfdndiq) {
    return '';
  }
  //是否是重要行业isZyqy()
  var zyhyBz = isZyqy();

  //1.当行业代码不是重要行业时，上期数有选择了重要行业的项目，
  //2.当行业代码是重要行业时，上期数有选择了其他行业的项目，那么则认为变更了行业
  if (
    isEmptyObj(formData.hq_.sqsbxx) ||
    isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid) ||
    isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb) ||
    formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb.length < 1
  ) {
    return '';
  }
  var gdzcjszjkcmxbGridlb = formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb;
  for (var i in gdzcjszjkcmxbGridlb) {
    if (gdzcjszjkcmxbGridlb[i].ewbhgjz == 'JSZJ' && gdzcjszjkcmxbGridlb[i].ewbhxh != 0) {
      if (zyhyBz == false && 'JSZJ0010' === gdzcjszjkcmxbGridlb[i].yhswsx) {
        var _jpath1 = 'ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnxsyhdzcyz';
        formulaEngine.apply(_jpath1, '');
        return 'Y';
      } else if (zyhyBz == true && 'JSZJ0020' === gdzcjszjkcmxbGridlb[i].yhswsx) {
        var _jpath1 = 'ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnxsyhdzcyz';
        formulaEngine.apply(_jpath1, '');
        return 'A';
      }
    }
  }
  return '';
}

//核心fzjg判断上海和新疆地区类型
function fzjgDqlx() {
  var swjgDm = formData.fq_.nsrjbxx.swjgDm.substring(0, 5);
  //下标： 0:新疆;1:海南;2:西部大开发;3:广东横琴、福建平潭、深圳前海;4:上海;5:广东横琴;6:福建平潭;7:深圳前海;8:南沙先行启动区;
  var result = ['N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N'];
  //西部大开发
  var mcArr = [
    '内蒙古',
    '广西壮族',
    '重庆',
    '成都',
    '四川',
    '贵州',
    '云南',
    '西藏',
    '陕西',
    '甘肃',
    '青海',
    '宁夏回族',
    '新疆维吾尔',
    '新疆生产建设兵团',
    '湖南省湘西土家族苗族',
    '湖北省恩施土家族苗族',
    '吉林省延边朝鲜族',
    '江西省赣州',
    '长白山保护开发区税务局池北',
  ];

  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (fzjgyhxx[i].xbdkfbz == 'Y') {
          result[2] = 'Y';
        }
        if (fzjgyhxx[i].hnzmgbz == 'Y') {
          result[1] = 'Y';
        }
        if (fzjgyhxx[i].qtdqbz == 'Y') {
          result[3] = 'Y';
        }
      }
    }
  }
  if (
    formData.hq_.fzjgxxGrid &&
    formData.hq_.fzjgxxGrid.fzjgxxGridlb &&
    formData.hq_.fzjgxxGrid.fzjgxxGridlb.length > 0
  ) {
    var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      var fzjgnsrsbh = isNull(fzjgxxGridlb[i].fzjgnsrsbh) ? '' : fzjgxxGridlb[i].fzjgnsrsbh;
      var zgswjgDm = isNull(fzjgxxGridlb[i].zgswjgDm) ? '' : fzjgxxGridlb[i].zgswjgDm;
      var zgswjgmc = isNull(fzjgxxGridlb[i].zgswjgmc) ? '' : fzjgxxGridlb[i].zgswjgmc;
      var sbhDqdm = '';
      var sbhSjdm = '';
      var sbhQjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhDqdm = fzjgnsrsbh.toString().substring(1, 4);
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
        sbhQjdm = fzjgnsrsbh.toString().substring(1, 8);
      }

      //新疆
      if (
        (isNull(zgswjgDm) && (sbhDqdm == '165' || sbhDqdm == '265')) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '165')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('新疆') > -1)
      ) {
        result[0] = 'Y';
      }
      //海南
      if (
        (isNull(zgswjgDm) && (sbhDqdm == '146' || sbhDqdm == '246')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('海南') > -1) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '146'))
      ) {
        result[1] = 'Y';
      }

      //西部大开发
      if (
        (isNull(zgswjgDm) &&
          (sbhDqdm == '115' ||
            sbhDqdm == '145' ||
            sbhDqdm == '150' ||
            sbhDqdm == '151' ||
            sbhDqdm == '152' ||
            sbhDqdm == '153' ||
            sbhDqdm == '154' ||
            sbhDqdm == '161' ||
            sbhDqdm == '162' ||
            sbhDqdm == '163' ||
            sbhDqdm == '164' ||
            sbhDqdm == '165' ||
            sbhSjdm == '14331' ||
            sbhSjdm == '14228' ||
            sbhSjdm == '12224' ||
            sbhSjdm == '13607' ||
            sbhQjdm == '1229002' ||
            sbhDqdm == '215' ||
            sbhDqdm == '245' ||
            sbhDqdm == '250' ||
            sbhDqdm == '251' ||
            sbhDqdm == '252' ||
            sbhDqdm == '253' ||
            sbhDqdm == '254' ||
            sbhDqdm == '261' ||
            sbhDqdm == '262' ||
            sbhDqdm == '263' ||
            sbhDqdm == '264' ||
            sbhDqdm == '265' ||
            sbhSjdm == '24331' ||
            sbhSjdm == '24228' ||
            sbhSjdm == '22224' ||
            sbhSjdm == '23607' ||
            sbhQjdm == '2229002')) ||
        (!isNull(zgswjgDm) &&
          (startWidthStr(zgswjgDm, '115') ||
            startWidthStr(zgswjgDm, '145') ||
            startWidthStr(zgswjgDm, '150') ||
            startWidthStr(zgswjgDm, '151') ||
            startWidthStr(zgswjgDm, '152') ||
            startWidthStr(zgswjgDm, '153') ||
            startWidthStr(zgswjgDm, '154') ||
            startWidthStr(zgswjgDm, '161') ||
            startWidthStr(zgswjgDm, '163') ||
            startWidthStr(zgswjgDm, '164') ||
            startWidthStr(zgswjgDm, '165') ||
            startWidthStr(zgswjgDm, '14331') ||
            startWidthStr(zgswjgDm, '14228') ||
            startWidthStr(zgswjgDm, '12224') ||
            startWidthStr(zgswjgDm, '13607') ||
            startWidthStr(zgswjgDm, '1229002'))) ||
        (!isNull(zgswjgmc) &&
          ((zgswjgmc + '').indexOf('内蒙古') > -1 ||
            (zgswjgmc + '').indexOf('广西壮族') > -1 ||
            (zgswjgmc + '').indexOf('重庆') > -1 ||
            (zgswjgmc + '').indexOf('成都') > -1 ||
            (zgswjgmc + '').indexOf('四川') > -1 ||
            (zgswjgmc + '').indexOf('贵州') > -1 ||
            (zgswjgmc + '').indexOf('云南') > -1 ||
            (zgswjgmc + '').indexOf('西藏') > -1 ||
            (zgswjgmc + '').indexOf('陕西') > -1 ||
            (zgswjgmc + '').indexOf('甘肃') > -1 ||
            (zgswjgmc + '').indexOf('青海') > -1 ||
            (zgswjgmc + '').indexOf('宁夏回族') > -1 ||
            (zgswjgmc + '').indexOf('新疆维吾尔') > -1 ||
            (zgswjgmc + '').indexOf('新疆生产建设兵团') > -1 ||
            (zgswjgmc + '').indexOf('湖南省湘西土家族苗族') > -1 ||
            (zgswjgmc + '').indexOf('湖北省恩施土家族苗族') > -1 ||
            (zgswjgmc + '').indexOf('吉林省延边朝鲜族') > -1 ||
            (zgswjgmc + '').indexOf('江西省赣州') > -1 ||
            (zgswjgmc + '').indexOf('长白山保护开发区税务局池北') > -1))
      ) {
        result[2] = 'Y';
      }
      //广东横琴、福建平潭、深圳前海
      if (
        (isNull(zgswjgDm) &&
          (sbhSjdm == '14404' ||
            sbhSjdm == '13593' ||
            sbhSjdm == '14403' ||
            sbhSjdm == '24404' ||
            sbhSjdm == '23593' ||
            sbhSjdm == '24403')) ||
        (!isNull(zgswjgDm) &&
          (startWidthStr(zgswjgDm, '14404') || startWidthStr(zgswjgDm, '13593') || startWidthStr(zgswjgDm, '14403'))) ||
        (!isNull(zgswjgmc) &&
          ((zgswjgmc + '').indexOf('珠海') > -1 ||
            (zgswjgmc + '').indexOf('平潭市') > -1 ||
            (zgswjgmc + '').indexOf('深圳市') > -1))
      ) {
        result[3] = 'Y';
      }
      //上海
      if (
        (isNull(zgswjgDm) && (sbhDqdm == '131' || sbhDqdm == '231')) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '131')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('上海') > -1)
      ) {
        result[4] = 'Y';
      }

      //广东横琴
      if (
        (isNull(zgswjgDm) && (sbhSjdm == '14404' || sbhSjdm == '24404')) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '14404')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('珠海') > -1)
      ) {
        result[5] = 'Y';
      }

      //福建平潭
      if (
        (isNull(zgswjgDm) && (sbhSjdm == '13593' || sbhSjdm == '23593')) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '13593')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('平潭市') > -1)
      ) {
        result[6] = 'Y';
      }

      //深圳前海
      if (
        (isNull(zgswjgDm) && (sbhSjdm == '14403' || sbhSjdm == '24403')) ||
        (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '14403')) ||
        (!isNull(zgswjgmc) && (zgswjgmc + '').indexOf('深圳市') > -1)
      ) {
        result[7] = 'Y';
      }

      //南沙先行启动区
      if ((isNull(zgswjgDm) && sbhQjdm == '14401150') || (!isNull(zgswjgDm) && startWidthStr(zgswjgDm, '14401150'))) {
        result[8] = 'Y';
      }
    }
  }
  return result;
}

function setMzzzdqdfjmLj(mzzzdqdfjmBq) {
  var val = 0;
  var sqsbxx = formData.hq_.sqsbxx;
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  if (!isNull(sqsbxx)) {
    var mzzzdqdfjmLj = sqsbxx.mzzzdqdfjmLj;
    if (!isNull(mzzzdqdfjmLj)) {
      val = mzzzdqdfjmLj;
    }
  }
  if (sbqylx == '0') {
    formData.ht_.ywbw.A200000Ywbd.sbxx.mzzzdqdfjmzfjghjLj = ROUND(val + mzzzdqdfjmBq, 2);
    var _jpath1 = 'ht_.ywbw.A200000Ywbd.sbxx.mzzzdqdfjmzfjghjLj';
    formulaEngine.apply(_jpath1, '');
  }
  return ROUND(val + mzzzdqdfjmBq, 2);
}

function setMzzzdqdfjmzfjghjLj(xsdfjmje, mzzzdqdfjmLj) {
  // var mzzzdqdfjmzfjghjLj = formData.ht_.ywbw.A200000Ywbd.sbxx.mzzzdqdfjmzfjghjLj;
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  if (sbqylx == '2') {
    return 0;
  } else if (sbqylx == '0') {
    return mzzzdqdfjmLj;
  } else if (sbqylx == '1') {
    //var fzjgfpbxxSum = Number(formData.hq_.sqsbxx.fzjgXsdfjmje);
    var fzjgfpbxxSum = Number(formData.kz_.temp.zb.sqfzjgXsdfjmje);
    var fzjgxxGridlb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
    var fzjgxxGridlbSum = 0;
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      if (fzjgxxGridlb[i].sfxsdfjm == 'Y') {
        fzjgxxGridlbSum += fzjgxxGridlb[i].xsdfjmje;
      }
    }
    var sum = ROUND(mzzzdqdfjmLj + fzjgfpbxxSum + fzjgxxGridlbSum, 2);
    return sum;
  }
}

//是否西部大开发地区
function isXbdkfdq(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].xbdkfbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }

  var arr1 = [
    '115',
    '145',
    '150',
    '151',
    '152',
    '153',
    '154',
    '161',
    '162',
    '163',
    '164',
    '165',
    '215',
    '245',
    '250',
    '251',
    '252',
    '253',
    '254',
    '261',
    '262',
    '263',
    '264',
    '265',
  ];
  var arr2 = ['14331', '14228', '12224', '13607', '24331', '24228', '22224', '23607'];

  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var dqdm = swjgDm.substring(0, 3);
    var sjdm = swjgDm.substring(0, 5);
    var sqdm = swjgDm.substring(0, 7);

    if (sqdm == '1229002') {
      flag = true;
    }

    for (var i = 0; i < arr1.length; i++) {
      if (arr1[i] == dqdm) {
        flag = true;
        break;
      }
    }

    for (var j = 0; j < arr2.length; j++) {
      if (arr2[j] == sjdm) {
        flag = true;
        break;
      }
    }
  } else {
    var xb = typeof index == 'object' ? index[0] : index;
    if (!isNull(xb)) {
      var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[xb].nsrsbh;
      var sbhDqdm = '';
      var sbhSjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhDqdm = fzjgnsrsbh.toString().substring(1, 4);
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
      }
      for (var i = 0; i < arr1.length; i++) {
        if (!isNull(sbhDqdm) && arr1[i] == sbhDqdm) {
          flag = true;
          break;
        }
      }

      for (var j = 0; j < arr2.length; j++) {
        if (!isNull(sbhSjdm) && arr2[j] == sbhSjdm) {
          flag = true;
          break;
        }
      }
    }
  }

  var mcArr = [
    '内蒙古',
    '广西壮族',
    '重庆',
    '成都',
    '四川',
    '贵州',
    '云南',
    '西藏',
    '陕西',
    '甘肃',
    '青海',
    '宁夏回族',
    '新疆维吾尔',
    '新疆生产建设兵团',
    '湖南省湘西土家族苗族',
    '湖北省恩施土家族苗族',
    '吉林省延边朝鲜族',
    '江西省赣州',
    '长白山保护开发区税务局池北',
  ];
  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  var matchName = false;
  for (var index = 0; index < mcArr.length; index++) {
    if (zgswjgmc != '' && zgswjgmc.indexOf(mcArr[index]) > -1) {
      matchName = true;
      break;
    }
  }
  flag = flag || matchName;
  return flag;
}

//海南自贸港判断
function isHnzmg(swjgDm, zgswjgmc, index, fzjgnsrsbh) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].hnzmgbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }

  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var dqdm = swjgDm.substring(0, 3);
    if (dqdm == '146' || dqdm == '246') {
      flag = true;
    }
  } else {
    fzjgnsrsbh = fzjgnsrsbh.toString();
    if (
      fzjgnsrsbh != '' &&
      fzjgnsrsbh != undefined &&
      (startWidthStr(fzjgnsrsbh, '9146') || fzjgnsrsbh.substring(1, 4) == '146' || fzjgnsrsbh.substring(1, 4) == '246')
    ) {
      flag = true;
    }
  }

  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  return flag || (zgswjgmc != null && zgswjgmc != undefined && zgswjgmc != '' && zgswjgmc.indexOf('海南省') > -1);
}

/**
 * @description 是否是珠海市、平潭市、深圳市
 * @param swjgDm 税务机关
 * @returns true 或 false
 */
function isZhPtSz(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].qtdqbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }
  var arr = ['14404', '13593', '14403', '24404', '23593', '24403'];
  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var sjdm = swjgDm.substring(0, 5);
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] == sjdm) {
        flag = true;
        break;
      }
    }
  } else {
    var xb = typeof index == 'object' ? index[0] : index;
    if (!isNull(xb)) {
      var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[xb].nsrsbh;
      var sbhSjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
      }
      for (var i = 0; i < arr.length; i++) {
        if (arr[i] == sbhSjdm) {
          flag = true;
          break;
        }
      }
    }
  }

  var mcArr = ['珠海', '平潭市', '深圳市'];
  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  var matchName = false;
  for (var index = 0; index < mcArr.length; index++) {
    if (zgswjgmc != '' && zgswjgmc.indexOf(mcArr[index]) > -1) {
      matchName = true;
      break;
    }
  }
  flag = flag || matchName;
  return flag;
}

/**
 * @description 是否是平潭市
 * @param swjgDm 税务机关
 * @returns true 或 false
 */
function isFjptlx(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].qtdqbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }
  var arr = ['13593', '23593'];
  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var sjdm = swjgDm.substring(0, 5);
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] == sjdm) {
        flag = true;
        break;
      }
    }
  } else {
    var xb = typeof index == 'object' ? index[0] : index;
    if (!isNull(xb)) {
      var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[xb].nsrsbh;
      var sbhSjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
      }
      if (sbhSjdm == '13593' || sbhSjdm == '23593') {
        flag = true;
      }
    }
  }

  var mcArr = ['平潭市'];
  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  var matchName = false;
  for (var index = 0; index < mcArr.length; index++) {
    if (zgswjgmc != '' && zgswjgmc.indexOf(mcArr[index]) > -1) {
      matchName = true;
      break;
    }
  }
  flag = flag || matchName;
  return flag;
}

/**
 * @description 是否是广东横琴市
 * @param swjgDm 税务机关
 * @returns true 或 false
 */
function isDdhqlx(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].qtdqbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }
  var arr = ['14404', '24404'];
  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var sjdm = swjgDm.substring(0, 5);
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] == sjdm) {
        flag = true;
        break;
      }
    }
  } else {
    var xb = typeof index == 'object' ? index[0] : index;
    if (!isNull(xb)) {
      var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[xb].nsrsbh;
      var sbhSjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
      }
      if (sbhSjdm == '14404' || sbhSjdm == '24404') {
        flag = true;
      }
    }
  }

  var mcArr = ['珠海'];
  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  var matchName = false;
  for (var index = 0; index < mcArr.length; index++) {
    if (zgswjgmc != '' && zgswjgmc.indexOf(mcArr[index]) > -1) {
      matchName = true;
      break;
    }
  }
  flag = flag || matchName;
  return flag;
}

/**
 * @description 是否是深圳前海
 * @param swjgDm 税务机关
 * @returns true 或 false
 */
function isSzqhlx(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].qtdqbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }
  var arr = ['14403', '24403'];
  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var sjdm = swjgDm.substring(0, 5);
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] == sjdm) {
        flag = true;
        break;
      }
    }
  } else {
    var xb = typeof index == 'object' ? index[0] : index;
    if (!isNull(xb)) {
      var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[xb].nsrsbh;
      var sbhSjdm = '';
      if (!isNull(fzjgnsrsbh)) {
        sbhSjdm = fzjgnsrsbh.toString().substring(1, 6);
      }
      if (sbhSjdm == '14403' || sbhSjdm == '24403') {
        flag = true;
      }
    }
  }

  var mcArr = ['深圳市'];
  // 防止zgswjgmc 为数值类型，而导致后面的zgswjgmc.indexOf报错
  zgswjgmc = zgswjgmc + '';
  var matchName = false;
  for (var index = 0; index < mcArr.length; index++) {
    if (zgswjgmc != '' && zgswjgmc.indexOf(mcArr[index]) > -1) {
      matchName = true;
      break;
    }
  }
  flag = flag || matchName;
  return flag;
}

/**
 * @description 是否是广东南沙市
 * @param swjgDm 税务机关
 * @returns true 或 false
 */
function isGdnslx(swjgDm, zgswjgmc, index) {
  var dqDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var swjgDm15 = formData.fq_.nsrjbxx.swjgDm.substring(1, 5);
  if ('37' === dqDm && '3702' != swjgDm15) {
    var fzjgyhxx = formData.ss_.fzjgyhxx;
    if (fzjgyhxx && fzjgyhxx.length > 0) {
      var fzjgxxGridlbVo = formData.hq_.fzjgxxGrid.fzjgxxGridlb[index];
      for (var i = 0; i < fzjgyhxx.length; i++) {
        if (
          fzjgyhxx[i].fzjgnsrsbh == fzjgxxGridlbVo.fzjgnsrsbh &&
          fzjgyhxx[i].fzjgmc == fzjgxxGridlbVo.fzjgmc &&
          fzjgyhxx[i].qtdqbz == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  if (
    (swjgDm == null || swjgDm == undefined || swjgDm == '') &&
    (zgswjgmc == null || zgswjgmc == undefined || zgswjgmc == '')
  ) {
    return false;
  }
  var arr = ['1440115', '2440115'];
  var flag = false;
  if (swjgDm != null && swjgDm != undefined && swjgDm != '') {
    swjgDm = swjgDm + '';
    var sjdm = swjgDm.substring(0, 7);
    for (var i = 0; i < arr.length; i++) {
      if (arr[i] == sjdm) {
        flag = true;
        break;
      }
    }
  }
  return flag;
}

/**
 * 修正13行减免弹框实际显示的行序号
 * #revise 去掉了区域个性化，根据新需求调整了显示序号
 * #clear 可抽取到业务公共，和填表页一致
 * */
 function sdjmyhsxSetXh() {
  var skssqq = formData.fq_.sssq.sqQ;
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqqDate = new Date(skssqq);
  var skssqzDate = new Date(skssqz);

  if (skssqqDate >= new Date('2022-01-01') && skssqqDate < new Date('2024-01-01')) {
    // 删除事项 JMSE00306A、JMSE00307A 后，第9行开始要调整序号
    for (var i = 10; i <= 30; i++) {
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_xh = i - 1;
    }
    // JMSE00605 上海
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[32].t_xh = 30;
    // JMSE00606 海南
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_xh = 31;
    // JMSE000604 拆成三个事项，JMSE006037 福建、JMSE00608 深圳、JMSE00609 横琴
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_xh = 32;
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_xh = 33;
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_xh = 34;
    if (skssqzDate >= new Date('2022-09-30')) {
      // JMSE00610 南沙
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_xh = 35;
      // JMSE99999 其他
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh = 36;
      // JMSE00901 冬奥
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[35].t_xh = 37;
      if (skssqqDate >= new Date('2023-01-01')) {
        // JMSE00611 河套
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[43].t_xh = 36;
        // JMSE99999 其他
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh = 37;
        // JMSE00901 冬奥
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[35].t_xh = 38;
      }
    }
  } else if (skssqqDate >= new Date('2024-01-01')) {
    // 删除事项 JMSE00301A、JMSE00304A、JMSE00305A、JMSE00306A、JMSE00307A 后，除了前3行，都要二次调整显示序号
    for (var i = 4; i <= 5; i++) {
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_xh = i;
    }
    for (var i = 10; i <= 30; i++) {
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[i].t_xh = i - 4;
    }
    // JMSE00605 上海
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[32].t_xh = 27;
    // JMSE00606 海南
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_xh = 28;
    // JMSE006037 福建
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_xh = 29;
    // JMSE00608 深圳
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_xh = 30;
    // JMSE00609 横琴
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_xh = 31;
    // JMSE00610 南沙
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_xh = 32;
    // JMSE00611 河套
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[43].t_xh = 33;
    // JMSE99999 其他
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[34].t_xh = 34;
    // JMSE00901 冬奥
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[35].t_xh = 35;
  }

  formData.fq_.sdjmyhsxSfyszxh = 'Y';
}

//202000表：享受民族地方优惠金额
function setXsdfjmje(fzjgzgswjDm, fzjgftdsdse, xsdfjmfd, fpbl) {
  var zjgftbz = formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgftbz;
  // 总机构参与分配时 ,享受民族地方优惠金额默认0
  if (zjgftbz === 'Y') {
    return;
  }
  var fzjgxx = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  for (var i = 0; i < fzjgxx.length; i++) {
    var fzjgzgswjDm = fzjgxx[i].fzjgzgswjDm;
    var sfxsdfjm = fzjgxx[i].sfxsdfjm;
    var fzjglxlb = fzjgxx[i].fzjglxlb;
    if (sfxsdfjm == 'Y' && fzjglxlb !== 'dlbm' && fzjglxlb !== 'zjg') {
      fzjgxx[i].xsdfjmje = ROUND(fzjgftdsdse * 0.4 * fzjgxx[i].xsdfjmfd * fzjgxx[i].fpbl, 2);
    }
  }
  var _jpath = 'ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].xsdfjmje';
  formulaEngine.apply(_jpath, '');
}

function calFpbl() {
  var fpbl = 0;
  var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
  if (isArray(fzjgxxGridlb) && fzjgxxGridlb.length > 0) {
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      var fzjgnsrsbh = fzjgxxGridlb[i].fzjgnsrsbh;
      var fzjgmc = fzjgxxGridlb[i].fzjgmc;
      var fzjgdjxh = fzjgxxGridlb[i].fzjgdjxh;
      if (
        fzjgdjxh == formData.fq_.nsrjbxx.djxh ||
        (fzjgnsrsbh === formData.fq_.nsrjbxx.nsrsbh && fzjgmc === formData.fq_.nsrjbxx.nsrmc)
      ) {
        fpbl = fzjgxxGridlb[i].fpbl;
        break;
      }
    }
  }
  return fpbl;
}

function calFzjgfpsdseBq() {
  var fzjgfpsdseBq = 0;
  var kdsAndkxqFlag = formData.hq_.qtxx.kdsAndkxqFlag;
  var kyyjye = formData.hq_.qtxx.kyyjye;
  var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
  if (isArray(fzjgxxGridlb) && fzjgxxGridlb.length > 0) {
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      if (
        fzjgxxGridlb[i].fzjgdjxh == formData.fq_.nsrjbxx.djxh ||
        (fzjgxxGridlb[i].fzjgnsrsbh === formData.fq_.nsrjbxx.nsrsbh &&
          fzjgxxGridlb[i].fzjgmc === formData.fq_.nsrjbxx.nsrmc)
      ) {
        var fzjglxlb = fzjgxxGridlb[i].fzjglxlb;
        var xsdfjmje = fzjgxxGridlb[i].xsdfjmje;
        var fpse = fzjgxxGridlb[i].fpse;
        if (kdsAndkxqFlag == '2') {
          //（1）核心期初返回的qtxx.kyyjye<fzjgxxGrid.fzjgxxGridlb.fpse：
          //主表22行=fzjgxxGrid.fzjgxxGridlb.fpse-qtxx.kyyjye的数值，自动带出，不可修改。
          if (fpse > kyyjye && !isNull(fpse)) {
            fzjgfpsdseBq = fpse - kyyjye;
          } else {
            //核心期初返回的qtxx.kyyjye>fzjgxxGrid.fzjgxxGridlb.fpse：
            // 主表22行=fzjgxxGrid.fzjgxxGridlb.fpse-qtxx.kyyjye的数值，若相减计算小于0，则取0值，自动带出，不可修改。
            fzjgfpsdseBq = 0;
          }
        } else {
          if (fzjglxlb != 'zjg') {
            fzjgfpsdseBq = fpse + xsdfjmje;
          } else {
            fzjgfpsdseBq = fzjgxxGridlb[i].fpse;
          }
        }
        break;
      }
    }
  }
  return fzjgfpsdseBq;
}

function afterFormulaExcuted() {
  var dzbdlist = formData.kz_.dzbd;
  var gzsbbz = formData.kz_.temp.gzsbbz;
  var swjgDm = formData.fq_.nsrjbxx.swjgDm;
  var swjgDm05 = swjgDm ? swjgDm.substring(0, 5) : '';
  var jpathList = [];
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  var yjfs = formData.hq_.qtxx.yjfs;
  if ((gzsbbz == 'Y' || gzsbbz == 'zx') && sbqylx == '2') {
    formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs4 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze4 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3 = '';
    formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze4 = '';

    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qccyrs1');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qccyrs2');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qccyrs3');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qccyrs4');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmcyrs4');

    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qczcze1');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qczcze2');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qczcze3');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qczcze4');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmzcze1');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmzcze2');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmzcze3');
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qmzcze4');
  }
  if (swjgDm05 == '13502' && 'Y' != gzsbbz) {
    if (formData.hq_.sqsbxx) {
      // 从业人数和资产总额
      if (formData.hq_.sqsbxx.qccyrs1 && formData.hq_.sqsbxx.qccyrs1 != formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs1) {
        jpathList.push('hq_.sqsbxx.qccyrs1');
      }
      if (formData.hq_.sqsbxx.qmcyrs1 && formData.hq_.sqsbxx.qmcyrs1 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs1) {
        jpathList.push('hq_.sqsbxx.qmcyrs1');
      }
      if (formData.hq_.sqsbxx.qczcze1 && formData.hq_.sqsbxx.qczcze1 != formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze1) {
        jpathList.push('hq_.sqsbxx.qczcze1');
      }
      if (formData.hq_.sqsbxx.qmzcze1 && formData.hq_.sqsbxx.qmzcze1 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze1) {
        jpathList.push('hq_.sqsbxx.qmzcze1');
      }
      if (formData.hq_.sqsbxx.qccyrs2 && formData.hq_.sqsbxx.qccyrs2 != formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs2) {
        jpathList.push('hq_.sqsbxx.qccyrs2');
      }
      if (formData.hq_.sqsbxx.qmcyrs2 && formData.hq_.sqsbxx.qmcyrs2 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs2) {
        jpathList.push('hq_.sqsbxx.qmcyrs2');
      }
      if (formData.hq_.sqsbxx.qczcze2 && formData.hq_.sqsbxx.qczcze2 != formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze2) {
        jpathList.push('hq_.sqsbxx.qczcze2');
      }
      if (formData.hq_.sqsbxx.qmzcze2 && formData.hq_.sqsbxx.qmzcze2 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze2) {
        jpathList.push('hq_.sqsbxx.qmzcze2');
      }
      if (formData.hq_.sqsbxx.qccyrs3 && formData.hq_.sqsbxx.qccyrs3 != formData.ht_.ywbw.A200000Ywbd.sbxx.qccyrs3) {
        jpathList.push('hq_.sqsbxx.qccyrs3');
      }
      if (formData.hq_.sqsbxx.qmcyrs3 && formData.hq_.sqsbxx.qmcyrs3 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmcyrs3) {
        jpathList.push('hq_.sqsbxx.qmcyrs3');
      }
      if (formData.hq_.sqsbxx.qczcze3 && formData.hq_.sqsbxx.qczcze3 != formData.ht_.ywbw.A200000Ywbd.sbxx.qczcze3) {
        jpathList.push('hq_.sqsbxx.qczcze3');
      }
      if (formData.hq_.sqsbxx.qmzcze3 && formData.hq_.sqsbxx.qmzcze3 != formData.ht_.ywbw.A200000Ywbd.sbxx.qmzcze3) {
        jpathList.push('hq_.sqsbxx.qmzcze3');
      }

      //刷新主表4行数据
      if (formData.hq_.sqsbxx.tdywjsdynssdeLj) {
        jpathList.push('hq_.sqsbxx.tdywjsdynssdeLj');
      }
    }

    if (formData.hq_.qtxx) {
      //刷新主表9行数据
      if (formData.hq_.qtxx.mbksehj) {
        jpathList.push('hq_.qtxx.mbksehj');
      }
      //刷新主表14行数据
      if (formData.hq_.qtxx.sqyjje) {
        jpathList.push('hq_.qtxx.sqyjje');
      }
      if (formData.hq_.qtxx.kyyjye) {
        jpathList.push('hq_.qtxx.kyyjye');
      }
      if (formData.hq_.qtxx.yyyjje) {
        jpathList.push('hq_.qtxx.yyyjje');
      }
    }

    if (formData.fq_.sqyyxx && formData.fq_.sqyyxx.tdywjsdynssdeLj) {
      jpathList.push('fq_.sqyyxx.tdywjsdynssdeLj');
    }
    //刷新主表7行数据
    setMssrsxSqs();
    //刷新主表8行数据
    setSdjmsxSqs();

    // 重置附报事项上期数据信息
    setFbsxSqxx();
    jpathList.push('ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].jehxxz');
    jpathList.push('ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[1].jehxxz');

    //A201020校验公式
    if (
      formData.ht_.ywbw.A201020Ywbd &&
      formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid &&
      formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2
    ) {
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnxsyhdzcyz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnljzjkcjedzz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnljzjkcjedaz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2[#].bnljzjkcjedxs');
    }
    if (
      formData.ht_.ywbw.A201020Ywbd &&
      formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid &&
      formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3
    ) {
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3[#].bnxsyhdzcyz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3[#].bnljzjkcjedzz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3[#].bnljzjkcjedaz');
      jpathList.push('ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3[#].bnljzjkcjedxs');
    }
  }

  //BJELECTAX-6166  SDSDSYHB-3767
  if (sbqylx == '2' && (gzsbbz == 'Y' || gzsbbz == 'zx')) {
    var fpbl = calFpbl();
    var fpse = calFzjgfpsdseBq();
    if (validation().fpbl == 'N') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.fpbl = fpbl;
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.fpbl');
    }

    if (validation().fzjgfpse == 'N') {
      formData.ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq = fpse;
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.fzjgfpsdseBq');
    }
  }

  /*
   *  XMDZSWJ-2646
   *  sbqylx=1 , gzsbbz==Y
   *  13行 更正初始化 清除（JMSE00604）、04033308（JMSE00601）、04039902（JMSE00606)填写了相关栏区域性优惠政策
   *
   */
  if (sbqylx == 1 && gzsbbz == 'Y') {
    //西部大开发区域
    var xbdkfdqlx = formData.kz_.xbdkfdqlx;
    //广东横琴等区域
    var glcylx = formData.kz_.glcylx;
    //海南自由贸易区 区域
    var hndqlx = formData.kz_.hndqlx;
    var sdseyhInfo = '';
    if (xbdkfdqlx != 'Y') {
      if (
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].yhjmje > 0 ||
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_jmsdssxSelect == true ||
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].zyywsrzb > 0
      ) {
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].zyywsrzb = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_jmsdssxSelect = false;
        sdseyhInfo += '0,';
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].zyywsrzb');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].yhjmje');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_jmsdssxSelect');
      }
    }
    if (glcylx != 'Y') {
      if (
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].yhjmje > 0 ||
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_jmsdssxSelect == true
      ) {
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_jmsdssxSelect = false;
        sdseyhInfo += '1,';
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].zyywsrzb');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].yhjmje');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_jmsdssxSelect');
      }
    }
    if (hndqlx != 'Y') {
      if (
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].yhjmje > 0 ||
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_jmsdssxSelect == true
      ) {
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].yhjmje = 0;
        formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_jmsdssxSelect = false;
        sdseyhInfo += '2,';
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].zyywsrzb');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].yhjmje');
        jpathList.push('ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_jmsdssxSelect');
      }
    }
    formData.kz_.sdseyhInfo = sdseyhInfo;
    jpathList.push('kz_.sdseyhInfo');
  }

  //JSONE-21114  企业类型为“一般企业”或“跨地区经营汇总纳税企业总机构”，且是季报或季末月报时的纳税人填报，否者锁定不可填写。
  if (sbqylx == '2' && (gzsbbz == 'Y' || gzsbbz == 'zx')) {
    formData.ht_.ywbw.A200000Ywbd.sbxx.qycyrsQnpjrs = '';
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.qycyrsQnpjrs');
  }

  if (jpathList.length > 0) {
    formulaEngine.apply4List(jpathList);
  }

  initTdstx();

  //XMDZSWJ-2902(暂存或者更正时 分支机构信息里面的zgswjgDm、zgswjgmc没有实时取期初数里面的 导致有变更后 公式规则用旧的数据判断 ) start
  var dzbdlist = document.getElementById('dzbdbmList').value;
  if (dzbdlist.indexOf('BDA0611037') > -1) {
    var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
    var a2020Data = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
    var fzjgnsrsbhObj = {};
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      var fzjgnsrsbh = fzjgxxGridlb[i].fzjgnsrsbh;
      var zgswjgDm = isNull(fzjgxxGridlb[i].zgswjgDm) ? '' : fzjgxxGridlb[i].zgswjgDm;
      var zgswjgmc = isNull(fzjgxxGridlb[i].zgswjgmc) ? '' : fzjgxxGridlb[i].zgswjgmc;
      var zgswjgxxObj = {
        zgswjgDm: zgswjgDm,
        zgswjgmc: zgswjgmc,
      };
      fzjgnsrsbhObj[fzjgnsrsbh] = zgswjgxxObj;
    }

    for (var i = 0; i < a2020Data.length; i++) {
      var nsrsbh = a2020Data[i].nsrsbh;
      var zgswjgxxObj = fzjgnsrsbhObj[nsrsbh];
      if (!isNull(zgswjgxxObj)) {
        var zgswjgDm = zgswjgxxObj.zgswjgDm;
        var zgswjgmc = zgswjgxxObj.zgswjgmc;
        formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].zgswjgDm = zgswjgDm;
        formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[i].zgswjgmc = zgswjgmc;
      }
    }
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgIndex = getZjgftbz('xb');
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgftbz = getZjgftbz('bz');
    var fzjgxxjpathList = [];
    fzjgxxjpathList.push('ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].zgswjgDm');
    fzjgxxjpathList.push('ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].zgswjgmc');
    fzjgxxjpathList.push('ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgIndex');
    fzjgxxjpathList.push('ht_.ywbw.A202000Ywbd.zjgxxForm.t_zjgftbz');
    fzjgxxjpathList.push('ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[#].zcze');
    formulaEngine.apply4List(fzjgxxjpathList);
  }
  //XMDZSWJ-2902 end

  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var xgrqDate = new Date('2021-09-30');
  var skssqzDate = new Date(skssqz);
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
  if (skssqzDate >= xgrqDate && sbqylx != '2' && yf_z >= 10) {
    var dsjdhjyJmxx = formData.fq_.dsjdhjyJmxx;
    if (!isNull(dsjdhjyJmxx)) {
      var jmxxList = dsjdhjyJmxx.jmxx;
      var jjkc1je = 0;
      var jjkc1Bz = 'N';
      var jjkc2je = 0;
      var jjkc2Bz = 'N';
      var jjkc3je = 0;
      var jjkc3Bz = 'N';
      var jjkc4je = 0;
      var jjkc4Bz = 'N';
      var jjkc5je = 0;
      var jjkc5Bz = 'N';
      var jjkc6je = 0;
      var jjkc6Bz = 'N';
      var jjkc7je = 0;
      var jjkc7Bz = 'N';

      for (var i = 0; i < jmxxList.length; i++) {
        var yhswsx = jmxxList[i].yhswsx;
        var yhjmje = jmxxList[i].yhjmje;
        if (yhswsx == 'JJKC011') {
          jjkc1je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc1Bz = 'Y';
        }
        if (yhswsx == 'JJKC012') {
          jjkc2je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc2Bz = 'Y';
        }
        if (yhswsx == 'JJKC021') {
          jjkc3je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc3Bz = 'Y';
        }
        if (yhswsx == 'JJKC022') {
          jjkc4je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc4Bz = 'Y';
        }

        if (yhswsx == 'JJKC013') {
          jjkc5je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc5Bz = 'Y';
        }
        if (yhswsx == 'JJKC023') {
          jjkc6je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc6Bz = 'Y';
        }
        if (yhswsx == 'JJKC031') {
          jjkc7je = isNull(yhjmje) ? 0 : yhjmje;
          jjkc7Bz = 'Y';
        }
      }
      var jpathList = [];
      if (jjkc1Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe = Number(jjkc1je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['22']);
      jpathList = [];
      if (jjkc2Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe = Number(jjkc2je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['23']);
      jpathList = [];
      if (jjkc3Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe = Number(jjkc3je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs = '0';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['24']);
      jpathList = [];
      if (jjkc4Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe = Number(jjkc4je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['25']);

      jpathList = [];
      if (jjkc5Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe = Number(jjkc5je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['26']);

      jpathList = [];
      if (jjkc6Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe = Number(jjkc6je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['27']);

      jpathList = [];
      if (jjkc7Bz == 'Y') {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs = 'Y';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe = Number(jjkc7je);
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe');
      } else {
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs = '';
        formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe = 0;
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrSfysqs');
        jpathList.push('ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[28].t_mssrQcsJe');
      }
      formulaEngine.apply4List(jpathList, ['28']);

      jpathList = [];
      if (jpathList.length > 0) {
        formulaEngine.apply4List(jpathList);
      }
    }
  }

  sort20000Grid();
  //XMDZSWJ-3402
  if (swjgDm05 == '13502') {
    var sbqylx = formData.ht_.ywbw.A200000Ywbd.nsrxx.sbqylx;
    var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
    //申报期止月份
    var sbyf = parseInt(skssqz.split('-')[1], 10);
    if (sbqylx != '2' && (sbyf == 3 || sbyf == 6 || sbyf == 9 || sbyf == 12)) {
      formData.ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy = 'N';
      var jpathList = [];
      jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.gjxzhjzhy');
      formulaEngine.apply4List(jpathList);
    }
  }

  //*********-5296
  if (gzsbbz == 'Y' || gzsbbz == 'zx') {
    if (yjfs == '1' && (sbqylx == '0' || sbqylx == '1')) {
      var sfs21jyhnd = formData.kz_.sfs21jyhnd;
      var hydm = formData.fq_.nsrjbxx.hydm;
      if (swjgDm05 == '13502' && sfs21jyhnd == 'N') {
        if (hydm >= 4710 && hydm <= 5090) {
          formData.ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj = ROUND(
            formData.hq_.qtxx.jaqyYjze + formData.hq_.sqsbxx.tdywyjzsdseLj,
            2,
          );
        } else {
          formData.ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj = 0;
        }
      } else {
        formData.ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj = ROUND(
          formData.hq_.qtxx.jaqyYjze + formData.hq_.sqsbxx.tdywyjzsdseLj,
          2,
        );
      }
    } else {
      formData.ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj = 0;
    }
    var jpathList = [];
    jpathList.push('ht_.ywbw.A200000Ywbd.sbxx.tdywyjzsdseLj');
    formulaEngine.apply4List(jpathList);
  }

  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var sbnd = parseInt(skssqz.split('-')[0], 10);
  if (sbnd >= 2022) {
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_fsbxSelect = false;
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].jehxxz = 0;
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqje = 0;
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].t_sqxszc = '';
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].xszc = '';
    formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].xxbz = 'N';
  }
}

/**
 * 退抵税提醒，初始化阻断，遮罩层
 */
function initTdstx() {
  var sssqQ = $('#sssqQ').val();
  var sssqZ = $('#sssqZ').val();
  var swjgDm = formData.fq_.nsrjbxx.swjgDm.substring(1, 3);
  var nowData = new Date();
  var month = nowData.getMonth() + 1;
  month = month < 10 ? '0' + month : month;
  var sysYearMonth = nowData.getFullYear() + '-' + month;
  var monthFlagStr = '';
  if (sssqQ == '2021-05-01' && sssqZ == '2021-05-31') {
    monthFlagStr = '06';
  } else if ((sssqQ == '2021-06-01' && sssqZ == '2021-06-30') || (sssqQ == '2021-04-01' && sssqZ == '2021-06-30')) {
    monthFlagStr = '07';
  } else if (
    sssqQ.substring(0, 4) >= 2022 &&
    ((sssqQ.substring(5, 10) == '06-01' && sssqZ.substring(5, 10) == '06-30') ||
      (sssqQ.substring(5, 10) == '04-01' && sssqZ.substring(5, 10) == '06-30'))
  ) {
    monthFlagStr = '2022_07';
  }
  if (swjgDm == '11' && (monthFlagStr == null || monthFlagStr == '')) {
    return;
  }
  var params = {};
  params.djxh = formData.fq_.nsrjbxx.djxh;
  params.zfjglxDm = formData.fq_.nsrjbxx.zfjglxDm;
  params.nsrmc = formData.fq_.nsrjbxx.nsrmc;
  params.nsrzt = formData.fq_.nsrjbxx.nsrztDm;
  params.sid = 'dzswj.ywzz.sb.qysdsa21yjd.queryTdsxxData';
  params.sssqQ = sssqQ;
  params.sssqZ = sssqZ;
  params.swjgDm = formData.fq_.nsrjbxx.swjgDm;

  parent.requestYwztData(
    params,
    function (data) {
      if (!data) {
        return;
      }
      if (typeof data === 'string') {
        data = JSON.parse(data);
      }

      var tdstxbz = data.tdstxbz;
      var djskhj = data.djskhj;
      var xthtUrl = data.xthtUrl;
      var tsgnUrl = data.tsgnUrl;
      var wstsgnUrl = data.wstsgnUrl;
      var tipsTime = data.tipsTime;

      if (tdstxbz == 'Y') {
        //全国版显示多缴信息
        if (swjgDm != '11') {
          var djxx = data.djxx;
          var trs = '';
          var msgTip =
            '您存在企业所得税多缴税款，建议申请办理退税。请逐项选择“申请退税”或“暂不办理”，并确认。选择申请退税，将自动转入退税申请业务。';
          var djxh = formData.fq_.nsrjbxx.djxh;
          for (var i in djxx) {
            var djxxObj = djxx[i];
            var nd = djxxObj.skssqq.substr(0, 4);
            var ssqNd = djxxObj.skssqq.substr(0, 4) + '年';
            var ttsjlxMc = djxxObj.ttsjlxDm == '03' ? '汇缴退税' : djxxObj.ttsjlxDm == '04' ? '误收退税' : '';
            var swsxDm = djxxObj.ttsjlxDm == '03' ? 'SXA071008003' : djxxObj.ttsjlxDm == '04' ? 'SXA071008006' : '';
            trs += '<tr>';
            trs += '<td align="center"><span>' + ssqNd + '</span></td>';
            trs += '<td align="center"><span>' + djxxObj.se + '</p></span></td>';
            trs += "<td align='center'><span>" + ttsjlxMc + '</p></span></td>';
            //	            trs+="<td align='center'><span><a class=\"layui-btn layui-btn-primary layui-btn-xs\" href=\"javascript:window.open(window.location.protocol + '//' + window.location.host+'/sxsq-cjpt-web/biz/sxsq/djskjtflxbl?swsxDm="+swsxDm+"&tdsfsDm=1');\" >申请退税</a><a class=\"layui-btn layui-btn-primary layui-btn-xs\" href=\"javascript:void(0)\" onclick=\"parent.parent.window[0].bccjbztzs("+djxh+")\">暂不办理</a></span></td>";
            trs +=
              '<td align=\'center\'><input type="radio" class="radioTs"  name="radioTs' +
              i +
              '" lay-filter= "menutype' +
              i +
              '" value="' +
              nd +
              '_sqts' +
              swsxDm +
              '"/></td>';
            trs +=
              '<td align=\'center\'><input type="radio" class="radioTs"  name="radioTs' +
              i +
              '"  lay-filter= "menutype' +
              i +
              '" value="' +
              nd +
              '_zbbl"/></td>';
            trs += '</tr>';
            index++;
          }
          layui.use(['form', 'laypage', 'layer'], function () {
            var form = layui.form;
            var laypage = layui.laypage;
            var layer = layui.layer;
            form.on('checkbox(menutype)', function (data) {
              var data = data;
            });

            msg =
              '<div>\n' +
              "       <div class='sfxz-box'>\n" +
              "       <div class='sfxz-search' style=' margin-top: 0px'>\n" +
              '       <div>\n' +
              '&nbsp &nbsp &nbsp &nbsp' +
              msgTip +
              '       </div>\n' +
              '   </div>\n' +
              '   </div>\n' +
              "   <div class='sfxz-table'>\n" +
              "       <form class='layui-form'>\n" +
              "       <table class='layui-table table-radio-center'>\n" +
              '       <thead>\n' +
              '       <tr>\n' +
              '   <th>税款所属年度 </th>\n' +
              '   <th>退税税款</th>\n' +
              '   <th>多缴类型</th>\n' +
              "   <th style='width: 120px;'>申请退税</th>\n" +
              "   <th style='width: 120px;'>暂不办理</th>\n" +
              '   </tr>\n' +
              trs +
              '   </tbody>\n' +
              '   </table>\n' +
              '   </form>\n' +
              '   </div>\n' +
              '   </div>';
            layer.open({
              type: 1,
              area: ['900px', '420px'],
              title: ['提示'],
              scrollbar: false,
              closeBtn: 0,
              content: msg,
              btn: ['确认'],
              success: function (index) {
                form.render();
              },
              btn1: function (index, layero) {
                var xzBz = '';
                var count = 0;
                var tsqkArr = [];
                $("input:radio[class='radioTs']").each(function () {
                  count = count + 1;
                  if ($(this)[0].checked) {
                    count = count - 2;
                    xzBz = xzBz + $(this).val(); // 每一个被选中项的值
                    var xzjg = $(this).val();
                    var nd = xzjg.split('_')[0];
                    var xzqkDm = '1';
                    if (xzjg.indexOf('zbbl') != -1) {
                      xzqkDm = '2';
                    }
                    var mx = nd + '_' + xzqkDm;
                    tsqkArr.push(mx);
                  }
                });
                if (count != 0) {
                  layer.alert('请把所有年度的退税信息选择上“申请退税”或“暂不办理”！', { icon: 5, title: '提示' });
                  return;
                }

                var sytpz = data.sytpz;
                var djxx = data.djxx;
                var styRtnCode = data.styRtnCode + '';
                var gzbz = formData.kz_.temp.gzsbbz;
                var swjgDm = formData.fq_.nsrjbxx.swjgDm;
                var nsrztDm = formData.fq_.nsrjbxx.nsrztDm;
                var tzgdSty = '';
                var gdStyUrl = '';
                var djxxObj = djxx[0];
                var nd = djxxObj.skssqq.substr(0, 4);
                var sssqZ = $('#sssqZ').val();
                var dqnbsq = parseInt(sssqZ.substr(0, 4), 10) - 1;

                if (
                  !isNull(djxx) &&
                  djxx.length == 1 &&
                  nd == dqnbsq &&
                  swjgDm.substring(0, 3) == '144' &&
                  gzbz != 'Y' &&
                  gzbz != 'zx' &&
                  styRtnCode === '0' &&
                  sytpz != undefined &&
                  sytpz != null &&
                  sytpz != '' &&
                  nsrztDm != '06' &&
                  nsrztDm != '07'
                ) {
                  var tsurl = sytpz.tsurl;
                  var qdsj = sytpz.qdsj;
                  var nowDate = new Date();
                  var qdsjDate = new Date(qdsj);
                  if (nowDate >= qdsjDate && tsurl != undefined && tsurl != null && tsurl != '') {
                    if (tsurl.indexOf('nd=') == -1) {
                      if (tsurl.indexOf('?') == -1) {
                        tsurl = tsurl + '?nd=' + dqnbsq;
                      } else {
                        tsurl = tsurl + '&nd=' + dqnbsq;
                      }
                    }

                    tzgdSty = 'Y';
                    gdStyUrl = tsurl;
                  }
                }

                if (
                  tzgdSty == 'Y' &&
                  gdStyUrl != '' &&
                  gdStyUrl != null &&
                  gdStyUrl != undefined &&
                  (xzBz.indexOf('sqtsSXA071008006') != -1 || xzBz.indexOf('sqtsSXA071008003') != -1)
                ) {
                  //符合条件的名单内的 广东区域要跳转到速退易
                  toGdSty(gdStyUrl, dqnbsq);
                } else {
                  if (xzBz.indexOf('sqtsSXA071008006') != -1) {
                    var backUrl =
                      parent.location.protocol +
                      '//' +
                      parent.location.host +
                      '/sxsq-cjpt-web/biz/sxsq/djskjtflxbl?swsxDm=SXA071008006&tdsfsDm=1';
                    if (wstsgnUrl != '' && wstsgnUrl != null && wstsgnUrl != undefined) {
                      backUrl = parent.location.protocol + '//' + parent.location.host + wstsgnUrl;
                    }
                    parent.window.open(backUrl);
                  }
                  if (xzBz.indexOf('sqtsSXA071008003') != -1) {
                    var backUrl =
                      parent.location.protocol +
                      '//' +
                      parent.location.host +
                      '/sxsq-cjpt-web/biz/sxsq/djskjtflxbl?swsxDm=SXA071008003&tdsfsDm=1';
                    if (tsgnUrl != '' && tsgnUrl != null && tsgnUrl != undefined) {
                      backUrl = parent.location.protocol + '//' + parent.location.host + tsgnUrl;
                    }
                    parent.window.open(backUrl);
                  }
                }

                if (xzBz.indexOf('zbbl') != -1) {
                  bccjbztzs(parent.curDjxh);
                }
                //JSONE-22147 保存 退税选择情况
                saveTsqk(tsqkArr);
                layer.close(index);
              },
            });
          });
        } else if (monthFlagStr == '2022_07') {
          //7月份退税提醒
          var tips =
            '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;尊敬的纳税人，系统检测到您存在企业所得税多缴未退税款，系统将自动转入一键退税申请业务。';
          var timeout = 5;
          var timeoutIndex = -1;
          var isclearTime = false;

          parent.layer.open(
            {
              type: 1,
              area: ['540px', '290px'],
              title: ['提示'],
              closeBtn: 0,
              scrollbar: false,
              content: tips,
              btn: ['即将跳转'],
              //btnAlign: 'r', //按钮居中
              success: function (layero, index) {
                // 倒计时10s
                timeoutIndex = setInterval(function () {
                  layero.find('.layui-layer-btn0').addClass('layui-btn-disabled');
                  layero.find('.layui-layer-btn0').css('backgroundColor', '#f7f7f7');
                  layero.find('.layui-layer-btn0').css('color', '#bbb');
                  layero.find('.layui-layer-btn0').css('border', '1px solid #DDD');
                  layero.find('.layui-layer-btn0').attr('disabled', 'true').css('pointer-events', 'none');
                  if (timeout >= 0) {
                    var txt = '(' + timeout + ')即将跳转';
                    layero.find('.layui-layer-btn0').text(txt);
                    timeout = timeout - 1;
                  } else {
                    txt = '即将跳转';
                    layero.find('.layui-layer-btn0').text(txt);
                    isclearTime = true;
                    layero.find('.layui-layer-btn0').removeClass('layui-btn-disabled');
                    layero.find('.layui-layer-btn0').css('backgroundColor', '#53acf3');
                    layero.find('.layui-layer-btn0').css('color', '#fff');
                    layero.find('.layui-layer-btn0').css('border', '1px solid #53acf3');
                    layero.find('.layui-layer-btn0').removeAttr('disabled').css('pointer-events', 'auto');
                    //跳转文书退税
                    var skssqz = $('#sssqZ').val();
                    var sbnd = parseInt(skssqz.split('-')[0], 10) - 1;
                    var backUrl =
                      parent.location.protocol +
                      '//' +
                      parent.location.host +
                      '/sxsq-cjpt-web/sxsq/hsqjdjskyjts/main.do?qysdsyjBz=Y&ssnd=' +
                      sbnd;
                    if (xthtUrl != '' && xthtUrl != null && xthtUrl != undefined) {
                      backUrl = parent.location.protocol + '//' + parent.location.host + xthtUrl;
                    }
                    parent.window.location.href = backUrl;
                    clearInterval(timeoutIndex);
                  }
                }, 1500);
                //maskts();
              },
            },
            function (index) {
              //倒计时未结束，不让点
              if (!isclearTime) {
                return true;
              }
              parent.layer.close(index);
              //maskts();
            },
          );
        } else if (monthFlagStr == '06') {
          //6月份多缴退税提醒
          var timeout = tipsTime == '' || tipsTime == null || tipsTime == undefined ? 30 : tipsTime;
          if (window.parent.location.href.indexOf('ysdj=Y') > -1) {
            timeout = 5;
          }
          var timeoutIndex = -1;
          var isclearTime = false;

          var tips =
            '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"尊敬的纳税人您好，您存在当年（2020年）汇算清缴多缴税款' +
            djskhj +
            '元，应于7月纳税申报期前（含）通过电子税务局提交退税申请或者放弃申请退税声明"。';
          parent.layer.open(
            {
              type: 1,
              area: ['540px', '290px'],
              title: ['提示'],
              closeBtn: 0,
              scrollbar: false,
              content: tips,
              btn: ['已阅读'],
              //btnAlign: 'r', //按钮居中
              success: function (layero, index) {
                // 倒计时10s
                timeoutIndex = setInterval(function () {
                  layero.find('.layui-layer-btn0').addClass('layui-btn-disabled');
                  layero.find('.layui-layer-btn0').css('backgroundColor', '#f7f7f7');
                  layero.find('.layui-layer-btn0').css('color', '#bbb');
                  layero.find('.layui-layer-btn0').css('border', '1px solid #DDD');
                  layero.find('.layui-layer-btn0').attr('disabled', 'true').css('pointer-events', 'none');
                  if (timeout >= 0) {
                    var txt = '(' + timeout + ')已阅读';
                    layero.find('.layui-layer-btn0').text(txt);
                    timeout = timeout - 1;
                  } else {
                    txt = '已阅读';
                    layero.find('.layui-layer-btn0').text(txt);
                    isclearTime = true;
                    layero.find('.layui-layer-btn0').removeClass('layui-btn-disabled');
                    layero.find('.layui-layer-btn0').css('backgroundColor', '#53acf3');
                    layero.find('.layui-layer-btn0').css('color', '#fff');
                    layero.find('.layui-layer-btn0').css('border', '1px solid #53acf3');
                    layero.find('.layui-layer-btn0').removeAttr('disabled').css('pointer-events', 'auto');
                    clearInterval(timeoutIndex);
                  }
                }, 1500);
                //maskts();
              },
            },
            function (index) {
              //倒计时未结束，不让点
              if (!isclearTime) {
                return true;
              }
              parent.layer.close(index);
              //maskts();
            },
          );
        } else if (monthFlagStr == '07') {
          //7月份退税提醒
          var msg =
            '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"尊敬的纳税人您好，您存在当年（2020年）汇算清缴多缴税款，应于7月纳税申报期内通过电子税务局提交退税申请或者放弃申请退税声明，如暂不提出申请，则不能正常报送完成申报"。';
          parent.layer.open({
            type: 1,
            area: ['520px', '400px'],
            title: ['提示'],
            scrollbar: false,
            content: msg,
            btn: ['申请退税', '放弃申请退税', '退出'],
            //btnAlign: 'r', //按钮居中
            btn1: function (index, layero) {
              parent.layer.closeAll();
              maskts();
              var backUrl =
                parent.location.protocol +
                '//' +
                parent.location.host +
                '/sxsq-cjpt-web/sxsq/hsqjdjskyjts/main.do?sbBz=Y';
              if (xthtUrl != '' && xthtUrl != null && xthtUrl != undefined) {
                backUrl = parent.location.protocol + '//' + parent.location.host + xthtUrl;
              }
              parent.window.open(backUrl);
            },
            btn2: function (index, layero) {
              parent.layer.close(index);
              fqsqts(data);
            },
            btn3: function (index, layero) {
              var timeout = tipsTime == '' || tipsTime == null || tipsTime == undefined ? 30 : tipsTime;
              if (window.parent.location.href.indexOf('ysdj=Y') > -1) {
                timeout = 5;
              }
              var timeoutIndex = -1;
              var isclearTime = false;

              var tips =
                '您的多缴税款应于本月纳税申报期内提交退税申请或者放弃申请退税声明，否则将无法在7月纳税申报期正常完成企业所得税月（季）度预缴纳税申报，退出后可以到电子税务局内相应功能模块办理退税申请及放弃申请退税声明。';
              parent.layer.open(
                {
                  type: 1,
                  area: ['540px', '290px'],
                  title: ['提示'],
                  closeBtn: 0,
                  scrollbar: false,
                  content: tips,
                  btn: ['已阅读'],
                  //btnAlign: 'r', //按钮居中
                  success: function (layero, index) {
                    // 倒计时10s
                    timeoutIndex = setInterval(function () {
                      layero.find('.layui-layer-btn0').addClass('layui-btn-disabled');
                      layero.find('.layui-layer-btn0').css('backgroundColor', '#f7f7f7');
                      layero.find('.layui-layer-btn0').css('color', '#bbb');
                      layero.find('.layui-layer-btn0').css('border', '1px solid #DDD');
                      layero.find('.layui-layer-btn0').attr('disabled', 'true').css('pointer-events', 'none');
                      if (timeout >= 0) {
                        var txt = '(' + timeout + ')已阅读';
                        layero.find('.layui-layer-btn0').text(txt);
                        timeout = timeout - 1;
                      } else {
                        txt = '已阅读';
                        layero.find('.layui-layer-btn0').text(txt);
                        isclearTime = true;
                        layero.find('.layui-layer-btn0').removeClass('layui-btn-disabled');
                        layero.find('.layui-layer-btn0').css('backgroundColor', '#53acf3');
                        layero.find('.layui-layer-btn0').css('color', '#fff');
                        layero.find('.layui-layer-btn0').css('border', '1px solid #53acf3');
                        layero.find('.layui-layer-btn0').removeAttr('disabled').css('pointer-events', 'auto');
                        clearInterval(timeoutIndex);
                      }
                    }, 1500);
                    maskts();
                  },
                },
                function (index) {
                  //倒计时未结束，不让点
                  if (!isclearTime) {
                    return true;
                  }
                  parent.layer.close(index);
                  maskts();
                },
              );
            },
            cancel: function () {
              closeWin();
            },
          });
        }
      }
    },
    function (data) {
      console.log('查询退抵税信息异常', data);
      maskts();
    },
  );
}

/**
 * 放弃申请退税
 */
function fqsqts(data) {
  var ydtuuid = data.ydtuuid;
  var djskssqq = data.djskssqq;
  var djskssqz = data.djskssqz;
  var djfsrq = data.djfsrq;
  var ttsjlx_dm = data.ttsjlx_dm;
  var se1 = data.se1;
  var se = data.se;
  var ydtlyuuid = data.ydtlyuuid;
  var nd = data.nd;
  var fqsqtssmUrl = data.fqsqtssmUrl;

  var msg =
    '尊敬的纳税人您好，多缴税款申请退税及放弃申请退税均是您的合法权利，如您确定放弃申请退税，该笔放弃申请的退税将无法再次选择退税，请慎重选择。';
  parent.layer.open({
    type: 1,
    area: ['520px', '400px'],
    title: ['提示'],
    scrollbar: false,
    content: msg,
    btn: ['确定', '取消'],
    //btnAlign: 'r', //按钮居中
    yes: function (index, layero) {
      parent.layer.closeAll();
      maskts();
      var backUrl = parent.location.protocol + '//' + parent.location.host + '/sxsq-cjpt-web/biz/sxsq/fqsqtssm';
      if (fqsqtssmUrl != '' && fqsqtssmUrl != null && fqsqtssmUrl != undefined) {
        backUrl = parent.location.protocol + '//' + parent.location.host + fqsqtssmUrl;
      }
      backUrl =
        backUrl +
        '?swsxDm=SXA072300001&ydtuuid=' +
        ydtuuid +
        '&zsxm_dm=10104&skssqq=' +
        djskssqq +
        '&skssqz=' +
        djskssqz +
        '&djfsrq=' +
        djfsrq +
        '&ttsjlx_dm=' +
        ttsjlx_dm +
        '&se1=' +
        se1 +
        '&se=' +
        se +
        '&ydtlyuuid=' +
        ydtlyuuid +
        '&nd=' +
        nd +
        '';
      parent.window.open(backUrl);
    },
    btn2: function (index, layero) {
      parent.layer.closeAll();
      maskts();
    },
    cancel: function () {
      closeWin();
    },
  });
}

/**
 * 退税遮罩层
 */
function maskts() {
  $('body').mask('');
  $('.areaHeadBtn .btn', parent.document).hide();
}

function setSfsJmyb(sqq, sqz) {
  var sqqMonth = parseInt(sqq.split('-')[1]);
  var sqzMonth = parseInt(sqz.split('-')[1]);
  if (
    (sqzMonth - sqqMonth === 0 && (sqzMonth === 3 || sqzMonth === 6 || sqzMonth === 9 || sqzMonth === 12)) ||
    sqzMonth - sqqMonth === 2
  ) {
    return 'Y';
  } else {
    return 'N';
  }
}

/**
 * 厦门个性化,在主表第13栏有填写区域性优惠JMSE00601,JMSE00604,JMSE00606这三项任意一项或多项,
 * A202000分配表多个符合条件的分支机构的“分支机构享受区域性优惠情况”自动带出相关优惠事项后，
 * 至少保留一条（多个分支机构符合选中同一种优惠，可修改取消，但每一种优惠确保有一分支机构选上）
 */
function atleastOne(dm) {
  var fzjgxxGridlb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var xbdkfNum = 0;
  var hnzmgNum = 0;
  var zhptszNum = 0;
  var hqNum = 0;
  var fjPtNum = 0;
  var szQhNum = 0;
  var gdNsNum = 0;
  //每次操作先遍历统计
  for (var i = 0; i < fzjgxxGridlb.length; i++) {
    //当统计各自有数据时，直接break; 减少循环次数
    if (
      (dm == '01' && xbdkfNum != 0) ||
      (dm == '02' && hnzmgNum != 0) ||
      (dm == '03' && zhptszNum != 0) ||
      (dm == '04' && hqNum != 0) ||
      (dm == '05' && fjPtNum != 0) ||
      (dm == '06' && szQhNum != 0) ||
      (dm == '07' && gdNsNum != 0)
    ) {
      break;
    }
    //该行符合西部大开发的 分支机构可选上
    if (
      isXbdkfdq(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '01') {
        xbdkfNum++;
      }
    }
    //该行符合海南自贸港的 分支机构可选上
    if (
      isHnzmg(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i], fzjgxxGridlb[i].t_fzjgnsrsbh_fzjglxlb) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '02') {
        hnzmgNum++;
      }
    }
    //该行符合珠海平潭深圳的 分支机构可选上
    if (
      isZhPtSz(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '03') {
        zhptszNum++;
      }
    }
    //减免方式细分 福建平潭
    if (
      isFjptlx(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '05') {
        zhptszNum++;
        fjPtNum++;
      }
    }
    //减免方式细分 深圳前海
    if (
      isSzqhlx(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '06') {
        zhptszNum++;
        szQhNum++;
      }
    }
    //减免方式细分 广东横琴
    if (
      isDdhqlx(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '04') {
        zhptszNum++;
        hqNum++;
      }
    }

    //减免方式细分 广东南沙
    if (
      isGdnslx(fzjgxxGridlb[i].zgswjgDm, fzjgxxGridlb[i].zgswjgmc, [i]) == true &&
      formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_jmsdssxSelect == true
    ) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '07') {
        gdNsNum++;
      }
    }
  }
  //根据条件返回
  if (dm == '01') {
    return xbdkfNum;
  } else if (dm == '02') {
    return hnzmgNum;
  } else if (dm == '03') {
    return zhptszNum;
  } else if (dm == '04') {
    return hqNum;
  } else if (dm == '05') {
    return fjPtNum;
  } else if (dm == '06') {
    return szQhNum;
  } else if (dm == '07') {
    return gdNsNum;
  } else {
    //都没有，则返回-1
    return -1;
  }
}

function checkAtleastOne_Xbdkfdq(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var xbdkfNum = atleastOne('01');
  //分情况校验 至少保留一条
  //该行符合西部大开发 分支机构可选上的
  if (
    isXbdkfdq(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[28].t_jmsdssxSelect == true
  ) {
    if (xbdkfNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}
function checkAtleastOne_Hnzmg(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var hnzmgNum = atleastOne('02');
  var fzjgnsrsbh = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb[row].t_fzjgnsrsbh_fzjglxlb;
  //分情况校验 至少保留一条
  //该行符合海南自贸港 分支机构可选上的
  if (
    isHnzmg(zgswjgDm, zgswjgmc, row, fzjgnsrsbh) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[33].t_jmsdssxSelect == true
  ) {
    if (hnzmgNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

function checkAtleastOne_Gdhq(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');
  if (skssqzDate < hbgDate) {
    return true;
  }

  var hqNum = atleastOne('04');
  //分情况校验 至少保留一条
  if (
    isDdhqlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_jmsdssxSelect == true
  ) {
    if (hqNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

function checkAtleastOne_Fjpt(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');
  if (skssqzDate < hbgDate) {
    return true;
  }
  var hqNum = atleastOne('05');
  //分情况校验 至少保留一条
  if (
    isFjptlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_jmsdssxSelect == true
  ) {
    if (hqNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

function checkAtleastOne_Szqh(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');
  if (skssqzDate < hbgDate) {
    return true;
  }
  var hqNum = atleastOne('06');
  //分情况校验 至少保留一条
  if (
    isSzqhlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_jmsdssxSelect == true
  ) {
    if (hqNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

function checkAtleastOne_Gdns(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');
  if (skssqzDate < hbgDate) {
    return true;
  }
  var hqNum = atleastOne('07');
  //分情况校验 至少保留一条
  if (
    isGdnslx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[42].t_jmsdssxSelect == true
  ) {
    if (hqNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

function checkAtleastOne_ZhPtSz(zgswjgDm, zgswjgmc, row, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');
  if (skssqzDate >= hbgDate) {
    return true;
  }

  var zhptszNum = atleastOne('03');
  //分情况校验 至少保留一条
  //该行符合珠海平潭深圳 分支机构可选上的
  if (
    isZhPtSz(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[31].t_jmsdssxSelect == true
  ) {
    if (zhptszNum >= 1) {
      return true;
    }
    return false;
  } else if (
    isFjptlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[39].t_jmsdssxSelect == true
  ) {
    if (zhptszNum >= 1) {
      return true;
    }
    return false;
  } else if (
    isSzqhlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[40].t_jmsdssxSelect == true
  ) {
    if (zhptszNum >= 1) {
      return true;
    }
    return false;
  } else if (
    isDdhqlx(zgswjgDm, zgswjgmc, row) == true &&
    formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[41].t_jmsdssxSelect == true
  ) {
    if (zhptszNum >= 1) {
      return true;
    }
    return false;
  } else {
    //都没有一个符合的分支机构，触发不校验，返回true
    return true;
  }
}

//version为1下载空模板，version为2导出带数据excel
function getSbzxDemoParams(sbywbm, ywlx, version) {
  if (version == '1') {
    var exportParams = {
      sbywbm: sbywbm,
      ywlx: ywlx,
      version: version,
    };
    var skssqq =
      $(window.frames['frmSheet'].document).find('#skssq').val() ||
      $(window.frames['frmSheet'].document).find('#skssqq').val();
    var skssqz = $(window.frames['frmSheet'].document).find('#skssqz').val();
    var djxh = $('#djxh').val();
    exportParams['skssqq'] = skssqq;
    exportParams['skssqz'] = skssqz;
    exportParams['djxh'] = djxh;
  } else if (version == '2') {
    var fzjgxxGridLb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb || [];
    var zjgxxForm = formData.ht_.ywbw.A202000Ywbd.zjgxxForm || '';
    var skssqq = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq;
    var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
    var fpb = formData.kz_.temp.fpb;
    var req_fzjgxxGridLb = [];
    for (var i = 0; i < fzjgxxGridLb.length; i++) {
      req_fzjgxxGridLb.push({
        nsrsbh: fzjgxxGridLb[i].nsrsbh || '',
        fzjgmc: fzjgxxGridLb[i].fzjgmc || '',
        srze: fzjgxxGridLb[i].srze || 0,
        gzze: fzjgxxGridLb[i].gzze || 0,
        zcze: fzjgxxGridLb[i].zcze || 0,
        fpbl: fzjgxxGridLb[i].fpbl || 0,
        fzjgxsqyxyhqk: formCT.fzjgxsyhqkCT[fzjgxxGridLb[i].fzjgxsqyxyhqk] || '',
        fzjgfpse: fzjgxxGridLb[i].fzjgfpse || 0,
        xsdfjmje: fzjgxxGridLb[i].xsdfjmje || 0,
        xsdfjmfd: fzjgxxGridLb[i].xsdfjmfd || 0,
      });
    }

    var exportParams = {
      A202000Ywbd: {
        fzjgxxGrid: {
          fzjgxxGridlb: req_fzjgxxGridLb || {},
        },
        zjgxxForm: {
          fzjgftdsdse: zjgxxForm.fzjgftdsdse || 0,
          ynsdse: zjgxxForm.ynsdse || 0,
          zjgczjzfpsdse: zjgxxForm.zjgczjzfpsdse || 0,
          zjgftsdse: zjgxxForm.zjgftsdse || 0,
          zjgmc: zjgxxForm.zjgmc || '',
          zjgnsrsbh: zjgxxForm.zjgnsrsbh || '',
          t_fpblHj: zjgxxForm.t_fpblHj || 0,
          t_fpseHj: zjgxxForm.t_fpseHj || 0,
        },
        kz: {
          zsGridLb: {
            skssqq: skssqq || '',
            skssqz: skssqz || '',
            t_fzjgsrzeHj: fpb.t_fzjgsrzeHj || 0,
            t_fzjggzzeHj: fpb.t_fzjggzzeHj || 0,
            t_fzjgzczeHj: fpb.t_fzjgzczeHj || 0,
            t_yhjeHj: fpb.t_yhjeHj || 0,
          },
        },
      },
    };
  }
  return exportParams;
}

//月季报导出功能
function sbzxBatchImportValidate(analysisJson) {
  var analysisJsonObject = JSON.parse(analysisJson);
  if (analysisJsonObject.errorMsgList && analysisJsonObject.errorMsgList.length > 0) {
    var errorMsg = analysisJsonObject.errorMsgList.join(';');
    layer.alert(errorMsg, { icon: 0 });
    return false;
  }

  // 1、核心qcs的fzjgxxGridlb构造索引，方便比对
  var hqFzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
  var hqFzjgxxGridlbIdx = {};
  var hqFzjgxxFzjgmcArr;
  var hqFzjgxxFzjgmcObj;
  for (var i = 0; i < hqFzjgxxGridlb.length; i++) {
    hqFzjgxxFzjgmcArr = hqFzjgxxGridlbIdx[hqFzjgxxGridlb[i].fzjgnsrsbh];
    if (!hqFzjgxxFzjgmcArr) {
      hqFzjgxxFzjgmcArr = [];
    }
    hqFzjgxxFzjgmcObj = {};
    hqFzjgxxFzjgmcObj.fzjgmc = hqFzjgxxGridlb[i].fzjgmc;
    //对应核心期初数的下标
    hqFzjgxxFzjgmcObj.idx = i;
    hqFzjgxxFzjgmcArr.push(hqFzjgxxFzjgmcObj);
    //以纳税人识别号为key的数组
    hqFzjgxxGridlbIdx[hqFzjgxxGridlb[i].fzjgnsrsbh] = hqFzjgxxFzjgmcArr;
  }

  var hqFzjgxxGridlbIdxCopy = jQuery.extend(true, {}, hqFzjgxxGridlbIdx);
  // 2、遍历excel返回的fzjgxxGrid进行数据校验

  var viewfzjgxxGridlb = analysisJsonObject.data.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var fzjgxxGrid = {};
  var fzjgxxGridArr = [];
  //excel对应节点补全以及数据转化为相应码表key值
  for (var i = 0; i < viewfzjgxxGridlb.length; i++) {
    //补全节点
    viewfzjgxxGridlb[i].t_fzjgnsrsbh_fzjglxlb = '';
    viewfzjgxxGridlb[i].fzjglxlb = '';
    viewfzjgxxGridlb[i].zgswjgmc = '';
    viewfzjgxxGridlb[i].sfxsdfjm = '';
    viewfzjgxxGridlb[i].zgswjgDm = '';
    viewfzjgxxGridlb[i].zgswjgDm = '';
    viewfzjgxxGridlb[i].djxh = 0;
    if (isNotEmptyObj(viewfzjgxxGridlb[i].fzjgxsqyxyhqk)) {
      if (viewfzjgxxGridlb[i].fzjgxsqyxyhqk.indexOf('西部大开发') > -1) {
        viewfzjgxxGridlb[i].fzjgxsqyxyhqk = '01';
      } else if (viewfzjgxxGridlb[i].fzjgxsqyxyhqk.indexOf('海南自贸港') > -1) {
        viewfzjgxxGridlb[i].fzjgxsqyxyhqk = '02';
      } else if (viewfzjgxxGridlb[i].fzjgxsqyxyhqk.indexOf('广东横琴、福建平潭、深圳前海') > -1) {
        viewfzjgxxGridlb[i].fzjgxsqyxyhqk = '03';
      } else {
        viewfzjgxxGridlb[i].fzjgxsqyxyhqk = '';
      }
    }
    //剔除nsrsbh或纳税人名称为空的无效数据
    if (viewfzjgxxGridlb[i].nsrsbh != '' || viewfzjgxxGridlb[i].fzjgmc != '') {
      fzjgxxGridArr.push(viewfzjgxxGridlb[i]);
    }
  }
  fzjgxxGrid.fzjgxxGridlb = fzjgxxGridArr;
  var tisMsg = '';
  var fzjgxxObjArr = [];
  var fzjgxxObj;
  var fzjgmc;
  // excel数据跟qcs数据进行比较
  for (var i = 0; i < fzjgxxGridArr.length; i++) {
    var fzjgxxGridlb = fzjgxxGridArr[i];
    //excel与qcs的nsrsbh进行匹配
    hqFzjgxxFzjgmcArr = hqFzjgxxGridlbIdx[fzjgxxGridlb.nsrsbh];
    //将比配不通过的错误数据记录下来
    if (!hqFzjgxxFzjgmcArr) {
      tisMsg =
        tisMsg +
        '第' +
        (i + 9) +
        '行[分支机构统一社会信用代码（纳税人识别号）]填写错误，请修改导入模版后重新导入。<br/>';
      continue;
    } else {
      var flag = false;
      var idx;
      var hqFzjgxxFzjgmcStr = '';
      for (var j = 0; j < hqFzjgxxFzjgmcArr.length; j++) {
        hqFzjgxxFzjgmcObj = hqFzjgxxFzjgmcArr[j];
        fzjgmc = hqFzjgxxFzjgmcObj.fzjgmc;
        if (j == hqFzjgxxFzjgmcArr.length - 1) {
          hqFzjgxxFzjgmcStr = hqFzjgxxFzjgmcStr + fzjgmc;
        } else {
          hqFzjgxxFzjgmcStr = hqFzjgxxFzjgmcStr + fzjgmc + '、';
        }

        if (fzjgmc == fzjgxxGridlb.fzjgmc) {
          flag = true;
          idx = hqFzjgxxFzjgmcObj.idx;
        }
      }

      if (!flag) {
        tisMsg =
          tisMsg +
          '第' +
          (i + 9) +
          '行[分支机构名称]填写错误，正确的名称为[' +
          hqFzjgxxFzjgmcStr +
          ']，请修改模版后重新导入<br/>';
        continue;
      }

      // 校验通过才需要设置值，补导入的excel数据中fzjgxxGridlb节点下不存在的节点
      if (tisMsg === '') {
        if (idx !== '' && idx !== null && idx !== undefined) {
          fzjgxxGridlb['sfxsdfjm'] = hqFzjgxxGridlb[idx].sfxsdfjm;
          fzjgxxGridlb['xsdfjmfd'] = hqFzjgxxGridlb[idx].xsdfjmfd;
          fzjgxxGridlb['xsdfjmje'] = hqFzjgxxGridlb[idx].xsdfjmje;
          fzjgxxGridlb['fzjgdjxh'] = hqFzjgxxGridlb[idx].fzjgdjxh;
          fzjgxxGridlb['fzjglxlb'] = hqFzjgxxGridlb[idx].fzjglxlb;
        }
      }

      // 比对完后，删除hqFzjgxxGridlbIdxCopy中相应节点
      hqFzjgxxFzjgmcArr = hqFzjgxxGridlbIdxCopy[fzjgxxGridlb.nsrsbh];
      if (hqFzjgxxFzjgmcArr) {
        if (hqFzjgxxFzjgmcArr.length > 0) {
          for (var j = 0; j < hqFzjgxxFzjgmcArr.length; j++) {
            hqFzjgxxFzjgmcObj = hqFzjgxxFzjgmcArr[j];
            fzjgmc = hqFzjgxxFzjgmcObj.fzjgmc;
            if (fzjgmc == fzjgxxGridlb.fzjgmc) {
              hqFzjgxxFzjgmcArr.splice(j, 1);
              if (hqFzjgxxFzjgmcArr.length === 0) {
                delete hqFzjgxxGridlbIdxCopy[fzjgxxGridlb.nsrsbh];
              }
              break;
            }
          }
        } else {
          delete hqFzjgxxGridlbIdxCopy[fzjgxxGridlb.nsrsbh];
        }
      } else {
        fzjgxxObj = {};
        fzjgxxObj.fzjgnsrsbh = fzjgxxGridlb.nsrsbh;
        fzjgxxObj.fzjgmc = fzjgxxGridlb.fzjgmc;
        fzjgxxObj.idx = i + 9;
        fzjgxxObjArr.push(fzjgxxObj);
      }
    }
  }

  //excel记录跟qcs的记录对应不上的校验
  if (tisMsg === '') {
    //删除已qcs与excel匹配的数据后，如hqFzjgxxGridlbIdxCopy剩余则表示excel导入的数据少于qcs
    if (!jQuery.isEmptyObject(hqFzjgxxGridlbIdxCopy)) {
      //excel比期初数少
      tisMsg = tisMsg + '您导入的数据缺少如下分支机构：对应的分支机构识别号和分支机构名称为';
      for (var key in hqFzjgxxGridlbIdxCopy) {
        hqFzjgxxFzjgmcArr = hqFzjgxxGridlbIdxCopy[key];
        for (var j = 0; j < hqFzjgxxFzjgmcArr.length; j++) {
          hqFzjgxxFzjgmcObj = hqFzjgxxFzjgmcArr[j];
          fzjgmc = hqFzjgxxFzjgmcObj.fzjgmc;
          tisMsg = tisMsg + '[' + key + '、' + fzjgmc + ']、';
        }
      }
      tisMsg = tisMsg.substring(0, tisMsg.length - 1);
      tisMsg = tisMsg + '，请在模版增加后重新导入。<br/>';
    }

    if (fzjgxxObjArr.length > 0) {
      //excel比期初数多
      var idxMsg = '',
        fzjgxxMsg = '';
      for (var i = 0; i < fzjgxxObjArr.length; i++) {
        fzjgxxObj = fzjgxxObjArr[i];
        if (i == fzjgxxObjArr.length - 1) {
          idxMsg = idxMsg + fzjgxxObj.idx;
          fzjgxxMsg = fzjgxxMsg + '[' + fzjgxxObj.fzjgnsrsbh + '、' + fzjgxxObj.fzjgmc + ']';
        } else {
          idxMsg = idxMsg + fzjgxxObj.idx + '、';
          fzjgxxMsg = fzjgxxMsg + '[' + fzjgxxObj.fzjgnsrsbh + '、' + fzjgxxObj.fzjgmc + ']、';
        }
      }
      tisMsg =
        tisMsg +
        '您导入的数据中，第' +
        idxMsg +
        '行的分支机构是无效的，对应的分支机构识别号和分支机构名称为' +
        fzjgxxMsg +
        '，请在模版删除后重新导入。<br/>';
    }
  }

  if (tisMsg !== '') {
    layer.open({
      type: 1,
      area: ['448px', '250px'], //固定宽高400px
      offset: 'c',
      icon: 6,
      title: ['提示'],
      scrollbar: false,
      closeBtn: 0,
      content: tisMsg,
      btn: ['确定'],
      btnAlign: 'r', //按钮居右
      yes: function (index) {
        layer.close(index);
      },
    });
  } else {
    formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb = bcfzjgxxGridlb;
    formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq = analysisJsonObject.data.A202000Ywbd.kz.zsGridLb.skssqq || '';
    formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz = analysisJsonObject.data.A202000Ywbd.kz.zsGridLb.skssqz || '';
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgmc = analysisJsonObject.data.A202000Ywbd.zjgxxForm.zjgmc || '';
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgnsrsbh = analysisJsonObject.data.A202000Ywbd.zjgxxForm.zjgnsrsbh || '';
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.ynsdse = analysisJsonObject.data.A202000Ywbd.zjgxxForm.ynsdse || 0;
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgftsdse = analysisJsonObject.data.A202000Ywbd.zjgxxForm.zjgftsdse || 0;
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.zjgczjzfpsdse =
      analysisJsonObject.data.A202000Ywbd.zjgxxForm.zjgczjzfpsdse || 0;
    formData.ht_.ywbw.A202000Ywbd.zjgxxForm.fzjgftdsdse =
      analysisJsonObject.data.A202000Ywbd.zjgxxForm.fzjgftdsdse || 0;
  }
}

function gethylb(hydm) {
  if (hydm >= 1600 && hydm <= 1699) {
    return '烟草制造业';
  }
  if (hydm >= 5100 && hydm <= 5299) {
    return '批发和零售业';
  }
  if (hydm >= 6100 && hydm <= 6299) {
    return '住宿和餐饮业';
  }
  if (hydm >= 7000 && hydm <= 7099) {
    return '房地产业';
  }
  if (hydm >= 7100 && hydm <= 7299) {
    return '租赁和商务服务业';
  }
  if (hydm >= 9000 && hydm <= 9099) {
    return '娱乐业';
  }
  var hymc = formData.fq_.nsrjbxx.hymc;
  return hymc;
}

function saveQysdsWjdc(qrjg) {
  var zcsxqrjg = [];
  var cshQrjg = formData.fq_.swsxCsh;
  var tjQrjg = formData.fq_.swsxtj;

  if (!isNull(cshQrjg)) {
    zcsxqrjg.push(cshQrjg);
  }
  if (!isNull(tjQrjg)) {
    zcsxqrjg.push(tjQrjg);
  }

  var reqVo = {};
  reqVo.ysqxxid = isNull(otherParams.ysqxxid) ? $('#ysqxxid').val() : otherParams.ysqxxid;
  reqVo.zcsxqrjg = JSON.stringify(zcsxqrjg);
  reqVo.sssqQ = otherParams.sssqQ;
  reqVo.sssqZ = otherParams.sssqZ;
  reqVo.ywbm = 'QYSDS_A_21YJD';
  reqVo.ywlx = 'SB';
  reqVo.ywzt = 'Y';
  reqVo.sid = 'dzswj.ywzz.sb.common.zctstxResolveCount';
  parent.requestYwztData(
    reqVo,
    function (_data) {
      try {
        if (typeof _data === 'string') {
          _data = JSON.parse(_data);
        }
        if ('000' != _data.rtnCode) {
          console.log(_data.rtnMsg);
        } else {
        }
      } catch (e) {
        console.log(_data);
      }
    },
    function (_error) {
      console.log(_error);
    },
  );
}

//山东个性化 SDSDSYHB-3732 第三季度预缴申报时未享受研发费用加计扣除优惠
function dsjdwxsyffyjjkcyhVerify() {
  var result = { isTs: 'N', msg: '' };
  //上年年报享受研发费用加计扣除优惠标志
  var snnbXsyffyjjkcyhbz = formData.hq_.qtxx.snnbXsyffyjjkcyhbz;

  var gbd3jd = formData.kz_.temp.zb.gbd3jd;
  var jjkc1 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].yhjmje;
  var jjkc2 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].yhjmje;
  var jjkc3 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].yhjmje;
  var jjkc4 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].yhjmje;
  if (nf_z >= 2022) {
    var jjkc5 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].yhjmje;
    var jjkc6 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].yhjmje;
  }

  var msg = '';
  var gxjswxsjjkcyh = false;
  var rjjcwxsjjkcyh = false;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var nf_z = parseInt(skssqz.split('-')[0], 10);
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  //1.当纳税人上一年企业所得税年度纳税申报表（A类，2017年版）中填报享受了研发费用加计扣除优惠，但第三季度预缴申报时未享受研发费用加计扣除优惠，同时满足以下条件时，给予纳税人申报提示
  if (
    yf_z == 9 &&
    gbd3jd === 'Y' &&
    snnbXsyffyjjkcyhbz === 'Y' &&
    jjkc1 == 0 &&
    jjkc2 == 0 &&
    jjkc3 == 0 &&
    jjkc4 == 0 &&
    (nf_z < 2022 || (nf_z >= 2022 && jjkc5 == 0 && jjkc6 == 0))
  ) {
    msg =
      '你公司上一年度企业所得税纳税申报享受了研发费用加计扣除优惠，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
  }

  if (gxjswxsjjkcyh && !rjjcwxsjjkcyh) {
    msg =
      '你公司属于有效期内的高新技术企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
  }
  if (rjjcwxsjjkcyh && !gxjswxsjjkcyh) {
    msg =
      '你公司是软件集成电路企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
  }
  if (gxjswxsjjkcyh && rjjcwxsjjkcyh) {
    var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
    var flag = false;
    //如A200000中第13行“减：减免所得税额（13.1+13.2+…）”的下拉选项选择了13.2项目（JMSE00201）且金额大于0，则申报提示
    if (jmsdGridlb[1].yhjmje > 0) {
      flag = true;
      msg =
        '你公司属于有效期内的高新技术企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
    }
    //如A200000中第13行“减：减免所得税额（13.1+13.2+…）”的下拉选项选择了13.4-13.22之间的项目（JMSE00301A~JMSE00310B）且金额大于0，则申报提示
    for (var i = 3; i <= 21; i++) {
      if (jmsdGridlb[i].yhjmje > 0) {
        flag = true;
        msg =
          '你公司是软件集成电路企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
        break;
      }
    }
    //除以上情况
    if (yf_z == 9 && !flag) {
      msg =
        '你公司属于有效期内的高新技术企业，请确认本年度是否发生研发费用支出，如果发生相关支出，可按照财政部 税务总局公告2021年第13号规定享受研发费用加计扣除优惠。';
    }
  }
  if (!isNull(msg)) {
    result.isTs = 'Y';
    result.msg = msg;
  }
  return result;
}

function jmsdseInfo(sdseyhInfo, bz) {
  var fjzgTip = '';
  var jmsdseTip = '';
  if (!isNull(sdseyhInfo)) {
    if (sdseyhInfo.indexOf('0') > -1) {
      fjzgTip += '西部大开发15%税率、';
      jmsdseTip += 'JMSE00601、';
    }
    if (sdseyhInfo.indexOf('1') > -1) {
      fjzgTip += '广东横琴、福建平潭、深圳前海等地15%税率、';
      jmsdseTip += 'JMSE00604、';
    }
    if (sdseyhInfo.indexOf('2') > -1) {
      fjzgTip += '海南自贸港15%税率、';
      jmsdseTip += 'JMSE00606、';
    }
  }
  if (bz == '1' && !isNull(fjzgTip)) {
    return fjzgTip.substring(0, fjzgTip.length - 1);
  } else {
    return jmsdseTip.substring(0, jmsdseTip.length - 1);
  }
}

//当主表13行和分配表的“分支机构享受区域性优惠情况”不匹配时，显示强制校验：主表第13栏填写的【区域性优惠】与企业所得税汇总纳税分支机构所得税分配表“分支机构享受区域性优惠情况”不一致，请选择正确的区域性优惠政策。
function jmsdVerifyfpb(index, t_jmsdssxSelect, fzjgxsqyxyhqk) {
  var skssqz = formData.fq_.sssq.sqZ;
  var skssqzDate = new Date(skssqz);
  var hbgDate = new Date('2022-09-30');

  var fzjgxxGridlb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
  var result = false;
  var jmsdGridlb = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb;
  //记录数组fzjgxsqyxyhqk的结果
  var resultStr = '';

  for (var i = 0; i < fzjgxxGridlb.length; i++) {
    resultStr += fzjgxxGridlb[i].fzjgxsqyxyhqk + ',';
    if (index == 28) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '01' && t_jmsdssxSelect == true) {
        result = true;
      }
    }
    if (index == 31) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' && t_jmsdssxSelect == true) {
        result = true;
      }
    }
    if (index == 39) {
      if ((fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '05') && t_jmsdssxSelect == true) {
        result = true;
      }
    }
    if (index == 40) {
      if ((fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '06') && t_jmsdssxSelect == true) {
        result = true;
      }
    }
    if (index == 41) {
      if ((fzjgxxGridlb[i].fzjgxsqyxyhqk == '03' || fzjgxxGridlb[i].fzjgxsqyxyhqk == '04') && t_jmsdssxSelect == true) {
        result = true;
      }
    }

    if (index == 42) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '07' && t_jmsdssxSelect == true) {
        result = true;
      }
    }

    if (index == 33) {
      if (fzjgxxGridlb[i].fzjgxsqyxyhqk == '02' && t_jmsdssxSelect == true) {
        result = true;
      }
    }
  }

  if (t_jmsdssxSelect == false) {
    if (index == 28 && resultStr.indexOf('01') == -1) {
      result = true;
    }

    if (skssqzDate < hbgDate) {
      if (
        jmsdGridlb[31].t_jmsdssxSelect ||
        jmsdGridlb[39].t_jmsdssxSelect ||
        jmsdGridlb[40].t_jmsdssxSelect ||
        jmsdGridlb[41].t_jmsdssxSelect
      ) {
        if (resultStr.indexOf('03') > -1) {
          result = true;
        }
      } else {
        if (resultStr.indexOf('03') == -1) {
          result = true;
        }
      }
    } else {
      if (index == 31 && resultStr.indexOf('03') == -1) {
        result = true;
      }

      if (index == 39 && resultStr.indexOf('05') == -1) {
        result = true;
      }

      if (index == 40 && resultStr.indexOf('06') == -1) {
        result = true;
      }

      if (index == 41 && resultStr.indexOf('04') == -1) {
        result = true;
      }

      if (index == 42 && resultStr.indexOf('07') == -1) {
        result = true;
      }
    }

    if (index == 33 && resultStr.indexOf('02') == -1) {
      result = true;
    }
  }

  return result;
}
/*
 * 初始化减征免征类型
 * 0：无，1：减征，2：免征
 * GDSDZSWJ-20593
 * */
function setJzmzlx() {
  var jzmzlx = '0';
  var sbqylx = formData.hq_.qtxx.sbqylx;
  if (
    sbqylx == '2' &&
    formData.hq_.fzjgxxGrid &&
    formData.hq_.fzjgxxGrid.fzjgxxGridlb &&
    formData.hq_.fzjgxxGrid.fzjgxxGridlb.length > 0
  ) {
    var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
    var djxh = formData.fq_.nsrjbxx.djxh;
    var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
    for (var i in fzjgxxGridlb) {
      if (
        (djxh == fzjgxxGridlb[i].fzjgdjxh || nsrsbh == fzjgxxGridlb[i].fzjgnsrsbh) &&
        fzjgxxGridlb[i].xsdfjmfd > 0 &&
        fzjgxxGridlb[i].sfxsdfjm == 'Y'
      ) {
        jzmzlx = '1';
        break;
      }
    }
  }
  return jzmzlx;
}
/*
 * 初始化减征幅度
 * GDSDZSWJ-20593
 * */
function setJzfd(jzmzlx) {
  var jzfd = formData.ht_.ywbw.A200000Ywbd.sbxx.jzfd;
  if (jzmzlx != '1') {
    jzfd = 0;
  } else {
    var sbqylx = formData.hq_.qtxx.sbqylx;
    if (
      sbqylx == '2' &&
      formData.hq_.fzjgxxGrid &&
      formData.hq_.fzjgxxGrid.fzjgxxGridlb &&
      formData.hq_.fzjgxxGrid.fzjgxxGridlb.length > 0
    ) {
      var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
      var djxh = formData.fq_.nsrjbxx.djxh;
      var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
      for (var i in fzjgxxGridlb) {
        if (
          (djxh == fzjgxxGridlb[i].fzjgdjxh || nsrsbh == fzjgxxGridlb[i].fzjgnsrsbh) &&
          fzjgxxGridlb[i].xsdfjmfd > 0 &&
          fzjgxxGridlb[i].sfxsdfjm == 'Y'
        ) {
          jzfd = fzjgxxGridlb[i].xsdfjmfd;
          break;
        }
      }
    }
  }
  return jzfd;
}
/*
 * 减征幅度锁定规则
 * GDSDZSWJ-20593
 * */
function checkJzfd(jzmzlx) {
  if (jzmzlx === '2') {
    formData.ht_.ywbw.A200000Ywbd.sbxx.jzfd = 1;
    formulaEngine.apply('ht_.ywbw.A200000Ywbd.sbxx.jzfd', 1);
  }else if (jzmzlx !== '1'){
    formData.ht_.ywbw.A200000Ywbd.sbxx.jzfd = 0;
    formulaEngine.apply('ht_.ywbw.A200000Ywbd.sbxx.jzfd', 0);
  }

  if (jzmzlx != '1') {
    return true;
  } else {
    var sbqylx = formData.hq_.qtxx.sbqylx;
    if (
      sbqylx == '2' &&
      formData.hq_.fzjgxxGrid &&
      formData.hq_.fzjgxxGrid.fzjgxxGridlb &&
      formData.hq_.fzjgxxGrid.fzjgxxGridlb.length > 0
    ) {
      var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
      var djxh = formData.fq_.nsrjbxx.djxh;
      var nsrsbh = formData.fq_.nsrjbxx.nsrsbh;
      for (var i in fzjgxxGridlb) {
        if (
          (djxh == fzjgxxGridlb[i].fzjgdjxh || nsrsbh == fzjgxxGridlb[i].fzjgnsrsbh) &&
          fzjgxxGridlb[i].sfxsdfjm == 'Y'
        ) {
          return true;
        }
      }
    }
  }
  return false;
}

function bccjbztzs(djxh) {
  var reqVo = {};
  if (parent.curDjxh != undefined && parent.curDjxh != '') {
    djxh = parent.curDjxh;
  }
  reqVo.djxh = djxh;
  reqVo.ywlx = 'SB';
  reqVo.ywzt = 'Y';
  reqVo.sid = 'dzswj.ywzz.sb.common.bcslzbbltstzs';
  parent.requestYwztData(
    reqVo,
    function (_data) {
      try {
        if (typeof _data === 'string') {
          _data = JSON.parse(_data);
        }
        var rtrBz = _data.rtrBz;
        if (rtrBz == 'N') {
          if (_data.ywwsuuid != undefined && _data.ywwsuuid != '') {
            var times = 3000;
            timeout = 10;
            timeoutIndex = setInterval(function () {
              if (timeout >= 0) {
                var wsData = cxWszb(_data.ywwsuuid);
                if (wsData != undefined && wsData != '' && wsData.rtrBz == 'Y') {
                  wsData.djxh = _data.djxh;
                  wsData.swjgMc = _data.swjgMc;
                  wsData.nsrmc = _data.nsrmc;
                  wsData.nsrsbh = _data.nsrsbh;
                  pjxml(wsData);
                  timeout = -1;
                }
                times = times * 2;
                timeout = timeout - 1;
              } else {
                clearInterval(timeoutIndex);
              }
            }, times);
          } else {
            return;
          }
        } else {
          pjxml(_data);
        }
      } catch (e) {
        console.log(_data);
      }
    },
    function (_error) {
      console.log(_error);
    },
  );
}

/**
 * 手动合成PDF
 * ywbm:业务编码（大写）
 * xmlData：PDF报文呢
 * sfdk：pdf合成后是否直接打开； Y:直接打开； 非Y:直接下载
 * 下载后的 文件名称
 */
function generatePdf(ywbm, xmlData, sfdk, filename, qzbz) {
  try {
    /*$("body").prepend("<form action='/sbzx-cjpt-web/sbzxPdf/generate.do?sbywbm="+ywbm+"&xmlData="+xmlData+"&sfdk="+sfdk+"&filename="+filename+"'     id='downloadPDF'   method='post'></form>");
		 $("#downloadPDF").submit();*/
    var mainUrl = window.location.protocol + '//' + window.location.host + '/' + window.location.pathname.split('/')[1];
    var url = mainUrl + '/sbzxPdf/generate.do?sbywbm=' + ywbm + '&sfdk=' + sfdk + '&qzbz=' + qzbz;
    //定义一个form表单
    var form = $('<form>');
    form.attr('style', 'display:none');
    form.attr('target', '');
    //请求类型
    form.attr('method', 'post');
    //请求地址
    form.attr('action', url);
    //将表单放置在web中
    $('body').append(form);

    //传递PDF报文
    var input = $('<input>');
    input.attr('type', 'hidden');
    input.attr('name', 'xmlData');
    input.attr('value', xmlData);
    var input1 = $('<input>');
    input1.attr('type', 'hidden');
    input1.attr('name', 'filename');
    input1.attr('value', filename);
    form.append(input);
    form.append(input1);
    //表单提交
    form.submit();
  } catch (e) {
    console.log(e);
  }
}

function cxWszb(ywwsuuid) {
  var reqVo = {};
  reqVo.ywwsuuid = ywwsuuid;
  reqVo.sid = 'dzswj.wbzy.sb.cxWszb.wszb';
  var redata = '';
  parent.requestYwztData(
    reqVo,
    function (_data) {
      try {
        if (typeof _data === 'string') {
          _data = JSON.parse(_data);
        }
        redata = _data;
      } catch (e) {
        console.log(_data);
      }
    },
    function (_error) {
      console.log(_error);
    },
  );
  return redata;
}

function pjxml(_data) {
  var toDay = new Date();
  var sbrq = toDay.getFullYear() + '年' + (toDay.getMonth() + 1) + '月' + toDay.getDate() + '日';

  if (_data.wszg == '' || _data.wszg == null || _data.wszg == undefined) {
    _data.wszg = _data.swjgjg + _data.zg + '〔' + _data.nh + '〕' + _data.wh + '号';
  }
  var xmlData =
    '<taxML>' +
    '<qzSwjgDm>' +
    _data.wszzswjg_dm +
    '</qzSwjgDm>' +
    '<wszg>' +
    _data.wszg +
    '</wszg>' +
    '<swjgMc>' +
    _data.swjgMc +
    '</swjgMc>' +
    '<nsrmc>' +
    _data.nsrmc +
    '</nsrmc>' +
    '<nsrsbh>' +
    _data.nsrsbh +
    '</nsrsbh>' +
    '<sbrq>' +
    sbrq +
    '</sbrq>' +
    '</taxML>';
  var filename = '《税务事项通知书》（受理暂不办理退抵税通知）';
  var qzbz = formData.fq_.qzbz;
  if (qzbz != 'N') {
    qzbz = 'Y';
  }
  generatePdf('TSTZS', xmlData, 'N', filename, qzbz);
}

function sqXmdmVerify(dm, xm) {
  //不是当年第一期才取接口数据
  var sfdndiq = formData.fq_.sfdndiq;
  var qygxhXmBz = formData.fq_.qygxhXmBz;
  if ('Y' === sfdndiq || qygxhXmBz != 'Y') {
    return true;
  }

  if (xm == 'gdzcjszjkcmxbGridlb2') {
    if (!isEmptyObj(formData.hq_.sqsbxx) && !isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid)) {
      var gdzcjszjkcmxbGridlb = formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb;
      for (var i in gdzcjszjkcmxbGridlb) {
        if (gdzcjszjkcmxbGridlb[i].ewbhgjz == 'JSZJ' && gdzcjszjkcmxbGridlb[i].ewbhxh != 0) {
          var yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
          var gdzcjszjkcmxbGridlb2 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
          var sfczBz = false;
          for (var j in gdzcjszjkcmxbGridlb2) {
            if (yhswsx == gdzcjszjkcmxbGridlb2[j].yhswsx) {
              sfczBz = true;
            }
          }
          if (!sfczBz) {
            return false;
          }
        }
      }
    }
    return true;
  }

  if (xm == 'gdzcjszjkcmxbGridlb3') {
    if (!isEmptyObj(formData.hq_.sqsbxx) && !isEmptyObj(formData.hq_.sqsbxx.gdzcjszjkcMxbGrid)) {
      var gdzcjszjkcmxbGridlb = formData.hq_.sqsbxx.gdzcjszjkcMxbGrid.gdzcjszjkcMxbGridlb;
      for (var i in gdzcjszjkcmxbGridlb) {
        if (gdzcjszjkcmxbGridlb[i].ewbhgjz == 'YCKC' && gdzcjszjkcmxbGridlb[i].ewbhxh != 0) {
          var yhswsx = gdzcjszjkcmxbGridlb[i].yhswsx;
          var gdzcjszjkcmxbGridlb3 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
          var sfczBz = false;
          for (var j in gdzcjszjkcmxbGridlb3) {
            if (yhswsx == gdzcjszjkcmxbGridlb3[j].yhswsx) {
              sfczBz = true;
            }
          }
          if (!sfczBz) {
            return false;
          }
        }
      }
    }
    return true;
  }

  return true;
}

function saveTsqk(tsqkArr) {
  try {
    for (var i = 0; i < tsqkArr.length; i++) {
      var nd = tsqkArr[i].split('_')[0];
      var xzqkDm = tsqkArr[i].split('_')[1];
      var xzqkObj = {};
      xzqkObj.nd = nd;
      xzqkObj.xzqkDm = xzqkDm;
      var zcsxqrjg = [];
      zcsxqrjg.push(xzqkObj);
      var reqVo = {};
      reqVo.ysqxxid = isNull(otherParams.ysqxxid) ? $('#ysqxxid').val() : otherParams.ysqxxid;
      reqVo.zcsxqrjg = JSON.stringify(zcsxqrjg);
      reqVo.sssqQ = nd + '-01-01';
      reqVo.sssqZ = nd + '-12-31';
      reqVo.ywbm = 'QYSDS_A_18ND_TSXZQK';
      reqVo.ywlx = 'SB';
      reqVo.ywzt = 'Y';
      reqVo.sid = 'dzswj.ywzz.sb.common.zctstxResolveCount';
      parent.requestYwztData(
        reqVo,
        function (_data) {
          try {
            if (typeof _data === 'string') {
              _data = JSON.parse(_data);
            }
            if ('000' != _data.rtnCode) {
              console.log(_data.rtnMsg);
            } else {
            }
          } catch (e) {
            console.log(_data);
          }
        },
        function (_error) {
          console.log(_error);
        },
      );
    }
  } catch (e) {
    console.log(e);
  }
}

//返回匹配fzjgxxGridlb的index
function matchingFzjglb() {
  var fzjgxxGridlb = formData.hq_.fzjgxxGrid.fzjgxxGridlb;
  var index = -1;
  if (isArray(fzjgxxGridlb) && fzjgxxGridlb.length > 0) {
    for (var i = 0; i < fzjgxxGridlb.length; i++) {
      var fzjgdjxh = fzjgxxGridlb[i].fzjgdjxh;
      var fzjgnsrsbh = fzjgxxGridlb[i].fzjgnsrsbh;
      var fzjgmc = fzjgxxGridlb[i].fzjgmc;
      if (
        fzjgdjxh == formData.fq_.nsrjbxx.djxh ||
        (fzjgnsrsbh === formData.fq_.nsrjbxx.nsrsbh && fzjgmc === formData.fq_.nsrjbxx.nsrmc)
      ) {
        index = i;
        break;
      }
    }
  }
  return index;
}

function toGdSty(gdStyUrl, sbnd) {
  var tips =
    '&nbsp &nbsp &nbsp &nbsp尊敬的纳税人：贵单位' +
    sbnd +
    '年度企业所得税汇算清缴申报产生多缴税款，为进一步优化纳税服务水平和税收营商环境，切实提高纳税人的获得感和满意度，广东省电子税务局将对符合信用等级高等特定条件的纳税人提供企业所得税汇算清缴多缴税款“速退易”智能退税服务，将加速审批企业所得税汇算清缴多缴税款的退税，并退至纳税人的默认退税银行账户，本事项不免除纳税人依法应承担的法律责任。请点击“确认”键同意使用“速退易”智能退税服务，确认后即视同申请办理企业所得税汇算清缴多缴退税。';
  var timeout = 10;
  var timeoutIndex = -1;
  var isclearTime = false;

  layer.open({
    type: 1,
    area: ['580px', '420px'],
    title: ['提示'],
    closeBtn: 0,
    scrollbar: false,
    content: tips,
    btn: ['确定'],
    //btnAlign: 'r', //按钮居中
    success: function (layero, index) {
      layero.find('.layui-layer-btn0').addClass('layui-btn-disabled');
      // 倒计时10s
      timeoutIndex = setInterval(function () {
        layero.find('.layui-layer-btn0').addClass('layui-btn-disabled');
        layero.find('.layui-layer-btn0').css('backgroundColor', '#f7f7f7');
        layero.find('.layui-layer-btn0').css('color', '#bbb');
        layero.find('.layui-layer-btn0').css('border', '1px solid #DDD');
        layero.find('.layui-layer-btn0').attr('disabled', 'true').css('pointer-events', 'none');
        if (timeout >= 0) {
          var txt = '(' + timeout + ')确定';
          layero.find('.layui-layer-btn0').text(txt);
          timeout = timeout - 1;
        } else {
          txt = '确定';
          layero.find('.layui-layer-btn0').text(txt);
          isclearTime = true;
          layero.find('.layui-layer-btn0').removeClass('layui-btn-disabled');
          layero.find('.layui-layer-btn0').css('backgroundColor', '#53acf3');
          layero.find('.layui-layer-btn0').css('color', '#fff');
          layero.find('.layui-layer-btn0').css('border', '1px solid #53acf3');
          layero.find('.layui-layer-btn0').removeAttr('disabled').css('pointer-events', 'auto');
          clearInterval(timeoutIndex);
        }
      }, 1500);
      //maskts();
    },
    yes: function (index) {
      //倒计时未结束，不让点
      if (!isclearTime) {
        return true;
      }
      //跳转速退易
      parent.window.open(gdStyUrl);
      layer.close(index);
      //maskts();
    },
  });
}

//北京个性化 申报提交前 加计扣除相关弹框
function bjJjkcTxtx() {
  var hydm = formData.fq_.nsrjbxx.hydm;
  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var nd = parseInt(skssqz.split('-')[0], 10);

  var a = formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[0].jehxxz;
  var b = formData.ht_.ywbw.A200000Ywbd.fbsxGrid.fbsxGridlb[1].jehxxz;
  var c = formData.fq_.yywzcLj;

  //期间费用中填报研发费白名单标志
  var qjfyyffBmdbz = formData.fq_.qjfyyffBmdbz;

  //软件企业白名单标志
  var rjqyBmdbz = formData.fq_.rjqyBmdbz;
  //科技中小型企业白名单标志
  var kjxzxqyBmdbz = formData.fq_.kjxzxqyBmdbz;

  var skssqz = formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqz;
  var nd = parseInt(skssqz.split('-')[0], 10);
  var yf_z = parseInt(skssqz.split('-')[1], 10);
  var jjkc1 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[22].yhjmje;
  var jjkc2 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[23].yhjmje;
  var jjkc3 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[24].yhjmje;
  var jjkc4 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[25].yhjmje;

  var jjkc013 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[26].yhjmje;
  var jjkc023 = formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb[27].yhjmje;

  //原有软件集成电路优惠继续执行至到期
  var yyRjjcdl1 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[3].yhjmje;
  var yyRjjcdl2 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[4].yhjmje;
  var yyRjjcdl3 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[5].yhjmje;
  var yyRjjcdl4 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[6].yhjmje;
  var yyRjjcdl5 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[7].yhjmje;
  var yyRjjcdl6 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[8].yhjmje;
  var yyRjjcdl7 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[9].yhjmje;
  var yyRjjcdl8 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[10].yhjmje;
  var yyRjjcdl9 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[11].yhjmje;

  //软件集成电路企业新政策
  var yyRjjcdl10 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[12].yhjmje;
  var yyRjjcdl11 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[13].yhjmje;
  var yyRjjcdl12 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[14].yhjmje;
  var yyRjjcdl13 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[15].yhjmje;
  var yyRjjcdl14 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[16].yhjmje;
  var yyRjjcdl15 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[17].yhjmje;
  var yyRjjcdl16 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[18].yhjmje;
  var yyRjjcdl17 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[19].yhjmje;
  var yyRjjcdl18 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[20].yhjmje;
  var yyRjjcdl19 = formData.ht_.ywbw.A200000Ywbd.jmsdGrid.jmsdGridlb[21].yhjmje;

  //享受软件集成电路优惠标志
  var xsrjjcdlyhbz = 'N';
  if (
    yyRjjcdl1 > 0 ||
    yyRjjcdl2 > 0 ||
    yyRjjcdl3 > 0 ||
    yyRjjcdl4 > 0 ||
    yyRjjcdl5 > 0 ||
    yyRjjcdl6 > 0 ||
    yyRjjcdl7 > 0 ||
    yyRjjcdl8 > 0 ||
    yyRjjcdl9 > 0
  ) {
    xsrjjcdlyhbz = 'Y';
  }
  if (
    yyRjjcdl9 > 0 ||
    yyRjjcdl10 > 0 ||
    yyRjjcdl11 > 0 ||
    yyRjjcdl12 > 0 ||
    yyRjjcdl13 > 0 ||
    yyRjjcdl14 > 0 ||
    yyRjjcdl15 > 0 ||
    yyRjjcdl16 > 0 ||
    yyRjjcdl17 > 0 ||
    yyRjjcdl18 > 0 ||
    yyRjjcdl19 > 0
  ) {
    xsrjjcdlyhbz = 'Y';
  }

  //debugger
  if (nd >= 2021 && yf_z == 9 && qjfyyffBmdbz == 'Y' && jjkc1 == 0 && jjkc2 == 0 && jjkc3 == 0 && jjkc4 == 0) {
    indexYffyjjkcyhzcTxtx = layer.open({
      type: 2,
      title: '',
      area: ['650px', '310px'],
      closeBtn: 0,
      content: result + '/biz/sb/qysds_a_21yjd/form/yffyjjkcYhzcTxtx.html?',
    });
  } else if (nd >= 2021 && yf_z == 9 && rjqyBmdbz == 'Y' && jjkc1 == 0 && jjkc2 == 0 && jjkc3 == 0 && jjkc4 == 0) {
    indexYffyjjkcyhzcTxtx = layer.open({
      type: 2,
      title: '',
      area: ['650px', '310px'],
      closeBtn: 0,
      content: result + '/biz/sb/qysds_a_21yjd/form/yffykcgxqyYhzcTxtx.html?',
    });
  } else if (nd >= 2021 && yf_z == 9 && xsrjjcdlyhbz == 'Y' && jjkc1 == 0 && jjkc2 == 0 && jjkc3 == 0 && jjkc4 == 0) {
    indexYffyjjkcyhzcTxtx = layer.open({
      type: 2,
      title: '',
      area: ['750px', '350px'],
      closeBtn: 0,
      content: result + '/biz/sb/qysds_a_21yjd/form/yffykcRjjcdlYhzcTxtx.html?',
    });
  } else if (nd >= 2021 && yf_z == 9 && kjxzxqyBmdbz == 'Y' && jjkc1 == 0 && jjkc2 == 0 && jjkc3 == 0 && jjkc4 == 0) {
    indexYffyjjkcyhzcTxtx = layer.open({
      type: 2,
      title: '',
      area: ['650px', '310px'],
      closeBtn: 0,
      content: result + '/biz/sb/qysds_a_21yjd/form/yffykcgxqyYhzcTxtx.html?',
    });
  } else if (nd >= 2021 && yf_z == 9 && hydm >= 1300 && hydm <= 4390 && jjkc1 == 0 && jjkc3 == 0) {
    indexYffyjjkcyhzcTxtx = layer.open({
      type: 2,
      title: '',
      area: ['600px', '300px'],
      closeBtn: 0,
      content: result + '/biz/sb/qysds_a_21yjd/form/yffykczzyYhzcTxtx.html?',
    });
  } else if (a + b > c && nd < 2022) {
    var br2space = '<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
    var br2 = '<br/>';
    var content =
      '<div class="layui-text">1.附报事项K01001新型冠状病毒感染的肺炎疫情防控捐赠支出全额扣除为：' +
      a +
      '元；\n' +
      br2 +
      '2.附报事项K01002扶贫捐赠支出全额扣除为：' +
      b +
      '元；\n' +
      br2 +
      '3.《利润表》中“营业外支出_本年累计金额”为：' +
      c +
      '元；\n' +
      br2 +
      '4.您企业填报的新型冠状病毒感染的肺炎疫情防控捐赠和扶贫捐赠金额大于营业外支出金额，请核实申报数据填报是否有误。\n' +
      '</div>';

    parent.layer.confirm(
      content,
      {
        title: '提示',
        type: 1,
        area: ['420px', '340px'],
        btn: ['确认申报', '返回修改'],
        btn2: function (index) {
          prepareMakeFlag = true;
          parent.layer.close(index);
        },
      },
      function (index) {
        parent.layer.close(index);
        //执行多缴退税提醒
        djtstxBtn();
      },
    );
  } else {
    //执行多缴退税提醒
    djtstxBtn();
  }
}

//记录A201020填写JSZJ1100和JSZJ1110第4列“资产原值”的合计金额
function getGxjsYcxkcHj(yhswsx, bnljzjkcjedxs) {
  var gdzcjszjkcmxbGridlb3 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
  var bnljzjkcjedxsHj = 0;
  for (var i in gdzcjszjkcmxbGridlb3) {
    if (gdzcjszjkcmxbGridlb3[i].yhswsx == 'JSZJ1100' || gdzcjszjkcmxbGridlb3[i].yhswsx == 'JSZJ1110') {
      bnljzjkcjedxsHj += gdzcjszjkcmxbGridlb3[i].bnljzjkcjedxs;
    }
  }
  return bnljzjkcjedxsHj;
}

//判断json数组对象中某个节点的值是否包含指定的值
function arrIndexByVal(jsonArr, jdmc, zdval) {
  var fag = false;
  for (var i = 0; i < jsonArr.length; i++) {
    if (jsonArr[i][jdmc] == zdval) {
      fag = true;
    }
  }

  return fag;
}

/**
 * 小微减免2023-02-01启用的需求是否是否方案二。禅道21401
 * 写死简单处理。
 * @returns {boolean} true 启用方案二 false 启用方案一
 */
function sdsxqbgQyfa2() {
  return true;
}

/**
 * 计算13栏第一行优惠减免金额
 */
function calclc13_1yhjmje(t_jmsdssxSelect, sjlreLj, tsnsrlxDm, slLj) {
  if (t_jmsdssxSelect == true) {
    let sde = sjlreLj * slLj;
    if (tsnsrlxDm == '10') {
      return 0;
    }
    sde = MAX(sde, 0);
    if (sdsxqbgQyfa2() && formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq >= '2023-01-01') {
      //方案二：2023年及以后： 0~300万  sjlreLj * slLj * 0.8
      if (sjlreLj > 0 && sjlreLj <= 3000000) {
        return ROUND(accMul(sde, 0.8), 2);
        //2023年及以后：300万~ 不符合减免
      } else {
        return 0;
      }
    }
    //方案一：2023年及以后：
    else if (formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq >= '2023-01-01') {
      //2023年及以后： 0~100万  sjlreLj * slLj * 0.2
      if (sjlreLj > 0 && sjlreLj <= 1000000) {
        return ROUND(accMul(sde, 0.2), 2);
        //2023年及以后： 100万~300万  sjlreLj * slLj * 0.8 - 15万
      } else if (sjlreLj > 1000000 && sjlreLj <= 3000000) {
        return ROUND(accMul(sde, 0.8) - 150000, 2);
        //2023年及以后：300万~ 不符合减免
      } else {
        return 0;
      }
      //2022年：
    } else if (formData.ht_.ywbw.A200000Ywbd.nsrxx.skssqq >= '2022-01-01') {
      //2022年： 0~100万  sjlreLj * slLj * 0.9
      if (sjlreLj > 0 && sjlreLj <= 1000000) {
        return ROUND(accMul(sde, 0.9), 2);
        //2022年：100万~300万  sjlreLj * slLj * 0.8 + 2.5万
      } else if (sjlreLj > 1000000 && sjlreLj <= 3000000) {
        return ROUND(accMul(sde, 0.8) + 25000, 2);
        //2022年：300万~ 不符合减免
      } else {
        return 0;
      }
    } else {
      //2022年以前： 0~100万  sjlreLj * slLj * 0.9
      if (sjlreLj > 0 && sjlreLj <= 1000000) {
        return ROUND(accMul(sde, 0.9), 2);
        //2022年以前： 100万~300万  sjlreLj * slLj * 0.6 + 7.5万
      } else if (sjlreLj > 1000000 && sjlreLj <= 3000000) {
        return ROUND(accMul(sde, 0.6) + 75000, 2);
        //2022年以前： 300万~ 不符合减免
      } else {
        return 0;
      }
    }
  } else {
    //未勾选： 为零
    return 0;
  }
}

/**
 * 码表个性化
 * @param json
 * @returns {*}
 */
function dmbFilter_gdzcjszjCT(json) {
  var ssqz = formData.fq_.sssq.sqZ;
  if (ssqz >= '2021-12-31') {
    json['JSZJ0050'] = '横琴粤澳深度合作区企业固定资产加速折旧';
    json['JSZJ0060'] = '横琴粤澳深度合作区企业无形资产加速摊销';
  }
  return json;
}

/**
 * 码表个性化
 * @param json
 * @returns {*}
 */
function dmbFilter_jszjYcxkcCT(json) {
  var ssqz = formData.fq_.sssq.sqZ;
  if (ssqz >= '2021-12-31') {
    json['JSZJ1050'] = '横琴粤澳深度合作区企业固定资产一次性扣除';
    json['JSZJ1060'] = '横琴粤澳深度合作区企业无形资产一次性扣除';
  }
  return json;
}

/**
 * 码表个性化
 * @param json
 * @returns {*}
 */
function dmbFilter_jszjYcxkc22nyhCT(json) {
  var ssqz = formData.fq_.sssq.sqZ;
  if (ssqz >= '2021-12-31') {
    json['JSZJ1050'] = '横琴粤澳深度合作区企业固定资产一次性扣除';
    json['JSZJ1060'] = '横琴粤澳深度合作区企业无形资产一次性扣除';
  }
  return json;
}

/**
 *
 * @param lc
 */
function getCwbbLcmc(lc) {
  var bsxx = formData.fq_.cbmxxx.bsxx;
  var zlbsxlDm = '';
  if (bsxx && bsxx.length > 0) {
    zlbsxlDm = bsxx[0].zlbsxlDm;
  }
  if (zlbsxlDm != '') {
    switch (zlbsxlDm + '_' + lc) {
      case 'ZL1001002_yysrLj':
      case 'ZL1001003_yysrLj':
        return '财务报表利润表中的[营业收入_本年累计金额]';
      case 'ZL1001002_yycbLj':
      case 'ZL1001003_yycbLj':
        return '财务报表利润表中的[营业成本_本年累计金额]';
      case 'ZL1001002_lrzeLj':
      case 'ZL1001003_lrzeLj':
        return '财务报表利润表中的[利润总额_本年累计金额]';
      case 'ZL1001050_yysrLj':
        return '财务报表利润表中的[营业收入_本期金额]';
      case 'ZL1001050_yycbLj':
        return '财务报表利润表中的[营业成本_本期金额]';
      case 'ZL1001050_lrzeLj':
        return '财务报表利润表中的[利润总额_本期金额]';
      case 'ZL1001051_yysrLj':
        return (
          '财务报表收入费用表_月报（适用执行政府会计制度的单位）中第2行（一）财政拨款收入“本年累计数”+第4行（二）事业收入“本年累计数”+第5行（三）上级补助收入“本年累计数”' +
          '+第6行（四）附属单位上缴收入“本年累计数”+第7行（五）经营收入“本年累计数”的合计值'
        );
      case 'ZL1001051_yycbLj':
        return '财务报表收入费用表_月报（适用执行政府会计制度的单位）中第14行[本期费用_本年累计金额]';
      case 'ZL1001051_lrzeLj':
        return '财务报表收入费用表_月报（适用执行政府会计制度的单位）中第23行本期盈余“本年累计数”+第21行所得费用“本年累计数”的合计值';
      case 'ZL1001023_yysrLj':
        return '财务报表中《业务活动表》第1行捐赠收入“本年累计数”+第2行会费收入“本年累计数”+第3行提供服务收入“本年累计数”+第4行商品销售收入“本年累计数”+第5行政府补助收入“本年累计数”的合计值';
      case 'ZL1001023_yycbLj':
        return '财务报表中《业务活动表》第35行“费用合计-本年累计数合计”';
      case 'ZL1001023_lrzeLj':
        return '财务报表中《业务活动表》第11行收入合计“本年累计数-合计”-第35行“费用合计-本年累计数合计”的运算值';
    }
  }
  //不属于枚举范围内的报送数据，则采用默认返回
  switch (lc) {
    case 'yysrLj':
      return '财务报表利润表中的[营业收入_本年累计金额]';
    case 'yycbLj':
      return '财务报表利润表中的[营业成本_本年累计金额]';
    case 'lrzeLj':
      return '财务报表利润表中的[利润总额_本年累计金额]';
  }
  return '';
}

// 框架组优化之前暂时：检查若是旧配置报文，弹窗提醒
function cwDataVerify() {
  if (formData.ht_.ywbw.A200000Ywbd.mssrGrid.mssrGridlb.length < 33) {
    // var msg = '经检查您填写的数据错误，请点击重置按钮后重新填写数据。';
    // top.$vue.$gtDialog.warning({
    //   header:'提示',
    //   body: msg,
    //   closeOnOverlayClick: false,
    //   closeOnEscKeydown: false,
    // });
    // showZcbwReadyMessage = false;
    return false;
  }
  return true;
}

function getNewIndex(array,key,index){
  var newArray = array;
  var count = 0;
  for (var i = 0; i < newArray.length; i++) {
    var obj = newArray[i];
    var isSelect = obj[key];
    if (isSelect) {
      count++;
      obj.t_xh = index+ "." + count;
    }else {
      //移除未选中选项
      newArray.splice(i,1);
      //更新索引，以免漏掉下一个元素
      i--;
    }
  }
  return newArray;
}

// pdf用到的t_中间节点统一赋值
function generatePdfAssignments() {
  //企业所得税月（季）度预缴纳税申报表（A类）
  if(formData.ht_.ywbw.A200000Ywbd){
    var prefix = formData.ht_.ywbw.A200000Ywbd;

    //1. 处理单选框多选框
    //国家限制或禁止行业  R代表选中，0代表未选中
    var gjxzhjzhy = prefix.sbxx.gjxzhjzhy;
    prefix.sbxx.t_pdf_gjxzhjzhyY = ("Y" === gjxzhjzhy ? "R":"0");
    prefix.sbxx.t_pdf_gjxzhjzhyN = ("N" === gjxzhjzhy ? "R":"0");
    //小微企业
    var sfsyxxwlqy = prefix.sbxx.sfsyxxwlqy;
    prefix.sbxx.t_pdf_sfsyxxwlqyY = ("Y" === sfsyxxwlqy ? "R":"0");
    prefix.sbxx.t_pdf_sfsyxxwlqyN = ("N" === sfsyxxwlqy ? "R":"0");
    //扶贫捐赠支撑全额扣除
    var t_fsbxSelect1 = prefix.fbsxGrid.fbsxGridlb[1].t_fsbxSelect;
    formData.kz_.pdf.t_pdf_fsbxSelectQekc = t_fsbxSelect1 ? "R":"0";
    //软件集成电路企业优惠政策适用类
    var t_fsbxSelect2 = prefix.fbsxGrid.fbsxGridlb[2].t_fsbxSelect;
    formData.kz_.pdf.t_pdf_fsbxSelectYhzc = t_fsbxSelect2 ? "R":"0";
    //原政策 [''：未选中  '0'：选中]
    var t_xszcy = prefix.fbsxGrid.fbsxGridlb[2].t_xszcy;
    formData.kz_.pdf.t_pdf_xszcy = isNull(t_xszcy) ? "0":"R";
    //新政策 [''：未选中  '0'：选中]
    var t_xszcx = prefix.fbsxGrid.fbsxGridlb[2].t_xszcx;
    formData.kz_.pdf.t_pdf_xszcx = isNull(t_xszcx) ? "0":"R";
    //减：民族自治地区企业所得税地方分享部分
    var jzmzlx = prefix.sbxx.jzmzlx;
    prefix.sbxx.t_pdf_jzmzlxW = "0"==jzmzlx ? "R":"0";
    prefix.sbxx.t_pdf_jzmzlxJZ = "1"==jzmzlx ? "R":"0";
    prefix.sbxx.t_pdf_jzmzlxMZ = "2"==jzmzlx ? "R":"0";

    //2. 处理序号问题
    //免税收入
    var mssrArray = JSON.parse(JSON.stringify(prefix.mssrGrid.mssrGridlb));
    var t_pdf_mssrArray = getNewIndex(mssrArray,"t_mssrsxSelect","7");

    //其他：免税收入类未列明优惠（减免税代码：
    var ssjmxzDm1 = prefix.mssrGrid.mssrGridlb[19].ssjmxzDm;

    //其他：减计收入类未列明优惠（减免税代码：
    var ssjmxzDm2 = prefix.mssrGrid.mssrGridlb[20].ssjmxzDm;

    //处理其他选项，添加减免税代码
    for (var i = 0; i < t_pdf_mssrArray.length; i++) {
      var item = t_pdf_mssrArray[i];
      if(item.yhswsx === 'MSSR999'){
        item.ewbhgjz = item.ewbhgjz+"（减免税代码："+ssjmxzDm1+"）";
      }else if(item.yhswsx === 'JJSR999'){
        item.ewbhgjz = item.ewbhgjz+"（减免税代码："+ssjmxzDm2+"）";
      }
    }

    var mssrObj = {};
    mssrObj.t_xh = 8;
    mssrObj.ewbhgjz = "减：所得减免（8.1+8.2+…）";
    mssrObj.yhjmje = prefix.sbxx.sdjmLj;
    t_pdf_mssrArray.push(mssrObj);
    formData.kz_.pdf.t_pdf_mssrArray = t_pdf_mssrArray;

    //所得减免
    var sdjmArray = JSON.parse(JSON.stringify(prefix.sdjmGrid.sdjmGridlb));
    var t_pdf_sdjmArray = getNewIndex(sdjmArray,"t_sdjmsxSelect","8");

    var sdjmObj = {};
    sdjmObj.t_xh = 9;
    sdjmObj.t_selectMc = "减：弥补以前年度亏损";
    sdjmObj.yhjmje = prefix.sbxx.mbyqndksLj;
    t_pdf_sdjmArray.push(sdjmObj);
    formData.kz_.pdf.t_pdf_sdjmArray = t_pdf_sdjmArray;

    //减免所得
    var jmsdArray = JSON.parse(JSON.stringify(prefix.jmsdGrid.jmsdGridlb));
    var t_pdf_jmsdArray = getNewIndex(jmsdArray,"t_jmsdssxSelect","13");

    var ssjmxzDm3 = prefix.jmsdGrid.jmsdGridlb[34].ssjmxzDm;
    //设在西部地区的鼓励类产业企业减按15%的税率征收企业所得税(主营业务收入占比
    var zyywsrzb = prefix.jmsdGrid.jmsdGridlb[28].zyywsrzb;

    for (var i = 0; i < t_pdf_jmsdArray.length; i++) {
      if(t_pdf_jmsdArray[i].yhswsx === "JMSE99999"){
        t_pdf_jmsdArray[i].jmsdssxMc = '其他（减免税代码：' +ssjmxzDm3+'）';
      }else if(t_pdf_jmsdArray[i].yhswsx === "JMSE00601"){
        t_pdf_jmsdArray[i].jmsdssxMc = t_pdf_jmsdArray[i].jmsdssxMc +"(主营业务收入占比" + zyywsrzb + ")";
      }
    }

    var jmsdObj = {};
    jmsdObj.t_xh = 14;
    jmsdObj.jmsdssxMc = "减：本年实际已缴纳所得税额";
    jmsdObj.yhjmje = prefix.sbxx.sjyyjsdseLj;
    t_pdf_jmsdArray.push(jmsdObj);
    formData.kz_.pdf.t_pdf_jmsdArray = t_pdf_jmsdArray;

    //处理附报事项
    var jehxxz = prefix.fbsxGrid.fbsxGridlb[1].jehxxz;
    formData.kz_.pdf.t_pdf_jehxxz = jehxxz;
  }
  // 资产加速折旧、摊销（扣除）优惠明细表
  if(formData.ht_.ywbw.A201020Ywbd){
    var gdzcjszjkcmxbGridlb2 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb2;
    if (Array.isArray(gdzcjszjkcmxbGridlb2)) {
      gdzcjszjkcmxbGridlb2.forEach((item, index) => {
        // 生成行次
        item.t_xh = '1.' + (index + 1);
      });
    }
    var gdzcjszjkcmxbGridlb3 = formData.ht_.ywbw.A201020Ywbd.gdzcjszjkcMxbGrid.gdzcjszjkcmxbGridlb3;
    if (Array.isArray(gdzcjszjkcmxbGridlb3)) {
      gdzcjszjkcmxbGridlb3.forEach((item, index) => {
        item.t_xh = '2.' + (index + 1);
      });
    }
  }
  // 企业所得税汇总纳税分支机构所得税分配表
  if (formData.ht_.ywbw.A202000Ywbd) {
    var fzjgxxGridlb = formData.ht_.ywbw.A202000Ywbd.fzjgxxGrid.fzjgxxGridlb;
    if (Array.isArray(fzjgxxGridlb)) {
      fzjgxxGridlb.forEach((item) => {
        // 转中文：分支机构享受区域性优惠情况
        item.yhsxmc = item.fzjgxsqyxyhqk ? pdf_fzjgxsyhqk[item.fzjgxsqyxyhqk] || '' : '';
      });
    }
  }
  // 技术成果投资入股企业所得税递延纳税备案表
  if(formData.ht_.ywbw.dynsbabYwbd){
  var dynsbabGridlb = formData.ht_.ywbw.dynsbabYwbd.dynsbabGrid.dynsbabGridlb;
    if (Array.isArray(dynsbabGridlb)) {
      var sfwglqyMap = {
        N: '否',
        Y: '是',
      };
      dynsbabGridlb.forEach((item, index) => {
        // 生成行次
        item.t_xh = index + 1;
        // 与投资方是否为关联企业：是/否
        item.t_pdf_sfwglqyMc = sfwglqyMap[item.sfwglqy] || item.sfwglqy;
        // 转中文：技术成果类型
        item.t_pdf_jscglxMc = item.jscglx ? pdf_jscglx[item.jscglx] || item.jscglx : '';
      });
    }
    //递延所得保留两位数
    var hj_dysd = formData.ht_.ywbw.dynsbabYwbd.dynsbabGrid.hj_dysd;
    formData.ht_.ywbw.dynsbabYwbd.dynsbabGrid.t_pdf_hj_dysd = hj_dysd.toFixed(2);
  }
}

export default {
  getSnxwqy,
  sfxwqy_ND,
  getCwbbLcmc,
  cal_qysds_a_18yjd_A201030_29Row,
  cal_qysds_a_18yjd_A201030_1Row,
  vaild_qysds_a_18yjd_A201030_1Row,
  vaild_qysds_a_18yjd_A201030_2_28Row,
  getXwinit,
  getBqxwqy,
  getfpbl,
  getFpse,
  getFpseZjg,
  setZjlxMc,
  isNull,
  yqsbVaild,
  mbksVerify,
  zyhyts,
  isNotEmptyObj,
  isEmptyObj,
  getSsjmxz,
  getEwbhxh,
  getSqzj,
  getSqs,
  hasHn,
  isHnjg,
  zjxmyhdm_different1,
  zjxmyhdm_different2,
  setFpbxx,
  getfpblhfpse,
  validFpbl,
  validation,
  getZjgftbz,
  valiZjgyftsdseBq,
  getzjgbl,
  getZjgftsdse,
  getSjlreLj,
  getYbtsdseLj,
  getJsyj,
  getYbtse,
  getYnse,
  getJzfd,
  sbbSfksbValid,
  getCwbbData,
  getZzsData,
  getCbAndZzsYsData,
  gdgxh,
  tstip,
  getcwbbxx,
  zdGetcwbbxx,
  getHelp,
  ifTipsReset,
  getYsbjds,
  a201020CalHj,
  calHjWithOldVersion,
  getQueryVariable,
  ysdjGetQmzcze,
  ysdjGetQczcze,
  ysdjGetcyrs,
  djxsjmsdsyh,
  alertFhtjdyxzlxsrLj,
  check6Zcyz,
  zyywsrb,
  zgcawhqdbjdazwfqzfsrLjReadonly,
  zgawhqdbjdazwsfsrLjReadonly,
  jzmzlxL15,
  setJbrzyzjhm,
  sfsbjccgb,
  fxsmServer,
  getFxsmParams,
  extractedNotMandatoryScan,
  extractedShowMessage,
  extractedNotConfigured,
  handleHtBw,
  hadleFhBw,
  numberToChinese,
  sectionToChinese,
  sumifArr,
  getZfjgsjxx,
  setSjlreLj,
  openYxzTips,
  ydyjskCx,
  checkcsh,
  setJmsdsyhsxBnlj,
  jyJmsdsyhsxSftg,
  getJMSE00601,
  getFzzgSfsyXbdkf,
  getFzzgSfyhndq,
  getQycyrsQnpjrsTip,
  getZczeQnpjsTip,
  getBjgxhTip,
  xmqylxjy6,
  setJmsdsyhsxSelect,
  setFbsxSqxx,
  setMssrsxSqs,
  setSdjmsxSqs,
  lineNum,
  bmdSfxsJmsdYh,
  disableFpblfbse,
  xmgxhQcgzbyzJd,
  xmgxhQcgzbyzJdTs,
  sort20000Grid,
  fb2GetHyDmSfbg,
  fzjgDqlx,
  setMzzzdqdfjmLj,
  setMzzzdqdfjmzfjghjLj,
  isXbdkfdq,
  isHnzmg,
  isZhPtSz,
  isFjptlx,
  isDdhqlx,
  isSzqhlx,
  sdjmyhsxSetXh,
  setXsdfjmje,
  calFpbl,
  calFzjgfpsdseBq,
  initTdstx,
  fqsqts,
  setSfsJmyb,
  atleastOne,
  checkAtleastOne_Xbdkfdq,
  checkAtleastOne_Hnzmg,
  checkAtleastOne_ZhPtSz,
  sbzxBatchImportValidate,
  gethylb,
  saveQysdsWjdc,
  dsjdwxsyffyjjkcyhVerify,
  jmsdseInfo,
  jmsdVerifyfpb,
  setJzmzlx,
  setJzfd,
  checkJzfd,
  bccjbztzs,
  cxWszb,
  pjxml,
  sqXmdmVerify,
  saveTsqk,
  matchingFzjglb,
  toGdSty,
  bjJjkcTxtx,
  getGxjsYcxkcHj,
  arrIndexByVal,
  isGdnslx,
  calclc13_1yhjmje,
  isSqs,
  dmbFilter_gdzcjszjCT,
  dmbFilter_jszjYcxkcCT,
  dmbFilter_jszjYcxkc22nyhCT,
  filterWbcsh,
  cwDataVerify,
  generatePdfAssignments,
};
