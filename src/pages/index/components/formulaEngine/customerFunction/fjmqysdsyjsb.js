import store from '@/pages/index/store'

const { ywbm } = store.state['sb/fjmqysdsyjsb/form'];

/**
 * 初始化主体生产经营业务分摊所得税情况信息和汇总纳税但未分摊所得税机构、场所情况信息
 * @returns
 */
function initDthgrild(){
	var qtjgxxGridlb=formData.hq_.qtjgxxGrid.qtjgxxGridlb;
	var wftjgxxGridlb= formData.hq_.wftjgxxGrid.wftjgxxGridlb;

	if(!isEmptyObject(qtjgxxGridlb)&&qtjgxxGridlb.length>0){
		formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb=[];
		for(var i=0;i<qtjgxxGridlb.length;i++){
			var ztscjyGridlb={};
			ztscjyGridlb.qtjgdjxh=qtjgxxGridlb[i].qtjgdjxh;
			ztscjyGridlb.qtjglx=qtjgxxGridlb[i].qtjglx;
			ztscjyGridlb.qtjgnsrsbh=qtjgxxGridlb[i].qtjgnsrsbh;
			ztscjyGridlb.qtjgnsrmc=qtjgxxGridlb[i].qtjgnsrmc;
			ztscjyGridlb.qtjgcsxzqh=qtjgxxGridlb[i].qtjgcsxzqh;
			ztscjyGridlb.qtjgcsxzqhMc=getDmFromCodeTable([{'url':'dm_sb_shi.json','name':'sjCT','node':'','dm':'','mc':'','filter':''}],'sjCT',qtjgxxGridlb[i].qtjgcsxzqh,'');
			ztscjyGridlb.yysr=qtjgxxGridlb[i].yysr;
			ztscjyGridlb.zgxc=qtjgxxGridlb[i].zgxc;
			ztscjyGridlb.zcze=qtjgxxGridlb[i].zcze;
			ztscjyGridlb.flag=true;
            formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb.push(ztscjyGridlb);
		}
	}
	
	if(!isEmptyObject(wftjgxxGridlb)&&wftjgxxGridlb.length>0){
		formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.hznswftGrid.hznswftGridlb=[];
		for(var j=0;j<wftjgxxGridlb.length;j++){
			var hznswftGridlb={};
			hznswftGridlb.wftjgdjxh=wftjgxxGridlb[j].wftjgdjxh;
			hznswftGridlb.wftjgnsrsbh=wftjgxxGridlb[j].wftjgnsrsbh;
			hznswftGridlb.wftjgmc=wftjgxxGridlb[j].wftjgmc;
			hznswftGridlb.wftjgxzqh=wftjgxxGridlb[j].wftjgxzqh;
			hznswftGridlb.wftjgxzqhMc=getDmFromCodeTable([{'url':'dm_sb_shi.json','name':'sjCT','node':'','dm':'','mc':'','filter':''}],'sjCT',wftjgxxGridlb[j].wftjgxzqh,'');
			hznswftGridlb.cbfye=wftjgxxGridlb[j].cbfye;
			hznswftGridlb.zcze=wftjgxxGridlb[j].zcze;
			hznswftGridlb.flag=true;
			formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.hznswftGrid.hznswftGridlb.push(hznswftGridlb);
		}
	}
}

/**
 * 初始化F400Gridlb的信息
 */
function initF400Gridlb(){
	var zsfsDm=formData.hq_.sbxxGrid.sbxxGridlb[0].zsfsDm;
	var fjmqysdshdGridlb=formData.fq_.hdxx.fjmqysdshdGrid.fjmqysdshdGridlb;
	var jnjghgrfbxmlxlb=formData.fq_.jnjglxmc.jnjghgrfbxmlxlb;
	if (zsfsDm==405) {
		formData.ht_.fjmqysdsyjsb2019Ywbw.F400Ywbd.F400Grid.F400Gridlb=[];
	}
	for(var i=0;i<fjmqysdshdGridlb.length;i++){
		if (fjmqysdshdGridlb[i].hdzsfsDm==405&&zsfsDm==405) {
			var f400Gridlb={};
			f400Gridlb.asrzehdsre=0.00;
			f400Gridlb.asrzehdynssde=0.00;
			f400Gridlb.asrzehdhdlrl=fjmqysdshdGridlb[i].hylrl;
			var xmmc_1="";
			if (!fjmqysdshdGridlb[i].xmmc) {
				fjmqysdshdGridlb[i].xmmc = ''
			}
			if (!fjmqysdshdGridlb[i].htzh) {
				fjmqysdshdGridlb[i].htzh = ''
			}
			f400Gridlb.xmmc=fjmqysdshdGridlb[i].xmmc+'|'+fjmqysdshdGridlb[i].htzh+'|';

			for(var j=0;j<jnjghgrfbxmlxlb.length;j++){
				if (!fjmqysdshdGridlb[i].xmlx) {
					fjmqysdshdGridlb[i].xmlx = ''
				}
				if(fjmqysdshdGridlb[i].xmlx.indexOf(jnjghgrfbxmlxlb[j].jnjghgrfbxmlxdm)!=-1){
					if(xmmc_1==""){
						xmmc_1+=jnjghgrfbxmlxlb[j].jnjghgrfbxmlxmc;
					}else{
						xmmc_1+=','+jnjghgrfbxmlxlb[j].jnjghgrfbxmlxmc;
					}
				}
			}
			f400Gridlb.xmmc=f400Gridlb.xmmc+xmmc_1;
			formData.ht_.fjmqysdsyjsb2019Ywbw.F400Ywbd.F400Grid.F400Gridlb.push(f400Gridlb);
		}else if(zsfsDm==406&&fjmqysdshdGridlb[i].hdzsfsDm==406){
			formData.ht_.fjmqysdsyjsb2019Ywbw.F400Ywbd.F400Form.acbfyhdhdlrl=fjmqysdshdGridlb[i].hylrl;
		}else if(zsfsDm==407&&fjmqysdshdGridlb[i].hdzsfsDm==407){
			formData.ht_.fjmqysdsyjsb2019Ywbw.F400Ywbd.F400Form.ajfzchshdlrl=fjmqysdshdGridlb[i].hylrl;
		}
	}
}

/**
 * 设置分配比例
 * @returns
 */
function setFpbl(yysrhj,zgxchj,zczehj){
	var ztscjyGridlb=formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	//分配比例总和
	var sumFpbl=0;
	//用来标识是否存在独立部门
	var flagIndex=-1;
	
	if(ztscjyGridlb.length==1&&ztscjyGridlb[0].yysr==0&&ztscjyGridlb[0].zgxc==0&&ztscjyGridlb[0].zcze==0){
		ztscjyGridlb[0].fpbl=0;
		var _jpath2 = "ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb[#].fpbl";
		formulaEngine.apply(_jpath2,  "");
		return ;
	}

	for(var i=0;i<ztscjyGridlb.length;i++){
		if(ztscjyGridlb[i].qtjglx=="dlbm"){
			flagIndex=i;
		}
		var yysrFpbl=ROUND((ztscjyGridlb[i].yysr/yysrhj)*0.35,16);
		
		var zgxcFpbl=ROUND((ztscjyGridlb[i].zgxc/zgxchj)*0.35,16);
		
		var zczeFpbl=ROUND((ztscjyGridlb[i].zcze/zczehj)*0.30,16);
		
		ztscjyGridlb[i].fpbl=ROUND(yysrFpbl+zgxcFpbl+zczeFpbl,10);
		
		sumFpbl+=ztscjyGridlb[i].fpbl;
	}
	
	if(flagIndex==-1){
		ztscjyGridlb[ztscjyGridlb.length-1].fpbl=ROUND((1-(sumFpbl-ztscjyGridlb[ztscjyGridlb.length-1].fpbl)),10);
	}else{
		ztscjyGridlb[flagIndex].fpbl=ROUND((1-(sumFpbl-ztscjyGridlb[flagIndex].fpbl)),10);
	}
	
	formData.kz_.temp.f300Fpb.fpblhj=sumFpbl;
	
	var _jpath2 = "ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb[#].fpbl";
	formulaEngine.apply(_jpath2,  "");
}

/**
 *设置分配所得税额
 */
function setFpsdse(ztscjyywftsdse,fpblhj){
	var ztscjyGridlb=formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	//用来标识是否存在独立部门
	var flagIndex=-1;
	//分配所得税额总和
	var sumFpsdse=0;
	

	if(ztscjyGridlb.length==1&&ztscjyGridlb[0].yysr==0&&ztscjyGridlb[0].zgxc==0&&ztscjyGridlb[0].zcze==0&&ztscjyGridlb[0].fpbl==0){
		ztscjyGridlb[0].fpsdse=0;
		var _jpath2 = "ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb[#].fpsdse";
		formulaEngine.apply(_jpath2,  "");
		return ;
	}
	
	
	for(var i=0;i<ztscjyGridlb.length;i++){
		ztscjyGridlb[i].fpsdse=ROUND(ztscjyGridlb[i].fpbl*ztscjyywftsdse,2);
		if(ztscjyGridlb[i].qtjglx=="dlbm"){
			flagIndex=i;
		}
		sumFpsdse+=ztscjyGridlb[i].fpsdse;
	}
	
	if(flagIndex==-1){
		ztscjyGridlb[ztscjyGridlb.length-1].fpsdse=ROUND((ztscjyywftsdse-(sumFpsdse-ztscjyGridlb[ztscjyGridlb.length-1].fpsdse)),2);
	}else{
		ztscjyGridlb[flagIndex].fpsdse=ROUND((ztscjyywftsdse-(sumFpsdse-ztscjyGridlb[flagIndex].fpsdse)),2);
	}
	
	var _jpath1 = "ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb[#].fpsdse";
	formulaEngine.apply(_jpath1,  "");
}
/**
 * 非空校验
 * @param obj
 * @returns {Boolean}
 */
function isEmptyObject(obj){
	if(obj===""||obj===null||obj===undefined){
		return true;
	}else{
		return false;
	}
}


/**
 *设置主表实际利润额
 */
function setSjlrejssb(cs,jsfs,yjfs){
	if(jsfs=='1'&&yjfs!="2"){
		return cs;
	}
	return formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.sbbxxForm.sjlrejssb;
}

/**
 * 处理境外成立地代码静态码表
 * @param jmgdqszdm
 */
function jwcldmMbz(jmgdqszdm){
	formData.kz_.temp.f400mxb.jwcldmmbmc=formCT.gjhdqCT1[jmgdqszdm];
}

/**
 *设置主表应补退税额
 */
function setYbtsdse(jscs,hdcs){
	var yjfs=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.yjfs;
	var sbqylx=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.sbqylx;
	var jsfs=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.jsfs;
	
	if(jsfs=="1"&&(yjfs=="1"||yjfs=="2")&&(sbqylx=="0"||sbqylx=="1")){
		return MAX(0,jscs);
	}
	
	if(jsfs=="2"&&(sbqylx=="0"||sbqylx=="1")){
		return MAX(0,hdcs);
	}
	
	return  formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.sbbxxForm.ybtsdse;
	
}

/**
 * 主要机构、场所纳税人识别号（统一社会信用代码）
 * @param sbqylx
 * @param nsrsbh
 * @returns
 */
function setZyjgcsnsrsbh(sbqylx,nsrsbh){
	if(sbqylx==1){
		return nsrsbh;
	}else if(sbqylx==2){
		var zjgnsrsbh=formData.hq_.zyjgxxVO.zyjgcsnsrsbh;
		if(isEmptyObject(zjgnsrsbh)){
			var zjgxxBycx=formData.fq_.nsrjbxxByZyjgcsdjxh;
			if(!isEmptyObject(zjgxxBycx)){
				zjgnsrsbh=zjgxxBycx.nsrsbh;
			}
		}
		
		return zjgnsrsbh;
	}
}

/**
 * 主要机构、场所名称
 * @param sbqylx
 * @param nsrmc
 * @returns
 */
function setZyjgcsmc(sbqylx,nsrmc){
	if(sbqylx==1){
		return nsrmc;
	}else if(sbqylx==2){
		var zjgmc=isEmptyObject(formData.hq_.zyjgxxVO)?"":formData.hq_.zyjgxxVO.zyjgcsmc;
		if(isEmptyObject(zjgmc)){
			var zjgxxBycx=formData.fq_.nsrjbxxByZyjgcsdjxh;
			if(!isEmptyObject(zjgxxBycx)){
				zjgmc=zjgxxBycx.nsrmc;
			}
		}
		return zjgmc;
	}
}

/**
 * 判断主要机构、场所名称是否能修改
 * @param sbqylx
 */
function flagZyjgcsmc(sbqylx){
	if(sbqylx==1){
		return true;
	}else if(sbqylx==2){
		var disabledFlag = formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.zyjgForm.flag;
		if (disabledFlag) {
			return true;
		}
		var zjgmc=isEmptyObject(formData.hq_.zyjgxxVO)?"":formData.hq_.zyjgxxVO.zyjgcsmc;
		if(isEmptyObject(zjgmc)){
			var zjgxxBycx=formData.fq_.nsrjbxxByZyjgcsdjxh;
			if(!isEmptyObject(zjgxxBycx)){
				zjgmc=zjgxxBycx.nsrmc;
			}
		}
		
		if(!isEmptyObject(zjgmc)){
			return true;
		}else{
			return false;
		}
	}
}

//已缴税额的计算

function getYjse(kyyjye, sqyjje, yyyjje) {
 var csz = formData.fq_.sjyjseqzBz;
	if (csz == undefined || csz == null || csz == "" || csz == "1") {
		return kyyjye + sqyjje + yyyjje;
	}
	if (csz == "2") {
		return kyyjye + sqyjje;
	}
	if (csz == "3") {
		return sqyjje + yyyjje;
	}
	if (csz == "4") {
		return kyyjye + yyyjje;
	}
	if (csz == "5") {
		return kyyjye;
	}
	if (csz == "6") {
		return sqyjje;
	}
	if (csz == "7") {
		return yyyjje;
	}
	return kyyjye + sqyjje + yyyjje;

}

/**
 * 判断非居民企业机构、场所汇总缴纳所得税税款分配表中大连个性化和全国版的区别对应框的锁定情况
 */
function flagxfbGrid(sbqylx, flag){
	flag = flag || false;
  if (sbqylx == '1' || sbqylx == '2') {
    return flag;
  } else {
    return true;
  }
}

/**
 * 主表25行公式
 */
function getZyjgztscjyftsdse(qtjgnsrsbhArr,qtjgcsxzqhArr,fpsdseArr,zyjgcsxzqh,ztscjyywftsdse){
	var fzjgxx=formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	var nsrsbh=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.nsrsbh;
	zyjgcsxzqh=isEmptyObject(zyjgcsxzqh)?"":zyjgcsxzqh;
	var key1=nsrsbh+"_"+zyjgcsxzqh;
	for(var i=0;i<fzjgxx.length;i++){
		var jgsbh=fzjgxx[i].qtjgnsrsbh;
		var jgqh=isEmptyObject(fzjgxx[i].qtjgcsxzqh)?"":fzjgxx[i].qtjgcsxzqh;
		var key2=jgsbh+"_"+jgqh;
		if(key1===key2){
			return fzjgxx[i].fpsdse;
		}
	}
	
	return 0.00;
}

/**
 * 主表26、27行公式
 */
function  getQtjgFpxx(qtjgnsrsbhArr,qtjgcsxzqhArr,fpblArr,fpseArr,bz){
	var fzjgxx=formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	var nsrsbh=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.nsrsbh;
	for(var i=0;i<fzjgxx.length;i++){
		var jgsbh=fzjgxx[i].qtjgnsrsbh;
		if(nsrsbh===jgsbh){
		   return bz=="bl"?fzjgxx[i].fpbl:fzjgxx[i].fpsdse;
		}
	}
	return 0.00;
}


/**
 * 检验主表减免性质是否重复
 */
function checkRepeatJmxz(jxmz){
	if (isEmptyObject(jxmz)) {
		return true;
	}
	var mxlist = formData.kz_.temp.fjmqysdsyjsbZb.mxList;
	var mssrGrid = formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.mssrGrid.mxGridlb;
	var gdzcGrid = formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.gdzcGrid.mxGridlb;
	var jmsdGrid = formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.jmsdGrid.mxGridlb;
	var index_ = 0;
	var gjz = "";
	for (var i = 0; i < mxlist.length; i++) {
		if (mxlist[i].jmxzSwsxDm == jxmz) {
			index_++;
		}
		if (!isEmptyObject(mxlist[i].ewbhgjz)) {
			gjz = mxlist[i].ewbhgjz;
		}
	}

	if (index_ >= 2) {
		return false;
	}

	if (!isEmptyObject(gjz) && gjz == "mssrGrid") {

		for (var i = 0; i < gdzcGrid.length; i++) {
			if (gdzcGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "gdzcGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < jmsdGrid.length; i++) {
			if (jmsdGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	if (!isEmptyObject(gjz) && gjz == "jmsdGrid") {

		for (var i = 0; i < mssrGrid.length; i++) {
			if (mssrGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		for (var i = 0; i < gdzcGrid.length; i++) {
			if (gdzcGrid[i].jmxzSwsxDm == jxmz) {
				index_++;
			}
		}

		if (index_ >= 2) {
			return false;
		}

	}

	return true;
}





function sfyxsb(){
	var skssqq=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.skssqq;
	var sbqylx=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrxxForm.sbqylx;
	var nd=(skssqq).split("-")[0];
	if(nd=="2019"&&sbqylx=="0"){
		var msg="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;单独机构、场所纳税人税款所属期2020年1月之前的申报，请使用2015版本的申报表申报，谢谢！";	                                                                                              
		var a =layer.confirm(msg,{
		 	area: ['340px','220px'],
		 	title:'提示',
		 	closeBtn : 0,
			btn : ['确定']
			},function(data){
			layer.close(a);
			closeWin();
	});
	
	}
	
}

/**
 * 关闭当前页面
 */

function closeWin() {

	if (navigator.userAgent.indexOf("MSIE") > 0) {
		if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
			window.opener = null;
			window.close();
		} else {
			window.open('', '_top');
			window.top.close();
		}
	} else if (navigator.userAgent.indexOf("Firefox") > 0) {
		window.location.href = 'about:blank ';
		window.close();
	} else if (navigator.userAgent.indexOf("Chrome") > 0) {
		top.open(location, '_self').close();
	} else {
		window.open('', '_top');
		window.top.close();
	}

}

function getJbrxx(smzbz,csz,bz){
	var jbr=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrqtxxForm.jbr;
	var jbrsfzjhm=formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.nsrqtxxForm.jbrsfzjhm;
	if(bz=="xm"){
		return smzbz=="Y"?csz:jbr;
	}
	if(bz=="zjhm"){
		return smzbz=="Y"?csz:jbrsfzjhm;
	}
	return "";
}

/**
 * 在中国的联系地址、邮政编码：联系地址与邮政编码之间用分号隔开，联系地址长度100个字符，邮政编码长度6个字符
 * 在居民国（地区）的联系地址、邮政编码：联系地址与邮政编码之间用分号（中文分号）隔开，总长度不超过107字符。
 */
function checkLxdqyzbm(lxdqyzbm,bz){
	if(isEmptyObject(lxdqyzbm)){
		return false;
	}else if((lxdqyzbm).lastIndexOf("；")==-1){
		return false;
	}else{
		var lastIndexOf=lxdqyzbm.lastIndexOf("；");
		var yzbm=lxdqyzbm.substring(lastIndexOf+1);
		var lxdz=lxdqyzbm.substring(-1,lastIndexOf);
		if(bz=='zg'){
			var regYzbm=/^\d{6}$/;
			if(lxdz!=""&&lxdz.length<=100&&regYzbm.test(yzbm)){
				return true;
			}else{
				return false;
			}
		}else{
			if(lxdz!=""&&lxdqyzbm.length<=107&&yzbm.length>0){
				return true;
			}else{
				return false;
			}
		}
	}
}

/**
 * 检验电子邮箱
 * @param dzxx
 * @returns {Boolean}
 */
function checkDzxx(dzxx){
	//var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})$/;
	//取消协定附表[10.电子邮箱]中对点后域名的校验
	var reg=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)/;
	if(reg.test(dzxx)){
		return true;
	}else{
		return false;
	}
}

/**
 * 设置受理日期
 */
function setSlrq(){
	var sysTime=getCurrentSystemTime();
	return sysTime;
}



/**
 * 设置享受协定待遇所得金额
 */
function setXsxddysdje(sjlrejssb,sjlrehdzs,jsfs){
	if(jsfs==1){
		return sjlrejssb;
	}else if(jsfs==2){
		return sjlrehdzs;
	}
}

/**
 * 设置享受协定待遇减免税额
 * @param xsxddyjmsejssb
 * @param xsxddyjmsehdzs
 * @param jsfs
 * @returns
 */
function setXsxddyjmse(xsxddyjmsejssb,xsxddyjmsehdzs,jsfs){
	if(jsfs==1){
		return xsxddyjmsejssb;
	}else if(jsfs==2){
		return xsxddyjmsehdzs;
	}
}

/**
 * 获得当前系统时间 格式"yyyy-MM-dd"
 */
function getCurrentSystemTime(){
	//获取当前日期
	var currentDate = new Date();
	var currentMonth = currentDate.getMonth()+1;
	var currentDay = currentDate.getDate();
	if (currentMonth < 10){
		currentMonth = "0"+currentMonth;
	}
	if (currentDay < 10){
		currentDay = "0"+currentDay;
	}
	return currentDate.getFullYear()+"-"+currentMonth+"-"+currentDay;
}


//提交时对300表报文做特殊处理
function del300bdBw(){
	var fb300bw = formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd;
	if (isEmptyObject(fb300bw)) {
		return;
	}
	var fzjgxxList = formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	var hzjgxxList = formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.hznswftGrid.hznswftGridlb;
	var fzjgxxNew = [];
	var hzjgxxNew = [];
	for (var i=0; i < fzjgxxList.length; i++) {
		if (fzjgxxList[i].qtjgnsrsbh != '' && fzjgxxList[i].qtjgnsrsbh != null
				&& fzjgxxList[i].qtjgnsrsbh != undefined) {
			fzjgxxNew.push(fzjgxxList[i]);
		}
	}
	for (var i=0; i < hzjgxxList.length; i++) {
		if (hzjgxxList[i].wftjgnsrsbh != ''
				&& hzjgxxList[i].wftjgnsrsbh != null
				&& hzjgxxList[i].wftjgnsrsbh != undefined) {
			hzjgxxNew.push(hzjgxxList[i]);
		}
	}

	if(isEmptyObject(fzjgxxNew)|| fzjgxxNew.length==0){
		delete	formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb;
	}else{
		formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.ztscjyGrid.ztscjyGridlb=fzjgxxNew;
	}

	if(isEmptyObject(hzjgxxNew)|| hzjgxxNew.length==0){
		delete	formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.hznswftGrid.hznswftGridlb;
	}else{
		formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd.hznswftGrid.hznswftGridlb=hzjgxxNew;
	}
	
}

/**
 * 《非居民纳税人享受协定待遇信息报告表》在主表的第18行减：享受协定待遇减免税额【数值】大于0时必填！
非阻断提示，当属期在2020年及之后，触发，同时触发《非居民纳税人享受协定待遇信息报告表》内的校验。
 * @param jmse
 * @param ssqq
 * @returns {Boolean}
 */
function fzdjy(jmse,ssqq){
	if(jmse>0&&DATE_CHECK_TIME_SIZE('2020-01-01',ssqq)){
		return false;
	}else{
		return true;
	}
}

/**
 * 提交时对协定待遇表报文做特殊处理
 */
function delXddybdBw(){
	var xddybw = formData.ht_.fjmqysdsyjsb2019Ywbw.xddyYwbd;
	if (isEmptyObject(xddybw)) {
		return;
	}
	var dzbd=formData.kz_.dzbd;
	
	var xsxddyjmsejssb = formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.sbbxxForm.xsxddyjmsejssb;
	var xsxddyjmsehdzs = formData.ht_.fjmqysdsyjsb2019Ywbw.F100Ywbd.sbbxxForm.xsxddyjmsehdzs;
	var xsxddyjmse = formData.ht_.fjmqysdsyjsb2019Ywbw.xddyYwbd.xddyxx.xsxddyjmse;
    if(xsxddyjmsejssb<=0&&xsxddyjmsehdzs<=0&&xsxddyjmse<=0){
    	delete formData.ht_.fjmqysdsyjsb2019Ywbw.xddyYwbd;
    	var xdbd="BDA0611121---";
    	if(dzbd.indexOf(xdbd)>-1){
    		var star=dzbd.indexOf(xdbd);
    		var end=star+xdbd.length;
    		var dzbdStr=dzbd.substring(0,star)+dzbd.substring(end);
    		formData.kz_.dzbd=dzbdStr;
    	}
     }
}

function syncFetch(_url, _async, data) {
	let res;
	const xhr = new XMLHttpRequest();
	xhr.open('GET', _url, _async);
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			res = JSON.parse(xhr.response);
		}
	};

	xhr.send(JSON.stringify(data));
	return res;
}

/**
 * 从码表中获取值
 * @param codeTableAttrs 初始化码表需要的参数（参照ng-codetable把属性改成json对象，如果ng-codetable含有contact需要传入数组，统一都传入数组）
 * @param name 码表对象的名称（含有contact传入数组需要知道取最终码表对象的名称）
 * @param key 码表中对应dm的值，根据该值获取相应的value值
 * @param fieldName 码表中对应dm的value值是Object，根据该值获取Object中fieldName对应的值（可以不传）
 *
 * 实现步骤
 *  1、根据传入attributes初始化码表
 *  2、根据传入的name和key查找码表中相应key的值
 */
function getDmFromCodeTable(codeTableAttrs, name, key, fieldName, defVal) {
    if (defVal == undefined) {
        defVal = "";
    }
    
	var callBack = function() {
		var codeObject = formCT[name];
		if (undefined === codeObject) {
			return defVal;
		} else {
			var codeValue = codeObject[key];
			if (codeValue && fieldName) {
				codeValue = codeValue[fieldName];
			}

			if (!codeValue) {
				codeValue = defVal;
			}

			return codeValue;
		}
	}

	if (formCT[name]) {
		return callBack();
	} else {
		if (codeTableAttrs && codeTableAttrs.length > 0) {
			//初始化码表
			initCodeTable(undefined, codeTableAttrs[0]);
			return callBack();
		} else {
			return defVal;
		}
	}
}

/**
 * 初始化码表
 * 从码表数据来源层面来说支持三种方式的码表
 *  1、码表数据来源于url（json文件和普通的getDmb.do）
 *  2、码表数据来源于期初数model
 *  3、码表数据来源于带参数的请求params（/nssb/getDtdmb.do）
 * 码表请求支持同步异步配置async（默认为异步true）
 * 码表支持累加contact，对于两个来源的数据码表的name一样，最后的结果会做合并操作
 * @param $scope
 * @param attributes
 */
function initCodeTable($scope, attributes) {
    var _name = attributes["name"];
    var _url = attributes["url"];
    var _model = attributes["model"];
    var _params = attributes["params"];
    var _jsons = {};
   
    if (undefined === formCT[_name] || JSON.stringify(formCT[_name]) === "{}") {//判断是否已缓存
        if ((undefined !== _url && "" !== _url) || (undefined !== _params && "" !== _params)) {//URL来源
            getDmFromUrl($scope, attributes, _jsons);
        } else if (undefined !== _model && "" !== _model) {//期初数来源
            getDmFromModel($scope, attributes, _jsons);
        } else {
            //codetable指令相关参数缺失
            console.log("ERROR:codetable指令相关参数缺失，请检查...");
            return;
        }
    }
}

/**
 * 从url（json文件和普通的getDmb.do）获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromUrl($scope, attributes, _jsons) {
    // 默认为同步
    var _async = false;
    var _node = attributes["node"];
    var _name = attributes["name"];
    var _url = attributes["url"];

    var _params = attributes["params"];
    var _dynamicParam = attributes["dynamic"];
    var _data;

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    var data = {};
	var index = location.pathname.lastIndexOf('/');
	_url = location.pathname.substr(0, index) + "/static/sb/" + ywbm + "/form/" + _url;
        
	// 允许添加参数，param为key, dynamicParam为value，可以多个逗号隔开，但个数必须一致
	if (_params && _dynamicParam) {
		var aryParam = _params.split(',');
		var aryDynamic = _dynamicParam.split(',');
		if (aryParam && aryDynamic && aryDynamic.length === aryDynamic.length) {
			for (var idx = 0; idx < aryParam.length; idx++) {
				_data = jsonPath($scope ? $scope.formData : formData, aryDynamic[idx])[0];
				if (_data) {
					_url = _url + (idx === 0 ? "?" : "&") + aryParam[idx] + "=" + _data;
				} else {
					// 发现有不符合的，则不进行拼接
					break;
				}
			}
		}
	}

	const response = syncFetch(_url, _async, data);
	_data = response;
	if (undefined === _url || "" === _url) {
		if (typeof response === "string") {
			response = JSON.parse(response);
		}
		if (response && response["dtdmbxx"]) {
			response = response["dtdmbxx"];
		}
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}
		if (typeof _data === "string") {
			_data = JSON.parse(_data);
		}
		if (_data && _data["root"]) {
			Object.keys(_data).forEach((k)=> {
				const v = _data[k];
				_jsons[v["dm"]] = v;
			});
			_data = _jsons;
		}
	} else {
		if (undefined === _node || "" === _node) {
			_data = response;
		} else {
			_data = response[_node];
		}

		var doFilter = false;
		Object.keys(_data).forEach((k)=> {
			const v = _data[k];
			doFilter = false;
			if (filterKey && filterValue && v) {
				if (v[filterKey] !== filterValue) {
					doFilter = true;
				}
			}
			if (!doFilter) {
				_jsons[k] = v;
			}
		});
	}

	formCT[_name] = _jsons;
}

/**
 * 从期初数model获取码表数据
 * @param $scope
 * @param attributes
 * @param _jsons
 */
function getDmFromModel($scope, attributes, _jsons) {
    var _name = attributes["name"];
    var _multi = attributes["multi"];
    var _data;

    var _model = attributes["model"];
    var _dm = attributes["dm"];

    var filterParam = attributes["filter"];
    var filterKey = filterParam ? filterParam.split("#")[0] : null;
    var filterValue = filterParam ? filterParam.split("#")[1] : null;

    _data = jsonPath($scope ? $scope.formData : formData, _model)[0];
    if (undefined === _data || "" === _data) {
        console.log("ERROR:codetable指令缓存代码表获取的data为空，对应的model为:" + _model + ",name为:" + _name);
        return;
    }

    if (undefined !== _dm && "" !== _dm) {
        var doFilter = false;
        Object.keys(_data).forEach((k)=> {
			const v = _data[k];
            doFilter = false;
            if (filterKey && filterValue) {
                if (v[filterKey] !== filterValue) {
                    doFilter = true;
                }
            }
            if (!doFilter) {
                if (_multi === 'true') {
                    if (!_jsons[v[_dm]])
                        _jsons[v[_dm]] = [];
                    _jsons[v[_dm]].push(v);
                } else {
                    _jsons[v[_dm]] = v;
                }
            }
        });
        _data = _jsons;
    }

	formCT[_name] = _data;
}

/**
 * 设置非居民纳税人签章或签字日期，自动带出系统日期
 */
function setFjmnsrqzrq(){
	return getCurrentSystemTime();
}

// 初始化时备份300表报文
function copyF300Bw2Kz() {
	formData.kz_.temp.F300Ywbd = JSON.stringify(formData.ht_.fjmqysdsyjsb2019Ywbw.F300Ywbd);
}

// 提交时删除备份的300表报文
function delKzF300Bw() {
	delete formData.kz_.temp.F300Ywbd;
}

export default {
	initDthgrild,
	initF400Gridlb,
	setFpbl,
	setFpsdse,
	isEmptyObject,
	setSjlrejssb,
	jwcldmMbz,
	setYbtsdse,
	setZyjgcsnsrsbh,
	setZyjgcsmc,
	flagZyjgcsmc,
	getYjse,
	flagxfbGrid,
	getZyjgztscjyftsdse,
	getQtjgFpxx,
	checkRepeatJmxz,
	sfyxsb,
	closeWin,
	getJbrxx,
	checkLxdqyzbm,
	checkDzxx,
	setSlrq,
	setXsxddysdje,
	setXsxddyjmse,
	del300bdBw,
	fzdjy,
	delXddybdBw,
	getDmFromCodeTable,
	setFjmnsrqzrq,
	copyF300Bw2Kz,
	delKzF300Bw
}