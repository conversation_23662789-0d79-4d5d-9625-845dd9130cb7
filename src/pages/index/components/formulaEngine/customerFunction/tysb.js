

window.ywztBz = true;

/**
 * 根据方法名执行对应方法
 * @param mathFlag
 * @param data
 * @param scope
 */
function extMethods(mathFlag, newData, olddata, scope) {
  if ("initSfxgm" == mathFlag) {
    isXgm();
  }

  //是“增值税小规模纳税人季中转一般纳税人”：（jzznsrzglx=03）
  //进入申报表页面，初始化弹出提示框：申报属期内同时存在一般人和小规模两种纳税人状态，若使用该申报功能，系统会全额计算减征额，请自行折算并修改减征额。点击【确定】按钮，弹出框关闭。
  //ng-init初始化执行
  if ("jzznsrVaild" == mathFlag) {
    jzznsrVaild();
  }

}

function jzznsrVaild() {
  //是否使用季中转规则的配置项
  var allowJzz = parent.formData.fq_.jzxx.xgmzg.allowJzz;
  //未配置或配置不为Y，跳过校验
  if (allowJzz == "N") {
    return;
  }
  //季中转纳税人类型
  var jzznsrzglx = parent.formData.fq_.jzxx.xgmzg.jzznsrzglx;
  if (jzznsrzglx == "03") {
    var tips = "申报属期内同时存在一般人和小规模两种纳税人状态，若使用该申报功能，系统会全额计算减征额，请自行折算并修改减征额。";
    var b = layer.confirm(tips, {
      title: '提示',
      closeBtn: false,
      btn: ['确定']
    }, function (index) {
      layer.close(b);
    });
    return;
  }
}


/**
 * 申报提交时框架校验成功后的业务特有校验，空方法，预留给产品具体业务实现
 * @param callBeforSubmitForm ：回调方法，调用表单提交前的业务特有提示
 * @param callSubmitForm ：回调方法，调用表单提交
 * @param params : 回调参数
 */
function doAfterVerify(callBeforSubmitForm, callSubmitForm, params) {
  /**
   * 已经把prepareMake中所有带有ywbm判断的代码都迁移到了这里
   * 由申报组吧具体的ywbm判断再迁移到具体的ywbm.js中
   * 迁移完成后doAfterVerify应该只有callBeforSubmitForm(callSubmitForm,params);这一句代码
   */
  var sftjbz = submitValid();
  if (sftjbz === false) {
    layer.alert("请选择需要申报的项目！", {icon: 2});
    $("body").unmask();
    if (typeof umMaskZdy == "function") {
      umMaskZdy();
    }
    prepareMakeFlag = true;
    return;
  }
  callBeforSubmitForm(callSubmitForm, params);
}

/**
 * 更正申报通过接口数据判断bqsfsyxgmyhzc的值
 * <phjmswsxDm></phjmswsxDm>
 * <phjmxzDm></phjmxzDm>
 * 有上面两个节点则bqsfsyxgmyhzc=Y，没有则bqsfsyxgmyhzc=N
 */
function setBqsfsyxgmyhzc() {
  var sbxxGridlbs = formData.fq_.sbxxGrid.sbxxGridlbVO;
  for (var index = 0; index < sbxxGridlbs.length; index++) {
    if (sbxxGridlbs[index].phjmxzDm != null && sbxxGridlbs[index].phjmxzDm != '' && sbxxGridlbs[index].phjmswsxDm != null && sbxxGridlbs[index].phjmswsxDm != '') {
      return 'Y';
    }
  }
  return 'N';
}

function isXgm() {
  var xgmbzQcs = parent.formData.fq_.jzxx.xgmzg.sfzzsxgmjz;
  if (xgmbzQcs == 'N') {
    var tips = "本期不适用增值税小规模纳税人优惠政策！请确认";
    var b = parent.layer.confirm(tips, {
      area: ['200px', '200px'],
      title: '提示',
      closeBtn: false,
      btn: ['是'],
      btn2: function (index) {

      }
    }, function (index) {
      parent.layer.close(b);
    });
  }
}

var phjzZsxmDms = ['10110', '10111', '10112', '10118', '10107', '10109', '30203', '30216'];



var getPhjzbl = function (zsxmDm, bqsfsyxgmyhzc, phjmBz, phjmxzDm) {
  var skssqq = formData.fq_.jzxx.sssqQ;
  var skssqz = formData.fq_.jzxx.sssqZ;
  // var allowJzz = formData.fq_.jzxx.xgmzg.allowJzz;
  var endTime = formData.fq_.jzxx.xgmzg.endTime;
  var startTime = formData.fq_.jzxx.xgmzg.startTime;
  var jzxxMxList = formData.fq_.jzxx.jzxxMxList;
  //一般纳税人季中转小规模(04)时，xgmbzQcs默认为N
//	var xgmbzQcs = formData.fq_.jzxx.xgmzg.jzznsrzglx=="04"?"N":formData.fq_.jzxx.xgmzg.sfzzsxgmjz;
//     var allowJzz = formData.fq_.jzxx.xgmzg.allowJzz;
//     var xgmbzQcs = "";
//     if ((formData.fq_.jzxx.xgmzg.jzznsrzglx == "04" || formData.fq_.jzxx.xgmzg.jzznsrzglx == "03")) {
//         xgmbzQcs = bqsfsyxgmyhzc;
//     } else {
//         xgmbzQcs = formData.fq_.jzxx.xgmzg.sfzzsxgmjz;
//     }
  var zsxm2jzbl = {};
  for (var index in jzxxMxList) {
    var jzxx = jzxxMxList[index];
    zsxm2jzbl[jzxx.zsxmDm] = jzxx.jzbl;
  }
  if (skssqq >= '2019-01-01' && skssqz <= endTime) {
    if ((bqsfsyxgmyhzc == 'Y' && phjmxzDm)) {
      //配置不存在或者是广东附加税的  返回0
      if (zsxm2jzbl[zsxmDm] == undefined || zsxmDm == "10106") {
        return 0.00;
      }
      switch (zsxmDm) {
        case '10109':
        case '30203':
        case '30216':
        case '10110':
        case '10111':
        case '10112':
        case '10118':
        case '10107':
          return zsxm2jzbl[zsxmDm];
        default:
          return 0.00;
      }
    } else {
      return 0.00;
    }
  } else {
    return 0.00;
    // if (formData.fq_.jzxx.xgmzg.jzznsrzglx == "03" || formData.fq_.jzxx.xgmzg.jzznsrzglx == "02") {
    //     return 0.00;
    // } else if (formData.fq_.jzxx.xgmzg.jzznsrzglx == "01" || formData.fq_.jzxx.xgmzg.jzznsrzglx == "04") {
    //     //配置不存在或者是广东附加税的  返回0
    //     if (zsxm2jzbl[zsxmDm] == undefined || zsxmDm == "10106") {
    //         return 0.00;
    //     }
    //     switch (zsxmDm) {
    //         case '10109':
    //         case '30203':
    //         case '30216':
    //         case '10110':
    //         case '10111':
    //         case '10112':
    //         case '10118':
    //         case '10107':
    //             return zsxm2jzbl[zsxmDm];
    //         default:
    //             return 0.00;
    //     }
    // }
  }
}

//当为一般纳税人时，让用户选择是否要申报减征优惠
var sfsyxgmyhzc = function (rqQ, sfxgm) {
  var days = getDays(rqQ);
  var sfxgmQcs = formData.fq_.jzxx.xgmzg.sfzzsxgmjz;
  var sfqzxXgmFlag = formData.fq_.jzxx.xgmzg.sfzzsxgmjz;
  if (days >= 0) {
    var tmp = true;
    if (sfxgmQcs == 'N' && sfqzxXgmFlag == 'N' && sfxgm == 'Y') {
      //tmp = false;
      var tips = "增值税一般纳税人不适用增值税小规模纳税人减征政策，是否确定要申报减征优惠？";
      var b = parent.layer.confirm(tips, {
        title: '提示',
        closeBtn: false,
        btn: ['是', '否'],
        btn2: function (index) {
          //置空增值税小规模纳税人减免性质和增值税小规模纳税人减征比例
          var sbxxGridlbArr = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
          if (sbxxGridlbArr != null && sbxxGridlbArr != undefined && sbxxGridlbArr.length > 0) {
            for (var i = 0; i < sbxxGridlbArr.length; i++) {
              sbxxGridlbArr[i].phjzbl = 0.00;
              sbxxGridlbArr[i].phjmxzMc = "";
              sbxxGridlbArr[i].phjmse = 0.00;
              //重新执行相关公式
              formulaEngine.apply("ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[" + i + "].phjmse", "N");
            }
          }
          // 去子页面刷新视图，子页面需要引入var subViewCustomScripts = ["/sbzs_res/tysb/js/tysb_cus.js"];
          $("#frmSheet")[0].contentWindow.refreshView();
          parent.layer.close(index);
        }
      }, function (index) {
        parent.layer.close(b);
      });
    } else if (sfxgmQcs == 'N' && sfqzxXgmFlag == 'Y' && sfxgm == 'Y') {
      tmp = false;
      var tips = "您本期非增值税小规模纳税人，不适用增值税小规模纳税人减征政策！";
      var b = parent.layer.confirm(tips, {
        area: ['200px', '200px'],
        title: '提示',
        closeBtn: false,
        btn: ['是'],
        btn2: function (index) {

        }
      }, function (index) {
        parent.layer.close(b);
      });
    }
    return tmp;
  } else {
    return true;
  }
}

//云上电局过滤除工会经费和水利基金以外的其他品目
var ysdjGlZspm = function () {
  // var sbxxGridlbVO = formData.fq_.sbxxGrid.sbxxGridlbVO;
  // var sbxxGridlbVO_new = [];
  // for (var i = 0; i < sbxxGridlbVO.length; i++) {
  //   /* if (sbxxGridlbVO[i].zsxmDm == "10106") {
  //        if (sbxxGridlbVO[i].zszmDm == "1010601002440031") {
  //            sbxxGridlbVO_new.push(sbxxGridlbVO[i]);
  //        }
  //    } else {
  //        sbxxGridlbVO_new.push(sbxxGridlbVO[i]);
  //    }*/
  //   // TODO 暂时写死工会经费
  //   if (sbxxGridlbVO[i].zspmDm === "399001010" || sbxxGridlbVO[i].zspmDm === "399001020") {
  //     sbxxGridlbVO_new.push(sbxxGridlbVO[i]);
  //   }
  // }
  // // TODO 如果认定没有税费种认定，则默认工会经费
  // if (formData.fq_.t_whdGhjf === 'Y') {
  //   var zspmCT = [
  //     {
  //       "zspmDm":"399001010",
  //       "zspmMc":"工会经费"
  //     },
  //     {
  //       "zspmDm":"399001020",
  //       "zspmMc":"工会筹备金"
  //     }
  //   ];
  //   formEngine.cacheCodeTable("zspmCT", zspmCT);
  // }
  // formData.fq_.sbxxGrid.sbxxGridlbVO = sbxxGridlbVO_new;
};

/**
 *  自动带出减免性质代码逻辑统一在此处理.
 */
function setSsjmxzDm(gzsbBz, fq_ssjmxzDm, zsxmDm, nsqxDm, ysx, jcx, zspmDm, sfkssqq, sfkssqz, index) {
  var bqynsfe = ysx - jcx;
  if (bqynsfe == 0) {
      return "";
  }
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var isXsjm = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[index].t_isXsjm;
  var jmxzDm = "";
  if(zsxmDm == "30221"){
      // 陕西
      if(zgswjDm.substring(0, 3) == '161'){
          if((nsqxDm == "06" && ysx <= 100000) || (nsqxDm == "08" && ysx <= 300000)){
              // 小于10万/30万，符合九大行业（且所属期在2020/1/1之后的）
              if(isHyDm() && DATE_CHECK_TIME_SIZE("2020-01-01", sfkssqq)){
                  jmxzDm = '6199011601';
              // 所属期在2016/2/1之后的
              }else if(DATE_CHECK_TIME_SIZE("2016-02-01", sfkssqq)){
                  jmxzDm = '0099129901';
              }
          }else if((nsqxDm == "06" && ysx > 100000) || (nsqxDm == "08" && ysx > 300000)){
              // 大于10万/30万,符合三大行业（且所属期2016-01-01至2025-12-31）
              if(isHyDm("Y") && DATE_CHECK_TIME_SIZE("2016-01-01", sfkssqq) && DATE_CHECK_TIME_SIZE(sfkssqz, "2025-12-31")){
                  jmxzDm = '6199010517';
              }
              // 符合九大行业（且所属期在2020/1/1之后的）
              if (isHyDm() && DATE_CHECK_TIME_SIZE("2020-01-01", sfkssqq) && bqynsfe > 0) {
                  jmxzDm = "6199011601";
              }
          }
        // 安徽  
      }else if(zgswjDm.substring(0, 3) == '134'){
        // 禅道：50706,安徽省大于10万/30万且为中小微企业
        if(((nsqxDm == "06" && ysx > 100000) || (nsqxDm == "08" && ysx > 300000)) && isXsjm == 'Y'){
          jmxzDm = '3499032401';
        // 其他情况按照全国版处理
        } else if(DATE_CHECK_TIME_SIZE("2016-02-01", sfkssqq) && ((nsqxDm == "06" && ysx <= 100000) || (nsqxDm == "08" && ysx <= 300000))){
          jmxzDm = '0099129901';
        }
      }else{
          // 全国版
          // 小于10万/30万，所属期在2016/2/1之后的
          if(DATE_CHECK_TIME_SIZE("2016-02-01", sfkssqq) && ((nsqxDm == "06" && ysx <= 100000) || (nsqxDm == "08" && ysx <= 300000))){
              jmxzDm = '0099129901';
          }
      }
  }
  return jmxzDm;
}

/**
 * 陕西个性化:JCDA2019-10513
 */
function changeJmxzDmSn(ysx, zspmDm, nsqxDm, bqynsfe, sfkssqq, sfkssqz) {
  //初始化码表,如果码表在html的来源被改了, 此处也要跟着改
  getDmFromCodeTable([{
    'model': 'fq_.sbxxGrid.sbxxGridlbVO',
    'name': 'jmxzDmCT',
    'node': '',
    'dm': 'zspmDm',
    'mc': ''
  }], 'jmxzDmCT');
  var jmxzOption = formCT.jmxzDmCT[zspmDm].option;
  /*
   * jzznsrzglx
   * 01 ：小规模纳税人
   * 02 ：一般纳税人
   * 03 ：小规模季中转一般人
   * 04 ：一般人季中转小规模
   */
  for (var i = 0; i < jmxzOption.length; i++) {
    //SNSWJ-1833:水利基金新增需求
    if (DATE_CHECK_TIME_SIZE("2021-01-01", sfkssqq) && DATE_CHECK_TIME_SIZE(sfkssqz, "2025-12-31") && isHyDm("Y")
      && jmxzOption[i].dm == "6199010517" && ((nsqxDm == "06" && ysx > 100000) || (nsqxDm == "08" && ysx > 300000))) {
      return "6199010517";
    }
    if (jmxzOption[i].dm == "6199011601" && isHyDm() && bqynsfe > 0
      && ((nsqxDm == "06" && ysx > 100000) || (nsqxDm == "08" && ysx > 300000))) {
      return "6199011601";
    }
  }
  return "";
}


/**
 * 减免性质锁定格子等性化需求,在此判断
 */
function controllerJmxz(ssjmxzDm, param, zsxmDm, zspmDm, bqynsfe, sfkssqq, sfkssqz, ysx, jcx, nsqxDm) {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  // 小于10万/30万（全国版，除陕西以外,禁用）
  if (DATE_CHECK_TIME_SIZE("2016-02-01", sfkssqq) && zgswjDm.substring(0, 3) != '161' && zsxmDm == "30221" && ssjmxzDm == "0099129901" && ((nsqxDm == "06" && ysx <= 100000) || (nsqxDm == "08" && ysx <= 300000))) {
      return true
  }
  return false;//其余放开
}

/**
 * 本期减免税（费）额锁定格子等性化需求,在此判断
 */
function controllerJmse(ssjmxzDm, param, zsxmDm, zspmDm, bqynsfe, sfkssqq, sfkssqz, ysx, jcx, nsqxDm) {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  // 小于10万/30万（全国版，除陕西以外,禁用）
  if (DATE_CHECK_TIME_SIZE("2016-02-01", sfkssqq) && zgswjDm.substring(0, 3) != '161' && zsxmDm == "30221" && ssjmxzDm == "0099129901" && ((nsqxDm == "06" && ysx <= 100000) || (nsqxDm == "08" && ysx <= 300000))) {
      return true
  }
  // 禅道：50706 安徽个性化
  if(zgswjDm.substring(0, 3) == '134' && ssjmxzDm == "3499032401" && ((nsqxDm == "06" && ysx > 100000) || (nsqxDm == "08" && ysx > 300000))){
    return true
  }
  return false;//其余放开
}

//获取起征点标志（达到起征为true,否则为false）
function getQzdBz(ysx, nsqxDm) {
  if ((nsqxDm == '06' && ysx > 100000) || (nsqxDm == '08' && ysx > 300000)) {
    return true;
  }
  return false;
}


/**
 * 登记注册类型代码校验,是否符合特定条件
 * @returns {Boolean}
 */
function djzclxCheck() {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
  var djzclxDmStr = "";
  //陕西需求:hx_dj.dj_nsrxx中登记注册类型非4%开头，非510、521、522、523、530、540、550、560、570、571，非空；
  if (zgswjDm.substring(0, 3) == '161') {
    djzclxDmStr = "510,521,522,523,530,540,550,560,570,571";
  }

  if (zgswjDm.substring(0, 3) == '161' && djzclxDm.startsWith(4) == false && !isNull(djzclxDm)
    && !isNull(djzclxDmStr) && djzclxDmStr.indexOf(djzclxDm) == -1) {
    return true;
  }
  return false;
}

/**
 * 校验行业代码是否符合特定条件
 * @returns {Boolean}
 */
function isHyDm(falgBz) {
  var hyDm = formData.fq_.nsrjbxx.hyDm;
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var sfkssqq = formData.ht_.fxmtysbbdVO.fxmtySbb.sbbhead.skssqq;
  var sfkssqz = formData.ht_.fxmtysbbdVO.fxmtySbb.sbbhead.skssqz;

  var hyDmAyy = [];
  //福建个性化, 检查行业代码是否属于给定条件
  if (zgswjDm.substring(0, 3) == '135') {
    //福建行业代码属于532%/543%/552%/581%/5612/541%/542%/551%/61%/62%时,
    //*********-3016增加7291,786%,544,553的行业
    hyDmAyy = [532, 543, 552, 581, 5612, 541, 542, 551, 61, 62, 7291, 786, 544, 553];
  } else if (zgswjDm.substring(0, 3) == '161') {
    //陕西行业代码属于7291/786%/61%/62%/728%/51%/52%/80%/53%/54%/55%/56%/57%/58%/83%/881%/882%/9051/9052/8760；
    hyDmAyy = [7291, 786, 61, 62, 728, 51, 52, 80, 53, 54, 55, 56, 57, 58, 83, 881, 882, 9051, 9052, 8760];
    //SNSWJ-1833:水利基金新增需求
    if (DATE_CHECK_TIME_SIZE("2021-01-01", sfkssqq) && DATE_CHECK_TIME_SIZE(sfkssqz, "2025-12-31") && falgBz != undefined && falgBz == 'Y') {
      hyDmAyy = [73, 74, 75];
    }
  }

  for (var i = 0; i < hyDmAyy.length; i++) {
    if (zgswjDm.substring(0, 3) == '135' && hyDm.startsWith(hyDmAyy[i])) {
      return true;
    } else if (zgswjDm.substring(0, 3) == '161' && hyDm.startsWith(hyDmAyy[i])) {
      return true;
    }
  }
  return false;
}

function djzclxAndHydmCheck() {
  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm;
  var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
  var hyDm = formData.fq_.nsrjbxx.hyDm;
  var djzclxDmStr = "";
  var hydmStr = 8310;
  if (zgswjDm.substring(0, 3) == '161') {
    djzclxDmStr = "520,521,522,523";
  }
  if (zgswjDm.substring(0, 3) == '161' && !isNull(djzclxDm) && !isNull(djzclxDmStr) && djzclxDmStr.indexOf(djzclxDm) != -1) {
    if (hyDm == hydmStr) {
      return true;
    }
  }
  return false;
}

/**
 * 扩展startsWith方法
 * 当传入特殊字符(&?等)时不要使用
 */
String.prototype.startsWith = function (str) {
  var reg = new RegExp("^" + str);
  return reg.test(this);
}


function getDays(startDate) {
  var endDate = "2019-01-01";//规定日期
  var startTime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
  var endTime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
  var dates = (startTime - endTime) / (1000 * 60 * 60 * 24);
  return dates;
}

function cshYjse(zspmDm, sfkssqq, sfkssqz) {
  var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  var count = 0;
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (zspmDm == sbxxGridlb[i].zspmDm) {
      count++;
    }
  }
  var bqyjse = 0;
  if (count == 1) {
    var yjxxGridlb = formData.hq_.yjxxGrid.yjxxGridlb;
    if (yjxxGridlb != undefined && yjxxGridlb != "") {
      for (var i = 0; i < yjxxGridlb.length; i++) {
        if (zspmDm == yjxxGridlb[i].zspmDm && bjrq(yjxxGridlb[i].skssqq, sfkssqq) && bjrq(sfkssqz, yjxxGridlb[i].skssqz)) {
          bqyjse += yjxxGridlb[i].yjye1 * 1;
        }
      }
    }
  }
  return bqyjse;
}

/**
 * 比较日期是否rq1>=rq2
 * @param rq1
 * @param rq2
 * @returns {Boolean}
 */
function bjrq(rq1, rq2) {
  var reg = new RegExp("-", "g"); //创建正则RegExp对象
  var numrq1 = rq1.replace(reg, "").substring(0, 8);
  var numrq2 = rq2.replace(reg, "").substring(0, 8);
  if (numrq1 >= numrq2) {
    return true;
  }
  return false;
}

function yjseHjByZspmDm(zsxmDm, zspmDm, sfkssqq, sfkssqz) {
  var yjseHj = 0;
  var sxYjseHj = 0;
  var yjxxGridlb = formData.hq_.yjxxGrid.yjxxGridlb;
  var swjgDm = formData.fq_.nsrjbxx.zgswjDm;
  //陕西水利基金个性化:核心返回合计大于查询结果合计，取核心合计值
  if (swjgDm.substring(0, 3) == '161' && zsxmDm == '30221') {
    var cxYjxxGridlb = null;
    if (!isNull(formData.fq_.tysbbInitData.yjskxxGrid)) {
      cxYjxxGridlb = formData.fq_.tysbbInitData.yjskxxGrid.yjxxGridlb;
    }
    if (!isNull(cxYjxxGridlb)) {
      for (var i = 0; i < cxYjxxGridlb.length; i++) {
        if (zspmDm == cxYjxxGridlb[i].zspmDm) {
          sxYjseHj += cxYjxxGridlb[i].yjye1 * 1;
        }
      }
    }
  }
  if (!isNull(yjxxGridlb)) {
    for (var i = 0; i < yjxxGridlb.length; i++) {
      if (zspmDm == yjxxGridlb[i].zspmDm && bjrq(yjxxGridlb[i].skssqq, sfkssqq) && bjrq(sfkssqz, yjxxGridlb[i].skssqz)) {
        yjseHj += yjxxGridlb[i].yjye1 * 1;
      }
    }
  }
  return yjseHj > sxYjseHj ? yjseHj : sxYjseHj;
}

function bqyjsfeHjByZspmDm(zspmDm, bqyjsfe) {
  var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  var bqyjsfeHj = 0;
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (zspmDm == sbxxGridlb[i].zspmDm) {
      bqyjsfeHj += sbxxGridlb[i].bqyjsfe * 1;
    }
  }
  return bqyjsfeHj;
}

function tysbMsg(flag, tips, tipType) { // 通用申报自定义提示框
  if (flag) {
    layer.open({
      title: '提示',
      content: tips,
      icon: 0
    });
    /*layer.open({
     type: 1,
     title: '提示',
     btn:['确定'],
     icon:0,
     content: tips
     }); */
  }
}

/**
 * 删除未勾选的sbxxGridlb
 */
function delUnSelectedSbxxGridlb() {
  var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  var sbxxGridlb_submit = [];
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (sbxxGridlb[i].t_checkedbz) {
      sbxxGridlb_submit.push(formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[i]);
    }
  }
  //JSONE-10955,如果一条都未勾选,则不用删除;doAfterVerify方法里也会阻断提交;
  if (sbxxGridlb_submit.length != 0) {
    formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb = sbxxGridlb_submit;
  }
}

/**
 * 申报成功的依申请明细信息保存数据,
 * delUnSelectedSbxxGridlb方法已经将未勾选的删掉了,所以剩下的都是已勾选,不用再过滤
 */
function setYsqxxSb() {
  var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  formData.kz_.ysqxx = {};
  var ysqxx = {};
  var sbmxlist = [];
  for (var i = 0; i < sbxxGridlb.length; i++) {
    var item = {};
    item.zsxmDm = sbxxGridlb[i].zsxmDm;
    item.zspmDm = sbxxGridlb[i].zspmDm;
    item.ybtse = sbxxGridlb[i].bqybtsfe;
    sbmxlist[i] = item;
  }
  ysqxx.yzpzzlDm = "BDA0610100";
  ysqxx.yzpzmc = "税（费）通用申报表";
  ysqxx.sbmxlist = sbmxlist;
  formData.kz_.ysqxx = ysqxx;
}

//其他收入（工会经费筹备金）,满足条件的要全额减免（减免税（费）额要=本期应纳税（费）额）
function setBqjmsfe(zspmDm, ysx, nsqxDm, index) {

  var zgswjDm = formData.fq_.nsrjbxx.zgswjDm; //主管税务局代码
  var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;  //除非企业标志(djzclxDm>=500为非企业)
  var qzdBz = getQzdBz(ysx, nsqxDm);  //起征点标志
  var sfkssqq = formData.ht_.fxmtysbbdVO.fxmtySbb.sbbhead.skssqq;
  var sfkssqz = formData.ht_.fxmtysbbdVO.fxmtySbb.sbbhead.skssqz;

  if (zgswjDm.substring(0, 3) == '132' && zspmDm == '399001020' && sfkssqq >= '2019-07-01' && sfkssqz <= '2021-12-31'
    && t_bqsfsyxgmyhzc == 'Y' && qzdBz == false && djzclxDm < 500) { //工会筹备金控制逻辑
    var sbxxGrid = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[index];
    var Prefix = 'ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[' + index + '].';
    sbxxGrid.bqjmsfe = sbxxGrid.bqynsfe;
    formulaEngine.apply(Prefix + 'bqjmsfe', sbxxGrid.bqynsfe);
  }
}

//得到jmxzdm
function getJmxzList() {
  var List = [];
  var tysbjmdmlist = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  var tysblength = tysbjmdmlist.length;
  if (tysblength > 0) {
    for (var i = 0; i < tysblength; i++) {
      var data = {};
      if (tysbjmdmlist[i].ssjmxzDm !== "") {
        data["zsxmDm"] = tysbjmdmlist[i].zsxmDm;
        data["zspmDm"] = tysbjmdmlist[i].zspmDm;
        data["ssjmxzDm"] = tysbjmdmlist[i].ssjmxzDm;
        data["djxh"] = "";
        List.push(data);
      }
    }
  }
  return List;
}

/**
 * 普惠减免性质代码: <phjmxzDm> <phjmswsxDm> <jzzcsyztDm> <phjzbl>
 * 2、<bqsfsyxgmyhzc>本期是否适用小微企业“六税两费”减征政策为“否”N，不传节点，界面显示为空
 * 二、税款属期不在在2019-01-01至2021-12-31内 【小微企业六税两费减征政策执行时间】2022-2024 默认为空，不传节点
 */

/**
 * 六税两费码表过滤
 * @param _json  jzzcztCT
 * @returns {*} 返回修改后的六税两费码表过滤处理后的json，赋值回formCT节点下
 */
function dmbFilter_jzzcztCT(_json) {
  var jzznsrzglx = formData.fq_.jzxx.xgmzg.jzznsrzglx;
  var startTime = formData.fq_.jzxx.xgmzg.startTime;
  var endTime = formData.fq_.jzxx.xgmzg.endTime;
  var skssqq = formData.fq_.tysbbInitData.sssq.rqQ;
  var skssqz = formData.fq_.tysbbInitData.sssq.rqZ;

  var sfgtgsh = 'N';
  var sfxwlqy = 'N';
  var syzt = '';
  var jzxx = formData.fq_.jzxx;
  if (jzxx && jzxx.xgmzg && jzxx.xgmzg.jzxx2022Grid) {
    sfgtgsh = formData.fq_.jzxx.xgmzg.jzxx2022Grid.sfgtgsh;
    if (formData.fq_.jzxx.xgmzg.jzxx2022Grid.syzt) {
      syzt = formData.fq_.jzxx.xgmzg.jzxx2022Grid.syzt;
    }
    if (syzt == '03') {
      sfxwlqy = 'Y';
    }
  }

  var jzzcztCT = _json;

  if (jzzcztCT) {
    jzzcztCT['11'].showBz = "N";
    jzzcztCT['21'].showBz = "N";
    jzzcztCT['22'].showBz = "N";

  }

  //【增值税小规模六税两费减征政策执行时间】2019-2021
  if (skssqq >= '2019-01-01' && skssqz <= '2021-12-31') {
    //小规模时只带出代号11
    if (jzznsrzglx == '01') {
      jzzcztCT['11'].showBz = "Y";
    }
    // ③jzznsrzglx=03、04，表单初始化为“空”（bqsfsyxgmyhzc=N），jzzcsyztDm为空不传节点,可修改为“增值税小规模纳税人”(bqsfsyxgmyhzc=Y)，jzzcsyztDm=11
    else if (jzznsrzglx == '03' || jzznsrzglx == '04') {
      jzzcztCT['11'].showBz = "Y";
    }
  }

  //【小微企业六税两费减征政策执行时间】2022-2024：税款所属期匹配
  if (skssqq >= startTime && skssqz <= endTime) {
    // jzznsrzglx=01、02非季中转（“小型微利企业（增值税一般纳税人）”+空）
    if (jzznsrzglx == '01' || jzznsrzglx == '02') {
      //①小规模纳税人：syzt=01小规模纳税人，表单为“增值税小规模纳税人”时，传11
      if (syzt == '01') {
        // return '11';
        jzzcztCT['11'].showBz = "Y";
      }
      //②个体工商户：syzt=02个体户，表单为“个体工商户（增值税一般纳税人）”时，传22，不可修改
      if (syzt == '02') {
        // return '22';
        jzzcztCT['22'].showBz = "Y";
      }
      // ③不为个体工商户且为小微企业：syzt=03小型微利企业，默认为“小型微利企业（增值税一般纳税人）”，传21，可修改为“空”，
      if (syzt == '03') {
        // return '21';
        jzzcztCT['21'].showBz = "Y";
      }
      // ④不为个体工商户且为不为小微企业：syzt=空，默认带出空选项，可以选择“小型微利企业（增值税一般纳税人）”，传21，；
      if (syzt == '') {
        // return '21';
        jzzcztCT['21'].showBz = "Y";
      }

    }

    //jzznsrzglx=03、04季中转：（[增值税小规模纳税人]+“小型微利企业（增值税一般纳税人）”）
    if (jzznsrzglx == '03' || jzznsrzglx == '04') {
      //⑤按照以下顺序判断：syzt=02个体户[“个体工商户（增值税一般纳税人）]，不可改
      // ——syzt=03小型微利企业[小型微利企业（增值税一般纳税人）]，不可改
      // ——syzt=01小规模纳税人[增值税小规模纳税人]，不可改
      // ——syzt=空，默认“增值税小规模纳税人”，可修改为“小型微利企业（增值税一般纳税人）”
      if (syzt == '02') {
        // return '22';
        jzzcztCT['22'].showBz = "Y";
      }
      if (syzt == '03') {
        // return '21';
        jzzcztCT['21'].showBz = "Y";
      }
      if (syzt == '01') {
        // return '11';
        jzzcztCT['11'].showBz = "Y";
      }
      if (syzt == '') {
        // return '11';
        jzzcztCT['11'].showBz = "Y";
        jzzcztCT['21'].showBz = "Y";
      }
    }
  }

  return jzzcztCT;
}


function controllerJzzcsyztDm(bqsfsyxgmyhzc) {
  var jzznsrzglx = formData.fq_.jzxx.xgmzg.jzznsrzglx;
  var startTime = formData.fq_.jzxx.xgmzg.startTime;
  var endTime = formData.fq_.jzxx.xgmzg.endTime;
  var skssqq = formData.fq_.tysbbInitData.sssq.rqQ;
  var skssqz = formData.fq_.tysbbInitData.sssq.rqZ;

  var syzt = '';
  var jzxx = formData.fq_.jzxx;
  if (jzxx && jzxx.xgmzg && jzxx.xgmzg.jzxx2022Grid) {
    if (formData.fq_.jzxx.xgmzg.jzxx2022Grid.syzt) {
      syzt = formData.fq_.jzxx.xgmzg.jzxx2022Grid.syzt;
    }
  }
  //1,【增值税小规模六税两费减征政策执行时间】2019-2021
  if (skssqq >= '2019-01-01' && skssqz <= '2021-12-31') {
    //①jzznsrzglx=01小规模纳税人，表单为“增值税小规模纳税人”，jzzcsyztDm=11；不可修改
    //②jzznsrzglx=02，表单为“空”jzzcsyztDm为空不传节点，bqsfsyxgmyhzc=N,不可修改
    // ③jzznsrzglx=03、04，表单初始化为“空”（bqsfsyxgmyhzc=N），jzzcsyztDm为空不传节点,可修改为“增值税小规模纳税人”(bqsfsyxgmyhzc=Y)，jzzcsyztDm=11
    if (jzznsrzglx == '03' || jzznsrzglx == '04') {
      return false;
    } else {//01  02  不可修改
      return true;
    }
  }
  // 2、【小微企业六税两费减征政策执行时间】2022-2024：税款所属期匹配
  else if (skssqq >= startTime && skssqz <= endTime) {
    // jzznsrzglx=01、02非季中转（“小型微利企业（增值税一般纳税人）”+空）
    if (jzznsrzglx == '01' || jzznsrzglx == '02') {
      //①小规模纳税人：syzt=01小规模纳税人，表单为“增值税小规模纳税人”时，传11 不可修改
      if (syzt == '01') {
        return true;
      }
      //②个体工商户：syzt=02个体户，表单为“个体工商户（增值税一般纳税人）”时，传22，不可修改
      if (syzt == '02') {
        return true;
      }
      if (syzt == '04') {
        return true;
      }
      // ③不为个体工商户且为小微企业：syzt=03小型微利企业，默认为“小型微利企业（增值税一般纳税人）”，传21，可修改为“空”，
      if (syzt == '03') {
        return false;
      }
      // ④不为个体工商户且为不为小微企业：syzt=空，默认带出空选项，可以选择“小型微利企业（增值税一般纳税人）”，传21，；
      if (syzt == '') {
        return false;
      }
    }
    //jzznsrzglx=03、04季中转：（[增值税小规模纳税人]+“小型微利企业（增值税一般纳税人）”）
    if (jzznsrzglx == '03' || jzznsrzglx == '04') {
      //⑤按照以下顺序判断：syzt=02个体户[“个体工商户（增值税一般纳税人）]，不可改
      // ——syzt=03小型微利企业[小型微利企业（增值税一般纳税人）]，不可改
      // ——syzt=01小规模纳税人[增值税小规模纳税人]，不可改
      // ——syzt=空，默认“增值税小规模纳税人”，可修改为“小型微利企业（增值税一般纳税人）”
      if (syzt == '02') {
        return true;
      }
      if (syzt == '03') {
        return true;
      }
      if (syzt == '04') {
        return true;
      }
      if (syzt == '01') {
        return true;
      }
      if (syzt == '') {
        return false;
      }
    }
  }

  //3、税款所属期起止不在【增值税小规模六税两费减征政策执行时间】、【小微企业六税两费减征政策执行时间】，则默认为空
  else if (!(skssqq >= '2019-01-01' && skssqz <= '2021-12-31') && !(skssqq >= startTime && skssqz <= endTime)) {
    return true;
  }
}

function filterWbcsh(wbcsh) {
  var gzsbBz = formData.fq_.tysbbInitData.gzsbBz;
  if (gzsbBz == 1) {
    for (var key in wbcsh) {
      if (key.indexOf(".phjmse") > -1 || key.indexOf(".phjmxzDm") > -1 || key.indexOf(".phjzbl") > -1) {
        delete wbcsh[key];
      }
      if (key == 'ht_.fxmtysbbdVO.fxmtySbb.slrxxForm.blrysfzjhm') {
        delete wbcsh[key];
      }
    }
  }

  if (typeof filterWbcshDeliver == 'function') {
    wbcsh = filterWbcshDeliver(wbcsh);
  }
  if (typeof filterWbcshCDeliver == 'function') {
    wbcsh = filterWbcshCDeliver(wbcsh);
  }
  return wbcsh;
}

//******************** 以下内容从ysq-cjpt-web\src\main\webapp\resources\js\nssb\tysb\tysb.js迁移过来  ******************//

var ZSXM_DM_YHS = '10111';// 印花税
var ZSPM_DM_YHS_CCZLHT = '101110105';// 财产租赁合同
// 个税征收品目代码array
var gs_zsxmDm = '10106';
var fjs_zspmDm_speacl = '30203,30216,30446,10109,30221'; // 附加税进行计算带出的品目数组配置。
var gs_zszmDm_speacl = '1010601002440031,'; // 个税子目中税率来源于税费种认定，不进行累计税率计算。
var srze_noDecimal_zspmDm = '101110400,101110599'; // 收入总额需要四舍五入到整数位的；如按件数。
var unableSbZspm = '101060200,101060300,101040001';	//配置通用申报不能申报的征收品目。
var ableSbZszm = '1010400012440200';		//配置通用申报里面可以申报的征收子目信息；
var DJZCLX_HHQY = '420,172,421,422,423,175,522';	//合伙企业登记注册类型
var ywztBz = true;

var validateTysb = {
  calculate_tysb_row: function (nodeLeftPart) {

    var zsfsDm = "5";// 测试使用，暂时没来源
    var zsxmDm = nodeLeftPart.zsxmDm;
    if (zsxmDm) {
      zsxmDm += "";
    }
    var zspmDm = nodeLeftPart.zspmDm;
    if (zspmDm) {
      zspmDm += "";
    }
    var zszmDm = nodeLeftPart.zszmDm;
    if (zszmDm) {
      zszmDm += "";
    }
    var srze = parseFloat(nodeLeftPart.ysx);
    var kcs = parseFloat(nodeLeftPart.jcx);
    var yssdl = parseFloat(nodeLeftPart.yssdl);
    var sl = parseFloat(nodeLeftPart.sflhdwse);
    var sskcs = parseFloat(nodeLeftPart.sskcs);
    var ynse = parseFloat(nodeLeftPart.bqynsfe);
    var jmse = parseFloat(nodeLeftPart.bqjmsfe);
    var yjse = parseFloat(nodeLeftPart.bqyjsfe);
    var hdynsjye = nodeLeftPart.hdynsjye ? nodeLeftPart.hdynsjye : nodeLeftPart.t_hdynsjye;
    var dqdeHdxxArray = validateTysb.getDqdeHdxxByZspm(zspmDm);
    if (dqdeHdxxArray.length > 0) {
      if (hdynsjye < dqdeHdxxArray[0].ynsjye) {
        hdynsjye = dqdeHdxxArray[0].ynsjye;
        nodeLeftPart.hdynsjye = dqdeHdxxArray[0].ynsjye;
        nodeLeftPart.t_hdynsjye = dqdeHdxxArray[0].ynsjye;
      }
    }
    var zfsbz = nodeLeftPart.zfsbz ? nodeLeftPart.zfsbz : nodeLeftPart.t_zfsbz;
    var hyDm = nodeLeftPart.hyDm ? nodeLeftPart.hyDm : nodeLeftPart.t_hyDm;
    var phjzbl = nodeLeftPart.phjzbl;
    var phjmse = nodeLeftPart.phjmse;

    // 收入总额需要四舍五入取整数。
    if (zspmDm != '' && inArray(zspmDm, srze_noDecimal_zspmDm) >= 0) {
      srze = Math.round(srze);
      nodeLeftPart.ysx = srze;
    }

    //当应税所得率为0或为空时默认为1
    if (yssdl == 0 || yssdl == "" || yssdl == "undefined" || yssdl == undefined) {
      nodeLeftPart.yssdl = yssdl;
      yssdl = 1;
    }
    // 计税（费）依据=(应税项-减除项)*yssdl
    var jsyj = Math.round(100 * (srze - kcs) * yssdl) / 100;
    if (jsyj < 0) {
      jsyj = 0;
    }

    // 调用个税计算税率和速算扣除数公式
    var json_sl_sskcs = new Array();
    if (zsfsDm.substr(0, 1) !== '4' && zsxmDm == gs_zsxmDm) {

      // 个税1010601002440031这个征收子目不需要根据累计税率进行计算，根据税费种认定带出。
      if (zszmDm == "" || inArray(zszmDm, gs_zszmDm_speacl) == -1) {

        //对于个税生产经营、承包承租经营所得，根据计税标志去进行计算。
        if (zspmDm == '101060200' || zspmDm == '101060300') {

          // 转调common.js里面定义的tysbCalc2018Gs
          var idx = getGsBodyRowIndex(zspmDm, zszmDm);
          var bodyRow = validateTysb.getQcsRow(idx);
          json_sl_sskcs = tysbCalc2018Gs(
            '',
            srze,
            kcs,
            yssdl,
            zspmDm,
            zszmDm,
            0,
            (bodyRow.nsqxDm ? bodyRow.nsqxDm : bodyRow.t_nsqxDm),
            bodyRow.sl,
            bodyRow.skssqq,
            bodyRow.skssqz,
            bodyRow.jsqq,
            bodyRow.jsbz,
            bodyRow.fdsl,
            bodyRow.dqdehdFlag,
            bodyRow.sfzrdxxbFlag
          );
        } else {
          json_sl_sskcs = calculate_gs_jsme_sl_sskcs(nodeLeftPart, zsxmDm, zspmDm, yssdl);
        }

      } else if ('1010601002440031' == zszmDm) {
        if ((sl * 1) == 0) {
          sl = 0.004;
          nodeLeftPart.sflhdwse = sl;
        }
        ynse = ROUND(jsyj * sl - sskcs, 2);
      }
    }
    if (json_sl_sskcs.length > 0) {
      jsyj = json_sl_sskcs[0].ynssde;
      jsyj = (jsyj < 0) ? 0 : jsyj;
      sl = json_sl_sskcs[0].sl;
      sskcs = json_sl_sskcs[0].sskcs;
      ynse = json_sl_sskcs[0].ynse;
      ynse = (ynse < 0) ? 0 : ynse;
      nodeLeftPart.sflhdwse = sl;
      nodeLeftPart.sskcs = sskcs;
      if (typeof json_sl_sskcs[0].kcs != 'undefined') {
        kcs = json_sl_sskcs[0].kcs;
        nodeLeftPart.jcx = kcs;
      }
    }
    //计算非个税的应纳税额
    if (zsxmDm != gs_zsxmDm || zspmDm == '101060100') {
      ynse = Math.round((jsyj * 100) * (sl * 1000000)) / 100000000;//计算精度问题
    }

    // 附加税特殊处理
    // 只要收入总额不变（保持核心带出），则附加税也不变，同样保持核心带出。本期应纳税额自动计算=计税依据*税率-速算扣除数；
    // 如果收入总额变更，则计税依据=【收入总额（应税项）-减除项】* 应税所得率；本期应纳税额=计税依据*税率-速算扣除数；
    if (['10109', '30203', '30216'].indexOf(zsxmDm) != -1) {
      var bodyIndex = getGsBodyRowIndex(zspmDm, zszmDm);
      if (bodyIndex > -1) {
        var tempBody = validateTysb.getQcsRow(bodyIndex);
        var srcSrze = tempBody.xssr;
        var szcJsyj = tempBody.jsyj;
        if (srze === srcSrze) {
          jsyj = szcJsyj;
          ynse = jsyj * sl - sskcs;
        } else {
          jsyj = (srze - kcs) * yssdl;
          ynse = jsyj * sl - sskcs;
        }
      }
    }

    jsyj = ROUND(jsyj, 2);
    ynse = ROUND(ynse, 2);
    jmse = ROUND(jmse, 2);
    yjse = ROUND(yjse, 2);

    //季中转规则
    var allowJzz = formData.fq_.jzxx.xgmzg.allowJzz;
    var jzznsrzglx = formData.fq_.jzxx.xgmzg.jzznsrzglx;
    var bqsfsyxgmyhzc = nodeLeftPart.bqsfsyxgmyhzc ? nodeLeftPart.bqsfsyxgmyhzc : formData.ht_.fxmtysbbdVO.fxmtySbb.t_bqsfsyxgmyhzc;
    var afterCalPhjmse = ROUND((ynse - jmse) * phjzbl, 2);
    if (allowJzz == 'Y' && (jzznsrzglx == '03' || jzznsrzglx == '04')) {
      var changed = nodeLeftPart.t_changed;
      if (bqsfsyxgmyhzc == 'Y') {
        if (changed != 'phjmse') {
          phjmse = afterCalPhjmse;
        }
      } else {
        phjmse = 0;
      }
    } else {
      //不清楚原来是否判断了bqsfsyxgmyhzc为Y
      if (bqsfsyxgmyhzc == 'Y') {
        phjmse = afterCalPhjmse;
      } else {
        phjmse = 0;
      }
    }

    //phjmse = ROUND((ynse-jmse)*phjzbl,2);
    var ybtse = (ynse - jmse - yjse - phjmse) < 0 ? 0.00 : (ynse - jmse - yjse - phjmse);
    /*水利基金应补退税额处理JSONE-9385*/
    if (zsxmDm == "30221") {
      ybtse = ynse - jmse - yjse - phjmse;
    }

    /* 印花税应补退税额处理 JSONE-10719 新增计算需求*/
    var ybtse_yhs = (ynse - jmse - yjse - phjmse).toFixed(2);
    if (zsxmDm == ZSXM_DM_YHS) {
      if (ybtse_yhs < 0.1)
        ybtse = 0;
      if (ybtse_yhs >= 0.1) {
        ybtse = (ynse - jmse - yjse - phjmse).toFixed(1);
      }
      if (zspmDm == ZSPM_DM_YHS_CCZLHT) {
        if (ybtse_yhs >= 0.1 && ybtse_yhs <= 1) {
          ybtse = 1;
        } else if (ybtse_yhs < 0.1) {
          ybtse = 0;
        }
        if (ybtse_yhs >= 1) {
          ybtse = (ynse - jmse - yjse - phjmse).toFixed(1);
        }
      }
    }

    nodeLeftPart.jsfyj = jsyj;
    nodeLeftPart.bqynsfe = ynse;
    nodeLeftPart.bqjmsfe = jmse;
    nodeLeftPart.phjmse = phjmse;
    nodeLeftPart.bqybtsfe = ROUND(ybtse, 2);
  },
  /**
   * 根据征收品目代码获取个税所得率核定信息。
   * @param zspmDm
   */
  getGrsdldlHdxx: function (zspmDm) {
    var hdxxArr = new Array();

    // 个人所得税核定信息节点路径
    var xmlDoc = $($.json2xml(formData));
    var path = "qcs > initData > tysbbInitData > grsdsdlHdxxGrid > grsdsdlHdxxGridlb";
    if (ywztBz) {
      path = "fq_ > grsdsdlHdxxGrid > grsdsdlHdxxGridlb";
    }
    xmlDoc.find(path).each(function () {
      var zspmDm_temp = $(this).children("zspmDm").text();
      var jsbz = ($(this).children("jsbz").text()) * 1;
      var ynssdl = $(this).children("ynssdl").text();
      if (zspmDm == zspmDm_temp && !jsbz && jsbz != 0
        && !ynssdl && ynssdl != '0') {
        var hdxxObj = {};
        hdxxObj['ynssdl'] = $(this).children("ynssdl").text();
        hdxxObj['jsbz'] = jsbz;
        hdxxArr.push(hdxxObj);
      }
    });

    return hdxxArr;
  },

  /**
   * 根据征收品目代码获取定期定额核定信息。
   * @param zspmDm
   * @param hyDm
   */
  getDqdeHdxxByZspm: function (zspmDm, hyDm) {
    var hdxxArr = new Array();

    var dqdeHdxxGridlb;
    if (ywztBz) {
      dqdeHdxxGridlb = formData["fq_"]["dqdeHdxxGrid"]["dqdeHdxxGridlb"];
    } else {
      dqdeHdxxGridlb = formData["qcs"]["initData"]["tysbbInitData"]["dqdeHdxxGrid"]["dqdeHdxxGridlb"];
    }
    for (var i in dqdeHdxxGridlb) {
      var zspmDm_temp = dqdeHdxxGridlb[i].zspmDm;
      var jsbz = (dqdeHdxxGridlb[i].jsbz) * 1;
      var yssdl = dqdeHdxxGridlb[i].yssdl;
      var sl = dqdeHdxxGridlb[i].sl;
      if (zspmDm == zspmDm_temp && !jsbz && jsbz != 0
        && (((jsbz == '2' || jsbz == '3') && !yssdl && yssdl != '0') ||
          (jsbz == '1' && sl != '' && sl != '0') || (jsbz == '5' && sl != '' && sl != '0'))) {
        var hdxxObj = {};
        hdxxObj['zspmDm'] = zspmDm_temp;
        hdxxObj['sl'] = sl;
        hdxxObj['ynse'] = dqdeHdxxGridlb[i].ynse;
        hdxxObj['hdse'] = dqdeHdxxGridlb[i].hdse;
        hdxxObj['jsyj'] = dqdeHdxxGridlb[i].jsyj;
        hdxxObj['ynsjye'] = dqdeHdxxGridlb[i].ynsjye;
        hdxxObj['yssdl'] = yssdl;
        hdxxObj['jsbz'] = jsbz;
        hdxxObj['hyDm'] = dqdeHdxxGridlb[i].hyDm;
        hdxxArr.push(hdxxObj);
      }
    }

    //如果定期定额核定信息里面存在多条同一品目的核定，则匹配到行业。
    if (hdxxArr.length > 1 && !hyDm && hyDm != 0) {
      for (var _i = 0; _i < hdxxArr.length; _i++) {
        var obj = hdxxArr[_i];
        if (obj.hyDm == hyDm) {
          hdxxArr = [obj];
          break;
        }
      }
    }
    return hdxxArr;
  },

  /**
   * 获取通用申报减免税额
   * 公式sbTysb.body.mxxxs.mxxx[].ynse-(sbTysb.body.mxxxs.mxxx[].jsyj*jzsl*(1-jzfd)-jzed)
   *
   * @param docElem
   * @returns
   */
  get_tysb_jmse: function (nodeLeftPart) {
    var jsyj = nodeLeftPart.jsfyj * 1;
    var sl = nodeLeftPart.sflhdwse * 1;
    var ynse = nodeLeftPart.bqynsfe * 1;
    var jmzlxDm = nodeLeftPart.jmzlxDm ? nodeLeftPart.jmzlxDm : nodeLeftPart.t_jmzlxDm; //未知
    var jzfd = nodeLeftPart.jzfd ? nodeLeftPart.jzfd : nodeLeftPart.t_jzfd;	//未知
    jzfd = jzfd == '' ? 0.00 : jzfd * 1;
    var jzsl = nodeLeftPart.jzsl ? nodeLeftPart.jzsl : nodeLeftPart.t_jzsl;	//未知
    jzsl = jzsl == '' ? 0.00 : jzsl * 1;
    var jzed = nodeLeftPart.jzed ? nodeLeftPart.jzed : nodeLeftPart.t_jzed;	//未知
    jzed = jzed == '' ? 0.00 : jzed * 1;

    var jmse = 0.00;
    var flag = false;//是否非按照税率(超额累进税率的情况)计税标志
    if (ynse != Math.round(jsyj * sl * 100) / 100) {//非按照税率计税的情况,此时对于税率减免和幅度减免不做减免税额计算，需要前台手工录入减免税额
      flag = true;
    }

    if (jmzlxDm == '02') {
      jmse = ynse;
    } else if (jmzlxDm == '01') {
      if (jzed > 0) {
        if (jzed > ynse) {
          jmse = ynse;
        } else {
          jmse = jzed;
        }
      } else if (jzsl > 0 && sl > jzsl) {
        var sljmje = Math.round(jsyj * (sl - jzsl) * 100) / 100;
        if (sljmje > ynse) {
          jmse = ynse;
        } else {
          jmse = sljmje;
        }
        if (flag) {
          jmse = 0;
        }
      } else if (jzfd > 0 && jzfd < 1) {
        jmse = Math.round(jsyj * (1 - jzfd) * sl * 100) / 100;
        if (flag) {
          jmse = 0;
        }
      }
    }

    return jmse;
  },

  /**
   * 取当前重复行下标
   * @param zspm
   * @param zszm
   * @returns
   */
  getCurrentRownum: function (nodeLeftPart) {
    var arrayNum = 0; //重复行下标
    var sbxxGridlb = validateTysb.getAllRow();
    for (var i = 0; i < sbxxGridlb.length; i++) {
      if (sbxxGridlb[i].zspmDm == nodeLeftPart.zspmDm) {
        if (sbxxGridlb[i].zszmDm != '' && sbxxGridlb[i].zszmDm == nodeLeftPart.zszmDm) {
          arrayNum = i;
          break;
        } else {
          arrayNum = i; //确定是重复行下标
        }
      }
    }
    return arrayNum;
  },

  /**
   * 取当前重复行
   * @param zspm
   * @param zszm
   * @returns
   */
  getCurrentRow: function (zspm, zszm, index) {
    var sbxxGridlb = validateTysb.getAllRow();
    return sbxxGridlb[index];
  },

  /**
   * 取指定重复行
   * @param zspm
   * @param zszm
   * @returns
   */
  getThisRow: function (i, bz) {
    if (ywztBz || bz) {
      return formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[i];
    } else {
      return formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[i];
    }
  },

  /**
   * 取所有重复行
   * @param zspm
   * @param zszm
   * @returns
   */
  getAllRow: function (bz) {
    if (ywztBz || bz) {
      return formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
    } else {
      return formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
    }

  },
  /**
   * 取所有期初数重复行
   * @param zspm
   * @param zszm
   * @returns
   */
  getAllQcsRow: function (bz) {
    if (ywztBz || bz) {
      return formData.fq_.sbxxGrid.sbxxGridlbVO;
    } else {
      return formData.qcs.formContent.sbTysb.body.sbxxGrid.sbxxGridlb;
    }
  },
  /**
   * 取所有期初数重复行
   * @param zspm
   * @param zszm
   * @returns
   */
  getQcsRow: function (idx, bz) {
    if (ywztBz || bz) {
      return formData.fq_.sbxxGrid.sbxxGridlbVO[idx];
    } else {
      return formData.qcs.formContent.sbTysb.body.sbxxGrid.sbxxGridlb[idx];
    }
  },
  /**
   * 获取主税计税依据
   */
  getZsJsyj: function (bz) {
    if (ywztBz || bz) {
      return formData.fq_.tysbbInitData.zsJsyj;
    } else {
      return formData.qcs.initData.tysbbInitData.zsJsyj;
    }
  },
  /**
   * 获取起征点信息
   */
  getQzdMxxx: function (bz) {
    if (ywztBz || bz) {
      return formData.fq_.qzdMxxxs.qzdMxxx;
    } else {
      return formData.qcs.initData.tysbbInitData.fjsQzdMxxxs.fjsQzdMxxx;
    }
  },
  /***************************************************************************
   * 获取一个属期征收项目相同的计税依据总额合计
   *
   * @param {}
   *            nodeLeftPart 节点路径
   * @return {} 收入总额合计金额
   */
  getYysJsyjHj: function (nodeLeftPart) {
    var ssqq = nodeLeftPart.sfkssqq;
    var ssqz = nodeLeftPart.sfkssqz;
    var zsxmDm = nodeLeftPart.zsxmDm;

    var jsyjHj = 0;
    var nodeLeftPartPrefix = validateTysb.getAllRow();
    // 起征点信息节点路径
    var rowLength = nodeLeftPartPrefix.length;
    for (var i = 0; i < rowLength; i++) {
      var zsxmDm_temp = nodeLeftPartPrefix[i].zsxmDm;
      var skssqq = nodeLeftPartPrefix[i].sfkssqq;
      var skssqz = nodeLeftPartPrefix[i].sfkssqz;
      if (zsxmDm == zsxmDm_temp && ssqq == skssqq && ssqz == skssqz) {
        //2015-02-11 wangfm 不能直接取nodeLeftPartPrefix[i].jsyj")。因为在填写的时候，jsyj还没有重新计算
        var srze = nodeLeftPartPrefix[i].ysx;
        var kcs = nodeLeftPartPrefix[i].jcx;
        var yssdl = nodeLeftPartPrefix[i].yssdl;
        //当应税所得率为0或为空时默认为1
        if (yssdl == 0 || yssdl == "" || yssdl == "undefined" || yssdl == undefined) {
          yssdl = 1;
        }
        // 计税（费）依据=(应税项-减除项)*yssdl
        var jsyj = Math.round(100 * (srze - kcs) * yssdl) / 100;
        if (jsyj < 0) jsyj = 0;
        jsyjHj += (jsyj / 1);
      }
    }
    return jsyjHj;
  },
  /**
   * @param nodeLeftPart
   *            节点路径
   * @returns 免征额度
   */
  getMzed: function (nodeLeftPart) {
    var mzed = 0;
    var ssqq = nodeLeftPart.sfkssqq;
    var ssqz = nodeLeftPart.sfkssqz;
    var zsxmDm = nodeLeftPart.zsxmDm;

    var _relydatanum;
    // 起征点信息节点路径
    var nodeLeftPartPrefix = validateTysb.getQzdMxxx();
    var rootLeft = nodeLeftPartPrefix;
    var _qzdSsqq;
    var qzdSsqz;
    var qzdZsxmDm;
    var qzdje;
    var ysbje;
    if (typeof rootLeft !== 'undefined') {
      // 是否存在多行数据。
      if (typeof rootLeft.length !== 'undefined') {
        _relydatanum = nodeLeftPartPrefix.length;
        for (var i = 0; i < _relydatanum; i++) {
          _qzdSsqq = nodeLeftPartPrefix[i].skssqq;
          _qzdSsqz = nodeLeftPartPrefix[i].skssqz;
          _qzdZsxmDm = nodeLeftPartPrefix[i].zsxmDm;
          if (ssqq == _qzdSsqq && ssqz == _qzdSsqz && zsxmDm == _qzdZsxmDm) {
            _qzdje = nodeLeftPartPrefix[i].qzdje;
            _ysbje = nodeLeftPartPrefix[i].ysbje;
            mzed = _qzdje - _ysbje;
            return mzed;
          }
        }
      } else {
        // 1行的时候需要特殊处理
        _qzdSsqq = nodeLeftPartPrefix[0].skssqq;
        _qzdSsqz = nodeLeftPartPrefix[0].skssqz;
        _qzdZsxmDm = nodeLeftPartPrefix[0].zsxmDm;
        if (ssqq == _qzdSsqq && ssqz == _qzdSsqz && zsxmDm == _qzdZsxmDm) {
          _qzdje = nodeLeftPartPrefix[0].qzdje;
          _ysbje = nodeLeftPartPrefix[0].ysbje;
          mzed = _qzdje - _ysbje;
        }
      }
    }

    return mzed;
  },

  /**
   * @param nodeLeftPart
   *            节点路径
   * @returns 免征额度
   */
  getYsbje: function (nodeLeftPart) {
    var ssqq = nodeLeftPart.sfkssqq;
    var ssqz = nodeLeftPart.sfkssqz;
    var zsxmDm = nodeLeftPart.zsxmDm;

    var _relydatanum;
    // 起征点信息节点路径
    var nodeLeftPartPrefix = null;
    if (ywztBz) {
      nodeLeftPartPrefix = formData.fq_.qzdMxxxs.qzdMxxx;
    } else {
      nodeLeftPartPrefix = formData.qcs.initData.tysbbInitData.qzdMxxxs.qzdMxxx;
    }
    var ysbje = 0;
    var qzdSsqq;
    var qzdSsqz;
    var qzdZsxmDm;
    if (typeof nodeLeftPartPrefix !== 'undefined') {
      // 是否存在多行数据。
      if (typeof nodeLeftPartPrefix.length !== 'undefined') {
        _relydatanum = nodeLeftPartPrefix.length;
        for (var i = 0; i < _relydatanum; i++) {
          qzdSsqq = nodeLeftPartPrefix[i].skssqq;
          qzdSsqz = nodeLeftPartPrefix[i].skssqz;
          qzdZsxmDm = nodeLeftPartPrefix[i].zsxmDm;
          if (ssqq == qzdSsqq && ssqz == qzdSsqz && zsxmDm == qzdZsxmDm) {
            ysbje = nodeLeftPartPrefix[i].ysbje;
            ysbje = ysbje == undefined ? 0.00 : ysbje;
            return ysbje * 1;
          }
        }
      } else {
        // 1行的时候需要特殊处理
        qzdSsqq = nodeLeftPartPrefix[0].skssqq;
        qzdSsqz = nodeLeftPartPrefix[0].skssqz;
        qzdZsxmDm = nodeLeftPartPrefix[0].zsxmDm;
        if (ssqq == qzdSsqq && ssqz == qzdSsqz && zsxmDm == qzdZsxmDm) {
          ysbje = nodeLeftPartPrefix[0].ysbje;
          ysbje = ysbje == undefined ? 0.00 : ysbje;
          return ysbje * 1;
        }
      }
    }

    return ysbje * 1;
  },
  /*
   * 计算通用申报合计数
   */
  calculate_tysb_hjxx: function () {
    var nodeLeftPartPrefix = validateTysb.getAllRow();
    var rowLength = nodeLeftPartPrefix.length;
    var ynseHj = 0;
    var jmseHj = 0;
    var yjseHj = 0;
    var ybtseHj = 0;
    var ysxHj = 0;
    var phjmseHj = 0;

    var ynseHjYgx = 0;
    var jmseHjYgx = 0;
    var yjseHjYgx = 0;
    var ybtseHjYgx = 0;
    var jsfyjHjYgx = 0;
    var phjmseHjYgx = 0;

    var jsfyjHj = 0;
    var bqjmsfeHj = 0;
    for (var i = 0; i < rowLength; i++) {
      if(nodeLeftPartPrefix[i].t_checkedbz){
        ynseHj += parseFloat(nodeLeftPartPrefix[i].bqynsfe);
        jmseHj += parseFloat(nodeLeftPartPrefix[i].bqjmsfe);
        yjseHj += parseFloat(nodeLeftPartPrefix[i].bqyjsfe);
        ybtseHj += parseFloat(nodeLeftPartPrefix[i].bqybtsfe);
  
        jsfyjHj += parseFloat(nodeLeftPartPrefix[i].jsfyj);
        bqjmsfeHj += parseFloat(nodeLeftPartPrefix[i].bqjmsfe);
        ysxHj += parseFloat(nodeLeftPartPrefix[i].ysx);
        phjmseHj += parseFloat(nodeLeftPartPrefix[i].phjmse);
      }

      if ((ywztBz && nodeLeftPartPrefix[i].t_checkedbz)
        || (!ywztBz && nodeLeftPartPrefix[i].checkedbz)) {
        ynseHjYgx += parseFloat(nodeLeftPartPrefix[i].bqynsfe);
        jmseHjYgx += parseFloat(nodeLeftPartPrefix[i].bqjmsfe);
        yjseHjYgx += parseFloat(nodeLeftPartPrefix[i].bqyjsfe);
        ybtseHjYgx += parseFloat(nodeLeftPartPrefix[i].bqybtsfe);
        jsfyjHjYgx += parseFloat(nodeLeftPartPrefix[i].jsfyj);
        phjmseHjYgx += parseFloat(nodeLeftPartPrefix[i].phjmse);
      }

    }
    if (ywztBz) {
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqynsfehj = ynseHj;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqjmsfehj = jmseHj;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqyjsfehj = yjseHj;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqybtsfehj = ybtseHj;

      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_jsfyjhj = jsfyjHj;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_ysxhj = ysxHj;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_phjmsehj = phjmseHj;

      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqynsfehjYgx = ynseHjYgx;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqjmsfehjYgx = jmseHjYgx;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqyjsfehjYgx = yjseHjYgx;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqybtsfehjYgx = ybtseHjYgx;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_jsfyjHjYgx = jsfyjHjYgx;
      formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_phjmseHjYgx = phjmseHjYgx;
    } else {
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqynsfehj = ynseHj;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqjmsfehj = jmseHj;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqyjsfehj = yjseHj;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqybtsfehj = ybtseHj;

      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqynsfehjYgx = ynseHjYgx;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqjmsfehjYgx = jmseHjYgx;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqyjsfehjYgx = yjseHjYgx;
      formData.fxmtysbbdVO.fxmtySbb.sbxxGrid.bqybtsfehjYgx = ybtseHjYgx;
    }

  },

  /**
   * 对于个税生产经营、企业承包承租，根据征收品目代码、税款所属期获取已申报金额信息。
   * @param zspmDm
   */
  getGsYsbjeByZspm: function (zspmDm, skssqq, skssqz) {
    var xmlDoc = $($.json2xml(formData));
    var ysbje = 0.00;

    xmlDoc.find("fq_ > sbxxGrid > sbxxGridlbVO").each(function () {
      var zspmDm_temp = $(this).children("zspmDm").text();
      var skssqq_temp = $(this).children("skssqq").text().substr(0, 10);
      var skssqz_temp = $(this).children("skssqz").text().substr(0, 10);
      if (zspmDm == zspmDm_temp && skssqq == skssqq_temp && skssqz == skssqz_temp) {
        ysbje += $(this).children("yjse").text() * 1 + $(this).children("ybtse").text() * 1;
      }
    });

    return Math.round(100 * ysbje) / 100;
  },


  /**
   * 改变计税依据、减免性质时重新计算减免税额，重复行所有行都重新计算
   */
  tysb_jmse: function (zsxmDm, zspmDm, ssjmxzDm, rdpzuuid) {
    var bqjmsfe;
    var qzdMxxxArr = [];
    var zsJsyj = validateTysb.getZsJsyj();
    var jsfyj = 0;
    var sflhdwse = 0;
    var bqynsfe = 0;
    var jmzlxDm = "";
    var jmed = 0;
    var jmsl = 0;
    var jmfd = 0;
    var sbxxGridlb = validateTysb.getAllRow();
    var fjsQzdxx = validateTysb.getQzdMxxx();
    for (var i = 0; i < sbxxGridlb.length; i++) {
      if (sbxxGridlb[i].rdpzuuid == rdpzuuid && sbxxGridlb[i].zspmDm == zspmDm) {
        jsfyj = sbxxGridlb[i].jsfyj;
        sflhdwse = sbxxGridlb[i].sflhdwse;
        bqynsfe = sbxxGridlb[i].bqynsfe;
        if (ssjmxzDm == null) {
          sbxxGridlb[i].ssjmxzDm = "";
          return 0;
        }
        if (ssjmxzDm == "0010101401") {
          jmzlxDm = "01";
          jmfd = 0.000000;
          jmed = 500.000000;
          jmsl = 0.000000;
        } else if (ssjmxzDm == "0010012702") {
          jmzlxDm = "02";
          jmfd = 0.900000;
          jmed = 0.000000;
          jmsl = 0.000000;
        } else if (ssjmxzDm == "0061042802" || ssjmxzDm == "0099042802") {
          jmzlxDm = "02";
          jmfd = 0.000000;
          jmed = 0.000000;
          jmsl = 0.000000;
        } else {
          if (formCT.jmxzDmCT && ssjmxzDm != "") {
            var code = formCT.jmxzDmCT[zspmDm].option;
            for (var j = 0; j < code.length; j++) {
              if (code[j].dm == ssjmxzDm) {
                jmzlxDm = code[j].jmzlxDm ? code[j].jmzlxDm : code[j].t_jmzlxDm;
                jmfd = code[j].jmfd;
                jmed = code[j].jmed;
                jmsl = code[j].jmsl;
                break;
              }
            }
          }
        }
        if (ywztBz) {
          if (formData.fq_.nsrjbxx.zgswjDm.indexOf('135') == 0 && isHyDm() && ssjmxzDm == "0099129999" && sbxxGridlb[i].zsxmDm == "30221"
            && bjrq(sbxxGridlb[i].sfkssqq, "2020-01-01") && bjrq("2020-12-31", sbxxGridlb[i].sfkssqz)) {
            jmzlxDm = "02";
          } else if (formData.fq_.nsrjbxx.zgswjDm.indexOf('135') == 0 && (ssjmxzDm == "3599042801" || ssjmxzDm == "0099129901")) {
            jmzlxDm = "02";
          } else if (formData.fq_.nsrjbxx.zgswjDm.indexOf('161') == 0 && (ssjmxzDm == "6199011601" || ssjmxzDm == "6199010517")) {
            jmzlxDm = "02";
          }
          sbxxGridlb[i].t_jmzlxDm = jmzlxDm;
          sbxxGridlb[i].t_jzfd = jmfd;
          sbxxGridlb[i].t_jzed = jmed;
          sbxxGridlb[i].t_jzsl = jmsl;
        } else {
          sbxxGridlb[i].jmzlxDm = jmzlxDm;
          sbxxGridlb[i].jzfd = jmfd;
          sbxxGridlb[i].jzed = jmed;
          sbxxGridlb[i].jzsl = jmsl;
        }
      }
    }
    //获取纳服配置中起征点明细信息中的起征点信息
    if (fjsQzdxx != undefined && isArray(fjsQzdxx)) {
      for (var indexc = 0; indexc < fjsQzdxx.length; indexc++) {
        qzdMxxxArr[fjsQzdxx[indexc].zsxmDm] = (parseFloat(fjsQzdxx[indexc].qzdje) - parseFloat(fjsQzdxx[indexc].ysbje)).toFixed(2);
      }
    }

    if (ssjmxzDm == "") {
      return 0;
    }
    if (qzdMxxxArr[zsxmDm + '_06'] != undefined && zsJsyj > 0) {
      if (zsJsyj < qzdMxxxArr[zsxmDm + '_06']) {
        return jsfyj * sflhdwse;
      } else {
        if (ssjmxzDm == "0099129999") {
          //  sbxxGridlb[arrayNum].ssjmxzDm="";
          return 0;
        }
      }
    }
    if (jmzlxDm == "") {
      bqjmsfe = 0;
    } else if (jmzlxDm == "02") {
      bqjmsfe = bqynsfe;
    } else {
      if (jmed > 0) {
        bqjmsfe = jmed > bqynsfe ? bqynsfe : jmed;
      } else if (jmsl > 0 && sflhdwse > jmsl) {
        bqjmsfe = jsfyj * (sflhdwse - jmsl)
      } else if (jmfd > 0 && jmfd < 1) {
        if (ssjmxzDm == "3599030301") {
          bqjmsfe = bqynsfe * (1 - jmfd);
        } else {
          bqjmsfe = jsfyj * (1 - jmfd);
        }
      } else {
        bqjmsfe = 0;
      }
    }
    return ROUND(bqjmsfe, 2);
  },

  /**
   * 改变计税依据，根据起征点信息改变减免性质
   * @param zsJsyj
   * @param jsfyj
   * @param fjsQzdxx
   * @returns
   */
  tysb_ssjmxzDm: function (zsxmDm, zspmDm, ysx, rdpzuuid, _ssjmxzDm) {
    _ssjmxzDm = (_ssjmxzDm == undefined || _ssjmxzDm == 'undefined') ? "" : _ssjmxzDm;
    var gzsbBz = formData.fq_.tysbbInitData.gzsbBz;
    var ssjmxzDm = "";
    var qzdMxxxArr = [];
    var zsJsyj = validateTysb.getZsJsyj();
    var sbxxGridlb = validateTysb.getAllQcsRow();
    var fjsQzdxx = validateTysb.getQzdMxxx();
    var nsqxDm = "";

    for (var i = 0; i < sbxxGridlb.length; i++) {
      if (sbxxGridlb[i].rdpzuuid == rdpzuuid) {
        ssjmxzDm = sbxxGridlb[i].ssjmxzDm;
        nsqxDm = sbxxGridlb[i].nsqxDm ? sbxxGridlb[i].nsqxDm : sbxxGridlb[i].t_nsqxDm;
      }
    }

    //获取纳服配置中起征点明细信息中的起征点信息
    if (fjsQzdxx != undefined && isArray(fjsQzdxx)) {
      for (var ii = 0; ii < fjsQzdxx.length; ii++) {
        qzdMxxxArr[fjsQzdxx[ii].zsxmDm] = (parseFloat(fjsQzdxx[ii].qzdje) - parseFloat(fjsQzdxx[ii].ysbje)).toFixed(2);
      }
    }
    if ((nsqxDm == '06' && ysx > 0 && ysx <= 100000 && ['30216', '30221', '30203'].indexOf(zsxmDm) > -1) || (nsqxDm == '08' && ysx > 0 && ysx <= 300000 && ['30216', '30221', '30203'].indexOf(zsxmDm) > -1)) {
      //*********-2315-福建个性化需求、广东及更正符合减免时可（自由选择与删除）
      if (formData.fq_.nsrjbxx.zgswjDm.indexOf('135') == 0 || formData.fq_.nsrjbxx.zgswjDm.indexOf('144') == 0 || gzsbBz == '1') {
        return _ssjmxzDm;
      }
      return '0061042802';
    } else if ((nsqxDm == '06' && ysx > 100000 && ['30216', '30221', '30203'].indexOf(zsxmDm) > -1) || (nsqxDm == '08' && ysx > 300000 && ['30216', '30221', '30203'].indexOf(zsxmDm) > -1)) {
      if (_ssjmxzDm == '0061042802' || ssjmxzDm == '0061042802' || _ssjmxzDm == '0099042802' || ssjmxzDm == '0099042802') {
        return "";
      } else {
        return ssjmxzDm;
      }
    } else if ((nsqxDm == '10' || nsqxDm == '11') && (_ssjmxzDm == '0061042802' || _ssjmxzDm == '0099042802')) {
      return _ssjmxzDm;
    }
    //根据主税的计税依据判断起征点，未达起征点返回默认值
    if (qzdMxxxArr[zsxmDm + '_06'] != undefined && zsJsyj > 0 && zsJsyj < qzdMxxxArr[zsxmDm + '_06']) {
      //*********-2315-福建个性化需求、广东及更正符合减免时可（自由选择与删除）
      if (formData.fq_.nsrjbxx.zgswjDm.indexOf('135') == 0 || formData.fq_.nsrjbxx.zgswjDm.indexOf('144') == 0 || gzsbBz == '1') {
        return _ssjmxzDm;
      }
      return '0061042802';
    } else {
      return ssjmxzDm;
    }
  }
};


/**
 * 计算实际经营的月份
 * @param kyrq    开业日期
 * @param tbrq    填报日期
 * @returns
 */
function calSjjyMonth(kyrq, tbrq) {
  if (kyrq == 'undefined' || kyrq == '' || kyrq == undefined) {
    return 12;
  }
  var kyrqYear = kyrq.substring(0, 4);
  var tbrqYear = tbrq.substring(0, 4);
  if (tbrqYear > kyrqYear) {
    return 12;
  }
  return parseFloat(12 - kyrq.substring(5, 7)) + 1;
}

/**
 * 计算已申报月份
 * @param kyrq    开业日期
 * @param tbrq    填报日期
 * @returns
 */
function calYsbMonth(kyrq, tbrq) {
  if (kyrq == 'undefined' || kyrq == '' || kyrq == undefined) {
    return tbrq.substring(5, 7);
  }
  var kyrqYear = kyrq.substring(0, 4);
  var tbrqYear = tbrq.substring(0, 4);
  if (tbrqYear > kyrqYear) {
    return parseFloat(tbrq.substring(5, 7));
  }
  return parseFloat(tbrq.substring(5, 7) - kyrq.substring(5, 7) + 1);
}

/**
 * 根据dm_gs_zspm表，对
 * 1.工资、薪金所得，2.生产经营所得和企事业单位承包经营所得，3.稿酬所得，4.劳动报酬所得，
 * 5.特许权使用费所得，利息股息红利所得，财产租赁所得，财产转让所得，偶然所得和其他所得
 * 进行减免税额，税率和速算扣除数的计算
 * @param nodeLeftPart 申报信息节点路径
 * @param zsxmDm 征收项目代码
 * @param zspmDm 征收品目代码
 * @param yssdl 应税所得率
 * @param kzxxObj 扩展信息对象。
 */
function calculate_gs_jsme_sl_sskcs(nodeLeftPart, zsxmDm, zspmDm, yssdl, kzxxObj) {
  var json = new Array();
  var srze;
  var kcs;
  if (nodeLeftPart == "") {
    srze = kzxxObj.srze;
    kcs = kzxxObj.kcs;
  } else {
    srze = parseFloat(nodeLeftPart.ysx);
    kcs = parseFloat(nodeLeftPart.jcx);
  }

  //省级环境特殊需求,增加应税所得率字段,modified by 2014-12-23 lijunfeng
  if (yssdl == 'undefined' || yssdl == undefined) {
    yssdl = 1;
  }

  var ynssde = parseFloat((srze - kcs) * yssdl);
  ynssde = Math.round(100 * ynssde) / 100;
  if (gs_zsxmDm == zsxmDm) {//个人所得税
    var jsonElement = {};
    switch (zspmDm) {
      case '101060200' :
        break;//个体户生产经营所得
      case '101060300' :
        break;//企事业承包承租经营所得

      case '101060400' :
        //劳务报酬所得税，先判断收入，不超过4000的，减800，超过4000的减20%后为应纳税所得额
        if (ynssde <= 4000) {
          ynssde = ynssde - 800;
        } else if (ynssde > 4000) {
          ynssde = ynssde * 0.8;
        }
        if (ynssde <= 20000) {
          jsonElement['sl'] = 0.2;
          jsonElement['sskcs'] = 0;
        } else if (ynssde > 20000 && ynssde <= 50000) {
          jsonElement['sl'] = 0.3;
          jsonElement['sskcs'] = 2000;
        } else if (ynssde > 50000) {
          jsonElement['sl'] = 0.4;
          jsonElement['sskcs'] = 7000;
        }
        break;//劳务报酬所得税
      case '101060500' :
        if (ynssde >= 800 && ynssde <= 4000) {
          ynssde = ynssde - 800;
        } else if (ynssde > 4000) {
          ynssde = ynssde * 0.8;
        }
        jsonElement['sl'] = 0.2;
        jsonElement['sskcs'] = ynssde * 0.2 * 0.3;
        //ynssde = ynssde*0.7;//再按应纳税额减征30%
        break;//稿酬所得
      case '101060600' : //特许权使用所得

      case '101060800' :
        if (ynssde >= 800 && ynssde <= 4000) {
          ynssde = ynssde - 800;
        } else if (ynssde > 4000) {
          ynssde = ynssde * 0.8;
        }
        jsonElement['sl'] = 0.2;
        jsonElement['sskcs'] = 0;
        break;//财产租赁所得
      case '101060100' :

        break;//工资薪金所得
      default :
        //取核心税率。
        break;
    }
    //计算应纳税所得额，如果特殊的应纳税所得额处理方式，在前面进行处理。
    if (jsonElement.ynssde == 'undefined' || jsonElement.ynssde == undefined) {
      jsonElement['ynssde'] = ynssde;
    }

    //计算应纳税额，如果特殊的应纳税额处理方式，在前面进行处理。
    if ((jsonElement.ynse == 'undefined' || jsonElement.ynse == undefined)
      && jsonElement.sl != 'undefined' && jsonElement.sl != undefined) {
      jsonElement['ynse'] = ynssde * jsonElement.sl - jsonElement.sskcs;
    }
    if (jsonElement.ynse != 'undefined' && jsonElement.ynse != undefined) {
      json.push(jsonElement);
    }
  }
  return json;
}

/**
 * 个税计算时，根据品目子目计算下标
 * @param zspmDm
 * @param zszmDm
 * @returns
 */
function getGsBodyRowIndex(zspmDm, zszmDm) {
  var sbxxGridlb = validateTysb.getAllQcsRow();
  var arrayNum = -1;
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (sbxxGridlb[i].zspmDm == zspmDm && sbxxGridlb[i].zszmDm == zszmDm) {
      arrayNum = i; //确定是重复行下标
    }
  }

  return arrayNum;
}

/**
 * 减免性质改变后，赋值减免税额。
 * @param docElem
 */
function changeJmxz(nodeLeftPart) {
  var ssjmxzDm = nodeLeftPart.ssjmxzDm;
  var zspmDm = nodeLeftPart.zspmDm;
  var zszmDm = nodeLeftPart.zszmDm;
  var bqjmsfe = nodeLeftPart.bqjmsfe;
  //税收减免性质代码不为空时触发。
  if (ssjmxzDm != '' && ssjmxzDm != undefined && ssjmxzDm != 'undefined') {
    //根据ssjmxzDm改变对应的jmzlxDm,jzed,jzfd,jzsl
    var sbxxGridlb = validateTysb.getAllQcsRow();
    var arrayNum = 0;
    for (var i = 0; i < sbxxGridlb.length; i++) {
      if (sbxxGridlb[i].zspmDm == zspmDm) {
        if (sbxxGridlb[i].zszmDm != '' && sbxxGridlb[i].zszmDm == zszmDm) {
          arrayNum = i;
          break;
        } else {
          arrayNum = i; //确定是重复行下标
        }
      }
    }
    var option = sbxxGridlb[arrayNum].option;
    for (var iii = 0; iii < option.length; iii++) {
      if (option[iii].dm == ssjmxzDm) {
        if (ywztBz) {
          nodeLeftPart.t_jmzlxDm = option[iii].jmzlxDm;
          nodeLeftPart.t_jzed = option[iii].jmed;
          nodeLeftPart.t_jzfd = option[iii].jmfd;
          nodeLeftPart.t_jzsl = option[iii].jmsl;
        } else {
          nodeLeftPart.jmzlxDm = option[iii].jmzlxDm;
          nodeLeftPart.jzed = option[iii].jmed;
          nodeLeftPart.jzfd = option[iii].jmfd;
          nodeLeftPart.jzsl = option[iii].jmsl;
        }
        break;
      }
    }
    validateTysb.calculate_tysb_row(nodeLeftPart);	// 先计算当前行的计算关系
    var jmse = validateTysb.get_tysb_jmse(nodeLeftPart);	//根据减免性质，确定可以减免的税额。
    var bqynsfe = nodeLeftPart.bqynsfe;
    var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
    var swjgDm = "";
    if (ywztBz) {
      swjgDm = formData.fq_.nsrjbxx.swjgDm;
    } else {
      swjgDm = formData.qcs.initData.nsrjbxx.swjgDm;
    }
    if (nodeLeftPart.ssjmxzDm != null && nodeLeftPart.ssjmxzDm != '') {
      if (zspmDm == "101110501" && (nodeLeftPart.ssjmxzDm == "0009129906" || nodeLeftPart.ssjmxzDm == "0009129907")) {
        nodeLeftPart.bqjmsfe = ROUND(bqynsfe * 0.5, 2);
      } else if (zspmDm == "101110599" && (nodeLeftPart.ssjmxzDm == "0009129906" || nodeLeftPart.ssjmxzDm == "0009129907")) {
        nodeLeftPart.bqjmsfe = bqynsfe;
      } else if (zspmDm == "101060200" && (nodeLeftPart.ssjmxzDm == "0005049901") && isStartWith(djzclxDm, "41")) {
        nodeLeftPart.bqjmsfe = ROUND(bqynsfe * 0.5, 2);
      } else if (jmse == 0) {
        nodeLeftPart.bqjmsfe = bqjmsfe < bqynsfe ? bqjmsfe : bqynsfe;
      } else {
        if (bqjmsfe == 0) {
          nodeLeftPart.bqjmsfe = jmse;
          //逻辑问题,只要不是改的减免税额,就返回计算结果(优化减免税额只能调小不能调大的逻辑)
        } else if (nodeLeftPart.t_changed != "bqjmsfe") {
          nodeLeftPart.bqjmsfe = jmse;
        } else {
          nodeLeftPart.bqjmsfe = bqjmsfe < jmse ? bqjmsfe : jmse;
        }
      }
    } else {
      //nodeLeftPart.bqjmsfe=jmse;
    }
  }
}

/**
 * 页面加载完毕后，进行调用初始化
 */
function afterLoadDataAction(bz) {
  var nodeLeftPart = "";
  var formsDataXml = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  if (formsDataXml && formsDataXml.length > 0) {
    for (var i in formsDataXml) {
      nodeLeftPart = validateTysb.getThisRow(parseInt(i));
      validateTysb.calculate_tysb_row(nodeLeftPart);
      nodeLeftPart.ysbse = validateTysb.getYsbje(nodeLeftPart);
    }
  }
  validateTysb.calculate_tysb_hjxx();
}

/*
 * 公共公式执行后、填充数据前执行业务逻辑 @return boolean
 */
function callbackBeforeSetData(nodeLeftPart) {
  var flag = validateGsscjyxx(nodeLeftPart);	//校验个税生产经营信息。如果是为空，则返回false，不能进行计算。
  if (!flag) {
    return false;
  }

  //调用减免性质逻辑。
  changeJmxz(nodeLeftPart);

  validateTysb.control_tysb_jmse(nodeLeftPart);
  validateTysb.calculate_tysb_row(nodeLeftPart);// 计算当前行的计算关系
  validateTysb.calculate_tysb_hjxx(nodeLeftPart);// 计算合计信息
}

/**
 * 数据改变时执行业务逻辑
 * @param zspm
 * @param zszm
 * @param ssjmxzdm
 * @param ysx
 * @param jcx
 * @param bqjmsfe
 * @param bqyjsfe
 * @param phjmse 普惠减免税额
 */
function changeData(zspm, zszm, ssjmxzDm, ysx, jcx, bqynsfe, bqjmsfe, bqyjsfe, phjmse, phjzbl, t_checkedbz, index) {
  var nodeLeftPart = validateTysb.getCurrentRow(zspm, zszm, index);
  //调用减免性质逻辑。
  if (inArray(nodeLeftPart.zsxmDm, fjs_zspmDm_speacl) != -1) {
    validateTysb.calculate_tysb_row(nodeLeftPart);// 计算当前行的计算关系
    var jmse = validateTysb.tysb_jmse(nodeLeftPart.zsxmDm, nodeLeftPart.zspmDm, nodeLeftPart.ssjmxzDm, nodeLeftPart.rdpzuuid);
    if (!isNullForStr(nodeLeftPart.ssjmxzDm)) {
      if (jmse == 0) {
        nodeLeftPart.bqjmsfe = bqjmsfe < bqynsfe ? bqjmsfe : bqynsfe;
      } else {
        if (ssjmxzDm == '0061042802' || ssjmxzDm == "0099042802") {
          nodeLeftPart.bqjmsfe = jmse;
        } else if (bqjmsfe == 0) {
          nodeLeftPart.bqjmsfe = jmse;
          //逻辑问题,只要不是改的减免税额,就返回计算结果(优化减免税额只能调小不能调大的逻辑)
        } else if (nodeLeftPart.t_changed != "bqjmsfe") {
          nodeLeftPart.bqjmsfe = jmse;
        } else {
          nodeLeftPart.bqjmsfe = bqjmsfe < jmse ? bqjmsfe : jmse;
        }
      }
    } else {
      if (ssjmxzDm == '0061042802' || ssjmxzDm == "0099042802") {
        nodeLeftPart.bqjmsfe = jmse;
      } else if (isNullForStr(ssjmxzDm)) {
        nodeLeftPart.bqjmsfe = 0;
      }
      //nodeLeftPart.bqjmsfe=jmse;
    }
  } else {
    changeJmxz(nodeLeftPart);
  }
  if (nodeLeftPart.bqynsfe == 0) {
    nodeLeftPart.bqjmsfe = 0;
    nodeLeftPart.ssjmxzDm = "";
  }
  if (nodeLeftPart.zsxmDm == '30221' && nodeLeftPart.ssjmxzDm == '0099129901') {
    nodeLeftPart.bqjmsfe = nodeLeftPart.bqynsfe;
  }
  // 禅道：50706 安徽个性化处理
  if (nodeLeftPart.zsxmDm == '30221' && nodeLeftPart.ssjmxzDm == '3499032401') {
    nodeLeftPart.bqjmsfe = ROUND(nodeLeftPart.bqynsfe * 0.1, 2);
  }
  /*
   * 增加一个标志(t_changedJmxzBz):手动选择减免性质代码的标志,t_changed会根据操作的格子而修改,
   * 但只要手动选择了减免性质(t_changed=="ssjmxzDm"),就添加个固定的手动选择标志
   */
  if (nodeLeftPart.t_changed == "ssjmxzDm") {
    nodeLeftPart.t_changedJmxzBz = "Y";
  }
  /*
   * 增加一个标志(t_ysJmxzBz):要素导入减免性质代码的标志
   * formData.wbcsh为要素报文
   */
  if (!isNull(formData.wbcsh) && !isNull(formData.wbcsh["ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[" + index + "].ssjmxzDm"])) {
    nodeLeftPart.t_ysJmxzBz = "Y";
  }
  validateTysb.calculate_tysb_row(nodeLeftPart);// 计算当前行的计算关系

  validateTysb.calculate_tysb_hjxx(nodeLeftPart);// 计算合计信息

}

/**
 * 个税生产经营所得信息判断。获取实际经营月份、已申报月份、已申报金额等字段。
 * @param docElem
 *
 * @returns true 已获取到相关信息；false:没有获取到相关信息
 */
function validateGsscjyxx(nodeLeftPart) {
  var zspmDm = nodeLeftPart.zspmDm;
  //只针对生产经营所得，进行特殊处理。
  if (zspmDm != '101060200' && zspmDm != '101060300') {
    return true;
  }
  //如果是合伙企业，则不进行特殊计算。
  var djzclxDm = null;
  if (ywztBz) {
    djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
  } else {
    djzclxDm = formData.qcs.initData.nsrjbxx.djzclxDm;
  }

  if (undefined != djzclxDm && djzclxDm != '' && inArray(djzclxDm, DJZCLX_HHQY) > -1) {
    return true;
  }

  var jsbz = '';
  var dlhdfsbz = '';	//定率核定方式标志；1: 定期定额表核定的。 2：在个税核定表中核定的。
  var gsdlHdxxArray = validateTysb.getGrsdldlHdxx(zspmDm);
  var dqdeHdxxArray = validateTysb.getDqdeHdxxByZspm(zspmDm);

  //判断是在定期定额表核定的还是在个税定率表核定的。如果在两个表中都不存在，则不需要获取生产经营计算公式相关的信息。
  if (dqdeHdxxArray.length > 0) {
    jsbz = dqdeHdxxArray[0].jsbz;
    dlhdfsbz = 1;
  } else if (gsdlHdxxArray.length > 0) {
    jsbz = gsdlHdxxArray[0].jsbz;
    dlhdfsbz = 2;
  } else {
    return true;
  }

  //如果计税标志不是为3的，不需要重新获取与计算公式相关的信息。
  if (jsbz != '3') {
    return true;
  }
  var sjjyyf = nodeLeftPart.sjjyyf ? nodeLeftPart.sjjyyf : nodeLeftPart.t_sjjyyf;
  var ysbyf = nodeLeftPart.ysbyf;

  //实际经营月份已经获取到，则不需要重新去获取。
  if (!sjjyyf && sjjyyf > 0) {
    return true;
  }

  //对于是在个税核定表中，如果已申报月份已获取到，则不需要重新去获取。
  if (dlhdfsbz == '2' && !ysbyf && ysbyf > 0) {
    return true;
  }

  //如果实际经营月份或者已申报月份没值，则需要纳税人自行录入。
  Message.win({
    message: buildGsscjyxxHtml(nodeLeftPart, dlhdfsbz),
    width: 600,
    height: 300,
    title: '生产经营信息录入',
    btn: ['OK'],
    closeBtn: false,
    autoClose: false,
    handler: function () {
      setGsscjyxx(nodeLeftPart, dlhdfsbz);
    },
    maxBtn: false,
    minBtn: false
  });
  return false;
}

/**
 * 初始化生产经营录入信息的html
 * @param nodeLeftPart 通用申报 dom 对象节点路径左边部分
 * @param dlhdfsbz 定率核定方式标志； 1 ：定期定额表核定的；2：个税核定表核定的。
 */
function buildGsscjyxxHtml(nodeLeftPart, dlhdfsbz) {
  var multipos = validateTysb.getCurrentRownum(nodeLeftPart);
  if (typeof multipos === 'undefined') {
    multipos = 0;
  }
  var skssqz = nodeLeftPart.sfkssqz;
  var zspmDm = nodeLeftPart.zspmDm;
  var kyrq = eval("formData.fq_.tysbbInitData.kyrq");
  var sjjyyf = calSjjyMonth(kyrq, skssqz);

  var content = '<link href="/skin/www/style/css-swzj-01/style.css"  rel="stylesheet" type="text/css">'
    + '<div></div>'
    + '<table border="0" cellpadding="0" cellspacing="0"  align="center" width="99%">'
    + '<tr align="center">'
    + '<td width="100px" class="title-td01">实际经营月份</td>'
    + '<td width="200px" class="title-td01f"><input type="text" id="' + 'sjjyyf' + multipos + '"  value ="' + sjjyyf + '" /></td>'
    + '<td width="200px" class="title-td01f" id="sjjyyfErrorMsg" style="color:red;font-size:11px;"></td>'
    + "</tr>";

  //如果核定方式标志为个税核定的，则需要纳税人自行填写已申报月份和已申报金额。
  if (dlhdfsbz == 2) {
    var ysbyfVal = calYsbMonth(kyrq, skssqz);
    //未知 	var array = parent.parent.parent.initBusinessFormFrame.getPreviousSkssq(skssqq, skssqz).split(":");
    var ysbjeVal = validateTysb.getGsYsbjeByZspm(zspmDm, array[0], array[1]);
    var ysbyfId = "ysbyf" + multipos;
    var ysbjeId = "ysbje" + multipos;

    content += '<tr align="center">'
      + '<td width="100px" class="title-td01">已申报月份</td>'
      + '<td width="200px" class="title-td01f"><input type="text" id="' + ysbyfId + '"  value="' + ysbyfVal + '" /></td>'
      + '<td width="200px" class="title-td01f" id="ysbyfErrorMsg" style="color:red;"></td>'
      + "</tr>"
      + '<tr align="center">'
      + '<td width="100px" class="title-td01">已申报金额</td>'
      + '<td width="200px" class="title-td01f"><input type="text" id="' + ysbjeId + '"  value="' + ysbjeVal + '"  disabled/></td>'
      + '<td width="200px" class="title-td01f" id="ysbjeErrorMsg" style="color:red;font-size:11px;"></td>'
      + "</tr>";
  }
  content += '</table>'
    + '<div style="height:50px"></div>'
    + '<table border="0" cellpadding="0" cellspacing="0"  align="center" width="99%">'
    + '<div style="color:#FF0000" id="tsxx">提示信息：<br/>1. 实际经营月份：默认为12，当以下两种情形时例外：（1）年中开业时，当年实际经营月份为开业当月至年底（年中注销除外）；（2）年中注销时，当年实际经营月份为年初（或开业当月）至注销月份.';

  if (dlhdfsbz == 2) {
    content += ' <br/>2. 已申报月数：税款所属月份-当年税费种认定月起+1';
  }
  content += '</div></table>';

  return content;
}

/**
 * 输入完生产经营信息后回调
 * @param docElem
 *  @param dlhdfsbz 定率核定方式标志； 1 ：定期定额表核定的；2：个税核定表核定的。
 */
function setGsscjyxx(nodeLeftPart, dlhdfsbz) {
  var multipos = validateTysb.getCurrentRownum(nodeLeftPart);
  if (typeof multipos === 'undefined') {
    multipos = 0;
  }
  var sjjyyf = document.getElementById('sjjyyf' + multipos).value;
  //校验值。
  if (sjjyyf == "") {
    document.getElementById('sjjyyfErrorMsg').innerHTML = "实际经营月份不能为空！<br/>";
    return false;
  } else if (sjjyyf.match(/^[-+]?\d*$/) == null) {
    document.getElementById('sjjyyfErrorMsg').innerHTML = '实际经营月份必须是整形数字！<br/>';
    return false;
  } else if (sjjyyf <= 0) {
    document.getElementById('sjjyyfErrorMsg').innerHTML = "实际经营月份不能小于等于0！<br/>";
    return false;
  } else if (sjjyyf > 12) {
    document.getElementById('sjjyyfErrorMsg').innerHTML = "实际经营月份不能大于12！<br/>";
    return false;
  } else {
    document.getElementById('sjjyyfErrorMsg').innerHTML = "";
  }


  if (dlhdfsbz == '2') {
    var ysbyf = document.getElementById('ysbyf' + multipos).value;
    var ysbje = document.getElementById('ysbje' + multipos).value;

    if (ysbyf == "") {
      document.getElementById('ysbyfErrorMsg').innerHTML = "已申报月份不能为空！";
      return false;
    } else if (ysbyf.match(/^[-+]?\d*$/) == null) {
      document.getElementById('ysbyfErrorMsg').innerHTML = '已申报月份必须是整形数字！<br>';
      return false;
    } else if (ysbyf <= 0) {
      document.getElementById('ysbyfErrorMsg').innerHTML = "已申报月份不能小于等于0！<br/>";
      return false;
    } else if (ysbyf > 12) {
      document.getElementById('ysbyfErrorMsg').innerHTML = "已申报月份不能大于12！<br/>";
      return false;
    } else {
      document.getElementById('ysbyfErrorMsg').innerHTML = "";
    }

    if (ysbje == "") {
      document.getElementById('ysbjeErrorMsg').innerHTML = "已申报金额不能为空！<br/>";
      return false;
    } else if (ysbje.match(/^\d*(\.\d{0,2})?$/) == null) {
      document.getElementById('ysbjeErrorMsg').innerHTML = '已申报金额必须是数字，且小数点位数不能大于2位！<br/>';
      return false;
    } else if (ysbje < 0) {
      document.getElementById('ysbjeErrorMsg').innerHTML = "已申报金额不能小于0！<br/>";
      return false;
    } else {
      document.getElementById('ysbjeErrorMsg').innerHTML = "";
    }

    nodeLeftPart.ysbyf = ysbyf;
    nodeLeftPart.ysbse = ysbje;
    nodeLeftPart.bqyjsfe = ysbje;
  }

  nodeLeftPart.sjjyyf = sjjyyf;
  nodeLeftPart.t_sjjyyf = sjjyyf;
  Message.close();
  callbackBeforeSetData(nodeLeftPart);
}

/**
 * 过滤不可以申报的征收品目。
 * @param nodeLeftPart
 */
function fiterZspm(nodeLeftPart) {
  var zspmDm = nodeLeftPart.zspmDm;
  var zspmCsz = unableSbZspm;
  if (typeof (zspmCsz) == "undefined" || zspmCsz == 'undefined' || zspmCsz == undefined || zspmCsz == '') {
    return true;
  }

  if (inArray(zspmDm, zspmCsz) > -1) {
    var zszmDm = nodeLeftPart.zszmDm;
    //配置可以申报的征收子目代码
    if (zszmDm != '' && zszmDm != undefined && inArray(zszmDm, ableSbZszm) > -1) {
      return true;
    }

    //对于个人所得税生产经营品目、承包承租经营所得，先查询定期定额表
    //如果在定期定额表中查询不到，则查询个税核定表;
    //如果都不存在，则不允许在此申报。
    if (zspmDm == '101060200' || zspmDm == '101060300') {
      var dqdeHdxxArray = validateTysb.getDqdeHdxxByZspm(zspmDm);
      if (dqdeHdxxArray.length > 0) {
        return true;
      } else {
        var gsdlHdxxArray = validateTysb.getGrsdldlHdxx(zspmDm);
        if (gsdlHdxxArray.length > 0) {
          return true;
        }
      }
    }

    nodeLeftPart.sftj = 'N';
    nodeLeftPart.t_sftj = 'N';

    //setRowReadOnly(nodeLeftPart);
    return false;
  } else {
    return true;
  }
}

/**
 * 过滤合伙企业登记注册类型
 * @param nodeLeftPart
 */
function fiterDjzclxByHhqy(nodeLeftPart) {
  var zspmDm = nodeLeftPart.zspmDm;
  var djzclxDm = null;
  if (ywztBz) {
    djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
  } else {
    djzclxDm = formData.qcs.initData.nsrjbxx.djzclxDm;
  }
  if (zspmDm == '101060200' || zspmDm == '101060300') {
    if (undefined != djzclxDm && djzclxDm != '' && inArray(djzclxDm, DJZCLX_HHQY) > -1) {
      nodeLeftPart.sftj = 'N';
      nodeLeftPart.t_sftj = 'N';
      //setRowReadOnly(nodeLeftPart);
      return false;
    }
  }
  return true;
}


//全选操作
function selectedAll(allSelected, col) {
  var checkedBz = 'checkedbz';
  //选择或取消全选同时改变是否提交标志
  var sbxxGridlb = validateTysb.getAllRow();
  if (col) {
    checkedBz = col;
    sbxxGridlb = validateTysb.getAllRow(true);
  }
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (allSelected == true) {
      sbxxGridlb[i][checkedBz] = true;
      //  sbxxGridlb[i].sftj="Y";
    } else {
      sbxxGridlb[i][checkedBz] = false;
      //  sbxxGridlb[i].sftj="N";
    }
  }
  validateTysb.calculate_tysb_hjxx();// 计算合计信息

  return true;
}

function submitValid() {
  var sbxxGridlb = validateTysb.getAllRow();
  for (var i = 0; i < sbxxGridlb.length; i++) {
    if (sbxxGridlb[i].sftj == "Y" || sbxxGridlb[i].t_sftj == "Y") {
      return true;
    }
  }
  return false;
}

function setYsbse() {
  validateTysb.getAllRow();
}

function inArray(elem, arr, i) {
  var len;

  if (arr) {
    len = arr.length;
    i = i ? i < 0 ? Math.max(0, len + i) : i : 0;

    // fix bug when ie8 ;这两行目的就是解决这个bug
    if (typeof arr === "string") {
      return arr.indexOf(elem);
    }

    for (; i < len; i++) {
      // Skip accessing in sparse arrays
      if ((i in arr) && (arr[i] === elem)) {
        return i;
      }
    }
  }

  return -1;
}

function getHdynsjye(ysx, index) {
  var sbxxGridlbVO = formData.fq_.sbxxGrid.sbxxGridlbVO[index];
  if (sbxxGridlbVO) {
    return ysx >= sbxxGridlbVO.hdynsjye;
  }
  return ysx >= 0;
}
function setSfl(zspmDm, zszmDm, index) {
  if ("Y" === formData.fq_.t_whdGhjf) {
    var sflhdwse = 0.02;
    if (!isNull(zszmDm)) {
      var zszmlb = formData.fq_.zszmlb;
      for (var i in zszmlb) {
        if (zszmDm === zszmlb[i].zszmDm) {
          sflhdwse = parseFloat(zszmlb[i].fdsl);
        }
      }
    } else {
      var zspmlb = formData.fq_.zspmlb;
      for (var i in zspmlb) {
        if (zspmDm === zspmlb[i].zspmDm) {
          sflhdwse = parseFloat(zspmlb[i].sl);
        }
      }
    }
    formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb[index].sflhdwse = sflhdwse;
  }
}

// pdf用到的t_中间节点统一赋值
function generatePdfAssignments() {
  // 征收项目、征收品目
  var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
  if (formData.fq_.t_whdGhjf !== 'Y') {
    sbxxGridlb.forEach((item) => {
      item.t_pdf_zsxmMc = item.zsxmMc || '';
      item.t_pdf_zspmMc = item.zspmMc || '';
    })
  } else {
    var zsxmOptionMap = {};
    var zspmOptionMap = {};
    if (Array.isArray(formData.fq_.zspmlb)) {
      formData.fq_.zspmlb.forEach((item) => {
        // 征收项目
        if(!zsxmOptionMap[item.zsxmDm]){
          zsxmOptionMap[item.zsxmDm] = item.zsxmMc;
        }
        // 征收品目
        zspmOptionMap[item.zspmDm] = item.zspmMc;
      })
    }
    sbxxGridlb.forEach((item) => {
      item.t_pdf_zsxmMc = zsxmOptionMap[item.zsxmDm] || '';
      item.t_pdf_zspmMc = zspmOptionMap[item.zspmDm] || '';
    })
  }
  // 减免性质
  var jmxzOptionMap = {};
  if (Array.isArray(formData.fq_.option)) {
    formData.fq_.option.forEach((item) => {
      jmxzOptionMap[item.pc] = item.mc;
    })
  }
  sbxxGridlb.forEach((item) => {
    item.t_pdf_ssjmxzMc = jmxzOptionMap[item.ssjmxzDm] || '';
        // 转换为小数
      for(var key in item){
        if(typeof item[key]==='number'){
          if(Math.floor(item[key]) === item[key]){
            item[key] = item[key].toFixed(2);
          }
        }
      }
  })
    // 合计行转换小数
  var sbxxGrid = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid
      for(var key in sbxxGrid){
      if(typeof sbxxGrid[key]==='number'){
          if(Math.floor(sbxxGrid[key]) === sbxxGrid[key]){
              sbxxGrid[key] = sbxxGrid[key].toFixed(2);
          }
      }
  }
}

export default {
  ysdjGlZspm,
  cshYjse,
  afterLoadDataAction,
  selectedAll,
  setSsjmxzDm,
  phjzZsxmDms,
  delUnSelectedSbxxGridlb,
  getPhjzbl,
  isHyDm,
  changeData,
  controllerJmxz,
  controllerJmse,
  controllerJzzcsyztDm,
  getHdynsjye,
  bqyjsfeHjByZspmDm,
  yjseHjByZspmDm,
  tysbMsg,
  doAfterVerify,
  setSfl,
  generatePdfAssignments,
}