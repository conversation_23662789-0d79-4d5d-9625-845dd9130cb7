/* eslint-disable */
var tempSaveOkTip = "所有填表数据暂存成功！";
var saveOkTip = "所有申报表数据保存成功！";
//ie9兼容，由于ie9的location里没有origin这个属性 GZDSDZSWJ-4331
var origin = !location.origin ? location.href.split('/')[0]+'//'+location.host : location.origin;

/**
* 重写暂存按钮
*/
var msgIndex="";

function tempSave() {
    var top="auto"//默认自动
	if (typeof ysdj5Flag !== "undefined") {
        msgIndex = window.layer.load(2, {offset:top,time: 10*500,shade:0.1});
	} else {
        msgIndex = parent.layer.load(2, {offset:top,time: 10*500,shade:0.1});
	}
    //暂存时增加校验的逻辑
    try{
        var child = document.getElementById("frmSheet").contentWindow;
        if(typeof(child.isTempSave) === 'function'){
            if(!child.isTempSave() && typeof ysdj5Flag !== "undefined"){
                window.layer.close(msgIndex);
                return;
            } else if (!child.isTempSave()) {
                parent.layer.close(msgIndex);
				return;
			}
        }
        if(typeof childFrameTempSave === 'function'){
            if(!childFrameTempSave()){
                parent.layer.close(msgIndex);
                return;
            }
        }
    }catch(e){

    }
    if(checkDIffDjxh()){//djxh不一致，不进行保存
        return;
    }

    var _guideParam=$("#_query_string_").val().replace(/\"/g,'').replace(/,/g,';').replace(/:/g,'-');//增加guideParam作为组合主键来确认是否生产一条新的依申请记录

    // SW2017150-361 联合申报业务,ybnsrzzs+fjssb, 暂存和保存按钮保存两份ysqxxVO和ysqZcbwVO, 在此处发起两笔业务的暂存请求
    var query_string = JSON.parse("{"+$("#_query_string_").val()+"}");
    if (query_string.lhsbywbm!==undefined && query_string.lhsbywbm!=='null') {
    	if (otherParams.fs_ysqxxid == undefined) {
    		if (typeof ysdj5Flag !== "undefined") {
                window.layer.close(msgIndex);
			} else {
                parent.layer.close(msgIndex);
			}
            parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 17px;"></i></span> 尊敬的纳税人：联合申报业务附税缺少必要参数，请刷新页面重试！',{time:2000});
			return ;
    	}
    	var zzs_data = getYbnsrzzsData(_guideParam);
    	var fs_data = getFsData(ywbm, query_string.lhsbywbm, _guideParam);

    	$.ajax({
    		type: "POST",
    		url: "xTempSave",
    		dataType: "json",
    		data: zzs_data,
    		success: function(data) {
    			if ('Y' == data.returnFlag) {
    				lhsbywSaveResult(true);
    			} else {
    				var returnType = data.returnType;
			    	if(returnType&&returnType==='refresh'){
			    		lhsbywSaveResult(false, data.errMsg);
			    		return;
			    	}
    			}
    		},
    		error : function() {
    			lhsbywSaveResult(false);
	        }
    	});
    	$.ajax({
    		type: "POST",
    		url: "../"+query_string.lhsbywbm+"/xTempSave",
    		dataType: "json",
    		data: fs_data,
    		success: function(data) {
    			if ('Y' == data.returnFlag) {
    				lhsbywSaveResult(true);
    			} else {
    				var returnType = data.returnType;
			    	if(returnType&&returnType==='refresh'){
			    		lhsbywSaveResult(false, data.errMsg);
			    		return;
			    	}
    			}
    		},
    		error: function() {
    			lhsbywSaveResult(false);
    		}
    	});
    } else {
	    var d = {};
	    if(typeof ysdj5Flag !== "undefined") {
            d['gdslxDm'] = $("#gdslxDm").val();
            d['ysqxxid'] = $("#ysqxxid").val();
            d['djxh'] = $("#djxh").val();
            d['nsrsbh'] = $("#nsrsbh").val();
            d['zcbw'] = encodeURIComponent(JSON.stringify(formData));
            d['sssqQ'] = $("#sssqQ").val();
            d['sssqZ'] = $("#sssqZ").val();
            d['ywbm'] = $("#ywbm").val();
            d['ywzt'] = "Y";
        } else {
            d['_query_string_'] = $("#_query_string_").val();
            d['gdslxDm'] = $("#gdslxDm").val();
            d['ysqxxid'] = $("#ysqxxid").val();
            d['nsrsbh'] = $("#nsrsbh").val();
            d['djxh'] = $("#djxh").val();
            d['secondLoadTag'] = $("#secondLoadTag").val();
            d['_bizReq_path_'] = $("#_bizReq_path_").val();;
            d['_guideParam'] = _guideParam;
            d['formData'] = encodeURIComponent(JSON.stringify(formData));
        }
	    $.ajax({
	        type : "POST",
	        url : typeof ysdj5Flag !== "undefined"?requestPrefix+"/sb/sl/YsqSl/tempSave?"+requestSuffix:"xTempSave",
            headers: typeof ysdj5Flag !== "undefined" ? ajaxHeader : {},
	        dataType : "json",
	        //contentType : "text/json",
	        data : typeof ysdj5Flag !== "undefined" ? JSON.stringify(d) : d,
	        success : function(data) {
	        	if (typeof ysdj5Flag !== "undefined") {
                    if(typeof data === "string"){
                        data = JSON.parse(data);
                    }
                    if ('000' === data.rtnCode) {
                        if(parent.location.href.indexOf("hbsb=Z")==-1) {
                            parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 24px;"></i></span> ' + tempSaveOkTip, {time: 2000});
                        }
                    } else {
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 24px;"></i></span> 尊敬的纳税人：暂存失败，请稍后再试！',{time:2000});
                    }
                    return;
				}
	            if ('Y' == data.returnFlag) {
                    if(parent.location.href.indexOf("hbsb=Z")==-1) {
                        //合并申报不弹
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 17px;"></i></span> ' + tempSaveOkTip, {time: 2000});
                    }

                    // 保存成功后回写合并申报
                    if(parent.location.href.indexOf("hbsb=Z")>-1 && typeof parent.parent.parent.updateYbcbz === "function") {
                        parent.parent.parent.updateYbcbz($("#ywbm").val());
                    }

                    if(typeof doSuccess === "function") {
                        doSuccess();
                    }
	            } else {
	            	var returnType = data.returnType;
			    	if(returnType&&returnType==='refresh'){
			    		var errMsg = data.errMsg;
			    		parent.layer.confirm(errMsg,{
		            		icon : 1,
		            		title:'提示',
		            		btn2noclose:1,
		            		btn : ["是","否"]
		            	},function(index){
			    			if (typeof ysdj5Flag !== "undefined") {
                                window.layer.close(msgIndex);
							} else {
                                parent.layer.close(msgIndex);
							}
		            		parent.layer.close(index);
		            		window.location.reload();
		            	});
			    		return;
			    	}else {
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 17px;"></i></span> 尊敬的纳税人：暂存失败，请稍后再试！',{time:2000});

			    	}
                    if(typeof doFail === "function") {
                        doFail();
                    }
	            }
	            if (typeof ysdj5Flag !== "undefined") {
                    window.layer.close(msgIndex);
				} else {
                    parent.layer.close(msgIndex);
				}
	        },
	        error : function() {
	        	if (typeof ysdj5Flag !== "undefined") {
                    window.layer.close(msgIndex);
				} else {
                    parent.layer.close(msgIndex);
				}
                if(typeof doFail === "function") {
                    doFail();
                }
                parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 17px;"></i></span> 尊敬的纳税人：暂存失败，请稍后再试！',{time:2000});
	        }
	    });
    }
}

// 统计联合申报业务两次tempSave请求的执行结果
var lhsbywSaveResult = (function (state, msg) {
    var num = 0;
    var result = true;
    var failMsg = undefined;
    return function saveResult(state, msg) {
        num++;
        result = result && state;
        failMsg = failMsg || msg;
        if (num === 2) {
            if (result) {
                parent.layer.close(msgIndex);
                parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 17px;"></i></span> 联合申报业务保存成功',{time:2000});
            } else {
                if (failMsg !== undefined) {
                    parent.layer.confirm(failMsg, {
                        icon : 0,
                        title:'提示',
                        btn2noclose:1,
                        btn : ["是","否"]
                    },function(index){
                        parent.layer.close(msgIndex);
                        parent.layer.close(index);
                        window.location.reload();
                    });
                } else {
                    parent.layer.close(msgIndex);
                    parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 17px;"></i></span> 尊敬的纳税人：暂存失败，请稍后再试！',{time:2000});
                }
            }
            num = 0;
            result = true;
            failMsg = undefined;
            return num;
        } else {
            return num;
        }
    }
})();

/**
 * 保存请求校验公式额外参数，默认为空。不带参数校验
 * @returns {null}
 */
function getSaveExtParam(){
    return null;
}

/**
 * 重写保存按钮
 */
function save() {
    var top="auto"//默认自动
    if (typeof ysdj5Flag !== "undefined") {
        msgIndex = window.layer.load(2, {offset:top,time: 10*500,shade:0.1});
	} else {
        msgIndex = parent.layer.load(2, {offset:top,time: 10*500,shade:0.1});
	}
    //暂存时增加校验的逻辑
    try{
        var child = document.getElementById("frmSheet").contentWindow;
        if(typeof(child.isTempSave) === 'function'){
            if(!child.isTempSave()){
            	if (typeof ysdj5Flag !== "undefined") {
                    window.layer.close(msgIndex);
				} else {
                    parent.layer.close(msgIndex);
				}
                return;
            }
        }
        if(typeof childFrameTempSave === 'function'){
            if(!childFrameTempSave()){
                parent.layer.close(msgIndex);
                return;
            }
        }
    }catch(e){

    }
    if(checkDIffDjxh()){//djxh不一致，不进行保存
        return;
    }

    var regEvent = new RegEvent();
    var extParam = getSaveExtParam();
    var tips = "";
    if(extParam){
        tips = regEvent.verifyAllNoAlertEx(extParam);
    }else{
        tips = regEvent.verifyAllNoAlertEx();
    }

    if('' != tips[0]){
    	if ($("#divSheetlist").children().length === 1) {
    		parent.layer.alert("<span style='color:red'>查看标黄底校验不通过的单元格并根据提示修改。表格所有校验通过后再保存。</span><br/><br/>" + tips[0], {
                title: "保存失败！（表格校验未通过）",
                icon: 2,
                area: ['50%', '50%']
            });
    	} else {
            parent.layer.alert("<span style='color:red'>左边附表栏红底数字表示此表单校验不通过的单元格数量，点击可进入对应表单查看标黄底校验不通过的单元格并根据提示修改。表格所有校验通过后再保存。</span><br/><br/>" + tips[0], {
                title: "保存失败！（表格校验未通过）",
                icon: 2,
                area: ['50%', '50%']
            });
    	}
        if (typeof ysdj5Flag !== "undefined") {
            window.layer.close(msgIndex);
		} else {
            parent.layer.close(msgIndex);
		}
        return;
    }else if('' != tips[1]){

        var index = parent.layer.confirm(tips[1], {
            title: "提示",
            icon: 0,
            closeBtn: 0,
            area: ['50%', '50%'],
            btn: ['返回修改', '确定'] //按钮
            , btn1: function () {
                //返回修改
                parent.layer.close(index);// 关闭
            }
            , btn2: function () {
                //忽略进入保存
                parent.layer.close(index);
                saveYsqbw(lhsbywSaveResult);
            }
        });

        return;
	}
    saveYsqbw(lhsbywSaveResult);

}

function saveYsqbw(fn){
    var _guideParam=$("#_query_string_").val().replace(/\"/g,'').replace(/,/g,';').replace(/:/g,'-');//增加guideParam作为组合主键来确认是否生产一条新的依申请记录

    // SW2017150-361 联合申报业务,ybnsrzzs+fjssb, 暂存和保存按钮保存两份ysqxxVO和ysqZcbwVO, 在此处发起两笔业务的暂存请求
    var query_string = JSON.parse("{"+$("#_query_string_").val()+"}");
    if (query_string.lhsbywbm === "fjssb") {
        var zzs_data = getYbnsrzzsData(_guideParam);
        var fjs_data = getFsData(ywbm, query_string.lhsbywbm, _guideParam);

        $.ajax({
            type: "POST",
            url: "xTempSave",
            dataType: "json",
            data: zzs_data,
            success: function(data) {
                if ('Y' == data.returnFlag) {
                    if(typeof fn == "function") {
                        fn(true);
                    }
                } else {
                    var returnType = data.returnType;
                    if(returnType&&returnType==='refresh'){
                        if(typeof fn == "function") {
                            fn(false, data.errMsg);
                        }
                        return;
                    }
                }
            },
            error : function() {
                if(typeof fn == "function") {
                    fn(false);
                }
            }
        });
        $.ajax({
            type: "POST",
            url: "../"+query_string.lhsbywbm+"/xTempSave",
            dataType: "json",
            data: fjs_data,
            success: function(data) {
                if ('Y' == data.returnFlag) {
                    if(typeof fn == "function") {
                        fn(true);
                    }
                } else {
                    var returnType = data.returnType;
                    if(returnType&&returnType==='refresh'){
                        if(typeof fn == "function") {
                            fn(false, data.errMsg);
                        }
                        return;
                    }
                }
            },
            error: function() {
                if(typeof fn == "function") {
                    fn(false);
                }
            }
        });
    } else {
        var d = {};
        if(typeof ysdj5Flag !== "undefined") {
            d['gdslxDm'] = $("#gdslxDm").val();
            d['ysqxxid'] = $("#ysqxxid").val();
            d['djxh'] = $("#djxh").val();
            d['nsrsbh'] = $("#nsrsbh").val();
            d['zcbw'] = encodeURIComponent(JSON.stringify(formData));
            d['sssqQ'] = $("#sssqQ").val();
            d['sssqZ'] = $("#sssqZ").val();
            d['ywbm'] = $("#ywbm").val();
            d['ywzt'] = "Y";
        } else {
            d['_query_string_'] = $("#_query_string_").val();
            d['gdslxDm'] = $("#gdslxDm").val();
            d['ysqxxid'] = $("#ysqxxid").val();
            d['nsrsbh'] = $("#nsrsbh").val();
            d['djxh'] = $("#djxh").val();
            d['secondLoadTag'] = $("#secondLoadTag").val();
            d['_bizReq_path_'] = $("#_bizReq_path_").val();
            ;
            d['_guideParam'] = _guideParam;
            d['formData'] = encodeURIComponent(JSON.stringify(formData));
        }
        $.ajax({
            type : "POST",
            url : typeof ysdj5Flag !== "undefined"?requestPrefix+"/sb/sl/YsqSl/tempSave?"+requestSuffix:"xTempSave",
            headers: typeof ysdj5Flag !== "undefined" ? ajaxHeader : {},
            dataType : "json",
            //contentType : "text/json",
            data : typeof ysdj5Flag !== "undefined" ? JSON.stringify(d) : d,
            success : function(data) {
                if (typeof ysdj5Flag !== "undefined") {
                    if(typeof data === "string"){
                        data = JSON.parse(data);
                    }

                    if ('000' === data.rtnCode) {
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-zhengque" style="color: #32bea6;font-size: 24px;"></i></span> '+saveOkTip,{time:2000});
                    } else {
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 24px;"></i></span> 尊敬的纳税人：保存失败，请稍后再试！',{time:2000});
                    }
                    return;
                }
                if ('Y' == data.returnFlag) {
                    parent.layer.alert(saveOkTip, {
                        icon: 1,title: "提示"
                    });

                    // 保存成功后回写合并申报
                    if(parent.location.href.indexOf("hbsb=Z")>-1 && typeof parent.parent.parent.updateYbcbz === "function") {
                        parent.parent.parent.updateYbcbz($("#ywbm").val());
                    }
                } else {
                    var returnType = data.returnType;
                    if(returnType&&returnType==='refresh'){
                        var errMsg = data.errMsg;
                        parent.layer.confirm(errMsg,{
                            icon : 1,
                            title:'提示',
                            btn2noclose:1,
                            btn : ["是","否"]
                        },function(index){
                            if (typeof ysdj5Flag !== "undefined") {
                                window.layer.close(msgIndex);
                            } else {
                                parent.layer.close(msgIndex);
                            }
                            parent.layer.close(index);
                            window.location.reload();
                        });
                        return;
                    }else{
                        parent.layer.alert('尊敬的纳税人：保存失败，请稍后再试！', {
                            icon : 5,title: "提示"
                        });
                    }

                }
                if (typeof ysdj5Flag !== "undefined") {
                    window.layer.close(msgIndex);
                } else {
                    parent.layer.close(msgIndex);
                }
            },
            error : function() {
                if (typeof ysdj5Flag !== "undefined") {
                    window.layer.close(msgIndex);
                    if (!blockTips) {
                        parent.layer.msg('<span class="ico-absolute"><i class="iconfont fsicon-tuichu" style="color: #ff6633;font-size: 24px;"></i></span> 尊敬的纳税人：保存失败，请稍后再试！',{time:2000});
                    }
                    if (fail) {
                        fail();
                    }
                    return;
                } else {
                    parent.layer.close(msgIndex);
                }
                parent.layer.alert('尊敬的纳税人：保存失败，请稍后再试！', {
                    icon : 5,title: "提示"
                });
            }
        });
    }
}


//根据总局验收标准，现对所有申报表点击申报时，进行提示（申报提示，文书不提示）
function ctips(isSecondCall){
    var url = parent.location.href;
    if (url.indexOf("lhsbywbm") > -1) {
        if (otherParams.fs_ysqxxid!==undefined) {
            var queryString = $("#_query_string_").val();
            $("#_query_string_").val(queryString+",\"fs_ysqxxid\":\""+otherParams.fs_ysqxxid+"\"");
            $("#fs_ysqxxid").val(otherParams.fs_ysqxxid);
        }
        else {
            parent.layer.alert('尊敬的纳税人：联合申报业务附税异常，缺少必要参数，请刷新页面重试。', {
                icon : 5,
                closeBtn: 0
            }, function(index) {
                parent.layer.close(index);
                $("body").unmask();
                prepareMakeFlag = true;
            });
            return;
        }
    }

    if(isZzyXwqyHj()){
        //调接口弹框
        var reqParams = {
            "sid":"dzswj.ywzz.sb.zzyxwqyhj.cxZzyxwqyhj",
            "pzxh":otherParams.gzsb == 'zx'?otherParams.pzxh:''
        };
        parent.requestYwztData(reqParams,function(data){
            if(typeof  afterCxZzyxwqyhj == "function"){
                data = afterCxZzyxwqyhj(data);
            }
            if(data && data.zzyxwqyhj){
                formData.kz_.zzyXwqyHj = data.zzyxwqyhj;
            }else{
                formData.kz_.zzyXwqyHj = {
                    "scsbbz":"Y",
                    "lbDm":"",
                    "xslx":"",
                    "bxsyy":"",
                    "bxsyybz":""
                }
            }
            if(data && data.ss_){
                if(!formData.ss_){
                    formData.ss_ = {};
                }
                formData.ss_.zzyxwqyhj = data.ss_.zzyxwqyhj;
            }

            var layerx = layer;
            var sssqZ = $("#sssqZ").val();
            //更正时,如果查到正常申报已享受缓缴,则默认缓缴
            if (zzyXwqyHjQzxsNoAlertBz() == 'Y' && (formData.kz_.zzyXwqyHj.lbDm == '000000000051' ||
                formData.kz_.zzyXwqyHj.lbDm == '000000000052')) {
                var zzyXwqyHjData = {};
                zzyXwqyHjData['lbDm']= formData.kz_.zzyXwqyHj.lbDm;
                zzyXwqyHjData['xslx']='Y';
                zzyXwqyHjData['yxshjzcLy']='';
                zzyXwqyHjData['bxsyy']='';
                saveZzyXwqyHj(zzyXwqyHjData, layerx, 0, isSecondCall);
            }else if(otherParams.gzsb == 'zx' && isGzTipsHj() && sssqZ <= '2021-12-31'){
                //更正时支持个性化白名单规则
                //更正时,如果正常申报已享受缓缴,则提示给纳税人,并默认提交缓缴
                layer.alert("若您已享受制造业中小微企业2021年第四季度部分税费延缓3个月缴纳的政策，按照《国家税务总局 "
                    +"财政部关于延续实施制造业中小微企业延缓缴纳部分税费有关事项的公告》（国家税务总局 财政部公告2022年第2号）规定，"
                    +"2021年第四季度相关税费缓缴期限继续延长6个月。"
                    +"<br/>若您需要退还已缴纳的符合2号公告延缓缴纳条件的税费，可申请办理退税（费）。",
                function(index){
                    layer.close(index);
                    var zzyXwqyHjData = {};
                    zzyXwqyHjData['lbDm']= formData.kz_.zzyXwqyHj.lbDm;
                    zzyXwqyHjData['xslx']='Y';
                    zzyXwqyHjData['yxshjzcLy']='';
                    zzyXwqyHjData['bxsyy']='';
                    saveZzyXwqyHj(zzyXwqyHjData, layerx, 0, isSecondCall);
                });
            }else
            //首次申报或制造业中小微企业才进行弹框处理  000000000051
            if(formData.kz_.zzyXwqyHj.scsbbz == 'Y' || formData.kz_.zzyXwqyHj.lbDm == '000000000051' ||
                formData.kz_.zzyXwqyHj.lbDm == '000000000052') {
                var qddm = "";
                if(parent.queryString && parent.queryString.indexOf("qddm")>-1){
                    qddm = parent.queryString2Obj().qddm;
                }
                layerx.open({
                    type: 2,
                    id: 'iFrameZzyXwqyHj',
                    area: '1010px',
                    title: false,
                    scrollbar: false,
                    closeBtn: false,
                    fixed: false,
                    btn: ['确认提交', '返回修改'],
                    content: 'form/zzyXwqyHj.html?qddm='+qddm,
                    yes: function (index, layero) {
                        //有校验存在就阻断
                        var frameWindow = layero.find("iframe")[0].contentWindow;
                        if (frameWindow.$("input.yellow").length > 0) {
                            frameWindow.$("input.yellow").mouseover();
                            setTimeout(function () {
                                frameWindow.layer.closeAll('tips');
                            }, 5000);
                            return false;
                        }
                        //点击【确定】
                        var frameBody = layerx.getChildFrame('body', index);
                        var rawFormData = frameBody.contents().find("#formDataSpan").text();
                        rawFormData = JSON.parse(rawFormData);
                        var data = rawFormData.data;
                        // 保存文件调查结果
                        saveZzyXwqyHj(data, layerx, index, isSecondCall);
                    },
                    btn2: function (index, layerDom) {
                        layerx.close(index);
                        prepareMakeFlag = true;
                        $("body").unmask();
                        if (typeof umMaskZdy == "function") {
                            umMaskZdy();
                        }
                    },
                    zIndex: layerx.zIndex, //重点1
                    success: function (layero, index) {
                        layerx.setTop(layero); //重点2
                        //找到当前弹出层的iframe元素
                        var iframe = layero.find('iframe');
                        //设定iframe的高度为当前iframe内body的高度
                        var height = iframe[0].contentDocument.body.offsetHeight;
                        height = MIN(380, height);
                        iframe.css('height', height);
                        //重新调整弹出层的位置，保证弹出层在当前屏幕的中间位置
                        layero.css('top', (window.innerHeight - iframe[0].offsetHeight) / 2);

                    }
                });
            }else{
                ctipsOrinal(isSecondCall);
            }
        },function(data){
            //接口调用失败.
            $("body").unmask();
            if(typeof umMaskZdy == "function"){
                umMaskZdy();
            }
            parent.layer.alert('调用服务失败!', {title: "填表页调用服务", icon: 5});
            prepareMakeFlag = true;
            return;
        });
    } else if(isZzyXwqyHjJmsb()){
        saveZzyXwqyHjJmsb();
    } else if(isZzyXwqyHjQysdsNd()){
        zzyHjQysds18ndMsg(isSecondCall);
    }else{
        ctipsOrinal(isSecondCall);
    }

}

/**
 * 更正提示缓缴弹框判断. 此处福建有个性化,根据白名单判断
 * @returns {boolean}
 */
function isGzTipsHj(){
    if(typeof isGzTipsHjDeliver == 'function'){
        return isGzTipsHjDeliver();
    }
     if(formData.kz_.zzyXwqyHj.hjbz == 'Y' && (formData.kz_.zzyXwqyHj.lbDm == '000000000051' ||
         formData.kz_.zzyXwqyHj.lbDm == '000000000052')){
         return true;
     }else{
         return false;
     }
}

/**
 * 是否年报缓交
 * @returns {boolean}
 */
function isZzyXwqyHjQysdsNd(){
    var ywbm = otherParams.ywbm!=null ? otherParams.ywbm.toUpperCase() : '';
    if('QYSDS_A_18ND'==ywbm || 'QYSDS_B_18ND'==ywbm || 'QYSDS_KDQJY_21ND'==ywbm || 'QYSDS_KDQJY_18ND'==ywbm){
        return true;
    }else if('QYSDS_B_YJD'==ywbm){
        var sssqQ = $("#sssqQ").val();
        var sssqZ = $("#sssqZ").val();
        if(!isNull(sssqQ) && !isNull(sssqZ)){
            var sssqQDate = new Date(sssqQ);
            var sssqZDate = new Date(sssqZ);
            var m = sssqZDate.getMonth()-sssqQDate.getMonth();
            if(m==11){
                return true;
            }
        }
    }
    return false;
}

/**
 * 企业所得税年报处理
 * @param isSecondCall
 */
function zzyHjQysds18ndMsg(isSecondCall){
        try{
            var lbDm;
            var layerx = parent.layer;
            var ybtse = formData.kz_.ysqxx.sbmxlist[0].ybtse;
            //查询原子服务
            var reqParams = {
                "sid":"dzswj.ywzz.sb.zzyxwqyhj.cxZzyxwqyhjQysdsNb"
            };

            if(ybtse<=0){
                reqParams.qType='qgroup';
            }

            var wrkxhhjz = null;
            parent.requestYwztData(reqParams,function(data) {
                if (data) {
                    data = data.zzyxwqyhj;
                    var jgbz = data.jgbz;
                    if(jgbz!='Y'){//符合缓交才往下走
                        ctipsOrinal(isSecondCall);
                        return;
                    }
                    var msg;
                    if(ybtse<=0){
                        //查询未入库销号合计值
                        wrkxhhjz = data.ybtse;
                        if(!isNull(wrkxhhjz)){
                            wrkxhhjz = parseFloat(wrkxhhjz);
                            if(Math.abs(ybtse) > wrkxhhjz){
                                lbDm = "QYSDSNB_1";
                                msg = "对制造业中小微企业实施缓缴税费是党中央、国务院助企纾困的重要举措，您享受了2021年第四季度延缓缴纳企业所得税政策，目前第四季度延缓缴纳的税款"+wrkxhhjz+"元尚未入库。如您申请退税，将先抵减第四季度缓缴税款，按照抵减后的余额办理退库。如您希望全额退库，请先缴纳2021年第四季度企业所得税缓缴税款，再办理退税申请。";
                            }else{
                                lbDm = "QYSDSNB_2";
                                msg = "对制造业中小微企业实施缓缴税费是党中央、国务院助企纾困的重要举措，您享受了2021年第四季度延缓缴纳企业所得税政策，目前第四季度延缓缴纳的税款"+wrkxhhjz+"元尚未入库。建议您在缓缴期限到期时办理退抵税申请，先抵减退税金额，再将尚未抵减的剩余第四季度缓缴税款缴纳入库。";
                            }
                        }

                    }else if(ybtse>0){
                        //查询2021年第四季度已享受制造业中小微企业缓税政策，且暂未全部缴纳第四季度享受缓税政策税款的纳税人
                        var yjsfNum = data.yjsfNum;
                        if(yjsfNum!=null && yjsfNum>0){
                            lbDm = "QYSDSNB_3";
                            msg = "对制造业中小微企业实施缓缴税费是党中央、国务院助企纾困的重要举措，您正处于2021年第四季度企业所得税缓缴期间，根据《国家税务总局 财政部关于延续实施制造业中小微企业延缓缴纳部分税费有关事项的公告》（国家税务总局 财政部公告2022年第2号）规定，2021年度汇算清缴申报产生应补税款的，可与2021年第四季度延缓缴纳的企业所得税一并延后缴纳入库。";
                        }
                    }

                    if(!isNull(msg)){
                        var a= layerx.open({
                            type: '1',
                            title: ['提示'],
                            area: ['500px'],
                            closeBtn:false,
                            content: msg,
                            btn: ['确定'],
                            yes: function (a) {
                                saveZzyxwqyhjQysdsNb(layerx,a,isSecondCall,wrkxhhjz,lbDm);
                            }
                        });
                    }else{
                        ctipsOrinal(isSecondCall);
                    }
                }else{
                    ctipsOrinal(isSecondCall);
                }
            },function(data){
                //接口调用失败.
                $("body").unmask();
                if(typeof umMaskZdy == "function"){
                    umMaskZdy();
                }
                parent.layer.alert('调用服务失败!', {title: "年报缓交调用服务", icon: 5});
                prepareMakeFlag = true;
                return;
            });
        }catch (e) {
            ctipsOrinal(isSecondCall);
        }
}

/**
 * 保存年报缓交
 */
function saveZzyxwqyhjQysdsNb(layerx,index,isSecondCall,wrkxhhjz,lbDm){
    var reqVo = {};
    reqVo.ysqxxid = $("#ysqxxid").val();
    reqVo.lbDm = lbDm;//年报虚拟出来
    reqVo.wrkxhhjz = wrkxhhjz;
    reqVo.sssqQ = otherParams.sssqQ;
    reqVo.sssqZ = otherParams.sssqZ;
    reqVo.ywbm = ywbm?ywbm:otherParams.ywbm;//也可以写死
    reqVo.ywlx = "SB";
    reqVo.ywzt = "Y";
    reqVo.gzlxDm = otherParams.gzsb == "zx"?"5":"1";
    reqVo.sid = "dzswj.ywzz.sb.zzyxwqyhj.insertZzyxwqyhj";
    parent.requestYwztData(reqVo,function(_data) {
        try {
            if(_data.retcode != 'Y'){
                layerx.alert(_data.retxx, {
                    title:'提示',
                    icon:7,
                    zIndex: layerx.zIndex, //重点1
                    success:function(layero){
                        layerx.setTop(layero);
                    }
                });
            }else{
                layerx.close(index);
                //判断云上是否有个性化
                if(typeof saveZzyHjCDeliver == "function" && otherParams.hbsb != 'Z'){
                    reqVo.sid = "dzswj.ywzz.sb.zzyxwqyhj.insertYsdjZzyxwqyhj";
                    reqVo.uuid = _data.id;
                    saveZzyHjCDeliver(reqVo,ctipsOrinal,isSecondCall);
                }else{
                    //保存成功后,继续之前的流程
                    ctipsOrinal(isSecondCall);
                }
            }
        }catch (e) {
            console.log(_data);
            layerx.alert("保存年报缓交失败!", {
                icon : 5,
                zIndex: layerx.zIndex, //重点1
                success:function(layero){
                    layerx.setTop(layero);
                }
            });
        }
    },function(_error){
        console.log(_error);
        layerx.alert("保存年报缓交失败!", {
            icon : 5,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
    });
}


/**
 * 是否默认享受
 * QHGSDZSWJ-1394首次申报的新办纳税人或标记为制造业中小微企业的纳税人，当应补退税额等于0时，强制享受税款缓征，不可修改
 * 配置dzswj_sbzs_code_CS_NSSB_XTCS : SBZX_ZZYXWQYHJ_MRXSBZ
 * return Y:N
 */
function isMrxsHjbz(){
    try{
        var ywbm = $("#ywbm").val().toUpperCase();
        var ybtse = null;
        if(ywbm=='DQDEZXSB'){
            ybtse = formData.ht_.dqdezxSbbdxxVO.dqdezxsb.dqdezxsbGrid.t_ybtseHj;
        }else if(ywbm=='TYSB'){
            ybtse = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.t_bqybtsfehj;
        }else{
            ybtse = formData.kz_.ysqxx.sbmxlist[0].ybtse;
        }

       var mrxsBz =  formData.kz_.zzyXwqyHj.mrxsBz;
       if(!isNull(mrxsBz)){
           return mrxsBz;
       }

    }catch (e) {

    }
    return "N";
}

/**
 * 前置判断,是否符合制造业小薇企业缓缴政策
 * @returns {boolean}
 */
function isZzyXwqyHj(checkJmsb){
    var ywbm = $("#ywbm").val().toUpperCase();
    var sssqQ = $("#sssqQ").val();
    var sssqZ = $("#sssqZ").val();

    if(isNull(checkJmsb)){
        //默认检查
        checkJmsb = "Y";
    }
    try {
    	//云上大连不调
        var nsrjbxx = formData.fq_.nsrjbxx;
        if (isNull(nsrjbxx)) {
        	//财报纳税人信息节点
        	nsrjbxx = formData.fq_.djNsrxx;
        }
    	if (!isNull(nsrjbxx) && !isNull(nsrjbxx.swjgDm) && nsrjbxx.swjgDm.substring(0,5)=="12102") {
    		 var url = parent.location.href;
    		 if (url.indexOf("ysdj") > -1) {
    			 return false;
    		 }
    	}
	} catch (e) {
		//纳税人信息获取有误,此种情况不处理
		console.log(e);
	}

    if(checkJmsb=='Y' && typeof isJmsb == "function" && isJmsb()){
        return false;
    }
    if(['YBNSRZZS','XGMZZS','XFSSB','QYSDS_A_YJD','QYSDS_B_YJD','TYSB','DQDEZXSB','QYSDS_A_YJD_YJLSB'].indexOf(ywbm)>-1 && sssqQ >= '2021-10-01' && sssqZ <= '2022-06-30'){
        //年报和月季报B用的同个ywbm，需要这里做下区分判断，年报不往下走
        if('QYSDS_B_YJD'==ywbm && isZzyXwqyHjQysdsNd()){
            return false;
        }
        if(ywbm == 'TYSB'){
            //企业所得税10104、个人所得税1016、增值税10101、消费税10102及附征的城市维护建设税10109、教育费附加30216、地方教育附加30203，才会进入【缓缴税款】判断流程
            var sbxxGridlb = formData.ht_.fxmtysbbdVO.fxmtySbb.sbxxGrid.sbxxGridlb;
            var flag = false;
            for(var i = 0; i < sbxxGridlb.length;i++){
                if(sbxxGridlb[i].zsxmDm == '10106' || sbxxGridlb[i].zsxmDm == '10104'
                    || sbxxGridlb[i].zsxmDm == '10101' || sbxxGridlb[i].zsxmDm == '10102'
                    || sbxxGridlb[i].zsxmDm == '10109' || sbxxGridlb[i].zsxmDm == '30216'
                    || sbxxGridlb[i].zsxmDm == '30203'){
                    flag = true;
                    break;
                }
            }
            return flag;
        }
        return true;
    }
    return false;
}

/**
 * 前置判断,是否符合制造业小薇企业缓缴政策(静默申报使用)
 */
function isZzyXwqyHjJmsb(){
    // var isYsdj = parent.location.href.indexOf("ysdj") > -1;
    // if(!isYsdj){
    //     return false;
    // }
    if(!(typeof isJmsb == "function" && isJmsb())){
        return false;
    }
    var checkJmsb = 'N';
    return isZzyXwqyHj(checkJmsb);
}

/**
 * 静默申报特殊处理
 */
function saveZzyXwqyHjJmsb(isSecondCall){
    var layerx = parent.layer;
    var params = getUrlParamSbzx();
    var hjbz = params['hjbz'];
    var yxshjzcLy = params['yxshjzcLy'];
    //如果是首次，lbDm不能为空,(制造业中型企业:000000000051  制造业小微企业:000000000052   均不符合:3)
    var lbDm = params['lbDm'];

    if(typeof isJmsb == "function" && isJmsb()){
        var reqParams = {
            "sid":"dzswj.ywzz.sb.zzyxwqyhj.cxZzyxwqyhj"
        };
        parent.requestYwztData(reqParams,function(data) {
            if (data && data.zzyxwqyhj) {
                formData.kz_.zzyXwqyHj = data.zzyxwqyhj;
                var scsbbz = data.zzyxwqyhj.scsbbz;
                if('Y'==scsbbz){
                    //首次均不符合走正常申报
                    if('3'==lbDm){
                        ctipsOrinal(isSecondCall);
                        return;
                    }
                }
                //以接口为准，接口没有才拿url参数值
                lbDm = !isNull(data.zzyxwqyhj.lbDm) ? data.zzyxwqyhj.lbDm : lbDm;

                //云上直接缓交
                var isYsdj = parent.location.href.indexOf("ysdj") > -1;
                if(isYsdj){
                    hjbz='Y';
                }

                //符合制造业51，52才进行缓交业务
                if('Y'==scsbbz || lbDm == '000000000051' || lbDm == '000000000052') {
                    //符合零申报配置缓交的企业，自动做缓交
                    var mrxsHjbz = isMrxsHjbz();
                    if(mrxsHjbz=='Y'){
                        hjbz='Y';
                    }
                    //符合缓交没传hjbz直接阻断
                    if(isNull(hjbz)){
                        layerx.alert("缓交标志不能为空!", {
            				title:'提示',
            				icon:7,
                            zIndex: layerx.zIndex, //重点1
                            success:function(layero){
                                layerx.setTop(layero);
                            }
                        });
                        return;
                    }
                    // 保存文件调查结果
                    var zzyXwqyHjData = {};
                    zzyXwqyHjData['lbDm']=lbDm;
                    zzyXwqyHjData['hjbz']=hjbz;
                    zzyXwqyHjData['yxshjzcLy']=yxshjzcLy;
                    zzyXwqyHjData['xslx']=hjbz;
                    zzyXwqyHjData['bxsyy']=yxshjzcLy;
                    saveZzyXwqyHj(zzyXwqyHjData, layerx, 0, isSecondCall);
                    return;
                }else{
                    ctipsOrinal(isSecondCall);
                    return;
                }
            }else{
                ctipsOrinal(isSecondCall);
                return;
            }
        });
    }else{
        ctipsOrinal(isSecondCall);
        return;
    }

}

/**
 * 获取url参数值
 * @returns {[]}
 */
function getUrlParamSbzx(){
    var data = [];
    try {
        /*获取请求信息*/
        var info = location.search;
        /*去除？*/
        info = info.length > 0 ? info.substring(1) : " ";
        /*以&分割字符串*/
        var result1 = info.split("&");
        /*存储key和value的数组*/
        var key,value;
        for(var i=0;i<result1.length;i++){
            /*以=分割字符串*/
            var result2 = result1[i].split("=");
            key = result2[0];
            value = result2[1];
            data[key] = value;
        }
    } catch (e) {
        // TODO: handle exception
    }
    return data;
}


/**
 * 原ctips逻辑, ctips中新增弹框
 * @param isSecondCall
 */
function ctipsOrinal(isSecondCall){
    //JSONE-15796 减税降费监控结果为true则表示减税降费已经弹框，则不需要原来的弹框了
    if(jsjfMonitor(isSecondCall)){
        return;
    }

    var xybNoTip = $("#xybNoTip").val();
    var info_submit_ing = "正在提交，请稍候...";
    var info_progress_ing = "正在处理，请稍候...";
    var confirm_submit = "您确定要提交申报吗？";
    if(typeof parent.submitName == "string"){
        info_submit_ing = "正在" + parent.submitName + "，请稍候...";
        confirm_submit = "您确定要"+ parent.submitName +"吗？";
    }
    if(xybNoTip != true && xybNoTip != "true" && (!isJmsb())) {
        layer.confirm(confirm_submit, {
            icon: -1, title: '提示',
            btn2: function (index) {
                $("body").unmask();
                if(typeof umMaskZdy == "function"){
                    umMaskZdy();
                }
                prepareMakeFlag = true;
                layer.close(index);
                return;
            }
            ,cancel: function(index, layero){
                $("body").unmask();
                if(typeof umMaskZdy == "function"){
                    umMaskZdy();
                }
                prepareMakeFlag = true;
                layer.close(index);
                return;
            }
        }, function (index) {
            if (typeof(parent.makeTypeDefualt) != "undefined" && parent.makeTypeDefualt == 'HTML') {
                // $("body").mask("正在提交，请稍候...");
                maskZdy(info_submit_ing);
            } else {
                // $("body").mask("正在处理，请稍候...");
                maskZdy(info_progress_ing);
            }

            /**
             * 申报提交时框架校验成功后调用业务特有校验（一般在ywbm.js中实现）
             * 然后在业务特有校验中调用doBeforSubmitForm，在提交表单之前做一些事情。如弹出申报前的确认提示
             * 最后由doBeforSubmitForm调用submitForm实现申报提交
             */
            doAfterVerify(doBeforSubmitForm, submitForm, isSecondCall);

            layer.close(index);
        });

    }else{
        if(typeof(parent.makeTypeDefualt) != "undefined" && parent.makeTypeDefualt == 'HTML') {
            // $("body").mask("正在提交，请稍候...");
            maskZdy(info_submit_ing);
        } else {
            // $("body").mask("正在处理，请稍候...");
            maskZdy(info_progress_ing);
        }

        /**
         * 申报提交时框架校验成功后调用业务特有校验（一般在ywbm.js中实现）
         * 然后在业务特有校验中调用doBeforSubmitForm，在提交表单之前做一些事情。如弹出申报前的确认提示
         * 最后由doBeforSubmitForm调用submitForm实现申报提交
         */
        doAfterVerify(doBeforSubmitForm,submitForm,isSecondCall);
    }
}



function getYbnsrzzsData(_guideParam) {
	var d = {};
    d['_query_string_'] = $("#_query_string_").val();
    d['gdslxDm'] = $("#gdslxDm").val();
    d['ysqxxid'] = $("#ysqxxid").val();
    d['nsrsbh'] = $("#nsrsbh").val();
    d['djxh'] = $("#djxh").val();
    d['secondLoadTag'] = $("#secondLoadTag").val();
    d['_bizReq_path_'] = _bizReq_path_;
    d['_guideParam'] = _guideParam;
    d['formData'] = encodeURIComponent(JSON.stringify(formData));
    return d;
}

function getFsData(ywbm, lhsbywbm, _guideParam) {
	var d = {};
	d['_query_string_'] = $("#_query_string_").val().replace(new RegExp(ywbm.toLowerCase(),'g'), lhsbywbm.toLowerCase()).replace(new RegExp(ywbm.toUpperCase(),'g'), lhsbywbm.toUpperCase());
    d['gdslxDm'] = $("#gdslxDm").val();
    d['ysqxxid'] = otherParams.fs_ysqxxid;
    d['nsrsbh'] = $("#nsrsbh").val();
    d['djxh'] = $("#djxh").val();
    d['secondLoadTag'] = $("#secondLoadTag").val();
    d['_bizReq_path_'] = _bizReq_path_.replace(new RegExp(ywbm.toLowerCase(),'g'), lhsbywbm.toLowerCase()).replace(new RegExp(ywbm.toUpperCase(),'g'), lhsbywbm.toUpperCase());
    d['_guideParam'] = _guideParam.replace(new RegExp(ywbm.toLowerCase(),'g'), lhsbywbm.toLowerCase()).replace(new RegExp(ywbm.toUpperCase(),'g'), lhsbywbm.toUpperCase());
    // 把formData拆开将有fs_后缀的节点抽取出来生成附税的报文
    var fs_formData = {};
    for(var key in formData){
    	if (key.indexOf("_fs_") > 0) {
    		fs_formData[key.substring(0, key.indexOf('_')+1)] = formData[key];
    		delete fs_formData[key];
    	}
    }
    d['formData'] = encodeURIComponent(JSON.stringify(fs_formData));
    return d;
}

/**
 * setNode("formData.hq_.yjxxGrid.yjxxGridlb","[]")
 * 修改节点的值，用于删除多余节点
 * @param jpath
 * @param value
 */
function setNode(jpath,value){
	eval(jpath+"="+value);
}

/**
 * setNotNullNode('formData.ht_.SB100VO.SB100BdxxVO.cjbdxml.qykjzdfzForm.zlbscjuuid','0','""')
 * 核心返回为0的话要置空
 * @param jpath
 * @param val
 * @param value
 */
function setNotNullNode(jpath,val,value){
    if(eval(jpath+"=="+val) ) {

        eval(jpath+"="+value);
    }
}

function flzlStateContains(flzlDm){
	if (otherParams.flzlState!=null) {
		return otherParams.flzlState[flzlDm] == 'Y';
	} else {
		return false;
	}
}

/**
 * 新冠疫情减免提示
 * 符合条件提示，否则不提示
 * @param hyDm
 * @returns void
 */
function zzsXgyqJmTips(hyDm){
    if( isJmsb() ) return;
	var reg1 = new RegExp("(532.*)|(543.*)|(552.*)|(581.*)|(5612)");
	var reg2 = new RegExp("(541.*)|(542.*)|(551.*)|(61.*)|(62.*)|(80.*)|(83.*)|(84.*)|(85.*)|(88.*)|(89.*)|(90.*)|(7291)|(602.*)|(609.*)");
	var content = "";
	var area = ['840px', '240px'];
	if(hyDm.replace(reg1,"") == ""){
		content = "尊敬的纳税人、缴费人，为进一步巩固拓展减税降费成效，助力市场主体纾困解难，对部分2020年底到期税费优惠政策（不包括社会保险费），您可暂按原政策文件规定申报享受优惠（原减免性质代码可继续使用）。详细情况可咨询主管税务分局、税务所或12366纳税服务热线。感谢您的理解与支持！";
	}else if(hyDm.replace(reg2,"") == ""){
		content = '尊敬的纳税人、缴费人，为进一步巩固拓展减税降费成效，助力市场主体纾困解难，对部分2020年底到期税费优惠政策（不包括社会保险费），您可暂按原政策文件规定申报享受优惠（原减免性质代码可继续使用）。详细情况可咨询主管税务分局、税务所或12366纳税服务热线。感谢您的理解与支持！';
	}else{
		return;
	}

	layer.open({
		type : 1,
		area : area,
		title : "提示",
		content : content,
		btn:['确定'],
		yes: function(index){
			layer.close(index);
		},
		zIndex: layer.zIndex, //重点1
		success: function(layero){
		    layer.setTop(layero); //重点2
		  }
	});

}

/**
 * 判断是否符合正则表达式
 * @param str
 * @param regTxt
 * @returns true 符合，false不符合
 */
function regMatch(str,regTxt){
	regTxt = regTxt.replace(/\./g,".*");
	var reg = new RegExp(regTxt);
	if(str.replace(reg,"") == ""){
		return true;
	}else{
		return false;
	}
}

var maskIndex = -1;
/**
 * mask自定义
 * 电子税务局走查,load有文字和无文字样式修改
 * 由于定时遮罩,还存在覆盖,所以不适用所有场景
 * @param msg 可以为空
 */
function maskZdy (msg) {
	if (!isNull(msg)) {
        maskIndex = layer.msg(msg, {
	        icon:20,
	        shade: [0.8,'#fff'],
	        skin: 'layui-layer-loadTxt',
	        time:20000
	    });
	}else {
        maskIndex = layer.load(20, {
		time: 20000,
		shade:[0.8,'#fff']});
	}
}

/**
 * unmask自定义
 * 电子税务局走查,load有文字和无文字样式修改
 * 由于定时遮罩,还存在覆盖,所以不适用所有场景
 */
function umMaskZdy(){
    layer.close(maskIndex);
}

function afterFormulaInitCallback(){
    if(jsonParams){
        var needUploadFormData = ((jsonParams["yqgzsb"] == "Y") && (jsonParams["gzsjlybz"] == "03"))
            || (jsonParams["yshbz"] == "Y");
        var useZcbw = jsonParams["useZcbwFlag"];
        if(needUploadFormData){
            var ysqxxid = $("#ysqxxid").val();
            var pzxh = jsonParams["pzxh"];
            var sbuuid = jsonParams["sbuuid"];
            var reqParams = {
                "sid":"dzswj.ywzz.dbSbzx.sbbd.initBwbd",
                "ysqxxid":ysqxxid,
                "pzxh":pzxh,
                "sbuuid":sbuuid,
                "useZcbwFlag":useZcbw,
                "formData":JSON.stringify(formData)
            };
            parent.requestYwztData(reqParams,function(data){
                console.log("uploadFormData success!");
                console.log(data);
            },function(data){
                console.log("!!ERROR: uploadFormData fail!");
            });
        }
    }
     if (typeof subAfterFormulaInitCallback == "function"){
         subAfterFormulaInitCallback();
     }
}

/**
 * 保存附税依申请报文
 * 联合申报：当配置为ocx时，走submitFormData方法时，保存报文需要同时保存一份附税的报文
 */
function saveFsYsqbw(saveYsqbwUrl, data) {
	// 处理主税的formData转为附税的
	var queryString = JSON.parse("{"+$("#_query_string_").val()+"}");
	var zsywbm = queryString.ywbm.toLowerCase();
	queryString.ywbm = queryString.lhsbywbm.toUpperCase();
	data['_query_string_'] = JSON.stringify(queryString).substring(1, JSON.stringify(queryString).length-1);
	data['ysqxxid'] = queryString.fs_ysqxxid;
	data['_bizReq_path_'] = data['_bizReq_path_'].replace(zsywbm, queryString.lhsbywbm);
	data['ywbm'] = queryString.lhsbywbm;
	if(queryString['fs_pzxh']){
        data['pzxh'] = queryString['fs_pzxh'];
    }
    if(queryString['fs_sbuuid']){
        data['sbuuid'] = queryString['fs_sbuuid'];
    }

	var saveData = data['saveData'];
	saveData = JSON.parse(decodeURIComponent(saveData));
	var fs_saveData = {};
    for(var key in saveData){
    	if (key.indexOf("_fs_") > 0) {
    		fs_saveData[key.substring(0, key.indexOf('_')+1)] = saveData[key];
    		delete fs_saveData[key];
    	}
    }
    data['saveData'] = encodeURIComponent(JSON.stringify(fs_saveData));

	var submitData = data['submitData'];
	if(submitData != null && submitData != "") {
        submitData = JSON.parse(decodeURIComponent(submitData));
        var fs_submitData = {};
        for (var key in submitData) {
            if (key.indexOf("_fs_") > 0) {
                fs_submitData[key.substring(0, key.indexOf('_') + 1)] = submitData[key];
                delete fs_submitData[key];
            }
        }
        data['submitData'] = encodeURIComponent(JSON.stringify(fs_submitData));
    }

	$.ajax({
		type : "POST",
		async : false,
		url : saveYsqbwUrl,
		data : data,
		dataType : "json",
		success: function(data) {
			var returnFlag = data.returnFlag;
		    if(returnFlag==='N'){
				var returnType = data.returnType;
		    	var errMsg = data.errMsg;
		    	if(returnType && returnType === 'refresh'){
		    		parent.layer.confirm(errMsg,{
	            		icon : 1,
	            		title:'提示',
	            		btn2noclose:1,
	            		btn : ["是","否"]
	            	},function(index){
	            		parent.layer.close(index);
	            		window.location.reload();
	            	});
		    		return false;
		    	}
		    	parent.layer.alert(errMsg, {title: "填表页附税报文保存异常", icon: 5});
		    	return false;
		    } else {
		    	return true;
		    }
		},
		error: function(aa) {
			window.parent.layer.open({
        		type:1,
        		area:['840px','420px'],
        		content:aa.responseText
        	});
			return false;
		}
	});
}
var popIndex = 0;
var dzbdtemp='';
function pop09(callBack,linkcs,fileNameTemp,dzbd){
    var sbywbm = $("#ywbm").val();
    if($('#importBtn').attr('upload-flag')==undefined){
        dzbdtemp=dzbd;
        $('#importBtn').attr('upload-flag',true);//防止重复初始化上传控件
        //当某个税种存在多个导入的附表时，该link拼接上的dzbd是无效的，在后台会优先取excel模板上的dzbd。
        var link = origin+(cp.indexOf("/")==0?"":"/")+cp+'/ywzt/importExcelFile.do?ywbm='+sbywbm+'&dzbd='+dzbd;
        if (location.href.indexOf("test=true") > -1) {
            link += "&djxh=" + $("#djxh").val() + "&nsrsbh=" + $("#nsrsbh").val()
                + "&test=true";
        }
        if(linkcs!=undefined&&fileNameTemp!=undefined)
        {
            $("#modelDownload").attr("href",origin+(cp.indexOf("/")==0?"":"/")+cp+'/biz/sb/'+linkcs)
                .attr("download",fileNameTemp).show();
        }
        var UPLOAD_FILES = {};
        upload.render({
            elem: '#selectFile'
            ,url: link
            ,auto: false
            ,bindAction: '#importBtn'
            ,exts:'xls'
            ,accept:'file'
            ,field:'xls'
            ,before: function(obj){
                if (JSON.stringify(UPLOAD_FILES) =="{}"){
                    layer.alert("请选择文件");
                    return false;
                }
                popIndex = layer.load("数据上传中：");
            }, choose: function (obj) {
                //选择文件后触发
                UPLOAD_FILES = obj.pushFile();
                clearFile(UPLOAD_FILES);
                obj.preview(function(index, file, result){
                    $("#selectedFileName").val(file.name);
                    obj.pushFile(UPLOAD_FILES);
                });
            }
            ,done: function(importData){
                if(typeof callBack =='function'){
                    callBack(importData,popIndex);
                }
                clearFile(UPLOAD_FILES);
                $("#selectedFileName").val("");
            }
            ,error: function(){
                layer.close(popIndex);
                layer.alert("文件导入异常！");
            }
        });
    }else{
        if(linkcs!=undefined&&fileNameTemp!=undefined)
        {
            $("#modelDownload").attr("href",origin+(cp.indexOf("/")==0?"":"/")+cp+'/biz/sb/'+linkcs)
                .attr("download",fileNameTemp).show();
        }
    }
    // if(linkcs!=undefined&&fileNameTemp!=undefined)
    // {
    //     $("#modelDownload").attr("href",location.origin+(cp.indexOf("/")==0?"":"/")+cp+'/biz/sb/'+linkcs)
    //         .attr("download",fileNameTemp).show();
    // }
    layer.open({
        type: 1
        ,area: ['600px']
        ,title:['Excel数据导入']
        ,scrollbar: false
        ,content: $("#pop-09")
        ,btn:[]
        ,success:function(){
            //$("#selectedFileName").val("");
            $("#pop-09").css("height","150px");
        },cancel:function(){
            $("#pop-09").css("height","190px");
        },yes:function(){
            //$("#pop-09").css("height","120px");
            return false;
        },btn2:function(){
            if($("#selectedFileName").val() == "" || $("#selectedFileName").val() == "请选择文件！"){
                $("#selectedFileName").val("请选择文件！");
                return false;
            }
            $("#pop-09").css("height","190px");
        },btn3:function(){
            $("#pop-09").css("height","190px");
        }
    });
}


//清空文件队列
function clearFile(UPLOAD_FILES) {
    for (var x in UPLOAD_FILES) {
        delete UPLOAD_FILES[x];
    }
}



var pop09CallBack = function(importData,index){
    if (importData!=null && importData!="" && typeof importData == "string") {
        importData = JSON.parse(importData);
    }

    if(importData.rtnCode == "999"){
        layer.alert("文件导入失败！"+importData.errInfo.msg);
    }else if (importData.rtnCode == "000") {
        var _wbcsh_in_formData = JSON.parse(importData.body);
        //var _wbcsh_in_dom = $("#wbcsh").val();
        // console.log("合并前_wbcsh_in_formData:");
        // console.dir(_wbcsh_in_formData);
        //if (_wbcsh_in_dom == "" || _wbcsh_in_dom == "null") {

        //} else {
            /* 合并初始化数据 */
            //_wbcsh_in_dom = JSON.parse(Base64.decode(_wbcsh_in_dom));
            //_wbcsh_in_formData = $.extend(true, _wbcsh_in_dom, _wbcsh_in_formData);
        //}
        //formulaEngine.otherParams['wbcsh'] = Base64.encode(JSON.stringify(_wbcsh_in_formData));
        //$("#wbcsh").val(Base64.encode(JSON.stringify(_wbcsh_in_formData)));
        //formData.wbcsh={};
        // console.log("合并后_wbcsh_in_formData:");
        // console.dir(_wbcsh_in_formData);
        // delete formData.wbcshInit;
        var $viewAppElement = $("#frmSheet").contents().find("#viewCtrlId");
        var viewEngine = $("#frmSheet")[0].contentWindow.viewEngine;
        var body = $("#frmSheet")[0].contentWindow.document.body;
        formulaEngine.executeWbcshFormulaByParam(Base64.encode(JSON.stringify(_wbcsh_in_formData)));
        formulaEngine.applyAssociatedFormulaVerify(null);
        viewEngine.formApply($viewAppElement);
        viewEngine.tipsForVerify(body);
    }else{

    }
    layer.close(popIndex);
}

/**
 *  聚合预缴信息
 */
function mergeYjxx(sssqQ,sssqZ){
    var swjgDm5 = (formData.fq_.nsrjbxx.swjgDm).substring(0, 5);
    if(formData && formData.hq_ && formData.hq_.yjxxGrid
        && formData.hq_.yjxxGrid.yjxxGridlb
    && formData.hq_.yjxxGrid.yjxxGridlb.length >0) {
        var yjxx = formData.hq_.yjxxGrid.yjxxGridlb;
        var yjxxMap = {};
        for (var i in yjxx) {
            //增值税的yjye1预缴不需要合并， 附加税的yjye1有效期是所属期范围内的yjye1参与合并，厦门：增值税的yjye1、是有效期为所属期范围内的yjye1参与合并
            var key = yjxx[i].sksxDm + "_" + yjxx[i].skssqq + "_" + yjxx[i].skssqz + "_" + yjxx[i].zspmDm;
            //【主表28栏“分次预缴税额”】厦门个性化：<yjxxGrid>预缴税款数据取yjye1合计值，限制当前税款所属期，zsxm_dm=10101
            if(yjxx[i].zsxmDm == '10101' && swjgDm5 == '13502'){
                if (DATE_CHECK_TIME_SIZE(sssqQ,yjxx[i].skssqq)&&DATE_CHECK_TIME_SIZE(yjxx[i].skssqz,sssqZ)) {
                    if (yjxxMap[key]) {
                        yjxxMap[key].yjze = ROUND(yjxxMap[key].yjze + yjxx[i].yjze, 2);
                        if (yjxxMap[key].yjye1 != undefined && yjxx[i].yjye1 != undefined) {
                            yjxxMap[key].yjye1 = ROUND(yjxxMap[key].yjye1 + yjxx[i].yjye1, 2);
                        }
                        if (yjxxMap[key].yjye != undefined && yjxx[i].yjye != undefined) {
                            yjxxMap[key].yjye = ROUND(yjxxMap[key].yjye + yjxx[i].yjye, 2);
                        }
                    } else {
                        yjxxMap[key] = yjxx[i];
                    }
                }
            }
            //【主表28栏“分次预缴税额”】全国版：<yjxxGrid>预缴税款数据取yjye1合计值时 不限制属期，zsxm_dm=10101
            else if (yjxx[i].zsxmDm == '10101' && swjgDm5 != '13502'){
                if (yjxxMap[key]) {
                    yjxxMap[key].yjze = ROUND(yjxxMap[key].yjze + yjxx[i].yjze, 2);
                    if (yjxxMap[key].yjye1 != undefined && yjxx[i].yjye1 != undefined) {
                        yjxxMap[key].yjye1 = ROUND(yjxxMap[key].yjye1 + yjxx[i].yjye1, 2);
                    }
                    if (yjxxMap[key].yjye != undefined && yjxx[i].yjye != undefined) {
                        yjxxMap[key].yjye = ROUND(yjxxMap[key].yjye + yjxx[i].yjye, 2);
                    }
                } else {
                    yjxxMap[key] = yjxx[i];
                }
            }
            //【附表五 13栏“本期已缴费额”】 全国版：<yjxxGrid/>限制 对应的征收项目、当前税款所属期自动带入yjye1（多条的取合计） 限制当前税款所属期
            else if (yjxx[i].zsxmDm != '10101' ){
                if (DATE_CHECK_TIME_SIZE(sssqQ,yjxx[i].skssqq)&&DATE_CHECK_TIME_SIZE(yjxx[i].skssqz,sssqZ)) {
                    if (yjxxMap[key]) {
                        yjxxMap[key].yjze = ROUND(yjxxMap[key].yjze + yjxx[i].yjze, 2);
                        if (yjxxMap[key].yjye1 != undefined && yjxx[i].yjye1 != undefined) {
                            yjxxMap[key].yjye1 = ROUND(yjxxMap[key].yjye1 + yjxx[i].yjye1, 2);
                        }
                        if (yjxxMap[key].yjye != undefined && yjxx[i].yjye != undefined) {
                            yjxxMap[key].yjye = ROUND(yjxxMap[key].yjye + yjxx[i].yjye, 2);
                        }
                    } else {
                        yjxxMap[key] = yjxx[i];
                    }
                }
            }
        }
        var tempYjxx = [];
        for (var i in yjxxMap) {
            tempYjxx.push(yjxxMap[i]);
        }
        formData.hq_.yjxxGrid.yjxxGridlb = tempYjxx;
        formData.hq_.yjxxGrid.bz = "mergeYjxx";
    }
}

function sbzxBatchImport(){
    layer.open({
        type: 2
        ,area: ['600px','250px']
        ,title:['Excel数据导入']
        ,scrollbar: false
        ,content: (origin+(cp.indexOf("/")==0?"":"/")+cp+"/sbzxFileIO/fszl.do")
    });
}

var analysisJsonFunction = new Function("analysisJson","return sbzxBatchImportValidate(analysisJson);");
function sbzxBatchImportCallback(callbackData){
	var bodyJson = JSON.parse(callbackData.body);
	var targetName = bodyJson.targetName;
	var zlpzxxJson = JSON.parse(bodyJson.zlpzxx);
	var sbywbm = zlpzxxJson.ywbm;
	$.ajax({
        url: cp+"/sbzxFileIO/getAnalysisJson.do",
        data:JSON.stringify({
        	"sbywbm":sbywbm,
        	"targetName":targetName
        }),
        type: "POST",
        contentType: "application/json",
        dataType: "json",
        success: function (analysisJson) {
        	var isValid = analysisJsonFunction(analysisJson);
        	if(isValid||isValid===undefined){
        		formulaEngine.applyImportFormulas(true);
                var viewEngine = $("#frmSheet")[0].contentWindow.viewEngine;
                var viewCtrlId = $(window.frames["frmSheet"].document).find("#viewCtrlId");
                viewEngine.dynamicFormApply(viewCtrlId, formData, formEngine);
                viewEngine.formApply(viewCtrlId);
                viewEngine.tipsForVerify(document.body);
        	}
        },
        error:function(){
        	layer.alert("文件导入失败，请稍后再试！", {icon:0});
        }
    });
}

function sbzxDownloadDemo(version){
	return sbzxDownloadFile(version);
}

var demoParamsFunction = new Function("sbywbm", "ywlx", "version", "return getSbzxDemoParams(sbywbm, ywlx, version);");
function sbzxDownloadFile(version){
	version = version||"1";
	var sbywbm = parent.window.location.pathname.split('/')[parent.window.location.pathname.split('/').length-1];
	var ywlx = parent.window.location.pathname.split('/')[parent.window.location.pathname.split('/').length-2];
	if($("#formForDownload").length==0){
		var formForDownload = $('<form id="formForDownload" style="display: none" method="post"/>');
		formForDownload.attr("action", cp+"/sbzxFileIO/exportExcelFile.do");
		var sbywbmInput = $('<input name="sbywbm" id="formForDownloadSbywbm"/>');
		var ywlxInput = $('<input name="ywlx" id="formForDownloadYwlx"/>');
		var versionInput = $('<input name="version" id="formForDownloadVersion"/>');
		var paramsInput = $('<input name="params" id="formForDownloadParams"/>');
		formForDownload.append(sbywbmInput);
		formForDownload.append(ywlxInput);
		formForDownload.append(versionInput);
		formForDownload.append(paramsInput);
		$('body').append(formForDownload);
	}
	$("#formForDownloadSbywbm").val(sbywbm);
	$("#formForDownloadYwlx").val(ywlx);
	$("#formForDownloadVersion").val(version);
	var demoParams = {};
	try{
		demoParams = demoParamsFunction(sbywbm, ywlx, version)||{};
	}catch(e){
		//业务没有实现传递导出参数的方法，默认没有入参
		console.log(e);
	}
	$("#formForDownloadParams").val(JSON.stringify(demoParams));
	$("#formForDownload").submit();
}


/**
 * 要素申报显示全表和简表按钮事件
 * @type {boolean}
 */
var yssbHideHtmlBz = true;
function showFullHtml(){
    yssbHideHtmlBz = !yssbHideHtmlBz;
    if(yssbHideHtmlBz){
        parent.$("#btnShowFullHtml").text("全表模式");
    }else{
        parent.$("#btnShowFullHtml").text("简表模式");
    }
    window.frames["frmSheet"].htmlAfterRender4dzbdbm();
}


function sbzxFileUploadRz(title,fileType,checkFileName,ywbm,callback){
    if(title==null || title==undefined || title==''){
        title="文件上传";
    }
    layer.open({
        type: 2
        ,area: ['600px','250px']
        ,title:[title]
        ,scrollbar: false
        ,content: (location.origin+(cp.indexOf("/")==0?"":"/")+cp+"/fileuploadrz/uploadrz.do?fileType="+fileType+"&checkFileName="+checkFileName+"&ywbm="+ywbm+"&callback="+callback)
    });
}

var countxhcs=0;
function dealExcelData(fileName){
    var index=parent.layer.msg('加载数据中，请稍候...', {
        icon:20,
        shade: [0.8,'#fff'],
        skin: 'layui-layer-loadTxt',
        time:false
    });
    //循环次数
    var xhcs=10;
    var interval = setInterval(function() {
        if(countxhcs>xhcs){
            clearInterval(interval);
            countxhcs=0;
            parent.layer.close(index);
        }else {
            checkHaveData(fileName);
        }
    },1000);
}

function checkHaveData(fileName) {
    var xhcs=10;
    var params = {};
    params.fileName = fileName;
    params.sid = "dzswj.ywzz.dbSbzx.excel.checkExcelData";
    parent.parent.requestYwztData(params,function(result){
        if (result) {
            result=JSON.parse(result);
            var code=result.code;
            countxhcs++;
            if(code=="00"){
                countxhcs=xhcs+1;
                if(typeof getExcelData =='function'){
                    getExcelData(fileName);
                }
            }else if(countxhcs>xhcs){
                parent.layer.confirm('未获取excel数据，请重新导入！无法申报。点击“确定”按钮，将关闭当前页面。',{
                    title:'提示',
                    btn : ['确定']
                },showMessageAndCloseWindow,showMessageAndCloseWindow);
            }
        }
    });
}

/**
 * 计税依据对比
 * @param jsyj
 * @param ybzzs
 */
function compareJsyj(jsyj,ybzzs){
    var equalFlag = 0;
    for(var i=0;i< jsyj.length;i++){
        if(typeof ybzzs == 'number'){
                if(jsyj[i] == ybzzs){
                    equalFlag++;
                }
        }else{
            if(jsyj[i] == ybzzs[i]){
                equalFlag++;
            }
        }
    }
    if(typeof jsyj == 'number' && typeof ybzzs == 'number'){
        if(jsyj == ybzzs){
            equalFlag++;
        }
    }
    var len = 1;
    if(typeof jsyj.length != "undefined"){
        len = jsyj.length;
    }
    return equalFlag == len;

}

/**
 * 问卷调查弹出
 * @param callBeforSubmitForm
 * @param submitForm
 * @param isSecondCall
 */
function doBeforSubmitFormWjdcDeliver(callBeforSubmitForm,submitForm,isSecondCall){

    //先判断是否静默申报的问卷调查处理
    if(formEngine.isJmsb() && typeof isWjdcJmsb == "function" && isWjdcJmsb()){
        saveWjdcJmsb(callBeforSubmitForm,submitForm, isSecondCall);
    }else if(isWjdc()){
        /*弹框处理*/
        var layerx = layer;
        layerx.open({
            type: 2,
            id :'iFrameWjdc',
            area: '1010px',
            title: false,
            scrollbar: false,
            closeBtn: false,
            fixed: false,
            btn: ['确认提交','返回修改'],
            content: 'form/wjdc.html',
            yes: function (index, layero) {
                //有校验存在就阻断
                var frameWindow = layero.find("iframe")[0].contentWindow;
                if(frameWindow.$("input.yellow").length>0){
                    frameWindow.$("input.yellow").mouseover();
                    setTimeout(function(){
                        frameWindow.layer.closeAll('tips');
                    },5000);
                    return false;
                }
                //点击【确定】
                var frameBody = layerx.getChildFrame('body',index);
                var rawFormData = frameBody.contents().find("#formDataSpan").text();
                rawFormData = JSON.parse(rawFormData);
                var arr = rawFormData.arr;
                // 保存文件调查结果
                saveWjdc(arr,layerx,index,callBeforSubmitForm,submitForm,isSecondCall,otherParams.ywbm);
            },
            btn2:function(index,layerDom){
                layerx.close(index);
                prepareMakeFlag = true;
                $("body").unmask();
                if(typeof umMaskZdy == "function"){
                    umMaskZdy();
                }
            },
            zIndex: layerx.zIndex, //重点1
            success: function (layero,index) {
                layerx.setTop(layero); //重点2
                //找到当前弹出层的iframe元素
                var iframe = layero.find('iframe');
                //设定iframe的高度为当前iframe内body的高度
                var height = iframe[0].contentDocument.body.offsetHeight;
                height = MIN(380,height);
                iframe.css('height', height);
                //重新调整弹出层的位置，保证弹出层在当前屏幕的中间位置
                layero.css('top', (window.innerHeight - iframe[0].offsetHeight) / 2);

            }
        });
    }else{
        callBeforSubmitForm(submitForm, isSecondCall);
    }
}

/**
 * 是否弹出问卷调查
 * @returns {boolean}
 */
function isWjdc(){
    return false;
}

/**
 * 保存问卷调查
 * @param arr
 * @param layerx
 * @param index
 * @param callBeforSubmitForm
 * @param submitForm
 * @param isSecondCall
 */
function saveWjdc(arr,layerx,index,callBeforSubmitForm,submitForm,isSecondCall,ywbm){
    var zcsxqrjg = [];
    var unCheckedCnt = 0;
    for(var i = 0;i < arr.length;i++){
        if(arr[i].show == "Y"){
            if(!arr[i].sxqrjg){
                unCheckedCnt++;
            }
            var yhzc = {};
            yhzc.sxDm = arr[i].sxDm;
            yhzc.sxqrjg = arr[i].sxqrjg == "Y"?"":arr[i].sxqrjg;
            yhzc.bz = arr[i].bz?arr[i].bz:"";
            zcsxqrjg.push(yhzc);
        }
    }
    if(unCheckedCnt == 0){
        if(formData.kz_.wjdcjg){
            var lastWjdcjg = formData.kz_.wjdcjg;
            for(var i = 0;i < lastWjdcjg.length;i++) {
                zcsxqrjg.push(lastWjdcjg[i]);
            }
        }
        var reqVo = {};
        reqVo.ysqxxid = $("#ysqxxid").val();
        reqVo.zcsxqrjg = JSON.stringify(zcsxqrjg);
        reqVo.sssqQ = otherParams.sssqQ;
        reqVo.sssqZ = otherParams.sssqZ;
        reqVo.ywbm = ywbm?ywbm:otherParams.ywbm;//也可以写死
        reqVo.ywlx = "SB";
        reqVo.ywzt = "Y";
        reqVo.gzlxDm = otherParams.gzsb == "zx"?"5":"1";
        reqVo.sid = "dzswj.ywzz.sb.common.zctstxResolveCount";
        parent.requestYwztData(reqVo,function(_data) {
            try {
                if (typeof _data === "string") {
                    _data = JSON.parse(_data);
                }
                if("000" != _data.rtnCode){
                    layerx.alert(_data.rtnMsg, {
                        icon : 5,
                        zIndex: layerx.zIndex, //重点1
                        success:function(layero){
                            layerx.setTop(layero);
                        }
                    });
                }else{
                    formData.kz_.wjdcjg = zcsxqrjg;
                    layerx.close(index);
                    if(typeof callBeforSubmitForm == "function") {
                        callBeforSubmitForm(submitForm, isSecondCall);
                    }
                }
            }catch (e) {
                console.log(_data);
                layerx.alert("保存问卷失败!", {
                    icon : 5,
                    zIndex: layerx.zIndex, //重点1
                    success:function(layero){
                        layerx.setTop(layero);
                    }
                });
            }
        },function(_error){
            console.log(_error);
            layerx.alert("保存问卷失败!", {
                icon : 5,
                zIndex: layerx.zIndex, //重点1
                success:function(layero){
                    layerx.setTop(layero);
                }
            });
        });

    }else{
        layerx.alert("请勾选!", {
            icon : 5,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
    }
}


/**
 * 保存制造业小薇企业缓缴选择情况
 * @param arr
 * @param layerx
 * @param index
 * @param callBeforSubmitForm
 * @param submitForm
 * @param isSecondCall
 */
function saveZzyXwqyHj(data,layerx,index,isSecondCall,callBack){
    var zcsxqrjg = [];
    var unCheckedCnt = 0;
    if(isNull(data.lbDm)){
        layerx.alert("请选择制造业小微企业（含个体工商户）标准!", {
            title:'提示',
            icon:7,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
        return;
    }

    if(data.lbDm != '3' && isNull(data.xslx)){
        layerx.alert("请选择是否享受", {
            title:'提示',
            icon:7,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
        return;
    }
    if(data.xslx == 'N'){
        var reqVo = {};
        reqVo.ysqxxid = $("#ysqxxid").val();
        reqVo.zdlx = 'SB';
        reqVo.ywbm = ywbm?ywbm:otherParams.ywbm;
        reqVo.sssqQ = otherParams.sssqQ;
        reqVo.sssqZ = otherParams.sssqZ;
        $.ajax({
            type: "POST",
            url: cp+"/djhjlog/insertlog.do",
            async: false,
            data: reqVo,
            success: function (data) {
            }
        });
    }
    if(typeof saveZzyXwqyHjValidateDeliver == "function" && !saveZzyXwqyHjValidateDeliver(data,layerx)){
        //如果有个性化校验不通过,那就直接返回.弹框由个性化处理.
        return;
    }

    if(data.xslx == 'N' && isNull(data.bxsyy)){
        layerx.alert("请选择不享受原因!", {
            title:'提示',
            icon:7,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
        return;
    }

    if(data.xslx == 'N' && data.bxsyy == '3' && isNull(data.bxsyybz)){
        layerx.alert("请填写其他不享受原因!", {
            title:'提示',
            icon:7,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
        return;
    }

    if(data.xslx == 'N' && data.bxsyy == '3' && len(data.bxsyybz) > 150){
        layerx.alert("输入内容不能大于150个字符或者50个汉字！", {
            title:'提示',
            icon:7,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
        return;
    }


    var reqVo = {};
    reqVo.ysqxxid = $("#ysqxxid").val();
    reqVo.lbDm = data.lbDm;
    reqVo.hjbz = data.xslx;
    reqVo.yxshjzcLy = data.yxshjzcLy;
    reqVo.sssqQ = otherParams.sssqQ;
    reqVo.sssqZ = otherParams.sssqZ;
    reqVo.ywbm = ywbm?ywbm:otherParams.ywbm;//也可以写死
    reqVo.ywlx = "SB";
    reqVo.ywzt = "Y";
    reqVo.gzlxDm = otherParams.gzsb == "zx"?"5":"1";
    reqVo.sid = "dzswj.ywzz.sb.zzyxwqyhj.insertZzyxwqyhj";
    parent.requestYwztData(reqVo,function(_data) {
        try {
            if (typeof _data === "string") {
                _data = JSON.parse(_data);
            }
            if(_data.retcode != 'Y'){
                layerx.alert(_data.retxx, {
                    title:'提示',
                    icon:7,
                    zIndex: layerx.zIndex, //重点1
                    success:function(layero){
                        layerx.setTop(layero);
                    }
                });
            }else{
                layerx.close(index);
                //判断云上是否有个性化
                if(typeof saveZzyHjCDeliver == "function" && otherParams.hbsb != 'Z'){
                	reqVo.sid = "dzswj.ywzz.sb.zzyxwqyhj.insertYsdjZzyxwqyhj";
                	reqVo.uuid = _data.id;
                    if(typeof callBack == "function"){
                        saveZzyHjCDeliver(reqVo,callBack,isSecondCall);
                    }else{
                        saveZzyHjCDeliver(reqVo,ctipsOrinal,isSecondCall);
                    }
                }else{
                	 //保存成功后,继续之前的流程
                    if(typeof callBack == "function"){
                        callBack(isSecondCall);
                    }else{
                        ctipsOrinal(isSecondCall);
                    }

                }
            }
        }catch (e) {
            console.log(_data);
            layerx.alert("保存问卷失败!", {
                icon : 5,
                zIndex: layerx.zIndex, //重点1
                success:function(layero){
                    layerx.setTop(layero);
                }
            });
        }
    },function(_error){
        console.log(_error);
        layerx.alert("保存问卷失败!", {
            icon : 5,
            zIndex: layerx.zIndex, //重点1
            success:function(layero){
                layerx.setTop(layero);
            }
        });
    });
}


var zzsjmssbConfirmIndex = [];
/**
 * 减免表校验确认弹框
 * @param flag
 * @param tips
 * @param index
 * @param xzBz  确定是减税还是免税。如果有传入值且值为‘js’则为减税
 */
function zzsjmssbConfirm(flag,tips,index,xzBz) {

    var zzsMsxmGridlb = null;
    var zzsmmsmxbPrefix = '';
    if(otherParams.ywbm == 'XGMZZS'){
        zzsMsxmGridlb = formData.ht_.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbmsxmGrid.zzsjmssbmxbmsxmGridlbVO;
        zzsmmsmxbPrefix = "ht_.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbmsxmGrid.zzsjmssbmxbmsxmGridlbVO[";
        if(xzBz && xzBz=='js'){
            zzsMsxmGridlb = formData.ht_.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbjsxmGrid.zzsjmssbmxbjsxmGridlbVO;
            zzsmmsmxbPrefix = "ht_.zzssyyxgmnsrySbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbjsxmGrid.zzsjmssbmxbjsxmGridlbVO[";
        }
    }else{
        zzsMsxmGridlb = formData.ht_.zzsybsbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbmsxmGrid.zzsjmssbmxbmsxmGridlbVO;
        zzsmmsmxbPrefix = 'ht_.zzsybsbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbmsxmGrid.zzsjmssbmxbmsxmGridlbVO[';
        if(xzBz && xzBz=='js'){
            zzsMsxmGridlb = formData.ht_.zzsybsbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbjsxmGrid.zzsjmssbmxbjsxmGridlbVO;
            zzsmmsmxbPrefix = 'ht_.zzsybsbSbbdxxVO.zzsjmssbmxb.zzsjmssbmxbjsxmGrid.zzsjmssbmxbjsxmGridlbVO[';
        }
    }
    if (flag && zzsjmssbConfirmIndex[index] == null) {
        zzsjmssbConfirmIndex[index] = layer.open({
            type: 1,
            btn: ['确定', '取消'],
            title: "提示",
            content: tips,
            yes: function () {
                layer.close(zzsjmssbConfirmIndex[index]);
                zzsjmssbConfirmIndex[index] = null;
            },
            cancel:function(){
                layer.close(zzsjmssbConfirmIndex[index]);
                zzsjmssbConfirmIndex[index] = null;
            },
            btn2: function () {
                layer.close(zzsjmssbConfirmIndex[index]);
                //清空此项减免性质
                zzsMsxmGridlb[index].t_hmc1 = "";
                zzsMsxmGridlb[index].hmc = "";
                zzsMsxmGridlb[index].swsxDm = "";
                formulaEngine.apply(zzsmmsmxbPrefix + index + '].t_hmc1', zzsMsxmGridlb[index].t_hmc1);
                // 3、刷新校验结果和控制结果
                var $viewAppElement = $("#frmSheet").contents().find("#viewCtrlId");
                var viewEngine = $("#frmSheet")[0].contentWindow.viewEngine;
                var body = $("#frmSheet")[0].contentWindow.document.body;
                viewEngine.formApply($viewAppElement);
                viewEngine.tipsForVerify(body);
                zzsjmssbConfirmIndex[index] = null;
            }
        });
    }
    return true;
}

/**
 * 制造业强制享受缓缴标志,目前仅江西提出需要强制缓缴.若存在其他区域,则新增报文节点读取配置中心
 * @returns {string}
 */
function getZzyZxwqyHjQzxsBz(){
    return isMrxsHjbz();
}

/**
 * 制造业缓缴强制享受不弹框标志
 */
function zzyXwqyHjQzxsNoAlertBz(){
    return 'N';
}

/**
 * 合并申报提交前校验
 * @param layer
 * @param callback
 */
function subSbVerify(layer,callbackSucc,callbackFail){
    if(typeof subSbVerify4Ywbm == "function"){
        setTimeout(function(){
            subSbVerify4Ywbm(subSbVerifyCommonZzyHj,layer,callbackSucc,callbackFail,otherParams);
        },50);
        return {"code":"001"};
    }else{
        return subSbVerifyCommonZzyHj(layer,callbackSucc,callbackFail,otherParams,true);
    }
}

/**
 * 合并申报提交前校验:缓缴部分
 * @param layer
 * @param callback
 * @param isImmediately 是否非延时调用(setTimeout)
 */
function subSbVerifyCommonZzyHj(layer,callbackSucc,callbackFail,otherParams,isImmediately){
    var ret = {
        "code":"",
        "msg":""
    };
    if(isZzyXwqyHj()){
        ret.code = "001";
        isImmediately = false;
        setTimeout(function () {
            hbsbHjAlert(layer,callbackSucc,callbackFail,otherParams);
        },50);
    }else{
        ret.code = "000";
        if(!isImmediately){
            //即时调用时无需额外调用. return即可. setTimeout需要执行回调.
            callbackSucc(otherParams.ywbm);
        }
    }
    return ret;
}

/**
 * 合并申报缓缴弹框.避免多个弹框一起扎堆弹导致弹不出来. 设定一个锁机制.
 * 当一个弹框准备弹的时候,其他弹框进入延时处理.
 * @param layer
 * @param callbackSucc
 * @param callbackFail
 * @param otherParams
 */
function hbsbHjAlert(layer,callbackSucc,callbackFail,otherParams){
    if(parent.parent.alertBz == 'Y'){
        setTimeout(function () {
            hbsbHjAlert(layer,callbackSucc,callbackFail,otherParams);
        },250);
        return;
    }else if (parent.parent.alertBz == 'pause'){
        //返回申报表,中止提交
        return;
    }
    parent.parent.alertBz = 'Y';
    //调接口弹框
    var reqParams = {
        "sid":"dzswj.ywzz.sb.zzyxwqyhj.cxZzyxwqyhj",
        "async":false
    };
    parent.requestYwztData(reqParams, function (data) {
        if (data && data.zzyxwqyhj) {
            formData.kz_.zzyXwqyHj = data.zzyxwqyhj;
        } else {
            formData.kz_.zzyXwqyHj = {
                "scsbbz": "Y",
                "lbDm": "",
                "xslx": "",
                "bxsyy": "",
                "bxsyybz": ""
            }
        }
        if (data && data.ss_) {
            if (!formData.ss_) {
                formData.ss_ = {};
            }
            formData.ss_.zzyxwqyhj = data.ss_.zzyxwqyhj;
        }

        var layerx = layer;
        if (zzyXwqyHjQzxsNoAlertBz() == 'Y' && (formData.kz_.zzyXwqyHj.lbDm == '000000000051' ||
            formData.kz_.zzyXwqyHj.lbDm == '000000000052')) {
            var zzyXwqyHjData = {};
            zzyXwqyHjData['lbDm'] = formData.kz_.zzyXwqyHj.lbDm;
            zzyXwqyHjData['xslx'] = 'Y';
            zzyXwqyHjData['yxshjzcLy'] = '';
            zzyXwqyHjData['bxsyy'] = '';
            saveZzyXwqyHj(zzyXwqyHjData, layerx, 0, otherParams.ywbm, callbackSucc);
        } else
        //首次申报或制造业中小微企业才进行弹框处理  000000000051
        if (formData.kz_.zzyXwqyHj.scsbbz == 'Y' || formData.kz_.zzyXwqyHj.lbDm == '000000000051' ||
            formData.kz_.zzyXwqyHj.lbDm == '000000000052') {
            var qddm = "";
            if (parent.queryString && parent.queryString.indexOf("qddm") > -1) {
                qddm = parent.queryString2Obj().qddm;
            }

            var link = cp+'/biz/sb/'+(otherParams.ywbm).toLowerCase()+'/form/zzyXwqyHj.html?qddm=' + qddm
                +'&scsbbz='+formData.kz_.zzyXwqyHj.scsbbz
                +'&lbDm='+formData.kz_.zzyXwqyHj.lbDm
                +'&qzxsBz='+getZzyZxwqyHjQzxsBz()
                +'&xslx='+formData.kz_.zzyXwqyHj.hjbz
                +'&mrxslx='+formData.kz_.zzyXwqyHj.hjbz
                +'&ywbm='+otherParams.ywbm
                +'&ywmc='+encodeURIComponent(parent.$("#sbbName").text());
            layerx.open({
                type: 2,
                id: 'iFrameZzyXwqyHj'+otherParams.ywbm,
                area: '1010px',
                title: false,
                scrollbar: false,
                closeBtn: false,
                fixed: false,
                btn: ['确认提交', '返回修改'],
                content: link,
                yes: function (index, layero) {
                    //有校验存在就阻断
                    var frameWindow = layero.find("iframe")[0].contentWindow;
                    if (frameWindow.$("input.yellow").length > 0) {
                        frameWindow.$("input.yellow").mouseover();
                        setTimeout(function () {
                            frameWindow.layer.closeAll('tips');
                        }, 5000);
                        return false;
                    }
                    //点击【确定】
                    var frameBody = layerx.getChildFrame('body', index);
                    var rawFormData = frameBody.contents().find("#formDataSpan").text();
                    rawFormData = JSON.parse(rawFormData);
                    var data = rawFormData.data;
                    // 保存文件调查结果
                    saveZzyXwqyHj(data, layerx, index, otherParams.ywbm, callbackSucc);
                },
                btn2: function (index, layerDom) {
                    layerx.close(index);
                    prepareMakeFlag = true;
                    parent.parent.ngScope.prepareMakeFlag = true;
                    $("body").unmask();
                    if (typeof umMaskZdy == "function") {
                        umMaskZdy();
                    }
                    parent.parent.alertBz = 'pause';
                },
                zIndex: layerx.zIndex, //重点1
                success: function (layero, index) {
                    layerx.setTop(layero); //重点2
                    //找到当前弹出层的iframe元素
                    var iframe = layero.find('iframe');
                    //设定iframe的高度为当前iframe内body的高度
                    var height = iframe[0].contentDocument.body.offsetHeight;
                    height = MIN(380, height);
                    iframe.css('height', height);
                    //重新调整弹出层的位置，保证弹出层在当前屏幕的中间位置
                    layero.css('top', (parent.parent.window.innerHeight - iframe[0].offsetHeight) / 2);

                }
            });
        } else {
            callbackSucc(otherParams.ywbm);
        }
    }, function (data) {
        //接口调用失败.
        $("body").unmask();
        if (typeof umMaskZdy == "function") {
            umMaskZdy();
        }
        parent.layer.alert('调用服务失败!', {title: "填表页调用服务", icon: 5});
        prepareMakeFlag = true;
        callbackFail(otherParams.ywbm,"调用制造业缓缴情况查询服务失败!");
        return;
    });
}

/**
 * 新版需求,返回普惠减免性质代码
 * @param zsxmDm
 * @param sssqQ
 * @param sssqZ
 * @param sfsyphjmzc
 * @param jzzcsyztDm 11:小规模 22:一般纳税人个体工商户 21:一般纳税人小型微利企业
 * @returns {string}
 */
function getPhjmxzDm2022(zsxmDm,sssqQ,sssqZ,sfsyphjmzc,jzzcsyztDm) {
    if(sfsyphjmzc != "Y"){
        return "";
    }
    var key = zsxmDm + "_";// + nd + "_" + ybrBz + "_" + gthBz + "_" + xwqyBz;
    if(sssqQ >= '2022-01-01'){
        key += "2022_";
    }
    //兼容旧属期版本
    else if (DATE_CHECK_TIME_SIZE("2019-01-01",sssqQ) && DATE_CHECK_TIME_SIZE(sssqZ,"2021-12-31")) {
    	switch (zsxmDm) {
			case "10109":return "0007049901";
			case "30203":return "0061049901";
			case "30216":return "0099049901";
			default:
				return "";
		}
    }
    key += jzzcsyztDm;
    var phjmxzDm = "";
    switch (key){
        case "10109_11":
        case "10109_2022_11":
            phjmxzDm = "0007049901";
            break;
        case "30203_11":
        case "30203_2022_11":
            phjmxzDm = "0061049901";
            break;
        case "30216_11":
        case "30216_2022_11":
            phjmxzDm = "0099049901";
            break;
        case "10109_2022_22":
            phjmxzDm = "0007049902";
            break;
        case "30203_2022_22":
            phjmxzDm = "0061049902";
            break;
        case "30216_2022_22":
            phjmxzDm = "0099049902";
            break;
        case "10109_2022_21":
            phjmxzDm = "0007049903";
            break;
        case "30203_2022_21":
            phjmxzDm = "0061049903";
            break;
        case "30216_2022_21":
            phjmxzDm = "0099049903";
            break;
        default:
            break;
    }
    return phjmxzDm;
}

/**
 * 新版需求,返回普惠减免税务事项代码
 * @param zsxmDm
 * @param sssqQ
 * @param sssqZ
 * @param sfsyphjmzc
 * @param jzzcsyztDm 11:小规模 22:一般纳税人个体工商户 21:一般纳税人小型微利企业
 * @returns {string}
 */
function getPhjmswsxDm2022(zsxmDm,sssqQ,sssqZ,sfsyphjmzc,jzzcsyztDm) {
    if(sfsyphjmzc != "Y"){
        return "";
    }
    var key = zsxmDm + "_";// + nd + "_" + ybrBz + "_" + gthBz + "_" + xwqyBz;
    if(sssqQ >= '2022-01-01'){
        key += "2022_";
    }
    //兼容旧属期版本
    else if (DATE_CHECK_TIME_SIZE("2019-01-01",sssqQ) && DATE_CHECK_TIME_SIZE(sssqZ,"2021-12-31")) {
    	switch (zsxmDm) {
		case "10109":return "SXA031900988";
		case "30203":return "SXA031900993";
		case "30216":return "SXA031900994";
		default:
			return "";
    	}
    }
    key += jzzcsyztDm;
    var phjmswsxDm = "";
    switch (key){
        case "10109_11":
        case "10109_2022_11":
            phjmswsxDm = "SXA031900988";
            break;
        case "30203_11":
        case "30203_2022_11":
            phjmswsxDm = "SXA031900993";
            break;
        case "30216_11":
        case "30216_2022_11":
            phjmswsxDm = "SXA031900994";
            break;
        case "10109_2022_22":
            phjmswsxDm = "SXA031901266";
            break;
        case "30203_2022_22":
            phjmswsxDm = "SXA031901272";
            break;
        case "30216_2022_22":
            phjmswsxDm = "SXA031901274";
            break;
        case "10109_2022_21":
            phjmswsxDm = "SXA031901267";
            break;
        case "30203_2022_21":
            phjmswsxDm = "SXA031901273";
            break;
        case "30216_2022_21":
            phjmswsxDm = "SXA031901275";
            break;
        default:
            break;
    }
    return phjmswsxDm;
}

var phjmxzDmb = {
    "0007049901": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0007049902": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0007049903": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0061049901": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0061049902": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0061049903": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0099049901": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0099049902": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号",
    "0099049903": "《财政部 税务总局关于进一步实施小微企业“六税两费”减免政策的公告》财政部 税务总局公告2022年第10号"
};
/**
 * 新版需求,返回普惠减免性质名称 phjmxzDm|phjmxzMc
 * @param phjmswsxDm
 * @returns {string}
 */
function getPhjmxzMc2022(phjmxzDm,sssqQ,sssqZ) {
	if (!isNull(sssqQ) && !isNull(sssqZ) && DATE_CHECK_TIME_SIZE("2019-01-01",sssqQ) && DATE_CHECK_TIME_SIZE(sssqZ,"2021-12-31")) {
		switch (phjmxzDm) {
		case "0007049901":return phjmxzDm+"|《财政部 税务总局关于实施小微企业普惠性税收减免政策的通知》 财税〔2019〕13号";
		case "0061049901":return phjmxzDm+"|《财政部 税务总局关于实施小微企业普惠性税收减免政策的通知》 财税〔2019〕13号";
		case "0099049901":return phjmxzDm+"|《财政部 税务总局关于实施小微企业普惠性税收减免政策的通知》 财税〔2019〕13号";
		default:
			return "";
		}
	}
    return phjmxzDmb[phjmxzDm]? (phjmxzDm + '|'+ phjmxzDmb[phjmxzDm]):"";
}

var phjmswsxDmb = {
    "SXA031900988": "增值税小规模纳税人城市维护建设税减征",
    "SXA031901266": "个体工商户城市维护建设税减征",
    "SXA031901267": "小型微利企业城市维护建设税减征",
    "SXA031900993": "增值税小规模纳税人教育费附加减征",
    "SXA031901272": "个体工商户教育费附加减征",
    "SXA031901273": "小型微利企业教育费附加减征",
    "SXA031900994": "增值税小规模纳税人地方教育附加减征",
    "SXA031901274": "个体工商户地方教育费附加减征",
    "SXA031901275": "小型微利企业地方教育费附加减征"
};
/**
 * 新版需求,返回普惠减免税务事项名称
 * @param phjmswsxDm
 * @returns {string}
 */
function getPhjmswsxMc2022(phjmswsxDm) {
    return phjmswsxDmb[phjmswsxDm]?phjmswsxDmb[phjmswsxDm]:"";
}

/**
 * 设置cookie
 * */
var cookie = {
    set: function (key, val, time, tbrq,subKey) {//设置cookie方法
        tbrq = parseDate(tbrq);//填报日期为当前时间，因为js的new Date()为电脑的时间
        var date = new Date(tbrq); //获取当前时间
        var expiresDays = time;  //将date设置为n天以后的时间
        date.setTime(date.getTime() + expiresDays * 24 * 3600 * 1000); //格式化为cookie识别的时间
        if(subKey){
            var oldVal = cookie.get(key);
            if((oldVal + "").length>500){
                val = subKey;
            }else{
                val = oldVal + "," + subKey;
            }
        }
        document.cookie = key + "=" + val + ";expires=" + date.toGMTString();  //设置cookie
    },
    get: function (key,subKey) {//获取cookie方法
        /*获取cookie参数*/
        var getCookie = document.cookie.replace(/[ ]/g, "");  //获取cookie，并且将获得的cookie格式化，去掉空格字符
        var arrCookie = getCookie.split(";");  //将获得的cookie以"分号"为标识 将cookie保存到arrCookie的数组中
        var tips = "";  //声明变量tips
        for (var i = 0; i < arrCookie.length; i++) {   //使用for循环查找cookie中的tips变量
            var arr = arrCookie[i].split("=");   //将单条cookie用"等号"为标识，将单条cookie保存为arr数组
            if(subKey && key == arr[0] && arr[1].indexOf(subKey)>-1){
                tips = "Y";
                break;
            }else if (key == arr[0]) {  //匹配变量名称，其中arr[0]是指的cookie名称，如果该条变量为tips则执行判断语句中的赋值操作
                tips = arr[1];   //将cookie的值赋给变量tips
                break;   //终止for循环遍历
            }
        }
        return tips;
    },
    del: function (key) { //删除cookie方法
        var date = new Date(); //获取当前时间
        date.setTime(date.getTime() - 10000); //将date设置为过去的时间
        document.cookie = key + "=v; expires =" + date.toGMTString();//设置cookie
    }
};

var ywbm6S2F = ["XGMZZS","YBNSRZZS","XFSSB","ZZSYJSB","FCJYSCLFSB",
    "WTDZSB","WTDZHZSB","TYDKDJSB","DQDEZXSB","CXSTYSBB"];

var tips6s2fMsgBz = false;
/**
 * 六税两费公用弹框
 * @param ywbm
 * @param sssqQ
 * @param sssqZ
 */
function tips6s2fMsg(ywbm,sssqQ,sssqZ) {
    return;
    /*if(formEngine.isJmsb()){
        return;
    }
    if(tips6s2fMsgBz){
        return;
    }
    tips6s2fMsgBz = true;
    if(ywbm6S2F.indexOf(ywbm) == -1){
        return;
    }
    var tbrq = formData.fq_.sbrq;
    if(!tbrq){
        tbrq = formData.fq_.nsrjbxx.tbrq;
    }
    if(tbrq < '2021-12-31' || tbrq > '2022-03-31'){
        return;
    }
    /!*if(sssqZ < '2022-01-01' || sssqQ > '2022-03-31'){
        return;
    }*!/
    var xgmBz = "N";
    if(ywbm == "WTDZSB" || ywbm == "WTDZHZSB" || ywbm == "TYDKDJSB" || ywbm == "DQDEZXSB"){
        xgmBz = "Y";
    }else if(ywbm == "YBNSRZZS" || ywbm == "XGMZZS" || ywbm == "ZZSYJSB"){
        xgmBz = "N";
    }else if(typeof isXgmNsr == "function"){
        xgmBz = isXgmNsr();
    }else if(ywbm == "FCJYSCLFSB" && formData && formData.fq_ && formData.fq_.nsrxx
        && formData.fq_.nsrxx.jzxx && formData.fq_.nsrxx.jzxx.xgmzg) {
        //判断是否小规模 01:小规模 04:一般纳税人转小规模
        if (formData.fq_.nsrxx.jzxx.xgmzg.jzznsrzglx == '01'
            || formData.fq_.nsrxx.jzxx.xgmzg.jzznsrzglx == '04') {
            xgmBz = "Y";
        }
    }else if(formData && formData.fq_ && formData.fq_.jzxx && formData.fq_.jzxx.xgmzg){
        if(formData.fq_.jzxx.xgmzg.jzznsrzglx == '01' || formData.fq_.jzxx.xgmzg.jzznsrzglx == '04') {
            xgmBz = "Y";
        }
    }else if(ywbm == "CXSTYSBB"){
        //财行税不存在jzxx节点时,为自然人进入.
        xgmBz = "Y";
    }
    if(xgmBz == 'N'){
        return;
    }

    var djxh = "";
    if(formData.fq_.nsrjbxx) {
        djxh = formData.fq_.nsrjbxx.djxh;
    }else if (formData.fq_.nsrxx){
        djxh = formData.fq_.nsrxx.djxh;
    }

    if(djxh == ""){
        return;
    }
    var key = "tips6s2fMsg";

    var value = cookie.get(key,djxh+'_'+ywbm);
    if(value == "Y"){
        return;
    }
    var content = '<div class="layui-text">尊敬的纳税人、缴费人，为进一步巩固拓展减税降费成效，自2022年1月1日起，暂继续执行小规模纳税人减征“六税两费”优惠政策，您可暂按原政策文件规定申报享受优惠（原减免性质代码可继续使用）。待正式文件出台后,以正式文件为准。需了解有关情况可咨询主管税务机关或12366纳税缴费服务热线。感谢您的理解与支持！</div>';
    parent.layer.open({
        type: 1,
        id:"tips6s2fMsgAlert",
        area: ['700px'],
        title: "提示",
        btn:["我已知晓"],
        closeBtn:false,
        content: content,
        yes: function (index) {
            cookie.set(key, "Y", 1, tbrq,djxh+'_'+ywbm);
            parent.layer.close(index);
        }
    });*/
}

/**
 * 六税两费公用弹框
 * @param ywbm
 * @param sssqQ
 * @param sssqZ
 */
function tipsJsjfMsg(ywbm,sssqQ,sssqZ) {
    return;
    /*if(formEngine.isJmsb()){
        return;
    }
    var tbrq = formData.fq_.sbrq;
    if(!tbrq){
        tbrq = formData.fq_.nsrjbxx.tbrq;
    }
    if(tbrq < '2021-12-31' || tbrq > '2022-03-31'){
        return;
    }
    /!*if(sssqZ < '2022-01-01' || sssqQ > '2022-03-31'){
     return;
     }*!/
    var xgmBz = true;
    if(ywbm == "ZZSYJSB" || ywbm=='zzsyjsb'){
        xgmBz = jsjf_isXgm();
    }
    if(!xgmBz){
        return;
    }
    var djxh = formData.fq_.nsrjbxx.djxh;
    var key = "tipsJsjfMsg";

    var value = cookie.get(key,djxh+'_'+ywbm);
    if(value == "Y"){
        return;
    }
    var content = '<div class="layui-text">尊敬的纳税人、缴费人，为进一步巩固拓展减税降费成效，自2022年1月1日起，暂继续执行增值税小规模纳税人征收率由3%减按1%征收增值税优惠政策、小规模纳税人减征“六税两费”优惠政策。您可暂按原政策文件规定申报享受优惠（原减免性质代码继续使用）。待正式文件出台后,以正式文件为准。需了解有关情况可咨询主管税务机关或12366纳税缴费服务热线。感谢您的理解与支持！</div>';
    parent.layer.open({
        type: 1,
        id:"tipsJsjfMsgAlert",
        area: ['700px'],
        title: "提示",
        btn:["我已知晓"],
        closeBtn:false,
        content: content,
        yes: function (index) {
            cookie.set(key, "Y", 1, tbrq,djxh+'_'+ywbm);
            parent.layer.close(index);
        }
    });*/
}

if(parent.queryString && parent.queryString.indexOf("hbsb=Z")>-1) {
    $(window.document).resize(function(){
        resizeLayer();
    });
}

/**
 * 处理因合并申报因begin.parent iframe高度为零时,layer弹框异常.
 * 切换到对应附表后,对layer弹框进行处理
 */
function resizeLayer() {
    if(parent.queryString && parent.queryString.indexOf("hbsb=Z")>-1) {
        if($(window.document).height() == 0){
            setTimeout(resizeLayer,100);
            return;
        }
        //合并申报时,有两种情况layer弹框样式异常.
        var layerAlertElem = $(window.document).find("div.layui-layer.layui-layer-dialog,div.layui-layer.layui-layer-page");
        if (layerAlertElem.length > 0 && layerAlertElem.css("display") === "block") {
            layerAlertElem.each(function(){
                if($(this).css("display") != "block"){
                    //continue
                    return true;
                }
                if ($(this).offset().top <= $(window.document).height()*0.14){
                    $(this).css("top","15%");
                }
                if($(this).offset().left <= $(window.document).width()*0.14) {
                    $(this).css("left","15%");
                    setTimeout(resizeLayer,100);
                    return false;
                }
                var layerTitle = $(this).find(".layui-layer-title");
                var layerContent = $(this).find(".layui-layer-content");
                var layerBtn = $(this).find(".layui-layer-btn");
                var layerResize = $(this).find(".layui-layer-resize");
                var contentHeight = layerContent.eq(0).height();
                var contentDiv = layerContent.children("div");
                //1.弹框内容非常宽.
                if (layerTitle.length > 0 && layerContent.length > 0 && contentHeight == 0) {
                    var paddingTop = layerContent.eq(0).css("padding-top");
                    var paddingBom = layerContent.eq(0).css("padding-bottom");
                    if(paddingTop != ''){
                        paddingTop = paddingTop.replace("px","") * 1;
                    }else{
                        paddingTop = 30;
                    }
                    if(paddingBom != ''){
                        paddingBom = paddingBom.replace("px","") * 1;
                    }else{
                        paddingBom = 30;
                    }
                    if(layerResize.length > 0){
                        layerContent.eq(0).height(this.offsetHeight - layerTitle[0].offsetHeight - layerBtn[0].offsetHeight - layerResize[0].offsetHeight - paddingTop - paddingBom);
                    }else{
                        layerContent.eq(0).height(this.offsetHeight - layerTitle[0].offsetHeight - layerBtn[0].offsetHeight- paddingTop - paddingBom);
                    }

                }else if(contentDiv.length > 0 && contentHeight - contentDiv.height() > 50){
                    var offsetheight = 0;
                    contentDiv.each(function(){
                        offsetheight += this.offsetHeight;
                    });
                    //2.弹框内容非常高.把按钮顶出去了.
                    layerContent.eq(0).height(offsetheight);
                }else if(contentDiv.length > 0 && contentHeight < contentDiv.height() && contentHeight < 300){
                    layerContent.eq(0).height(contentDiv.height());
                }
            });
        }
    }
}

/**
 * 增值税减免性质中间节点
 * 用于兼容pdf减免性质带出
 * （由于存在新增减免性质时以往都需要在base包下的文件补充对应的减免）
 * @param key 可以从减免性质码表得到对应名称
 * @param bz  表示获得那个原有节点的中间节点（jmxz——ssjmxzdm,swsx——swsxdm）
 *@param jmlx 增值税分减税，免税两部分（JS——减税，MS——免税）
 */
function setInterMediateNode(key,bz,jmlx){
    var jmxzCT;
    var mc='';
    var isJsonCode=false;
    var swjgDm5 = (formData.fq_.nsrjbxx.swjgDm).substring(0, 5);
    var swjgDm3 = (formData.fq_.nsrjbxx.swjgDm).substring(0, 3);
    //一般人： 厦门、陕西、山东、青海、江西是静态码表
    if (swjgDm5 == '13502' || swjgDm3 == '161' || (swjgDm3 == '137'&&swjgDm5!='13702') || swjgDm3 == '163' || swjgDm3 == '136') {
        isJsonCode=true;
    }
    //业务实现
    if(typeof getJmxzCode == "function"  ){
        //增值税一般人，小规模
        if(jmlx){
            jmxzCT=getJmxzCode(jmlx);
        }else{
            jmxzCT=getJmxzCode();
        }
    }

    //英文的<>需要替换下成中文＜＞,不然转xml时会存在问题
    if(jmxzCT && jmxzCT[key]) {
        if (!isJsonCode) {

            if (bz == 'jmxz') {
                mc = jmxzCT[key].ssjmxzmc ? jmxzCT[key].ssjmxzmc.replace(new RegExp("<","gm"),"＜").replace(new RegExp(">","gm"),"＞") : '';
            } else {
                mc = jmxzCT[key].swsxmc ? jmxzCT[key].swsxmc.replace(new RegExp("<","gm"),"＜").replace(new RegExp(">","gm"),"＞") : '';
            }
        }else{
            if(jmxzCT[key].msxzMc){
                var split = jmxzCT[key].msxzMc.split("|");
                if (bz == 'jmxz') {
                    mc = split[3] ? split[3].replace(new RegExp("<","gm"),"＜").replace(new RegExp(">","gm"),"＞") : '';
                } else {
                    mc = split[2] ? split[2].replace(new RegExp("<","gm"),"＜").replace(new RegExp(">","gm"),"＞") : '';
                }
            }
        }
    }
    return mc;
}

export default {
  getSaveExtParam,
  isGzTipsHj,
  isZzyXwqyHjQysdsNd,
  zzyHjQysds18ndMsg,
  isMrxsHjbz,
  isZzyXwqyHj,
  isZzyXwqyHjJmsb,
  getUrlParamSbzx,
  getYbnsrzzsData,
  getFsData,
  setNode,
  setNotNullNode,
  flzlStateContains,
  zzsXgyqJmTips,
  mergeYjxx,
  checkHaveData,
  compareJsyj,
  doBeforSubmitFormWjdcDeliver,
  isWjdc,
  saveWjdc,
  saveZzyXwqyHj,
  zzsjmssbConfirm,
  getZzyZxwqyHjQzxsBz,
  zzyXwqyHjQzxsNoAlertBz,
  getPhjmxzDm2022,
  getPhjmswsxDm2022,
  getPhjmxzMc2022,
  getPhjmswsxMc2022,
  tips6s2fMsg,
  tipsJsjfMsg,
  setInterMediateNode,
}
