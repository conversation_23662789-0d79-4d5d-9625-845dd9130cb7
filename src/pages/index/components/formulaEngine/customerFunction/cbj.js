/**
 * 进入表单初始化校验,
 * 1:核心期初数是否有申报数据;
 * 2:判断属期是否是在认定有效期内:sfCzSfzrd!=Y,则在当前税款属期内没有应申报信息;
 * 后期新增各种初始化校验,均在这里加.
 * note: 
 * fid: 0001
 */
function sbqCheck(sfCzSfzrd) {
	var msg;
	var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
    var djzclxMc = formData.fq_.nsrjbxx.djzclxMc;

    // 跳过校验配置 测试环境使用
    var tgjypz = formData.fq_.cjrjybzjInitData.tgjypz;
    var gzsbbz = formData.fq_.cjrjybzjInitData.gzsbbz;
	var nsrztDm = formData.fq_.nsrjbxx.nsrztDm;

	if(djzclxDm.substring(0,1) == "4"){
		msg = "您是个体工商户、基层群众自治组织和个人主体，不是残疾人就业保障金缴纳义务人，无需申报。";
	} else if(!isNotEmptyObject(formData.hq_) || !isNotEmptyObject(formData.hq_.sbxxVoList) || !isNotEmptyObject(formData.hq_.sbxxVoList.sbxxGridlb) || sfCzSfzrd != "Y"){
		msg = "尊敬的纳税人，您在当前税款属期内没有应申报信息，无法继续申报!";
	}

	if(msg !== undefined) {
		$vue.$gtDialog.confirm({
			header:'提示',
			theme: 'info',
			body: msg,
			closeOnOverlayClick: false,
			closeOnEscKeydown: false,
			onClosed: function() {
			closeWindow();
		}});
	} else {
		// 通过阻断性校验提示
		var qqdcx = getUrlParam("qqdcx");
		if(qqdcx === "Y"){
			return;
		}
		var bdspgz = formData.fq_.cjrjybzjInitData.bdspgz;
		var qqdcxbz = formData.fq_.qqdcxbz;
		if((!isNotEmptyObject(bdspgz)) && (qqdcxbz!="Y")){
			var year = (formData.fq_.sjHqRqq).split('-')[0];
			var msg = "尊敬的纳税人，" + year + "年社会平均工资未配置，将按照表单实际公式计算，本期应纳费额上不封顶，请确认是否继续申报？";
			$vue.$gtDialog.warning({
				header:'提示',
				body: msg,
				closeOnOverlayClick: false,
				closeOnEscKeydown: false,
				onConfirm: function() {
					tipsMsgBySnsjapcjrjyrs();
				}, onCancel: function() {
					closeWindow();
				}}, {type:'confirm'});
		} else {
			// 初始化弹窗太多 为了不影响原有逻辑 延迟10秒执行
			window.setTimeout(tipsMsgBySnsjapcjrjyrsFun, 100);
			function tipsMsgBySnsjapcjrjyrsFun(){
				tipsMsgBySnsjapcjrjyrs();
			}
		}
	}
}

/*
 * JSONE-23571
 *【1200.06金三版本】残保金上年实际安排残疾人就业人数接口需求调整
 */
function tipsMsgBySnsjapcjrjyrs(){
	var snsjapcjrjyrs_hq = formData.hq_.snsjapcjrjyrs;
	if(!isNotEmptyObject(snsjapcjrjyrs_hq)){
		var nd =  (formData.fq_.sjHqRqq).split('-')[0];

		$vue.$gtDialog.confirm({
			header:'温馨提示',
			theme: 'info',
			body: nd+"年度企业实际安排残疾人就业人数"+snsjapcjrjyrs_hq+"人，如有异议，请联系主管税务机关或当地残联核实。",
			closeOnOverlayClick: false,
			closeOnEscKeydown: false,
			onClosed: function() {
				tipsMsgBySnsjapcjrjyrs2();
			}});
	} else {
		tipsMsgBySnsjapcjrjyrs2();
	}
}

function tipsMsgBySnsjapcjrjyrs2(){
	var gzsbbz = formData.fq_.cjrjybzjInitData.gzsbbz;
	if(gzsbbz == "Y"){
		var snsjapcjrjyrs_hq = formData.hq_.snsjapcjrjyrs;
		var snsjapcjrjyrs_ym = formData.ht_.cjrvoList.cjrvoListlb[0].snsjapcjrjyrs;
		if(snsjapcjrjyrs_hq != snsjapcjrjyrs_ym){
			$vue.$gtDialog.confirm({
				header:'提示',
				theme: 'info',
				body: "尊敬的纳税人，“实际安排残疾人就业人数”与最新配置数据不符，残疾人就业人数应为：" + snsjapcjrjyrs_hq + "，是否重新计算？",
				closeOnOverlayClick: false,
				closeOnEscKeydown: false,
				confirm: function() {
					formData.ht_.cjrvoList.cjrvoListlb[0].snsjapcjrjyrs = snsjapcjrjyrs_hq;
					var ybtse_jpath = "ht_.cjrvoList.cjrvoListlb[0].snsjapcjrjyrs";
					formulaEngine.apply(ybtse_jpath,snsjapcjrjyrs_hq);
				}}, {type: 'confirm'});
		}
	}
}

/**
 * 
 * @param {*} snzzzggzze 
 * @param {*} nsqxDm
 * note: 
 * rule未使用
 */
function tipsMsgByNsqxDm(snzzzggzze,nsqxDm) {
	// var zgswjdm = formData.fq_.nsrjbxx.zgswjDm;
	// if (zgswjdm.substring(0,3) == "152" && nsqxDm != '10') {
	// 	layer.alert("你选择的是【按半年】申报残保金，请将【在职职工工资总额】自行除以2 后填写。", {
	// 		title: "提示",
	// 		icon: 7,
	// 		offset: 'auto'
	// 	});
	// }
}

/**
 * note:
 * fid: 06100122010100142
 */
function tipsMsgCjrsAndZgrsJk(zgryVO) {
	if (!isNull(zgryVO) && zgryVO.cjrs !=0 && zgryVO.zgrs != 0) {
		$("#frmSheet").contents().find("#snzzzggzzeId").attr("ng-show-tbsm", "[上年在职职工人数]、[上年实际安排残疾人就业人数]已根据核心征管接口自动带出，请您结合企业实际情况确认或修改！");
		$("#frmSheet").contents().find("#snzzzggzzeId").attr("title", "[上年在职职工人数]、[上年实际安排残疾人就业人数]已根据核心征管接口自动带出，请您结合企业实际情况确认或修改！");
	}
}

function tipsMsgBySnzzzgrs(snzzzgrs,cyrs,type) {
	var zgswjdm = formData.fq_.nsrjbxx.zgswjDm;
	var result = true;
	if ((zgswjdm.substring(0,3) == "137" || zgswjdm.substring(0,3) == "144") && cyrs != 0) {
		if(type == "result"){
			result = false;
			return result;
		} else if(type == "tip"){
			if(snzzzgrs != cyrs){
				// layer.alert("尊敬的纳税人，您上年企业所得税年度报告表中从业人数为"+cyrs+"，请确认后修改！", {
				// 	title: "提示",
				// 	icon: 7,
				// 	offset: 'auto'
				// });
			}
		}
	}
	return result;
}

function tipsMsgBySnzzzggzze(snzzzggzze,gzxjzcssje,type) {
	var zgswjdm = formData.fq_.nsrjbxx.zgswjDm;
	var result = true;
	if ((zgswjdm.substring(0,3) == "137" || zgswjdm.substring(0,3) == "144") && gzxjzcssje != 0) {
		if(type == "result"){
			result = false;
			return result;
		} else if(type == "tip"){
			if(snzzzggzze != gzxjzcssje){
				// layer.alert("尊敬的纳税人，您上年企业所得税年度报告表中工资薪金支出税收金额为"+gzxjzcssje+"，请确认后修改！", {
				// 	title: "提示",
				// 	icon: 7,
				// 	offset: 'auto'
				// });
			}
		}
	}
	return result;
}

/**
 * 残保金更正申报，初始化联系电话
 * note: 残保金个性化函数
 * fid: 06100122010100004
 */
function CBJ_INIT_LXDH() {
	let lxdh = formData.ht_.cjrvoList.cjrvoListlb[0].zcdlxdh;
	if (isNull(lxdh) && !isNull(formData.fq_.nsrjbxx.zcdlxdh)) {
		lxdh = formData.fq_.nsrjbxx.zcdlxdh;
	} else if (isNull(lxdh)) {
		lxdh = formData.fq_.nsrjbxx.fddbrgddh;
	}
	return lxdh;
}

/*
*禅道：47514(全国通用)
*费款征期：2018年4月1日后
*“取当地社会平均工资”时，需要取申报年度社会平均工资（月：除12乘2 | 季：除4乘2 | 半年：除2乘2 | 年：乘2）
*费款征期：2018年4月1日前
*“取当地社会平均工资”时，需要取申报年度社会平均工资（月：除12乘3 | 季：除4乘3 | 半年：除2乘3 | 年：乘3）
*/
function getSpgz(sbqx_, sbqx, nsqxDm, bdspgz){
	var spgz = 0;
	if(rqbj(sbqx_, sbqx)){
		// 2018年4月1日后
		if(nsqxDm == "06"){
			// 月
			spgz = (bdspgz / 12) * 2;
		}else if(nsqxDm == "08"){
			// 季
			spgz = (bdspgz / 4) * 2;
		}else if(nsqxDm == "09"){
			//半年
			spgz = bdspgz;
		}else if(nsqxDm == "10"){
			//年
			spgz = bdspgz * 2;
		}
	} else{
		// 2018年4月1日前
		if(nsqxDm == "06"){
			// 月
			spgz = (bdspgz / 12) * 3;
		}else if(nsqxDm == "08"){
			// 季
			spgz = (bdspgz / 4) * 3;
		}else if(nsqxDm == "09"){
			//半年
			spgz = (bdspgz / 2) * 3;
		}else if(nsqxDm == "10"){
			//年
			spgz = bdspgz * 3;
		}
	}
	return spgz;
}

/**
 * 计算实际年平均工资
 * JSONE-8913
 */
function sjNpjgz(snzzzggzze, snzzzgrs) {
	var snzzzgnpjgz = 0;
	var bdspgz = formData.fq_.cjrjybzjInitData.bdspgz;
	if (isNotEmptyObject(snzzzgrs)) {
		snzzzgnpjgz = ROUND(snzzzggzze/snzzzgrs,6);
		if (!isNotEmptyObject(bdspgz)) {
			return ROUND(snzzzgnpjgz,2);
		}
	}
	var nsqxDm = formData.ht_.cjrvoList.cjrvoListlb[0].nsqxDm;
	var sbqx = formData.hq_.sbxxVoList.sbxxGridlb[0].sbqx;
	var sbqx_ = "2018-04-01";

	// 获取“取当地社会平均工资”
	var spgz = getSpgz(sbqx_, sbqx, nsqxDm, bdspgz);

	if(snzzzgnpjgz > spgz){
		snzzzgnpjgz = spgz;
	}
	return ROUND(snzzzgnpjgz,2);
}

/**
 * 计算本期应纳费额
 * @param snzzzggzze 上年在职职工工资总额
 * @param snzzzgrs 上年在职职工人数
 * @param yapcjrjybl 应安排残疾人就业比例
 * @param snsjapcjrjyrs 上年实际安排残疾人就业人数
 */
var cSnzzzggzze = 0;
var cSnzzzgrs = 0;
function change(snzzzggzze, snzzzgrs, yapcjrjybl, snsjapcjrjyrs, snzzzgnpjgz){
	var bqynse = 0;
	var bdspgz = formData.fq_.cjrjybzjInitData.bdspgz;
	// 征收系数
	var zsxs = formData.fq_.zsxs;
	var cersblxsw =  formData.fq_.cersblxsw;

	/**
	 * 计算（上年在职职工人数*应安排残疾人就业比例-上年实际安排残疾人就业人数）的值时，其精度依据核心系统参数A0000001061001488控制，具体精度根据接口返回cersblxsw的值来判断。
	 * medianValue 是计算本期应纳税额的中间值
	 */
	var medianValue = cersblxsw !== '' ? parseFloat((snzzzgrs * yapcjrjybl - snsjapcjrjyrs)).toFixed(Number(cersblxsw)) : (snzzzgrs * yapcjrjybl - snsjapcjrjyrs);

	//页面显示的实际年平均工资可能会被社平工资替代,原始值还得用snzzzggzze/snzzzgrs,禅道27005:生产按照2位保留两位小数计算
	if (isNotEmptyObject(snzzzgrs)) {
		snzzzgnpjgz = snzzzggzze / snzzzgrs;
	}

	/*
	 * 当本地社平工资为0或者没有录入数据库时,本期应纳税额用申报表实际年平均工资来计算
	 */
	if (!isNotEmptyObject(bdspgz)) {
		snzzzgnpjgz = snzzzggzze / snzzzgrs;
		bqynse = medianValue * snzzzgnpjgz;
		return bqynse >= 0 ? bqynse : 0;
	}

	var sbqx = formData.hq_.sbxxVoList.sbxxGridlb[0].sbqx;
	// 如果获取申报期限为空则阻断
	if(!isNotEmptyObject(sbqx)){

		var msg = "尊敬的纳税人，申报期限为空，不符合计算条件，无法继续申报!";
		$vue.$gtDialog.confirm({
			header:'提示',
			theme: 'info',
			body: msg,
			closeOnOverlayClick: false,
			closeOnEscKeydown: false,
			onClosed: function() {
			closeWindow();
		}});
		return;
	}

	var sbqx_ = "2018-04-01";
	var nsqxDm = formData.ht_.cjrvoList.cjrvoListlb[0].nsqxDm;

	// 获取“取当地社会平均工资”的n倍
	var spgz = getSpgz(sbqx_, sbqx, nsqxDm, bdspgz);
	// 获取“取当地社会平均工资”
	var ddshpjgz;

	if(nsqxDm == "06"){
		ddshpjgz = bdspgz / 12;
	}else if(nsqxDm == "08"){
		ddshpjgz = bdspgz / 4;
	}else if(nsqxDm == "09"){
		ddshpjgz = bdspgz / 2;
	}else if(nsqxDm == "10"){
		ddshpjgz = bdspgz;
	}

	var tips;
	if (rqbj(sbqx_,sbqx) && snzzzgnpjgz > spgz) {
		tips = "【政策依据】按照财政部公告〔2019〕98 号文件规定：残疾人就业保障金征收标准上限，按照当地社会平均工资2倍执行。上年在职职工平均工资[" + ROUND(snzzzgnpjgz,2).toFixed(2) + "]大于社会平均工资[" + ROUND(ddshpjgz,2).toFixed(2) + "]的2倍，本期税额以社会平均工资的2倍进行计算！";
		snzzzgnpjgz = spgz * zsxs;
	} else if (snzzzgnpjgz > spgz) {
		tips = "在职职工平均工资[" + ROUND(snzzzgnpjgz,2).toFixed(2) + "]大于社会平均工资[" + ROUND(ddshpjgz,2).toFixed(2) + "]的3倍，本期税额以社会平均工资的3倍进行计算！";
		snzzzgnpjgz = spgz * zsxs;
	} else{
		snzzzgnpjgz = snzzzggzze / snzzzgrs;
	}
	if (cSnzzzggzze === snzzzggzze && cSnzzzgrs === snzzzgrs) {
		tips = undefined;
	}
	if (tips != undefined) {
		cSnzzzggzze = snzzzggzze;
		cSnzzzgrs = snzzzgrs;
		$vue.$gtDialog.confirm({
			header:'提示',
			theme: 'info',
			body: tips,
			closeOnOverlayClick: false,
			closeOnEscKeydown: false});
	}

	//本期应纳费额-按照纳税期限代码计算标志
	var nsqxDmJsBz = formData.fq_.nsqxDmJsBz;
	var csz=1;
	if(nsqxDmJsBz === "Y") {
		if (nsqxDm == "06") {
			csz = 12;
		}
		if (nsqxDm == "08") {
			csz = 4;
		}
		if (nsqxDm == "09") {
			csz = 2;
		}
	}
	bqynse = (medianValue * snzzzgnpjgz) / csz;
	return  bqynse >= 0? bqynse : 0;
}

var gzJmxx = "Y";
// 计算本期减免费额
function jsjmse(snzzzggzze,snzzzgrs,bqynse,jmxzDm,yapcjrjybl) {
	var bqjmse = 0;
	var zgswjdm = formData.fq_.nsrjbxx.zgswjDm;
	var swjgdm = zgswjdm.substring(0,3);
	var kyslrq = formData.fq_.cjrjybzjInitData.kyslrq.substring(0,10);
	var jmbl = formData.fq_.cjrjybzjInitData.jmbl;
	var qtjmxzBz = false;
	var snsjapcjrjyrs = formData.ht_.cjrvoList.cjrvoListlb[0].snsjapcjrjyrs;
	var sbqx = formData.hq_.sbxxVoList.sbxxGridlb[0].sbqx;
	var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
	var sssqq = formData.fq_.cjrjybzjInitData.sssq.rqQ;
	var sssqz = formData.fq_.cjrjybzjInitData.sssq.rqZ;
	//减免性质是否锁定
	var jmxzlockBz = true;

	//【金三版本升级】残保金——1189.04版本升级JSONE-8570
	// 申报期限在2020年1月1日至2022年12月31日期间
	var xsqq = "2020-01-01";
	var xsqz = "2027-12-31";
	var betweenXsq = rqbj(xsqq,sbqx) && rqbj(sbqx,xsqz);
	// 实际安排残疾人就业比例 =上年实际安排残疾人就业人数/上年在职职工人数
	var sjapcjrjybl = snsjapcjrjyrs / snzzzgrs;
	var sjapcjrjyblNotNull = !isNull(sjapcjrjybl);
	// 当前纳税人登记注册类型为企业（非5开头）
	var nsrIsEnterprise = isNotEmptyObject(djzclxDm) && (djzclxDm.substring(0,1) != "5");
	// 申报年份
	var year = getYears(sssqz, kyslrq);
	// 税款所属期止-开业日期<=3
	var openingYearWithin3 = isNotEmptyObject(kyslrq) && year <= 3;
	var openingYearBeyond3 = isNotEmptyObject(kyslrq) && year > 3;
	// 申报月份数
	var SbMonth = getMonth(sssqz, sssqq);
	// 可免税月份数
	var MsMonth = jsMSMonth(kyslrq, sssqq, sssqz) >= 0 ? jsMSMonth(kyslrq, sssqq, sssqz) : 0;
	// 可免税减免金额 = 本期应纳税额/申报月份数*可免税月份数
	var kmsjmse = bqynse / SbMonth * MsMonth;

	if (swjgdm == "132" || zgswjdm.startsWith("14403")) {
		// 深圳和江苏省
		if (snzzzgrs <= 30 && djzclxDm.substring(0,3) != "900") {
			//jmse按照应纳税额100%计算
			bqjmse = bqynse;
		} else {
			bqjmse = 0;
		}
	} else if (swjgdm == "161") {
		// 陕西个性化
		if (nsrIsEnterprise) {
			if (isOneOfNineTradesInSN()) {
				if (betweenXsq) {
					// 小于等于30人且登记注册类型为“900-其他”时不享受减免
					if (snzzzgrs <= 30 && djzclxDm.substring(0,3) != "900") {
						bqjmse = bqynse;
					} else if (rqbj(xsqq,sbqx) && rqbj(sbqx,'2020-01-31') && snzzzgrs > 30) {
						if (sjapcjrjyblNotNull && sjapcjrjybl < 0.01) {
							bqjmse = bqynse * 0.1;
						} else if (sjapcjrjyblNotNull && sjapcjrjybl >= 0.01 && sjapcjrjybl < yapcjrjybl) {
							bqjmse = bqynse * 0.5;
						}
					} else if (rqbj('2020-02-01',sbqx) && rqbj(sbqx,xsqz) && snzzzgrs > 30){
						qtjmxzBz = true;
						bqjmse = bqynse;
					}
				} else if (rqbj(xsqz,sbqx)) {
					qtjmxzBz = true;
					bqjmse = bqynse;
				} else if (rqbj(sbqx,xsqq)) {
					if (snzzzgrs <= 30 && openingYearWithin3) {
						jmxzlockBz = false;
						bqjmse = kmsjmse;
					} else if (snzzzgrs > 30 || openingYearBeyond3) {
						bqjmse = 0;
					}
				}
			} else {
				if (betweenXsq) {
					// 小于等于30人且登记注册类型为“900-其他”时不享受减免
					if (snzzzgrs <= 30 && djzclxDm.substring(0,3) != "900") {
						bqjmse = bqynse;
					} else {
						if (sjapcjrjyblNotNull && sjapcjrjybl < 0.01) {
							bqjmse = bqynse * 0.1;
						} else if (sjapcjrjyblNotNull && sjapcjrjybl >= 0.01 && sjapcjrjybl < yapcjrjybl) {
							bqjmse = bqynse * 0.5;
						}
					}
				} else {
					if (snzzzgrs <= 30 && openingYearWithin3) {
						jmxzlockBz = false;
						bqjmse = kmsjmse;
					} else if (snzzzgrs > 30 || openingYearBeyond3) {
						bqjmse = 0;
					}
				}
			}
		} else if (betweenXsq) {
			if (sjapcjrjyblNotNull && sjapcjrjybl < 0.01) {
				bqjmse = bqynse * 0.1;
			} else if (sjapcjrjyblNotNull && sjapcjrjybl >= 0.01 && sjapcjrjybl < yapcjrjybl) {
				bqjmse = bqynse * 0.5;
			}
		}
	} else {
		// 全国版
		if (betweenXsq) {
			if(nsrIsEnterprise) {
				// 小于等于30人且登记注册类型为“900-其他”时不享受减免
				if (snzzzgrs <= 30 && bqynse>0 && djzclxDm.substring(0,3) != "900") {
					bqjmse = bqynse;
				} else if (snzzzgrs > 30 && bqynse > 0) {
					if (sjapcjrjyblNotNull && sjapcjrjybl < 0.01) {
						bqjmse = bqynse * 0.1;
					} else if (sjapcjrjyblNotNull && sjapcjrjybl >= 0.01 && sjapcjrjybl < yapcjrjybl) {
						bqjmse = bqynse * 0.5;
					}
				}
			} else {
				if (bqynse > 0) {
					if (sjapcjrjyblNotNull && sjapcjrjybl < 0.01) {
						bqjmse = bqynse * 0.1;
					} else if (sjapcjrjyblNotNull && sjapcjrjybl >= 0.01 && sjapcjrjybl < yapcjrjybl) {
						bqjmse = bqynse * 0.5;
					}
				}
			}
		} else {
			if (snzzzgrs <= 30 && openingYearWithin3) {
				jmxzlockBz = false;
				bqjmse = kmsjmse;
			} else if (snzzzgrs > 30 || openingYearBeyond3) {
				bqjmse = 0;
			}
		}
	}
	var otherDm = '0099129999';
	var otherMc = '其他';
	//当减免性质存在下拉值时（申报期限小于2022-01-01 或大于2027-12-31，开业三年且在职职工人数小于等于30人时，属期在3年有效期内，放开选择时），不对选择的减免性质进行再次赋值
	if(jmxzlockBz){
		// 减免性质
		if (ROUND(bqjmse,2) > 0 && betweenXsq) {
			if (bqjmse == bqynse) {
				var dm = "";
				var swsxDm = "";
				var mc = "";

				dm = "0035049901";
				swsxDm = "SXA031901109";
				mc="在职职工总数30人（含）以下暂免征收残保金│《财政部关于延续实施残疾人就业保障金优惠政策的公告》 财政部公告2023年第8号 第二条";
				if(rqbj(sbqx,"2022-12-31")){
					mc = "在职职工总数30人（含）以下暂免征收残保金|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(四)款";
				}
				if(qtjmxzBz){
					dm = otherDm;
					swsxDm = otherDm;
					mc = otherMc;
				}
				formCT.ssjmxzCT = {};
				var jmxzObj = formCT.ssjmxzCT;
				jmxzObj[dm] = mc;
				formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
				formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
			} else if (bqynse > bqjmse) {
				var dm = "";
				var swsxDm = "";
				var mc = "";
				dm = "0035013601";
				swsxDm = "SXA031901110";
				mc="残保金调整为分档征收│《财政部关于延续实施残疾人就业保障金优惠政策的公告》 财政部公告2023年第8号 第一条";
				if (rqbj(sbqx,"2022-12-31")) {
					mc = "残保金调整为分档征收|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(三)款";
				}
				if (qtjmxzBz) {
					dm = otherDm;
					swsxDm = otherDm;
					mc = otherMc;
				}
				formCT.ssjmxzCT = {};
				var jmxzObj = formCT.ssjmxzCT;
				jmxzObj[dm] = mc;
				formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
				formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
			}
		} else if (ROUND(bqjmse,2)) {
			formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = otherDm;
			formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = otherDm;
		} else {
			formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
			formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
		}
	}

	var jmxzObj = initJmxz();
	if (jmxzlockBz) {
		formData.fq_.jmxzLock="Y";
		formCT.ssjmxzCT=jmxzObj;
	} else {
		formData.fq_.jmxzLock="N";
		var jmxzObjcopy = {};
		if (swjgdm == "151") {
			jmxzObjcopy["5199049902"] = "自工商登记注册之日起3年内，对安排残疾人就业未达到规定比例、在职职工总数30人以下（含30人）的小微企业，免征保障金|《财政部关于取消、调整部分政府性基金有关政策的通知》（财税〔2017〕18号）第二条第（一）款";
		} else {
			// 自动带出 其他
			if (ROUND(bqjmse,2)) {
				formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = otherDm;
				formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = otherDm;
			}
			jmxzObjcopy[otherDm] = otherMc;
		}
		formCT.ssjmxzCT=jmxzObjcopy;
	}
	var _jpath1= "fq_.jmxzLock";
	formulaEngine.apply(_jpath1, "");
	// var viewEngine = $("#frmSheet")[0].contentWindow.viewEngine;
	// var $viewAppElement = $("#frmSheet").contents().find("#viewCtrlId");
	// viewEngine.tipsForVerify(document.body);
	// viewEngine.formApply($viewAppElement);

	// 赋值给临时节点用于公式校验 本期减免费额【数值】不能大于自动带出的本期减免费额【数值】
	//只有自动带出,tempBqjmse的值才可能大于0
	formData.fq_.cjrjybzjInitData.tempBqjmse = ROUND(bqjmse,2);
	return bqjmse;
}

// 陕西SN校验是否属于九大行业
function isOneOfNineTradesInSN(){
	var result = false;
	var hyDm = formData.fq_.nsrjbxx.hyDm;
	var codeEntireList = ['7291', '9051', '9052', '8760'];
	var codeEntireListLen = codeEntireList.length;
	for (var i = 0; i < codeEntireListLen; i++) {
		if (hyDm === codeEntireList[i]){
			result = true;
			break;
		}
	}
	var codeStartsWithList = ['786', '61', '62', '728', '51', '52', '80', '53', '54', '55', '56', '57', '58', '83', '881', '882'];
	var codeStartsWithListLen = codeStartsWithList.length;
	if (!result) {
		for (var i = 0; i < codeStartsWithListLen; i++) {
			if (hyDm.indexOf(codeStartsWithList[i]) === 0){
				result = true;
				break;
			}
		}
	}
	return result;
}

function jsjmse_xm(djzclxDm,kyslrq,sssqq,sssqz,snzzzgrs,bqynse,jmbl){
	var bqjmse = 0;
	// 当skssqq小于等于2018年1月1日或大于等于2022年1月1日
	if (rqbj(sssqq,"2018-01-01") || (sssqq.replace(/\-/g, "\/") >= "2022-01-01".replace(/\-/g, "\/"))) {
		if (bqynse > 0 && isNotEmptyObject(kyslrq)) {
			// 申报年份
			var year = getYears(sssqz, kyslrq);
			// 申报月份数
			var SbMonth = getMonth(sssqz, sssqq);
			// 可免税月份数
			var MsMonth = jsMSMonth(kyslrq, sssqq, sssqz);
			// 未免税月份数
			var WmsMonth = SbMonth - MsMonth;
			// 当可免税月分数为0
			if (MsMonth == 0 && isNotEmptyObject(jmbl) && SbMonth >= 1) {
				// jmbl *（本期应纳费额/申报月份数）* 剩余可享受减免的月份数
				bqjmse = bqynse/SbMonth*jmbl*SbMonth;
			}
			// 1、当前纳税人登记注册类型为企业（非5开头）
			if(isNotEmptyObject(djzclxDm) && (djzclxDm.substring(0,1) != "5")) {
				/*①若“上年在职职工人数”小于等于30：
				查询hx_dj.DJ_PZJGXX中kyslrq与申报属期止比较，当属期止-登记日期≤3年*/
				if (year <= 3 && snzzzgrs <= 30 && djzclxDm.substring(0,3) != "900") {
					// 当可免税月分数为0
					if (MsMonth == 0 && isNotEmptyObject(jmbl) && WmsMonth >= 1) {
						// jmbl*（本期应纳费额/申报月份数）* 剩余可享受减免的月份数
						bqjmse = bqynse/SbMonth*jmbl*WmsMonth;
					} else if(MsMonth > 0){
						// 减免额 = 本期应纳费额/申报月份数*可免税月份数
						bqjmse =bqynse/SbMonth*MsMonth;
						if (bqjmse >= 0 && isNotEmptyObject(jmbl) && WmsMonth >= 1) {
							// 减免额 = ①已享受的减免+jmbl*（本期应纳费额/申报月份数）* 剩余可享受减免的月份数
							bqjmse = bqjmse + bqynse/SbMonth*jmbl*WmsMonth;
						}
					}
					if (bqjmse >= 0){
						// 自动带出减免性质0035049901，不可修改
						var dm = "0035049901";
						var swsxDm = "SXA031901109";
						var mc = "在职职工总数30人（含）以下暂免征收残保金|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(四)款";
						formCT.ssjmxzCT = {};
						var jmxzObj = formCT.ssjmxzCT;
						jmxzObj[dm] = mc;
						formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
						formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
					} else {
						formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
						formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
					}
				} else if (isNotEmptyObject(jmbl) && SbMonth >= 1) {
					// 当属期止-登记日期＞3年
					if (year > 3 || snzzzgrs > 30) {
						// jmbl *（本期应纳费额/申报月份数）* 剩余可享受减免的月份数
						bqjmse =jmbl*(bqynse/SbMonth)*SbMonth;
						if (bqjmse >= 0){
							// 自动带出减免性质“0099129999”，不可修改
							var dm = "0099129999";
							var swsxDm = "0099129999";
							var mc = "其他";
							formCT.ssjmxzCT = {};
							var jmxzObj = formCT.ssjmxzCT;
							jmxzObj[dm] = mc;
							formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
							formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
						} else {
							formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
							formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
						}
					} else {
						formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
						formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
					}
				}
			} else if (isNotEmptyObject(jmbl) && SbMonth >= 1 && isNotEmptyObject(djzclxDm) && (djzclxDm.substring(0,1) == "5")) {
				 // jmbl *（本期应纳费额/申报月份数）* 剩余可享受减免的月份数
				 bqjmse =jmbl*(bqynse/SbMonth)*SbMonth;
				 if (bqjmse >= 0){
					var dm = "0099129999";
					var swsxDm = "0099129999";
					var mc = "其他";
					formCT.ssjmxzCT = {};
					var jmxzObj = formCT.ssjmxzCT;
					jmxzObj[dm] = mc;
					formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
					formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
				 } else {
					formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
					formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
				}
			}
		} else {
			formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
			formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
		}
	} else {
		// 当skssqq为2019年1月1日至2021年12月31日之间
		if (bqynse > 0) {
			if(isNotEmptyObject(djzclxDm) && (djzclxDm.substring(0,1) != "5")) {
				if (snzzzgrs <= 30 && djzclxDm.substring(0,3) != "900") {
					bqjmse = bqynse;
					var dm = "0035049901";
					var swsxDm = "SXA031901109";
					var mc = "在职职工总数30人（含）以下暂免征收残保金|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(四)款";
					formCT.ssjmxzCT = {};
					var jmxzObj = formCT.ssjmxzCT;
					jmxzObj[dm] = mc;
					formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
					formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
				} else {
					if (isNotEmptyObject(jmbl)) {
						bqjmse = jmbl*bqynse;
						if (bqjmse >= 0){
							var dm = "0035013601";
							var swsxDm = "SXA031901110";
							var mc = "残保金调整为分档征收|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》发改价格规[2019]2015号附件：第二条第(三)款";
							formCT.ssjmxzCT = {};
							var jmxzObj = formCT.ssjmxzCT;
							jmxzObj[dm] = mc;
							formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
							formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
						}
					} else {
						formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
						formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
					}
				}
			} else if(isNotEmptyObject(djzclxDm) && (djzclxDm.substring(0,1) == "5")) {
				bqjmse = 0.1 * bqynse;
				if (bqjmse >= 0){
					var ssqnd = sssqq.split("-")[0];
					if(ssqnd == 2019){
						var dm = "0099129999";
						var swsxDm = "0099129999";
						var mc = "其他";
						formCT.ssjmxzCT = {};
						var jmxzObj = formCT.ssjmxzCT;
						jmxzObj[dm] = mc;
					} else if(ssqnd == 2020 || ssqnd == 2021){
						var dm = "0035013601";
						var swsxDm = "SXA031901110";
						var mc = "残保金调整为分档征收|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》发改价格规[2019]2015号附件：第二条第(三)款";
						formCT.ssjmxzCT = {};
						var jmxzObj = formCT.ssjmxzCT;
						jmxzObj[dm] = mc;
					}
					formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = dm;
					formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = dm;
				} else {
					formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
					formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
				}
			}
		} else {
			formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm = "";
			formData.ht_.cjrvoList.cjrvoListlb[0].ssjmxzDm = "";
		}
	}

	if(sssqq.substring(0,4) == "2016" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
	} else if(sssqq.substring(0,4) == "2016" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0099129999"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "残疾人就业保障金在现行基础上减半征收。以上政策从2016年6月1日起执行，其中降低社会保险费用和残疾人就业保障金政策执行至 2017年 12月 31日 。（厦府〔2016〕163号）");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "残疾人就业保障金在现行基础上减半征收。以上政策从2016年6月1日起执行，其中降低社会保险费用和残疾人就业保障金政策执行至 2017年 12月 31日 。（厦府〔2016〕163号）");
	} else if(sssqq.substring(0,4) == "2017" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
	} else if(sssqq.substring(0,4) == "2017" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0099129999"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
	} else if(sssqq.substring(0,4) == "2018" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "工商注册登记未满3年、在职职工总数30人（含）以下的企业，可在剩余时期内按规定免征残疾人就业保障金。（财税〔2017〕18号）2017年4月起施行");
	} else if(sssqq.substring(0,4) == "2018" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0099129999"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
	} else if(sssqq.substring(0,4) == "2019" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
	} else if(sssqq.substring(0,4) == "2019" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035013601" && (bqynse * 0.5) == bqjmse){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "根据厦府规〔2020〕5号规定第二条，按规定减半征收2020年企业应缴纳2019年度的残保金");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "根据厦府规〔2020〕5号规定第二条，按规定减半征收2020年企业应缴纳2019年度的残保金");
	} else if(sssqq.substring(0,4) == "2019" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0099129999"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "经市政府研究决定，继续执行残疾人就业保障金减半征收政策，执行期限从2018年1月1日起至2019年12月31日。《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社[2018]11号）");
	} else if(sssqq.substring(0,4) == "2020" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
	} else if(sssqq.substring(0,4) == "2020" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035013601" && (bqynse * 0.5) == bqjmse){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "依据《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社〔2021〕12号）规定减半征收2021年企业应缴纳的2020年残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "依据《厦门市财政局 厦门市残疾人联合会 厦门市税务局关于继续执行残疾人就业保障金减负政策的通知》（厦财社〔2021〕12号）规定减半征收2021年企业应缴纳的2020年残疾人就业保障金。");
	} else if(sssqq.substring(0,4) == "2020" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035013601" && (bqynse * 0.1) == bqjmse){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "除企业外的用人单位依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第三条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第三条规定，自2020年1月1日起至2022年12月31日，用人单位安排残疾人就业比例在1%以下的，按规定应缴费额的90%缴纳残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "除企业外的用人单位依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第三条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第三条规定，自2020年1月1日起至2022年12月31日，用人单位安排残疾人就业比例在1%以下的，按规定应缴费额的90%缴纳残疾人就业保障金。");
	} else if(sssqq.substring(0,4) == "2021" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035049901"){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第四条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第四条规定，自2020年1月1日起至2022年12月31日，在职职工人数在30人（含）以下的企业，暂免征收残疾人就业保障金。");
	} else if(sssqq.substring(0,4) == "2021" && formData.ht_.cjrvoList.cjrvoListlb[0].jmxzDm == "0035013601" && (bqynse * 0.1) == bqjmse){
		$("#frmSheet").contents().find("#bqjmseId").attr("ng-show-tbsm", "除企业外的用人单位依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第三条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第三条规定，自2020年1月1日起至2022年12月31日，用人单位安排残疾人就业比例在1%以下的，按规定应缴费额的90%缴纳残疾人就业保障金。");
		$("#frmSheet").contents().find("#bqjmseId").attr("title", "除企业外的用人单位依据《国家发展改革委 财政部 民政部 人力资源社会保障部 税务总局 中国残联关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》（发改价格规〔2019〕2015号）附件第三条、《财政部关于调整残疾人就业保障金征收政策的公告》（2019年第98号）第三条规定，自2020年1月1日起至2022年12月31日，用人单位安排残疾人就业比例在1%以下的，按规定应缴费额的90%缴纳残疾人就业保障金。");
	} else {
		$("#frmSheet").contents().find("#bqjmseId").removeAttr("ng-show-tbsm");
		$("#frmSheet").contents().find("#bqjmseId").removeAttr("title");
	}

	return bqjmse;
}

//申报确认提示前执行业务特有校验
function doBeforeCtipsVerify(isSecondCall){
	// 上年在职职工工资总额
    var snzzzggzze = Number(formData.ht_.cjrvoList.cjrvoListlb[0].snzzzggzze);
    // 上年在职职工人数
    var snzzzgrs = Number(formData.ht_.cjrvoList.cjrvoListlb[0].snzzzgrs);
    if(snzzzggzze < snzzzgrs){
		layer.confirm("[上年在职职工工资总额]【"+snzzzggzze+"】小于[上年在职职工人数]【"+snzzzgrs+"】，请核实是否有误！",{
			icon : 3,
			title:'提示',
			btn : ['返回修改','继续申报'],
			cancel: function(index){
				//X按钮,认为无须核实，弹出公共申报确认提示
				layer.close(index);
				ctips(isSecondCall);
			},
			btn2:function(index){
				//继续申报按钮,认为无须核实，弹出公共申报确认提示
				layer.close(index);
				ctips(isSecondCall);
			}
		},function(index){
			//点击【返回修改】，认为需要核实，停留在填表页；
			layer.close(index);
			$("body").unmask();
			if(typeof umMaskZdy == "function"){
				umMaskZdy();
			}
			prepareMakeFlag = true;
			return;
		});
	}else {
		ctips(isSecondCall);
	}
}

// 校验非企业小于等于30人,应纳税额不能等于减免税额
function checkFqy(snzzzgrs,bqynse,bqjmse){
	var result = true;
    var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
    // 如果是非企业并且应纳税额等于减免税额
	if(bqynse > 0 && bqjmse > 0){
        if(djzclxDm.substring(0,1) == "5" ){
            if(bqynse == bqjmse){
                result = false;
            }
        }
	}
	return result;
}

function setJmxz(jmxzDm,swsxDm,mc){
	var obj = {};
	obj.dm = jmxzDm;
	obj.swsxDm = swsxDm;
	obj.mc = mc;
	obj.jmzlxDm = "";
	obj.jmed = "";
	obj.jmfd = "";
	obj.jmsl = "";
	return obj;
}

// 计算日期相差多少月份
function getMonth(a,b){
    var arrA = a.toString().split("-");
    var arrB = b.toString().split("-");
    var month = (arrA[0] - arrB[0]) * 12 + (arrA[1] - arrB[1]) +1;
    return month;
}

// 计算日期相差多少年
function getYears(a,b){
    var arrA = a.toString().split("-");
    var arrB = b.toString().split("-");
    var year = 0;
    if(arrA[1]<arrB[1]||arrA[2]<arrB[2]){
        year = arrA[0] - arrB[0]-1;
    } else {
        year = arrA[0] - arrB[0];
    }
    return Number(year);
}

// 计算免税额月份
function jsMSMonth(kyslrq,sssqq,sssqz) {
	var arrA = kyslrq.toString().split("-");
	var arrB = sssqq.toString().split("-");
	var arrC = sssqz.toString().split("-");
	var yxrq = (Number(arrA[0])+3)+"-"+arrA[1]+"-"+arrA[2];
	var Month = 0;
	// 税款所属期止<免税有效日期 申报几月份就减免几月份的税额
    if(rqbj(sssqz,yxrq)){
       Month = getMonth(sssqz,sssqq);
    } else {
       // 税款所属期止>免税有效日期
	   // var yxrqArr = yxrq.toString().split("-");
       // Month = (yxrqArr[1]-1)-arrB[1]+1;
	   Month = getMonth(yxrq,sssqq)-1;
    }
	return Month;
}

function jmxxtsk(month){
	var jmblVal = formData.fq_.cjrjybzjInitData.jmbl;
	if(month > 0 && isNotEmptyObject(jmblVal)) {
		layer.alert(
			"您本次享受的审批减缴比例为【"+jmblVal*100+"%】，减免月份数为【"+month+"】!", {
			title : "提示",
			icon : 6,
			offset : 'auto'
		});
	}
}

//获取上年实际安排残疾人就业人数
function getCjrs() {
	var result = 0;

	var sfdyjk = formData.fq_.cjrjybzjInitData.sfdyjk;
	var zgryVO = formData.fq_.cjrjybzjInitData.zgryVO;
	var snsjapcjrjyrs_hq = formData.hq_.snsjapcjrjyrs; // 核心数据
	var wbjhkqsbz = formData.fq_.cjrjybzjInitData.wbjhkqsbz; // 外部交换库取数标志
	var wbjhSnsjapcjrjyrs = formData.fq_.cjrjybzjInitData.wbjhSnsjapcjrjyrs;// 外部交换库数据
	var azcjnndhzs = formData.fq_.cjrjybzjInitData.xmzdycs.azcjnndhzs; // 厦门外部交换库数据
	// 残疾人数
	var cjrs = 0;
	if (!isNull(zgryVO)){
		cjrs = zgryVO.cjrs;
	}
	if(cjrs==0){
		result = snsjapcjrjyrs_hq;
	}else if (cjrs != 0){
		result = cjrs;
	}
	return result;
}

// 获取当月最后一天
function getLastDay(year,month){
     var new_year = year;    //取当前的年份
     var new_month = month++;//取下一个月的第一天，方便计算（最后一天不固定）
     if(month > 12){
      new_month -=12;        //月份减
      new_year++;            //年份增
     }
     var new_date = new Date(new_year,new_month,1);                //取当年当月中的第一天
     return (new Date(new_date.getTime()-1000*60*60*24)).getDate();//获取当月最后一天日期
}

// 校验小数是否超过4位数，如果超过4位数，返回false
function jyxs(cs) {
	if(cs == 0 || cs == null) {
		return true;
	} else if(cs < 0){
		return false;
	}
	var xs_csz = cs.toString().split(".")[1];
	if(typeof(xs_csz) != "undefined") {
		if(xs_csz) {
			var len = cs.toString().split(".")[1].length;
			if(len >= 4) {
				dsw = xs_csz.substring(3, 4);
				if(dsw > 0) {
					return false;
				}
			}
		}
	}
	return true;
}

// 陕西个性化
function sx_jyxs(cs) {
	if(cs == undefined || cs == null) {
		return true;
	} else if(cs <= 0){
		return false;
	}
	if(!(/(^[1-9]\d*$)/.test(cs))){
		return false;
	}
	return true;
}

/*
 * 判断数据不为空的方法
 */
function isNotEmptyObject(obj){
	if(obj==""||obj==null||obj==undefined||obj==0){
		return false;
	} else {
		return true;
	}
}

// 福建校验开业设立日期时间 电子税务局限制零申报，当开业设立日期小于2019年12月时
function checkKyslrq(value){
	if(value > 0){
		return true;
	}
	var kyslrq = formData.fq_.cjrjybzjInitData.kyslrq;
	if(isNotEmptyObject(kyslrq)){
		kyslrq = kyslrq.substring(0,10);
		if(rqbj(kyslrq,"2019-12-01")){
			return true;
		} else {
			return false;
		}
	}
	return false;
}

// 陕西校验是否是特殊行业纳税人
function checkTsHyNsr(){
	var result = false;
	var djzclxDm = formData.fq_.nsrjbxx.djzclxDm;
    var hyDm = formData.fq_.nsrjbxx.hyDm;
    if(((djzclxDm.substring(0,1) == "1") ||
		(djzclxDm.substring(0,1) == "2") ||
		(djzclxDm.substring(0,1) == "3")) &&
		((hyDm.substring(0,4) == "7291") ||
		(hyDm.substring(0,3) == "786") ||
		(hyDm.substring(0,2) == "61") ||
		(hyDm.substring(0,2) == "62") ||
		(hyDm.substring(0,3) == "728") ||
		(hyDm.substring(0,2) == "51") ||
		(hyDm.substring(0,2) == "52") ||
		(hyDm.substring(0,2) == "80") ||
		(hyDm.substring(0,2) == "53") ||
		(hyDm.substring(0,2) == "54") ||
		(hyDm.substring(0,2) == "55") ||
		(hyDm.substring(0,2) == "56") ||
		(hyDm.substring(0,2) == "57") ||
		(hyDm.substring(0,2) == "58") ||
		(hyDm.substring(0,2) == "83") ||
		(hyDm.substring(0,3) == "881") ||
		(hyDm.substring(0,3) == "882") ||
		(hyDm.substring(0,4) == "9051") ||
		(hyDm.substring(0,4) == "9052") ||
		(hyDm.substring(0,4) == "8760"))
	){
		result = true;
	}
    return result;
}

/**
 * 日期比较
 * @param beginDate
 * @param endDate
 * @return (beginDate <= endDate) true
 */
function rqbj(beginDate, endDate) {
	var d1 = new Date(beginDate.replace(/\-/g, "\/"));
	var d2 = new Date(endDate.replace(/\-/g, "\/"));
	if (d1 <= d2) {
		return true;
	}
	return false;
}

/**
 * 取得url参数
 */
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象
    var r = window.location.search.substr(1).match(reg);  // 匹配目标参数
    if (r != null) return unescape(r[2]); return null; // 返回参数值
}

/**
 * 除法
 */
function finedDiv(arg1,arg2){
	var t1=0,t2=0,r1,r2;
  	try{t1=arg1.toString().split(".")[1].length}catch(e){}
  	try{t2=arg2.toString().split(".")[1].length}catch(e){}
  	r1=Number(arg1.toString().replace(".",""));
  	r2=Number(arg2.toString().replace(".",""));
	return (r1/r2)*Math.pow(10,t2-t1);
}

//初始化jmxz下拉
function initJmxz(){
	var jmxzObj = {};
	jmxzObj["0035049901"] = "在职职工总数30人（含）以下暂免征收残保金|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(四)款";
	jmxzObj["0035013601"] = "残保金调整为分档征收|《国家发展和改革委员会 财政部 民政部 人力资源和社会保障部 国家税务总局 中国残疾人联合会关于印发<关于完善残疾人就业保障金制度更好促进残疾人就业的总体方案>的通知》 发改价格规[2019]2015号附件：第二条第(三)款";
	jmxzObj["0099129999"] = "其他";
	jmxzObj["5199049902"] = "自工商登记注册之日起3年内，对安排残疾人就业未达到规定比例、在职职工总数30人以下（含30人）的小微企业，免征保障金|《财政部关于取消、调整部分政府性基金有关政策的通知》（财税〔2017〕18号）第二条第（一）款";
	return jmxzObj;
}

// pdf用到的t_中间节点统一赋值
function generatePdfAssignments() {
	for (let i = 0; i < formData.ht_.cjrvoList.cjrvoListlb.length; i++) {
		formData.ht_.cjrvoList.cjrvoListlb[i].t_xh = i+2;
	}
}

// 计算当地法定最低工资标准
function getDdfdzdgz(ddfdzdgz){
	var nsqxDm = formData.ht_.cjrvoList.cjrvoListlb[0].nsqxDm;
	var fdzdgz = 0;
	if(nsqxDm == "06"){
		fdzdgz = ddfdzdgz / 12;
	}else if(nsqxDm == "08"){
		fdzdgz = ddfdzdgz / 4;
	}else if(nsqxDm == "09"){
		fdzdgz = ddfdzdgz / 2;
	}else if(nsqxDm == "10"){
		fdzdgz = ddfdzdgz;
	}
	return fdzdgz;
}

export default {
	sbqCheck,
	tipsMsgBySnsjapcjrjyrs,
	tipsMsgBySnsjapcjrjyrs2,
	tipsMsgCjrsAndZgrsJk,
  CBJ_INIT_LXDH,
	sjNpjgz,
	change,
	jsjmse,
	jsjmse_xm,
	doBeforeCtipsVerify,
	checkFqy,
	setJmxz,
	getMonth,
	getYears,
	getDdfdzdgz,
	jsMSMonth,
	jmxxtsk,
	getCjrs,
	getLastDay,
	jyxs,
	sx_jyxs,
	isNotEmptyObject,
	checkKyslrq,
	checkTsHyNsr,
	rqbj,
	getUrlParam,
	finedDiv,
	tipsMsgByNsqxDm,
	tipsMsgBySnzzzggzze,
	tipsMsgBySnzzzgrs,
	generatePdfAssignments
}