<template>
  <div class="cbdhl">
    <t-menu theme="light" defaultValue="item1" collapsed="collapsed">
      <t-menu-item class="menu-first-item-in-blue" value="item1" @click="showCollapse">
        <template #icon>
          <icon :name="iconName" />
        </template>
        菜单
      </t-menu-item>
      <t-menu-item value="item2" @click="openGuide">
        <template #icon>
          <TipsIcon slot="icon" />
          <!-- <icon name="tips" /> -->
        </template>
        操作指引
      </t-menu-item>
      <t-menu-item value="item3" @click="openYdPage">
        <template #icon>
          <ControlPlatformIcon slot="icon" />
          <!-- <icon name="control-platform" /> -->
        </template>
        台账总览
      </t-menu-item>
      <t-menu-item value="item4" @click="gotoSbPage">
        <template #icon>
          <LinkIcon slot="icon" />
          <!-- <icon name="link" /> -->
        </template>
        去申报
      </t-menu-item>
      <t-menu-item value="item5" @click="gotoGzPage">
        <template #icon>
          <Edit1Icon slot="icon" />
          <!-- <icon name="edit-1" /> -->
        </template>
        去更正
      </t-menu-item>
    </t-menu>
  </div>
</template>

<script>
import { Icon, TipsIcon, ControlPlatformIcon, LinkIcon, Edit1Icon } from 'tdesign-icons-vue';

export default {
  props: {},
  data() {
    return {
      showSidebar: true, // 控制侧边栏的显示/隐藏
    };
  },
  components: {
    Icon,
    TipsIcon,
    ControlPlatformIcon,
    LinkIcon,
    Edit1Icon,
  },
  computed: {
    iconName() {
      return this.showSidebar ? 'menu-unfold' : 'menu-fold';
    },
    collapsed() {
      return false;
    },
    getCurrentTzmcUrl() {
      const URL = window.location.pathname;
      if (URL.indexOf('/znsb/view/tzzx/zzstz') > -1) {
        return { tzmc: '增值税台账', url: '/znsb/view/tzzx/zzstz' };
      }
      if (URL.indexOf('/znsb/view/tzzx/yhstz') > -1) {
        return { tzmc: '印花税台账', url: '/znsb/view/tzzx/yhstz' };
      }
      if (URL.indexOf('/znsb/view/tzzx/qysdsyjtz') > -1) {
        return { tzmc: '企业所得税预缴台账', url: '/znsb/view/tzzx/qysdsyjtz' };
      }
      if (URL.indexOf('/znsb/view/tzzx/zzsyjtz') > -1) {
        return { tzmc: '增值税预缴台账', url: '/znsb/view/tzzx/zzsyjtz' };
      }
      return '';
    },
  },
  created() {},
  methods: {
    openGuide() {
      const alertDia = this.$dialog.alert({
        theme: 'info',
        header: '提示',
        body: '功能暂未开放。',
        cancelBtn: null,
        onConfirm: () => {
          // 销毁弹框
          alertDia.destroy();
        },
      });
    },
    openYdPage() {
      const alertDia = this.$dialog.alert({
        theme: 'info',
        header: '提示',
        body: '功能暂未开放。',
        cancelBtn: null,
        onConfirm: () => {
          // 销毁弹框
          alertDia.destroy();
        },
      });
    },
    gotoSbPage() {
      sessionStorage.setItem('qsb2tz', JSON.stringify(this.getCurrentTzmcUrl));
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/nssb/sbrwmx`;
        window.parent.goSelfChange(menuParams);
      }
    },
    gotoGzPage() {
      sessionStorage.setItem('qgz2tz', JSON.stringify(this.getCurrentTzmcUrl));
      if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = `/znsb/view/nssb/sbgzyzf`;
        window.parent.goSelfChange(menuParams);
      }
    },
    showCollapse() {
      this.showSidebar = !this.showSidebar; // 切换 showSidebar 的值
      this.toggleSidebarDisplay();
    },
    toggleSidebarDisplay() {
      const sidebar = document.querySelector('.gt-collapse-menu-sidebar');
      if (sidebar) {
        sidebar.style.display = this.showSidebar ? 'block' : 'none';
      }
    },
  },
};
</script>
<style lang="less" scoped>
.cbdhl {
  background-color: #eee;
  /deep/.t-default-menu {
    width: 55px !important;
    height: calc(100% - 10px) !important;
  }
  /deep/.t-menu--scroll {
    padding: 0 !important;
  }
  /deep/.t-menu--scroll .t-menu__item {
    height: 55px !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  /deep/.menu-first-item-in-blue {
    color: #ecf2fe !important;
    background-color: #4285f4 !important;
    border-radius: 0 !important;
    .t-icon {
      color: #ecf2fe !important;
    }
  }
}
</style>
