<template>
  <div>
    <p>当前单据号: {{ nextBillNumber }}</p>
    <button @click="generateNextBillNumber">生成下一个单据号</button>
  </div>
</template>

<script>
export default {
  props: {
    startNumber: {
      type: Number,
      default: 1,
    },
    length: {
      type: Number,
      default: 6,
    },
  },
  data() {
    return {
      currentNumber: this.startNumber,
    };
  },
  computed: {
    nextBillNumber() {
      return this.currentNumber.toString().padStart(this.length, '0');
    },
  },
  methods: {
    generateNextBillNumber() {
      this.currentNumber += 1;
      this.$emit('update:startNumber', this.currentNumber); // 更新父组件的起始单据号
    },
  },
};
</script>
