<!--
 * @Descripttion: 面包屑公共组件
 * @Version: 1.0
 * @Author: wanglei-2
 * @Date: 2024-04-23 9:37:00
 * @LastEditors: wanglei-2
 * @LastEditTime: 2024-04-23 9:37:00
-->
<template>
  <div class="breadcrumb" style="display: flex; justify-content: space-between; align-items: center">
    <!--        <t-breadcrumb :maxItemWidth="'150'">-->
    <!--          <t-breadcrumbItem>页面1</t-breadcrumbItem>-->
    <!--          <t-breadcrumbItem>页面2面包屑文案超长时悬浮显示文案全部信息</t-breadcrumbItem>-->
    <!--          <t-breadcrumbItem :maxWidth="'160'" :class="'myTitle'">面包屑中文案过长时可缩略显示，鼠标hover时显示全部</t-breadcrumbItem>-->
    <!--        </t-breadcrumb>-->

    <t-breadcrumb :maxItemWidth="'300'">
      <a @click="goBack" class="backA" :class="{ noAllowed: isCanGoBack === false }">
        <span class="backIconSpan">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="22" viewBox="0 0 22 22">
            <path
              d="M21 11H6.414l5.293-5.293-1.414-1.414L2.586 12l7.707 7.707 1.414-1.414L6.414 13H21z"
              :fill="svgColor"
            />
          </svg>
        </span>
        <span class="backSpan">返回</span></a
      >

      <t-breadcrumbItem
        v-for="(v, i) in mybreadList"
        :key="i"
        :class="i === mybreadList.length - 1 ? 'myTitle' : ''"
        @click="syOpen(i)"
        class="t-breadcrumb__item"
        >{{ v }}</t-breadcrumbItem
      >
    </t-breadcrumb>
    <div style="display: flex; width: 415px; align-items: center" v-show="isxlxShow">
      <div style="margin-right: 16px">当前企业</div>
      <div style="width: 325px; margin-right: 16px" class="xlx">
        <!-- <t-select v-model="selectedCompany" @change="companyChanged" :disabled="!disabled">
          <t-option v-for="item in companyList" :value="item.jguuid" :label="item.jgmc" :key="item.jguuid"></t-option>
        </t-select> -->
        <t-select-input
          :value="selectValue"
          :popup-visible="popupVisible"
          :popup-props="{ overlayInnerStyle: { padding: '6px', maxHeight: '500px', overflow: 'auto' } }"
          style="width: 100%"
          clearable
          allow-input
          @popup-visible-change="onPopupVisibleChange"
          @clear="onClear"
          @input-change="onInputChange"
          @focus="onFocus"
          :disabled="!disabled"
        >
          <template #panel>
            <ul class="jgxxSelect">
              <li v-for="item in queryCompanyList" :key="item.value" @click="() => onOptionClick(item)">
                <span
                  :class="{
                    'highlight-option': selectValue.value === item.jguuid,
                  }"
                >
                  {{ item.jgmc }}</span
                >
              </li>
            </ul>
          </template>
          <template #suffixIcon>
            <chevron-down-icon />
          </template>
        </t-select-input>
      </div>
    </div>
  </div>
</template>

<script>
// import { fetch } from '@/core/request';
// import api from '@/pages/index/api/mhsy/mhsyRequest.js';
import { ChevronDownIcon } from 'tdesign-icons-vue';

export default {
  components: { ChevronDownIcon },
  props: {},
  data() {
    return {
      disabled: false,
      selectedCompany: '',
      companyList: [],
      goBackParams: {},
      queryCompanyList: [],
      ysCompanyList: [],
      selectValue: { label: '', value: 1 },
      popupVisible: false,
      mbxUrlList: [],
      xlxShow: false, // 控制是否置灰
      isxlxShow: true, // 控制是否展示
      mybreadList: ['首页', '111', '222'],
      mbxUrlParamsList: [],
      isCanGoBack: false,
      goBackPath: '',
      svgColor: '#333333',
      iframeGoBack: false,
      jgxxMap: {},
    };
  },
  created() {
    const testData = [{"jguuid":"60135693bf4b4d8abe3e296a790980b7","jgmc":"演示集团北京分公司2","xzqhmc":"锡林郭勒盟","nsrsbh":"9144030070846113CS","djxh":"10111525000001030002","xzqhszDm":"152500","qydmz":"","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"f88cea6644264cf8b4a0e5a199bef1d1","jgmc":"1000|浙江森马服饰股份有限公司","xzqhmc":"浙江省","nsrsbh":"91330000736007862B","djxh":"10113303000047418586","xzqhszDm":"330000","qydmz":"1000","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"feb2726dd8354ab1a629706fea443d0e","jgmc":"10001|四川省新晨忆然文化发展有限公司","xzqhmc":"四川省","nsrsbh":"91510321MA6205QK2R","djxh":"10115103010000010576","xzqhszDm":"510000","qydmz":"10001","qylxz":"1","nsrlx":"2","zgswskfjmc":null},{"jguuid":"feb2726dd8354ab1a629706fea443d0x","jgmc":"10002|四川郎酒股份有限公司","xzqhmc":"四川省","nsrsbh":"91510500665362620B","djxh":"10115105000066881849","xzqhszDm":"510000","qydmz":"10002","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"5f5dd426d8ce411db5ccfb99a2491231","jgmc":"11|陕西亿道新能源科技有限公司","xzqhmc":"陕西省","nsrsbh":"91610133MA6TXHHD53","djxh":"10116101000051606503","xzqhszDm":"610000","qydmz":"11","qylxz":"1","nsrlx":"4","zgswskfjmc":null},{"jguuid":"f7cbe421265240648183ed507868fe9d","jgmc":"1100|上海森马服饰有限公司","xzqhmc":"上海市","nsrsbh":"91310112773731630K","djxh":"10013101001120017369","xzqhszDm":"310000","qydmz":"1100","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"5f5dd426d8ce411db5ccfb99a2490e0c","jgmc":"11000|演示集团北京分公司1","xzqhmc":"锡林郭勒盟","nsrsbh":"91310000MA1FL70BCS","djxh":"10111525000001030001","xzqhszDm":"152500","qydmz":"11000","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"znsb_mhqx_jgxxb_uuid_01","jgmc":"1111|千户集团阿里云测试账户（勿删）","xzqhmc":"安徽省","nsrsbh":"91340300711791371R","djxh":"10113403000106935062","xzqhszDm":"340000","qydmz":"1111","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"5c37020b3af04dc99a6b77645d8d7aba","jgmc":"1111|增值税一般纳税人成品油测试企业","xzqhmc":"锡林郭勒盟","nsrsbh":"91310000MA2SB002CS","djxh":"10111525000003330002","xzqhszDm":"152500","qydmz":"1111","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"c3327129cfdf4645b91a78c390ff4106","jgmc":"1111|安徽测试","xzqhmc":"安徽省","nsrsbh":"91340100790122917R","djxh":"1021000000078857","xzqhszDm":"340000","qydmz":"1111","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"0488f98ef802439fb28efb41423e3392","jgmc":"1111|安徽测试2","xzqhmc":"安徽省","nsrsbh":"91341024151783006H","djxh":"10213410000000135383","xzqhszDm":"340000","qydmz":"1111","qylxz":"1","nsrlx":"2","zgswskfjmc":null},{"jguuid":"fb387d5b03b04da6b863aa98b2f2d271","jgmc":"1111|爱立信（中国）通信有限公司西安分公司","xzqhmc":"广东省","nsrsbh":"91610000773814479K","djxh":"10116101000051858660","xzqhszDm":"440000","qydmz":"1111","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sichuanlqcs000000000000000000003","jgmc":"1234321|南充南百大珠宝有限公司","xzqhmc":"四川省","nsrsbh":"91511302791806982U","djxh":"10115113000069417805","xzqhszDm":"510000","qydmz":"1234321","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"sichuanlqcs000000000000000000005","jgmc":"1234321|四川大学华西口腔医院","xzqhmc":"四川省","nsrsbh":"12510000450755937Y","djxh":"10115190000068040377","xzqhszDm":"510000","qydmz":"1234321","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_001","jgmc":"20001|中国移动通信集团山西有限公司大同分公司","xzqhmc":"上海市","nsrsbh":"91140200743512277T","djxh":"10215101000002288881","xzqhszDm":"310000","qydmz":"20001","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_002","jgmc":"20002|中国移动通信集团山西有限公司阳泉分公司","xzqhmc":"上海市","nsrsbh":"9114000074351837XM","djxh":"10215101000002288882","xzqhszDm":"310000","qydmz":"20002","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_003","jgmc":"20003|中国移动通信集团山西有限公司长治分公司","xzqhmc":"上海市","nsrsbh":"91140000741095553D","djxh":"10215101000002288883","xzqhszDm":"310000","qydmz":"20003","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_004","jgmc":"20004|中国移动通信集团山西有限公司晋城分公司","xzqhmc":"上海市","nsrsbh":"91140000741095764W","djxh":"10215101000002288884","xzqhszDm":"310000","qydmz":"20004","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_005","jgmc":"20005|中国移动通信集团山西有限公司忻州分公司","xzqhmc":"上海市","nsrsbh":"91140000741095721F","djxh":"10215101000002288885","xzqhszDm":"310000","qydmz":"20005","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_006","jgmc":"20006|中国移动通信集团山西有限公司晋中分公司","xzqhmc":"上海市","nsrsbh":"91140000743502490G","djxh":"10215101000002288886","xzqhszDm":"310000","qydmz":"20006","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_007","jgmc":"20007|中国移动通信集团山西有限公司临汾分公司","xzqhmc":"上海市","nsrsbh":"91140000741096177C","djxh":"10215101000002288887","xzqhszDm":"310000","qydmz":"20007","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_008","jgmc":"20008|中国移动通信集团山西有限公司运城分公司","xzqhmc":"上海市","nsrsbh":"91140800741091747G","djxh":"10215101000002288888","xzqhszDm":"310000","qydmz":"20008","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_009","jgmc":"20009|中国移动通信集团山西有限公司太原分公司","xzqhmc":"上海市","nsrsbh":"911400007435195673","djxh":"10215101000002288889","xzqhszDm":"310000","qydmz":"20009","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"sxyd_jguuid_000","jgmc":"304110|中国移动通信集团山西有限公司","xzqhmc":"上海市","nsrsbh":"91140000710937455Q","djxh":"11140098000000167212","xzqhszDm":"310000","qydmz":"304110","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"74e7aac50b944e5fbbda572590644b8c","jgmc":"3100|武汉森意服饰有限公司","xzqhmc":"湖北省","nsrsbh":"91420106MA4KYE5082","djxh":"10114201010000252007","xzqhszDm":"420000","qydmz":"3100","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"1febd5acb72a4635a1e73feee87bd3f7","jgmc":"3102|武汉森意服饰有限公司金桥店","xzqhmc":"湖北省","nsrsbh":"91420102MA4K47R482","djxh":"10214201000000062732","xzqhszDm":"420000","qydmz":"3102","qylxz":"1","nsrlx":"1","zgswskfjmc":null},{"jguuid":"e4bb6bec24e94a37ae04e0d15c342087","jgmc":"7700|上海出森入花服饰有限公司","xzqhmc":"上海市","nsrsbh":"91310112MAD33XQU0M","djxh":"10213101000000921054","xzqhszDm":"310000","qydmz":"7700","qylxz":"1","nsrlx":null,"zgswskfjmc":null},{"jguuid":"558bbbaf458f47bfb6a61b94f7dc4524","jgmc":"8901|江苏森马服饰有限公司邗江中路店","xzqhmc":"江苏省","nsrsbh":"91321003MAEJYNCM4D","djxh":"10213210000000325775","xzqhszDm":"320000","qydmz":"8901","qylxz":"1","nsrlx":"2","zgswskfjmc":null}];
    window.sessionStorage.setItem('companyList', JSON.stringify(testData));
    const jgxxList = {"jguuid":"f7cbe421265240648183ed507868fe9d","jgmc":"1100|上海森马服饰有限公司","xzqhmc":"上海市","nsrsbh":"91310112773731630K","djxh":"10013101001120017369","xzqhszDm":"310000","qydmz":"1100","qylxz":"1","nsrlx":"1","zgswskfjmc":null};
    window.sessionStorage.setItem('jgxxList', JSON.stringify(jgxxList));
  },
  mounted() {
    // 在组件加载完成后，调用子组件方法
  },
  methods: {
    syOpen(i) {
      if (i === this.mybreadList.length - 1) {
        return;
      }
      if (i === 0) {
        window.parent.gotoHomePage();
      } else if (window.parent) {
        const menuParams = {};
        menuParams.menuPath = this.mbxUrlList[i - 1]; // 路径
        if (this.mbxUrlParamsList) {
          if (this.mbxUrlParamsList[i - 1]) {
            const keys = Object.keys(this.mbxUrlParamsList[i - 1]);
            const values = Object.values(this.mbxUrlParamsList[i - 1]);
            menuParams.menuParmasStr = `&${keys.map((element, index) => `${element}=${values[index]}`).join('&')}`;
          }
        }

        window.parent.goSelfChange(menuParams);
      }
    },
    onPopupVisibleChange(val) {
      console.log(val);
      this.popupVisible = val;
    },
    initMyBre(params) {
      this.mybreadList = params.mybreadList;
      this.companyList = params.companyList;
      this.selectedCompany = params.selectedCompany;
      this.disabled = params.xlxShow;
      this.mbxUrlList = params.mbxUrlList;
      this.goBackParams = params.goBackParams;
      this.iframeGoBack = params.iframeGoBack;
      this.isxlxShow = params.isxlxShow;
      if (this.isxlxShow === undefined) {
        this.isxlxShow = true;
      }

      // 是否有测试数据
      if (!this.companyList || this.companyList.length === 0) {
        this.queryCompanyList = JSON.parse(window.sessionStorage.getItem('companyList'));
        this.ysCompanyList = JSON.parse(window.sessionStorage.getItem('companyList'));
      }
      if (!this.selectedCompany) {
        if (window.sessionStorage.getItem('jgxxList')) {
          this.jgxxMap = JSON.parse(window.sessionStorage.getItem('jgxxList'));
          this.selectValue.label = this.ysCompanyList.find((t) => t.jguuid === this.jgxxMap.jguuid)?.jgmc;
          this.selectValue.value = this.jgxxMap.jguuid;
        }
      } else {
        this.selectValue.label = this.ysCompanyList.find((t) => t.jguuid === this.selectedCompany)?.jgmc;
        this.selectValue.value = this.selectedCompany;
      }
      if (params.isCanGoBack === true || params.isCanGoBack === false) {
        this.isCanGoBack = params.isCanGoBack;
      }
      this.goBackPath = params.goBackPath;
      if (this.isCanGoBack === true) {
        // 可以后退的颜色
        this.svgColor = '#333333';
      } else {
        // 不能后退的颜色
        this.svgColor = '#E1E1E2';
      }
    },
    onInputChange(val, context) {
      // 过滤功能
      if (val) {
        this.queryCompanyList = this.ysCompanyList.filter((item) => {
          return item.jgmc.includes(val);
        });
      } else {
        this.queryCompanyList = JSON.parse(window.sessionStorage.getItem('companyList'));
      }
      console.log(val, context);
    },
    onClear() {
      this.selectValue = { label: '', value: '' };
    },
    goBack() {
      // 回退方法
      if (this.isCanGoBack === true) {
        // 触发回退操作
        // this.$router.push(this.goBackPath);
        // 自刷新
        if (this.iframeGoBack) {
          window.parent.gotoHomePage();
        } else if (window.parent) {
          const menuParams = {};
          menuParams.menuPath = this.goBackPath; // 路径
          if (this.goBackParams) {
            const keys = Object.keys(this.goBackParams);
            const values = Object.values(this.goBackParams);
            menuParams.menuParmasStr = `&${keys.map((element, index) => `${element}=${values[index]}`).join('&')}`;
          }
          // menuParams.menuParmasStr = `&djxh=${row.djxh}&skssqq=`+this.rqFromat(row.skssqq)+`&skssqz=`+this.rqFromat(row.skssqz)+`&nsrsbh=${row.nsrsbh}&zsxmDm=${row.zsxmDm}&xzqhszDm=${row.xzqhszDm}&sbrwuuid=${row.sbrwuuid}&jguuid=${this.jguuid}`;//参数
          window.parent.goSelfChange(menuParams);
        }
      }
    },
    onFocus(val, context) {
      console.log('focus:', val, context);
    },
    onOptionClick(item) {
      this.selectValue.label = item.jgmc;
      this.selectValue.value = item.jguuid;
      this.companyChanged(item);
      this.popupVisible = false;
    },
    async companyChanged(item) {
      const { jguuid } = item;
      let params = [];
      params = this.ysCompanyList.filter((item) => {
        return item.jguuid === jguuid;
      });
      if (params.jgmc && params.jgmc.includes('|')) {
        [, params.jgmc] = params.jgmc.split('|');
      }
      this.$emit('jguuidOnChange', params);
    },
    setXlxShow(params) {
      this.disabled = params;
    },
  },
};
</script>

<style lang="less" scoped>
.xlx /deep/ .t-input.t-is-readonly {
  width: 340px;
}
.breadcrumb {
  background-color: #fff;
}
.t-breadcrumb {
  height: 60px;
  padding-left: 30px;
  /deep/.t-breadcrumb__item {
    font-family: PingFangSC;
    color: #4285f4;
    span {
      cursor: pointer;
    }
  }
  /deep/.myTitle.t-breadcrumb__item {
    font-family: PingFangSC;
    color: #333;
  }
  .myTitle :hover {
    color: #333;
    cursor: not-allowed;
  }
  .backA {
    height: 28px;
    line-height: 28px;
    text-decoration: none;
    cursor: pointer;
    .backIconSpan {
      width: 25px;
      height: 28px;
      font-size: 20px;
      line-height: 28px;
    }
    svg {
      position: relative;
      top: 2px;
      left: 4px;
    }
    .backSpan {
      position: relative;
      top: -1px;
      left: 0;
      width: 40px;
      height: 28px;
      margin: 0 30px 0 10px;
      font-family: PingFangSC-Medium;
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
      letter-spacing: 0;
      color: #333;
    }
  }
  .backA.noAllowed {
    cursor: not-allowed;
    svg {
      color: #e1e1e2;
    }
    .backSpan {
      color: #e1e1e2;
    }
  }
}
/deep/.t-input.t-is-disabled {
  background-color: #eee !important;
  border: 1px solid #dcdcdc;
}

.jgxxSelect {
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 2px;
}
.jgxxSelect > li {
  display: block;
  padding: 3px 8px;
  overflow: hidden;
  line-height: 22px;
  color: var(--td-text-color-primary);
  text-overflow: ellipsis;
  word-wrap: normal;
  white-space: nowrap;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s linear;
}

.jgxxSelect > li:hover {
  background-color: var(--td-bg-color-container-hover);
}

.highlight-option {
  font-weight: 500;
  color: #0052d9;
  background-color: #f2f3ff;
}
</style>
