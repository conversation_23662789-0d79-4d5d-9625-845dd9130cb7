<template>
  <div class="znsbBackGroupDiv">
    <div class="skeleton-wrap">
      <div class="content">
        <t-skeleton :rowCol="rowCol"></t-skeleton>
      </div>
      <t-row>
        <t-col :span="12" v-for="(_, index) in 10" :key="index">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '10px 20px' }]]"
        /></t-col>
        <t-col :span="12">
          <t-skeleton animation="gradient" :row-col="[[{ height: '24px', margin: '10px 20px 20px 20px' }]]"
        /></t-col>
      </t-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'show-table',
  data() {
    return {
      rowCol: [
        [
          {
            type: 'rect',
            width: '60%',
            height: '125px',
            margin: '10px 20px',
          },
          {
            type: 'rect',
            width: '25%',
            height: '125px',
            margin: '10px 20px',
          },
          {
            type: 'rect',
            width: '15%',
            height: '125px',
            margin: '10px 20px',
          },
        ],
        [
          {
            type: 'rect',
            width: '60%',
            height: '125px',
            margin: '10px 20px',
          },
          {
            type: 'rect',
            width: '25%',
            height: '125px',
            margin: '10px 20px',
          },
          {
            type: 'rect',
            width: '15%',
            height: '125px',
            margin: '10px 20px',
          },
        ],
      ],
    };
  },
};
</script>

<style lang="less" scoped>
@import '../../styles/sbPageGy.less';
.skeleton-wrap {
  // width: 100%;
  // height: 100%;
  padding: 10px;
  // box-sizing: border-box;
  background: #fff;
}
</style>
