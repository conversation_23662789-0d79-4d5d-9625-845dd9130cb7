<!--
 * @Descripttion: 面包屑公共组件
 * @Version: 1.0
 * @Author: wanglei-2
 * @Date: 2024-04-23 9:37:00
 * @LastEditors: wanglei-2
 * @LastEditTime: 2024-04-23 9:37:00
-->
<template>
  <div>
    <!--        <t-breadcrumb :maxItemWidth="'150'">-->
    <!--          <t-breadcrumbItem>页面1</t-breadcrumbItem>-->
    <!--          <t-breadcrumbItem>页面2面包屑文案超长时悬浮显示文案全部信息</t-breadcrumbItem>-->
    <!--          <t-breadcrumbItem :maxWidth="'160'" :class="'myTitle'">面包屑中文案过长时可缩略显示，鼠标hover时显示全部</t-breadcrumbItem>-->
    <!--        </t-breadcrumb>-->

    <t-breadcrumb :maxItemWidth="'150'">
      <a @click="goBack" class="backA" :class="{ noAllowed: isCanGoBack === false }">
        <span class="backIconSpan">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="22" viewBox="0 0 22 22">
            <path
              d="M21 11H6.414l5.293-5.293-1.414-1.414L2.586 12l7.707 7.707 1.414-1.414L6.414 13H21z"
              :fill="svgColor"
            />
          </svg>
        </span>
        <span class="backSpan">返回</span></a
      >

      <t-breadcrumbItem v-for="(v, i) in mybreadList" :key="i" :class="i === mybreadList.length - 1 ? 'myTitle' : ''">{{
        v
      }}</t-breadcrumbItem>
    </t-breadcrumb>
  </div>
</template>

<script>
// import { fetch } from '@/core/request';

export default {
  components: {},
  props: {},
  data() {
    return {
      mybreadList: ['首页', '111', '222'],
      isCanGoBack: false,
      goBackPath: '',
      svgColor: '#333333',
    };
  },
  created() {},
  mounted() {
    // 在组件加载完成后，调用子组件方法
  },
  methods: {
    initMyBre(params) {
      this.mybreadList = params.mybreadList;
      if (params.isCanGoBack === true || params.isCanGoBack === false) {
        this.isCanGoBack = params.isCanGoBack;
      }
      this.goBackPath = params.goBackPath;
      if (this.isCanGoBack === true) {
        // 可以后退的颜色
        this.svgColor = '#333333';
      } else {
        // 不能后退的颜色
        this.svgColor = '#E1E1E2';
      }
    },
    goBack() {
      // 回退方法
      if (this.isCanGoBack === true) {
        // 触发回退操作
        this.$router.push(this.goBackPath);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.t-breadcrumb {
  height: 60px;
  padding-left: 30px;
  background-color: white;
  /deep/.t-breadcrumb__item {
    font-family: PingFangSC;
    color: #4285f4;
    span {
      cursor: default;
    }
  }
  /deep/.myTitle.t-breadcrumb__item {
    font-family: PingFangSC;
    color: #333;
  }
  .backA {
    height: 28px;
    line-height: 28px;
    text-decoration: none;
    cursor: pointer;
    .backIconSpan {
      width: 25px;
      height: 28px;
      font-size: 20px;
      line-height: 28px;
    }
    svg {
      position: relative;
      top: 2px;
      left: 4px;
    }
    .backSpan {
      position: relative;
      top: -1px;
      left: 0;
      width: 40px;
      height: 28px;
      margin: 0 30px 0 10px;
      font-family: PingFangSC-Medium;
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
      letter-spacing: 0;
      color: #333;
    }
  }
  .backA.noAllowed {
    cursor: not-allowed;
    svg {
      color: #e1e1e2;
    }
    .backSpan {
      color: #e1e1e2;
    }
  }
}
</style>
