<template>
  <t-dialog
    :header="false"
    :visible.sync="visible"
    :closeOnOverlayClick="otherProps.closeOnOverlayClick || false"
    :onEscKeydown="hideConfirm"
    :closeBtn="isCloseBtn"
    attach="body"
    destroyOnClose
    v-bind="otherProps"
  >
    <template #body>
      <div class="dialog_body__wrap">
        <div class="dialog_body__pb16">
          <t-icon
            :name="iconProp.name || 'info-circle-filled'"
            :size="iconProp.size || '3em'"
            :style="{ color: iconProp.color || '#4285f4' }"
          />
        </div>
        <div class="dialog_body__title dialog_body_font__black" v-if="isShowConfirmTitle">
          {{ confirmTitle }}
        </div>
        <slot name="confirmContent">
          <div class="dialog_body_font__grey" v-if="contentIsString">{{ confirmContent }}</div>
          <template v-else>
            <div class="dialog_body_font__grey" v-for="(item, index) in confirmContent" :key="index">
              {{ item }}
            </div>
          </template>
        </slot>
        <div class="dialog_body_font__black" v-if="otherConfirm">{{ otherConfirm }}</div>
      </div>
    </template>
    <template #footer>
      <div class="dialog_footer__wrap" v-if="showFooter">
        <t-button @click="onCancel" theme="default" v-if="showCancel">{{ cancelText }}</t-button>
        <t-button @click="onConfirm" v-if="showConfirms">{{ confirmText }}</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script>
export default {
  props: {
    iconProp: {
      type: Object,
      default: () => ({}),
    },
    confirmTitle: {
      type: String,
      default: '提示',
    },
    isShowConfirmTitle: {
      type: Boolean,
      default: true,
    },
    isCloseBtn: {
      type: Boolean,
      default: true,
    },
    otherConfirm: {
      type: String,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showConfirms: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    confirmText: {
      type: String,
      default: '确定',
    },
    confirmContent: {
      type: [String, Array],
    },
    otherProps: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    contentIsString() {
      return Object.prototype.toString.call(this.confirmContent) === '[object String]';
    },
  },
  data() {
    return {
      visible: false,
    };
  },

  methods: {
    showConfirm() {
      this.visible = true;
    },
    hideConfirm() {
      this.visible = false;
    },
    async onCancel() {
      this.hideConfirm();
      setTimeout(() => {
        this.$emit('cancel');
      });
    },
    async onConfirm() {
      this.hideConfirm();
      setTimeout(() => {
        this.$emit('confirm');
      });
    },
  },
};
</script>

<style lang="less" scoped>
.dialog_body__wrap {
  text-align: center;
  .dialog_body__pb16 {
    padding-bottom: 16px;
  }
  .dialog_body__title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
  }
  .dialog_body_font__grey {
    padding: 0 16px;
    color: #666;
  }
  .dialog_body_font__black {
    color: #333;
  }
}
.dialog_footer__wrap {
  text-align: center;
}
</style>
