<template>
  <div>
    <div @click="showConfirm">显示确认框</div>
    <common-confirm ref="commonConfirm" @confirm="onConfirm">
      <template #confirmContent>
        <div>主体提示内容主体提示内容主体提示内容主体提示内容主体提示内容</div>
      </template>
    </common-confirm>
  </div>
</template>
<script>
import commonConfirm from '@/pages/index/components/common-confirm';

export default {
  components: {
    commonConfirm,
  },
  data() {
    return {};
  },

  methods: {
    showConfirm() {
      this.$refs.commonConfirm.showConfirm();
    },
    onConfirm() {
      // 确认操作函数
    },
  },
};
</script>
