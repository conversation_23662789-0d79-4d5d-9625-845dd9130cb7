<template>
  <div class="footerbar">
    <gt-space size="10px">
      <t-button v-if="!isFtBj" theme="primary" variant="outline" @click="onBack">退出</t-button>
      <t-button v-if="!isFtBj" theme="primary" :disabled="declareDisabled" @click="toDeclare">提交</t-button>
      <t-button v-if="isFtBj" theme="primary" @click="onBack">跳转申报</t-button>
    </gt-space>
  </div>
</template>

<script>
import { MessagePlugin } from 'tdesign-vue';

export default {
  name: 'sbb-bottom-submit',
  props: {
    /**
     * 申报提交按钮是否置灰
     */
    declareDisabled: {
      type: Boolean,
      default: false,
    },
    isFtBj: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    onBack() {
      // 取消申报 跳转参数待定
      this.$emit('quitCallBackListener', (val) => {
        this.$EventBus.$emit('querySbxxByZsxm', val, '1');
      });
      this.$router.push('/hjbhsfh');
    },
    toDeclare() {
      // console.log(111);
      let sbDataList = [];
      this.$emit('submitClickListener', (val) => {
        if (val) {
          sbDataList = val;
          if (sbDataList.length <= 0) {
            MessagePlugin.info('提交的数据不能为空');
            return;
          }
          // 待调整
          if (sbDataList[0]?.zsxmdm === '10114' && sbDataList[0]?.syuuidList.length <= 0) {
            MessagePlugin.warning('请选择税源信息');
            return;
          }
          this.$EventBus.$emit('querySbxxByZsxm', sbDataList, '2');
          this.$router.push('/hjbhsfh');
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.footerbar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2499;
  width: 100%;
  height: 64px;
  line-height: 64px;
  text-align: center;
  background-color: white;
  box-shadow: 0 -2px 5px 0 rgba(6, 13, 97, 0.1);
}
</style>
