<template>
  <div class="gov-validate-dialog">
    <div
      v-show="sortValidateRules.length"
      ref="maximum"
      :class="[
        'maximum',
        {
          minimum: !maximum,
        },
      ]"
    >
      <div ref="maximum-header" class="gov-validate-dialog-header">
        <span v-show="maximum" ref="maximum-header-title" style="margin-right: 16px">校验问题:</span>
        <span :style="maximum ? 'margin-right: 40px' : 'margin-right: 0; width: 58px'">
          <error-circle-filled-icon color="#ed5353" size="16" style="margin-right: 4px" />
          {{ sortValidateRules.filter((r) => r.type === 'error').length }}
        </span>
        <span :style="maximum ? 'margin-right: 40px' : 'margin-right: 0; width: 58px'">
          <error-circle-filled-icon color="#ff9838" size="16" style="margin-right: 4px" />
          {{ sortValidateRules.filter((r) => r.type === 'warning').length }}
        </span>
        <span v-show="maximum" class="pagination">
          <caret-left-small-icon
            size="22"
            :color="curIndex === 0 ? '#666' : '#4285f4'"
            style="float: left"
            @click="prev"
          />
          <span style="float: left">{{ curIndex + 1 }} / {{ sortValidateRules.length }}</span>
          <caret-right-small-icon
            size="22"
            :color="curIndex === sortValidateRules.length - 1 ? '#666' : '#4285f4'"
            style="float: left"
            @click="next"
          />
        </span>
        <div class="operation">
          <gt-space :size="12">
            <div v-if="maximum" class="operator-section">
              <remove-icon v-if="multiple" @click="toggleMultiple" />
              <format-vertical-align-left-icon v-else @click="toggleMultiple" />
            </div>
            <div v-if="maximum" class="operator-section">
              <chevron-down-icon @click="toggleMaximum(false)" />
            </div>
            <div v-show="!maximum" class="operator-section">
              <chevron-right-icon size="20" @click="toggleMaximum(true)" />
            </div>
          </gt-space>
        </div>
      </div>
      <div ref="scroller" class="gov-validate-dialog-content">
        <ul style="list-style: none">
          <li
            v-for="(obj, index) in sortValidateRules"
            v-show="multiple || (!multiple && index === curIndex)"
            :key="index"
            :class="[
              'rule',
              {
                active: curIndex === index,
              },
            ]"
            @click="onRuleClick(index, obj)"
          >
            <div class="serial">
              <span>
                {{ (multiple ? index : curIndex) + 1 }}
              </span>
              <error-circle-filled-icon
                v-show="obj.type === 'error'"
                color="#ed5353"
                size="16"
                style="margin-right: 14px; vertical-align: middle"
              />
              <error-circle-filled-icon
                v-show="obj.type === 'warning'"
                color="#ff9838"
                size="16"
                style="margin-right: 14px; vertical-align: middle"
              />
            </div>
            <span class="content">
              {{ obj.content }}
              <span class="handleMsg" v-if="handleMsg" @click="$emit('handleMsg', obj)">{{ obj.handleMsg }}</span>
              {{ obj.extraContent }}
              <span class="handleMsg" v-if="extraHandleMsg" @click="$emit('extraHandleMsg', obj)">{{
                obj.extraHandleMsg
              }}</span>
              {{ obj.finalContent }}
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';

import './TweenMax.min.js';

import {
  CaretLeftSmallIcon,
  CaretRightSmallIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  CloseIcon,
  ErrorCircleFilledIcon,
  FormatVerticalAlignLeftIcon,
  RemoveIcon,
} from 'tdesign-icons-vue';

const duration = 0.2;

const advance = '-=0.1';

const headerHeight = 46;

/**
 * 信息窗口
 * @category Layout
 * @cover ValidateDialog
 */
export default {
  name: 'gt-validate-dialog',
  // timeline: null,
  components: {
    // eslint-disable-next-line vue/no-unused-components
    CloseIcon,
    RemoveIcon,
    FormatVerticalAlignLeftIcon,
    ChevronDownIcon,
    ErrorCircleFilledIcon,
    CaretLeftSmallIcon,
    CaretRightSmallIcon,
    ChevronRightIcon,
    // UnfoldLessIcon,
    // UnfoldMoreIcon,
    // InfoCircleFilledIcon,
    // MenuFoldIcon,
    // MenuUnfoldIcon,
  },
  props: {
    /**
     * 提示内容：[{type, content, ...}, ...]
     */
    validateRules: {
      type: Array,
      default: () => [],
    },
    /**
     * 是否自动定位第1条提示
     */
    autoPosition: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否文字点击事件
     */
    handleMsg: {
      type: [Boolean, String],
      default: false,
    },
    /**
     * 是否包含额外的文字点击事件
     */
    extraHandleMsg: {
      type: [Boolean, String],
      default: false,
    },
  },
  data() {
    return {
      multiple: true,
      maximum: true,
      curIndex: 0,
    };
  },
  computed: {
    sortValidateRules() {
      return [...this.validateRules].sort((a, b) =>
        // eslint-disable-next-line no-nested-ternary
        a.type !== b.type
          ? a.type === 'error' && b.type === 'warning'
            ? -1
            : 1
          : Number(a.messagePriority || 0) - Number(b.messagePriority || 0),
      );
    },
  },
  watch: {
    curIndex: {
      immediate: true,
      handler(val) {
        const { scroller } = this.$refs;
        const liList = scroller?.children[0].children || [];
        // 计算需要滚动的高度
        const height =
          new Array(val).fill(1).reduce((res, cur, index) => {
            console.log(liList[index]?.offsetHeight);
            return res + (liList[index]?.offsetHeight || 0);
          }, 0) || 0;
        scroller?.scrollTo({ top: height, behavior: 'smooth' });
        // val >= 0 && this.toggleRule(this.sortValidateRules[val] || null);
      },
    },
    sortValidateRules(val) {
      // 校验结果变化需要重新回到顶部
      this.curIndex = 0;
      if (this.autoPosition) {
        this.toggleRule(val[this.curIndex] || null);
      }
      this.$nextTick(() => {
        this.$emit('toggle', this.maximum, this.$refs.maximum.offsetHeight);
      });
    },
  },
  methods: {
    toggleMultiple() {
      this.multiple = !this.multiple;
    },
    toggleMaximum(bool) {
      const vm = this;
      const refsMaximum = this.$refs.maximum;
      const refsHeader = this.$refs['maximum-header'];
      const refsHeaderTitle = this.$refs['maximum-header-title'];
      if (bool) {
        const timeline = new global.TimelineMax({
          onStart() {
            refsMaximum.style.display = '';
          },
          onComplete() {
            vm.maximum = true;
            vm.$emit('toggle', true, refsMaximum.offsetHeight);
          },
        });
        timeline
          .to(refsMaximum, duration, {
            bezier: [
              {
                marginLeft: '20px',
                width: '50%',
                maxHeight: '88px',
                height: '44px',
              },
              {
                marginLeft: '0',
                width: '100%',
                maxHeight: '212px',
                height: 'auto',
              },
            ],
            // ease: global.Power1.easeInOut,
          })
          .to(
            refsHeader,
            0.1,
            {
              padding: '12px 40px',
            },
            advance,
          )
          .to(
            refsHeaderTitle,
            0.1,
            {
              display: 'inline-block',
            },
            advance,
          );
      } else {
        const timeline = new global.TimelineMax({
          onStart() {
            vm.$emit('toggle', false, headerHeight);
          },
          onComplete() {
            vm.maximum = false;
          },
        });
        timeline
          .to(refsMaximum, duration, {
            bezier: [
              {
                marginLeft: '80px',
                width: '50%',
                maxHeight: `${headerHeight}px`,
              },
              {
                marginLeft: '40px',
                width: '181px',
                maxHeight: `${headerHeight}px`,
              },
            ],
            // ease: global.Power1.easeInOut,
          })
          .to(
            refsHeader,
            0.1,
            {
              padding: '16px',
            },
            advance,
          )
          .to(
            refsHeaderTitle,
            0.1,
            {
              display: 'none',
            },
            advance,
          );
      }
    },
    onRuleClick(index) {
      this.curIndex = index;
      this.toggleRule(this.sortValidateRules[index] || null);
    },
    toggleRule(obj) {
      /**
       * 提示激活回调
       *
       * @event ruleClick
       * @property {Object} obj 被激活的规则信息
       */
      this.$emit('ruleClick', { ...obj, timestamp: +dayjs() });
    },
    prev() {
      // this.curIndex = this.curIndex === 0 ? this.validateRules.length - 1 : this.curIndex - 1;
      if (this.curIndex > 0) {
        this.curIndex -= 1;
      }
    },
    next() {
      // this.curIndex = this.curIndex === this.validateRules.length - 1 ? 0 : this.curIndex + 1;
      if (this.curIndex < this.sortValidateRules.length - 1) {
        this.curIndex += 1;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.gov-validate-dialog {
  position: relative;

  .t-icon {
    vertical-align: text-bottom;
    cursor: pointer;
  }

  .maximum {
    // position: fixed;
    // bottom: 64px;
    // left: 0;
    position: absolute;
    bottom: 0;
    width: 100%;
    max-height: 212px;
    min-width: 181px;
    overflow: hidden;
    box-shadow: 0 -4px 10px 0 rgba(6, 13, 97, 0.1);
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .minimum {
    border: 1px solid rgba(39, 40, 46, 0.2);
    border-bottom: 0;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  &-header {
    height: 46px;
    padding: 12px 40px;
    background: #fff;
    // box-shadow: 0 -4px 10px 0 rgba(6, 13, 97, 0.1);
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);

    & > span {
      display: inline-block;
      height: 22px;
      vertical-align: middle;
    }

    .pagination {
      margin-left: 40px;

      & > span {
        display: inline-block;
        margin: 0 16px;
      }
    }

    .operation {
      float: right;

      .operator-section {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: #f7f8fa;
        border-radius: 3px;

        > .t-icon {
          font-size: 24px;
        }
      }
    }
  }

  &-content {
    max-height: 166px;
    padding: 16px;
    overflow-y: scroll;
    background: #fafafa;
    // box-shadow: 0 -4px 10px 0 rgba(6, 13, 97, 0.1);
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);

    li.rule {
      position: relative;
      padding: 8px 16px;
      cursor: pointer;

      span {
        display: inline-block;
        line-height: 20px;
        //vertical-align: middle;
      }

      .serial {
        position: absolute;
        width: 70px;

        span {
          display: inline;
          width: 22px;
          margin-right: 12px;
        }
      }

      .content {
        margin-left: 70px;
      }
      .handleMsg {
        color: #0052d9;
      }
    }

    li.active {
      background: rgba(210, 231, 251, 0.6);
      border-radius: 3px;
    }
  }
}
</style>

<style scoped lang="less">
/* stylelint-disable-next-line no-duplicate-selectors */
.gov-validate-dialog-content {
  &::-webkit-scrollbar {
    width: 16px;
    height: 16px;

    &-corner {
      background-color: transparent;
    }

    &-thumb {
      background-color: var(--td-scrollbar-color);
      border: 5px solid transparent;
      border-radius: 11px;
      background-clip: content-box;

      &:hover {
        background-color: #999;
      }
    }
  }
}
</style>
