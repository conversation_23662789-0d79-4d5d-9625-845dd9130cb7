<template>
  <div class="header_common">
    <!--<div v-show="qhzzShow">
      <t-dialog style="display: block" @confirm="onConfirm">
        <div v-for="item in zzList" :key="item.zzid">
          <t-radio-group v-model="value">
            <t-radio :value="item.zzid">{{ item.zzmc }}</t-radio>
          </t-radio-group>
        </div>
      </t-dialog>
    </div>-->

    <ul style="display: flex; height: 100%; justify-content: space-between; align-items: center">
      <div class="header_logo">
        <img style="width: 29px; height: 23px" :src="sylogo" class="header_logo_pic" />
        <span style="font-weight: bolder">企业乐享平台</span>
      </div>
      <div style="height: 100%; margin-left: -20%">
        <span class="menuItem" href="/mhzc/mhsy" style="padding-top: 10px; padding-bottom: -10px">
          <t-icon name="creditcard" size="large" style="margin-top: -1px; margin-right: 8px" />
          <span style="font-size: 16px" @click="sy">首页</span></span
        >
        <div
          v-for="one in menuVO.oneMenus"
          :key="one.gnDm"
          :class="{ menuItem: true, menuisactive: tabActiveIndex === one.gnDm }"
          @mouseover="changeTopTab(one.gnDm)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <div style="padding-top: 10px; padding-bottom: -10px; margin-right: 8px; margin-left: 8px; font-size: 16px">
              <t-icon :name="one.icon" size="large" style="margin-right: 8px" />
              {{ one.gnmc }}
            </div>
          </div>
        </div>
      </div>

      <div class="user_area">
        <div>
          <span class="user_function">【{{ yhxxVO.zzjg }}】&nbsp;{{ yhxxVO.xm }}&nbsp;&nbsp;&nbsp;&nbsp;</span>
          <t-badge size="small" :count="xxtxCount" :offset="[25, 5]" style="float: left">
            &nbsp;&nbsp;&nbsp;&nbsp;<img
              src="../../assets/txld.png"
              class="user_arrow user_function"
              @mouseover="openXxzx"
            />
          </t-badge>
          <img :src="yhxxVO.yhtx" class="user_pic user_function" @click="openUser" @mouseover="openUser" />
        </div>
        <div
          ref="box"
          class="xxzx"
          v-show="xxzxShow"
          @mouseover="
            {
              xxzxShow = true;
            }
          "
          @mouseleave="headMouseLeave"
        >
          <Xxzx></Xxzx>
        </div>
        <div
          class="user_info_div"
          ref="box"
          v-show="userShow"
          @mouseover="
            {
              userShow = true;
            }
          "
          @mouseleave="headMouseLeave"
        >
          <div class="grxx">
            <div style="margin-left: 20px">
              <span style="color: black"
                ><span style="font-size: larger; font-weight: bold; color: black">{{ yhxxVO.xm }}</span
                ><br />{{ yhxxVO.zzjg }}&nbsp;-&nbsp;{{ yhxxVO.yhjs }}</span
              >
            </div>
          </div>
          <h6 style="height: 40px; padding-left: 32px">
            <img src="../../assets/qhzz.png" style="margin-right: 8px" /><span>切换组织</span>
          </h6>
          <h6 style="height: 40px; padding-left: 32px">
            <img src="../../assets/grsz.png" style="margin-right: 8px" /><span @click="grsz">个人设置</span>
          </h6>
          <h6 style="height: 40px; padding-left: 32px">
            <img src="../../assets/gxsz.png" style="margin-right: 8px" /><span>个性设置</span>
          </h6>
          <h6 style="height: 40px; padding-left: 32px" @click="showXtgl">
            <img src="../../assets/yhsz.png" style="margin-right: 8px" /><span>系统管理</span><img :src="xtglImg" />
          </h6>
          <div v-show="xtglShow">
            <h6 @click="zzjggl" style="height: 40px; padding-left: 48px">
              <img src="../../assets/zzjggl.png" style="margin-right: 8px" /><span>组织结构管理</span>
            </h6>
            <h6 @click="jsqxgl" style="height: 40px; padding-left: 48px">
              <img src="../../assets/jsqxgl.png" style="margin-right: 8px" /><span>角色权限管理</span>
            </h6>
            <h6 @click="yhgl" style="height: 40px; padding-left: 48px">
              <img src="../../assets/yhgl.png" style="margin-right: 8px" /><span>用户管理</span>
            </h6>
            <h6 @click="glqyfp" style="height: 40px; padding-left: 48px">
              <img src="../../assets/glqyfp.png" style="margin-right: 8px" /><span>管理企业分配</span>
            </h6>
          </div>
          <div class="user_info_manage">
            <!-- <img src="../../assets/tcdl.png" /> -->
            <div @click="exit" class="tcdl">退出登录</div>
          </div>
        </div>
      </div>
    </ul>

    <div
      v-for="one in menuVO.oneMenus"
      :key="'xx' + one.gnDm"
      :class="{ menuTab: true, menuActive: tabActiveIndex === one.gnDm }"
      @mouseover="
        () => {
          tabActiveIndex = one.gnDm;
        }
      "
      style="z-index: 120; height: 65%; box-shadow: 0 10px 10px 5px #f1f1f1"
      @mouseleave="handleMouseLeave"
    >
      <div class="menuLeft">
        <div class="cpmsContext" style="position: relative">
          <h2>{{ one.gnbt }}</h2>
          <p>{{ one.msxx }}</p>
          <p style="position: absolute; right: 0; bottom: 0"><img :src="one.picture" /></p>
          <div class="cpms"></div>
        </div>
      </div>
      <div class="menuCenter">
        <ul class="menu_content_list">
          <div v-for="two in menuVO.twoMenus" :class="{ tabActive: activeIndex === two.gnDm }" :key="two.gnDm">
            <li style="text-align: center" v-if="one.gnDm === two.sjgnDm" @mouseover="changeTab(two.gnDm)">
              <span><img :src="two.icon" style="vertical-align: middle" /></span>
              <span>&nbsp;&nbsp;&nbsp;{{ two.gnmc }}</span>
            </li>
          </div>
        </ul>
      </div>
      <div class="menuRight">
        <div class="search_line">
          <t-input placeholder="支持功能搜索" @focus="switchResult" @blur="switchResult" @keyup="search">
            <template #suffixIcon>
              <search-icon :style="{ cursor: 'pointer' }" />
            </template>
          </t-input>
          <ul class="search_result" v-show="showResult">
            <li v-for="menu in searchMenu" :key="'s' + menu.gnDm">
              <a @click="openMenu(menu.gnurl)" :title="menu.gnmc">{{ menu.gnmc }}</a>
            </li>
          </ul>
        </div>
        <div class="content_Height">
          <div
            v-for="thr in menuVO.thrMenus"
            :key="thr.gnDm"
            :class="{ content: true, conActive: activeIndex === thr.sjgnDm }"
          >
            <div class="content_list">
              <h2 class="content_list_title">{{ thr.gnmc }}</h2>
              <ul class="content_list_names">
                <div v-for="fou in menuVO.fouMenus" :key="fou.gnDm">
                  <li v-if="fou.sjgnDm === thr.gnDm">
                    <a @click="openMenu(fou.gnurl)" :title="fou.gnmc">{{ fou.gnmc }}</a>
                  </li>
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import { SearchIcon, UserIcon } from 'tdesign-icons-vue';
import sylogo from '@/pages/index/assets/sylogo.png';
import { logout } from '@/pages/index/api/login';
import Xxzx from '@/pages/index/views/xxzx/xxzx.vue';
import { logo, menuVO } from './config';

export default {
  components: { SearchIcon, Xxzx },
  props: {},
  data() {
    return {
      value: '',
      zzList: [
        { zzid: '1', zzmc: '1111111111' },
        { zzid: '2', zzmc: '2222222' },
        { zzid: '3', zzmc: '33333333333' },
      ],
      qhzzShow: false,
      xxzxShow: false,
      sylogo,
      logo,
      menuVO,
      userShow: false,
      yhxxVO: {
        xm: '张三',
        zzjg: '华北区域一部',
        yhjs: '系统管理员',
        yhtx: 'data:image/jpeg;base64,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',
      },
      xxtxCount: '999',
      activeIndex: '',
      tabActiveIndex: '',
      xtglShow: false,
      xtglImg: require('../../assets/2.png'),
      showResult: false,
      searchMenu: [],
    };
  },
  computed: {
    icon() {
      return () => <UserIcon />;
    },
  },
  created() {
    this.initHeader();
  },
  methods: {
    // onConfirm() {
    //   console.log(this.value);
    //   const a = this.zzList.filter((e) => {
    //     return e.zzid === this.value;
    //   });
    //   this.yhxxVO.zzjg = a[0].zzmc;
    //   this.qhzzShow = false;
    // },
    async initHeader() {
      //   await api
      //     .initHeader()
      //     .then((res) => {
      //       if (res.data && res.data.returnCode === '001') {
      //         return res.data.data;
      //       }
      //       this.$message.error(res.data.returnMsg);
      //       return null;
      //     })
      //     .then((data) => {
      //       if (data) {
      //         this.logo = data.logo;
      //         this.yhxxVO = data.yhxxVO;
      //         this.xxtxCount = data.xxtxCount;
      //         this.menuVO = data.menuVO;
      //       }
      //     })
      //     .catch((error) => {
      //       console.log(error);
      //     });
    },
    openXxzx() {
      this.xxzxShow = !this.xxzxShow;
    },
    openUser() {
      this.userShow = !this.userShow;
    },
    headMouseLeave() {
      this.userShow = false;
      this.xxzxShow = false;
    },
    changeTab(index) {
      this.activeIndex = index;
      console.log(this.activeIndex);
    },
    changeTopTab(index) {
      this.tabActiveIndex = index;
      for (let i = 0; i < this.menuVO.twoMenus.length; i++) {
        if (this.menuVO.twoMenus[i].sjgnDm === index) {
          this.activeIndex = this.menuVO.twoMenus[i].gnDm;
          return;
        }
      }
      console.log('index', index);
    },
    handleMouseLeave() {
      this.tabActiveIndex = '';
    },
    sy() {
      window.open('/znsb/view/mhzc/mhsy');
    },
    grsz() {
      window.open('/znsb/view/mhzc/grsz/grsz');
    },
    zzjggl() {
      window.open('/znsb/view/mhzc/zzjggl/zzjgglmain');
    },
    jsqxgl() {
      window.open('/znsb/view/mhzc/jsqxgl/jsqxglmain');
    },
    yhgl() {
      window.open('/znsb/view/mhzc/yhgl/yhglmain');
    },
    glqyfp() {
      window.open('/znsb/view/mhzcglqyfp/glqyfpmain');
    },
    exit() {
      logout().then(() => {
        const { href } = window.location;
        window.location.href = href.substring(0, href.indexOf('/znsb/view')).concat('/znsb/view/sso/login');
      });
    },
    showXtgl() {
      this.xtglShow = !this.xtglShow;
      if (this.xtglShow) {
        this.xtglImg = require('../../assets/1.png');
      } else {
        this.xtglImg = require('../../assets/2.png');
      }
    },
    switchResult() {
      setTimeout(() => {
        this.showResult = !this.showResult;
      }, 300);
    },
    search(val) {
      this.searchMenu = [];
      if (this.menuVO && this.menuVO.fouMenus && val) {
        for (let i = 0; i < this.menuVO.fouMenus.length; i++) {
          const { gnmc, gnsjcpi, gnsmcpy } = this.menuVO.fouMenus[i];
          if (gnmc.indexOf(val) > -1 || gnsjcpi.indexOf(val) > -1 || gnsmcpy.indexOf(val) > -1) {
            this.searchMenu.push(this.menuVO.fouMenus[i]);
          }
        }
      }
    },
    openMenu(url) {
      window.open(url);
    },
  },
};
</script>
<style lang="less" scoped>
.header_common {
  width: 100%;
  height: 56px;
  background-color: #1663e3;
}
.header_logo {
  display: flex;
  align-items: center;
  float: left;
  height: 43px;
  margin-left: 24px;
}
.header_logo_pic {
  display: block;
  float: left;
  height: 43px;
}
.header_logo span {
  display: block;
  float: left;
  margin-left: 8px;
  font-size: 20px;
  line-height: 43px;
  color: #fff;
}
.user_area {
  position: relative;
  float: right;
  margin-right: 24px;
}
.user_area .user_pic {
  float: left;
  width: 36px;
  height: 36px;
  border-radius: 50px;
}
.user_area span {
  float: left;
  margin-right: 10px;
  font-size: 14px;
  line-height: 36px;
  color: #fff;
}
.user_function {
  cursor: pointer;
}
.user_arrow {
  float: left;
  width: 16px;
  height: 16px;
  margin-top: 10px;
  margin-right: 10px;
  margin-left: 6px;
}
.user_info_div {
  position: absolute;
  top: 47px;
  right: -22px;
  z-index: 69; //为了盖住gird组件的头部（要大于 z-index  50）
  width: 300px;
  padding-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 6px 6px 5px #0000000f;
}
.user_info_div h6:hover {
  height: 100%;
  background: #cee2ff;
  border-radius: 5px;
}
.tcdl:hover {
  background: #cee2ff;
}
.user_info_div h6 {
  display: block;
  width: 100%;
  height: 30px;
  padding-top: 8px;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  color: #999;
  cursor: pointer;
}
.user_info_div h6 img {
  display: block;
  float: left;
  width: 16px;
  height: 16px;
  margin-top: 3px;
}
.user_info_div h6 span {
  display: block;
  float: left;
  padding-left: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #333;
}
.user_info_manage {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 50px;
  padding: 16px 0;
  margin: 16px 0;
  border-top: solid 2px #eeeeef;
}
.tcdl {
  width: 80%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  border: 1px solid #eeeeef;
}
.user_info_manage a {
  display: block;
  float: left;
  width: 100%;
  height: 22px;
  line-height: 22px;
  cursor: pointer;
}
.user_info_manage a img {
  display: block;
  float: left;
  width: 16px;
  height: 16px;
  margin-top: 3px;
}
.user_info_manage a span {
  display: block;
  float: left;
  padding-left: 4px;
  font-size: 14px;
  line-height: 22px;
  color: #333;
}
.menuItem {
  float: left;
  width: 120px;
  height: 100%;
  margin: 0;
  // margin-top: 10px;
  font-size: 14px;
  line-height: 35px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  border-radius: 0;
}
// .menuItem span:before {
//   position: relative;
//   top: 3px;
//   right: 5px;
//   content: url('../../assets/layers.png');
// }
.menuisactive div {
  height: 100%;
  background: #0242aa;
  border-radius: 5px;
}
// .menuisactive span:before {
//   content: url('../../assets/layers.png');
// }
.menuItem:hover div {
  height: 100%;
  background: #0242aa;
  border-radius: 5px;
}
// .menuItem:hover div span:before {
//   content: url('../../assets/qhlayers.png');
// }
.menuTab {
  position: absolute;
  top: 56px;
  z-index: 69; //为了盖住gird组件的头部（要大于 z-index  50）
  display: none;
  width: 100%;
  height: 60%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 6px 0 #0000000f;
}
.menuActive {
  display: block;
}
.menuLeft {
  float: left;
  width: 15%;
  height: 100%;
  background-image: url('../../assets/leftbg.png');
  background-size: 100% 100%;
}
.menuCenter {
  float: left;
  width: 12%;
  height: 100%;
  border-right: 1px solid #ccc;
}
.menuRight {
  float: left;
  width: 73%;
  height: 100%;
  overflow: auto;
}
.menuRight .content {
  display: none;
  width: 100%;
  margin-left: 30px;
  overflow-y: auto;
}
.menuRight .conActive {
  display: block;
}
.cpmsContext {
  width: 100%;
  height: 100%;
  padding: 30px 20px 10px;
}
.cpmsContext p {
  margin-top: 20px;
  line-height: 200%;
  letter-spacing: 1px;
  text-align: justify;
}
.cpms {
  width: 100%;
  height: 50%;
}
.cpms div {
  display: block;
  padding: 10px;
  margin-top: 15px;
  background-color: #fafaf8;
  border: 1px solid #ccc;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
}
.cpms img {
  position: relative;
  top: 2px;
  left: 5px;
}
.cpms span {
  margin-left: 10px;
}

ul.menu_content_list {
  width: 100%;
  padding: 0;
  margin: 4px 0 0;
  list-style: none;
}
ul.menu_content_list li {
  width: 100%;
  height: 36px;
  padding-left: 20px;
  font-size: 14px;
  line-height: 36px;
  color: #333;
  text-indent: 19px;
}
ul.menu_content_list li:hover {
  cursor: pointer;
}
ul.menu_content_list div {
  width: 80%;
  margin: auto;
  margin-top: 20px;
}
ul.menu_content_list div.tabActive {
  background-color: #cee2ff;
}
ul.menu_content_list div.tabActive a {
  color: #1663e3;
}
ul.menu_content_list div.tabActive a:before {
  content: url('../../assets/tyx1.png');
}
ul.menu_content_list a:before {
  position: relative;
  top: 3px;
  right: 10px;
  content: url('../../assets/tyx.png');
}
.search_line {
  position: relative;
  float: left;
  width: 286px;
  height: 32px;
  margin-top: 24px;
  margin-left: 30px;
}
ul.search_result {
  position: absolute;
  top: 33px;
  left: -1px;
  z-index: 1;
  width: 286px;
  height: 224px;
  padding: 10px 0;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  border: solid 1px rgba(39, 40, 46, 0.2);
}
ul.search_result li {
  width: 100%;
  height: 32px;
  line-height: 32px;
  list-style: none;
}
ul.search_result li a {
  display: block;
  width: 90%;
  margin: auto;
  overflow: hidden;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
ul.search_result li:hover {
  background-color: #f5f6fa;
}
ul.search_result li:hover a {
  color: #4285f4;
}
.content_Height {
  width: 100%;
  margin: 68px auto 0;
  overflow-x: hidden;
  overflow-y: auto;
}
.content_list {
  width: 100%;
  margin-bottom: 16px;
  overflow: hidden;
}
.content_list_title {
  display: block;
  width: 100%;
  height: 30px;
  font-size: 14px;
  font-weight: 600;
  line-height: 30px;
  color: #4285f4;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}
ul.content_list_names {
  width: 948px;
  margin-left: -24px;
  overflow: hidden;
}
ul.content_list_names li {
  float: left;
  width: 292px;
  height: 30px;
  margin: 8px 0 0 24px;
  list-style: none;
}
ul.content_list_names li a {
  width: 100%;
  height: 30px;
  font-size: 14px;
  line-height: 30px;
  color: #333;
}
ul.content_list_names li a:hover {
  color: #4285f4;
  cursor: pointer;
}
.grxx {
  display: flex;
  height: 80px;
  background: linear-gradient(to bottom left, #9fbff7, #d8e7fc 50%, #bfe8ff);

  // background: #cee2ff;
  // background-color: #9fbff7;
  // justify-content: space-around;
  border-bottom: 1px solid #eeeeef;
  align-items: center;
}
.xxzx {
  position: absolute;
  top: 47px;
  right: -9%;
  z-index: 9;
  width: 726px;
  padding-bottom: 10px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 6px 6px 5px #0000000f;
}
</style>
