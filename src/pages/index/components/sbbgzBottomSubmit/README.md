### 统一申报提交说明

### 可配置字段说明

| 参数            | 说明              | 类型     |  可选值 |  默认值 |
| --------------- | ----------------  | -------  | ------ | ------ |
| declareDisabled | 是否置灰提交按钮   | Boolean  |  --     | false  |

带回卡片的数据格式： sybzdm税源标志代码没有可传空
[
  {
    'zsxmdm': '10110',
	  'syuuidList': [
	  	{
	  		'syuuid': 'AEB41A718923271DE053990C170BF413',
	  		'sybzdm': '4',
	  	},
	  	{
	  		'syuuid': 'AEB41A718923271DE053990C170BF445',
	  		'sybzdm': '4',
	  	}
	  ],
  },
]
或者
[
  {
    'zsxmdm': '10110',
	  'syuuidList': [],
  }
]

#### 基础用法

```vue
<template>
  <div>
    <gz-sbb-bottom-submit @submitClickListener="submitClickListener"></gz-sbb-bottom-submit>
  </div>
</template>

<script>
import gzSbbBottomSubmit from '@/pages/index/components/sbbgzBottomSubmit/index';

export default {
  components: { sbbBottomSubmit },
  data() {
    return {
    };
  },
  props: {},
  methods: {
    // 点击提交触发的回调， 在这个方法里返回提交数据(以房土为例)
    submitClickListener(callback) {
        const sbData = [
        {
          zsxmdm: '10120',
        },
      ];
      callback(sbData);
    },
  },
};
</script>

<style lang="less" scoped></style>
```

