<template>
  <div class="footerbar">
    <gt-space size="10px">
      <t-button theme="primary" :disabled="declareDisabled" @click="toDeclare">提交</t-button>
    </gt-space>
  </div>
</template>

<script>
import { MessagePlugin } from 'tdesign-vue';

export default {
  name: 'gz-sbb-bottom-submit',
  props: {
    /**
     * 申报提交按钮是否置灰
     */
    declareDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    toDeclare() {
      let sbDataList = [];
      this.$emit('submitClickListener', (val) => {
        if (val) {
          sbDataList = val;
          if (sbDataList.length <= 0) {
            MessagePlugin.info('提交的数据不能为空');
            return;
          }
          // 待调整
          // if (sbDataList[0]?.zsxmDm === '10114' && sbDataList[0]?.symxVOList.length <= 0) {
          //   MessagePlugin.warning('请选择税源信息');
          //   return;
          // }
          // this.$EventBus.$emit('querySbxxgzByZsxm', sbDataList);
          console.log('sbDataList=', sbDataList);
          window.sessionStorage.setItem('sbUpdateDataList', JSON.stringify(sbDataList));
          this.$router.push('/cchxwsnssbUpdate');
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.footerbar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2499;
  width: 100%;
  height: 64px;
  line-height: 64px;
  text-align: center;
  background-color: white;
  box-shadow: 0 -2px 5px 0 rgba(6, 13, 97, 0.1);
}
</style>
