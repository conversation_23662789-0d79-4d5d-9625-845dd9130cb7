<template>
  <t-drawer :visible="isVisible" v-bind="$attrs" v-on="$listeners">
    <template v-for="(slot, slotName) in $slots" #[slotName]>
      <slot :name="slotName" />
    </template>
  </t-drawer>
</template>

<script>
export default {
  name: 'CssDrawer',
  props: {
    visible: {
      type: [Boolean, Object],
      default: true,
    },
  },
  watch: {
    visible(n) {
      if (!n) {
        this.isVisible = false;
        setTimeout(() => {
          this.$parent.$emit('update:visible', false);
        }, 300);
      } else {
        this.isVisible = true;
      }
    },
  },
  data() {
    return {
      isVisible: false,
    };
  },
  mounted() {
    if (this.visible) this.isVisible = true;
  },
};
</script>
<style lang="less" scoped>
/deep/ .t-drawer__body {
  overflow-x: hidden;
}
/deep/.t-drawer__header {
  font-weight: 700;
}
</style>
