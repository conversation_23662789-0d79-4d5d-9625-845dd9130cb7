<template>
  <!-- <t-drawer class="css-drawer" :visible="isVisible" v-bind="$attrs" v-on="$listeners">
    <template v-for="(slot, slotName) in $slots" #[slotName]>
      <slot :name="slotName" />
    </template>
  </t-drawer> -->
  <t-dialog class="css-dialog" :visible="isVisible" v-bind="$attrs" v-on="$listeners">
    <template v-for="(slot, slotName) in $slots" #[slotName]>
      <slot :name="slotName" />
    </template>
  </t-dialog>
</template>

<script>
export default {
  name: 'CssDialog',
  props: {
    visible: {
      type: [Boolean, Object],
      default: true,
    },
  },
  data() {
    return {
      isVisible: false,
    };
  },
  watch: {
    visible(n) {
      if (!n) {
        this.isVisible = false;
        setTimeout(() => {
          this.$parent.$emit('update:visible', false);
        }, 300);
      } else {
        this.isVisible = true;
      }
    },
  },
  mounted() {
    if (this.visible) this.isVisible = true;
  },
};
</script>
<style lang="less" scoped>
.css-dialog {
  /deep/ .t-dialog {
    // padding: 0;
    // &__header {
    //   // height: 56px;
    //   padding: 16px 20px;
    //   border-bottom: 1px solid #27282e14;
    // }
    // &__body {
    //   padding: 16px 20px;
    // }
    &__footer {
      // position: relative;
      padding: 12px 24px;
      text-align: right;
    }
  }
  // /deep/ .t-dialog__close {
  //   top: 18px;
  // }
}
</style>
