_注意：ResultPage 继承 GtResultPage 继承 GtResultInfo 组件，GtResultInfo 组件所有 props 属性和插槽都适用于 ResultPage 组件。_

## 主要作用

### 复写 subTitle 默认插槽

当没有主动提供 subTitle 插槽 并且 useOverrideSubTitleSlot 为真时，使用申报缴款默认插槽

> 优先识别具名插槽
> useOverrideSubTitleSlot:true 使用复写插槽
> useOverrideSubTitleSlot = false 使用默认插槽

### useOverrideSubTitleSlot

默认值为 true，当不主动提供 subTitle 插槽并希望使用 GtResultInfo 的默认插槽，置为 false

> sub-title 透传给 gt-result-page 用属性控制渲染

### 复写 btn-group 默认插槽

当没有主动提供 btn-group 插槽 并且 useOverrideBtnGroupSlot 为真时，使用申报立即缴款按钮组（支持由 generateUrl 点击时动态生成路径）

### 引入 subTitle 通用样式

```vue
<style lang="less" scoped>
@import './index.less';
</style>
```
