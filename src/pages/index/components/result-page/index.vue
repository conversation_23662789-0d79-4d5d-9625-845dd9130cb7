<template>
  <gt-result-page
    :btn-group="btnGroup"
    :show-evaluation-btn="showEvaluationBtn"
    :more-operation="moreOperationHandler"
    :title="title"
    v-bind="$attrs"
  >
    <template #title>
      <slot v-if="$slots.title" name="title" />
    </template>
    <template #subTitle>
      <template v-if="!$slots.subTitle && useOverrideSubTitleSlot">
        <div v-if="submitResult.returnFlag === 'Y'" class="subj-title" :style="{ textAlign: 'center' }">
          <p v-if="submitResult.ybtse > 0">
            实际应补（退）税费额：<span>{{ format(submitResult.ybtse) }}</span> 元，请尽快进行税款缴纳。
          </p>
          <p v-else>
            实际应补（退）税费额：<span>{{ format(submitResult.ybtse) }}</span> 元。
          </p>
        </div>
        <div v-else class="subj-title">
          <p>{{ submitResult.errInfo }}</p>
        </div>
      </template>
      <slot v-else-if="$slots.subTitle" name="subTitle" />
    </template>
    <template #extra>
      <template v-if="onlyShowInfo"><div style="display: none"></div></template>
      <slot v-else-if="$slots.extra" name="extra" />
    </template>
    <template #btn-group>
      <template v-if="!$slots['btn-group'] && btnGroup.length > 0">
        <t-button
          v-for="(item, index) in btnGroup"
          :key="index"
          theme="primary"
          :variant="item.variant || (!showEvaluationBtn && index === btnGroup.length - 1) ? 'base' : 'outline'"
          @click="goPage(item)"
        >
          {{ item.btnTxt }}
        </t-button>
      </template>
      <slot v-else-if="$slots['btn-group']" name="btn-group" />
    </template>
    <template>
      <template v-if="onlyShowInfo"><div style="display: none"></div></template>
      <slot v-else-if="$slots.default" />
    </template>
  </gt-result-page>
</template>

<script>
import { format } from '@gt/components';
import { getYwbmByURL } from '@/utils';
import { getNamedSession } from '@/utils/proactor.js';

export default {
  name: 'sb-result-page',
  props: {
    // 不直接透传，否则 title 属性会有悬停提示
    title: {
      type: String,
      default: '',
    },
    btnGroup: {
      type: Array,
      default: () => [],
    },
    showEvaluationBtn: {
      type: Boolean,
      default: false,
    },
    moreOperation: {
      type: Object,
      default: () => ({}),
    },
    useOverrideSubTitleSlot: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      onlyShowInfo: false, // 只展示 icon title subTitle
    };
  },
  computed: {
    storeName() {
      if (!this.$route.meta.storeName) this.$gtDialog.error({ body: '路由中的meta必须要有storeName字段' });
      return this.$route.meta.storeName;
    },
    submitResult() {
      return this.$store.state[this.storeName].submitResult;
    },
    moreOperationHandler() {
      return this.onlyShowInfo ? {} : this.moreOperation;
    },
  },
  mounted() {
    // 集团办税回执页仅提供关闭页面功能
    const memberSession = getNamedSession('GT4_MEMBER_SUBSESSION_ID', this.$route.query.GT4_MEMBER_SUBSESSION_ID);
    const ywbm = getYwbmByURL();
    const jtbsYwbmList = ['qysds_a_yjd', 'qysds_a_nd', 'qysds_b_yjd'];
    if (memberSession && jtbsYwbmList.includes(ywbm)) {
      this.onlyShowInfo = true;
      // 覆盖 GtPageLayout 的面包屑配置
      this.$store.commit('index/SET_GLOBALSTATEMETA', {
        hideBack: true,
        hasHome: false,
      });
    }
  },
  beforeDestroy() {
    // 销毁前将 meta 全局覆盖清除掉
    this.$store.commit('index/CLEAR_GLOBALSTATEMETA');
  },
  methods: {
    goPage({ path, routeName, isNewPage, generateUrl }) {
      let generateHref;
      generateUrl && (generateHref = generateUrl());
      if (routeName) {
        this.$router.replace({
          name: routeName,
        });
        return;
      }

      const href = `${window.location.origin}${path}`;
      if (isNewPage) {
        window.open(generateHref || href);
        return;
      }
      window.location.href = generateHref || href;
    },

    // 格式化函数
    format(value) {
      return format(value, 2);
    },
  },
};
</script>

<style lang="less" scoped>
@import './index.less';
</style>
