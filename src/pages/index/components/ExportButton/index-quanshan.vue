<!-- 泉膳定制需求:
  销项台账，收入台账，进项台账，进项税额转出台账，增加“一键导出”按钮。
  对当前操作人员所管辖下所有税号的相应台账进行导出。
  例如，销项台账的“一键导出”按钮，导出所有管辖企业的销项台账结果，导出文件1个，每个企业一个sheet页,sheet页名为“企业名称”。
  收入台账，进项台账，进项税额转出台账相同。 -->
<template>
  <t-button
    variant="outline"
    theme="primary"
    :disabled="exportButtonFlag"
    :loading="exportLoading"
    @click="handleExport"
  >
    <FileExcelIcon slot="icon" />{{ buttonText }}
  </t-button>
</template>

<script>
import { FileExcelIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { downloadBlobFile } from '@/core/download';

export default {
  props: {
    yzpzzlDm: {
      type: String,
      default: 'BDA0610606',
    },
    tzlx: {
      type: String,
      default: '',
    },
    fileName: {
      type: String,
      default: '',
    },
    tzQueryParams: {
      type: Object,
      default: () => ({}), // 修正为返回空对象
    },
    exportButtonFlag: {
      type: Boolean,
      default: false,
    },
    buttonText: {
      type: String,
      default: '一键导出',
    },
  },
  data() {
    return {
      MessagePlugin,
      exportLoading: false,
    };
  },
  components: {
    FileExcelIcon,
  },
  methods: {
    async handleExport() {
      this.exportLoading = true;
      const params = {
        yzpzzlDm: this.yzpzzlDm,
        tzlx: this.tzlx,
        fileName: this.fileName,
        cxParam: this.tzQueryParams,
      };
      try {
        const res = await downloadBlobFile({
          baseURL: `/gyExport/exportZz`,
          method: 'post',
          data: params,
        });
        if (res.status === 200) {
          MessagePlugin.success({ content: '导出成功', duration: 500 });
        } else {
          MessagePlugin.error({ content: '导出失败', duration: 500 });
        }
      } catch (error) {
        MessagePlugin.error({ content: '导出失败', duration: 500 });
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>
