<template>
  <div v-if="pagingFlag">
    <t-dropdown :options="dropdownOptions" :disabled="exportButtonFlag">
      <t-button variant="outline" theme="primary" :disabled="exportButtonFlag" :loading="exportLoading">
        <DownloadIcon slot="icon" />导出
      </t-button>
    </t-dropdown>
  </div>
  <div v-else>
    <t-button
      variant="outline"
      theme="primary"
      :disabled="exportButtonFlag"
      :loading="exportLoading"
      @click="handleExport"
      ><DownloadIcon slot="icon" />导出</t-button
    >
  </div>
</template>

<script>
import { DownloadIcon } from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';
import { downloadBlobFile } from '@/core/download';

export default {
  props: {
    yzpzzlDm: {
      type: String,
      default: 'BDA0610606',
    },
    tzlx: {
      type: String,
      default: '',
    },
    fileName: {
      type: String,
      default: '',
    },
    tzQueryParams: {
      type: Object,
      default: () => ({}), // 修正为返回空对象
    },
    exportButtonFlag: {
      type: Boolean,
      default: false,
    },
    pagingFlag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      MessagePlugin,
      exportLoading: false,
      dropdownOptions: [
        { content: '导出当前页', value: 1, onClick: this.handleExport },
        { content: '导出所有页', value: 2, onClick: () => this.handleExport('all') },
      ],
    };
  },
  components: {
    DownloadIcon,
  },
  methods: {
    async handleExport(isAll) {
      this.exportLoading = true;
      const params = {
        yzpzzlDm: this.yzpzzlDm,
        tzlx: this.tzlx,
        fileName: this.fileName,
        cxParam: this.tzQueryParams,
      };
      if (isAll === 'all') {
        params.cxParam.pageNum = 1;
        params.cxParam.pageSize = 1000000;
      }
      console.log(params);
      try {
        const res = await downloadBlobFile({ baseURL: `/gyExport/exportTz`, method: 'post', data: params });
        if (res.status === 200) {
          MessagePlugin.success({ content: '导出成功', duration: 500 });
        } else {
          MessagePlugin.error({ content: '导出失败', duration: 500 });
        }
      } catch (err) {
        MessagePlugin.error({ content: '导出失败', duration: 500 });
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
