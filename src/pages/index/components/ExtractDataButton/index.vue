<template>
  <div v-show="!shanxiyidongFlag">
    <!-- 特殊处理模式，进项发票提取数据存在用户可选分支，使用此分支选择执行条件 -->
    <t-dropdown v-if="jxfpzzTqsjBranchFlag" :options="jxfpzzFlagDropdownOptions" @click="handleDropdownClick">
      <t-button :variant="variant" theme="primary"> <CloudDownloadIcon slot="icon" />提取数据 </t-button>
    </t-dropdown>
    <t-button v-else :variant="variant" theme="primary" @click="handleClick">
      <CloudDownloadIcon slot="icon" />
      提取数据
    </t-button>
    <t-dialog
      v-show="boxvisible"
      theme="warning"
      style="display: block; border-radius: 10px"
      width="45%"
      header="注意"
      :onConfirm="onConfirm"
      :onClose="onClose"
    >
      <template #body>
        <div class="custom-body">
          <P></P>
          <p>提取数据后系统将更新当天最新的{{ ywtsMsg }}，此操作需要消耗一定时间，请勿频繁操作。</p>
          <p>提取数据后系统将自动进行台账数据加工及算税，请注意核对数据。是否确认提取数据？</p>
        </div>
      </template>
    </t-dialog>
    <t-tooltip
      content="系统正在加工明细生成台账，请稍等，当台账中提示信息“系统算税执行中”消失后，即可查看台账数据，以免台账数据与明细汇总数据不一致。"
      theme="warning"
      :showArrow="false"
      v-if="!readyStatus"
    >
      <t-button theme="warning" style="position: absolute; right: 10px"
        ><LoadingIcon slot="icon" />系统算税执行中</t-button
      >
    </t-tooltip>
    <div v-else></div>
  </div>
</template>

<script>
import { CloudDownloadIcon, LoadingIcon } from 'tdesign-icons-vue';
import { extract, extractInvoice, yhsExtract, fcsExtract, qysdsyjExtract } from '@/pages/index/api/tzzx/gyApi/gyapi.js';
import {
  jyssReadyStatusFetch,
  yhsJyssReadyStatusFetch,
  fcsJyssReadyStatusFetch,
  qysdsyjJyssReadyStatusFetch,
} from '@/pages/index/views/util/tzzxTools.js';

export default {
  props: {
    sszq: {
      type: String,
      default: '',
    },
    variant: {
      type: String,
      default: 'base',
    },
    readyStatus: {
      type: Boolean,
      default: true,
    },
    jxfpzzTqsjBranchFlag: {
      type: Boolean,
      default: false,
    },
    ywtsMsg: {
      type: String,
      default: '财务凭证数据',
    },
    ywlx: {
      type: String,
      default: '',
    },
    tqsjParams: {
      type: Object, // 应对不同台账细类提取数据按钮调用参数不同时使用
      default: () => ({}), // 修正为返回空对象
    },
    isJxfpmx: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      boxvisible: false,
      jxfpzzSelectedValue: null, // 新增的标志变量
      jxfpzzFlagDropdownOptions: [
        { content: '提取凭证数据', value: 1 },
        { content: '提取发票数据', value: 2 },
      ],
    };
  },
  components: {
    LoadingIcon,
    CloudDownloadIcon,
  },
  computed: {
    shanxiyidongFlag() {
      return (
        !this.$store.state.zdmczh.companyDifferentiationConfig ||
        this.$store.state.zdmczh.companyDifferentiationConfig?.jtbm === '000003'
      );
    },
    getBranch() {
      const URL = window.location.pathname;
      if (URL.indexOf('/znsb/view/tzzx/zzstz') > -1) {
        return '1';
      }
      if (URL.indexOf('/znsb/view/tzzx/yhstz') > -1) {
        return '2';
      }
      if (URL.indexOf('/znsb/view/tzzx/qysdsyjtz') > -1) {
        return '3';
      }
      if (URL.indexOf('/znsb/view/tzzx/zzsyjtz') > -1) {
        return '4';
      }
      return '1';
    },
  },
  methods: {
    handleClick() {
      this.boxvisible = true;
    },
    handleDropdownClick(option) {
      this.jxfpzzSelectedValue = option.value; // 记录选择的值
      this.ywtsMsg = option.value === 1 ? '财务凭证数据' : '发票数据'; // 根据选择的值更新ywtsMsg
      this.handleClick();
    },
    async onConfirm() {
      try {
        const params = {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          gsh: this.$store.state.zzstz.userInfo?.qydmz || 'default',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          sszq: this.sszq,
          ywlx: this.ywlx,
        };
        let res = {};
        if (this.jxfpzzTqsjBranchFlag) {
          // 使用新的标志变量jxfpzzSelectedValue
          if (this.jxfpzzSelectedValue === 1) {
            res = await extract(params);
          } else if (this.jxfpzzSelectedValue === 2) {
            res = await extractInvoice(params);
          }
        } else if (this.isJxfpmx) {
          // 特殊处理：jxfpmx页面调用时直接使用extractInvoice
          res = await extractInvoice(params);
        } else {
          // 原有逻辑保持不变
          switch (this.getBranch) {
            case '1':
              res = await extract(params);
              break;
            case '2':
              res = await yhsExtract(params);
              break;
            case '3':
              res = await qysdsyjExtract(params);
              break;
            case '4':
              res = await fcsExtract({
                djxh: this.$store.state.zzstz.userInfo?.djxh || '',
                gsh: this.$store.state.zzstz.userInfo?.qydmz || 'default',
                ywlx: 'zzsyjtz',
              });
              break;
            default:
              res = await extract(params);
              break;
          }
        }
        console.log('提取数据操作返回msg', res.msg);
        if (this.getBranch === '1') {
          await jyssReadyStatusFetch(
            this.$store.state.zzstz.userInfo?.djxh || '',
            this.$store.state.zzstz.userInfo?.nsrsbh || '',
            this.sszq,
            () => {
              // 轮询完成后刷新数据
              console.log('jyssReadyStatusFetch轮询结束，刷新数据');
              this.$emit('query', { flag: true, initQuery: true });
            },
          )
            .then((readyStatus) => {
              this.$emit('query', { flag: true, initQuery: true });
              if (readyStatus) {
                this.$nextTick(() => {
                  this.readyStatus = true;
                  this.$emit('query', { flag: true, initQuery: true });
                });
              }
            })
            .catch((error) => {
              console.error('请求失败:', error);
            });
        } else if (this.getBranch === '2') {
          yhsJyssReadyStatusFetch(
            this.$store.state.zzstz.userInfo?.djxh || '',
            this.$store.state.zzstz.userInfo?.qydmz || 'default',
            this.$store.state.zzstz.userInfo?.nsrsbh || '',
            this.sszq,
            this.ywlx,
            () => {
              // 轮询完成后刷新数据
              this.$emit('query', { flag: true });
            },
          );
        } else if (this.getBranch === '3') {
          qysdsyjJyssReadyStatusFetch(
            {
              djxh: this.$store.state.zzstz.userInfo?.djxh || '',
              gsh: this.$store.state.zzstz.userInfo?.qydmz || 'default',
              nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
              sszq: this.sszq,
              ywlx: this.ywlx,
            },
            () => {
              // 轮询完成后刷新数据
              this.$emit('query', { flag: true });
            },
          );
        } else if (this.getBranch === '4') {
          fcsJyssReadyStatusFetch(
            {
              djxh: this.$store.state.zzstz.userInfo?.djxh || '',
              gsh: this.$store.state.zzstz.userInfo?.qydmz || 'default',
              ywlx: 'zzsyjtz',
            },
            () => {
              // 轮询完成后刷新数据
              this.$emit('query', { flag: true });
            },
          );
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.boxvisible = false;
        this.$emit('query', { flag: true });

        // 如果没有使用轮询，则直接刷新
        if (!['1', '2', '3', '4'].includes(this.getBranch)) {
          this.$emit('query', { flag: true });
        }
      }
    },
    onClose() {
      this.boxvisible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.custom-body {
  display: flex;
  flex-direction: column; /* 垂直排列 */
  align-items: flex-start; /* 左对齐 */
}

.custom-body p {
  margin-bottom: 10px; /* 段落之间的间距 */
}
</style>
