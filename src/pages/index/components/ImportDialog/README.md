### 模板下班说明

### 可配置字段说明

| 参数         | 说明                   | 类型    | 可选值 | 默认值 |
| ------------ | ---------------------- | ------- | ------ | ------ |
| isShowDialog | 是否显示弹窗格式       | Boolean | --     | false  |
| visible      | 是否以 dialog 方式显示 | Boolean | --     | false  |
| description  | 上传格式描述           | String  | --     | --     |
| upLoadUrl    | 上传路径               | String  | --     | --     |

#### 基础用法

```vue
<template>
  <div>
    <template-import
          ref="importLog"
          is-show-dialog
          description="上传格式为EXCEL"
          :upLoadUrl="upLoadUrl"
          :file-path="filePath"
          @handleSuccess="handleSuccess"
          @change="closeChange"
        ></template-import>
  </div>
</template>
<script>
import { importfile } from '@/pages/index/api/ccszxsb/cxstbsRequest';
export default {
  data() {
    return {
      visible: false,
      upLoadUrl: importfile,
      filePath: '/xjspffyxxcj/v1/download',
    };
  },
    methods: {
        onUploadTemp() {
          this.$refs.importLog.isShow();
        },
        handleSuccess(fileInfo) {
          console.log('fileInfo---->', fileInfo);
        },
        closeChange() {
          this.$refs.importLog.close();
        },
    }
};
</script>
```

#### Dialog 用法

```vue
<template>
  <div>
    <t-switch v-model="visible">
      <template v-slot:label="slotProps">{{ slotProps.value ? '开' : '关' }}</template>
    </t-switch>
    <gt-template-import
      is-show-dialog
      :visible="visible"
      @change="(val) => (visible = val)"
      description="上传格式为EXCEL"
      upLoadUrl="http://localhost:8080/uploadPath"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
    };
  },
};
</script>
```
