<template>
  <div class="gov-template-import">
    <!-- NOTICE: transition -> Fragment -->
    <component
      :is="isShowDialog ? 't-dialog' : 'transition'"
      width="50%"
      top="28vh"
      header="批量导入"
      :visible.sync="myVisible"
      :footer="false"
      :on-close="fileUploadCancel"
    >
      <t-config-provider :globalConfig="globalConfig">
        <div class="upload-dialog-content">
          <t-upload
            v-model="files"
            theme="file-flow"
            placeholder="只支持xls、xlsx格式批量上传"
            :disabled="false"
            :auto-upload="false"
            :sizeLimit="{ size: 10, unit: 'MB', message: '图片大小不超过 10 MB' }"
            :beforeUpload="beforeUpload"
            :max="max"
            :allow-upload-duplicate-file="true"
            :is-batch-upload="true"
            :multiple="true"
            :draggable="true"
            :upload-all-files-in-one-request="true"
            :action="upLoadUrl"
            :onFail="handleFail"
            :onSuccess="handleSucess"
            @dragenter="onDragenter"
            @dragleave="onDragleave"
            @drop="onDrop"
          />
          <span class="temp-download" @click="downloadDrmb">模板下载</span>
        </div>
      </t-config-provider>
    </component>
  </div>
</template>

<script>
import { MessagePlugin } from 'tdesign-vue';
// eslint-disable-next-line import/no-unresolved
import api from '@/pages/index/api/ccszxsb/cxstbsRequest';
/**
 * @category DataInput
 * @cover AccordionSection
 */
export default {
  name: 'template-import',
  props: {
    /**
     * 是否显示弹窗格式
     */
    isShowDialog: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否以dialog方式显示
     */
    visible: {
      type: Boolean,
      default: false,
    },
    /**
     * 上传格式描述
     */
    description: {
      type: String,
      default: '上传格式为EXCEL',
    },
    /**
     * 上传路径
     */
    upLoadUrl: {
      type: String,
      default: 'https://service-bv448zsw-**********.gz.apigw.tencentcs.com/api/upload-demo',
    },
    /**
     * 下传路径
     */
    filePath: {
      type: String,
      default: 'https://service-bv448zsw-**********.gz.apigw.tencentcs.com/api/upload-demo',
    },
  },
  data() {
    return {
      max: 10,
      files: [],
      myVisible: this.visible,
      disabled: false,
      autoUpload: false,
      globalConfig: {
        upload: {
          triggerUploadText: {
            normal: '确定',
          },
          cancelUploadText: '取消',
        },
      },
    };
  },
  computed: {},
  watch: {
    visible(val) {
      this.myVisible = val;
    },
  },
  methods: {
    setting() {
      this.fileUploadCancel();
    },
    fileUploadCancel() {
      this.myVisible = false;
      console.log('fileUploadCancel visible-', this.myVisible);
      this.$emit('change', false);
    },
    onDragenter(p) {
      console.log('dragenter', p);
    },
    onDragleave(p) {
      console.log('dragleave', p);
    },
    onDrop(p) {
      console.log('drop', p);
    },
    handleSucess(fileInfo) {
      console.log(fileInfo.response.Response);
      this.$emit('handleSuccess', fileInfo.response.Response);
    },
    handleFail(context) {
      // this.$message.warning(context.response.Response.Data.message, 3000);
      console.log('错误信息：', context);
    },
    beforeUpload(file) {
      return this.checkFile(file.name, ['xls', 'xlsx']);
    },
    // 下载模板文件
    downloadDrmb() {
      return api.downloadDrmb(this.filePath);
    },
    // 上传前文件校验
    checkFile(filename = {}, suffixGather = [], message = '') {
      const suffix = filename.split('.').pop();
      const isPass = suffixGather.some((v) => v === suffix || v === `.${suffix}`);
      if (!isPass) {
        MessagePlugin.error({ content: message || '文件格式错误', deration: 2000 });
        // console.log('files=》', this.files);
      }
      return isPass;
    },
    isShow() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.gov-template-import {
  .upload-dialog-content {
    position: relative;
    width: 100%;
    padding: 10px 0;
  }

  .content-bottom {
    display: flex;
    min-height: 50px;
    padding: 16px 24px;
    margin-top: 16px;
    color: #666;
    background: #f9fafd;
    // border: 1px solid #B8CCFF;
    border-radius: 2px;
    align-items: flex-start;
  }

  .temp-download {
    position: absolute;
    top: 20px;
    right: 10px;
    color: #4285f4;
    cursor: pointer;
  }
}
</style>
