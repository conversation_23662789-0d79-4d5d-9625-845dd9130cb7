/*
 * @Descripttion: 功能树
 * @Version: 1.0
 * @Author: wjx
 * @Date: 2024-01-22 18:31:01
 * @LastEditors: wjx
 * @LastEditTime: 2024-02-04 14:27:02
 */
export const topbarConfig = [
  {
    name: '首页',
    path: '/home',
    key: 'index', // 用于设置该导航高亮的key
  },
  // {
  //   name: '无侧栏',
  //   path: '/detail-article', // 不设置key，则默认使用path/href
  // },
  // {
  //   key: 'qq',
  //   name: '顶级-外部链接',
  //   href: 'https://www.qq.com/', // 不设置key，则默认使用path/href
  // },
  // {
  //   // 有二级栏目的目录结构
  //   name: '顶部导航列表',
  //   key: 'list', // 用于设置该导航高亮的key
  //   children: [
  //     {
  //       key: 'mail',
  //       name: '外部链接',
  //       iconClassName: 'link',
  //       href: 'https://mail.qq.com/',
  //       target: '_blank',
  //     },
  //     {
  //       name: '配置变更',
  //       iconClassName: 'heart',
  //       path: '/',
  //     },
  //     {
  //       name: '扩容',
  //       iconClassName: 'add-circle',
  //       path: '/',
  //     },
  //     {
  //       name: 'Docker扩容',
  //       iconClassName: 'layers',
  //       path: '/',
  //     },
  //   ],
  // },
];

export const sideBarConfig = [
  {
    name: '首页',
    imgUrl: require('@/pages/index/assets/menu-icon.svg'),
    path: '/home',
  },
  {
    name: 'demo',
    iconClassName: 'view-module',
    children: [
      {
        name: 'demo',
        path: '/demo/demo',
      },
    ],
  },
];
