<!--<template>-->
  <!--  <div class="cxssb-content" style="min-width: 1300px">-->
  <!--    <div>-->
  <!--      <keep-alive>-->
  <!--        <router-view v-if="$route.meta.keepAlive"></router-view>-->
  <!--      </keep-alive>-->
  <!--      <router-view v-if="!$route.meta.keepAlive"></router-view>-->
  <!--    </div>-->
  <!--  </div>-->
  <!--</template>-->

  <template>
    <div
      style="position: absolute; display: flex; width: 100%; height: 100%; background: #f9fafd; flex-direction: column"
    >
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive"></router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive"></router-view>
    </div>
  </template>

  <script>
    // import { GLayout } from '@gtff/tdesign-gt-vue';
    import { getClientHeight } from '@gtff/tdesign-gt-vue';
    import { initComputeSszq} from '@/pages/index/views/util/tzzxTools.js';

    export default {
      data() {
        return {
          publicPath: window.STATIC_ENV_CONFIG.RESOURCE_PREFIX,
          containerHeight: 0,
          business: '',
          businessId: '',
          iframeUrl: '',
        };
      },
      watch: {
        $route: 'changeRoute',
      },
      computed: {
        type() {
          if (this.$route?.meta?.isShowSideBar) {
            return 'widthScreen';
          }
          return '';
        },
      },
      mounted() {
        this.setTopbarActive();
        initComputeSszq();
      },
      created() {
        this.getHeight();
        this.$EventBus.$on('changeBread', (data) => {
          if (data) {
            this.$refs.gtbread.meta = data;
          }
        });
        this.breadInit();
      },
      methods: {
        breadInit() {
          let mhMeta = window.localStorage.getItem('mhMeta');
          if (mhMeta) {
            mhMeta = JSON.parse(mhMeta);
            this.meta = {
              isBack: false,
              breadCrumbs: [...mhMeta],
            };
          }
          if (this.meta) {
            const breandCrumbs = this.meta.breadCrumbs;
            if (breandCrumbs && breandCrumbs.length > 1) {
              let breadcrumb = breandCrumbs[breandCrumbs.length - 1].path;
              breadcrumb = breadcrumb.substring(breadcrumb.lastIndexOf('/'));
              breandCrumbs[breandCrumbs.length - 1].path = breadcrumb;
            }
          }
          if (this.meta) {
            // 通过eventBus 派发emitEvent事件
            this.$refs.gtbread.meta = this.meta;
          }
        },
        getHeight() {
          this.containerHeight = getClientHeight() - 164;
        },
        changeRoute(route) {
          const isShowSideBar = typeof route.meta.isShowSideBar === 'undefined' ? true : route.meta.isShowSideBar;
          this.$store.commit(`global/${this.$storeGlobalTypes.SET_SIDEBAR_STATUS}`, isShowSideBar);
          this.setTopbarActive();
        },
        setTopbarActive() {
          this.$store.commit(
            `global/${this.$storeGlobalTypes.SET_TOPBAR_ACTIVE_KEY}`,
            this.$route.meta.topbarKey || this.$route.path,
          );
        },
      },
    };
  </script>

  <style lang="less" src="./styles/index.less"></style>
  <style lang="less" src="./styles/demo.less"></style>
  <style lang="less" src="../../styles/index.less"></style>
  <style lang="less" src="./styles/index_sds.less"></style>
  <style lang="less" src="../../styles/index_sds.less"></style>
</template>
