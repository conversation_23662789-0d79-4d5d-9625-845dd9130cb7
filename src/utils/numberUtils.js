/*
 * @Descripttion: 数字转换工具类
 * @Version: 1.0
 * @Author: wjx
 * @Date: 2024-04-02 14:24:36
 * @LastEditors: wjx
 * @LastEditTime: 2024-04-02 14:26:28
 */
export function toCurrency(number, decimalPlaces, currencySymbol) {
  if (typeof number === 'undefined') {
    return '';
  }
  if (Number.isNaN(number)) {
    return number;
  }
  const formattedNumber = parseFloat(number).toFixed(decimalPlaces);
  const parts = formattedNumber.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  let result = parts.join('.');
  if (currencySymbol) {
    result = `${currencySymbol}${result}`;
  }
  return result;
}
export function toRMB(number) {
  return toCurrency(number, 2, '¥');
}

export function numberToPrice(number) {
  const value = Number(number).toFixed(2);
  if (!value) return 0;
  // 获取整数部分
  const intPart = Math.trunc(value);
  // 整数部分处理，增加,
  const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
  // 预定义小数部分
  let floatPart = '';
  // 将数值截取为小数部分和整数部分
  const valueArray = value.toString().split('.');
  if (valueArray.length === 2) {
    // 有小数部分
    floatPart = valueArray[1].toString(); // 取得小数部分
    return `${intPartFormat}.${floatPart}`;
  }
  return intPartFormat + floatPart;
}

/*
 * @Descripttion: 将数字转换为百分比格式，默认保留小数点后两位
 * @样例：toPercentage(0.1);           输出：10.00%
 *       toPercentage(0.1, 3);        输出：10.000%
 *       toPercentage('aaa');         输出：'aaa'
 *       toPercentage('0.123');       输出：12.30%
 * @Author: cjx
 * @Date: 2024-02-29 10:34:57
 */
export function toPercentage(number, decimalPlaces = 2) {
  if (typeof number === 'undefined') {
    return '';
  }
  if (Number.isNaN(number)) {
    return number;
  }
  // 将数字乘以100并保留两位小数
  const percentage = (number * 100).toFixed(decimalPlaces);
  // 添加百分号
  return `${percentage}%`;
}
