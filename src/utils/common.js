/**
 * 将数字金额转换为大写中文金额
 * @param {number|string} n
 * @returns {string}
 */
const amountCapitalizedInChinese = (n) => {
  // if (!/^(?:0|[1-9]\d*)(?:\.\d+)?$/.test(n)) {
  //     console.warn('参数 ' + n + ' 为非法数值！');
  //     return ''
  // }
  let unit = '京亿万仟佰拾兆万仟佰拾亿仟佰拾万仟佰拾元角分';
  let str = '';
  let curreny = n;
  curreny += '00';
  const p = curreny.indexOf('.');
  if (p > -1) {
    curreny = curreny.substring(0, p) + curreny.substr(p + 1, 2);
  }
  unit = unit.substr(unit.length - curreny.length);
  for (let l = curreny.length, i = 0; i < l; i++) {
    str += '零壹贰叁肆伍陆柒捌玖'.charAt(curreny.charAt(i)) + unit.charAt(i);
  }
  return (
    str
      .replace(/零(?:仟|佰|拾|角)/g, '零')
      .replace(/零+/g, '零')
      .replace(/零(兆|万|亿|元)/g, '$1')
      // replace(/(兆|亿)万/g, '$1').
      // replace(/(京|兆)亿/g, '$1').
      // replace(/(京)兆/g, '$1').
      // replace(/(京|兆|亿|仟|佰|拾)(万?)(\S)仟/g, '$1$2零$3仟').
      .replace(/^元零?|零分/g, '')
      .replace(/(元|角)$/g, '$1整')
      .replace(/^分$/g, '零元整')
  );
};

/**
 * @description 获取表格 key
 * @param {String} key 原 key
 * @param {Boolean} isUpperCase 首字母是否转大学
 * @returns key
 */
const getColKey = (key, isUpperCase) => {
  return isUpperCase ? key.replace(/( |^)[a-z]/g, (l) => l.toUpperCase()) : key;
};

// 移除HTML标签代码
function removeHTMLTag(str) {
  let delHtml = str;
  // 去除HTML tag
  delHtml = delHtml.replace(/<\/?[^>]*>/g, '');
  // 去除行尾空白
  delHtml = delHtml.replace(/[ | ]*\n/g, '\n');
  // 去除多余空行
  delHtml = delHtml.replace(/\n[\s| | ]*\r/g, '\n');
  // 去掉
  delHtml = delHtml.replace(/ /gi, '');
  return delHtml;
}
// 转意符换成普通字符
function escape2Html(str) {
  const arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };
  return str.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all, t) {
    return arrEntities[t];
  });
}
export function removeEditorTag(str) {
  let str1 = removeHTMLTag(str);
  str1 = escape2Html(str1);
  return str1;
}

export function getLabelStr(keyArr, selectList, keyName) {
  let value = '';
  if (keyArr) {
    selectList.forEach(function (item) {
      if (keyArr.indexOf(item[keyName ? keyName.value : 'value']) > -1) {
        value += `${item[keyName ? keyName.label : 'label']}，`;
      }
    });
  }
  return value.substring(0, value.length - 1);
}
export { amountCapitalizedInChinese, getColKey };
