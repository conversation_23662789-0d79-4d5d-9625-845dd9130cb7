export function numberToCurrencyNo(value) {
  if (!value) return 0;

  // 获取整数部分
  const intPart = Math.trunc(value);
  // 整数部分处理，增加
  const inPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
  // 预定义小数部分
  let floatPart = '';
  // 将数值截取为小数部分和整数部分
  const valueArray = value.toString().split('.');
  if (valueArray.length === 2) {
    floatPart = valueArray[1].toString();
    return `${inPartFormat}.${floatPart}`;
  }
  return inPartFormat + floatPart;
}
// 改进版本的数字转金额方法（添加-0的处理）
export function numberToPrice(number, decimalPlaces = 2) {
  const value = Number(number).toFixed(decimalPlaces);
  if (!value) return 0;
  // 获取整数部分
  const intPart = Math.trunc(value);
  // 整数部分处理，增加,
  const intPartFormat = Object.is(intPart, -0) ? '-0' : intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
  // 预定义小数部分
  let floatPart = '';
  // 将数值截取为小数部分和整数部分
  const valueArray = value.toString().split('.');
  if (valueArray.length === 2) {
    // 有小数部分
    floatPart = valueArray[1].toString(); // 取得小数部分
    return `${intPartFormat}.${floatPart}`;
  }
  return intPartFormat + floatPart;
}
// 会计金额格式化
export function moneyFromat(num) {
  // eslint-disable-next-line no-restricted-globals
  if (!isNaN(parseFloat(num))) {
    const newNum = Number(Number(num).toFixed(2)).toLocaleString('zh', { minimumFractionDigits: 2 });
    return newNum;
  }
  return '';
}

// 小写金额转大写金额
export function changeNumMoneyToChinese(smallMoney) {
  let money = smallMoney;
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']; // 汉字的数字
  const cnIntRadice = ['', '拾', '佰', '仟']; // 基本单位
  const cnIntUnits = ['', '万', '亿', '兆']; // 对应整数部分扩展单位
  const cnDecUnits = ['角', '分', '毫', '厘']; // 对应小数部分单位
  const cnInteger = '整'; // 整数金额时后面跟的字符
  const cnIntLast = '元'; // 整型完以后的单位
  const maxNum = 999999999999999.9999; // 最大处理的数字
  let IntegerNum; // 金额整数部分
  let DecimalNum; // 金额小数部分
  let ChineseStr = ''; // 输出的中文金额字符串
  let parts; // 分离金额后用的数组，预定义
  let Symbol = ''; // 正负值标记
  if (money === '') {
    return '';
  }

  money = parseFloat(money);
  if (money >= maxNum) {
    // alert('超出最大处理数字');
    return '';
  }
  if (money === 0) {
    ChineseStr = cnNums[0] + cnIntLast + cnInteger;
    return ChineseStr;
  }
  if (money < 0) {
    money = -money;
    Symbol = '负 ';
  }
  money = money.toString(); // 转换为字符串
  if (money.indexOf('.') === -1) {
    IntegerNum = money;
    DecimalNum = '';
  } else {
    parts = money.split('.');
    // eslint-disable-next-line prefer-destructuring
    IntegerNum = parts[0];
    DecimalNum = parts[1].substr(0, 4);
  }
  if (parseInt(IntegerNum, 10) > 0) {
    // 获取整型部分转换
    let zeroCount = 0;
    const IntLen = IntegerNum.length;
    for (let i = 0; i < IntLen; i++) {
      const n = IntegerNum.substr(i, 1);
      const p = IntLen - i - 1;
      const q = p / 4;
      const m = p % 4;
      if (n === '0') {
        zeroCount += 1;
      } else {
        if (zeroCount > 0) {
          ChineseStr += cnNums[0];
        }
        zeroCount = 0; // 归零
        ChineseStr += cnNums[parseInt(n, 10)] + cnIntRadice[m];
      }
      if (m === 0 && zeroCount < 4) {
        ChineseStr += cnIntUnits[q];
      }
    }
    ChineseStr += cnIntLast;
    // 整型部分处理完毕
  }
  if (DecimalNum !== '') {
    // 小数部分
    const decLen = DecimalNum.length;
    for (let i = 0; i < decLen; i++) {
      const n = DecimalNum.substr(i, 1);
      if (n !== '0') {
        ChineseStr += cnNums[Number(n)] + cnDecUnits[i];
      }
    }
  }
  if (ChineseStr === '') {
    ChineseStr += cnNums[0] + cnIntLast + cnInteger;
  } else if (DecimalNum === '') {
    ChineseStr += cnInteger;
  }
  ChineseStr = Symbol + ChineseStr;

  return ChineseStr;
}
