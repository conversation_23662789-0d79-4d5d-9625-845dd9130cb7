/**
 * 企业数据处理工具类
 * 提供企业列表获取、格式化、树形结构构建等公共方法
 */

/**
 * 格式化跨区税源位置信息
 * @param {Object} item - 公司信息对象
 * @returns {String} 格式化后的位置信息
 */
export function formatKqsyLocation(item) {
  const xzqhmc = item.xzqhmc || '未知区域';
  let zgswskfjmc = item.zgswskfjmc || '';
  
  // 去掉zgswskfjmc中的"国家税务总局"前缀
  if (zgswskfjmc.startsWith('国家税务总局')) {
    zgswskfjmc = zgswskfjmc.replace('国家税务总局', '');
  }
  
  // 如果没有主管税务所分局名称，只显示行政区划名称
  if (!zgswskfjmc) {
    return xzqhmc;
  }
  
  // 检查主管税务所分局名称中是否已包含行政区划名称
  if (zgswskfjmc.includes(xzqhmc)) {
    // 如果已包含，则只显示主管税务所分局名称
    return zgswskfjmc;
  } else {
    // 如果不包含，则显示行政区划名称+主管税务所分局名称(不添加空格)
    return `${xzqhmc}${zgswskfjmc}`;
  }
}

/**
 * 构建树形结构数据
 * @param {Array} companyList - 主户列表
 * @param {Array} kqjgList - 跨区税源户列表
 * @param {Object} options - 配置选项
 * @param {String} options.allValue - "全部管辖企业"的值，默认为'ALL'
 * @param {String} options.allLabel - "全部管辖企业"的标签，默认为'全部管辖企业'
 * @returns {Array} 树形结构数据
 */
export function buildTreeData(companyList, kqjgList, options = {}) {
  const { allValue = 'ALL', allLabel = '全部管辖企业' } = options;
  const treeData = [];
  
  // 添加"全部管辖企业"选项
  treeData.push({
    value: allValue,
    label: allLabel,
    children: []
  });
  
  if (companyList && companyList.length > 0) {
    // 筛选主户（qylxz === '1'）
    const mainCompanies = companyList.filter(item => item.qylxz === '1');
    
    mainCompanies.forEach(mainCompany => {
      const treeNode = {
        value: mainCompany.djxh,
        label: mainCompany.jgmc,
        children: []
      };
      
      // 查找与主户纳税人识别号一致的跨区税源户
      if (kqjgList && kqjgList.length > 0) {
        const relatedKqjg = kqjgList.filter(kqItem => 
          kqItem.nsrsbh === mainCompany.nsrsbh && kqItem.qylxz === '3'
        );
        
        // 将跨区税源户作为子节点添加
        relatedKqjg.forEach(kqItem => {
          treeNode.children.push({
            value: kqItem.djxh,
            label: kqItem.jgmc
          });
        });
      }
      
      treeData.push(treeNode);
    });
  }
  
  // 如果有独立的跨区税源户（没有对应主户），单独添加
  if (kqjgList && kqjgList.length > 0) {
    const mainNsrsbhList = companyList ? companyList.map(item => item.nsrsbh) : [];
    const independentKqjg = kqjgList.filter(kqItem => 
      kqItem.qylxz === '3' && !mainNsrsbhList.includes(kqItem.nsrsbh)
    );
    
    independentKqjg.forEach(kqItem => {
      treeData.push({
        value: kqItem.djxh,
        label: kqItem.jgmc,
        children: []
      });
    });
  }
  
  return treeData;
}

/**
 * 构建简单的企业选择列表（用于select组件）
 * @param {Array} companyList - 主户列表
 * @param {Array} kqjgList - 跨区税源户列表
 * @param {Object} options - 配置选项
 * @param {String} options.allValue - "全部管辖企业"的值，默认为'ALL'
 * @param {String} options.allLabel - "全部管辖企业"的标签，默认为'全部管辖企业'
 * @returns {Array} 企业选择列表
 */
export function buildCompanySelectList(companyList, kqjgList, options = {}) {
  const { allValue = 'ALL', allLabel = '全部管辖企业' } = options;
  const selectList = [];
  
  // 添加"全部管辖企业"选项
  selectList.push({
    value: allValue,
    label: allLabel
  });
  
  // 合并所有企业数据
  let allCompanyList = [];
  if (companyList && companyList.length > 0) {
    allCompanyList = allCompanyList.concat(companyList);
  }
  if (kqjgList && kqjgList.length > 0) {
    allCompanyList = allCompanyList.concat(kqjgList);
  }
  
  if (allCompanyList.length > 0) {
    // 将企业列表转换为下拉选项格式
    const companyOptions = allCompanyList.map((item) => ({
      value: item.djxh, // 使用djxh作为value
      label: item.jgmc  // 使用机构名称作为label
    }));
    
    selectList.push(...companyOptions);
  }
  
  return selectList;
}

/**
 * 收集所有企业的ID信息用于查询
 * @param {Array} companyList - 主户列表
 * @param {Array} kqjgList - 跨区税源户列表
 * @returns {Object} 包含nsrsbhList和djxhList的对象
 */
export function collectCompanyIds(companyList, kqjgList) {
  const companyNsrsbhList = [];
  const companyDjxhList = [];
  
  if (companyList && companyList.length > 0) {
    companyList.forEach(item => {
      companyNsrsbhList.push(item.nsrsbh);
      companyDjxhList.push(item.djxh);
    });
  }
  
  if (kqjgList && kqjgList.length > 0) {
    kqjgList.forEach(item => {
      companyNsrsbhList.push(item.nsrsbh);
      companyDjxhList.push(item.djxh);
    });
  }
  
  return {
    companyNsrsbhList,
    companyDjxhList
  };
}

/**
 * 从sessionStorage获取企业数据
 * @returns {Object} 包含companyList和kqjgList的对象
 */
export function getCompanyDataFromStorage() {
  try {
    const companyList = JSON.parse(window.sessionStorage.getItem('companyList')) || [];
    const kqjgList = JSON.parse(window.sessionStorage.getItem('kqjgList')) || [];
    return { companyList, kqjgList };
  } catch (error) {
    console.error('从sessionStorage获取企业数据失败:', error);
    return { companyList: [], kqjgList: [] };
  }
}

/**
 * 将企业数据存储到sessionStorage
 * @param {Array} companyList - 主户列表
 * @param {Array} kqjgList - 跨区税源户列表
 */
export function saveCompanyDataToStorage(companyList, kqjgList) {
  try {
    window.sessionStorage.setItem('companyList', JSON.stringify(companyList || []));
    window.sessionStorage.setItem('kqjgList', JSON.stringify(kqjgList || []));
  } catch (error) {
    console.error('保存企业数据到sessionStorage失败:', error);
  }
}

/**
 * 初始化企业数据
 * 从sessionStorage获取企业列表数据，如果缓存为空则从API获取
 * @param {Function} apiFunction - 获取企业数据的API函数
 * @param {Object} options - 配置选项
 * @param {String} options.allValue - "全部管辖企业"的值，默认为'ALL'
 * @param {String} options.allLabel - "全部管辖企业"的标签，默认为'全部管辖企业'
 * @param {Boolean} options.buildTree - 是否构建树形结构，默认为true
 * @returns {Object} 包含企业数据和配置的对象
 */
export function initCompanyData(options = {}) {
  const { 
    allValue = 'ALL', 
    allLabel = '全部管辖企业',
    buildTree = true 
  } = options;
  
  try {
    // 从sessionStorage获取数据
    let { companyList, kqjgList } = getCompanyDataFromStorage();
    
    // 确保数据为数组格式
    companyList = companyList || [];
    kqjgList = kqjgList || [];
    
    // 处理kqjgList中qylxz === '3'的数据，转换jgmc格式
    if (kqjgList && kqjgList.length > 0) {
      kqjgList = kqjgList.map(item => {
        if (item.qylxz === '3') {
          // 格式化跨区税源位置信息
          const kqsyLocation = formatKqsyLocation(item);
          return {
            ...item,
            jgmc: `【跨区税源】${kqsyLocation}`
          };
        }
        return item;
      });
    }
    
    // 构建数据结构
    const result = {
      companyList,
      kqjgList,
      ...collectCompanyIds(companyList, kqjgList)
    };
    
    if (buildTree) {
      result.treeData = buildTreeData(companyList, kqjgList, { allValue, allLabel });
    } else {
      result.selectList = buildCompanySelectList(companyList, kqjgList, { allValue, allLabel });
    }
    
    return result;
  } catch (error) {
    console.error('初始化企业数据失败:', error);
    return {
      companyList: [],
      kqjgList: [],
      companyNsrsbhList: [],
      companyDjxhList: [],
      treeData: buildTree ? [{ value: allValue, label: allLabel, children: [] }] : undefined,
      selectList: !buildTree ? [{ value: allValue, label: allLabel }] : undefined
    };
  }
}

/**
 * 处理企业选择变化的通用逻辑
 * @param {Array|String} value - 选中的值
 * @param {String} allValue - "全部管辖企业"的值
 * @returns {Array} 处理后的选中值数组
 */
export function handleCompanyChange(value, allValue = 'ALL') {
  // 获取当前选中的值数组
  let selectedValues = value;
  if (!Array.isArray(selectedValues)) {
    selectedValues = value ? [value] : [];
  }
  
  if (!selectedValues || selectedValues.length === 0) {
    return [];
  }

  // 检查是否包含"全部管辖企业"
  const hasSelectAll = selectedValues.includes(allValue);
  // 检查是否有其他具体企业被选中
  const hasOtherSelections = selectedValues.some(val => val !== allValue);
  
  // 如果同时选中了"全部管辖企业"和其他具体企业，则移除"全部管辖企业"
  if (hasSelectAll && hasOtherSelections) {
    return selectedValues.filter(val => val !== allValue);
  }
  
  return selectedValues;
}

/**
 * 获取所有企业的djxh集合
 * @param {Array} companyList - 主户列表
 * @param {Array} kqjgList - 跨区税源户列表
 * @returns {Array} 所有企业的djxh数组
 */
export function getAllCompanyDjxhList(companyList, kqjgList) {
  try {
    let allCompanyList = [];
    
    if (companyList && companyList.length > 0) {
      allCompanyList = allCompanyList.concat(companyList);
    }
    if (kqjgList && kqjgList.length > 0) {
      allCompanyList = allCompanyList.concat(kqjgList);
    }
    
    // 提取所有企业的djxh
    const djxhList = allCompanyList.map(item => item.djxh).filter(djxh => djxh);
    return djxhList;
  } catch (error) {
    console.error('获取企业djxh集合失败:', error);
    return [];
  }
}

/**
 * 收集选中企业的nsrsbh和djxh列表
 * @param {Array} companyTreeOptions - 企业树形选项数据
 * @param {Array} selectedCompanies - 选中的企业列表
 * @param {Array} allNsrsbhList - 全部企业的nsrsbh列表
 * @param {Array} allDjxhList - 全部企业的djxh列表
 * @returns {Object} 包含nsrsbhList和djxhList的对象
 */
export function collectSelectedCompanyData(companyTreeOptions, selectedCompanies, allNsrsbhList, allDjxhList) {
  if (selectedCompanies.includes('ALL') || selectedCompanies.length === 0) {
    // 选择了"全部管辖企业"选项，使用全部企业数据
    return {
      nsrsbhList: allNsrsbhList,
      djxhList: allDjxhList
    };
  }
  
  // 处理选中的具体企业数据（排除"全部管辖企业"选项）
  const nsrsbhList = [];
  const djxhList = [];
  
  /**
   * 递归收集树形结构中的所有企业ID
   * @param {Array} nodes - 树形节点数组
   * @param {Array} selectedValues - 选中的值数组
   */
  const collectSelectedCompanies = (nodes, selectedValues) => {
    nodes.forEach(node => {
      if (selectedValues.includes(node.value) && node.value !== 'ALL') {
        // 选择了具体企业（排除"全部管辖企业"）
        djxhList.push(node.value);
        // 从缓存中查找对应的nsrsbh
        const findNsrsbh = (treeNodes) => {
          for (const treeNode of treeNodes) {
            if (treeNode.value === node.value) {
              // 从原始数据中查找nsrsbh
              const companyList = JSON.parse(window.sessionStorage.getItem('companyList') || '[]');
              const kqjgList = JSON.parse(window.sessionStorage.getItem('kqjgList') || '[]');
              const allCompanies = [...companyList, ...kqjgList];
              const company = allCompanies.find(c => c.djxh === node.value);
              if (company && company.nsrsbh) {
                nsrsbhList.push(company.nsrsbh);
              }
              return;
            }
            if (treeNode.children && treeNode.children.length > 0) {
              findNsrsbh(treeNode.children);
            }
          }
        };
        findNsrsbh(companyTreeOptions);
      }
      
      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        collectSelectedCompanies(node.children, selectedValues);
      }
    });
  };
  
  collectSelectedCompanies(companyTreeOptions, selectedCompanies);
  
  // 去重并返回
  return {
    nsrsbhList: [...new Set(nsrsbhList)],
    djxhList: [...new Set(djxhList)]
  };
}

export default {
  formatKqsyLocation,
  buildTreeData,
  buildCompanySelectList,
  collectCompanyIds,
  getCompanyDataFromStorage,
  saveCompanyDataToStorage,
  initCompanyData,
  handleCompanyChange,
  getAllCompanyDjxhList,
  collectSelectedCompanyData
};