const type = ['String', 'Array', 'Object', 'Number', 'FormData', 'Function'];

function isType(obj, t) {
  return Object.prototype.toString.call(obj) === `[object ${t}]`;
}

function createJudgeFactory() {
  const judge = {};
  type.forEach((item) => {
    judge[`is${item}`] = (obj) => isType(obj, item);
  });
  return judge;
}

// eslint-disable-next-line no-shadow
export function downloadExcel(type, name, data) {
  const link = document.createElement('a');
  const blob = new Blob([data]);
  link.style.display = 'none';
  link.href = URL.createObjectURL(blob);
  link.setAttribute('download', `${name}.${type}`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 清除对象中字符串的首尾空格
export function objectStringTrim(data = {}) {
  if (!data) return data;
  const newData = {};
  Object.entries(data).forEach(([key, value]) => {
    if (isType(value, 'String')) {
      newData[key] = value.trim();
      return;
    }
    newData[key] = value;
  });
  return newData;
}

// 是否为11以下的ie浏览器
export function isMSIE() {
  return !!window.ActiveXObject || 'ActiveXObject' in window;
}
// 判断是否是ie11
export function isIE11() {
  if (/Trident\/7\./.test(navigator.userAgent)) {
    return true;
  }
  return false;
}

export default {
  isType,
  ...createJudgeFactory(),
};
