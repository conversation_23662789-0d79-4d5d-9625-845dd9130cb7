/* eslint-disable prettier/prettier */
// 日期库
import moment from 'dayjs';

// 获取本年第n季度的起止日期、
function getQuarter(_date, format = 'YYYY-MM-DD') {
  const nowQuarter = _date ? moment(_date).quarter() : moment().quarter();
  const quarter = nowQuarter;
  const monthArray = [1, 4, 7, 10];
  // 获取季度对应的月份
  const month = monthArray[quarter - 1];
  const m = moment().month(month).date(5);
  const date = {};
  date.startDay = moment(m.startOf('quarters').toDate().getTime()).format(format);
  date.endDay = moment(m.endOf('quarters').toDate().getTime()).format(format);
  return [date.startDay, date.endDay, quarter];
}

// 日期比较-x是否早于y
function dateBigThenLastDate(date, lastDate) {
  return moment(date).valueOf() > moment(lastDate).valueOf();
}

// 日期比较-x是否晚于y
function dateLessThenLastDate(date, lastDate) {
  return moment(date).valueOf() < moment(lastDate).valueOf();
}

// 获取指定月份首末日
function getMonthStartEndDay(_date) {
  const time = _date ? new Date(_date).setDate(1) : new Date().setDate(1);
  const date = {};
  date.startDay = moment(time).format('YYYY-MM-DD');
  date.endDay = `${moment(time).format('YYYY-MM')}-${moment(time).daysInMonth()}`;
  return [date.startDay, date.endDay];
}

// 获取指定月份次月的首末日
function getPreMonthStartEndDay(_date) {
  const time = _date ? new Date(_date).setDate(1) : new Date().setDate(1);
  const date = {};
  date.startDay = moment(time).add(1, 'month').startOf('month').format('YYYY-MM-DD');
  date.endDay =  moment(time).add(1, 'month').endOf('month').format('YYYY-MM-DD');
  return [date.startDay, date.endDay];
}
// 获取指定月份上月的首末日
function getLastMonthStartEndDay(_date) {
  const time = _date ? new Date(_date).setDate(1) : new Date().setDate(1);
  const date = {};
  date.startDay = moment(time).subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
  date.endDay =  moment(time).subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
  return [date.startDay, date.endDay];
}

// 获取当月第一天、当月最后一天
function getThisMonthRange(format = 'YYYY-MM-DD') {
  return [moment().startOf('month').format(format), moment().endOf('month').format(format)];
}

// 获取上月第一天、上月最后一天
function getLastMonthRange(format = 'YYYY-MM-DD') {
  return [
    moment().subtract(1, 'month').startOf('month').format(format),
    moment().subtract(1, 'month').endOf('month').format(format),
  ];
}

// 获取次月第一天、次月最后一天
function getPreMonthRange(format = 'YYYY-MM-DD') {
  return [
    moment().add(1, 'month').startOf('month').format(format),
    moment().add(1, 'month').endOf('month').format(format),
  ];
}

function momentToString(v, format) {
  if (!v) return v;
  return moment(v).format(format);
}

function dateToMoment(v) {
  if (!v) return '';
  return moment(v);
}

function dateToString(v) {
  if (!v) return moment().format('YYYY-MM-DD HH:mm:ss');
  return moment(v).format('YYYY-MM-DD HH:mm:ss');
}

function stringToMoment(v) {
  if (!v) return v;
  // 兼容IE
  let date = v.replace(/-\d{1}$/, function (m) {
    return `-0${m.charAt(1)}`;
  });
  // 兼容IE不能处理未格式化带T的时间格式
  if (date.indexOf('T') > -1) {
    //
    date = date.substring(0, 10);
  }
  return moment(new Date(date));
}

function getLastWeekRange(format = 'YYYY-MM-DD') {
  return [
    moment().subtract(1, 'week').startOf('week').format(format),
    moment().subtract(1, 'week').endOf('week').format(format),
  ];
}

function getThisWeekRange(date, format = 'YYYY-MM-DD') {
  return [moment(date).startOf('week').format(format), moment(date).endOf('week').format(format)];
}

function getThisYearRange(format = 'YYYY-MM-DD') {
  return [moment().startOf('year').format(format), moment().endOf('year').format(format)];
}

function getLastYearRange(format = 'YYYY-MM-DD') {
  return [
    moment().subtract(1, 'year').startOf('year').format(format),
    moment().subtract(1, 'year').endOf('year').format(format),
  ];
}

function getDate(format) {
  if (!format) return moment().valueOf();
  return moment().format(format);
}

export {
  getQuarter,
  dateBigThenLastDate,
  dateLessThenLastDate,
  getDate,
  getMonthStartEndDay,
  dateToString,
  dateToMoment,
  stringToMoment,
  getPreMonthRange,
  momentToString,
  getLastWeekRange,
  getThisWeekRange,
  getThisMonthRange,
  getLastMonthRange,
  getThisYearRange,
  getLastYearRange,
  getPreMonthStartEndDay,
  getLastMonthStartEndDay,
};
