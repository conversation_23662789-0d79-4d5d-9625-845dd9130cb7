import router from '@/pages/index/router';
import { getNamedSession } from './proactor.js';

/**
 * 防抖处理
 * @param {function} fn 需要防抖的函数
 * @param {number} delay 防抖时间 毫秒
 * @returns {function} fn
 * 注意使用时不能用箭头函数，而应使用 throttle(function () { ... })，否则函数体内无法使用 vue 实例 this
 */
export function throttle(fn, delay = 500) {
  let startTime = 0;
  return function (...args) {
    const nowTime = +new Date();
    if (nowTime - startTime < delay) return;
    startTime = nowTime;
    fn.apply(this, args);
  };
}

// 关闭窗口
export function closePage(win = window) {
  win.close();
  win.opener = null;
  win.open('', '_self');
}

// 截取业务编码
export function getYwbmByURL() {
  let { hash = '' } = window.location;
  // 去掉 #/yyzx/
  hash = hash.slice(7) || '';
  const index1 = hash.indexOf('/');
  const index2 = hash.indexOf('?');
  let index = hash.length;
  if (index1 > 0) {
    index = index1;
  } else if (index2 > 0) {
    index = index2;
  }
  if (!hash || index <= 0) {
    return '';
  }
  return hash.slice(0, index);
}

/**
 * 要素页获取报送模式函数
 * @param {boolean} isSubmit 是否为提交时调用，默认为初始化时调用
 * @returns 'xxxx'
 */
/** 返回值约定说明
 * 报送模式。目前申报业务包含了多种申报报送模式，需要进行区分，保存到流水表中，方便后续甲方统计数据
 * bsms（CHAR(4)）:前两位数字为申报模式大类，后两位为小类（以下罗列是目前使用的，后续新增继续补充）
 * 大类：
 *  00：在线申报
 *  01：离线申报
 *  02：补录式申报
 *  03：确认式
 *  04：excel导入
 *  05：财报转换
 *  06：综合申报
 * 小类：
 *  00：填写式申报
 *  01：一键零申报
 *  02：简易申报
 *  03：按税务机关确定的其他方式预缴
 *  04：跨地区汇总纳税企业分支机构
 *  05：要素式申报
 *  06：集团办税申报
 * <p>
 * 当前定义的业务的报送模式
 * 在线填写 0000
 * excel导入 0400
 * 财报转换 0500
 * 确认式-跨地区汇总纳税企业分支机构 0304
 * 补录式一键零申报 0201
 * 补录式简易申报 0202
 * 补录式-按税务机关确定的其他方式预缴 0203
 * 离线申报 0100
 * 集团办税申报 0006
 */
export function getBsms(duringSubmitForm = false) {
  const { name, query } = router.currentRoute;
  const sbms = name.substring(name.lastIndexOf('/') + 1);

  const YWLC = {
    // 补录式-一键零申报
    yjlsb: '0201',
    // 补录式-简易申报
    jysb: '0202',
    // 补录式-按税务机关确定的其他方式预缴
    aswjg: '0203',
    // 确认式-跨地区汇总纳税企业分支机构
    kdqjyfzjg: '0304',
  };
  let bsms = YWLC[sbms];
  if (!bsms) {
    // 补录式-要素申报
    bsms = '0205';
    // 针对代扣代缴文化事业建设费申报使用vue新翻的填表页做特殊处理
    if (sbms === 'tbsb') {
      // 填写式申报
      bsms = '0000';
    }
  }
  const { Sbuuid, Pzxh, JsjgUuid, jtbsSbuuid } = query;
  if (!duringSubmitForm) {
    // 初始化时，更正的情况下都是 0000，因为更正都走填表式
    const isCorrect = !!Sbuuid && !!Pzxh;
    isCorrect && (bsms = '0000');
  }
  // 综合申报只有要素模式，进入元子业务覆盖为 0600
  if (JsjgUuid) {
    bsms = '0600';
  }
  // 集团办税调整报送模式
  const jtbsSession = getNamedSession('jtbsSbuuid', jtbsSbuuid);
  if (jtbsSession) bsms = '0006';
  return bsms;
}
