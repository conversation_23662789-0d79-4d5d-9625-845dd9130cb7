/**
 * 对象转url参数
 * @param {*} data,对象
 * @param {*} isPrefix,是否自动加上"?"
 */
function queryParams(data = {}, isPrefix = true) {
  const prefix = isPrefix ? '?' : '';
  const result = [];

  // eslint-disable-next-line no-restricted-syntax, guard-for-in
  for (const key in data) {
    const value = data[key];
    result.push(`${key}=${value}`);
  }
  return result.length ? prefix + result.join('&') : '';
}

export { queryParams };
