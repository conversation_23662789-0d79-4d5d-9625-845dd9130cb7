/*
 * @Descripttion: https://github.com/nefe/number-precision/blob/master/src/index.ts
 * @Author: gan<PERSON><PERSON>
 * @Date: 2020-09-18 10:34:57
 * @LastEditors  : ganbowen
 * @LastEditTime : 2021-11-16 16:26:21
 */

// 是否进行边界检查，默认开启
// eslint-disable-next-line no-underscore-dangle
const _boundaryCheckingState = true;

const DOUBLE_BYTE_THRESHOLD = 255;

/**
 * 把错误的数据转正
 * strip(0.09999999999999998)=0.1
 */
function strip(num = 0, precision = 15) {
  return +parseFloat(Number(num).toPrecision(precision));
}

/**
 * Return digits length of a number
 * @param {*number} num Input number
 */
function digitLength(num = 0) {
  // Get digit length of e
  const eSplit = num.toString().split(/[eE]/);
  const len = (eSplit[0].split('.')[1] || '').length - +(eSplit[1] || 0);
  return len > 0 ? len : 0;
}

/**
 * 把小数转成整数，支持科学计数法。如果是小数则放大成整数
 * @param {*number} num 输入数
 */
function float2Fixed(num = 0) {
  if (num.toString().indexOf('e') === -1) {
    return Number(num.toString().replace('.', ''));
  }
  const dLen = digitLength(num);
  // eslint-disable-next-line no-restricted-properties
  return dLen > 0 ? strip(Number(num) * Math.pow(10, dLen)) : Number(num);
}

/**
 * 检测数字是否越界，如果越界给出提示
 * @param {*number} num 输入数
 */
function checkBoundary(num = 0) {
  if (_boundaryCheckingState) {
    if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {
      console.warn(`${num} is beyond boundary when transfer to integer, the results may not be accurate`);
    }
  }
}

/**
 * 四舍五入
 */
export function round(num, ratio) {
  // eslint-disable-next-line no-restricted-properties
  const base = Math.pow(10, ratio);
  // eslint-disable-next-line no-use-before-define
  return divide(Math.round(times(num, base)), base);
}

/**
 * 按字节长度截取字符串
 * str: 原字符串
 * len: 截取字节长度
 */
export function subStringByBytes(str, len) {
  if (!str || str === undefined) return '';
  let num = 0;
  let result = '';
  for (let i = 0; i < str.length; i++) {
    num += str.charCodeAt(i) > DOUBLE_BYTE_THRESHOLD ? 2 : 1;
    if (num > len) {
      break;
    } else {
      result = str.substring(0, i + 1);
    }
  }
  return result;
}

/**
 * 精确乘法
 */
export function times(num1, num2, ...others) {
  if (others.length > 0) {
    return times(times(num1, num2), others[0], ...others.slice(1));
  }
  const num1Changed = float2Fixed(num1);
  const num2Changed = float2Fixed(num2);
  const baseNum = digitLength(num1) + digitLength(num2);
  const leftValue = num1Changed * num2Changed;
  checkBoundary(leftValue);
  // eslint-disable-next-line no-restricted-properties
  return leftValue / Math.pow(10, baseNum);
}

/**
 * 精确加法
 */
export function plus(num1, num2, ...others) {
  if (others.length > 0) {
    return plus(plus(num1, num2), others[0], ...others.slice(1));
  }
  // eslint-disable-next-line no-restricted-properties
  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));
  return (times(num1, baseNum) + times(num2, baseNum)) / baseNum;
}

/**
 * 精确减法
 */
export function minus(num1, num2, ...others) {
  if (others.length > 0) {
    return minus(minus(num1, num2), others[0], ...others.slice(1));
  }
  // eslint-disable-next-line no-restricted-properties
  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));
  return (times(num1, baseNum) - times(num2, baseNum)) / baseNum;
}

/**
 * 精确除法
 */
export function divide(num1, num2, ...others) {
  if (others.length > 0) {
    return divide(divide(num1, num2), others[0], ...others.slice(1));
  }
  const num1Changed = float2Fixed(num1);
  const num2Changed = float2Fixed(num2);
  checkBoundary(num1Changed);
  checkBoundary(num2Changed);
  // fix: 类似 10 ** -4 为 0.00009999999999999999，strip 修正
  // eslint-disable-next-line no-restricted-properties
  return times(num1Changed / num2Changed, strip(Math.pow(10, digitLength(num2) - digitLength(num1))));
}

/**
 * 科学计数法转string
 * @param {number} num
 * @returns {string}
 */
export function exponentialNumToSting(num) {
  const m = num.toExponential().match(/(\d)(?:\.(\d*))?e([+-]\d+)/);
  // (?:\.(\d*)) 匹配包含小数点后的数字 但不保存到match的数组中
  let s = 0;
  const repeatStr = '0';
  if (+m[3] > 20) {
    // 兼容当大与20时toFixed返回的还是科学计数法
    s = +m[3] - 20;
    // eslint-disable-next-line no-restricted-properties
    return `${m[1]}.${m[2] || 0}` * Math.pow(10, 20) + repeatStr.repeat(s);
  }
  return num.toFixed(Math.max(0, (m[2] || '').length - m[3]));
}

/**
 * 计算内容的字节长度：中文汉字为2字节，英文、数字为1字节
 */

export function getByteLength(data) {
  let total = 0;
  if (data === undefined || data === null) {
    return total;
  }
  for (let i = 0; i < data.length; ++i) {
    total += data.charCodeAt(i) > DOUBLE_BYTE_THRESHOLD ? 2 : 1;
  }
  return total;
}

/**
 * 校验数据是否为undefined、0、'';
 */
export function checkIsNull(data) {
  let result = false;
  const isNaN = Number.isNaN(data);
  if (data === undefined || data === '' || data === null || isNaN === true || data === 0) {
    result = true;
  }
  return result;
}
