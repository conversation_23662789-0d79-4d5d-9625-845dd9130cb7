// 手机号码
export function mobile(rule, value, callback) {
  if (!value) {
    callback();
  }
  // let reg = /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/;
  if (!/^\d{11}$/.test(value)) {
    callback(new Error('请输入正确的手机号码'));
  } else {
    callback();
  }
}

// 电话座机号码
export function telephone(rule, value, callback) {
  if (!value) {
    callback();
  }
  const reg = /^(([0+]\d{2,3}-)?(0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/;
  if (!reg.test(value)) {
    callback(new Error('请输入正确的电话号码'));
  } else {
    callback();
  }
}

// 姓名
export function name(rule, value, callback) {
  if (!/^[\u4e00-\u9fa5]{2,10}$/.test(value)) {
    callback(new Error('请输入正确的姓名'));
  } else {
    callback();
  }
}

export function commonName(rule, value, callback) {
  if (!value || value === '-') {
    callback(new Error('输入不能为空'));
  } else {
    callback();
  }
}

// 输入过滤（检测）
export function common(rule, value, callback) {
  if (/[_%<>(\\)/]/.test(value)) {
    callback(new Error('不能包含特殊字段'));
  } else {
    callback();
  }
}

// 身份证校验
export function ID(rule, value, callback) {
  // let reg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!/^[0-9]{16,17}[a-zA-Z0-9]{1}$/.test(value) || value[0] === '0') {
    callback(new Error('请输入正确的身份证号'));
  } else {
    callback();
  }
}

// 数字校验---正整数
export function positiveNumber(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入数值'));
  } else if (!/^[0-9]*$/.test(value) || value[0] === '0') {
    callback(new Error('输入数值不正确，请输入大于0的整数'));
  } else {
    callback();
  }
}

export function price(rule, value, callback) {
  if (!value) {
    callback(new Error('价格不能为空'));
  } else if (value && !(/^[+-]?((0|([1-9]\d*))\.\d+)?$/.test(value) || /^\d*$/.test(value))) {
    callback(new Error('价格输入不符合规范'));
  } else {
    callback();
  }
}

export function email(rule, value, callback) {
  if (!value) {
    callback();
  }
  const reg = /^([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
  if (!reg.test(value)) {
    callback(new Error('请输入正确的邮箱号码'));
  } else {
    callback();
  }
}
export function mail(rule, value, callback) {
  if (!value) {
    callback();
  }
  if (!/^\d{6}$/.test(value)) {
    callback(new Error('请输入正确的邮编'));
  } else {
    callback();
  }
}

export function posNumber(rule, value, callback) {
  if (!/^\d*$/.test(value)) {
    callback(new Error('请输入正整数'));
  } else {
    callback();
  }
}
