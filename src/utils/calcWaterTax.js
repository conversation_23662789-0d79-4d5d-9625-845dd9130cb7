import { times, plus, minus, divide } from '@/utils/calc';

export function jstrue(sqljqsl, bqqsl, jhqsl) {
  console.log('params', sqljqsl, bqqsl, jhqsl);
  const cjhbl = divide(minus(plus(sqljqsl, bqqsl), jhqsl), jhqsl);
  // TODO 通过系统参数获取,系统参数配置规则为分号以前为倍数,分号以后为倍数对应的超计划比例,如果倍数比计划大1,则表示最后一个倍数为大于最后一集超计划比例的倍数
  const xtcs = '1,2,3;0,0.3';
  const ar = xtcs.split(';');

  const a = ar[0].split(','); // 1,2,3
  const b = ar[1].split(','); // 0,0.3

  let bqdc = 0; // 档次
  for (let i = b.length - 1; i > 0; i--) {
    if (b.length < a.length && cjhbl > b[i]) {
      bqdc = i + 1;
      break;
    } else if (cjhbl <= b[i] && cjhbl > b[i - 1]) {
      bqdc = i;
      break;
    } else if (cjhbl <= 0) {
      bqdc = 0;
      break;
    }
  }

  let sqdc = 0; // 档次
  const cjhblsq = divide(minus(sqljqsl, jhqsl), jhqsl);
  for (let i = b.length - 1; i > 0; i--) {
    if (b.length < a.length && cjhblsq > b[i]) {
      sqdc = i + 1;
      break;
    } else if (cjhblsq <= b[i] && cjhblsq > b[i - 1]) {
      sqdc = i;
      break;
    } else if (cjhblsq <= 0) {
      sqdc = 0;
      break;
    }
  }

  const sskcsList = [0];
  for (let i = 1; i < b.length; i++) {
    const zjjsje = times(jhqsl, +b[i] + 1, a[i]);
    let jjjsje = times(jhqsl, a[0]);
    for (let j = 1; j <= i; j++) {
      jjjsje = plus(jjjsje, times(minus(b[j], b[j - 1]), jhqsl, a[j]));
    }
    // console.log('zjjsje===========>', zjjsje);
    // console.log('jjjsje===========>', jjjsje);
    sskcsList.push(divide(minus(zjjsje, jjjsje), jhqsl));
  }
  // debugger;
  if (b.length < a.length) {
    const zjjsje = times(jhqsl, 10000000, a[a.length - 1]);
    let jjjsje = times(jhqsl, a[0]);
    for (let i = 1; i <= b.length - 1; i++) {
      jjjsje = plus(jjjsje, times(minus(b[i], b[i - 1]), jhqsl, a[i]));
      // jjjsje = plus(jjjsje, times(minus(b[i], times(b[i - 1]), jhqsl, a[i])));
    }
    jjjsje = plus(jjjsje, times(minus(10000000, b[b.length - 1], 1), jhqsl, a[a.length - 1]));
    // console.log('zjjsje===========>11111', zjjsje);
    // console.log('jjjsje===========>111111', jjjsje);
    // console.log('jhqsl===========>111111', sskcsList);
    sskcsList.push(divide(minus(zjjsje, jjjsje), jhqsl));
    // console.log('jhqsl===========>111111', jhqsl);
    // console.log('jhqsl===========>111111', jhqsl);
    // console.log('jhqsl===========>111111', jhqsl);
  }
  // debugger;
  // console.log('  times(plus(sqljqsl, bqqsl), a[bqdc])', times(plus(sqljqsl, bqqsl), a[bqdc]));
  // console.log(' times(sskcsList[bqdc], jhqsl)', times(sskcsList[bqdc], jhqsl));
  // console.log(' times(sqljqsl, a[sqdc])', times(sqljqsl, a[sqdc]));
  // console.log(' times(sskcsList[sqdc], jhqsl)', times(sskcsList[sqdc], jhqsl));
  // console.log('   minus(times(sqljqsl, a[sqdc]), times(sskcsList[sqdc], jhqsl))',  minus(times(sqljqsl, a[sqdc]), times(sskcsList[sqdc], jhqsl));
  const je = minus(
    times(plus(sqljqsl, bqqsl), a[bqdc]),
    times(sskcsList[bqdc], jhqsl),
    minus(times(sqljqsl, a[sqdc]), times(sskcsList[sqdc], jhqsl)),
  );
  console.log('result', je);
  return je;
}
