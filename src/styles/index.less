html,body,div,span,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,abbr,address,cite,code,del,dfn,em,img,ins,kbd,q,samp,small,strong,sub,sup,var,b,i,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary,time,mark,audio,video
{
  padding: 0;
  margin: 0;
  // font-size: 100%;
  // vertical-align: baseline;
  border: 0;
}

//.t-layout__content {
//  min-width: 1217px;
//  .t-layout__content {
//    min-width: initial;
//  }
//}
.fl {
  float: left;
}

.fr {
  float: right;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
}
::v-deep .cxssbbg::-webkit-scrollbar {
  display: none;
  width: 0;
}

::v-deep .cxssbbg {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.clearfix:after{
  display: block;
  height: 0;
  clear: both;
  content: "";
  visibility: hidden;
}

.clearfix {
  zoom: 1;
}

// 除左侧菜单的右侧内容区域的固定样式
.content-main {
  padding: 24px;
  background: #fff;
  &-header {
    font-size: 24px;
    font-weight: 600;
    line-height: 28px;
    letter-spacing: 0;
    color: #333;
    text-align: center;
    background: #fff;
  }
}
.mt-24 {
  margin-top: 24px;
}
.mb-12 {
  margin-bottom: 12px;
}
.mb-24 {
  margin-bottom: 24px;
}
.mr-12 {
  margin-right: 12px;
}
.mr-24 {
  margin-right: 24px;
}