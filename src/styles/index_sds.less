html,body,div,span,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,abbr,address,cite,code,del,dfn,em,img,ins,kbd,q,samp,small,strong,sub,sup,var,b,i,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary,time,mark,audio,video
{
  padding: 0;
  margin: 0;
  border: 0;
}
html{
  min-width: 1232px;
  background: #F7F8FA;
}

.fl {
  float: left;
}

.fr {
  float: right;
}
.tdgv-wrapper .t-layout, .tdgv-wrapper .t-layout__direction-vertical{
  background: unset;
}
.tdgv-wrapper .t-layout__content {
  min-width: unset; 
  background: unset;
}
.clearfix:after{
  display: block;
  height: 0;
  clear: both;
  content: "";
  visibility: hidden;
}

.clearfix {
  zoom: 1;
}

.tdgv-wrapper .t-tree__item.t-is-disabled.t-tree__item--visible .t-tree__label {
  color: rgba(0, 0, 0, 0.9);
}
.tdgv-wrapper .t-input.t-is-disabled .t-input__inner::placeholder {
  color: #ccc;
}

//  临时处理手风琴组件gt-collapse-panel倒三角图标左边距，待腾讯组件调整正确后删除
.tdgv-wrapper .t-collapse-panel__wrapper .t-collapse-panel__header.t-is-clickable {
  padding-left: 20px;
}

//  覆盖tdesign-gt-vue写的引导组件样式
.tdgv-wrapper .t-guide__footer {
  padding: 0;
  border: 0;
}
//  提示图标颜色浅问题修改
.fxgt-tax-calc-item__label svg.fxgt-tax-calc-infoIcon,
.gt-tax-caption-info svg.gt-tax-caption-info-icon {
  color: rgb(102, 102, 102);
}

.tdgv-wrapper .t-upload__flow-table__batch-row {
  border-left: 0;
}