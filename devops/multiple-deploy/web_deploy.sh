#!/bin/bash

filename=$1
IFS=$'\n\n'

if [ ! -n "$1" ] ;then
    echo "请输入要替换的配置文件名"
else
	for line in `cat $filename`
	do
		# echo $line
		OLD_IFS="$IFS"
		IFS="="
		array=($line)
		IFS="$OLD_IFS"

		old_str=${array[0]}
		new_str=${array[1]}
		echo "正在将 '${old_str}' 替换为 '${new_str}'"
		sed -ie "s#$old_str#$new_str#g" `grep "$old_str" -rl ./ | grep -v "$1"`
		echo "'${old_str}' 替换成功"
	done
	echo "执行成功"
fi