def depots = [
[
    name: "${PROJECT_NAME}",
    origin: "${GIT_SSH_URL}",
  ]
]
def getCheckoutJob(depot) {
  return {
    dir(path: "./modules/${depot.get('name')}") {
      checkout([$class: 'GitSCM', branches: [[name: env.GIT_BUILD_REF]],
                          userRemoteConfigs: [[url: depot.get('origin'), credentialsId: env.CREDENTIALS_ID]]])
      script {
        if (depot.get('name') == 'coding-artifacts') {
          sh 'git submodule sync'
          sh 'git submodule update --init --recursive'
        }
      }
    }
  }
}
def application_name = env.APPLICATION_NAME.split(",")
def ports = env.PROTOCOL_PORTS.split(",")
def jobs = []
application_name.eachWithIndex{
	it,i -> jobs[i]=[application_name[i],ports[i]]

}

def parallelStagesMap = jobs.collectEntries {
    ["检测tsf应用${it[0]}" : generateStage(it)]
}
def generateStage(jdata) {
    echo '${project_pub_name}'
    return {
        stage("stage: ${project_pub_name}") {
            echo "${jdata[0]}"
            checkTsfApplication secretId: "${env.SECRET_ID}", secretKey: "${env.SECRET_KEY}", applicationName: "${project_pub_name}", applicationType: "${env.APPLICATION_TYPE}", endpoint: "${env.TSF_ENDPOINT}",  microserviceType: "${env.MICROSERVICE_TYPE}"
        }
    }
}


def parallelStagesMap2 = jobs.collectEntries {
    ["部署tsf应用${it[0]}" : generateStage2(it)]
}
def generateStage2(jdata) {
    echo '${project_pub_name}'
    return {
      stage("stage: ${project_pub_name}") {
        echo "${jdata[0]}"
        deployTsf secretId: "${env.SECRET_ID}", secretKey: "${env.SECRET_KEY}", endpoint: "${env.TSF_ENDPOINT}", applicationName: "${project_pub_name}", clusterName: "${env.CLUSTER_NAME}", namespace: "${DEPLOY_ENV}", groupName: "${project_pub_name}-${DY_ENV}",  pkgVersion: "${VERSION}-${DEPLOY_ENV}", protocolPorts: "[{Protocol:\"TCP\",Port:"+"${jdata[1]}"+",TargetPort:" +"${jdata[1]}"+"}]", accessType: "${env.ACCESS_TYPE}", updateType: "${env.UPDATE_TYPE}", businessLogNames: "${logpath}"
      }
    }
}
def deploy_env = "${DEPLOY_ENV}"
def createDY_ENV() {
    return deploy_env.split("-")[1]
}

def createName() {
    return deploy_env.split("-")[0]
}

// TODO: 这里应该要根据实际的运行域决定，待整理规范
def getClusterName() {
    if (env.DEPLOY_ENV.contains("dev") ){
       return "IPC-public-开发环境"
    }
    else if (env.DEPLOY_ENV.contains("test") ){
      return "IPC-public-测试环境"
     }
    else {
      return "IPC-public-演示环境"
    }
}


pipeline {
  agent any

  stages {
      stage("检出项目代码仓库") {
      steps {
        checkout([$class: 'GitSCM', branches: [[name: env.SUB_BRANCH]],
                    userRemoteConfigs: [[url: "${env.SUB_GIT_REPO_URL}/${project_name}.git", credentialsId: env.SUB_CREDENTIALS_ID]]])
        script {
                sh """
                echo "machine git.code.tencent.com" > ~/.netrc
                echo "login ${GIT_USERNAME}" >> ~/.netrc
                echo  "password ${GIT_PASSWORD}" >> ~/.netrc
                """
                sh 'cat ~/.netrc'
                sh 'git submodule sync'
                sh 'git submodule update --init --recursive'

        }
      }
    }
    stage('检出子仓库') {
      steps {
        script {
          checkoutJobs = [:]
          depots.findAll{
            depot -> (depot.get('origin') != '')
          }.each { depot ->
            echo "submodule > ${depot}"
            checkoutJobs[depot.get('name')] = getCheckoutJob(depot)
            checkoutJobs['failFast'] = false
          }
          parallel checkoutJobs
        }
      }
    }
     stage('校验tsf环境') {
        steps{
          script {
              parallel parallelStagesMap
            }
          }
        }
      stage('编译构建') {

        agent {
            docker {
             image 'ipcmaven.pdcts.com.cn:8082/npm:tsf'
              args '-u root --network=host -v /var/run/docker.sock:/var/run/docker.sock --add-host=raw.githubusercontent.com:************** -v /usr/bin/docker:/usr/bin/docker -v /etc/hosts:/etc/hosts'
              reuseNode true
            }
        }
        steps {
          script {
              withCredentials([usernamePassword(credentialsId: "${DOCKER_REGISTRY_CREDENTIALS_ID}", usernameVariable: 'REGISTRY_USER', passwordVariable: 'REGISTRY_PASS')]) {
              sh """
                    pwd
                    ls -ls .
                    ls -ls ./modules
                    npm config ls
                    mv ./modules/${DEPOT_NAME}/build .
                    # 编译构建
                    bash -e build/build.sh  ${TSF_REPO} ${project_pub_name} ${HARBOR_ACCOUNT_NAME} ${HARBOR_PASSWORD} ${image_version} ${BUILD_TYPE} ${NPM_BUILD_SCRIPT}
                 """
              }

          }
        }
      }


     stage('触发tsf部署') {
      when {
        environment name:'DEPLOY_ENABLE', value:'true'
      }
     steps {
        script {
            parallel parallelStagesMap2
        }
        sh """
            # 如果还要执行别的一些sh命令，可在此添加，比如企业微信的通知机器人
        """
      }
     }
    }
  environment {
    PROTOCOL = 'http' // 系统预留参数
    APPLICATION_TYPE = 'C' // 项目类型，系统预留参数
    MICROSERVICE_TYPE = 'N' // 系统预留参数
    DY_ENV = createDY_ENV() // 环境
    PName = createName() // 项目组名passid和里约网关passid一致
    image_version = "${VERSION}-${DEPLOY_ENV}" // 镜像版本
    project_name = "${APPLICATION_NAME}" // 项目工程名称
    project_pub_name = "${PName}-${GIT_PROJECT_NAME ? GIT_PROJECT_NAME : project_name}" // TSF 项目名称
    UPDATE_TYPE = "1" // 更新类型，运维预留参数，暂时未启用
    ACCESS_TYPE = "2" // 制品类型，运维预留参数，暂时未启用
    BUILD_TYPE = "${BUILD_TYPE}" // 编译类型
    CLUSTER_NAME = getClusterName() // 部署组名称
    logpath = "${PName}-web-logs" // 日志名称
  }
}
