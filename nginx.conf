worker_processes auto;
worker_rlimit_nofile 65535;
error_log logs/error.log;
pid /var/run/nginx.pid;
daemon off;

events {
    worker_connections 2048;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$http_referer"';

    access_log  logs/access.log  main;
    client_max_body_size    100m;
    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;

    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 256;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss application/javascript text/javascript application/vnd.ms-fontobject application/x-font-ttf font/opentype image/svg+xml image/x-icon;


    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    send_timeout 300s;
    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_read_timeout 300;

    include             /usr/local/openresty/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include servers/*.conf;

    server {
        listen       8080 default_server;
	    listen       [::]:8080 default_server;
        server_name  _;
        server_tokens off;
        #root         /usr/local/openresty/nginx/html/;
        
        # 重写env.config.js路径
        location ~ .+/env.config.js$  {
           rewrite ^(.*)$ /env.config.js break;
           root  /usr/local/openresty/nginx/html;
        }

        # 重写svg.js路径
        location ~ .+/static_res/svg.js$  {
           rewrite ^(.*)$ /static_res/svg.js break;
           root  /usr/local/openresty/nginx/html;
        }

        # 卸载context
        location /znsb/view/tzzx  {
           alias /etc/nginx/html;
           index index.html;
           try_files $uri $uri/ /index.html;
        }

        # 重写/assets/img/中图片资源路径
        location ~ .+/assets_res/img/  {
           rewrite ^.*(\/assets_res\/img\/.*)$ $1 break;
           root  /usr/local/openresty/nginx/html;
        }

        # 主入口配置
        location / {
            add_header Cache-Control 'proxy-revalidate';
            alias /usr/local/openresty/nginx/html/;
            index index.html;
            try_files $uri $uri/ /index.html;

            if ($request_filename ~* .*.(html|htm)$)
            {
                expires -1s;
            }

            if ($request_filename ~* .*.(gif|jpg|jpeg|png|svg)$)
            {
                expires 30d;
            }

            if ($request_filename ~ .*.(js|css)$)
            {
                expires 12h;
            }
        }
        location /ping {
            return 200 "oK";
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }
    }
}
